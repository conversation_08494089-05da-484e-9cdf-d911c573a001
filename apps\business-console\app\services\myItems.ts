import { ApiResponse } from "~/types/api/Api";
import {
  SelectedSellerItem,
  SellerItem,
} from "~/types/api/businessConsoleService/MyItemList";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getSellerItems(
  page?: number,
  pageSize?: number,
  search?: string | undefined,
  request?: Request
): Promise<ApiResponse<SellerItem[]>> {
  const url = new URL(`${API_BASE_URL}/bc/seller/items`);
  if (page !== undefined) url.searchParams.append("pageNo", page.toString());
  if (pageSize !== undefined)
    url.searchParams.append("size", pageSize.toString());
  if (search) url.searchParams.append("matchBy", search);

  const response = await apiRequest<SellerItem[]>(
    url.toString(),
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch seller items");
  }
}

export async function getSelectedSellerItems(
  selectedItem: number,
  page: number,
  pagesize: number,
  request?: Request
): Promise<ApiResponse<SelectedSellerItem[]>> {
  const response = await apiRequest<SelectedSellerItem[]>(
    `${API_BASE_URL}/bc/seller/item/${selectedItem}/sales?page=${page}&size=${pagesize}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch buyer details");
  }
}
