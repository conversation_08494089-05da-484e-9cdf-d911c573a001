import { MyVariationData } from "~/types/api/businessConsoleService/SellerManagement";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import React, { useState, useEffect } from "react";
import { useFetcher } from "@remix-run/react";
import SpinnerLoader from "../loader/SpinnerLoader";
import { useToast } from "../ui/ToastProvider";

interface VariationProps {
      isOpen: boolean;
      data?: MyVariationData | undefined;
      onClose: () => void;
      header: string;
      sellerId: number,
      isEdit: boolean
}
const AddVariation: React.FC<VariationProps> = ({ isOpen, data, onClose, header, sellerId, isEdit }) => {
      const [formData, setFormData] = useState<MyVariationData>(
            data
            || {
                  internalName: "",
                  displayName: "",
                  groupName: "",
                  active: false,
            }
      );
      useEffect(() => {
            if (data && isEdit) {
                  setFormData(data)
            }
            else {
                  setFormData({
                        internalName: "",
                        displayName: "",
                        groupName: "",
                        active: false,
                  })
            }
      }, [data, isOpen])


      const { showToast } = useToast()
      const variationFetcher = useFetcher()
      useEffect(() => {
            if (variationFetcher.data && variationFetcher.data?.sucess) {
                  showToast("updatedSucessFully", "success")
                  onClose()
                  setFormData({
                        internalName: "",
                        displayName: "",
                        groupName: "",
                        active: false,
                  })
            }
            else {
                  if (variationFetcher.data?.error) {
                        showToast(variationFetcher.data?.error, "error")
                        onClose()
                  }
            }
      }, [variationFetcher.data])




      const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
            const { name, value } = e.target;
            setFormData((prevData) => ({
                  ...prevData,
                  [name]: name === "active" ? value === "true" : value,
            }));
      };
      const handleSave = () => {
            const fetcherData = new FormData();
            fetcherData.append("actionType", "variationAdd");
            fetcherData.append("sellerId", sellerId as unknown as string);
            fetcherData.append("VariationData", JSON.stringify(formData))
            fetcherData.append("isCreating", header === "Add Variation" ? "true" : "false")
            variationFetcher.submit(fetcherData, { method: "POST" })
      };
      if (!isOpen) return null;

      const loading = variationFetcher.state !== "idle";

      const isFormValid = () => {
            return (
                  formData.internalName.trim() !== "" &&
                  formData.displayName.trim() !== "" &&
                  formData.groupName.trim() !== ""
            );
      };




      return (
            <Dialog open={isOpen} onOpenChange={onClose}>

                  {loading && <SpinnerLoader loading={loading} size={20} />}
                  <DialogContent className="max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg">
                        <DialogTitle className="text-xl font-bold text-gray-900 sm:text-2xl">{header}</DialogTitle>
                        <div className="mt-4 space-y-5 max-h-[60vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                              {/* Editable Fields */}
                              <div className="space-y-4">
                                    {[
                                          { id: 1, name: "internalName" as const, type: "text", label: "InternalName" },
                                          { id: 2, name: "displayName" as const, type: "text", label: "DisplayName" },
                                          { id: 3, name: "groupName" as const, type: "text", label: "GroupName" },

                                    ].map((field) => (
                                          <div key={field.name} className="flex flex-col gap-2 sm:flex-row sm:items-center">
                                                <label className="w-full text-sm font-medium text-gray-700 sm:w-1/3">{field.label}</label>


                                                <input
                                                      type={field.type}
                                                      name={field.name}
                                                      value={formData?.[field.name as keyof MyVariationData] ?? ""} onChange={handleChange}
                                                      className="w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3 border"
                                                      placeholder={`Enter ${field.label}`}
                                                />

                                          </div>
                                    ))}
                              </div>

                              {/* Radio Button Group */}
                              <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                                    <label className="w-full text-sm font-medium text-gray-700 sm:w-1/3">Active Status</label>
                                    <div className="flex gap-6 w-full sm:w-2/3">
                                          <label className="flex items-center gap-2 text-sm text-gray-600 cursor-pointer">
                                                <input
                                                      type="radio"
                                                      name="active"
                                                      value="true"
                                                      checked={formData?.active === true}
                                                      onChange={handleChange}
                                                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                                      autoFocus
                                                />
                                                Yes
                                          </label>
                                          <label className="flex items-center gap-2 text-sm text-gray-600 cursor-pointer">
                                                <input
                                                      type="radio"
                                                      name="active"
                                                      value="false"
                                                      checked={formData?.active === false}
                                                      onChange={handleChange}
                                                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                                />
                                                No
                                          </label>
                                    </div>
                              </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end">
                              <button
                                    onClick={onClose}
                                    className="w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto"
                              >
                                    Cancel
                              </button>
                              <button
                                    onClick={handleSave}
                                    disabled={loading || !isFormValid()}
                                    className={`w-full rounded-lg px-4 py-2 text-sm font-medium text-white focus:outline-none sm:w-auto ${loading || !isFormValid()
                                          ? "bg-gray-400 cursor-not-allowed"
                                          : "bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500"
                                          }`}
                              >
                                    {loading ? "Saving..." : "Save"}
                              </button>
                        </div>
                  </DialogContent>
            </Dialog>
      );
};

export default AddVariation;