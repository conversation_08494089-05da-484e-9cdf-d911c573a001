
import { ActionFunctionArgs } from "@remix-run/node";
import { Form, json, useActionD<PERSON>, useF<PERSON>cher, useLoaderData } from "@remix-run/react";
import { Edit } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { getNetWorkAreas } from "~/services/netWorks"
import { User } from "~/types";
import { NetWorkAreaDetails, NetWorkDetails } from "~/types/api/businessConsoleService/netWorkinfo";
import { getSession } from "~/utils/session.server";
import { withAuth, withResponse } from "@utils/auth-utils";



export const loader = withAuth(async ({ request }) => {
      try {

            // const response = await getNetWorks(request);
            return withResponse({ data: [] }, new Headers());
      } catch (error) {
            console.error('Network details error:', error);
            throw new Error(`Error fetching network details: ${error}`);
      }
});

export const action = withAuth(async ({ request }) => {
      try {
            const formData = await request.formData();
            const networkId = formData.get("netWorkId");

            if (!networkId) {
                  throw json(
                        { error: "Network ID is required" },
                        { status: 400 }
                  );
            }

            const session = await getSession(request.headers.get("Cookie"));
            const access_token = session.get("access_token") as string | null;

            const response = await getNetWorkAreas(Number(networkId), access_token);
            return withResponse({ data: response.data }, response.headers);
      } catch (error) {
            console.error('Network areas error:', error);
            throw new Response("Failed to get network areas", { status: 500 });
      }
});

export default function NetWorkAreas() {
      const initialData = useLoaderData<{ data: NetWorkDetails[] }>();
      const areaData = useActionData<{ data: NetWorkAreaDetails[] }>();
      const [netWorkAreas, setNetWorkAreas] = useState<NetWorkAreaDetails[] | []>(areaData?.data || [])
      const [searchTerm, setSearchTerm] = useState("")
      const [netWorkId, setNetWorkId] = useState("")
      const [pageSize, setPageSize] = useState("50");
      const [currentPage, setCurrentPage] = useState(1);

      useEffect(() => {
            if (areaData?.data) {
                  setNetWorkAreas(areaData.data)
            }


      }, [areaData?.data])

      const filterNetWorks = netWorkAreas
            ?.filter((x) => x.agentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  x.networkAreaName.toLowerCase().includes(searchTerm.toLowerCase()))

      const paginatedData = useMemo(() => {
            const start = (currentPage - 1) * Number(pageSize);
            const end = start + Number(pageSize);
            return [...filterNetWorks]
                  .sort((a, b) => (a.networkAreaId > b.networkAreaId ? -1 : 1))
                  .slice(start, end);
      }, [filterNetWorks, currentPage, pageSize]);

      const totalPages = Math.ceil(filterNetWorks.length / Number(pageSize));

      return (
            <div className="container mx-auto w-full" >
                  <div className="flex my-7 space-x-10">
                        <Form method="post" className="flex space-x-8">
                              <Select value={netWorkId} onValueChange={setNetWorkId} name="netWorkId">
                                    <SelectTrigger className="w-[180px]">
                                          <SelectValue placeholder="Select NetWork " />
                                    </SelectTrigger>
                                    <SelectContent>
                                          {initialData.data.sort((a, b) => a.managerName.localeCompare(b.managerName)).map((x) => {
                                                return (
                                                      <SelectItem value={x.id.toString()}>{x.name}</SelectItem>

                                                )
                                          })
                                          }

                                    </SelectContent>
                              </Select>
                              <Button type="submit" >
                                    Get NetWork Areas
                              </Button>
                        </Form>

                        <Input placeholder="Search Area/Agent"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"

                        />
                        <Select value={pageSize} onValueChange={setPageSize}>
                              <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="Rows per page" />
                              </SelectTrigger>
                              <SelectContent>
                                    <SelectItem value="5">5 per page</SelectItem>
                                    <SelectItem value="10">10 per page</SelectItem>
                                    <SelectItem value="20">20 per page</SelectItem>
                                    <SelectItem value="50">50 per page</SelectItem>
                              </SelectContent>
                        </Select>
                  </div>

                  <Table>
                        <TableHeader className="bg-gray-100">
                              <TableHead>Area Name</TableHead>
                              <TableHead>Agent Name </TableHead>
                              <TableHead>      </TableHead>


                        </TableHeader>
                        <TableBody>

                              {paginatedData.length > 0 ? (
                                    paginatedData?.sort((a, b) => a.networkAreaName.localeCompare(b.networkAreaName)).map((area) =>
                                    (<TableRow key={area.networkAreaId}>
                                          <TableCell>{area.networkAreaName}</TableCell>
                                          <TableCell>{area.agentName}</TableCell>
                                          <TableCell>

                                                <Edit className="h-4 w-4" />
                                          </TableCell>



                                    </TableRow>))
                              )
                                    : (<TableRow>
                                          <TableCell
                                                colSpan={9}
                                                className="h-24 text-center"
                                          >
                                                No results.
                                          </TableCell>
                                    </TableRow>
                                    )}

                        </TableBody>
                  </Table>
                  <div className="flex items-center space-x-2">
                        <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                              disabled={currentPage === 1}
                        >
                              Previous
                        </Button>
                        <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                              }
                              disabled={currentPage === totalPages}
                        >
                              Next
                        </Button>
                  </div>
            </div>
      )
}
