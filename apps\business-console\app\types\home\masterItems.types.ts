export interface FormState {
  translations: {
    kanadaName: string;
    hindiName: string;
    tamilName: string;
    teluguName: string;
    bengaliName: string;
    malyalumName: string;
    marathiName: string;
    gujaratiName: string;
    assamiName: string;
  };
  images: Array<{
    id: number;
    url: string;
    sequence: number;
    isDefault: boolean;
  }>;
  searchTags: string[];
  assignedCategories: number[];
  itemConfig: {
    type: "B2B" | "B2C";
    unit?: string;
    minimumOrderQty: number;
    incrementOrderQty: number;
    weightFactor: number;
    packaging?: string;
    mrpPerUnit: number;
    maximumOrderQty: number;
    maxAvailableQty: number;
    productId?: string;
    originalProductId: string;
    isDefaultVariant: boolean;
    sequencePriority: string;
    gstEligible: "yes" | "no";
    gstHsnCode?: string;
    gstRate?: number;
    disabled: boolean;
    ondcDomain: "RET11" | "RET10",
    taxExempt?: boolean,
    description?: string;
    diet?: string;
  };
  [key: string]: unknown;
  // Include all MasterItemDto fields
  id?: number;
  name: string;
  picture?: string;
  defaultUnit?: string;
  b2b?: boolean;
  b2c?: boolean;
  searchTag?: string;
  categories?: Array<{
    id: number;
    name?: string;
    picture?: string;
    picturex?: string;
    picturexx?: string;
    level?: number;
    totalItems?: number;
    parentCategories?: string[];
  }>;
  source?: string;
  sourceKey?: string;
  mrp?: number;
  brandName?: string;
  gstHsnCode?: string;
  gstRate?: number;
  packaging?: string;
  defaultWeightFactor?: number;
  productId?: string;
  groupId?: string;
  groupSeq?: number;
  nameInKannada?: string;
  nameInHindi?: string;
  nameInTamil?: string;
  nameInTelugu?: string;
  nameInBangla?: string;
  nameInMalayalam?: string;
  nameInMarathi?: string;
}

export interface MasterItem {
  id: number;
  name: string;
  details: string;
  category: string;
}

export interface ItemConfig {
  type: "B2B" | "B2C";
  unit: string;
  minimumOrderQty: number;
  incrementOrderQty: number;
  weightFactor: number;
  packaging: string;
  mrpPerUnit: number;
  maximumOrderQty: number;
  maxAvailableQty: number;
  productId: string;
  originalProductId: string | null;
  isDefaultVariant: boolean;
  sequencePriority: string;
  gstEligible: "yes" | "no";
  gstHsnCode: string;
  gstRate: number;
  disabled: boolean;
  ondcDomain: "RET11" | "RET10",
  taxExempt?: boolean,
  description?: string,
  diet?: string
}

export interface StepComponentProps {
  formDataState: Record<string, unknown>;
  setFormDataState: React.Dispatch<React.SetStateAction<Record<string, unknown>>>;
  actionData?: {
    success?: boolean;
    errors?: Record<string, string[]>;
  };
}

export interface MasterItemRequest {
  id?: number;
  defaultUnit: string;
  name: string;
  picture: string;
  nameInKannada?: string;
  nameInTelugu?: string;
  nameInTamil?: string;
  nameInMalayalam?: string;
  nameInHindi?: string;
  nameInAssame?: string;
  nameInGujarati?: string;
  nameInMarathi?: string;
  nameInBangla?: string;
  defaultWeightFactor?: number;
  gstHsnCode?: string;
  gstRate?: number;
  source: string;
  sourceKey: string;
  productId?: string;
  brandName?: string;
  packaging?: string;
  mrp: number;
  b2b: boolean;
  b2c: boolean;
  groupId?: string;
  groupSeq?: number;
  searchTag?: string;
  minimumOrderQty?: number;
  maximumOrderQty?: number;
  incrementOrderQty?: number;
  maxAvailableQty?: number;
  disabled?: boolean;
  ondcDomain: string;
  taxExempt?: boolean;
  description?: string;
  diet?: string;
  categories?: Array<{
    id: number;
    name?: string;
    picture?: string;
    picturex?: string;
    picturexx?: string;
    level?: number;
    totalItems?: number;
    parentCategories?: string[];
  }>;
}

export interface MasterItemDto {
  id: number;
  defaultUnit: string;
  name: string;
  picture: string;
  nameInKannada: string;
  nameInTelugu: string;
  nameInTamil: string;
  nameInMalayalam: string;
  nameInHindi: string;
  nameInAssame: string;
  nameInGujarati: string;
  nameInMarathi: string;
  nameInBangla: string;
  defaultWeightFactor: number;
  gstHsnCode: string;
  gstRate: number;
  source: string;
  sourceKey: string;
  productId: string;
  brandName: string;
  packaging: string;
  mrp: number;
  ownerBId: number;
  b2b: boolean;
  b2c: boolean;
  groupId: string;
  groupSeq: number;
  searchTag: string;
  minimumOrderQty: number;
  incrementOrderQty: number;
  maximumOrderQty: number;
  maxAvailableQty: number;
  disabled: boolean;
  diet: string;
  description: string;
  displayOrder: string;
  taxExempt: boolean;
  ondcDomain: string;
  categories?: Array<{
    id: number;
    name?: string;
    picture?: string;
    picturex?: string;
    picturexx?: string;
    level?: number;
    totalItems?: number;
    parentCategories?: string[];
    sequence: number,

  }>;

}

export interface MasterItemResponse {
  masterItemDtoList: MasterItemDto[];
  totalPages: number;
}

export interface Category {
  id: number;
  name: string;
  picture?: string;
  picturex?: string;
  picturexx?: string;
  level?: number;
  totalItems?: number;
  parentCategories?: Array<{
    id: number;
    name: string;
    picture?: string;
    picturex?: string;
    picturexx?: string;
    level?: number;
    parentCategories?: number[];
    totalItems?: number;
    myItems?: number;
  }>;
}

export type CategoryResponse = Category[];

export interface LoaderData {
  items?: MasterItemDto[];
  tab?: string;
  search?: string;
  currentPage?: number;
  totalPages?: number;
  totalItems?: number;
  itemsPerPage?: number;
  // For brands search
  brands?: Array<{ id: string; name: string }>;
  brandPage?: number;
  // For categories search
  categories?: CategoryResponse;
  categoryPage?: number;
}

export interface ActionData {
  success?: boolean;
  intent: "uploadImage" | 'create' | 'edit' | 'duplicate';
  errors?: {
    [key: string]: string[] | undefined;
  };
}

export interface GetMasterItemsParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: string;
}

export interface GetCategoriesParams {
  level?: number;
  matchBy?: string;
  ondcDomain?: "RET10" | "RET11";
  pageNo?: number;
  size?: number;
}