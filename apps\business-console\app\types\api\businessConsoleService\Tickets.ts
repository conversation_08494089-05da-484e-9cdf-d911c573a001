export type SupportTicketStatus = "OPEN" | "WIP" | "CLOSED";

export interface Ticket {
  ticketId: number;
  userId: number;
  userName: string;
  userMobileNo: string;
  ticketType: string;
  status: SupportTicketStatus;
  orderGroupId: number;
  description: string;
  closingRemarks: string;
  createdDate: string;
  distinctId: string;
  requestedCallBack: boolean;
  userType: string;
  lastModifiedDate: string;
  sellerId: number;
  sellerName: string;
}

export interface TicketNote {
  ticketNoteId: number;
  ticketId: number;
  note: string;
}

export interface UpdateTicketRequest {
  ticketId: number;
  userId: number;
  userName: string;
  userMobileNo: string;
  ticketType: string;
  status: SupportTicketStatus;
  orderGroupId: number;
  description: string;
  closingRemarks: string;
  createdDate: string;
  distinctId: string;
  requestedCallBack: boolean;
  userType: string;
  lastModifiedDate: string;
  sellerId: number;
  sellerName: string;
}

export interface AddTicketNoteRequest {
  ticketId: number;
  note: string;
} 