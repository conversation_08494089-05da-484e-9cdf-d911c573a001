# Firebase Collection Backup Script

A simple and safe script to create a complete copy of the `facebook-connects` collection in Firebase.

## What it does

1. Copies the entire `facebook-connects` collection to a new backup collection
2. Preserves all document IDs and data exactly as they are
3. Creates a timestamped backup collection name
4. Saves metadata to a JSON file
5. No data loss - safe and simple

## Usage

```bash
npm run backup:sellers
```

## Output

### Firebase Backup Collection
- Name: `facebook-connects-backup-YYYY-MM-DDTHH-MM-SS-sssZ`
- Contains: Exact copy of all documents from `facebook-connects`
- Document IDs: Preserved exactly as in original collection

### Metadata JSON File
- Location: `src/scripts/output/backup_metadata_YYYY-MM-DDTHH-MM-SS-sssZ.json`
- Contains: Backup information and statistics

## Example Output

### Console Output
```
🚀 Starting Firebase collection backup...
📋 Copying facebook-connects collection to facebook-connects-backup-2024-01-15T10-30-45-123Z...
✅ Found 150 documents to copy
📊 Copied 10/150 documents...
📊 Copied 20/150 documents...
...
🎉 Backup completed successfully!
📊 Summary: 150/150 documents copied
🔥 Firebase backup collection: facebook-connects-backup-2024-01-15T10-30-45-123Z
📁 Metadata file: src/scripts/output/backup_metadata_2024-01-15T10-30-45-123Z.json
```

### Metadata File
```json
{
  "timestamp": "2024-01-15T10-30-45.123Z",
  "sourceCollection": "facebook-connects",
  "backupCollection": "facebook-connects-backup-2024-01-15T10-30-45-123Z",
  "totalDocuments": 150,
  "copiedDocuments": 150,
  "backupDate": "2024-01-15T10-30-45.123Z"
}
```

## Safety Features

- Creates exact copy of collection with all documents
- Preserves document IDs and structure
- Timestamped backup collection names
- Progress tracking during copy
- Error handling for individual documents
- Metadata tracking

## Run

```bash
npm run backup:sellers
```
