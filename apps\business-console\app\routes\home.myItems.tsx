
import { useState } from 'react'
import { <PERSON><PERSON> } from "@components/ui/button"
import { Input } from "@components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { Form, Link, useLoaderData, useNavigate } from "@remix-run/react"
import { SellerItem } from '~/types/api/businessConsoleService/MyItemList'
import { getSellerItems } from '~/services/myItems'
import { withAuth, withResponse } from "@utils/auth-utils";

interface LoaderData {
      data: SellerItem[];
      currentPage: number;
      hasNextPage: boolean;
      totalPages: number;
}

export const loader = withAuth(async ({ request }) => {
      try {
            const url = new URL(request.url);
            const page = parseInt(url.searchParams.get("page") || "0");
            const pageSize = parseInt(url.searchParams.get("pageSize") || "50");
            const search = (url.searchParams.get("matchBy") || "");

            const response = await getSellerItems(page, pageSize, search, request);
            const hasNextPage = response.data.length >= pageSize;

            return withResponse({
                  data: response.data,
                  currentPage: page,
                  hasNextPage,
                  totalPages: Math.ceil(response.data.length / pageSize)
            }, response.headers);
      } catch (error) {
            console.error("Seller items error:", error);
            throw new Response("Failed to fetch seller items data", {
                  status: 500
            });
      }
});

export default function MyItems() {
      const navigate = useNavigate();
      const { data: sellerItems, currentPage, hasNextPage, totalPages } = useLoaderData<LoaderData>()
      const [searchTerm, setSearchTerm] = useState('')
      const [pageSize, setPageSize] = useState("50")

      const handlePageSizeChange = (newPageSize: string) => {
            setPageSize(newPageSize)
            navigate(`/home/<USER>
      }

      const filterSellerItems = sellerItems
            .filter(item =>
                  item.name.toLowerCase().includes(searchTerm.toLowerCase())
            )


      const handlePageSearch = (value: string) => {
            if (value.length >= 3) {
                  setSearchTerm(value)
                  navigate(`/home/<USER>

            }
            else {
                  setSearchTerm(value)
            }
      }

      return (
            <div className="container mx-auto p-6">
                  <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold">My Items</h1>
                  </div>
                  <div className="flex justify-between mb-4">
                        <Input
                              placeholder="Search by Item Name"
                              value={searchTerm}
                              onChange={(e) => handlePageSearch(e.target.value)}
                              className="max-w-sm"
                        />
                        <Select value={pageSize} onValueChange={handlePageSizeChange}>
                              <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="Items per page" />
                              </SelectTrigger>
                              <SelectContent>
                                    <SelectItem value="5">5 per page</SelectItem>
                                    <SelectItem value="10">10 per page</SelectItem>
                                    <SelectItem value="20">20 per page</SelectItem>
                                    <SelectItem value="50">50 per page</SelectItem>
                              </SelectContent>
                        </Select>
                  </div>
                  <Table>
                        <TableHeader>
                              <TableRow>
                                    <TableHead>Item Image</TableHead>
                                    <TableHead>Item Name</TableHead>
                                    <TableHead>Item Unit</TableHead>
                              </TableRow>
                        </TableHeader>
                        <TableBody>
                              {filterSellerItems.sort((a, b) => a.name.localeCompare(b.name)).map((item) => (
                                    <TableRow key={item.Id}>
                                          <TableCell>
                                                <img src={item.picture}
                                                      alt="ItemImage"
                                                      className="h-10 w-10" />
                                          </TableCell>
                                          <TableCell>
                                                <Link to={`/home/<USER>/${item.Id}`}
                                                      className="text-blue-600 hover:underline">
                                                      <div>
                                                            <div>{item.name}</div>
                                                      </div>
                                                </Link>
                                          </TableCell>
                                          <TableCell>{item.unit}</TableCell>
                                    </TableRow>
                              ))}
                        </TableBody>
                  </Table>
                  <div className="flex items-center justify-between space-x-2 py-4">
                        <div className="flex-1 text-sm text-muted-foreground">
                              {/* Page {currentPage + 1} of {totalPages} */}
                        </div>
                        <div className="flex items-center space-x-2">
                              <Form method="get">
                                    <input type="hidden" name="page" value={currentPage - 1} />
                                    <input type="hidden" name="pageSize" value={pageSize} />
                                    <Button
                                          variant="outline"
                                          size="sm"
                                          type="submit"
                                          disabled={currentPage <= 0}
                                    >
                                          Previous
                                    </Button>
                              </Form>
                              <Form method="get">
                                    <input type="hidden" name="page" value={currentPage + 1} />
                                    <input type="hidden" name="pageSize" value={pageSize} />
                                    <Button
                                          variant="outline"
                                          size="sm"
                                          type="submit"
                                          disabled={!hasNextPage}
                                    >
                                          Next
                                    </Button>
                              </Form>
                        </div>
                  </div>
            </div>
      )
}
