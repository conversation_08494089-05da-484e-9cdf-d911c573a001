import { NotificationLogService } from './notificationLogService.js';
import { NotificationStatus, NotificationLog } from '../database/entities/NotificationLog.js';
import * as admin from 'firebase-admin';
import { MessagingPayload } from 'firebase-admin/messaging';

export class FirebaseNotificationService {
    private notificationLogService: NotificationLogService;
    private messaging: admin.messaging.Messaging;

    constructor() {
        this.notificationLogService = new NotificationLogService();
        
        // Initialize Firebase Admin if not already initialized
        if (!admin.apps.length) {
            admin.initializeApp({
                credential: admin.credential.applicationDefault(),
                projectId: process.env.FIREBASE_PROJECT_ID
            });
        }
        
        this.messaging = admin.messaging();
    }

    async sendNotification(
        businessId: string,
        deviceToken: string,
        notificationPayload: MessagingPayload
    ): Promise<string> {
        let logEntry: NotificationLog | null = null;

        try {
            // Create the message
            const message: admin.messaging.Message = {
                token: deviceToken,
                ...notificationPayload
            };

            // Log the initial attempt
            logEntry = await this.notificationLogService.logFirebaseNotification(
                businessId,
                businessId,
                deviceToken,
                notificationPayload,
                message,
                null,
                NotificationStatus.PENDING
            );

            // Send the message
            const response = await this.messaging.send(message);

            // Update log with success
            await this.notificationLogService.updateNotificationStatus(
                logEntry.notificationId,
                logEntry.timestamp,
                NotificationStatus.SENT,
                { messageId: response }
            );

            return response;
        } catch (error) {
            if (logEntry && error instanceof Error) {
                await this.notificationLogService.updateNotificationStatus(
                    logEntry.notificationId,
                    logEntry.timestamp,
                    NotificationStatus.FAILED,
                    null,
                    error.message
                );

                // Handle specific Firebase error codes
                if ('code' in error && error.code === 'messaging/invalid-token') {
                    await this.handleInvalidToken(businessId, deviceToken);
                }
            }
            throw error;
        }
    }

    async sendMulticastNotification(
        businessId: string,
        deviceTokens: string[],
        notificationPayload: MessagingPayload
    ): Promise<admin.messaging.BatchResponse> {
        let logEntries: NotificationLog[] = [];

        try {
            // Create the multicast message
            const message: admin.messaging.MulticastMessage = {
                tokens: deviceTokens,
                ...notificationPayload
            };

            // Log initial attempts for each token
            logEntries = await Promise.all(
                deviceTokens.map(token =>
                    this.notificationLogService.logFirebaseNotification(
                        businessId,
                        businessId,
                        token,
                        notificationPayload,
                        message,
                        null,
                        NotificationStatus.PENDING
                    )
                )
            );

            // Send the multicast message
            const response = await this.messaging.sendEachForMulticast(message);

            // Update logs based on responses
            await Promise.all(
                response.responses.map((resp: admin.messaging.SendResponse, index: number) => {
                    const logEntry = logEntries[index];
                    const status = resp.success ? NotificationStatus.SENT : NotificationStatus.FAILED;
                    
                    return this.notificationLogService.updateNotificationStatus(
                        logEntry.notificationId,
                        logEntry.timestamp,
                        status,
                        resp,
                        resp.error?.message
                    );
                })
            );

            return response;
        } catch (error) {
            // Log failure for all pending notifications
            await Promise.all(
                logEntries.map(logEntry =>
                    this.notificationLogService.updateNotificationStatus(
                        logEntry.notificationId,
                        logEntry.timestamp,
                        NotificationStatus.FAILED,
                        null,
                        error instanceof Error ? error.message : 'Unknown error'
                    )
                )
            );
            throw error;
        }
    }

    private async handleInvalidToken(businessId: string, deviceToken: string): Promise<void> {
        // Implement your logic to handle invalid tokens
        // For example, you might want to:
        // 1. Remove the token from your user's device tokens
        // 2. Notify the business about the invalid token
        // 3. Update user's device status in your database
        console.warn(`Invalid Firebase token detected for business ${businessId}: ${deviceToken}`);
    }
} 