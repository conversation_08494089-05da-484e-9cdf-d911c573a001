'use client'

import React, { useEffect, useState } from 'react'
import { ArrowLeft, EditIcon, Save, } from 'lucide-react'
import { Button } from "@components/ui/button"
import { Input } from "@components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@components/ui/card"
import { useFetcher, useNavigate } from "@remix-run/react"
import { json } from "@remix-run/node"
import { useLoaderData } from "@remix-run/react"
import { addContractPrice, getContractPricesForSellerItem, getDashboardDataForItem, getSelectedItemSummary, updateContractPrice, updateContractStatus } from "@services/businessConsoleService"
import { ContractPrice, SelectedItemSummary } from "~/types/api/businessConsoleService/BuyerOrdersResponse";
import { Switch } from '~/components/ui/switch'
import { withAuth, withResponse } from "@utils/auth-utils";
import { SelectedSellerItem } from '~/types/api/businessConsoleService/MyItemList'
import { format } from 'date-fns'
import { getSelectedSellerItems } from '~/services/myItems'
import SpinnerLoader from '~/components/loader/SpinnerLoader'
import { formatWeight, formatCurrency } from '@utils/format'
import { DashboardGroupBy } from '~/types/home'
import CustomerDashboard, { calculateTargetDate } from '~/components/ui/bar-dashboard'
import { SellerConsoleDataResponse } from '~/types/api/businessConsoleService/SellerConsoleDataResponse'
interface LoaderData {
      sellectedSellerItemSummay: SelectedItemSummary
      SelectedSellerItem: SelectedSellerItem[];
      currentPage: number;
      hasNextPage: boolean;
      userPermissions: string[];
      itemId: number;
      itemDashBoardData: SellerConsoleDataResponse[];
      dashboardGroupBy: DashboardGroupBy
}


interface SuccessResponse {
      success: true;
      data: unknown;
}

interface ActionErrorResponse {
      error: string;
}

export const loader = withAuth(async ({ user, request }) => {
      const params = new URL(request.url).pathname.split('/');
      const itemId = parseInt(params[params.length - 1]);
      const userPermissions = user?.userPermissions || []; -
            console.log("hello123", userPermissions)
      if (isNaN(itemId)) {
            throw json(
                  { error: "Invalid item ID" },
                  { status: 400 }
            );
      }

      const url = new URL(request.url);
      const page = parseInt(url.searchParams.get("page") || "0");
      const pageSize = 50;
      const dashboardGroupBy = DashboardGroupBy.Daily;
      try {
            const [selectedItemSummary, SalesReportSummary, ItemDashBoardReponse] = await Promise.all([
                  getSelectedItemSummary(itemId, request),
                  getSelectedSellerItems(itemId, page, pageSize, request),
                  getDashboardDataForItem(itemId, new Date(), dashboardGroupBy)
            ]);
            const responseHeaders = new Headers();
            [selectedItemSummary, SalesReportSummary, ItemDashBoardReponse].forEach(response => {
                  if (response.headers?.has('Set-Cookie')) {
                        responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);
                  }
            });
            return withResponse({
                  itemId,
                  sellectedSellerItemSummay: selectedItemSummary.data,
                  SelectedSellerItem: SalesReportSummary.data,
                  itemDashBoardData: ItemDashBoardReponse.data,
                  dashboardGroupBy: dashboardGroupBy,
                  currentPage: page,
                  userPermissions,
                  hasNextPage: SalesReportSummary.data.length >= pageSize,
            }, responseHeaders);

      } catch (error) {
            console.error("Contract prices error:", error);
            if (error instanceof Response && error.status === 404) {
                  throw withResponse(
                        {
                              error: "Error in item Sales",
                              status: 404
                        }
                  );
            }
            throw new Response("Failed to fetch contract prices data", { status: 500 });
      }
});

export const action = withAuth(async ({ user, request }) => {
      const formData = await request.formData();
      const intent = formData.get("intent")
      const sellerItemId = formData.get("siItemId") as string;
      console.log()

      const buyerId = formData.get("buyerId") as string
      const cPriceId = formData.get("cPriceId") as string;
      const cPriceEnable = formData.get("cPriceEnable") as unknown as boolean;

      const cPrice = formData.get("cPrice") as unknown as number;
      if (intent === "fetchContractPrices") {
            try {
                  const itemId = formData.get("itemId") as string;
                  const contractPrices = await getContractPricesForSellerItem(Number(itemId), request);

                  return json(contractPrices.data);
            } catch (error) {
                  return json<ActionErrorResponse>(
                        { error: "Failed to fetch contract prices" },
                        { status: 500 }
                  );
            }
      }

      if (intent === "ContractPrice") {
            try {
                  const response = await addContractPrice(Number(sellerItemId), Number(buyerId), request)
                  return json(response.data)

            }
            catch (error) {

                  return json<ActionErrorResponse>(
                        { error: "Failed to Updating ContractPriceStatus " },
                        { status: 500 }
                  );
            }

      }
      if (intent === "ContractUpdateStatus") {

            try {
                  const response = await updateContractStatus(cPriceEnable, Number(cPriceId), request)
                  return json(response.data)

            }

            catch (error) {

                  return json<ActionErrorResponse>(
                        { error: "Failed to Updating ContractPriceStatus " },
                        { status: 500 }
                  );
            }

      }

      if (intent === "ContractUpdatePrice") {
            try {
                  const response = await updateContractPrice(Number(cPriceId), cPrice, request)
                  return json(response.data)
            }

            catch (error) {

                  return json<ActionErrorResponse>(
                        { error: "Failed to create addContractPrice" },
                        { status: 500 }
                  );
            }

      }
      else if (intent === "itemdashboard") {
            const dashboardGroupBy = formData.get("dashboardGroupBy") as DashboardGroupBy;
            const summaryDate = new Date(formData.get("summaryDate") as string);
            const ItemId = formData.get("ItemId") as string;
            if (!Object.values(DashboardGroupBy).includes(dashboardGroupBy)) {
                  throw json({ error: "Invalid groupBy parameter" }, { status: 400 });
            }

            const response = await getDashboardDataForItem(Number(ItemId), summaryDate, dashboardGroupBy, request);
            return withResponse(
                  {
                        data: response.data,
                        dashboardGroupBy: dashboardGroupBy
                  },
                  response.headers
            );
      }

});

export default function ItemDetails() {
      const { sellectedSellerItemSummay, SelectedSellerItem, currentPage, hasNextPage, userPermissions, itemId, itemDashBoardData, dashboardGroupBy } = useLoaderData<LoaderData>();

      const [searchTerm, setSearchTerm] = useState('')
      const [activeTab, setActiveTab] = useState('dashboard')

      const fetcher = useFetcher<{ ContractPrice: ContractPrice[], data: SellerConsoleDataResponse[], dashboardGroupBy: DashboardGroupBy }>()
      const navigate = useNavigate();

      const [dynamicContractPrices, setDynamicContractPrices] = useState<ContractPrice[]>([]);
      const [uniqueId, setUnqueId] = useState(0);
      const [cPriceEnable, setCPriceEnable] = useState(false);
      const [cPriceEditable, setCPriceEditable] = useState(false);
      const [customergraphData, setCustomerGraphData] = useState<SellerConsoleDataResponse[]>(itemDashBoardData);
      const [dashboardGroup, setIsDashBoardGroup] = useState<DashboardGroupBy>(dashboardGroupBy)
      useEffect(() => {

            if (userPermissions) {

                  const isContractPrice = userPermissions.includes("seller_app.contracPriceEnabled");
                  const isContractPriceEditable = userPermissions.includes("seller_app.contracPriceEditable");
                  setCPriceEditable(isContractPriceEditable);
                  setCPriceEnable(isContractPrice)
            }
      }, [userPermissions])

      useEffect(() => {
            if (fetcher?.data?.ContractPrice && fetcher.data.ContractPrice?.length > 0) {

                  setDynamicContractPrices(fetcher.data.ContractPrice as ContractPrice[]);
            }

      }, [fetcher?.data, activeTab === "myBuyers"]);

      useEffect(() => {

            if (fetcher.data?.data && activeTab === "dashboard") {
                  setCustomerGraphData(fetcher.data.data);
                  setIsDashBoardGroup(fetcher.data.dashboardGroupBy)

            }
      }, [fetcher.state, activeTab === "dashboard"])

      const searchHandle = (x: ContractPrice) => {
            return x.buyerName.toLocaleLowerCase().includes(searchTerm.toLocaleLowerCase()) ||
                  x.buyerMobile.toString().toLocaleLowerCase().includes(searchTerm.toLocaleLowerCase())
      }
      const handleTabChange = (tab: string) => {
            setActiveTab(tab);
            if (tab === 'myBuyers') {
                  const formData = new FormData();
                  formData.append("intent", "fetchContractPrices");
                  formData.append("itemId", itemId.toString());
                  fetcher.submit(formData, { method: "post" });


            }
      }

      const handleUniqId = (buyerId: number) => {
            setUnqueId(buyerId);
      }


      const handleGraph = (value: string, summaryDate: Date) => {
            fetcher.submit(
                  {
                        dashboardGroupBy: value,
                        summaryDate: summaryDate.toISOString(),
                        intent: "itemdashboard",
                        ItemId: itemId.toString()
                  },
                  { method: "post" }
            );

      }

      return (
            <div className="container mx-auto p-6">

                  <div className="flex items-center gap-2 mb-6">
                        <Button variant="ghost" size="sm" onClick={() => navigate(-1)}>
                              <ArrowLeft className="h-4 w-4 mr-2" />
                              Back to My Items
                        </Button>
                        <span className="text-muted-foreground">/</span>
                        <span className="font-semibold">{sellectedSellerItemSummay?.itemName}</span>
                  </div>
                  <Card className="mb-4 ">
                        <CardHeader>
                              <CardTitle className="flex items-center justify-between">
                                    <div className='flex gap-4 '>
                                          {<img src={sellectedSellerItemSummay?.itemImage}

                                                height={80}
                                                width={80}

                                          />}
                                          <span className='self-center'>{sellectedSellerItemSummay?.itemName}</span>
                                    </div>

                              </CardTitle>
                        </CardHeader>
                        <CardContent>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex ">

                                    <p className="flex items-center mt-1 text-sm gap-1">
                                          Minimum Order Qty : <p className='text-sm text font-bold' >{sellectedSellerItemSummay?.itemMinOrderQty} </p> {sellectedSellerItemSummay.unit}

                                    </p>
                                    <p className="flex items-start mt-1 text-sm gap-1">
                                          Increment Order Qty:<p className='text-sm text font-bold' > {sellectedSellerItemSummay?.itemIncrementQty}</p> {sellectedSellerItemSummay.unit}
                                    </p>



                              </div>
                        </CardContent>
                  </Card>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <Card>
                              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
                                    <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                              </CardHeader>
                              <CardContent>
                                    <div className="text-2xl font-bold">{formatCurrency(sellectedSellerItemSummay?.revenue || 0)}</div>
                                    <p className="text-xs text-muted-foreground">
                                          from {sellectedSellerItemSummay?.totalOrders.toLocaleString('en-IN') || 0} Customers & {sellectedSellerItemSummay?.totalDaysOfDelivery.toLocaleString('en-IN') || 0} Deliveries
                                    </p>

                              </CardContent>
                        </Card>
                        <Card>
                              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
                                    <CardTitle className="text-sm font-medium">Returns</CardTitle>

                              </CardHeader>
                              <CardContent>
                                    <div className="text-xl font-bold">{formatWeight(sellectedSellerItemSummay?.returnedQty || 0)} </div>
                                    <p className="text-xs text-destructive">Revenue loss : {formatCurrency(sellectedSellerItemSummay?.returnedAmount || 0)}</p>
                              </CardContent>
                        </Card>
                  </div>

                  <Tabs value={activeTab} onValueChange={handleTabChange}>
                        <TabsList>
                              <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                              <TabsTrigger value="sales" >Sales Report</TabsTrigger>
                              {cPriceEnable && <TabsTrigger value="myBuyers"  >Contract Prices</TabsTrigger>}
                        </TabsList>
                        {activeTab === "myBuyers" && <div className="flex justify-between items-center my-4">
                              <Input
                                    placeholder="Search by BuyerName"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="max-w-sm"
                              />
                        </div>}

                        <TabsContent value="dashboard">
                              <CustomerDashboard data={customergraphData || []} handleGraph={handleGraph} dashboardGroupBy={dashboardGroup!} />
                        </TabsContent>
                        <TabsContent value="sales">
                              <Table>
                                    <TableHeader>
                                          <TableRow>
                                                <TableHead>Delivery Date</TableHead>
                                                <TableHead>Ordered Quantity</TableHead>
                                                <TableHead>Supply Shortage</TableHead>
                                                <TableHead>Return Quantity</TableHead>
                                                <TableHead>Delivered Quantity</TableHead>
                                                <TableHead>Revenue</TableHead>
                                          </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                          {SelectedSellerItem?.map((item) => (
                                                <TableRow key={item?.deliveryDate}>
                                                      <TableCell>{format(item?.deliveryDate, "yyyy-MM-dd")}</TableCell>
                                                      <TableCell>
                                                            {item.bookedQty > 0 ? `${item?.bookedQty} ${sellectedSellerItemSummay?.unit}` : "-"}
                                                      </TableCell>
                                                      <TableCell>
                                                            {item?.cancelledQty > 0 ? `${item?.cancelledQty} ${sellectedSellerItemSummay?.unit}` : "-"}
                                                      </TableCell>
                                                      <TableCell>
                                                            {item.returnedQty > 0 ? `${item?.returnedQty}${sellectedSellerItemSummay?.unit}` : "-"}

                                                      </TableCell>
                                                      <TableCell>
                                                            {item.deliveredQty > 0 ? `${item?.deliveredQty} ${sellectedSellerItemSummay?.unit}` : "-"}
                                                      </TableCell>
                                                      <TableCell>
                                                            {item.revenue > 0 ? `₹ ${item?.revenue}` : "-"}
                                                      </TableCell>

                                                </TableRow>
                                          ))}
                                    </TableBody>
                              </Table>
                              {/* <div className="flex justify-center gap-4 mt-4">
                                    <Form method="get">
                                          <input type="hidden" name="page" value={currentPage - 1} />
                                          <Button type="submit" disabled={currentPage <= 0}>
                                                Previous
                                          </Button>
                                    </Form>
                                    <span className="flex items-center">
                                          Page {currentPage + 1}
                                    </span>
                                    <Form method="get">
                                          <input type="hidden" name="page" value={currentPage + 1} />
                                          <Button type="submit" disabled={!hasNextPage}>
                                                Next
                                          </Button>
                                    </Form>
                              </div> */}
                        </TabsContent>



                        {cPriceEnable && <TabsContent value="myBuyers">
                              {fetcher.state !== "idle" && <SpinnerLoader size={8} loading={true} />}


                              <Table>
                                    <TableHeader>
                                          <TableRow>
                                                <TableHead>BuyerId </TableHead>
                                                <TableHead>Buyer Name</TableHead>
                                                <TableHead>Buyer Mobile</TableHead>
                                                <TableHead>Price</TableHead>
                                                <TableHead>Enabled</TableHead>
                                          </TableRow>
                                    </TableHeader>
                                    <TableBody>

                                          {dynamicContractPrices?.filter((x) => searchHandle(x)).map((buyer) => (
                                                <BuyerContactPrice buyerData={buyer}

                                                      uniqueId={uniqueId}

                                                      cPriceEditable={cPriceEditable}
                                                      handleUniqId={(buyerId: number) => handleUniqId(buyerId)}

                                                />))}
                                    </TableBody>
                              </Table>
                        </TabsContent>}
                  </Tabs>
            </div>
      )
}

function BuyerContactPrice({ buyerData, handleUniqId, uniqueId, cPriceEditable }: { buyerData: ContractPrice, handleUniqId: (buyerId: number) => void, uniqueId: number, cPriceEditable: boolean }) {

      const fetcher = useFetcher<ContractPrice>()
      const [cPrice, setCPrice] = useState(0);
      const [buyer, setBuyer] = useState<ContractPrice>(buyerData);
      const [isEdit, setIsEdit] = useState(false);


      const onAddHanle = () => {
            handleUniqId(buyer.buyerId);
            const formData = new FormData();
            formData.append("intent", "ContractPrice")
            formData.append("siItemId", buyer.sellerItemId.toString())
            formData.append("buyerId", buyer.buyerId.toString())
            fetcher.submit(formData, { method: "POST" });
      }
      const onHandleStatus = (enable: boolean) => {
            handleUniqId(buyer.buyerId);
            const formData = new FormData();
            formData.append("intent", "ContractUpdateStatus")
            formData.append("cPriceId", buyer.contractPriceId.toString())
            formData.append("cPriceEnable", enable.toString())
            fetcher.submit(formData, { method: "POST" });
            setIsEdit(false)
      }
      const onHandlPrice = () => {

            handleUniqId(buyer.buyerId);
            const formData = new FormData();
            formData.append("intent", "ContractUpdatePrice")
            formData.append("cPriceId", buyer.contractPriceId.toString())
            formData.append("cPrice", cPrice.toString())
            fetcher.submit(formData, { method: "POST" });
            setIsEdit(false)
      }


      const handleEdit = () => {
            handleUniqId(buyer.buyerId);
            setIsEdit(true)

      }
      useEffect(() => {
            if (fetcher.data) {
                  setBuyer(fetcher.data);
            }


      }, [fetcher.data])



      return (<TableRow key={buyer.buyerId}>
            <TableCell>{buyer?.buyerId}</TableCell>
            <TableCell>
                  {buyer?.buyerName}
            </TableCell>
            <TableHead>{buyer?.buyerMobile}</TableHead>

            {buyer.newItem === false && <TableCell className='flex gap-2'>

                  {isEdit && uniqueId === buyer.buyerId ? <>
                        <Input placeholder={buyer?.cbItemPrice.toString()} onChange={e => setCPrice(Number(e.target.value))} type="number" className='w-20 h-10 ' />
                        <Save height={20} width={20} onClick={() => onHandlPrice()} className='my-3 cursor-pointer ' />
                  </> : <> {`₹ ${buyer?.cbItemPrice}`}

                        {cPriceEditable && <EditIcon height={20} width={20} onClick={() => handleEdit()} />}

                  </>}
            </TableCell>}
            <TableCell>{buyer.newItem ? <Button type="button" onClick={() => onAddHanle()}>{uniqueId === buyer.buyerId ? "ADDING..." : "ADD"}</Button> : <Switch checked={buyer.enabled} onCheckedChange={() => onHandleStatus(!buyer.enabled)} />}
            </TableCell>


      </TableRow>);
}
