import { useEffect, useState, useCallback, useRef } from 'react'
import { json, LoaderFunction, ActionFunction, redirect } from '@remix-run/node'
import { useLoaderData, useNavigate, Form } from '@remix-run/react'
import { ArrowLeft } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@components/ui/card"
import { Input } from "@components/ui/input"
import { Button } from "@components/ui/button"
import { updateArea, getAreaById } from '~/services/businessConsoleService'
import { getSession } from "@utils/session.server"
import { GoogleMap, LoadScript, Polygon, DrawingManager, StandaloneSearchBox } from '@react-google-maps/api'
import { decodePolygon } from "@utils/polyline-utils"

// Correctly import from a CommonJS module:
import * as polylineCodec from '@googlemaps/polyline-codec';

import type { User } from "~/types"
import { MasterLocalities } from '~/types/api/businessConsoleService/SellerManagement'

interface LoaderData {
  area: MasterLocalities
  googleMapsApiKey: string
}

export const loader: LoaderFunction = async ({ params, request }) => {
  try {
    const session = await getSession(request.headers.get("Cookie"))
    const user = session.get("user") as User
    const areaId = params.areaId

    if (!areaId) {
      throw new Error("Area ID is required")
    }

    const areaResponse = await getAreaById(parseInt(areaId, 10), request)

    return json<LoaderData>({
      area: areaResponse.data,
      googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY || '',
    })
  } catch (error) {
    console.error('Failed to fetch area:', error)
    return json({ error: "Failed to fetch area" }, { status: 500 })
  }
}

export const action: ActionFunction = async ({ request, params }) => {
  const session = await getSession(request.headers.get("Cookie"))
  const user = session.get("user") as User
  const formData = await request.formData()
  const areaId = params.areaId

  if (!areaId) {
    throw new Error("Area ID is required")
  }

  if (request.method.toUpperCase() === "PUT") {
    const name = formData.get('name') as string
    const polygon = formData.get('polygon') as string

    try {
      await updateArea(
        user.userId,
        {
          id: parseInt(areaId, 10),
          name,
          polygon,
        },
        request
      )
      return redirect('/home/<USER>')
    } catch (error) {
      return json({ error: "Failed to update area" }, { status: 500 })
    }
  }

  return null
}

const BANGALORE_CENTER = { lat: 12.9716, lng: 77.5946 }

export default function EditLocality() {
  const { area, googleMapsApiKey } = useLoaderData<LoaderData>()
  // Ensure we only render on the client.
  const [isClient, setIsClient] = useState(false)
  // Track when the Google Maps script is loaded.
  const [scriptLoaded, setScriptLoaded] = useState(false)
  const [name, setName] = useState(area.name)
  const [polygon, setPolygon] = useState<google.maps.LatLngLiteral[]>(
    area.polygon ? decodePolygon(area.polygon) : []
  )
  const [map, setMap] = useState<google.maps.Map | null>(null)
  const [searchBox, setSearchBox] = useState<google.maps.places.SearchBox | null>(null)
  const polygonRef = useRef<google.maps.Polygon | null>(null)
  const polygonListenersRef = useRef<google.maps.MapsEventListener[]>([])
  const navigate = useNavigate()

  // Set client flag on mount.
  useEffect(() => {
    setIsClient(true)
  }, [])

  const onLoad = useCallback((mapInstance: google.maps.Map) => {
    setMap(mapInstance)
  }, [])

  const onPolygonComplete = useCallback((poly: google.maps.Polygon) => {
    // Remove any previously drawn polygon and clear its listeners.
    if (polygonRef.current) {
      polygonRef.current.setMap(null)
      polygonListenersRef.current.forEach(listener =>
        google.maps.event.removeListener(listener)
      )
      polygonListenersRef.current = []
    }
    polygonRef.current = poly

    // Convert LatLng objects to LatLngLiteral objects.
    const polyArray = poly.getPath().getArray().map((latlng) => ({
      lat: latlng.lat(),
      lng: latlng.lng(),
    }))
    setPolygon(polyArray)

    const path = poly.getPath()
    const updatePolygon = () => {
      setPolygon(
        path.getArray().map((latlng) => ({
          lat: latlng.lat(),
          lng: latlng.lng(),
        }))
      )
    }

    const setAtListener = google.maps.event.addListener(path, 'set_at', updatePolygon)
    const insertAtListener = google.maps.event.addListener(path, 'insert_at', updatePolygon)
    const removeAtListener = google.maps.event.addListener(path, 'remove_at', updatePolygon)

    polygonListenersRef.current.push(setAtListener, insertAtListener, removeAtListener)
  }, [])

  const onSearchBoxLoad = (ref: google.maps.places.SearchBox) => {
    setSearchBox(ref)
  }

  const onPlacesChanged = () => {
    if (searchBox && map) {
      const places = searchBox.getPlaces()
      if (places && places.length > 0) {
        const place = places[0]
        if (place.geometry && place.geometry.location) {
          map.panTo(place.geometry.location)
          map.setZoom(15)
        }
      }
    }
  }

  useEffect(() => {
    if (map && polygon.length > 0) {
      const bounds = new google.maps.LatLngBounds()
      polygon.forEach(coord => bounds.extend(coord))
      map.fitBounds(bounds)
    }
  }, [map, polygon])

  // Cleanup polygon listeners on unmount.
  useEffect(() => {
    return () => {
      polygonListenersRef.current.forEach(listener =>
        google.maps.event.removeListener(listener)
      )
      polygonListenersRef.current = []
    }
  }, [])

  // Until we are on the client, display a loading message.
  if (!isClient) {
    return <div>Loading...</div>
  }

  return (
    <div className="h-[calc(100vh-64px)] bg-white p-6 overflow-y-auto">
      <div className="flex justify-between items-center mb-6">
        <Button onClick={() => navigate('/home/<USER>')} variant="ghost" size="icon">
          <ArrowLeft size={24} />
        </Button>
      </div>

      <Card className="w-full mx-auto">
        <CardHeader>
          <CardTitle>Edit Area: {area.name}</CardTitle>
          <CardDescription>Update the area name and boundary</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Form method="PUT">
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Area Name
                </label>
                <Input
                  id="name"
                  name="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  className="mt-1"
                />
              </div>

              {/* Hidden input to store the encoded polygon */}
              <input type="hidden" name="polygon" value={encode(polygon)} />

              <div className="h-[400px] relative">
                <LoadScript
                  googleMapsApiKey={googleMapsApiKey}
                  libraries={["drawing", "places"]}
                  onLoad={() => setScriptLoaded(true)}
                >
                  <GoogleMap
                    mapContainerClassName="w-full h-full"
                    center={BANGALORE_CENTER}
                    zoom={11}
                    onLoad={onLoad}
                  >
                    <StandaloneSearchBox onLoad={onSearchBoxLoad} onPlacesChanged={onPlacesChanged}>
                      <Input
                        type="text"
                        placeholder="Search for a location"
                        className="absolute top-2 left-1/2 transform -translate-x-1/2 w-1/3 z-10"
                      />
                    </StandaloneSearchBox>

                    {polygon.length > 0 && (
                      <Polygon
                        path={polygon}
                        options={{
                          fillColor: "#4F46E5",
                          fillOpacity: 0.4,
                          strokeColor: "#4F46E5",
                          strokeWeight: 2,
                        }}
                      />
                    )}

                    {/* Render DrawingManager only after the script has loaded */}
                    {scriptLoaded &&
                      typeof window !== "undefined" &&
                      window.google && (
                        <DrawingManager
                          onPolygonComplete={onPolygonComplete}
                          options={{
                            polygonOptions: {
                              fillColor: "#4F46E5",
                              fillOpacity: 0.4,
                              strokeColor: "#4F46E5",
                              strokeWeight: 2,
                              clickable: true,
                              editable: true,
                              draggable: true,
                            },
                            drawingControl: true,
                            drawingControlOptions: {
                              position: window.google.maps.ControlPosition.TOP_CENTER,
                              drawingModes: [window.google.maps.drawing.OverlayType.POLYGON],
                            },
                          }}
                        />
                      )}
                  </GoogleMap>
                </LoadScript>
              </div>

              <Button type="submit" className="w-full">
                Update Area
              </Button>
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}