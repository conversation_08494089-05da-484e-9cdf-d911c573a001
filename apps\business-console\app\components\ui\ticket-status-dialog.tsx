import { useState, useEffect } from "react";
import { useFetcher } from "@remix-run/react";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "./dialog";
import { <PERSON><PERSON> } from "./button";
import { Label } from "./label";
import { RadioGroup, RadioGroupItem } from "./radio-group";
import { SupportTicketStatus } from "~/types/api/businessConsoleService/Tickets";

interface TicketStatusDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ticket: {
    ticketId: number;
    status: SupportTicketStatus;
  };
}

export function TicketStatusDialog({ 
  isOpen, 
  onClose, 
  ticket
}: TicketStatusDialogProps) {
  const [status, setStatus] = useState<SupportTicketStatus>(ticket.status);
  const updateStatusFetcher = useFetcher();
  const [hasSubmitted, setHasSubmitted] = useState(false);
  
  const isSubmitting = updateStatusFetcher.state === "submitting";
  
  // Close dialog when status update is successful
  useEffect(() => {
    if (
      hasSubmitted &&
      updateStatusFetcher.state === "idle" && 
      updateStatusFetcher.data?.success === true
    ) {
      setHasSubmitted(false);
      onClose();
    }
  }, [updateStatusFetcher.state, updateStatusFetcher.data, hasSubmitted, onClose]);
  
  const handleSubmit = () => {
    if (status === ticket.status) {
      onClose();
      return;
    }
    
    setHasSubmitted(true);
    updateStatusFetcher.submit(
      { 
        status,
        ticketId: ticket.ticketId.toString(),
        _action: "updateStatus"
      },
      { 
        method: "post", 
        action: `/home/<USER>/status` 
      }
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Ticket Status</DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          <RadioGroup value={status} onValueChange={(value) => setStatus(value as SupportTicketStatus)}>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="OPEN" id="open" />
              <Label htmlFor="open" className="cursor-pointer">Open</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="WIP" id="wip" />
              <Label htmlFor="wip" className="cursor-pointer">Work In Progress</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="CLOSED" id="closed" />
              <Label htmlFor="closed" className="cursor-pointer">Closed</Label>
            </div>
          </RadioGroup>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            onClick={handleSubmit} 
            loading={isSubmitting}
            disabled={isSubmitting || status === ticket.status}
          >
            Update
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 