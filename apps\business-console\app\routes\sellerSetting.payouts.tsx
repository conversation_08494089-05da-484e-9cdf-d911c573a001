import { useLoaderData } from "@remix-run/react";
import { metabaseService } from "../utils/metabase";
import { withAuth, withResponse } from "../utils/auth-utils";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import {
  CalendarIcon,
  Search,
  Download,
  CreditCard,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Clock,
  AlertCircle,
  CheckCircle,
  Eye,
  FileText,
  Building2,
  Banknote,
  Receipt,
  ArrowUpDown
} from "lucide-react";
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { ComingSoonOverlay } from "~/components/modals/ComingSoonOverlay";

const mockPayouts = [
  {
    id: "PAY-001",
    outlet: "Downtown",
    settlementStart: "2025-01-07",
    settlementEnd: "2025-01-13",
    paidOn: "2025-01-14",
    grossSales: 156780,
    discounts: 7840,
    refunds: 2340,
    platformFee: 4703,
    pgFee: 2819,
    deliveryFee: 8900,
    gstWithheld: 7839,
    tcs: 1568,
    netPayout: 121771,
    bankRefId: "UTR2025011456789",
    status: "Settled"
  },
  {
    id: "PAY-002",
    outlet: "Mall Road",
    settlementStart: "2025-01-07",
    settlementEnd: "2025-01-13",
    paidOn: "2025-01-14",
    grossSales: 198450,
    discounts: 9923,
    refunds: 1890,
    platformFee: 5954,
    pgFee: 3572,
    deliveryFee: 11230,
    gstWithheld: 9923,
    tcs: 1985,
    netPayout: 154973,
    bankRefId: "UTR2025011456790",
    status: "Settled"
  },
  {
    id: "PAY-003",
    outlet: "Downtown",
    settlementStart: "2025-01-01",
    settlementEnd: "2025-01-06",
    paidOn: "2025-01-08",
    grossSales: 134560,
    discounts: 6728,
    refunds: 3240,
    platformFee: 4037,
    pgFee: 2420,
    deliveryFee: 7680,
    gstWithheld: 6728,
    tcs: 1346,
    netPayout: 102381,
    bankRefId: "UTR2025010834567",
    status: "Settled"
  },
  {
    id: "PAY-004",
    outlet: "Mall Road",
    settlementStart: "2025-01-14",
    settlementEnd: "2025-01-20",
    paidOn: null,
    grossSales: 178920,
    discounts: 8946,
    refunds: 2140,
    platformFee: 5368,
    pgFee: 3221,
    deliveryFee: 10250,
    gstWithheld: 8946,
    tcs: 1789,
    netPayout: 138260,
    bankRefId: null,
    status: "Pending"
  },
  {
    id: "PAY-005",
    outlet: "Downtown",
    settlementStart: "2024-12-23",
    settlementEnd: "2024-12-29",
    paidOn: "2025-01-02",
    grossSales: 145670,
    discounts: 7284,
    refunds: 4350,
    platformFee: 4370,
    pgFee: 2620,
    deliveryFee: 8230,
    gstWithheld: 7284,
    tcs: 1457,
    netPayout: 110075,
    bankRefId: "UTR2025010234561",
    status: "Disputed"
  }
];

const payoutTrends = [
  { week: "Week 1", grossSales: 280000, netPayout: 215000, fees: 65000 },
  { week: "Week 2", grossSales: 310000, netPayout: 240000, fees: 70000 },
  { week: "Week 3", grossSales: 295000, netPayout: 225000, fees: 70000 },
  { week: "Week 4", grossSales: 355000, netPayout: 277000, fees: 78000 },
  { week: "Week 5", grossSales: 320000, netPayout: 248000, fees: 72000 },
  { week: "Week 6", grossSales: 340000, netPayout: 265000, fees: 75000 }
];

const feeBreakdown = [
  { name: "Platform Fee", value: 22032, color: "#00A390" },
  { name: "Payment Gateway", value: 13152, color: "#DB3532" },
  { name: "Delivery Fee", value: 46290, color: "#28a745" },
  { name: "GST Withheld", value: 40720, color: "#ffc107" },
  { name: "TCS", value: 8145, color: "#6f42c1" }
];

const outletPerformance = [
  { outlet: "Downtown", grossSales: 437010, netPayout: 334227, totalFees: 102783 },
  { outlet: "Mall Road", grossSales: 376870, netPayout: 293233, totalFees: 83637 }
];

export const loader = withAuth(async ({ user }) => {
  const embedUrl = metabaseService.generateDashboardUrl(9, {
    seller_id_: user.sellerId,
  });

  return withResponse({ embedUrl });
});

export default function Payouts() {
  const { embedUrl } = useLoaderData<typeof loader>();
  const [selectedPayout, setSelectedPayout] = useState<typeof mockPayouts[0] | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [outletFilter, setOutletFilter] = useState("all");
  const [selectedTab, setSelectedTab] = useState("overview");

  const filteredPayouts = mockPayouts.filter(payout => {
    const matchesSearch = payout.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payout.bankRefId?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || payout.status.toLowerCase() === statusFilter;
    const matchesOutlet = outletFilter === "all" || payout.outlet.toLowerCase().replace(" ", "-") === outletFilter;

    return matchesSearch && matchesStatus && matchesOutlet;
  });

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Settled": return "default";
      case "Pending": return "secondary";
      case "Disputed": return "destructive";
      default: return "secondary";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Settled": return <CheckCircle className="h-3 w-3" />;
      case "Pending": return <Clock className="h-3 w-3" />;
      case "Disputed": return <AlertCircle className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  const calculateTotals = () => {
    return mockPayouts.reduce((acc, payout) => ({
      totalGrossSales: acc.totalGrossSales + payout.grossSales,
      totalNetPayout: acc.totalNetPayout + payout.netPayout,
      totalFees: acc.totalFees + (payout.platformFee + payout.pgFee + payout.deliveryFee + payout.gstWithheld + payout.tcs),
      pendingAmount: acc.pendingAmount + (payout.status === "Pending" ? payout.netPayout : 0)
    }), { totalGrossSales: 0, totalNetPayout: 0, totalFees: 0, pendingAmount: 0 });
  };

  const totals = calculateTotals();

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Total Gross Sales</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totals.totalGrossSales.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-green-600">+8.2%</span>
              <span className="ml-1">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Net Payouts</CardTitle>
            <Banknote className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totals.totalNetPayout.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-green-600">+6.8%</span>
              <span className="ml-1">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Total Fees & Charges</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totals.totalFees.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <span className="text-orange-600">{((totals.totalFees / totals.totalGrossSales) * 100).toFixed(1)}%</span>
              <span className="ml-1">of gross sales</span>
            </div>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Pending Payouts</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totals.pendingAmount.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <span>{mockPayouts.filter(p => p.status === "Pending").length}</span>
              <span className="ml-1">pending settlements</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payout Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-normal">Payout Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={payoutTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="week" />
                <YAxis />
                <Tooltip formatter={(value) => [`₹${value?.toLocaleString()}`, '']} />
                <Legend />
                <Area type="monotone" dataKey="grossSales" stackId="1" stroke="#00A390" fill="#00A390" fillOpacity={0.3} name="Gross Sales" />
                <Area type="monotone" dataKey="netPayout" stackId="2" stroke="#28a745" fill="#28a745" fillOpacity={0.3} name="Net Payout" />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Fee Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-normal">Fee Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={feeBreakdown}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ₹${value?.toLocaleString()}`}
                >
                  {feeBreakdown.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`₹${value?.toLocaleString()}`, '']} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Outlet Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-normal">Outlet Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {outletPerformance.map((outlet) => (
              <div key={outlet.outlet} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <Building2 className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium">{outlet.outlet}</h4>
                    <p className="text-sm text-muted-foreground">
                      {((outlet.netPayout / outlet.grossSales) * 100).toFixed(1)}% net payout ratio
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold">₹{outlet.netPayout.toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground">₹{outlet.grossSales.toLocaleString()} gross</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderPayoutHistory = () => (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by payout ID or bank reference..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="settled">Settled</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="disputed">Disputed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={outletFilter} onValueChange={setOutletFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by outlet" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Outlets</SelectItem>
                <SelectItem value="downtown">Downtown</SelectItem>
                <SelectItem value="mall-road">Mall Road</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Payout Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-black font-semibold">Payout ID</TableHead>
                <TableHead className="text-black font-semibold">Outlet</TableHead>
                <TableHead className="text-black font-semibold">Settlement Period</TableHead>
                <TableHead className="text-black font-semibold">Gross Sales</TableHead>
                <TableHead className="text-black font-semibold">Net Payout</TableHead>
                <TableHead className="text-black font-semibold">Status</TableHead>
                <TableHead className="text-black font-semibold">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPayouts.map((payout) => (
                <TableRow key={payout.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{payout.id}</p>
                      {payout.bankRefId && (
                        <p className="text-sm text-muted-foreground">{payout.bankRefId}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span>{payout.outlet}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="text-sm">{new Date(payout.settlementStart).toLocaleDateString()}</p>
                      <p className="text-sm text-muted-foreground">
                        to {new Date(payout.settlementEnd).toLocaleDateString()}
                      </p>
                      {payout.paidOn && (
                        <p className="text-xs text-green-600">
                          Paid: {new Date(payout.paidOn).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <p className="font-medium">₹{payout.grossSales.toLocaleString()}</p>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium text-green-600">₹{payout.netPayout.toLocaleString()}</p>
                      <p className="text-xs text-muted-foreground">
                        {((payout.netPayout / payout.grossSales) * 100).toFixed(1)}% of gross
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Badge variant={getStatusBadgeVariant(payout.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(payout.status)}
                          {payout.status}
                        </div>
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedPayout(payout)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Payouts</h1>
        <p className="text-gray-600 mt-2">Manage your earnings and payments</p>
      </div>

      <div>

        {/* Tabs */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="grid grid-cols-2 w-full lg:w-auto">
            <TabsTrigger value="overview" className="font-semibold">Overview</TabsTrigger>
            <TabsTrigger value="history" className="font-semibold">Payout History</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="w-full h-screen">
            {embedUrl ?
              (<iframe
                id="metabase-payouts-iframe"
                src={embedUrl}
                title="Payouts Dashboard"
                className="w-full h-full border-0"
              />) : renderOverview()
            }
          </TabsContent>

          <TabsContent value="history">
            {renderPayoutHistory()}
          </TabsContent>
        </Tabs>
        {(selectedTab === "history") && <ComingSoonOverlay />}

        {/* Payout Detail Dialog */}
        <Dialog open={!!selectedPayout} onOpenChange={() => setSelectedPayout(null)}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            {selectedPayout && (
              <>
                <DialogHeader>
                  <DialogTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <CreditCard className="h-6 w-6 text-primary" />
                      <div>
                        <h3>Payout {selectedPayout.id}</h3>
                        <p className="text-sm text-muted-foreground">{selectedPayout.outlet}</p>
                      </div>
                    </div>
                    <Badge variant={getStatusBadgeVariant(selectedPayout.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(selectedPayout.status)}
                        {selectedPayout.status}
                      </div>
                    </Badge>
                  </DialogTitle>
                  <DialogDescription>
                    Detailed breakdown of settlement period from {new Date(selectedPayout.settlementStart).toLocaleDateString()} to {new Date(selectedPayout.settlementEnd).toLocaleDateString()}
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                  {/* Settlement Summary */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Settlement Summary</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm text-muted-foreground">Settlement Period</label>
                          <p>{new Date(selectedPayout.settlementStart).toLocaleDateString()} - {new Date(selectedPayout.settlementEnd).toLocaleDateString()}</p>
                        </div>
                        <div>
                          <label className="text-sm text-muted-foreground">Payment Date</label>
                          <p>{selectedPayout.paidOn ? new Date(selectedPayout.paidOn).toLocaleDateString() : "Pending"}</p>
                        </div>
                        <div>
                          <label className="text-sm text-muted-foreground">Bank Reference</label>
                          <p>{selectedPayout.bankRefId || "Not Available"}</p>
                        </div>
                        <div>
                          <label className="text-sm text-muted-foreground">Outlet</label>
                          <p>{selectedPayout.outlet}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Financial Breakdown */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Revenue Breakdown</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex justify-between">
                          <span>Gross Sales</span>
                          <span className="font-medium">₹{selectedPayout.grossSales.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-red-600">
                          <span>Less: Discounts</span>
                          <span>-₹{selectedPayout.discounts.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-red-600">
                          <span>Less: Refunds</span>
                          <span>-₹{selectedPayout.refunds.toLocaleString()}</span>
                        </div>
                        <div className="border-t pt-2">
                          <div className="flex justify-between font-medium">
                            <span>Net Sales</span>
                            <span>₹{(selectedPayout.grossSales - selectedPayout.discounts - selectedPayout.refunds).toLocaleString()}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Fees & Charges</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex justify-between">
                          <span>Platform Fee (3%)</span>
                          <span className="text-red-600">₹{selectedPayout.platformFee.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Payment Gateway Fee</span>
                          <span className="text-red-600">₹{selectedPayout.pgFee.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Delivery Fee</span>
                          <span className="text-red-600">₹{selectedPayout.deliveryFee.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>GST Withheld</span>
                          <span className="text-red-600">₹{selectedPayout.gstWithheld.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>TCS (1%)</span>
                          <span className="text-red-600">₹{selectedPayout.tcs.toLocaleString()}</span>
                        </div>
                        <div className="border-t pt-2">
                          <div className="flex justify-between font-medium">
                            <span>Total Deductions</span>
                            <span className="text-red-600">
                              ₹{(selectedPayout.platformFee + selectedPayout.pgFee + selectedPayout.deliveryFee + selectedPayout.gstWithheld + selectedPayout.tcs).toLocaleString()}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Final Payout */}
                  <Card className="border-green-200 bg-green-50">
                    <CardContent className="pt-6">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-bold text-lg">Net Payout Amount</h3>
                          <p className="text-sm text-muted-foreground">
                            {((selectedPayout.netPayout / selectedPayout.grossSales) * 100).toFixed(1)}% of gross sales
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-3xl font-bold text-green-600">₹{selectedPayout.netPayout.toLocaleString()}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button className="flex-1">
                      <Download className="h-4 w-4 mr-2" />
                      Download Statement
                    </Button>
                    <Button variant="outline" className="flex-1">
                      <FileText className="h-4 w-4 mr-2" />
                      GST Invoice
                    </Button>
                    {selectedPayout.status === "Disputed" && (
                      <Button variant="destructive" className="flex-1">
                        <AlertCircle className="h-4 w-4 mr-2" />
                        Report Issue
                      </Button>
                    )}
                  </div>
                </div>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
} 