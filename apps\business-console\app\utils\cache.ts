/**
 * Cache Utility Service with Expiry Features
 * Supports both server-side and client-side caching
 */

import type { NetworkTheme } from "../types/api/common";
import { parseJWT } from "./token-utils";

export interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds (default: 5 minutes)
  maxSize?: number; // Maximum number of items in cache (default: 100)
  cleanupInterval?: number; // Cleanup interval in milliseconds (default: 10 minutes)
}

/**
 * Extract userId from JWT token for secure cache key generation
 */
export function extractUserIdFromToken(token: string | null): string | null {
  if (!token) return null;
  
  try {
    const tokenData = parseJWT(token);
    return tokenData?.userDetails?.userId?.toString() || null;
  } catch (error) {
    console.error('Error extracting userId from token:', error);
    return null;
  }
}

/**
 * Generate secure cache key using userId and domain
 */
export function generateCacheKey(domain: string, userId: string | null): string {
  if (userId) {
    return `theme:${domain}:user:${userId}`;
  }
  return `theme:${domain}:anonymous`;
}

export class CacheService<T = unknown> {
  private cache = new Map<string, CacheItem<T>>();
  private readonly defaultTTL: number;
  private readonly maxSize: number;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(options: CacheOptions = {}) {
    this.defaultTTL = options.ttl || 5 * 60 * 1000; // 5 minutes default
    this.maxSize = options.maxSize || 100;
    
    // Only start cleanup if interval is greater than 0
    const cleanupInterval = options.cleanupInterval || 10 * 60 * 1000; // 10 minutes default
    if (cleanupInterval > 0) {
      this.startCleanup(cleanupInterval);
    }
  }

  /**
   * Set a value in cache with optional TTL
   */
  set(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const itemTTL = ttl || this.defaultTTL;

    // Remove oldest items if cache is full
    while (this.cache.size >= this.maxSize) {
      this.evictOldest();
    }

    this.cache.set(key, {
      data,
      timestamp: now,
      ttl: itemTTL,
    });

    console.log(`Cache: Set key "${key}" with TTL ${itemTTL}ms`);
  }

  /**
   * Get a value from cache
   */
  get(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      console.log(`Cache: Miss for key "${key}"`);
      return null;
    }

    const now = Date.now();
    const isExpired = (now - item.timestamp) > item.ttl;

    if (isExpired) {
      console.log(`Cache: Expired for key "${key}"`);
      this.cache.delete(key);
      return null;
    }

    console.log(`Cache: Hit for key "${key}"`);
    return item.data;
  }

  /**
   * Check if a key exists and is not expired
   */
  has(key: string): boolean {
    const item = this.cache.get(key);
    
    if (!item) {
      return false;
    }

    const now = Date.now();
    const isExpired = (now - item.timestamp) > item.ttl;

    if (isExpired) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Delete a specific key from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      console.log(`Cache: Deleted key "${key}"`);
    }
    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    console.log('Cache: Cleared all entries');
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const [key, item] of this.cache.entries()) {
      const isExpired = (now - item.timestamp) > item.ttl;
      if (isExpired) {
        expiredEntries++;
        this.cache.delete(key);
      } else {
        validEntries++;
      }
    }

    return {
      total: this.cache.size,
      valid: validEntries,
      expired: expiredEntries,
      maxSize: this.maxSize,
    };
  }

  /**
   * Get or set with automatic fallback
   */
  async getOrSet(
    key: string, 
    fetchFn: () => Promise<T>, 
    ttl?: number
  ): Promise<T> {
    // Try to get from cache first
    const cached = this.get(key);
    if (cached !== null) {
      return cached;
    }

    // Fetch fresh data
    try {
      console.log(`Cache: Fetching fresh data for key "${key}"`);
      const data = await fetchFn();
      this.set(key, data, ttl);
      return data;
    } catch (error) {
      console.error(`Cache: Error fetching data for key "${key}":`, error);
      throw error;
    }
  }

  /**
   * Evict the oldest entry (LRU strategy)
   */
  private evictOldest(): void {
    // Find the oldest entry
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`Cache: Evicted oldest key "${oldestKey}"`);
    }
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, item] of this.cache.entries()) {
      const isExpired = (now - item.timestamp) > item.ttl;
      if (isExpired) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`Cache: Cleaned up ${cleanedCount} expired entries`);
    }
  }

  /**
   * Start automatic cleanup
   */
  private startCleanup(interval: number): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    if (interval > 0) {
      this.cleanupInterval = setInterval(() => {
        this.cleanup();
      }, interval);
    }
  }

  /**
   * Stop automatic cleanup
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
  }
}

// Pre-configured cache instances for common use cases
export const themeCache = new CacheService<NetworkTheme>({
  ttl: 15 * 60 * 1000, // 15 minutes
  maxSize: 50,
  cleanupInterval: 10 * 60 * 1000, // 10 minutes
});

export const apiCache = new CacheService<unknown>({
  ttl: 5 * 60 * 1000, // 5 minutes
  maxSize: 100,
  cleanupInterval: 5 * 60 * 1000, // 5 minutes
});

// Utility function for creating domain-specific caches
export function createDomainCache<T>(domain: string, options?: CacheOptions): CacheService<T> {
  return new CacheService<T>({
    ttl: 10 * 60 * 1000, // 10 minutes default
    maxSize: 20,
    cleanupInterval: 15 * 60 * 1000, // 15 minutes
    ...options,
  });
} 