import { json, LoaderFunction } from "@remix-run/node";
import {
  NavigateFunction,
  useFetcher,
  useLoaderData,
  useNavigate,
  useRevalidator,
} from "@remix-run/react";
import { format, parseISO } from "date-fns";
import { Edit2Icon, Trash2Icon, SearchIcon, XCircleIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Separator } from "~/components/ui/separator";
import { useToast } from "~/components/ui/ToastProvider";
import { addCoupon, deleteCoupon, getCoupons } from "~/services/coupons";
import { CouponDetailsType } from "~/types/api/businessConsoleService/coupons";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { camelCaseToWords } from "~/utils/format";

interface Loaderdata {
  data: CouponDetailsType[];
}

export const loader: LoaderFunction = withAuth(async ({ request }) => {
  try {
    const response = await getCoupons(request);
    return withResponse({ data: response.data }, response.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    throw new Response("Failed to fetch coupons data", {
      status: 500,
    });
  }
});

export const action = withAuth(async ({ request }) => {
  const formData = await request.formData();
  const actionType = formData.get("actionType");
  const couponId = Number(formData.get("couponId"));

  if (actionType === "deleteCoupon") {
    try {
      const response = await deleteCoupon(couponId, request);
      return withResponse(
        { data: response.data, actionType: actionType, success: true },
        response.headers
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }
  if (actionType === "addCoupon") {
    const payload = JSON.parse(formData.get("payload") as string);
    try {
      const response = await addCoupon(payload, request);
      return withResponse(
        { data: response.data, actionType: actionType, success: true },
        response.headers
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  console.log("Invalid action type:", actionType);
  return json(
    { data: null, actionType: actionType, success: false },
    { status: 400 }
  );
});

export function CouponCard({
  coupon,
  fetcher,
  navigate,
}: {
  coupon: CouponDetailsType;
  fetcher: ReturnType<typeof useFetcher>;
  navigate: NavigateFunction;
}) {
  return (
    <div className="p-4 rounded-xl shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)] hover:shadow-[0px_4px_12px_0px_rgba(0,0,0,0.15)] transition-shadow duration-200 bg-white border border-neutral-100">
      <div className="mb-3">
        <div className="flex flex-row justify-between items-center">
          <div className="p-1 border border-neutral-600 rounded-md bg-[#FCFCFD]">
            <h3 className="text-base font-semibold text-typography-400">
              {coupon.couponCode}
            </h3>
          </div>

          <span
            className={`px-2 py-1 rounded-md text-xs font-medium ${coupon.active
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
              }`}
          >
            {coupon.active === true
              ? "Active"
              : coupon.active === false
                ? "Expired"
                : ""}
          </span>
        </div>
        <p className="mt-2 text-sm text-typography-500 line-clamp-2">
          {coupon.description}
        </p>
      </div>

      <div className="border-b border-neutral-200" />

      <div className="my-3">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-typography-400 mb-1">Coupon Type</p>
            <p className="text-sm font-medium text-typography-700">
              {coupon.couponName
                ? camelCaseToWords(coupon.couponName)
                : "Not specified"}
            </p>
          </div>
          <div className="text-right">
            <p className="text-xs text-typography-400 mb-1">Valid Till</p>
            <p className="text-sm font-medium text-typography-700">
              {coupon.validTo
                ? format(parseISO(coupon.validTo), "dd MMMM yyyy")
                : "Not specified"}
            </p>
          </div>
        </div>
        <div className="mt-4 grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-typography-400 mb-1">Coupon Usage</p>
            <div className="flex flex-row items-center text-sm font-medium text-typography-700">
              <span className="mr-1">{coupon.totalUsers || "0"} Users</span>
              <Separator
                orientation="vertical"
                className="h-4 mx-1 bg-neutral-300"
              />
              <span className="ml-1">{coupon.totalOrders} Orders</span>
            </div>
          </div>
          <div className="text-right">
            <p className="text-xs text-typography-400 mb-1">Discount Amount</p>
            <p className="text-sm font-medium text-typography-700">
              ₹ {coupon.discountValue}
            </p>
          </div>
        </div>
      </div>

      <div className="border-b border-neutral-200" />

      <div className="relative mt-3 flex flex-row gap-3 justify-end">
        <button
          className="border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-primary-50 hover:border-primary-200 transition-colors duration-200"
          onClick={() => navigate(`/sellerSetting/coupon/${coupon.id}`)}
          aria-label="Edit coupon"
        >
          <Edit2Icon className="w-4 h-4 text-primary-600" />
        </button>
        <button
          className="border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-red-50 hover:border-red-200 transition-colors duration-200"
          onClick={() => {
            if (confirm("Are you sure you want to delete this coupon?")) {
              fetcher.submit(
                { actionType: "deleteCoupon", couponId: String(coupon.id) },
                { method: "DELETE" }
              );
            }
          }}
          aria-label="Delete coupon"
        >
          <Trash2Icon className="w-4 h-4 text-red-500" />
        </button>
      </div>
    </div>
  );
}

function QuickCouponCard({
  title,
  description,
  icon,
  defaultValue,
  onSubmit,
  isLoading,
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  defaultValue: string;
  onSubmit: (value: string) => void;
  isLoading: boolean;
}) {
  const [discountValue, setDiscountValue] = useState(defaultValue);

  return (
    <div className="p-6 rounded-xl border-2 border-dashed border-primary-200 bg-gradient-to-br from-primary-50 to-white hover:border-primary-300 transition-all duration-200">
      <div className="flex flex-col items-center text-center space-y-4">
        <div className="p-3 rounded-full bg-primary-100">
          {icon}
        </div>
        <div>
          <h3 className="text-lg font-semibold text-typography-700 mb-1">
            {title}
          </h3>
          <p className="text-sm text-typography-500">{description}</p>
        </div>
        <div className="flex items-center space-x-2 w-full max-w-xs">
          <Input
            type="number"
            value={discountValue}
            onChange={(e) => setDiscountValue(e.target.value)}
            className="text-center font-medium"
            min="1"
            max="100"
          />
          <span className="text-sm font-medium text-typography-600 whitespace-nowrap">% off</span>
        </div>
        <Button
          onClick={() => onSubmit(discountValue)}
          disabled={isLoading || !discountValue || Number(discountValue) <= 0}
          className="w-full bg-primary hover:bg-primary-600 text-white font-medium"
        >
          {isLoading ? "Creating..." : "Save Coupon"}
        </Button>
      </div>
    </div>
  );
}


export default function Coupons() {
  const { data: coupons } = useLoaderData<Loaderdata>();
  const navigate = useNavigate();
  const { revalidate } = useRevalidator();
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCoupons, setFilteredCoupons] = useState<CouponDetailsType[]>(
    coupons || []
  );

  const fetcher = useFetcher<{
    data: void;
    success: boolean;
    actionType: string;
  }>();
  const fetcher1 = useFetcher<{
    data: void;
    success: boolean;
    actionType: string;
  }>();
  const { showToast } = useToast();

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
  };

  const clearSearch = () => {
    setSearchTerm("");
  };

  useEffect(() => {
    if (!coupons) return;

    if (searchTerm.trim() === "") {
      setFilteredCoupons(coupons);
    } else {
      const term = searchTerm.toLowerCase().trim();
      const filtered = coupons.filter((coupon) =>
        coupon.couponCode.toLowerCase().includes(term)
      );
      setFilteredCoupons(filtered);
    }
  }, [searchTerm, coupons]);

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "deleteCoupon"
      ) {
        showToast("Coupon deleted successfully", "success");
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "deleteCoupon"
      ) {
        showToast("Failed to delete coupon", "error");
      } else if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "addCoupon"
      ) {
        showToast("Coupon created successfully", "success");
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "addCoupon"
      ) {
        showToast("Failed to create coupon", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);

  useEffect(() => {
    if (fetcher1.state === "idle" && fetcher1.data) {
      if (
        fetcher1.data.success === true &&
        fetcher1.data.actionType === "addCoupon"
      ) {
        showToast("Coupon created successfully", "success");
        revalidate();
      } else if (
        fetcher1.data.success === false &&
        fetcher1.data.actionType === "addCoupon"
      ) {
        showToast("Failed to create coupon", "error");
      }
    }
  }, [fetcher1.state, fetcher1.data]);

  const handleQuickCoupon = async (type: 'newCustomer' | 'repeating', discountValue: string) => {
    const configurations = {
      newCustomer: {
        code: "WELCOME_" + discountValue,
        name: "newCustomer",
        description: `Flat ${discountValue}% off for new customers`,
        discountConfiguration: [
          {
            property: "discountPercent",
            operator: "EQ",
            value: discountValue + ""
          }
        ],
        filterConfiguration: [],
        validityConfiguration: [
          {
            property: "firstNOrders",
            operator: "EQ",
            value: "1"
          },
        ]
      },
      repeating: {
        code: "REPEAT_" + discountValue,
        name: "highOrderValue",
        description: `Flat ${discountValue}% off for loyal customers`,
        discountConfiguration: [
          {
            property: "discountPercent",
            operator: "EQ",
            value: discountValue + ""
          }
        ],
        filterConfiguration: [
          {
            property: "orderCount",
            operator: "GTE",
            value: "2"
          }
        ],
        validityConfiguration: []
      }
    };

    if (type === "newCustomer") {
      fetcher.submit(
        {
          actionType: "addCoupon",
          payload: JSON.stringify(configurations[type])
        },
        { method: "POST" }
      );
    } else if (type === "repeating") {
      fetcher1.submit(
        {
          actionType: "addCoupon",
          payload: JSON.stringify(configurations[type])
        },
        { method: "POST" }
      );
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Coupons</h1>
        <p className="text-gray-600 mt-2">Manage your promotional offers and discounts</p>
      </div>
      
      <div className="mb-6">
        <div className="relative max-w-md">
          <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
            <SearchIcon className="h-4 w-4 text-muted-foreground" />
          </div>
          <Input
            type="text"
            placeholder="Search coupons by code..."
            className="pl-10 pr-10"
            value={searchTerm}
            onChange={handleSearchChange}
          />
          {searchTerm && (
            <div className="absolute inset-y-0 right-3 flex items-center">
              <button
                onClick={clearSearch}
                className="text-muted-foreground hover:text-foreground transition-colors"
                aria-label="Clear search"
              >
                <XCircleIcon className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
      </div>

      <div
        aria-labelledby="coupons-list"
        className="pb-20 md:pb-5"
      >
        {filteredCoupons.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {filteredCoupons.map((coupon) => (
              <CouponCard
                key={coupon.id}
                coupon={coupon}
                fetcher={fetcher}
                navigate={navigate}
              />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-10 text-center">
            {searchTerm ? (
              <>
                <p className="text-muted-foreground mb-2">
                  No coupons found matching &quot;{searchTerm}&quot;
                </p>
                <p className="text-muted-foreground text-sm">
                  Try a different search term or clear the search
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={clearSearch}
                >
                  Clear Search
                </Button>
              </>
            ) : (
              <>
                <div className="w-full max-w-4xl">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-semibold text-foreground mb-2">
                      Create Your First Coupon
                    </h3>
                    <p className="text-muted-foreground">
                      Get started with these popular coupon templates or create a custom one
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <QuickCouponCard
                      title="New Customer Discount"
                      description="Welcome first-time buyers with a special discount"
                      icon={
                        <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      }
                      defaultValue="30"
                      onSubmit={(value) => handleQuickCoupon('newCustomer', value)}
                      isLoading={fetcher.state === "submitting" || fetcher.state === "loading"}
                    />

                    <QuickCouponCard
                      title="Repeating Customer Discount"
                      description="Reward your returning customers for their loyalty"
                      icon={
                        <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      }
                      defaultValue="20"
                      onSubmit={(value) => handleQuickCoupon('repeating', value)}
                      isLoading={fetcher1.state === "submitting" || fetcher1.state === "loading"}
                    />
                  </div>

                  <div className="text-center">
                    <p className="text-muted-foreground text-sm mb-4">
                      Need more customization?
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => navigate("/sellerSetting/coupon/add")}
                      className="font-medium"
                    >
                      Create Custom Coupon
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </div>

      <div className="fixed bottom-0 left-0 right-0 z-30 p-4 flex justify-center md:static md:justify-end md:border-0">
        <Button
          onClick={() => navigate("/sellerSetting/coupon/add")}
          className="w-full md:w-auto"
        >
          Add New Coupon
        </Button>
      </div>
    </div>
  );
}
