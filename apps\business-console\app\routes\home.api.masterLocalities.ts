import { json } from "@remix-run/node";
import { getMasterLocalities } from "~/services/businessConsoleService";
import { createSellerArea } from "~/services/masterItemCategories";
import { withAuth, withResponse } from "~/utils/auth-utils";

export const loader = withAuth(async ({ request }) => {
  const url = new URL(request.url);
  const userId = Number(url.searchParams.get("userId"));
  const state = url.searchParams.get("state");
  const district = url.searchParams.get("district");
  let response;
  if (!state || !district) {
    return json({ error: "State and District are required" }, { status: 400 });
  }

  try {
    const masterLocalities = await getMasterLocalities(userId, state, district);
    return json({ masterLocalities });
  } catch (error) {
    console.error("Error fetching Master Localities:", error);
    return json(
      { error: "Fetching Master Localities failed" },
      { status: 500 }
    );
  }
});
