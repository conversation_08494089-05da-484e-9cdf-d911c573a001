import { json } from "@remix-run/node";
import s3Service from "~/services/s3.service";
import { withAuth } from "~/utils/auth-utils";

export const action = withAuth(async ({ user, request }) => {
      const formData = await request.formData();
      const intent = formData.get("_intent");

      if (formData.get("_action") === "uploadImage") {
            try {
                  const file = formData.get("file");
                  console.log("Received file:", {
                        type: file?.constructor.name,
                        isBlob: file instanceof Blob,
                        size: file instanceof Blob ? file.size : 'N/A',
                        contentType: file instanceof Blob ? file.type : 'N/A'
                  });

                  if (!file || !(file instanceof Blob)) {
                        return json({ success: false, error: "No file provided" }, { status: 400 });
                  }

                  // Validate file size
                  const MAX_FILE_SIZE = 500 * 1024; // 500KB
                  if (file.size > MAX_FILE_SIZE) {
                        return json({
                              success: false,
                              error: "File size exceeds 500KB limit"
                        }, { status: 400 });
                  }

                  // Read file as buffer
                  const arrayBuffer = await file.arrayBuffer();
                  const buffer = Buffer.from(arrayBuffer);

                  const fileUrl = await s3Service.uploadFile({
                        file: buffer,
                        fileName: (file as File).name || 'image.jpg',
                        contentType: file.type || 'image/jpeg',
                  });

                  return json({ success: true, fileUrl, intent });
            } catch (error) {
                  console.error("File upload error:", error);
                  if (error instanceof Error) {
                        return json({
                              success: false,
                              error: error.message || "Failed to upload file"
                        }, { status: 500 });
                  }
                  return json({
                        success: false,
                        error: "An unexpected error occurred while uploading the file"
                  }, { status: 500 });
            }
      }
})