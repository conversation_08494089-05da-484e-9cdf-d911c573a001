import jwt from "jsonwebtoken";

export interface MetabaseConfig {
  siteUrl: string;
  secretKey: string;
}

export interface MetabaseQuestionPayload {
  resource: { question: number };
  params?: Record<string, string | number | boolean>;
  exp?: number;
}

export interface MetabaseDashboardPayload {
  resource: { dashboard: number };
  params?: Record<string, string | number | boolean>;
  exp?: number;
}

export class MetabaseService {
  private config: MetabaseConfig;

  constructor(config: MetabaseConfig) {
    this.config = config;
  }

  /**
   * Generate a Metabase embed URL for a question
   */
  generateQuestionUrl(
    questionId: number,
    params: Record<string, string | number | boolean> = {},
    expirationMinutes: number = 10
  ): string {
    const payload: MetabaseQuestionPayload = {
      resource: { question: questionId },
      params,
      exp: Math.round(Date.now() / 1000) + (expirationMinutes * 60)
    };

    const token = jwt.sign(payload, this.config.secretKey);
    return `${this.config.siteUrl}/embed/question/${token}#bordered=true&titled=true`;
  }

  /**
   * Generate a Metabase embed URL for a dashboard
   */
  generateDashboardUrl(
    dashboardId: number,
    params: Record<string, string | number | boolean> = {},
    expirationMinutes: number = 10
  ): string {
    const payload: MetabaseDashboardPayload = {
      resource: { dashboard: dashboardId },
      params,
      exp: Math.round(Date.now() / 1000) + (expirationMinutes * 60)
    };

    const token = jwt.sign(payload, this.config.secretKey);
    return `${this.config.siteUrl}/embed/dashboard/${token}#bordered=true&titled=false`;
  }

  /**
   * Create a Metabase service instance with environment variables
   */
  static createFromEnv(): MetabaseService {
    const siteUrl = process.env.METABASE_SITE_URL;
    const secretKey = process.env.METABASE_SECRET_KEY;

    if (!siteUrl) {
      throw new Error("METABASE_SITE_URL environment variable is required");
    }

    if (!secretKey) {
      throw new Error("METABASE_SECRET_KEY environment variable is required");
    }

    return new MetabaseService({ siteUrl, secretKey });
  }
}

// Export a default instance for convenience
export const metabaseService = MetabaseService.createFromEnv(); 