import { Check, CirclePlus } from "lucide-react";
import { Input } from "./input";
import { Label } from "./label";
import { Dialog, DialogContent, DialogTrigger } from "./dialog";
import { But<PERSON> } from "./button";
import { useState, useRef, useEffect } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select";
import { useFetcher } from "@remix-run/react";
import { MasterItemCategories } from "~/types/api/businessConsoleService/MasterItemCategory";
import { CategoryItem, SearchableCategories } from "../masterItems/searchableCategories";
import { RadioGroup, RadioGroupItem } from "./radio-group";

interface AddMasterCategoryProps {
      categoryDetails?: MasterItemCategories;
      buttonName: string;
      handleSubmit: (ondcDomain: "RET10" | "RET11", level: number, name: string, sequence: number, picturUrl: string, ParentCategoryIds: CategoryItem[], closeDialog: () => void, mode?: string, icId?: number) => void;
      mode?: string
}
export default function AddMasterCategory({
      categoryDetails,
      buttonName,
      handleSubmit,
      mode
}: AddMasterCategoryProps) {
      const [ctnBtnClicked, setCtnBtnClicked] = useState(false);
      const [categoryName, setCategoryName] = useState<string | undefined>(mode === "Edit" ? categoryDetails?.name : "");

      const [uploadingImage, setUploadingImage] = useState(false);
      const [uploadError, setUploadError] = useState<string | null>(null);
      const [selectedFile, setSelectedFile] = useState<File | null>(null);
      const [filePreviewUrl, setFilePreviewUrl] = useState<string | null>(null); // Preview URL
      const [uploadedImageUrl, setUploadedImageUrl] = useState<string>(
            mode === "Edit" && categoryDetails?.picture ? categoryDetails.picture.toString() : ""
      ); const fileInputRef = useRef<HTMLInputElement>(null);
      const uploadFetcher = useFetcher<{ success: boolean; fileUrl: string; error?: string }>();
      const [selectedLevel, setSelectedLevel] = useState<string | undefined>(mode === "Edit" ? categoryDetails?.level.toString() : "");
      const [sequence, setSequence] = useState<string | undefined>(
            mode === "Edit" && categoryDetails?.sequence !== undefined
                  ? categoryDetails.sequence.toString()
                  : ""
      );
      const [ondcDomain, setOndcDomain] = useState<"RET10" | "RET11">(
            mode === "Edit" && categoryDetails?.ondcDomain ? categoryDetails.ondcDomain as "RET10" | "RET11" : "RET10"
      );

      const [images, setImages] = useState<any[]>([]);
      const [dialogOpen, setDialogOpen] = useState(false);

      const transformToCategoryItem = (parentCategories: MasterItemCategories[]): CategoryItem[] => {
            return parentCategories.map((category) => ({
                  value: category.id.toString(),
                  label: category.name,
                  numericId: category.id,
            }));
      };
      const [updatedCategory, setUpdatedCategory] = useState<CategoryItem[]>(() =>
            mode === "Edit" && categoryDetails?.parentCategories ? transformToCategoryItem(categoryDetails?.parentCategories) : []
      );
      const resetForm = () => {
            if (mode !== "Edit") {
                  setCategoryName("");
                  setSelectedLevel("");
                  setUpdatedCategory([]);
                  setSelectedFile(null);
                  setFilePreviewUrl(null);
                  setUploadedImageUrl("");
                  setUploadingImage(false);
                  setUploadError(null);
                  setCtnBtnClicked(false);
            }


      };

      const levels = [
            { id: "1", name: "1" },
            { id: "2", name: "2" },
            { id: "3", name: "3" },
      ];
      const handleSelectedLevel = (value: string) => {
            setSelectedLevel(value);

      };

      const isContinueDisabled = !categoryName || !selectedLevel;
      const isSubmitDisabled = () => {
            if (ondcDomain === "RET10") {
                  return !uploadedImageUrl || !categoryName || !selectedLevel;
            } else {
                  return !categoryName || !selectedLevel;
            }
      }
      const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
            const file = event.target.files?.[0];
            if (!file) return;

            // Reset error state
            setUploadError(null);

            // Validate file size (e.g., 5MB limit)
            const MAX_FILE_SIZE = 500 * 1024 * 1024; // 5MB
            if (file.size > MAX_FILE_SIZE) {
                  setUploadError("File size exceeds 5MB limit");
                  return;
            }

            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                  setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed");
                  return;
            }

            setSelectedFile(file);
            const reader = new FileReader();
            reader.onloadend = () => {
                  setFilePreviewUrl(reader.result as string); // Set the preview image URL
            };
            reader.readAsDataURL(file); // Read the file to generate a URL for the preview
      };

      const handleUpload = () => {
            if (!selectedFile) return;

            setUploadingImage(true);
            const formData = new FormData();
            formData.append("_action", "uploadImage");
            formData.append("file", selectedFile, selectedFile.name);

            uploadFetcher.submit(formData, {
                  method: "post",
                  encType: "multipart/form-data",
            });
      };

      const openFilePicker = () => {
            fileInputRef.current?.click();
      };
      useEffect(() => {
            if (uploadFetcher.data) {
                  if (uploadFetcher.data.error) {
                        setUploadError(uploadFetcher.data.error);
                        setUploadingImage(false);
                  } else if (uploadFetcher.data.fileUrl) {

                        setUploadedImageUrl(uploadFetcher.data.fileUrl);

                        const newImage = {
                              id: Date.now(),
                              url: uploadFetcher.data.fileUrl,
                              sequence: images.length + 1,
                              isDefault: false,
                        };

                        setImages((prevImages) => [...prevImages, newImage]);
                        setUploadingImage(false);
                        setUploadError(null);
                        setSelectedFile(null);
                        setFilePreviewUrl(null);
                        if (fileInputRef.current) {
                              fileInputRef.current.value = ''; // Reset file input
                        }
                  }
            }
      }, [uploadFetcher.data]);

      const closeDialog = () => {
            if (mode !== "Edit") {
                  setCategoryName("");
                  setSelectedLevel("");
                  setSelectedFile(null);
                  setUpdatedCategory([])
                  setFilePreviewUrl(null);
                  setUploadedImageUrl("");
                  setUploadingImage(false);
                  setUploadError(null);
                  setCtnBtnClicked(false);
                  setDialogOpen(false);
            }
            setDialogOpen(false);

      }
      const onHandleSubmit = () => {
            if (!selectedLevel || !categoryName) {
                  console.error("Required fields are missing.");
                  return;
            }

            handleSubmit(
                  ondcDomain,
                  parseInt(selectedLevel),
                  categoryName,
                  parseInt(sequence ?? "0"),
                  uploadedImageUrl,
                  updatedCategory,
                  closeDialog,
                  mode,
                  categoryDetails?.id
            );
      };

      const handleOndcDomainChange = (val: "RET10" | "RET11") => {
            setOndcDomain(val);
            if (val === "RET11") {
                  setSelectedLevel("1");
                  setUpdatedCategory([]);
            } else {
                  setSelectedLevel(mode === "Edit" ? categoryDetails?.level.toString() : "");
                  setUpdatedCategory(() =>
                        mode === "Edit" && categoryDetails?.parentCategories ? transformToCategoryItem(categoryDetails?.parentCategories) : []
                  );
            }
      };

      return (
            <div className="container mx-auto   ">
                  <Dialog open={dialogOpen} onOpenChange={(isOpen) => {
                        setDialogOpen(isOpen);
                        if (!isOpen) {
                              resetForm();
                        }
                  }}>
                        {mode === "Edit" ? <DialogTrigger asChild className="flex">
                              <Button
                                    size="sm"
                                    variant="secondary"

                              >
                                    Edit
                              </Button>

                        </DialogTrigger> : <DialogTrigger asChild className="flex">
                              <Button className="fixed bottom-5 right-5 rounded-full">+ {buttonName}</Button>

                        </DialogTrigger>}
                        <DialogContent
                              className="w-full sm:w-[400px] md:w-[600px] max-h-[90vh] overflow-y-auto absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                        >
                              <div className="grid gap-4">
                                    <div className="space-y-2">
                                          {mode === "Edit" ? <h4 className="font-medium leading-none text-center md:text-left">
                                                Edit Category
                                          </h4> : <h4 className="font-medium leading-none text-center md:text-left">
                                                Add Category
                                          </h4>}
                                    </div>
                                    <div className="grid gap-4">
                                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div className="space-y-2 md:col-span-2">
                                                      <Label htmlFor="ondcDomain">Business Type</Label>
                                                      <RadioGroup
                                                            value={ondcDomain}
                                                            onValueChange={(val: "RET10" | "RET11") => handleOndcDomainChange(val)}
                                                            className="grid grid-cols-3 gap-4 mt-1"
                                                      // disabled={mode === "Edit"}
                                                      >
                                                            <div className="flex items-center space-x-2">
                                                                  <RadioGroupItem id="type-RET11" value="RET11" />
                                                                  <Label htmlFor="type-RET11">Restaurant</Label>
                                                            </div>
                                                            <div className="flex items-center space-x-2">
                                                                  <RadioGroupItem id="type-RET10" value="RET10" />
                                                                  <Label htmlFor="type-RET10">Non-Restaurant</Label>
                                                            </div>
                                                      </RadioGroup>
                                                </div>
                                                <div className="space-y-2">
                                                      <Label htmlFor="categoryName">Category Name (Required)</Label>
                                                      <Input
                                                            id="categoryName"
                                                            placeholder="Enter category name"
                                                            className="w-full h-8"
                                                            required
                                                            value={categoryName}
                                                            onChange={(e) => setCategoryName(e.target.value)}
                                                            disabled={mode === "Edit"} />
                                                      <p className="text-sm text-slate-400 ">Enter the English Name</p>
                                                </div>
                                                {ondcDomain === "RET10" && <div className="space-y-2">
                                                      <Label htmlFor="categoryDetails">Category Details (Required)</Label>
                                                      <Select
                                                            value={selectedLevel}
                                                            onValueChange={handleSelectedLevel}
                                                            disabled={mode === "Edit"}
                                                      >
                                                            <SelectTrigger>
                                                                  <SelectValue placeholder="Select a Level" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                  {levels.map((x) => (
                                                                        <SelectItem value={x.id} key={x.id}>
                                                                              {x.name}
                                                                        </SelectItem>
                                                                  ))}
                                                            </SelectContent>
                                                      </Select>
                                                </div>}
                                                {ctnBtnClicked && (
                                                      <>
                                                            <div className="col-span-full">
                                                                  {selectedLevel !== "3" && ondcDomain === "RET10" && <SearchableCategories
                                                                        label="Parent Categories"
                                                                        apiUrl="/home/<USER>"

                                                                        selectedCategories={updatedCategory || []}
                                                                        onCategoryAdd={(categoryId, categoryName) => {

                                                                              setUpdatedCategory((prevUpdatedCategory) => [
                                                                                    ...(prevUpdatedCategory || []),
                                                                                    { numericId: categoryId, value: categoryName, label: "" }
                                                                              ]);
                                                                        }}
                                                                        onCategoryRemove={(categoryId) => {

                                                                              setUpdatedCategory((prevUpdatedCategory) =>
                                                                                    (prevUpdatedCategory || []).filter(
                                                                                          (cat) => cat.numericId !== categoryId
                                                                                    )
                                                                              );
                                                                        }}
                                                                        required={true}
                                                                        level={(Number(selectedLevel) || 0) + 1}
                                                                  />}
                                                            </div>
                                                            <div className="col-span-full">
                                                                  <Label>Category Image {ondcDomain === "RET10" ? "(Required)" : "(Optional)"}</Label>
                                                                  <div className="flex gap-2 items-center">
                                                                        <Input
                                                                              type="text"
                                                                              readOnly
                                                                              value={selectedFile?.name || "No file selected"}
                                                                              className="flex-grow"
                                                                              onClick={openFilePicker}
                                                                              style={{ cursor: 'pointer' }}
                                                                        />
                                                                        <input
                                                                              ref={fileInputRef}
                                                                              type="file"
                                                                              accept="image/*"
                                                                              onChange={handleFileSelect}
                                                                              className="hidden"
                                                                        />
                                                                        <Button
                                                                              type="button"
                                                                              onClick={selectedFile ? handleUpload : openFilePicker}
                                                                              disabled={uploadingImage}
                                                                        >
                                                                              {uploadingImage ? (
                                                                                    <span className="flex items-center gap-2">
                                                                                          <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                                                                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                                                                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                                                                                          </svg>
                                                                                          Uploading...
                                                                                    </span>
                                                                              ) : selectedFile ? (
                                                                                    'Upload'
                                                                              ) : (
                                                                                    'Add Image'
                                                                              )}
                                                                        </Button>
                                                                  </div>
                                                                  {uploadError && (
                                                                        <p className="text-red-500 mt-1 text-sm">{uploadError}</p>
                                                                  )}
                                                                  <div className="mt-2 space-y-2">
                                                                        {filePreviewUrl && (
                                                                              <div className="flex justify-center">
                                                                                    <img src={filePreviewUrl} alt="Image Preview" className="max-w-full h-20 object-cover rounded" />
                                                                              </div>
                                                                        )}
                                                                        {uploadedImageUrl && !filePreviewUrl && (
                                                                              <div className="flex justify-center">
                                                                                    <img src={uploadedImageUrl} alt="Uploaded Image" className="max-w-full h-20 object-cover rounded" />
                                                                              </div>
                                                                        )}
                                                                  </div>
                                                            </div>
                                                            {selectedLevel === "1" && <div className="space-y-2">
                                                                  <Label htmlFor="Sequence">Sequence (Required)</Label>
                                                                  <Input
                                                                        id="sequence"
                                                                        placeholder="Enter Sequence number"
                                                                        className="w-full h-8"
                                                                        required
                                                                        value={sequence}
                                                                        onChange={(e) => setSequence(e.target.value)}
                                                                        type="number"
                                                                  />
                                                            </div>}
                                                      </>
                                                )}
                                          </div>
                                    </div>
                              </div>
                              <div className="flex flex-col md:flex-row justify-end gap-2 mt-5">
                                    {!ctnBtnClicked && (
                                          <Button
                                                size="sm"
                                                className="w-full md:w-auto"
                                                disabled={isContinueDisabled}
                                                onClick={() => setCtnBtnClicked(true)}
                                          >
                                                Continue
                                          </Button>
                                    )}
                                    {ctnBtnClicked && (
                                          <Button
                                                size="sm"
                                                className="w-full md:w-auto"
                                                onClick={() => onHandleSubmit()}
                                                disabled={isSubmitDisabled()}>
                                                Submit
                                          </Button>
                                    )}
                              </div>
                        </DialogContent>
                  </Dialog>

            </div>
      );
}
