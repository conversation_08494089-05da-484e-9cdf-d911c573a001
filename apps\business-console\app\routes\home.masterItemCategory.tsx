import { j<PERSON>, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON><PERSON><PERSON>, useN<PERSON>gate, useSearchParams } from "@remix-run/react";
import { useEffect, useState } from "react";
import { CategoryItem } from "~/components/masterItems/searchableCategories";
import AddMasterCategory from "~/components/ui/addMasterCategory";
import { Input } from "~/components/ui/input";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "~/components/ui/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Tabs, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { useDebounce } from "~/hooks/useDebounce";
import { getMasterItemCategory, UpdateMasterItemCategory } from "~/services/masterItemCategories";
import s3Service from "~/services/s3.service";
import { MasterItemCategories } from "~/types/api/businessConsoleService/MasterItemCategory";
import { withAuth, withResponse } from "~/utils/auth-utils";

export const loader = withAuth(async ({ request }) => {
      const url = new URL(request.url);
      const tab = url.searchParams.get("tab") || "0" as unknown as number;
      const page = parseInt(url.searchParams.get("page") || "0", 10);
      const searchTerm = url.searchParams.get("searchTerm") || "";
      const isSearch = searchTerm !== "" ? true : false;
      const levelNumber = tab === "0" ? 0 : tab as number
      try {
            const MasterItemCategoryResponse = await getMasterItemCategory(request, page, 20, isSearch, levelNumber, searchTerm);
            return withResponse({
                  data: MasterItemCategoryResponse.data
            })
      }
      catch (error) {
            if (error instanceof Response && error.status === 404) {
                  throw json({ error: "MasterItemCategory pg Not found" }, { status: 404 });
            }
            throw new Response("Failed to fetch MasterItemCategory ", { status: 500 });
      }
})
export const action = withAuth(async ({ request }) => {
      const formData = await request.formData();
      const intent = formData.get("_intent");
      const ondcDomain = formData.get("ondcDomain") as "RET10" | "RET11";
      const categoryName = formData.get("categoryName") as string;
      const categoryLevel = formData.get("categoryLevel") as unknown as number;
      const sequence = formData.get("sequence") as unknown as number;

      const picture = formData.get("imageUrl") as string;
      const parentId = formData.get("parentId");
      const icId = formData.get("icId") as unknown as number;
      const mode = formData.get("mode") as string;
      const parsedParentCat = parentId ? JSON.parse(parentId as string) : [];

      if (formData.get("_action") === "uploadImage") {
            try {
                  const file = formData.get("file");
                  console.log("Received file:", {
                        type: file?.constructor.name,
                        isBlob: file instanceof Blob,
                        size: file instanceof Blob ? file.size : 'N/A',
                        contentType: file instanceof Blob ? file.type : 'N/A'
                  });

                  if (!file || !(file instanceof Blob)) {
                        return json({ success: false, error: "No file provided" }, { status: 400 });
                  }

                  // Validate file size
                  const MAX_FILE_SIZE = 500 * 1024 * 1024; // 5MB
                  if (file.size > MAX_FILE_SIZE) {
                        return json({
                              success: false,
                              error: "File size exceeds 5MB limit"
                        }, { status: 400 });
                  }

                  // Read file as buffer
                  const arrayBuffer = await file.arrayBuffer();
                  const buffer = Buffer.from(arrayBuffer);

                  const fileUrl = await s3Service.uploadFile({
                        file: buffer,
                        fileName: (file as File).name || 'image.jpg',
                        contentType: file.type || 'image/jpeg',
                  });

                  return json({ success: true, fileUrl });
            } catch (error) {
                  console.error("File upload error:", error);
                  if (error instanceof Error) {
                        return json({
                              success: false,
                              error: error.message || "Failed to upload file"
                        }, { status: 500 });
                  }
                  return json({
                        success: false,
                        error: "An unexpected error occurred while uploading the file"
                  }, { status: 500 });
            }
      }
      else if (intent === "_createCategory") {
            try {
                  const response = await UpdateMasterItemCategory(ondcDomain, categoryName, categoryLevel, categoryLevel === 1 ? sequence : 0, picture, parsedParentCat, request, mode, icId);
                  return withResponse({ data: response.data }, response.headers);

            } catch (error) {
                  return json({ error: "Something went wrong" }, { status: 500 });
            }
      }

      return json({ error: "No valid action found" }, { status: 400 });
});




export default function MasterItemCategory() {
      const navigate = useNavigate();
      const [searchParams] = useSearchParams();
      const categories = useLoaderData<{ data: MasterItemCategories[] }>();
      const currentPage = parseInt(searchParams.get("page") || "0", 10);
      const activeTab = searchParams.get("tab") || "0";


      const [searchTerm, setSearchTerm] = useState("");

      const debouncedSearchTerm = useDebounce(searchTerm, 500)

      const fetcher = useFetcher()

      const handleTabChange = (newTab: string) => {
            navigate(`?tab=${newTab}&page=0&searchTerm=${searchTerm}`);
      };

      const handlePageChange = (newPage: number) => {
            navigate(`?tab=${activeTab}&page=${newPage}&searchTerm=${searchTerm}`);
      };

      useEffect(() => {
            if (debouncedSearchTerm.length >= 3) {
                  navigate(`?tab=${activeTab}&page=${currentPage}&searchTerm=${debouncedSearchTerm.toLocaleLowerCase()}`);
            }
            else if (debouncedSearchTerm === "") {
                  navigate(`?tab=${activeTab}&page=${currentPage}`)

            }
      }, [debouncedSearchTerm, navigate, activeTab, currentPage])


      const handleSubmit = (ondcDomain: "RET10" | "RET11", selectedLevel: number, categoryName: string, sequence: number, uploadedImageUrl: string, parentId: CategoryItem[], closeDialog: () => void, mode?: string, icId?: number) => {
            const formData = new FormData();
            formData.append("ondcDomain", ondcDomain)
            formData.append("categoryName", categoryName)
            formData.append("categoryLevel", selectedLevel.toString())
            formData.append("sequence", sequence.toString())

            formData.append("imageUrl", uploadedImageUrl)
            formData.append("parentId", JSON.stringify(parentId));
            formData.append("icId", icId as unknown as string);
            formData.append("mode", mode as string);
            formData.append("_intent", "_createCategory")
            fetcher.submit(formData, { method: "POST" })

            closeDialog()
      }

      return (
            <div className="container mx-auto p-6">
                  <div className="flex justify-between items-center mb-4">
                        <h1 className="text-2xl font-bold">Categories</h1>
                  </div>
                  <div className="flex flex-wrap justify-between items-start gap-4 mb-2">
                        <div className="w-full md:w-auto">
                              < p className="flex flex-wrap">Type</p>
                              <Tabs value={activeTab} onValueChange={handleTabChange} className="mb-6">
                                    <TabsList>
                                          <TabsTrigger value="1">Level1</TabsTrigger>
                                          <TabsTrigger value="2">Level2</TabsTrigger>
                                          <TabsTrigger value="3">Level3</TabsTrigger>
                                          <TabsTrigger value="0">All</TabsTrigger>
                                    </TabsList>
                              </Tabs>
                        </div>
                        <div className=" flex flex-wrap justify-center gap-4 items-center">
                              <Input
                                    placeholder="Search by Name"
                                    value={searchTerm}
                                    onChange={(e) => {
                                          setSearchTerm(e.target.value)

                                    }}
                                    type="search"
                              />
                              {/* <ListFilter size={45} /> */}
                        </div>
                  </div>
                  <Table>
                        <TableHeader>
                              <TableRow className="border-b bg-gray-100">
                                    <TableHead className="font-bold">Category Id</TableHead>
                                    <TableHead className="cursor-pointer font-bold">Category Name</TableHead>
                                    <TableHead className="font-bold">Category Level</TableHead>
                                    <TableHead className="font-bold">Parent Category</TableHead>
                                    <TableHead className="font-bold">Category Details</TableHead>
                                    <TableHead className="font-bold">Actions</TableHead>
                              </TableRow>
                        </TableHeader>
                        <TableBody>
                              {categories?.data.length === 0 ?
                                    <TableRow>
                                          <TableCell className="py-4 text-center align-middle" colSpan={100}>
                                                No Categories Found
                                          </TableCell>
                                    </TableRow>

                                    :
                                    categories?.data?.map((item) => (
                                          <TableRow key={item.id}>
                                                <TableCell>{item.id}</TableCell>
                                                <TableCell
                                                      className={`cursor-pointer text-blue-500 font-bold  items-center`}
                                                      onClick={() =>
                                                            navigate(`/home/<USER>
                                                      }
                                                >
                                                      <div className="flex gap-2 items-center">
                                                            <img src={item?.picture} alt="" className="w-12 h-12 object-cover" />    {item.name}

                                                      </div>
                                                </TableCell>
                                                {/* <TableCell><span className={`px-2 py-1 rounded-full text-xs ${x.disabled ? 'bg-red-100 text-red-800' : 'bg-green-200 text-green-800'}`}>{item?.disabled === true ? "Disabled" : "Active"}</span></TableCell> */}
                                                <TableCell>
                                                      {item.level === 1 ? "L1" : item.level === 2 ? "L2" : item.level === 3 ? "L3" : "-"}
                                                </TableCell>

                                                <TableCell>
                                                      <div className={`flex flex-col gap-1  `}>{item?.parentCategories?.length > 0
                                                            ? item?.parentCategories.map((parent) =>

                                                                  <span key={parent.id}><span className={`${parent.disabled ? 'text-red-600 font-bold' : ''} items-center`}>{parent?.name}</span></span>)
                                                            : "-"}
                                                      </div>
                                                </TableCell>
                                                <TableCell>
                                                      <div className="text-xs">
                                                            <span className="font-medium">Items:</span> {item.totalItems || '-'}
                                                      </div>
                                                      <div className="text-xs">
                                                            <span className="font-medium">Sequence:</span> {item.sequence || '-'}
                                                      </div>
                                                      {item?.disabled && <div className="text-xs">
                                                            <span className=" text-red-600 font-bold">Disabled</span>
                                                      </div>}
                                                      {/* {item?.disabled && <span className={`px-2 py-1 rounded-full text-xs bg-red-100 text-red-800 }`}>Disabled</span>} */}


                                                </TableCell>
                                                <TableCell>
                                                      <AddMasterCategory buttonName="Add New Category" categoryDetails={item}
                                                            handleSubmit={handleSubmit} mode="Edit" />
                                                </TableCell>
                                          </TableRow>
                                    ))}
                        </TableBody>
                  </Table>
                  <div className="flex justify-between items-center mt-6 overflow-hidden">
                        <Pagination>
                              <PaginationContent>
                                    {currentPage > 0 && (
                                          <PaginationItem>
                                                <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} className="cursor-pointer" />
                                          </PaginationItem>
                                    )}
                                    <PaginationItem className="cursor-pointer">
                                          <PaginationLink>{currentPage + 1}</PaginationLink>
                                    </PaginationItem>
                                    <PaginationItem>
                                          <PaginationNext onClick={() => handlePageChange(currentPage + 1)} className="cursor-pointer" />
                                    </PaginationItem>
                              </PaginationContent>
                        </Pagination>
                        <AddMasterCategory buttonName="Add New Category"
                              handleSubmit={handleSubmit} />
                  </div>
            </div>
      );
}