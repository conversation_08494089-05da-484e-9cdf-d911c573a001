# Metabase Utility

This utility provides a clean interface for generating Metabase embed URLs for both questions and dashboards.

## Usage

### Basic Usage

```typescript
import { metabaseService } from "~/utils/metabase";

// Generate a dashboard URL
const dashboardUrl = metabaseService.generateDashboardUrl(10);

// Generate a question URL with parameters
const questionUrl = metabaseService.generateQuestionUrl(70, {
  seller_id: "123",
  order_date: "2024-01-01"
});
```

### In a Remix Loader

```typescript
import { json } from "@remix-run/node";
import { metabaseService } from "~/utils/metabase";

export async function loader() {
  const embedUrl = metabaseService.generateDashboardUrl(10);
  return json({ embedUrl });
}
```

### In a Remix Component

```typescript
import { useLoaderData } from "@remix-run/react";

export default function Dashboard() {
  const { embedUrl } = useLoaderData<typeof loader>();

  return (
    <div className="w-full h-screen">
      <iframe
        src={embedUrl}
        title="Dashboard"
        className="w-full h-full border-0"
      />
    </div>
  );
}
```

## Configuration

The utility uses environment variables:

- `METABASE_SITE_URL`: Your Metabase instance URL
- `METABASE_SECRET_KEY`: Your Metabase secret key for signing tokens

## Methods

### `generateDashboardUrl(dashboardId, params?, expirationMinutes?)`

Generates an embed URL for a Metabase dashboard.

- `dashboardId`: The ID of the dashboard
- `params`: Optional parameters to pass to the dashboard
- `expirationMinutes`: Token expiration time (default: 10 minutes)

### `generateQuestionUrl(questionId, params?, expirationMinutes?)`

Generates an embed URL for a Metabase question.

- `questionId`: The ID of the question
- `params`: Optional parameters to pass to the question
- `expirationMinutes`: Token expiration time (default: 10 minutes)

## Examples

### Dashboard with Parameters

```typescript
const url = metabaseService.generateDashboardUrl(10, {
  seller_id: "123",
  date_range: "last_30_days"
});
```

### Question with Custom Expiration

```typescript
const url = metabaseService.generateQuestionUrl(70, {
  seller_id: "123"
}, 30); // 30 minutes expiration
```

### Custom Configuration

```typescript
import { MetabaseService } from "~/utils/metabase";

const customService = new MetabaseService({
  siteUrl: "https://your-metabase-instance.com",
  secretKey: "your-secret-key"
});

const url = customService.generateDashboardUrl(10);
``` 