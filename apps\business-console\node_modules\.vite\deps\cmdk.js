"use client";
import {
  Content,
  Overlay,
  Portal,
  Root
} from "./chunk-ZQ5G6WG2.js";
import "./chunk-KGZPLPTS.js";
import {
  useId
} from "./chunk-6QRTJNMX.js";
import "./chunk-7YBUH5AU.js";
import "./chunk-RHMROCXN.js";
import "./chunk-HU5GIT22.js";
import "./chunk-POSTA5CJ.js";
import {
  Primitive
} from "./chunk-5PWFSGIO.js";
import "./chunk-76VWABPJ.js";
import "./chunk-7D3DGLAV.js";
import "./chunk-RXX2RYKY.js";
import {
  require_react
} from "./chunk-PW5ESXJ3.js";
import {
  __commonJS,
  __toESM
} from "./chunk-N5SXXOWC.js";

// node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js
var require_use_sync_external_store_shim_development = __commonJS({
  "node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"(exports) {
    "use strict";
    (function() {
      function is(x, y) {
        return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;
      }
      function useSyncExternalStore$2(subscribe, getSnapshot) {
        didWarnOld18Alpha || void 0 === React.startTransition || (didWarnOld18Alpha = true, console.error(
          "You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release."
        ));
        var value = getSnapshot();
        if (!didWarnUncachedGetSnapshot) {
          var cachedValue = getSnapshot();
          objectIs(value, cachedValue) || (console.error(
            "The result of getSnapshot should be cached to avoid an infinite loop"
          ), didWarnUncachedGetSnapshot = true);
        }
        cachedValue = useState2({
          inst: { value, getSnapshot }
        });
        var inst = cachedValue[0].inst, forceUpdate = cachedValue[1];
        useLayoutEffect2(
          function() {
            inst.value = value;
            inst.getSnapshot = getSnapshot;
            checkIfSnapshotChanged(inst) && forceUpdate({ inst });
          },
          [subscribe, value, getSnapshot]
        );
        useEffect2(
          function() {
            checkIfSnapshotChanged(inst) && forceUpdate({ inst });
            return subscribe(function() {
              checkIfSnapshotChanged(inst) && forceUpdate({ inst });
            });
          },
          [subscribe]
        );
        useDebugValue(value);
        return value;
      }
      function checkIfSnapshotChanged(inst) {
        var latestGetSnapshot = inst.getSnapshot;
        inst = inst.value;
        try {
          var nextValue = latestGetSnapshot();
          return !objectIs(inst, nextValue);
        } catch (error) {
          return true;
        }
      }
      function useSyncExternalStore$1(subscribe, getSnapshot) {
        return getSnapshot();
      }
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
      var React = require_react(), objectIs = "function" === typeof Object.is ? Object.is : is, useState2 = React.useState, useEffect2 = React.useEffect, useLayoutEffect2 = React.useLayoutEffect, useDebugValue = React.useDebugValue, didWarnOld18Alpha = false, didWarnUncachedGetSnapshot = false, shim = "undefined" === typeof window || "undefined" === typeof window.document || "undefined" === typeof window.document.createElement ? useSyncExternalStore$1 : useSyncExternalStore$2;
      exports.useSyncExternalStore = void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
    })();
  }
});

// node_modules/use-sync-external-store/shim/index.js
var require_shim = __commonJS({
  "node_modules/use-sync-external-store/shim/index.js"(exports, module) {
    "use strict";
    if (false) {
      module.exports = null;
    } else {
      module.exports = require_use_sync_external_store_shim_development();
    }
  }
});

// node_modules/cmdk/dist/chunk-NZJY6EH4.mjs
var U = 1;
var Y = 0.9;
var H = 0.8;
var J = 0.17;
var p = 0.1;
var u = 0.999;
var $ = 0.9999;
var k = 0.99;
var m = /[\\\/_+.#"@\[\(\{&]/;
var B = /[\\\/_+.#"@\[\(\{&]/g;
var K = /[\s-]/;
var X = /[\s-]/g;
function G(_, C, h, P, A, f, O) {
  if (f === C.length) return A === _.length ? U : k;
  var T2 = `${A},${f}`;
  if (O[T2] !== void 0) return O[T2];
  for (var L = P.charAt(f), c = h.indexOf(L, A), S = 0, E, N2, R, M2; c >= 0; ) E = G(_, C, h, P, c + 1, f + 1, O), E > S && (c === A ? E *= U : m.test(_.charAt(c - 1)) ? (E *= H, R = _.slice(A, c - 1).match(B), R && A > 0 && (E *= Math.pow(u, R.length))) : K.test(_.charAt(c - 1)) ? (E *= Y, M2 = _.slice(A, c - 1).match(X), M2 && A > 0 && (E *= Math.pow(u, M2.length))) : (E *= J, A > 0 && (E *= Math.pow(u, c - A))), _.charAt(c) !== C.charAt(f) && (E *= $)), (E < p && h.charAt(c - 1) === P.charAt(f + 1) || P.charAt(f + 1) === P.charAt(f) && h.charAt(c - 1) !== P.charAt(f)) && (N2 = G(_, C, h, P, c + 1, f + 2, O), N2 * p > E && (E = N2 * p)), E > S && (S = E), c = h.indexOf(L, c + 1);
  return O[T2] = S, S;
}
function D(_) {
  return _.toLowerCase().replace(X, " ");
}
function W(_, C, h) {
  return _ = h && h.length > 0 ? `${_ + " " + h.join(" ")}` : _, G(_, C, D(_), D(C), 0, 0, {});
}

// node_modules/cmdk/dist/index.mjs
var n = __toESM(require_react(), 1);
var import_shim = __toESM(require_shim(), 1);
var N = '[cmdk-group=""]';
var Q = '[cmdk-group-items=""]';
var be = '[cmdk-group-heading=""]';
var Z = '[cmdk-item=""]';
var le = `${Z}:not([aria-disabled="true"])`;
var Y2 = "cmdk-item-select";
var I = "data-value";
var he = (r, o, t) => W(r, o, t);
var ue = n.createContext(void 0);
var K2 = () => n.useContext(ue);
var de = n.createContext(void 0);
var ee = () => n.useContext(de);
var fe = n.createContext(void 0);
var me = n.forwardRef((r, o) => {
  let t = k2(() => {
    var e, s;
    return { search: "", value: (s = (e = r.value) != null ? e : r.defaultValue) != null ? s : "", filtered: { count: 0, items: /* @__PURE__ */ new Map(), groups: /* @__PURE__ */ new Set() } };
  }), u2 = k2(() => /* @__PURE__ */ new Set()), c = k2(() => /* @__PURE__ */ new Map()), d = k2(() => /* @__PURE__ */ new Map()), f = k2(() => /* @__PURE__ */ new Set()), p2 = pe(r), { label: v, children: b, value: l, onValueChange: y, filter: E, shouldFilter: C, loop: H2, disablePointerSelection: ge = false, vimBindings: $2 = true, ...O } = r, te = useId(), B2 = useId(), F = useId(), x = n.useRef(null), R = Te();
  M(() => {
    if (l !== void 0) {
      let e = l.trim();
      t.current.value = e, h.emit();
    }
  }, [l]), M(() => {
    R(6, re);
  }, []);
  let h = n.useMemo(() => ({ subscribe: (e) => (f.current.add(e), () => f.current.delete(e)), snapshot: () => t.current, setState: (e, s, i) => {
    var a, m2, g;
    if (!Object.is(t.current[e], s)) {
      if (t.current[e] = s, e === "search") W2(), U2(), R(1, z);
      else if (e === "value" && (i || R(5, re), ((a = p2.current) == null ? void 0 : a.value) !== void 0)) {
        let S = s != null ? s : "";
        (g = (m2 = p2.current).onValueChange) == null || g.call(m2, S);
        return;
      }
      h.emit();
    }
  }, emit: () => {
    f.current.forEach((e) => e());
  } }), []), q = n.useMemo(() => ({ value: (e, s, i) => {
    var a;
    s !== ((a = d.current.get(e)) == null ? void 0 : a.value) && (d.current.set(e, { value: s, keywords: i }), t.current.filtered.items.set(e, ne(s, i)), R(2, () => {
      U2(), h.emit();
    }));
  }, item: (e, s) => (u2.current.add(e), s && (c.current.has(s) ? c.current.get(s).add(e) : c.current.set(s, /* @__PURE__ */ new Set([e]))), R(3, () => {
    W2(), U2(), t.current.value || z(), h.emit();
  }), () => {
    d.current.delete(e), u2.current.delete(e), t.current.filtered.items.delete(e);
    let i = A();
    R(4, () => {
      W2(), (i == null ? void 0 : i.getAttribute("id")) === e && z(), h.emit();
    });
  }), group: (e) => (c.current.has(e) || c.current.set(e, /* @__PURE__ */ new Set()), () => {
    d.current.delete(e), c.current.delete(e);
  }), filter: () => p2.current.shouldFilter, label: v || r["aria-label"], getDisablePointerSelection: () => p2.current.disablePointerSelection, listId: te, inputId: F, labelId: B2, listInnerRef: x }), []);
  function ne(e, s) {
    var a, m2;
    let i = (m2 = (a = p2.current) == null ? void 0 : a.filter) != null ? m2 : he;
    return e ? i(e, t.current.search, s) : 0;
  }
  function U2() {
    if (!t.current.search || p2.current.shouldFilter === false) return;
    let e = t.current.filtered.items, s = [];
    t.current.filtered.groups.forEach((a) => {
      let m2 = c.current.get(a), g = 0;
      m2.forEach((S) => {
        let P = e.get(S);
        g = Math.max(P, g);
      }), s.push([a, g]);
    });
    let i = x.current;
    _().sort((a, m2) => {
      var P, V;
      let g = a.getAttribute("id"), S = m2.getAttribute("id");
      return ((P = e.get(S)) != null ? P : 0) - ((V = e.get(g)) != null ? V : 0);
    }).forEach((a) => {
      let m2 = a.closest(Q);
      m2 ? m2.appendChild(a.parentElement === m2 ? a : a.closest(`${Q} > *`)) : i.appendChild(a.parentElement === i ? a : a.closest(`${Q} > *`));
    }), s.sort((a, m2) => m2[1] - a[1]).forEach((a) => {
      var g;
      let m2 = (g = x.current) == null ? void 0 : g.querySelector(`${N}[${I}="${encodeURIComponent(a[0])}"]`);
      m2 == null || m2.parentElement.appendChild(m2);
    });
  }
  function z() {
    let e = _().find((i) => i.getAttribute("aria-disabled") !== "true"), s = e == null ? void 0 : e.getAttribute(I);
    h.setState("value", s || void 0);
  }
  function W2() {
    var s, i, a, m2;
    if (!t.current.search || p2.current.shouldFilter === false) {
      t.current.filtered.count = u2.current.size;
      return;
    }
    t.current.filtered.groups = /* @__PURE__ */ new Set();
    let e = 0;
    for (let g of u2.current) {
      let S = (i = (s = d.current.get(g)) == null ? void 0 : s.value) != null ? i : "", P = (m2 = (a = d.current.get(g)) == null ? void 0 : a.keywords) != null ? m2 : [], V = ne(S, P);
      t.current.filtered.items.set(g, V), V > 0 && e++;
    }
    for (let [g, S] of c.current) for (let P of S) if (t.current.filtered.items.get(P) > 0) {
      t.current.filtered.groups.add(g);
      break;
    }
    t.current.filtered.count = e;
  }
  function re() {
    var s, i, a;
    let e = A();
    e && (((s = e.parentElement) == null ? void 0 : s.firstChild) === e && ((a = (i = e.closest(N)) == null ? void 0 : i.querySelector(be)) == null || a.scrollIntoView({ block: "nearest" })), e.scrollIntoView({ block: "nearest" }));
  }
  function A() {
    var e;
    return (e = x.current) == null ? void 0 : e.querySelector(`${Z}[aria-selected="true"]`);
  }
  function _() {
    var e;
    return Array.from(((e = x.current) == null ? void 0 : e.querySelectorAll(le)) || []);
  }
  function J2(e) {
    let i = _()[e];
    i && h.setState("value", i.getAttribute(I));
  }
  function X2(e) {
    var g;
    let s = A(), i = _(), a = i.findIndex((S) => S === s), m2 = i[a + e];
    (g = p2.current) != null && g.loop && (m2 = a + e < 0 ? i[i.length - 1] : a + e === i.length ? i[0] : i[a + e]), m2 && h.setState("value", m2.getAttribute(I));
  }
  function oe(e) {
    let s = A(), i = s == null ? void 0 : s.closest(N), a;
    for (; i && !a; ) i = e > 0 ? Ie(i, N) : Me(i, N), a = i == null ? void 0 : i.querySelector(le);
    a ? h.setState("value", a.getAttribute(I)) : X2(e);
  }
  let ie = () => J2(_().length - 1), ae = (e) => {
    e.preventDefault(), e.metaKey ? ie() : e.altKey ? oe(1) : X2(1);
  }, se = (e) => {
    e.preventDefault(), e.metaKey ? J2(0) : e.altKey ? oe(-1) : X2(-1);
  };
  return n.createElement(Primitive.div, { ref: o, tabIndex: -1, ...O, "cmdk-root": "", onKeyDown: (e) => {
    var s;
    if ((s = O.onKeyDown) == null || s.call(O, e), !e.defaultPrevented) switch (e.key) {
      case "n":
      case "j": {
        $2 && e.ctrlKey && ae(e);
        break;
      }
      case "ArrowDown": {
        ae(e);
        break;
      }
      case "p":
      case "k": {
        $2 && e.ctrlKey && se(e);
        break;
      }
      case "ArrowUp": {
        se(e);
        break;
      }
      case "Home": {
        e.preventDefault(), J2(0);
        break;
      }
      case "End": {
        e.preventDefault(), ie();
        break;
      }
      case "Enter":
        if (!e.nativeEvent.isComposing && e.keyCode !== 229) {
          e.preventDefault();
          let i = A();
          if (i) {
            let a = new Event(Y2);
            i.dispatchEvent(a);
          }
        }
    }
  } }, n.createElement("label", { "cmdk-label": "", htmlFor: q.inputId, id: q.labelId, style: Le }, v), j(r, (e) => n.createElement(de.Provider, { value: h }, n.createElement(ue.Provider, { value: q }, e))));
});
var ye = n.forwardRef((r, o) => {
  var F, x;
  let t = useId(), u2 = n.useRef(null), c = n.useContext(fe), d = K2(), f = pe(r), p2 = (x = (F = f.current) == null ? void 0 : F.forceMount) != null ? x : c == null ? void 0 : c.forceMount;
  M(() => {
    if (!p2) return d.item(t, c == null ? void 0 : c.id);
  }, [p2]);
  let v = ve(t, u2, [r.value, r.children, u2], r.keywords), b = ee(), l = T((R) => R.value && R.value === v.current), y = T((R) => p2 || d.filter() === false ? true : R.search ? R.filtered.items.get(t) > 0 : true);
  n.useEffect(() => {
    let R = u2.current;
    if (!(!R || r.disabled)) return R.addEventListener(Y2, E), () => R.removeEventListener(Y2, E);
  }, [y, r.onSelect, r.disabled]);
  function E() {
    var R, h;
    C(), (h = (R = f.current).onSelect) == null || h.call(R, v.current);
  }
  function C() {
    b.setState("value", v.current, true);
  }
  if (!y) return null;
  let { disabled: H2, value: ge, onSelect: $2, forceMount: O, keywords: te, ...B2 } = r;
  return n.createElement(Primitive.div, { ref: G2([u2, o]), ...B2, id: t, "cmdk-item": "", role: "option", "aria-disabled": !!H2, "aria-selected": !!l, "data-disabled": !!H2, "data-selected": !!l, onPointerMove: H2 || d.getDisablePointerSelection() ? void 0 : C, onClick: H2 ? void 0 : E }, r.children);
});
var Se = n.forwardRef((r, o) => {
  let { heading: t, children: u2, forceMount: c, ...d } = r, f = useId(), p2 = n.useRef(null), v = n.useRef(null), b = useId(), l = K2(), y = T((C) => c || l.filter() === false ? true : C.search ? C.filtered.groups.has(f) : true);
  M(() => l.group(f), []), ve(f, p2, [r.value, r.heading, v]);
  let E = n.useMemo(() => ({ id: f, forceMount: c }), [c]);
  return n.createElement(Primitive.div, { ref: G2([p2, o]), ...d, "cmdk-group": "", role: "presentation", hidden: y ? void 0 : true }, t && n.createElement("div", { ref: v, "cmdk-group-heading": "", "aria-hidden": true, id: b }, t), j(r, (C) => n.createElement("div", { "cmdk-group-items": "", role: "group", "aria-labelledby": t ? b : void 0 }, n.createElement(fe.Provider, { value: E }, C))));
});
var Ee = n.forwardRef((r, o) => {
  let { alwaysRender: t, ...u2 } = r, c = n.useRef(null), d = T((f) => !f.search);
  return !t && !d ? null : n.createElement(Primitive.div, { ref: G2([c, o]), ...u2, "cmdk-separator": "", role: "separator" });
});
var Ce = n.forwardRef((r, o) => {
  let { onValueChange: t, ...u2 } = r, c = r.value != null, d = ee(), f = T((l) => l.search), p2 = T((l) => l.value), v = K2(), b = n.useMemo(() => {
    var y;
    let l = (y = v.listInnerRef.current) == null ? void 0 : y.querySelector(`${Z}[${I}="${encodeURIComponent(p2)}"]`);
    return l == null ? void 0 : l.getAttribute("id");
  }, []);
  return n.useEffect(() => {
    r.value != null && d.setState("search", r.value);
  }, [r.value]), n.createElement(Primitive.input, { ref: o, ...u2, "cmdk-input": "", autoComplete: "off", autoCorrect: "off", spellCheck: false, "aria-autocomplete": "list", role: "combobox", "aria-expanded": true, "aria-controls": v.listId, "aria-labelledby": v.labelId, "aria-activedescendant": b, id: v.inputId, type: "text", value: c ? r.value : f, onChange: (l) => {
    c || d.setState("search", l.target.value), t == null || t(l.target.value);
  } });
});
var xe = n.forwardRef((r, o) => {
  let { children: t, label: u2 = "Suggestions", ...c } = r, d = n.useRef(null), f = n.useRef(null), p2 = K2();
  return n.useEffect(() => {
    if (f.current && d.current) {
      let v = f.current, b = d.current, l, y = new ResizeObserver(() => {
        l = requestAnimationFrame(() => {
          let E = v.offsetHeight;
          b.style.setProperty("--cmdk-list-height", E.toFixed(1) + "px");
        });
      });
      return y.observe(v), () => {
        cancelAnimationFrame(l), y.unobserve(v);
      };
    }
  }, []), n.createElement(Primitive.div, { ref: G2([d, o]), ...c, "cmdk-list": "", role: "listbox", "aria-label": u2, id: p2.listId }, j(r, (v) => n.createElement("div", { ref: G2([f, p2.listInnerRef]), "cmdk-list-sizer": "" }, v)));
});
var Pe = n.forwardRef((r, o) => {
  let { open: t, onOpenChange: u2, overlayClassName: c, contentClassName: d, container: f, ...p2 } = r;
  return n.createElement(Root, { open: t, onOpenChange: u2 }, n.createElement(Portal, { container: f }, n.createElement(Overlay, { "cmdk-overlay": "", className: c }), n.createElement(Content, { "aria-label": r.label, "cmdk-dialog": "", className: d }, n.createElement(me, { ref: o, ...p2 }))));
});
var we = n.forwardRef((r, o) => T((u2) => u2.filtered.count === 0) ? n.createElement(Primitive.div, { ref: o, ...r, "cmdk-empty": "", role: "presentation" }) : null);
var De = n.forwardRef((r, o) => {
  let { progress: t, children: u2, label: c = "Loading...", ...d } = r;
  return n.createElement(Primitive.div, { ref: o, ...d, "cmdk-loading": "", role: "progressbar", "aria-valuenow": t, "aria-valuemin": 0, "aria-valuemax": 100, "aria-label": c }, j(r, (f) => n.createElement("div", { "aria-hidden": true }, f)));
});
var Ve = Object.assign(me, { List: xe, Item: ye, Input: Ce, Group: Se, Separator: Ee, Dialog: Pe, Empty: we, Loading: De });
function Ie(r, o) {
  let t = r.nextElementSibling;
  for (; t; ) {
    if (t.matches(o)) return t;
    t = t.nextElementSibling;
  }
}
function Me(r, o) {
  let t = r.previousElementSibling;
  for (; t; ) {
    if (t.matches(o)) return t;
    t = t.previousElementSibling;
  }
}
function pe(r) {
  let o = n.useRef(r);
  return M(() => {
    o.current = r;
  }), o;
}
var M = typeof window == "undefined" ? n.useEffect : n.useLayoutEffect;
function k2(r) {
  let o = n.useRef();
  return o.current === void 0 && (o.current = r()), o;
}
function G2(r) {
  return (o) => {
    r.forEach((t) => {
      typeof t == "function" ? t(o) : t != null && (t.current = o);
    });
  };
}
function T(r) {
  let o = ee(), t = () => r(o.snapshot());
  return (0, import_shim.useSyncExternalStore)(o.subscribe, t, t);
}
function ve(r, o, t, u2 = []) {
  let c = n.useRef(), d = K2();
  return M(() => {
    var v;
    let f = (() => {
      var b;
      for (let l of t) {
        if (typeof l == "string") return l.trim();
        if (typeof l == "object" && "current" in l) return l.current ? (b = l.current.textContent) == null ? void 0 : b.trim() : c.current;
      }
    })(), p2 = u2.map((b) => b.trim());
    d.value(r, f, p2), (v = o.current) == null || v.setAttribute(I, f), c.current = f;
  }), c;
}
var Te = () => {
  let [r, o] = n.useState(), t = k2(() => /* @__PURE__ */ new Map());
  return M(() => {
    t.current.forEach((u2) => u2()), t.current = /* @__PURE__ */ new Map();
  }, [r]), (u2, c) => {
    t.current.set(u2, c), o({});
  };
};
function ke(r) {
  let o = r.type;
  return typeof o == "function" ? o(r.props) : "render" in o ? o.render(r.props) : r;
}
function j({ asChild: r, children: o }, t) {
  return r && n.isValidElement(o) ? n.cloneElement(ke(o), { ref: o.ref }, t(o.props.children)) : t(o);
}
var Le = { position: "absolute", width: "1px", height: "1px", padding: "0", margin: "-1px", overflow: "hidden", clip: "rect(0, 0, 0, 0)", whiteSpace: "nowrap", borderWidth: "0" };
export {
  Ve as Command,
  Pe as CommandDialog,
  we as CommandEmpty,
  Se as CommandGroup,
  Ce as CommandInput,
  ye as CommandItem,
  xe as CommandList,
  De as CommandLoading,
  me as CommandRoot,
  Ee as CommandSeparator,
  he as defaultFilter,
  T as useCommandState
};
/*! Bundled license information:

use-sync-external-store/cjs/use-sync-external-store-shim.development.js:
  (**
   * @license React
   * use-sync-external-store-shim.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=cmdk.js.map
