import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "~/lib/utils";
import { Button } from "~/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "~/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { useDebounce } from "~/hooks/useDebounce";

export interface ComboboxItem {
  value: string;
  label: string;
}

interface SearchableComboboxProps {
  value: string | string[];
  onChange: (value: string | string[]) => void;
  onSearch: (query: string) => void;
  onLoadMore?: () => void;
  items: ComboboxItem[];
  placeholder?: string;
  searchPlaceholder?: string;
  loading?: boolean;
  multiple?: boolean;
}

export function SearchableCombobox({
  value,
  onChange,
  onSearch,
  onLoadMore,
  items,
  placeholder = "Select...",
  searchPlaceholder = "Search...",
  loading = false,
  multiple = false,
}: SearchableComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const debouncedSearch = useDebounce(searchQuery, 300);
  const commandRef = React.useRef<HTMLDivElement>(null);
  const searchTimeoutRef = React.useRef<NodeJS.Timeout>();
  const abortControllerRef = React.useRef<AbortController>();

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Handle search with debounce and abort controller
  React.useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Only search if the query has changed
    if (debouncedSearch !== searchQuery) {
      abortControllerRef.current = new AbortController();
      searchTimeoutRef.current = setTimeout(() => {
        onSearch(debouncedSearch);
      }, 300);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [debouncedSearch, onSearch, searchQuery]);

  const handleScroll = React.useCallback(() => {
    if (!commandRef.current || !onLoadMore) return;
    const { scrollTop, scrollHeight, clientHeight } = commandRef.current;
    if (scrollHeight - scrollTop <= clientHeight * 1.5) {
      onLoadMore();
    }
  }, [onLoadMore]);

  const selectedItems = React.useMemo(() => {
    if (multiple) {
      return items.filter(item => (value as string[]).includes(item.value));
    }
    return items.find(item => item.value === value);
  }, [items, value, multiple]);

  const displayValue = React.useMemo(() => {
    if (multiple) {
      const selected = selectedItems as ComboboxItem[];
      return selected.length > 0 
        ? selected.map(item => item.label).join(", ")
        : placeholder;
    }
    const selected = selectedItems as ComboboxItem;
    return selected ? selected.label : placeholder;
  }, [selectedItems, placeholder, multiple]);

  // Handle open/close
  const handleOpenChange = React.useCallback((isOpen: boolean) => {
    
    if (!isOpen) {
      setSearchQuery("");
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    }
    setOpen(isOpen);
  }, []);

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          type="button"
        >
          <span className="truncate">{displayValue}</span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        className="w-full p-0" 
        align="start"
        side="bottom"
        sideOffset={4}
      >
        <Command shouldFilter={false} ref={commandRef} onScroll={handleScroll}>
          <CommandInput 
            placeholder={searchPlaceholder} 
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            <CommandEmpty>{loading ? "Loading..." : "No results found."}</CommandEmpty>
            <CommandGroup>
              {items.map((item) => {
                const isSelected = multiple 
                  ? (value as string[]).includes(item.value)
                  : item.value === value;

                return (
                  <CommandItem
                    key={item.value}
                    value={item.value}
                    onSelect={(currentValue) => {
                      if (multiple) {
                        const currentValues = value as string[];
                        const newValue = isSelected
                          ? currentValues.filter((val) => val !== item.value)
                          : [...currentValues, item.value];
                        onChange(newValue);
                      } else {
                        onChange(currentValue);
                        setOpen(false);
                      }
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        isSelected ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {item.label}
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
} 