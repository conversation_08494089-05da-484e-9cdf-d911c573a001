import { useEffect, useState } from "react"
import {
    Bar,
    BarChart,
    Cell,
    Pie,
    <PERSON><PERSON>hart,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis,
    CartesianGrid
} from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "@components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@components/ui/tabs"
import { Link, useFetcher, useLoaderData } from "@remix-run/react"
import { ActionFunction, json, LoaderFunction, redirect, TypedResponse } from "@remix-run/node"
import { getAllDashboardDataFor, getDashBoardBuyerSummary, getOrderItemSummary } from "@services/businessConsoleService"
import { BuyerSummary, OrderItem, SellerConsoleDataResponse } from "~/types/api/businessConsoleService/SellerConsoleDataResponse"
import { getSession } from "@utils/session.server"
import { User } from "~/types"
import { DashboardGroupBy } from "~/types/home"
import { formatWeight, formatCurrency } from "@utils/format"
import { ScrollArea } from "@components/ui/scroll-area"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@components/ui/table"
import { CodeSquare, Target, Users } from "lucide-react"
import { withAuth, withResponse } from "@utils/auth-utils";
import SpinnerLoader from "~/components/loader/SpinnerLoader"
import CustomerDashboard, { DataPoint } from "~/components/ui/bar-dashboard"

// Define colors for the chart
const COLORS = ['#afded7', '#f8c8cd', '#cfc2ed', '#AFEFED', '#90c8e7', '#FDDFB4', '#E789B7', '#FFD384', '#00a390', '#7551ce']

interface LoaderData {
    data: SellerConsoleDataResponse[];
    dashboardGroupBy: DashboardGroupBy;
    tonnageReportData: OrderItem[];
    buyerOrderSummary: BuyerSummary[]

}

export const loader = withAuth(async ({ user, request }) => {
    const dashboardGroupBy = DashboardGroupBy.Daily;
    const url = new URL(request.url);
    const returnTo = url.searchParams.get('returnTo');

    if (returnTo) {
        return redirect(returnTo);
    }

    try {

        const today = new Date();
        const yesterday = new Date();
        yesterday.setDate(today.getDate() - 1);
        const [dashBoardData, orderItemResponse, orderBuyerSummary] = await Promise.all([
            getAllDashboardDataFor(user.userId, today, dashboardGroupBy, request),
            getOrderItemSummary(yesterday, today, request),
            getDashBoardBuyerSummary(yesterday, today, request)
        ])

        const responseHeaders = new Headers();
        [dashBoardData, orderItemResponse, orderBuyerSummary].forEach(response => {
            if (response.headers?.has('Set-Cookie')) {
                responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);
            }
        });




        // const data = await
        // getAllDashboardDataFor(user.userId, today, dashboardGroupBy, request);
        // const yesterday = new Date();
        // yesterday.setDate(today.getDate() - 1);
        // const OrderItemResponse = await getOrderItemSummary(yesterday, today, request)
        // const OrderBuyerSummary = await getDashBoardBuyerSummary(yesterday, today, request)
        // const responseHeaders = new Headers();


        // if (data && data.headers?.has('Set-Cookie')) {
        //     responseHeaders.set('Set-Cookie', data.headers.get('Set-Cookie')!);
        // }


        return withResponse({
            data: dashBoardData.data,
            dashboardGroupBy: dashboardGroupBy,
            tonnageReportData: orderItemResponse.data,
            buyerOrderSummary: orderBuyerSummary.data
        }, responseHeaders);
    }
    catch (error) {
        console.error("Customer details error:", error);
        if (error instanceof Response && error.status === 404) {
            throw json({ error: "Customer not found" }, { status: 404 });
        }
        throw new Response("Failed to fetch customer data", { status: 500 });
    }
})
export const action = withAuth(async ({ user, request }) => {
    const formData = await request.formData();
    const dashboardGroupBy = formData.get("dashboardGroupBy") as DashboardGroupBy;
    const summaryDate = new Date(formData.get("summaryDate") as string);
    const intent = formData.get("intent") as string;

    if (!Object.values(DashboardGroupBy).includes(dashboardGroupBy)) {
        throw json({ error: "Invalid groupBy parameter" }, { status: 400 });
    }
    const startDate = new Date(formData.get("startDate") as string);
    const endDate = new Date(formData.get("endDate") as string);

    if (!startDate || !endDate) {
        throw json({ error: "Invalid date range" }, { status: 400 });
    }
    let responseData: any = {};
    let responseHeaders: Headers = new Headers();


    if (intent === "graphDashBoard") {
        const graphDashboardResponse = await getAllDashboardDataFor(user.userId, summaryDate, dashboardGroupBy, request);
        responseData = graphDashboardResponse.data;
        responseHeaders = graphDashboardResponse.headers ?? new Headers();
    }

    let tonnageResponse: any = {};
    let buyerSummaryres: any = {};

    if (intent === "barSelected") {
        const graphDashboardResponse = await getAllDashboardDataFor(user.userId, summaryDate, dashboardGroupBy, request);
        responseData = graphDashboardResponse.data;
        const OrderItemResponse = await getOrderItemSummary(startDate, endDate, request)

        const OrderBuyerSummary = await getDashBoardBuyerSummary(startDate, endDate, request)
        tonnageResponse = OrderItemResponse.data;
        buyerSummaryres = OrderBuyerSummary.data;

    }
    return withResponse({
        data: responseData,
        dashboardGroupBy: dashboardGroupBy,
        tonnageReportData: tonnageResponse,
        buyerOrderSummary: buyerSummaryres


    }, responseHeaders ?? new Headers());
});

export default function HomeDashboard() {


    const { data, dashboardGroupBy, tonnageReportData, buyerOrderSummary } = useLoaderData<LoaderData>()

    const fetcher = useFetcher<LoaderData>();
    const [barGraphData, setBarGraphData] = useState<SellerConsoleDataResponse[]>(data)
    const [dashboardGroup, setIsDashBoardGroup] = useState<DashboardGroupBy>(dashboardGroupBy)
    const [tonnageReports, setTonnageReports] = useState<OrderItem[]>(tonnageReportData);
    const [buyerSummaryReports, setBuyerSummaryReports] = useState<BuyerSummary[]>(buyerOrderSummary)
    const [selectedGraphData, setSelectedGraphData] = useState<DataPoint | undefined>()


    useEffect(() => {
        if (fetcher.data) {
            setBarGraphData(fetcher.data.data)
            setBuyerSummaryReports(fetcher.data.buyerOrderSummary)
            setTonnageReports(fetcher.data.tonnageReportData)
            console.log(fetcher.data.tonnageReportData, "ddddddddddd")
            setIsDashBoardGroup(fetcher.data.dashboardGroupBy)
        }

    }, [fetcher.data])



    const consolidatePieData = (data: OrderItem[] = []) => {
        if (!data || data.length === 0) return [];
        const sortedData = [...data].sort((a, b) => b.totalWeight - a.totalWeight);
        const topFive = sortedData.slice(0, 4);
        const others = sortedData.slice(4).reduce((acc, curr) => {
            acc.totalWeight += curr.totalWeight;
            return acc;
        }, { itemName: 'Others', totalWeight: 0 });
        const unsortedcombined = [...topFive, others]
        const combined = [...unsortedcombined].sort((a, b) => b.totalWeight - a.totalWeight);
        // Alternate between topFive and others
        const result: OrderItem[] = [];
        let left = 0;
        let right = combined.length - 1;

        while (left <= right) {
            if (left === right) {
                result.push(combined[left]); // Add the remaining element if odd-length
            } else {
                result.push(combined[left], combined[right]);
            }
            left++;
            right--;
        }

        return result.map((item, index) => ({
            ...item,
            color: COLORS[index % COLORS.length],
        }));
    };

    const handleGraph = (value: string, summaryDate: Date) => {
        setSelectedGraphData(undefined)
        fetcher.submit(
            {
                dashboardGroupBy: value,
                summaryDate: summaryDate.toISOString(),
                intent: "graphDashBoard"
            },
            { method: "post" }
        );
    }



    const handleBarSelected = (startDate: Date, endDate: Date, tab: string, summaryDate: Date, selectedSellerData: DataPoint) => {

        console.log()

        setSelectedGraphData(selectedSellerData ? selectedSellerData : undefined)
        fetcher.submit(
            {
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
                intent: "barSelected",
                dashboardGroupBy: tab,
                summaryDate: summaryDate.toISOString()
            },
            { method: "post" }
        )

    }

    const totalWeight = Array.isArray(tonnageReports) && tonnageReports.length > 0 && tonnageReports.map((x) => x.totalWeight).reduce((acc, cur) => acc + cur, 0)

    const TotalPendingAmount = Array.isArray(buyerSummaryReports) && buyerSummaryReports.length > 0 && buyerSummaryReports.map((x) => x.PendingAmount).reduce((acc, cur) => acc + cur, 0)
    const totalPeningCount = Array.isArray(buyerSummaryReports) && buyerSummaryReports.length > 0 && buyerSummaryReports.map((x) => x.PendingAmountCount).reduce((acc, cur) => acc + cur, 0)
    {
        fetcher.state !== "idle"
            && <SpinnerLoader size={8} loading={true} />
    }
    const wrapText = (text: string, maxChars: number) => {
        const words = text.split(" ");
        const lines: string[] = [];
        let currentLine = "";

        words.forEach((word) => {
            if ((currentLine + word).length <= maxChars) {
                currentLine += (currentLine ? " " : "") + word;
            } else {
                lines.push(currentLine);
                currentLine = word;
            }
        });

        if (currentLine) {
            lines.push(currentLine);
        }

        return lines;
    };

    const TonnagePieChart = () => {
        const [screenWidth, setScreenWidth] = useState(1024); // Default for SSR-safe value

        useEffect(() => {
            const updateWidth = () => setScreenWidth(window.innerWidth);
            updateWidth(); // Set initial width
            window.addEventListener("resize", updateWidth);
            return () => window.removeEventListener("resize", updateWidth);
        }, []);

        return (
            <Card>
                <CardHeader>
                    <CardTitle>Tonnage</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="h-[250px] w-100% relative">
                        <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                                <Pie
                                    data={consolidatePieData(Array.isArray(tonnageReports) ? tonnageReports : [])}
                                    cx="50%"
                                    cy="50%"
                                    innerRadius={60}
                                    outerRadius={80}
                                    fill="#8884d8"
                                    paddingAngle={2}
                                    dataKey="totalWeight"
                                    labelLine={false}
                                    label={(props) => {
                                        const {
                                            cx, cy, midAngle, outerRadius, fill, payload, value,
                                        } = props;
                                        const RADIAN = Math.PI / 180;
                                        const sin = Math.sin(-RADIAN * midAngle);
                                        const cos = Math.cos(-RADIAN * midAngle);
                                        const sx = cx + (outerRadius + 10) * cos;
                                        const sy = cy + (outerRadius + 10) * sin;
                                        const mx = cx + (outerRadius + 20) * cos;
                                        const my = cy + (outerRadius + 30) * sin;
                                        const ex = mx + (cos >= 0 ? 1 : -1) * 20;
                                        const ey = my;
                                        const textAnchor = cos >= 0 ? 'start' : 'end';
                                        const totalValue = selectedGraphData?.Quantity ?? 0;
                                        const percentage = parseFloat(((value / totalValue) * 100).toFixed(2));
                                        const truncate = (str: string, maxLength: number) => {
                                            return str.length > maxLength ? `${str.slice(0, maxLength)}...` : str;
                                        };

                                        if (percentage < 4) return null; // Skip labels for very small segments
                                        return (
                                            <g>
                                                <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
                                                <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
                                                <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} textAnchor={textAnchor} fill="#333">
                                                    {`${truncate(payload.itemName, 20)}`}
                                                </text>
                                                <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={16} textAnchor={textAnchor} fill="#999">
                                                    {`${formatWeight(value)}  | ${percentage}%`}
                                                </text>
                                            </g>
                                        );
                                    }}
                                >
                                    {consolidatePieData(Array.isArray(tonnageReports) ? tonnageReports : []).map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={entry.color} />
                                    ))}
                                </Pie>
                            </PieChart>
                        </ResponsiveContainer>
                        <div className="absolute inset-0 flex items-center justify-center flex-col">
                            <p className="text-sm font-semibold">
                                {'Total'}
                            </p>
                            <p className="text-xl font-bold">
                                {formatWeight(selectedGraphData?.Quantity > 0 ? selectedGraphData?.Quantity : totalWeight)}
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    };


    return (
        <div className="flex flex-col h-screen bg-background">

            <div className="flex-1 p-4 space-y-4 overflow-auto border-3  border-red-200">
                <CustomerDashboard data={barGraphData || []} handleGraph={handleGraph} dashboardGroupBy={dashboardGroup} handleBarSelected={handleBarSelected} />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 " >
                    <TonnagePieChart />
                    <Card>
                        <CardHeader>
                            <CardTitle>Orders</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <ScrollArea className="h-[250px]">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Business Name</TableHead>
                                            <TableHead>Items</TableHead>
                                            <TableHead>Weight</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {Array.isArray(buyerSummaryReports) && buyerSummaryReports.length > 0 ? (
                                            buyerSummaryReports.map((x) => (
                                                <TableRow key={x.buyerId}>
                                                    <TableCell className="font-medium">{x.buyerName}</TableCell>
                                                    <TableCell>{x.itemCount}</TableCell>
                                                    <TableCell>{formatWeight(x.totalWeight)}</TableCell>
                                                </TableRow>
                                            ))
                                        ) : (
                                            <TableRow>
                                                <TableCell colSpan={3} className="text-center">
                                                    No orders available
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </TableBody>
                                </Table>
                            </ScrollArea>
                            <div className="mt-2 text-sm">
                                <Link to="/home/<USER>" className="text-primary hover:underline">
                                    See all orders
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Returns</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-xl font-bold">{formatWeight(selectedGraphData?.returnweight || 0)} </div>
                            <p className="text-xs text-destructive"> Revenue Loss : {formatCurrency(selectedGraphData?.returnAmount || 0)}</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-xl font-bold">
                                {formatCurrency((TotalPendingAmount || 0))}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                from {totalPeningCount || 0} Customers
                            </p>
                        </CardContent>


                    </Card>
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-sm font-medium">Metrics</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                {[
                                    { title: "Items", icon: Target, value: tonnageReports?.length },
                                    // { title: "Employees", icon: Users, value: tonnageReportsL[0].employeeCount },
                                ].map((item, index) => (
                                    <div key={index} className="flex items-center">
                                        <item.icon className="h-4 w-4 mr-2 text-primary" />
                                        <div>
                                            <p className="text-xs font-medium">{item.title}</p>
                                            <p className="text-lg font-bold">{item.value}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
