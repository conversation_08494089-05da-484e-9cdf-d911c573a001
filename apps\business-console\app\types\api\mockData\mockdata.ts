import { DiscountType, FreeItem } from "../businessConsoleService/FreeItem";


export const DISCOUNT_TYPES: { value: DiscountType; label: string }[] = [
  { value: "percentage", label: "Percentage Discount" },
  { value: "flat", label: "Flat Discount" },
  { value: "freeitem", label: "Free Item" },
  { value: "buyonegetone", label: "Buy One Get One" },
];

export const FREE_ITEM_OPTIONS = [
  { value: "item1", label: "Item 1" },
  { value: "item2", label: "Item 2" },
  { value: "item3", label: "Item 3" },
  { value: "item4", label: "Item 4" },
  { value: "item5", label: "Item 5" },
];

export const MOCK_FREE_ITEMS: FreeItem[] = [
  {
    id: "1",
    discountPercentage: 15,
    discountUpto: 500,
    discountMinOrderQty: 1000,
    validFrom: new Date(2023, 4, 1),
    validTo: new Date(2023, 6, 30),
    discountDisabled: false,
    discountType: "percentage",
    createdAt: new Date(2023, 3, 15),
    updatedAt: new Date(2023, 3, 15),
  },
  {
    id: "2",
    discountFlat: 200,
    discountMinOrderQty: 500,
    validFrom: new Date(2023, 5, 1),
    validTo: new Date(2023, 7, 31),
    discountDisabled: false,
    discountType: "flat",
    createdAt: new Date(2023, 4, 10),
    updatedAt: new Date(2023, 4, 10),
  },
  {
    id: "3",
    freeItemId: "item1",
    freeItemQty: 1,
    discountMinOrderQty: 1500,
    validFrom: new Date(2023, 5, 15),
    validTo: new Date(2023, 8, 15),
    discountDisabled: false,
    discountType: "freeitem",
    createdAt: new Date(2023, 5, 1),
    updatedAt: new Date(2023, 5, 1),
  },
  {
    id: "4",
    validFrom: new Date(2023, 6, 1),
    validTo: new Date(2023, 9, 30),
    discountDisabled: true,
    discountType: "buyonegetone",
    createdAt: new Date(2023, 5, 20),
    updatedAt: new Date(2023, 5, 20),
  },
  {
    id: "5",
    discountPercentage: 20,
    discountUpto: 1000,
    discountMinOrderQty: 2000,
    validFrom: new Date(2023, 7, 1),
    validTo: new Date(2023, 10, 31),
    discountDisabled: false,
    discountType: "percentage",
    createdAt: new Date(2023, 6, 15),
    updatedAt: new Date(2023, 6, 15),
  },
];