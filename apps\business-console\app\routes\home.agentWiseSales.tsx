import { j<PERSON>, LoaderFunction } from "@remix-run/node";
import { Form, useFetcher, useLoaderData, useNavigate, useSearchParams } from "@remix-run/react";
import * as React from "react";
import { ArrowLeft, CalendarIcon } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import {
      Popover,
      PopoverContent,
      PopoverTrigger,
} from "~/components/ui/popover";
import { Calendar } from "~/components/ui/calendar";
import { format } from "date-fns";
import {
      Select,
      SelectContent,
      SelectItem,
      SelectTrigger,
      SelectValue,
} from "~/components/ui/select";
import { SalesData, SalesDetails, SellerSalesInfo } from "~/types/api/businessConsoleService/salesinfo";
import { getPrivateSellerSales, getSalesData } from "~/services/salesinfoDetails";
import { withAuth, withResponse } from "@utils/auth-utils";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "~/components/ui/tabs";
import { useState } from "react";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "~/components/ui/pagination";
import { ResponsiveTable } from "~/components/ui/responsiveTable";
import { DateRange } from "react-day-picker";
import { cn } from "~/lib/utils";

export interface LoaderData {
      salesData: SalesDetails,
      sellerId: number,
      selectedTab: string,
      AgentId: number,
      name: string,
      areaId: number,
      sellerRole: string


}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
      const url = new URL(request.url);
      const sellerPermission = user.userPermissions?.includes('seller_app.scBasic')
      const fromDate = url.searchParams.get("from") || new Date().toISOString().split("T")[0];
      const toDate = url.searchParams.get("to") || fromDate;
      const selectedTab = url.searchParams.get("selectedTab")
      const activeTab = url.searchParams.get("activeTab") || selectedTab;
      const sellerId = Number(url.searchParams.get("sellerId"));
      const name = (url.searchParams.get("name"));
      const agentId = Number(url.searchParams.get("agentId"));
      const areaId = Number(url.searchParams.get("areaId"));
      const page = Number(url.searchParams.get("page")) || 0;
      const pageSize = Number(url.searchParams.get("pageSize")) || 10;
      if (sellerPermission) {
            try {
                  let response = null;
                  switch (activeTab) {

                        case 'agentWise':
                              response = await getPrivateSellerSales(page, pageSize, "agentwise", fromDate, toDate, request, sellerId, agentId, areaId)
                              break;
                        case 'localityWise':
                              response = await getPrivateSellerSales(page, pageSize, "localitywise", fromDate, toDate, request, sellerId, agentId, areaId)
                              break;
                        default:
                              // If no valid activeTab is provided, handle the default case (optional)
                              console.log("No valid activeTab selected");
                              break;

                  }
                  return withResponse({
                        salesData: response?.data,
                        sellerId: sellerId,
                        selectedTab: selectedTab,
                        AgentId: agentId,
                        name: name,
                        areaId: areaId,
                        sellerPermission: sellerPermission

                  }, response?.headers)
            } catch (error) {
                  console.error("Seller sales error:", error);
                  throw new Response("Failed to fetch seller sales", { status: 500 });
            }
      }
      else {

            try {
                  let response = null;

                  switch (activeTab) {
                        case 'sellerWise':
                              response = await getSalesData(0, pageSize, "Sellerwise", fromDate, toDate, request, sellerId, agentId, areaId)

                              break;
                        case 'agentWise':
                              response = await getSalesData(page, pageSize, "Agentwise", fromDate, toDate, request, sellerId, agentId, areaId)
                              break;
                        case 'localityWise':
                              response = await getSalesData(page, pageSize, "Localitywise", fromDate, toDate, request, sellerId, agentId, areaId)
                              break;
                        default:
                              // If no valid activeTab is provided, handle the default case (optional)
                              console.log("No valid activeTab selected");
                              break;

                  }
                  return withResponse({
                        salesData: response?.data,
                        sellerId: sellerId,
                        selectedTab: selectedTab,
                        AgentId: agentId,
                        name: name,
                        areaId: areaId,
                        sellerRole: sellerPermission

                  }, response?.headers)
            } catch (error) {
                  console.error("Seller sales error:", error);
                  throw new Response("Failed to fetch seller sales", { status: 500 });
            }
      }
});
export default function AgentWiseSales() {

      const navigate = useNavigate();
      const { salesData, sellerId, selectedTab, AgentId, name, areaId, sellerRole} = useLoaderData<LoaderData>()

      const [activeTab, setActiveTab] = React.useState(selectedTab === "localityWise" ? "sellerWise" : selectedTab);
      const [currentPage, setCurrentPage] = useState(0)
      const [searchParams] = useSearchParams();
      const selectedDate = searchParams.get("date")
      const [date, setDate] = React.useState<Date>(
            selectedDate ? new Date(selectedDate) : new Date() // Parse the date if present; use the current date otherwise
      );
      const startDate = searchParams.get("from")
      const endDate = searchParams.get("to")


      const [dateRange, setDateRange] = React.useState<DateRange>({
            from: startDate ? new Date(startDate) : new Date(),
            to: endDate ? new Date(endDate) : new Date(),
      });


      const itemsPerPage = 20;
      const handleTabChange = (newTab: string) => {
            if (!dateRange.from) return; // Ensure 'from' date exists

            const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
            const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;
            setActiveTab(newTab);
            navigate(`?activeTab=${newTab}&page=${currentPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}&agentId=${AgentId}&sellerId=${sellerId}&selectedTab=${selectedTab}&name=${name}&areaId=${areaId}`)
      };
      const handlePageChange = (newPage: number) => {
            if (!dateRange.from) return; // Ensure 'from' date exists
            const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
            const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;
            setCurrentPage(newPage)
            navigate(`?activeTab=${activeTab}&page=${newPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}&agentId=${AgentId}&sellerId=${sellerId}&selectedTab=${selectedTab}&name=${name}&areaId=${areaId}`);
      };
      const totalDeliveredWt = salesData?.salesData && Array.isArray(salesData.salesData)
            ? salesData.salesData.map((x) => x?.deliveredWeight || 0).reduce((acc, wet) => acc + wet, 0)
            : 0;

      const totalReturnsWt = salesData?.salesData && Array.isArray(salesData.salesData)
            ? salesData.salesData.map((x) => x?.returnedWeight || 0).reduce((a, b) => a + b, 0)
            : 0;

      const totalBookingWt = salesData?.salesData && Array.isArray(salesData.salesData)
            ? salesData.salesData.map((x) => x?.bookedWeight || 0).reduce((a, b) => a + b, 0)
            : 0;

      const totalCancelWt = salesData?.salesData && Array.isArray(salesData.salesData)
            ? salesData.salesData.map((x) => x?.cancelledWeight || 0).reduce((a, b) => a + b, 0) : 0;
      const sellerWiseHeaders = [
            "Seller ID",
            "Seller Name",
            "Booked Qty",
            "Delivered Qty",
            "Return Qty",
            "Cancel Qty",
            "Total Shops",
            "Ordered Shops",
            "Active Shops"



      ];
      const AgentWiseHeaders = [
            "Agent ID",
            "Agent Name",
            "Booked Qty",
            "Delivered Qty",
            "Return Qty",
            "Cancel Qty",
            "Total Shops",
            "Ordered Shops",
            "Active Shops"

      ];
      const sellerAgentWiseHeaders = [

            "Agent ID",
            "Agent Name",
            "Booked Qty",
            "Delivered Qty",
            "Return Qty",
            "Cancel Qty",
            "Ordered Shops",
            "BookedAmt",
            "DeliveredAmt",
           

      ]


      const LocalityWise = [
            "Locality ID",
            "Locality Name",
            "Booked Qty",
            "Delivered Qty",
            "Return Qty",
            "Cancel Qty",
            "Total Shops",
            "Ordered Shops",
            "Active Shops"

      ];
      const sellerLocalityWise = [
            "Locality ID",
            "Locality Name",
            "Booked Qty",
            "Delivered Qty",
            "Return Qty",
            "Cancel Qty",
            "Active Shops",
             "BookedAmt",
            "DeliveredAmt"

      ];
      const footerTotals = [
            "",
            "",
            totalBookingWt.toFixed(2),
            totalDeliveredWt.toFixed(2),
            totalReturnsWt.toFixed(2),
            totalCancelWt.toFixed(2)

      ];
      const handleViewSales = () => {

            if (!dateRange.from) return; // Ensure 'from' date exists

            const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
            const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;
            navigate(`?activeTab=${activeTab}&page=${currentPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}&agentId=${AgentId}&sellerId=${sellerId}&selectedTab=${selectedTab}&name=${name}&areaId=${areaId}`);

      }

      const handleDateChange = (range: DateRange | undefined) => {
            if (!range?.from) return; // Ensure 'from' is never undefined

            setDateRange({
                  from: range.from,
                  to: range.to || undefined, // If 'to' is not selected, keep it undefined
            });
      };

      return (
            <div className="container mx-auto w-full p-6">
                  <div className=" items-center mb-6">
                        <Button variant="ghost" size="sm" onClick={() => navigate(`/home/<USER>"yyyy-MM-dd")}`)}>
                              <ArrowLeft className="h-4 w-4 mr-2" />
                              Back to Sales Report
                        </Button>
                        <span className="text-muted-foreground">/</span>
                        <span className="font-semibold">{name}</span>
                  </div>
                  <div className="flex flex-col space-y-5 md:flex-row md:space-x-5 md:space-y-0 my-5">
                        <div className="grid grid-cols-1 gap-3 md:flex md:items-center md:space-x-2">
                              <div className="w-full md:w-auto">
                                    <Popover>
                                          <PopoverTrigger asChild>
                                                <Button
                                                      id="date"
                                                      variant={"outline"}
                                                      className={cn(
                                                            "w-[300px] justify-start text-left font-normal",
                                                            !dateRange.from && "text-muted-foreground"
                                                      )}
                                                >
                                                      <CalendarIcon />
                                                      {dateRange?.from ? (
                                                            dateRange.to ? (
                                                                  <>
                                                                        {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                                                                  </>
                                                            ) : (
                                                                  format(dateRange.from, "LLL dd, y")
                                                            )
                                                      ) : (
                                                            <span>Pick a date</span>
                                                      )}
                                                </Button>

                                          </PopoverTrigger>
                                          <PopoverContent className="w-auto p-0" align="start">
                                                <Calendar
                                                      initialFocus
                                                      selected={dateRange}
                                                      mode="range" // Enable range selection
                                                      onSelect={handleDateChange}
                                                />
                                          </PopoverContent>
                                    </Popover>
                              </div>

                              <Button
                                    type="submit"
                                    className="w-full md:w-auto md:rounded-full"
                                    onClick={() => handleViewSales()}

                              >
                                    View Report
                              </Button>
                        </div>

                  </div>
                  <Tabs value={activeTab} onValueChange={handleTabChange} className="my-5">
                        <TabsList>
                              {!sellerRole && (selectedTab === "sellerWise" || selectedTab === "localityWise") && <TabsTrigger value="sellerWise">Seller Wise</TabsTrigger>
                              }
                              {!sellerRole && (selectedTab === "agentWise" || selectedTab === "localityWise") && <TabsTrigger value="agentWise">Agent Wise</TabsTrigger>
                              }
                              {(selectedTab !== "localityWise") && <TabsTrigger value="localityWise">Locality Wise</TabsTrigger>
                              }                        </TabsList>
                        <TabsContent value="sellerWise">
                              <ResponsiveTable
                                    headers={sellerWiseHeaders}
                                    data={
                                          salesData.salesData
                                    }
                                    renderRow={(row) => (
                                          <tr key={row.id} className="border-b">
                                                <td className="py-2 px-3 font-medium text-left">{row.id}</td>
                                                <td className="py-2 px-3  text-left">
                                                      {row.name}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.bookedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.deliveredWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.returnedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.cancelledWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.customerCount || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.orderCount || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.activeShopCount || "-"}
                                                </td>
                                          </tr>
                                    )}
                                    footerTotals={footerTotals}
                                    emptyMessage="No data available for the selected filters."
                              />
                        </TabsContent>
                        <TabsContent value="agentWise">
                              <ResponsiveTable
                                    headers={sellerRole?sellerAgentWiseHeaders:AgentWiseHeaders}
                                    data={
                                          salesData.salesData
                                    }
                                    renderRow={(row) => (
                                          <tr key={row.id} className="border-b">
                                                <td className="py-2 px-3 font-medium text-left">{row.id}</td>
                                                <td className="py-2 px-3  text-left">

                                                      {row.name}

                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.bookedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.deliveredWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.returnedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.cancelledWeight.toFixed(1) || "-"}
                                                </td>
                                                {!sellerRole && (
 <td className="py-2 px-3 text-right">
                                                      {row?.customerCount || "-"}
                                                </td>)}
                                                <td className="py-2 px-3 text-right">
                                                      {row?.orderCount || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.activeShopCount || "-"}
                                                </td>
                                                {sellerRole && (
                                                      <td className="py-2 px-3 text-right">
                                                            {row?.bookedAmount.toFixed(2) || "-"}
                                                      </td>
                                                )}
                                                {sellerRole && (
                                                      <td className="py-2 px-3 text-right">
                                                            {row?.deliveredAmount.toFixed(2) || "-"}
                                                      </td>
                                                )}
                                          </tr>
                                    )}
                                    footerTotals={footerTotals}
                                    emptyMessage="No data available for the selected filters."
                              />
                        </TabsContent>
                        <TabsContent value="localityWise">
                              <ResponsiveTable
                                    headers={ sellerRole?sellerLocalityWise:LocalityWise}
                                    data={
                                          salesData.salesData
                                    }
                                    renderRow={(row) => (
                                          <tr key={row.id} className="border-b">
                                                <td className="py-2 px-3 font-medium text-left">{row.id}</td>
                                                <td className="py-2 px-3  text-left">

                                                      {row.name}

                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.bookedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.deliveredWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.returnedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.cancelledWeight.toFixed(1) || "-"}
                                                </td>
                                                {sellerRole && (
<td className="py-2 px-3 text-right">
                                                      {row?.customerCount || "-"}
                                                </td>)}
                                                <td className="py-2 px-3 text-right">
                                                      {row?.orderCount || "-"}
                                                </td>
                                                {!sellerRole && (
  <td className="py-2 px-3 text-right">
                                                      {row?.activeShopCount || "-"}
                                                </td>)}
                                                {sellerRole && (
                                                      <td className="py-2 px-3 text-right">
                                                            {row?.bookedAmount.toFixed(2) || "-"}
                                                      </td>
                                                )}
                                                {sellerRole && (
                                                      <td className="py-2 px-3 text-right">
                                                            {row?.deliveredAmount.toFixed(2) || "-"}
                                                      </td>
                                                )}
                                                 
                                          </tr>
                                    )}
                                    footerTotals={footerTotals}
                                    emptyMessage="No data available for the selected filters."
                              />
                        </TabsContent>
                  </Tabs>
                  {/* <div className="rounded-md border">
                        <TabsContent value="sellerWise">
                              <ResponsiveTable
                                    headers={sellerWiseHeaders}
                                    data={salesData}
                                    renderRow={(row) => (
                                          <tr key={row.id} className="border-b">
                                                <td className="py-2 px-3 font-medium text-left">{row.id}</td>
                                                <td className="py-2 px-3 text-blue-600 underline cursor-pointer text-left">
                                                      <span
                                                            onClick={() =>
                                                                  navigate(
                                                                        `/home/<USER>
                                                                              "en-CA"
                                                                        )}&sellerName=${row.name.toString()}`
                                                                  )
                                                            }
                                                      >
                                                            {row.name}
                                                      </span>
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.bookedWeight.toFixed(2)}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.deliveredWeight.toFixed(2)}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.returnedWeight.toFixed(2)}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.cancelledWeight.toFixed(2)}
                                                </td>
                                          </tr>
                                    )}
                                    footerTotals={footerTotals}
                                    emptyMessage="No data available for the selected filters."
                              />
                        </TabsContent>
                        <TabsContent value="agentWise">
                              <ResponsiveTable
                                    headers={AgentWiseHeaders}
                                    data={data}
                                    renderRow={(row) => (
                                          <tr key={row.sellerId} className="border-b">
                                                <td className="py-2 px-3 font-medium text-left">{row.sellerId}</td>
                                                <td className="py-2 px-3 text-blue-600 underline cursor-pointer text-left">
                                                      <span
                                                            onClick={() =>
                                                                  navigate(
                                                                        `/home/<USER>
                                                                              "en-CA"
                                                                        )}&sellerName=${row.sellerName.toString()}`
                                                                  )
                                                            }
                                                      >
                                                            {row.sellerName}
                                                      </span>
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.totalBookedWeight.toFixed(2)}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.totalDeliveredWeight.toFixed(2)}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.totalReturnedWeight.toFixed(2)}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.totalCancelledWeight.toFixed(2)}
                                                </td>
                                          </tr>
                                    )}
                                    footerTotals={footerTotals}
                                    emptyMessage="No data available for the selected filters."
                              />
                        </TabsContent>
                        <TabsContent value="localityWise">
                              <ResponsiveTable
                                    headers={LocalityWise}
                                    data={data}
                                    renderRow={(row) => (
                                          <tr key={row.sellerId} className="border-b">
                                                <td className="py-2 px-3 font-medium text-left">{row.sellerId}</td>
                                                <td className="py-2 px-3 text-blue-600 underline cursor-pointer text-left">
                                                      <span
                                                            onClick={() =>
                                                                  navigate(
                                                                        `/home/<USER>
                                                                              "en-CA"
                                                                        )}&sellerName=${row.sellerName.toString()}`
                                                                  )
                                                            }
                                                      >
                                                            {row.sellerName}
                                                      </span>
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.totalBookedWeight.toFixed(2)}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.totalDeliveredWeight.toFixed(2)}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.totalReturnedWeight.toFixed(2)}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row.totalCancelledWeight.toFixed(2)}
                                                </td>
                                          </tr>
                                    )}
                                    footerTotals={footerTotals}
                                    emptyMessage="No data available for the selected filters."
                              />
                        </TabsContent>
                  </div> */}
                  <div className="flex justify-between items-center mt-6 overflow-hidden ">
                        <Pagination>
                              <PaginationContent>
                                    {currentPage > 0 && (
                                          <PaginationItem>
                                                <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} />
                                          </PaginationItem>
                                    )}
                                    <PaginationItem>
                                          <PaginationLink>{currentPage + 1}</PaginationLink>
                                    </PaginationItem>
                                    <PaginationItem>
                                          <PaginationNext onClick={() => handlePageChange(currentPage + 1)} />
                                    </PaginationItem>
                              </PaginationContent>
                        </Pagination>
                  </div>
            </div>

      );
}
