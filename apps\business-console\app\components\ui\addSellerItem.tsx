import { useState } from "react";
import { useNavigate } from "@remix-run/react";
export default function ItemAdditionPage() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    masterItemId: 0,
    unit: "kg",
    weightFactor: 0,
    packaging: "",
    name: "",
    picture: "",
    minOrderQty: 0,
    incrementOrderQty: 0,
    maxOrderQty: 0,
    maxAvailableQty: 0,
    pricePerUnit: 0,
    isActive: true,
    rating: 0,
    nameInKannada: "",
    nameInTelugu: "",
    nameInTamil: "",
    nameInMalayalam: "",
    nameInHindi: "",
    nameInAssame: "",
    nameInGujarati: "",
    nameInMarathi: "",
    nameInBangla: "",
    displayOrder: 0,
  });

  const unitOptions = ["kg", "pcs", "grams", "punits", "bags"];
  const [preview, setPreview] = useState<string | null>(null); // Holds the preview URL

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const target = e.target;

    if (target instanceof HTMLInputElement && target.type === "checkbox") {
      setFormData({
        ...formData,
        [target.name]: target.checked,
      });
    } else {
      setFormData({
        ...formData,
        [target.name]: target.value,
      });
    }
  };


  const handleCancel = () => {
    navigate(-1);
  };
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Submitted Data", formData);
  };
  return (
    <div className="min-h-screen bg-gray-50 p-8 flex flex-col items-center">
      <div className="w-full max-w-3xl bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-semibold mb-6 text-gray-700"> Item  Addition Form</h1>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex flex-col">
              <label htmlFor="unit" className="text-sm font-medium text-gray-600">
                Master Item ID
              </label>
              <select
                name="masterItemId"
                id="masterItemId"
                value={formData.masterItemId}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              >
                {unitOptions.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex flex-col">
              <label htmlFor="unit" className="text-sm font-medium text-gray-600">
                Unit
              </label>
              <select
                name="unit"
                id="unit"
                value={formData.unit}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              >
                {unitOptions.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex flex-col">
              <label htmlFor="picture" className="text-sm font-medium text-gray-600">
                Picture
              </label>
              <input
                type="file"
                name="picture"
                id="picture"
                accept="image/*"
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              />
              {preview && (
                <img
                  src={preview}
                  alt="Preview"
                  className="mt-4 w-32 h-32 object-cover rounded-md shadow-md"
                />
              )}
            </div>

            {Object.keys(formData)
              .filter((key) => key !== "masterItemId" && key !== "unit" && key !== "picture")
              .map((key) => (
                <div key={key} className="flex flex-col">
                  <label
                    htmlFor={key}
                    className="text-sm font-medium text-gray-600 capitalize"
                  >
                    {key}
                  </label>
                  <input
                    type={typeof formData[key as keyof typeof formData] === "number" ? "number" : "text"}
                    name={key}
                    id={key}
                    value={formData[key as keyof typeof formData] as string}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                  />
                </div>
              ))}
          </div>

          <div className="flex justify-end gap-4 mt-6">
            <button
              type="button"
              onClick={handleCancel}
              className="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700"
            >
              Add
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
