# Webhook Logging System Tests 🧪

This directory contains comprehensive tests for the webhook logging system, organized into focused test suites.

## 🏗 Test Structure

```
src/tests/
├── index.ts                    # Master test runner
├── webhook-types.test.ts       # Type safety & message classification tests
├── ist-timestamps.test.ts      # IST timezone functionality tests
└── README.md                   # This documentation
```

## 🚀 Running Tests

### Run All Tests
```bash
npm run test:webhook
```

### Quick Health Check
```bash
npm run test:health
```

### Individual Test Suites
```bash
# Run specific test file
npx tsx src/tests/webhook-types.test.ts
npx tsx src/tests/ist-timestamps.test.ts
```

## 📋 Test Suites

### 1. Webhook Types & Classification (`webhook-types.test.ts`)
Tests type-safe WhatsApp webhook implementation based on Meta documentation:

- **Type Guards**: Validates TypeScript type guards for different message types
- **Message Type Mapping**: Tests conversion from WhatsApp types to internal types  
- **Webhook Validation**: Validates webhook structure and message presence
- **Message Code Classification**: Tests automatic categorization (GREETING, ORDER_STATUS)
- **🆕 Status Update Logging**: Tests WhatsApp delivery status tracking (sent, delivered, read, failed)
- **🆕 Error Notification Logging**: Tests WhatsApp error notification handling
- **🆕 Delivery Analytics**: Tests delivery statistics and system health metrics
- **Signature Validation**: Tests HMAC signature generation and formatting

#### WhatsApp Status & Error Features
The enhanced test suite now covers:

**Status Update Webhooks**: 
- Detects status update webhooks using `hasStatusUpdates()`
- Logs delivery status changes with `MessageType.STATUS_UPDATE`
- Captures WhatsApp status: `sent`, `delivered`, `read`, `failed`
- Uses `messageId` field for WhatsApp message identification

**Error Notification Webhooks**:
- Detects error webhooks using `hasErrors()`
- Logs errors with `MessageType.ERROR_NOTIFICATION`
- Captures error details with codes and descriptions
- Automatically sets status to `WhatsAppMessageStatus.FAILED`

**Analytics & Monitoring**:
- Tests delivery rate calculations
- Validates system health with WhatsApp metrics
- Tests error analysis and breakdown by error codes

### 2. IST Timestamp Integration (`ist-timestamps.test.ts`)
Tests timezone support with real DynamoDB operations:

- **Timestamp Utilities**: Tests IST conversion and format validation
- **Webhook Logging with IST**: Tests webhook creation with IST timestamps
- **Status Updates with IST**: Tests processing status changes with timestamps
- **Query Response with IST**: Tests that queries return proper IST timestamps
- **Timezone Consistency**: Tests chronological ordering and offset consistency

## 🎯 Test Features

### Comprehensive Coverage
- ✅ Type safety validation
- ✅ Message classification rules
- ✅ 🆕 WhatsApp status tracking
- ✅ 🆕 Error notification handling
- ✅ 🆕 Delivery analytics
- ✅ Database integration
- ✅ Timezone handling
- ✅ Real webhook workflows
- ✅ Error scenarios

### Enhanced Test Examples

**Status Update Testing**:
```typescript
// Test status update webhook detection and logging
const statusLog = await webhookService.logIncomingWebhook(req, statusUpdateWebhook);
assert(statusLog.messageType === MessageType.STATUS_UPDATE);
assert(statusLog.whatsappStatus === 'delivered');
assert(statusLog.messageId === 'wamid.MESSAGE_ID_1');
```

**Error Notification Testing**:
```typescript
// Test error notification webhook detection and logging  
const errorLog = await webhookService.logIncomingWebhook(req, errorNotificationWebhook);
assert(errorLog.messageType === MessageType.ERROR_NOTIFICATION);
assert(errorLog.whatsappStatus === WhatsAppMessageStatus.FAILED);
assert(errorLog.whatsappErrors.length > 0);
```

**Delivery Analytics Testing**:
```typescript
// Test delivery statistics and system health
const deliveryStats = await webhookService.getDeliveryStats('businessNumber', 24);
const health = await webhookService.getSystemHealth();
assert(typeof deliveryStats.deliveryRate === 'number');
assert(health.metrics.whatsappDeliveryRate >= 0);
```

### Detailed Reporting
```
🧪 Starting Webhook Logging System Test Suite

============================================================
This test suite validates:
• Type-safe webhook processing
• Message classification (GREETING, ORDER_STATUS)  
• IST timezone functionality
• DynamoDB integration
• Real webhook logging workflows
============================================================

📦 Test Suite 1/2: Webhook Types & Classification
📄 Type-safe WhatsApp webhook implementation based on Meta documentation
--------------------------------------------------
🔍 Testing Type Guards...
   ✅ Text message type guard passed
   ✅ Location message type guard passed
   ✅ Interactive message type guard passed
✅ All type guards passed!

🗂 Testing Message Type Mapping...
   ✅ Text message mapped to: text
   ✅ Location message mapped to: location
   ✅ Interactive message mapped to: interactive
✅ Message type mapping passed!

[... detailed test output ...]

📊 Webhook Types Test Summary:
   ✅ Passed: 5
   ❌ Failed: 0
   📈 Success Rate: 100%

🎉 All webhook types tests passed!
✅ Suite "Webhook Types & Classification" PASSED (1250ms)
```

## 🛠 Prerequisites

### 1. DynamoDB Table
Ensure the webhook logs table exists:
```bash
npm run create-webhook-table
```

### 2. AWS Configuration
Set up AWS credentials and region:
```bash
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret  
export AWS_REGION=us-east-1
```

### 3. Environment
```bash
export NODE_ENV=uat  # or production
```

## 🔧 Test Configuration

### Mock Data
Tests use realistic webhook payloads based on Meta's official documentation:
- Text messages
- Location sharing
- Interactive buttons
- Greeting messages
- Order status inquiries

### Test Environment
- Uses mock Express request objects
- Creates real DynamoDB entries (cleaned up automatically)
- Tests actual service methods and data flows
- Validates real timestamp generation and formatting

## 🐛 Troubleshooting

### Common Issues

**Table Not Found**
```bash
# Create the table first
npm run create-webhook-table
```

**AWS Credentials Error**
```bash
# Check AWS configuration
aws configure list
```

**Type Errors**
```bash
# Ensure dependencies are installed
npm install
```

**Timestamp Format Issues**
```bash
# Run health check
npm run test:health
```

### Debug Mode
Run individual test functions for detailed debugging:

```typescript
import { testTypeGuards } from './webhook-types.test.js';
await testTypeGuards();
```

## 📈 Adding New Tests

### 1. Create Test File
```typescript
// src/tests/new-feature.test.ts
export async function testNewFeature() {
    console.log("🔧 Testing New Feature...");
    // Your test logic
    console.log("✅ New feature tests passed!\n");
}

export async function runNewFeatureTests() {
    const tests = [
        { name: "New Feature", fn: testNewFeature }
    ];
    
    // Standard test runner pattern
    // ... (see existing files for implementation)
}
```

### 2. Update Master Runner
```typescript
// src/tests/index.ts
import { runNewFeatureTests } from './new-feature.test.js';

const testSuites: TestSuite[] = [
    // ... existing suites
    {
        name: "New Feature Tests",
        description: "Tests for the new feature functionality",
        runner: runNewFeatureTests
    }
];
```

## 🎯 Success Criteria

✅ All type guards pass  
✅ Message classification working  
✅ IST timestamps properly formatted  
✅ DynamoDB integration functional  
✅ Real webhook workflows validated  
✅ Error handling robust  

When all tests pass, the webhook logging system is ready for production! 🚀 