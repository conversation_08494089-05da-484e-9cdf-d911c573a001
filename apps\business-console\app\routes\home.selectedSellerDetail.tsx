import { Tab } from "@headlessui/react";
import { json, LoaderFunction } from "@remix-run/node";
import { Form, useFetcher, useLoaderData, useNavigate } from "@remix-run/react";
import { error } from "console";
import { ArrowLeft } from "lucide-react";
import { useEffect, useState } from "react";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import SellerAreaList from "~/components/ui/sellerAreaList";
import { Switch } from "~/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { useToast } from "~/components/ui/ToastProvider";
import { getSelectedSeller, getSelectedSellerAreas, getSellerItemCategories, updateArea, updateAttributes, } from "~/services/masterItemCategories";
import { MasterItemCategories, SelectedSeller, SellerAreas } from "~/types/api/businessConsoleService/MasterItemCategory";
import { withAuth, withResponse } from "~/utils/auth-utils";
interface LoaderData {
      data: SelectedSeller,
      sellerId: number,
      name: string
      isResponse?: false,


}

type ActionData = {
      intent: "netWorkItemDetails" | "sellerAreas" | "updateAttributes";
      data?: any; // Replace with specific types for `data` if possible
      selectedId?: number;
      name?: string;
      isResponse?: boolean;
};
export const loader = withAuth(async ({ user, request }) => {
      const url = new URL(request.url);
      const sellerId = Number(url.searchParams.get("sellerId"));
      const name = (url.searchParams.get("name"));
      try {
            const SelectedNetworkDetails = await getSelectedSeller(sellerId, request);
            return withResponse({
                  data: SelectedNetworkDetails.data,
                  sellerId: sellerId,
                  name: name
            }, SelectedNetworkDetails.headers)
      }
      catch (error) {
            if (error instanceof Response && error.status === 404) {
                  throw json({ error: "MasterItemCategory pg Not found" }, { status: 404 });
            }
            throw new Response("Failed to fetch MasterItemCategory ", { status: 500 });
      }
})
export const action = withAuth(async ({ request }) => {
      const formData = await request.formData();
      const intent = formData.get("intent")
      const sellerId = formData.get("sellerId") as unknown as number;
      const attribute = formData.get("attribute") as string;
      const updateValue = formData.get("value");
      const areaStatus = formData.get("areaStatus") as unknown as boolean
      const areaId = formData.get("areaId") as unknown as number
      const type = formData.get("updateType") as string;
      if (intent === "netWorkItemDetails") {
            const response = await getSellerItemCategories(sellerId, request);
            return withResponse(
                  {
                        intent: intent,
                        data: response.data,
                        selectedId: sellerId,
                        name: updateValue,
                        isResponse: false
                  },
                  response.headers
            );
      }
      if (intent === "sellerAreas") {
            const response = await getSelectedSellerAreas(sellerId, request);
            return withResponse(
                  {
                        intent: intent,
                        data: response.data,
                        selectedId: sellerId,
                        name: updateValue,
                        isResponse: false
                  },
                  response.headers
            );
      }
      if (intent === "updateArea") {
            const response = await updateArea(sellerId, areaId, areaStatus, request);
            return withResponse(
                  {
                        intent: intent,
                        data: response.data,
                        selectedId: sellerId,
                        name: updateValue,
                        isResponse: false
                  },
                  response.headers
            );
      }
      const supportedAttributes = ["autoAccept", "autoPack", "autoPickup", "autoDispatch", "approxPricing", "strikeoffEnabled", "itemPickEnabled",
            "contractPriceEnabled", "isPayLaterEnabled", "wa_enable", "autoActivate", "allowCoD", "favItemsEnabled", "name", "minimumRequiredBalance", "deliveryTime",
            "minimumOrderValue", "categoryLevel", "bookingCloseTime", "bookingOpenTime", "dispatchTime", "deliveryTime", "minimumOrderQty"

      ];
      console.log(attribute, "999999999999999999")
      if (supportedAttributes.includes(attribute)) {
            const updatedResponse = await updateAttributes(type, sellerId, attribute, updateValue, request);


            return withResponse(
                  {
                        intent: intent,
                        data: updatedResponse.data,
                        selectedId: sellerId,
                        name: updateValue,
                        isResponse: false
                  },
                  updatedResponse.headers
            );


      }
})
export default function SelectedSellerDetail() {
      const { showToast } = useToast(); // Get showToast function from context

      const navigate = useNavigate()
      const { data, sellerId, name, isResponse } = useLoaderData<LoaderData>()
      const [selectedNetworkItemCategory, setSelectedNetworkItemCategory] = useState<MasterItemCategories[]>([]);
      const [sellerAreaList, setSellerAreaList] = useState<SellerAreas[]>([]);
      const [activeTab, setActiveTab] = useState('config');
      const [selectedAttribute, setSelectedAttribute] = useState("");
      const [loader, setLoader] = useState(false);

      const [sellerData, setSellerData] = useState({
            name: data?.name,
            autoAccept: data?.auto_accept,
            autoPack: data?.auto_pack,
            autoPickup: data?.auto_pickup,
            autoDispatch: data?.auto_dispatch,
            strikeoffEnabled: data?.strikeoff_enabled,
            favItemsEnabled: data?.fav_items_enabled,
            itemPickEnabled: data?.item_pick_enabled,
            contractPriceEnabled: data?.contract_price_enabled,
            approxPricing: data?.approx_pricing,
            isPayLaterEnabled: data?.is_pay_later_enabled,
            autoActivate: data?.auto_activate,
            allowCoD: data.allow_cod,
            wa_enable: data.wa_enable,
            minimumRequiredBalance: data.mininum_required_balance,
            minimumOrderQty: data.minimum_order_qty,
            minimumOrderValue: data.minimum_order_value,
            categoryLevel: data.category_level,
            bookingCloseTime: data.booking_close_time,
            bookingOpenTime: data.booking_open_time,
            dispatchTime: data.dispatch_time,
            deliveryTime: data.delivery_time

      });
      const [updateAttributes, setUpdateAttributes] = useState<boolean | undefined>(isResponse);
      const [selectedField, setSelectedField] = useState('');
      const [searchTerm, setSearchTerm] = useState('')
      const updateSellerData = (key: string, value: any) => {
            setSellerData((prev) => ({ ...prev, [key]: value }));
      };
      const fetcher = useFetcher<{ data: MasterItemCategories[] | SellerAreas[] }>();
      const [loadingFields, setLoadingFields] = useState<Record<string, boolean>>({});

      const handleTabChange = (newTab: string) => {
            setActiveTab(newTab);
            if (newTab === 'netWorkItemDetails') {
                  const formData = new FormData();
                  formData.append("intent", "netWorkItemDetails");
                  formData.append("sellerId", sellerId.toString());
                  fetcher.submit(formData, { method: "POST" });
            }
            if (newTab === "sellerAreas") {
                  setUpdateAttributes(false)
                  const formData = new FormData();
                  formData.append("intent", "sellerAreas");
                  formData.append("sellerId", sellerId.toString());
                  fetcher.submit(formData, { method: "POST" });
            }

      };

      const handleUpdate = async (attributeType: string, val: any, id: number, type: string) => {


            console.log(loader, "99999999999999")
            setLoadingFields((prev) => ({ ...prev, [attributeType]: true })); // Set loading to true before request

            const formData = new FormData();
            formData.append("updateType", type);
            formData.append("sellerId", id.toString());
            formData.append("attribute", attributeType);
            formData.append("value", val.toString());

            try {


                  await fetcher.submit(formData, { method: "POST" });

                  showToast(`${attributeType.replace(/([A-Z])/g, " $1")} updated successfully`, "success");
                  updateSellerData(attributeType, val);



                  // Update the UI after success
            } catch (error) {
                  showToast(`Failed to update ${attributeType.replace(/([A-Z])/g, " $1")}`, "error");
            } finally {
                  setLoadingFields((prev) => ({ ...prev, [attributeType]: false })); // Reset loading state
            }
      };


      const handleSwitch = async (
            field: "autoAccept" | "autoPack" | "autoPickup" | "autoDispatch" | "approxPricing" |
                  "strikeoffEnabled" | "itemPickEnabled" | "contractPriceEnabled" | "isPayLaterEnabled" |
                  "wa_enable" | "autoActivate" | "allowCoD" | "favItemsEnabled"
      ) => {
            setLoadingFields((prev) => ({ ...prev, [field]: true }));

            const currentValue = sellerData[field];
            const newValue = !currentValue;

            try {
                  const formData = new FormData();
                  formData.append("updateType", "seller");
                  formData.append("sellerId", sellerId.toString());
                  formData.append("attribute", field);
                  formData.append("value", newValue.toString());

                  await fetcher.submit(formData, { method: "POST" });

                  showToast(`${field.replace(/([A-Z])/g, " $1")} updated successfully`, "success");
                  updateSellerData(field, newValue);
                  setSelectedField(field)
                  // Update UI only after successful response
            } catch (error) {
                  showToast(`Failed to update ${field.replace(/([A-Z])/g, " $1")}`, "error");
            } finally {
                  setLoadingFields((prev) => ({ ...prev, [field]: false }));
            }
      };
      useEffect(() => {
            if (fetcher.data) {
                  if (activeTab === "netWorkItemDetails" && fetcher.data.data) {
                        setSelectedNetworkItemCategory(fetcher.data.data as MasterItemCategories[]);
                  } else if (activeTab === "sellerAreas" && fetcher.data.data) {
                        setSellerAreaList(fetcher.data.data as SellerAreas[]);
                  }
            }
      }, [fetcher.data, activeTab]);
      const updateToggle = (status: boolean, areaId: number) => {
            const formData = new FormData()
            formData.append("sellerId", sellerId as unknown as string)
            formData.append("areaStatus", status as unknown as string)
            formData.append("areaId", areaId as unknown as string)
            formData.append("intent", "updateArea")
            formData.append("name", name)
            fetcher.submit(formData, { method: "POST" })
      }
      { fetcher.state !== "idle" && <SpinnerLoader size={8} loading={true} /> }
      return (
            <div className="container mx-auto p-6">
                  <div className="flex items-center gap-2 mb-6">
                        <Button variant="ghost" size="sm" onClick={() => navigate(-1)}>
                              <ArrowLeft className="h-4 w-4 mr-2" />
                              Back to Sellers
                        </Button>
                        <span className="text-muted-foreground">/</span>
                        <span className="font-semibold">{name}</span>
                  </div>
                  <Tabs value={activeTab} onValueChange={handleTabChange} className="mb-6">
                        <TabsList>
                              <TabsTrigger value="config">Config</TabsTrigger>
                              <TabsTrigger value="sellerAreas">Seller Areas</TabsTrigger>
                              {/* <TabsTrigger value="SellerItems">Seller Items</TabsTrigger>
                              <TabsTrigger value="sellerItemCategory">SellerItem Category</TabsTrigger> */}
                        </TabsList>
                        <TabsContent value="config">
                              <div className="flex flex-col md:flex-row gap-6">
                                    {/* Left Side - Basic Info */}
                                    <div className="w-full md:w-1/2 bg-white p-6 border rounded-lg shadow-md space-y-6">
                                          <div className="flex justify-between items-center">
                                                <p className="text-lg font-semibold text-gray-700">Update Details</p>
                                                <Switch checked={updateAttributes} onClick={() => setUpdateAttributes(!updateAttributes)} />
                                          </div>

                                          <div className="space-y-4">
                                                <div className="flex items-center space-x-4">
                                                      <p className="text-lg font-semibold text-gray-700">ID:</p>
                                                      <p className="text-lg text-gray-900">{data.id}</p>
                                                </div>

                                                <div className="flex items-center space-x-4">
                                                      <p className="text-lg font-semibold text-gray-700">Name:</p>
                                                      <Input
                                                            placeholder="Enter Name"
                                                            value={sellerData?.name}
                                                            onChange={(e) => updateSellerData("name", e.target.value)}
                                                            disabled={!updateAttributes}

                                                      />
                                                      {updateAttributes && (
                                                            <Button onClick={() => handleUpdate("name", sellerData?.name, sellerId, "seller")}>
                                                                  Update
                                                            </Button>
                                                      )}
                                                </div>


                                                {[




                                                      { label: "Minimum Required Balance", key: "minimumRequiredBalance", value: sellerData.minimumRequiredBalance },
                                                      { label: "Minimum Order Quantity", key: "minimumOrderQty", value: sellerData.minimumOrderQty },
                                                      { label: "Minimum Order Value", key: "minimumOrderValue", value: sellerData.minimumOrderValue },
                                                      { label: "Platform Fee (Fixed):", key: "platformFee", value: sellerData.minimumOrderValue },
                                                      { label: "Platform Fee (%)", key: "platformFeePerc", value: sellerData.minimumOrderValue },
                                                      { label: "Platform Fee per Kg:", key: "platformFeePkg",value: sellerData.minimumOrderValue },

                                                ].map((item) => (
                                                      <div key={item.key} className="flex items-center space-x-4">
                                                            <p className="text-lg font-semibold text-gray-700">{item.label}:</p>
                                                            <Input
                                                                  type="number"
                                                                  placeholder={`Enter ${item.label}`}
                                                                  value={item.value}
                                                                  onChange={(e) => updateSellerData(item.key, e.target.value)}
                                                                  disabled={!updateAttributes}

                                                            />
                                                            {updateAttributes && (
                                                                  <Button onClick={() => handleUpdate(item.key, item.value, sellerId, "seller")}>
                                                                        Update
                                                                  </Button>
                                                            )}
                                                      </div>
                                                ))}

                                                {/* Integer Fields */}
                                                {[
                                                      { label: "Category Level", key: "categoryLevel", value: sellerData.categoryLevel },
                                                      { label: "Booking Close Time", key: "bookingCloseTime", value: sellerData.bookingCloseTime },
                                                      { label: "Booking Open Time", key: "bookingOpenTime", value: sellerData.bookingOpenTime },
                                                ].map((item) => (
                                                      <div key={item.key} className="flex items-center space-x-4">
                                                            <p className="text-lg font-semibold text-gray-700">{item.label}:</p>
                                                            <Input
                                                                  type="number"
                                                                  placeholder={`Enter ${item.label}`}
                                                                  value={item.value}
                                                                  onChange={(e) => updateSellerData(item.key, e.target.value)}
                                                                  disabled={!updateAttributes}

                                                            />
                                                            {updateAttributes && (
                                                                  <Button onClick={() => handleUpdate(item.key, item.value, sellerId, "seller")}>
                                                                        Update
                                                                  </Button>
                                                            )}
                                                      </div>
                                                ))}

                                                {/* String Fields */}
                                                {[
                                                      { label: "Dispatch Time", key: "dispatchTime", value: sellerData.dispatchTime },
                                                      { label: "Delivery Time", key: "deliveryTime", value: sellerData.deliveryTime },
                                                ].map((item) => (
                                                      <div key={item.key} className="flex items-center space-x-4">
                                                            <p className="text-lg font-semibold text-gray-700">{item.label}:</p>
                                                            <Input
                                                                  type="text"
                                                                  placeholder={`Enter ${item.label}`}
                                                                  value={item.value}
                                                                  onChange={(e) => updateSellerData(item.key, e.target.value)}
                                                                  disabled={!updateAttributes}
                                                            />
                                                            {updateAttributes && (
                                                                  <Button onClick={() => handleUpdate(item.key, item.value, sellerId, "seller")}
                                                                        disabled={loadingFields[item.key] || !updateAttributes}


                                                                        loading={loadingFields[item.key]}
                                                                  >
                                                                        {loadingFields[item.key] ? "Updating..." : "Update"}

                                                                  </Button>
                                                            )}
                                                      </div>
                                                ))}
                                          </div>
                                    </div>

                                    {/* Right Side - Toggle Switches */}
                                    <div className="w-full md:w-1/2 bg-white p-6 border rounded-lg shadow-md space-y-4">
                                          {[
                                                { label: "Auto Accept", key: "autoAccept" as const, value: sellerData?.autoAccept },
                                                { label: "Auto Pack", key: "autoPack" as const, value: sellerData?.autoPack },
                                                { label: "Auto Pick", key: "autoPickup" as const, value: sellerData?.autoPickup },
                                                { label: "Auto Dispatch", key: "autoDispatch" as const, value: sellerData?.autoDispatch },
                                                { label: "Strike Off", key: "strikeoffEnabled" as const, value: sellerData?.strikeoffEnabled },
                                                { label: "Item Pick", key: "itemPickEnabled" as const, value: sellerData?.itemPickEnabled },
                                                { label: "Contract Price", key: "contractPriceEnabled" as const, value: sellerData?.contractPriceEnabled },
                                                { label: "Approx Price", key: "approxPricing" as const, value: sellerData?.approxPricing },
                                                { label: "Pay Later", key: "isPayLaterEnabled" as const, value: sellerData?.isPayLaterEnabled },
                                                { label: "WhatsApp Enable", key: "wa_enable" as const, value: sellerData?.wa_enable },
                                                { label: "Favorite Items", key: "favItemsEnabled" as const, value: sellerData?.favItemsEnabled },
                                                { label: "Auto Activate", key: "autoActivate" as const, value: sellerData?.autoActivate },
                                                { label: "Allow CoD", key: "allowCoD" as const, value: sellerData?.allowCoD },


                                          ].map((item) => (
                                                <div key={item.key} className="flex justify-between items-center">
                                                      <p className="text-lg font-semibold text-gray-700">{item.label}</p>
                                                      <Switch
                                                            checked={!item.value}
                                                            onClick={() => handleSwitch(item.key)}
                                                            disabled={loadingFields[item.key] || !updateAttributes}
                                                            {...loadingFields[item.key] && <SpinnerLoader size={10} loading={true} />}

                                                      />
                                                </div>
                                          ))}
                                    </div>
                              </div>
                        </TabsContent >

                        {/* <TabsContent value="sellerItemCategory">
                              <CommonItemCategoryList data={selectedNetworkItemCategory} />
                        </TabsContent> */}
                        < TabsContent value="sellerAreas" >
                              <SellerAreaList sellerAreaData={sellerAreaList} updateToggle={updateToggle} />
                        </TabsContent >
                        {/* <TabsContent value="SellerItems">
                              <div className="flex justify-between mb-4 my-4">
                                    <Input
                                          placeholder="Search by Name"
                                          value={searchTerm}
                                          onChange={(e) => setSearchTerm(e.target.value)}
                                          className="max-w-sm"
                                    />
                                    <Button onClick={() => setIsAddSellerItem(!isAddSellerItem)} size="sm">
                                          Add Seller Items
                                    </Button>

                              </div>
                              {isAddSellerItem && <AddSellerItem />}
                              <Table>
                                    <TableHeader>
                                          <TableRow>
                                                <TableHead>Item Name</TableHead>
                                                <TableHead>Item Image</TableHead>
                                                <TableHead>Unit</TableHead>
                                                <TableHead>Is Active</TableHead>
                                          </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                          <TableRow>
                                                <TableCell></TableCell>
                                                <TableCell></TableCell>
                                                <TableCell></TableCell>
                                                <TableCell></TableCell>

                                          </TableRow>
                                    </TableBody>
                              </Table>
                        </TabsContent> */}

                  </Tabs >
            </div >
      )
}


