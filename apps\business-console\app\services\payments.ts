import { API_BASE_URL, apiRequest } from "@utils/api";
import { Transaction } from "~/types/api/businessConsoleService/payments";
import { ApiResponse } from "~/types/api/Api";

export async function getTransactionDetails(
  date: string,
  request: Request
): Promise<ApiResponse<Transaction[]>> {
  const response = await apiRequest<Transaction[]>(
    `${API_BASE_URL}/mc/deposits/${date}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch buyer summary data");
  }
}

export async function updateTransaction(
  depositId: number | null,
  request: Request
): Promise<ApiResponse<Transaction>> {
  return apiRequest<Transaction>(
    `${API_BASE_URL}/mc/deposit/${depositId}/updatestatus`,
    "PUT", // Fixed capitalization for consistency
    undefined,
    {},
    true,
    request
  );
}

export async function updateMarkAsPaid(
  userId: number,
  depositId: number,
  request: Request
): Promise<ApiResponse<Transaction[]>> {
  return apiRequest<Transaction[]>(
    `${API_BASE_URL}/bc/user/${userId}/deposit/${depositId}/manualstatusupdate`,
    "PUT", // Fixed capitalization for consistency
    undefined,
    {},
    true,
    request
  );
}
