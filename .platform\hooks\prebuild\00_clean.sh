#!/bin/bash
# set -e

# echo "Running enhanced prebuild steps..."

# # Clean up root-level node_modules and lock file
# if [ -d "node_modules" ]; then
#   echo "Removing root node_modules..."
#   rm -rf node_modules
# fi
# if [ -f "package-lock.json" ]; then
#   echo "Removing root package-lock.json..."
#   rm -f package-lock.json
# fi

# # Clean up apps/business-console modules and lock file
# if [ -d "apps/business-console/node_modules" ]; then
#   echo "Removing apps/business-console/node_modules..."
#   rm -rf apps/business-console/node_modules
# fi
# if [ -f "apps/business-console/package-lock.json" ]; then
#   echo "Removing apps/business-console/package-lock.json..."
#   rm -f apps/business-console/package-lock.json
# fi

# echo "Cleaning npm cache..."
# npm cache clean --force

# echo "Enhanced prebuild steps completed."
