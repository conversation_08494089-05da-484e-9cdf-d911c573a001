import {useEffect, useState, use<PERSON><PERSON>back, useMemo} from 'react'
import {json, LoaderFunction, ActionFunction, redirect} from '@remix-run/node'
import {useLoaderData, useNavigate, Form, useSearchParams} from '@remix-run/react'
import {ArrowLeft, ArrowUpDown} from 'lucide-react'
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@components/ui/card"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@components/ui/select"
import {Button} from "@components/ui/button"
import {Checkbox} from "@components/ui/checkbox"
import {getDistrictsAndStates, activateSellerArea, getGlobalAreas} from '~/services/businessConsoleService'
import {getSession} from "@utils/session.server"
import {GoogleMap, LoadScript, Polygon} from '@react-google-maps/api'
import {decodePolygon} from "@utils/polyline-utils"
import type {User} from "~/types"
import {Seller<PERSON><PERSON>} from "~/types/api/businessConsoleService/Areas"
import {withAuth, withResponse} from "@utils/auth-utils";

interface LoaderData {
    states: string[]
    districts: { [state: string]: string[] }
    googleMapsApiKey: string
    globalAreas: SellerArea[]
}

export const loader: LoaderFunction = withAuth(async ({request, user}) => {
        try {
            const url = new URL(request.url)
            const state = url.searchParams.get('state')
            const district = url.searchParams.get('district')

            const [areasResponse, globalAreasResponse] = await Promise.all([
                getDistrictsAndStates(user.userId, request),
                (state && district)
                    ? getGlobalAreas(user.userId, state, district, request)
                    : Promise.resolve({data: []})
            ])

            const areas = areasResponse.data
            const statesSet = new Set<string>()
            const districtsMap: { [state: string]: Set<string> } = {}

            areas.forEach((area: SellerArea) => {
                statesSet.add(area.state)
                if (!districtsMap[area.state]) {
                    districtsMap[area.state] = new Set<string>()
                }
                districtsMap[area.state].add(area.district)
            })
            return withResponse({
                states: Array.from(statesSet).sort(),
                districts: Object.fromEntries(
                    Object.entries(districtsMap).map(([state, districtsSet]) => [
                        state,
                        Array.from(districtsSet).sort()
                    ])
                ),
                googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY || '',
                globalAreas: globalAreasResponse.data || []
            })
        } catch (error) {
            console.error('Failed to fetch areas:', error)
            return json({error: "Failed to fetch areas"}, {status: 500})
        }
    }
)

export const action: ActionFunction = withAuth(async ({request}) => {
        const session = await getSession(request.headers.get("Cookie"))
        const user = session.get("user") as User
        const formData = await request.formData()

        if (request.method === "POST") {
            const areaIds = formData.getAll('areaIds')

            await Promise.all(
                areaIds.map(id => activateSellerArea(user.userId, parseInt(id.toString()), request))
            )
            return redirect('/home/<USER>')
        }

        return null
    }
)
const BANGALORE_CENTER = {lat: 12.9716, lng: 77.5946}

export default function AttachLocality() {
    const {states, districts, googleMapsApiKey, globalAreas = []} = useLoaderData<LoaderData>()
    const [searchParams, setSearchParams] = useSearchParams()
    const [selectedState, setSelectedState] = useState(searchParams.get('state') || '')
    const [selectedDistrict, setSelectedDistrict] = useState(searchParams.get('district') || '')
    const [selectedAreaIds, setSelectedAreaIds] = useState(new Set<number>())
    const [map, setMap] = useState<google.maps.Map | null>(null)
    const [mapLoaded, setMapLoaded] = useState(false)
    const [sortBy, setSortBy] = useState<'name' | 'state' | 'district'>('name')
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
    const navigate = useNavigate()

    const areasWithPolygons = useMemo(() => globalAreas.filter(area => area.polygon), [globalAreas])

    const handleStateChange = (state: string) => {
        setSelectedState(state)
        setSelectedDistrict('')
        setSelectedAreaIds(new Set())
        setSearchParams({state})
    }

    const handleDistrictChange = (district: string) => {
        setSelectedDistrict(district)
        setSearchParams({state: selectedState, district})
        setSelectedAreaIds(new Set())
    }

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        const formData = new FormData()
        selectedAreaIds.forEach(id => formData.append('areaIds', id.toString()))
        fetch('/home/<USER>/attach', {
            method: 'POST',
            body: formData
        }).then(() => {
            navigate('/home/<USER>')
        })
    }

    const toggleAreaSelection = useCallback((areaId: number) => {
        setSelectedAreaIds(prev => {
            const newSet = new Set(prev)
            if (newSet.has(areaId)) {
                newSet.delete(areaId)
            } else {
                newSet.add(areaId)
            }
            return newSet
        })
    }, [])

    const fitBounds = useCallback(() => {
        if (map && areasWithPolygons.length > 0) {
            const bounds = new google.maps.LatLngBounds()
            const areasToFit = selectedAreaIds.size > 0
                ? areasWithPolygons.filter(area => selectedAreaIds.has(area.id))
                : areasWithPolygons

            areasToFit.forEach(area => {
                if (area.polygon) {
                    decodePolygon(area.polygon).forEach(coord => bounds.extend(coord))
                }
            })
            map.fitBounds(bounds)
        }
    }, [map, areasWithPolygons, selectedAreaIds])

    useEffect(() => {
        if (mapLoaded) {
            fitBounds()
        }
    }, [mapLoaded, fitBounds, selectedDistrict, selectedAreaIds])

    const handleSort = (column: 'name' | 'state' | 'district') => {
        if (sortBy === column) {
            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
        } else {
            setSortBy(column)
            setSortOrder('asc')
        }
    }

    const sortedAreas = useMemo(() => {
        return [...areasWithPolygons].sort((a, b) => {
            if (sortBy === 'name') {
                return sortOrder === 'asc' ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name)
            } else if (sortBy === 'state') {
                return sortOrder === 'asc' ? a.state.localeCompare(b.state) : b.state.localeCompare(a.state)
            } else {
                return sortOrder === 'asc' ? a.district.localeCompare(b.district) : b.district.localeCompare(a.district)
            }
        })
    }, [areasWithPolygons, sortBy, sortOrder])

    return (
        <div className="h-[calc(100vh-64px)] bg-white p-6 overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
                <Button
                    onClick={() => navigate('/home/<USER>')}
                    variant="ghost"
                    size="icon"
                >
                    <ArrowLeft size={24}/>
                </Button>
            </div>

            <Card className="w-full mx-auto">
                <CardHeader>
                    <CardTitle>Select Areas to Attach</CardTitle>
                    <CardDescription>Choose areas from available localities</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex gap-4 mb-4">
                        <Select value={selectedState} onValueChange={handleStateChange}>
                            <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Select state"/>
                            </SelectTrigger>
                            <SelectContent>
                                {states.map(state => (
                                    <SelectItem key={state} value={state}>{state}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        <Select
                            value={selectedDistrict}
                            onValueChange={handleDistrictChange}
                            disabled={!selectedState}
                        >
                            <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Select district"/>
                            </SelectTrigger>
                            <SelectContent>
                                {selectedState && districts[selectedState]?.map(district => (
                                    <SelectItem key={district} value={district}>{district}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="flex gap-4 h-[calc(100vh-300px)]">
                        <div className="w-1/2 h-full">
                            <LoadScript googleMapsApiKey={googleMapsApiKey}>
                                <GoogleMap
                                    mapContainerClassName="w-full h-full"
                                    center={BANGALORE_CENTER}
                                    zoom={11}
                                    onLoad={(map) => {
                                        setMap(map)
                                        setMapLoaded(true)
                                    }}
                                    options={{
                                        streetViewControl: false,
                                        mapTypeControl: false,
                                        fullscreenControl: false,
                                        zoomControl: true,
                                        clickableIcons: false
                                    }}
                                >
                                    {mapLoaded && areasWithPolygons.map((area) => (
                                        <Polygon
                                            key={area.id}
                                            paths={decodePolygon(area.polygon)}
                                            options={{
                                                fillColor: selectedAreaIds.has(area.id) ? '#4F46E5' : '#9CA3AF',
                                                fillOpacity: selectedAreaIds.has(area.id) ? 0.4 : 0.2,
                                                strokeWeight: selectedAreaIds.has(area.id) ? 2 : 1,
                                                strokeColor: selectedAreaIds.has(area.id) ? '#4F46E5' : '#9CA3AF',
                                                clickable: true
                                            }}
                                            onClick={() => toggleAreaSelection(area.id)}
                                        />
                                    ))}
                                </GoogleMap>
                            </LoadScript>
                        </div>
                        <div className="w-1/2 h-full overflow-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50 sticky top-0">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Select
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <Button variant="ghost" onClick={() => handleSort('name')}
                                                className="flex items-center">
                                            Name
                                            <ArrowUpDown size={14} className="ml-1"/>
                                        </Button>
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <Button variant="ghost" onClick={() => handleSort('state')}
                                                className="flex items-center">
                                            State
                                            <ArrowUpDown size={14} className="ml-1"/>
                                        </Button>
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <Button variant="ghost" onClick={() => handleSort('district')}
                                                className="flex items-center">
                                            District
                                            <ArrowUpDown size={14} className="ml-1"/>
                                        </Button>
                                    </th>
                                </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                {sortedAreas.map((area) => (
                                    <tr key={area.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <Checkbox
                                                checked={selectedAreaIds.has(area.id)}
                                                onCheckedChange={() => toggleAreaSelection(area.id)}
                                                id={`area-${area.id}`}
                                            />
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <label htmlFor={`area-${area.id}`} className="text-sm text-gray-900">
                                                {area.name}
                                            </label>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {area.state}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {area.district}
                                        </td>
                                    </tr>
                                ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <Form onSubmit={handleSubmit}>
                        <Button
                            type="submit"
                            disabled={selectedAreaIds.size === 0}
                            className="w-full"
                        >
                            Attach Selected Areas
                        </Button>
                    </Form>
                </CardContent>
            </Card>
        </div>
    )
}
