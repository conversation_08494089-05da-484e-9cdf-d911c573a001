/**
 * Timestamp Utility Tests
 * 
 * Tests for IST timestamp conversion functionality using date-fns-tz library
 */

import { formatInTimeZone } from 'date-fns-tz';

/**
 * Local implementation of timestamp utilities for testing
 */
function toISTISOString(timestamp: number): string {
    return formatInTimeZone(
        new Date(timestamp),
        'Asia/Kolkata',
        "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
    );
}

class TimestampUtils {
    static getCurrentISTString(): string {
        return toISTISOString(Date.now());
    }

    static istISOToTimestamp(istISO: string): number {
        return new Date(istISO).getTime();
    }

    static formatForDisplay(timestamp: number) {
        const date = new Date(timestamp);
        
        return {
            ist: toISTISOString(timestamp),
            utc: date.toISOString(),
            readable: formatInTimeZone(date, 'Asia/Kolkata', 'PPpp'),
            dateOnly: formatInTimeZone(date, 'Asia/Kolkata', 'yyyy-MM-dd'),
            timeOnly: formatInTimeZone(date, 'Asia/Kolkata', 'HH:mm:ss')
        };
    }

    static isISTBusinessHours(timestamp: number): boolean {
        const hour = parseInt(formatInTimeZone(new Date(timestamp), 'Asia/Kolkata', 'HH'));
        return hour >= 9 && hour < 18;
    }

    static getISTDayBounds(timestamp: number): { startOfDay: number; endOfDay: number } {
        const date = new Date(timestamp);
        const dateString = formatInTimeZone(date, 'Asia/Kolkata', 'yyyy-MM-dd');
        
        const startOfDay = new Date(`${dateString}T00:00:00+05:30`).getTime();
        const endOfDay = new Date(`${dateString}T23:59:59.999+05:30`).getTime();
        
        return { startOfDay, endOfDay };
    }
}

function demonstrateTimestampHandling() {
    const now = Date.now();
    
    console.log('=== Enhanced Timestamp Handling with date-fns-tz ===');
    console.log('Current timestamp:', now);
    console.log('IST ISO:', toISTISOString(now));
    console.log('UTC ISO:', new Date(now).toISOString());
    
    const formatted = TimestampUtils.formatForDisplay(now);
    console.log('Formatted:', formatted);
    
    return true;
}

/**
 * Mock test framework functions for basic testing
 */
const describe = (name: string, fn: () => void) => {
    console.log(`\n=== ${name} ===`);
    fn();
};

const test = (name: string, fn: () => void) => {
    try {
        console.log(`\n📝 ${name}`);
        fn();
        console.log(`✅ PASSED: ${name}`);
    } catch (error) {
        console.log(`❌ FAILED: ${name}`);
        console.error(error);
    }
};

const expect = (actual: any) => ({
    toBe: (expected: any) => {
        if (actual !== expected) {
            throw new Error(`Expected ${actual} to be ${expected}`);
        }
    },
    toMatch: (pattern: RegExp) => {
        if (!pattern.test(actual)) {
            throw new Error(`Expected ${actual} to match ${pattern}`);
        }
    },
    toBeDefined: () => {
        if (actual === undefined) {
            throw new Error(`Expected ${actual} to be defined`);
        }
    },
    toBeGreaterThan: (expected: number) => {
        if (actual <= expected) {
            throw new Error(`Expected ${actual} to be greater than ${expected}`);
        }
    },
    toBeLessThan: (expected: number) => {
        if (actual >= expected) {
            throw new Error(`Expected ${actual} to be less than ${expected}`);
        }
    },
    toEqual: (expected: any) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
            throw new Error(`Expected ${JSON.stringify(actual)} to equal ${JSON.stringify(expected)}`);
        }
    }
});

// ===================================================================
// 🧪 TIMESTAMP UTILITY TESTS
// ===================================================================

describe('Timestamp Utility Functions', () => {
    describe('toISTISOString Function', () => {
        test('should convert Unix timestamp to IST ISO format', () => {
            const timestamp = 1704355800123; // 2024-01-04T15:30:00.123Z UTC
            const result = toISTISOString(timestamp);
            
            // Should be in format: YYYY-MM-DDTHH:mm:ss.sss+05:30
            expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
            
            // Should include IST timezone offset
            expect(result).toMatch(/\+05:30$/);
        });

        test('should handle current timestamp', () => {
            const now = Date.now();
            const result = toISTISOString(now);
            
            expect(result).toBeDefined();
            expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
        });

        test('should handle different timestamp values consistently', () => {
            const timestamps = [
                1704355800000, // No milliseconds
                1704355800123, // With milliseconds
                1704355800999  // Max milliseconds
            ];
            
            timestamps.forEach(timestamp => {
                const result = toISTISOString(timestamp);
                expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
            });
        });
    });

    describe('TimestampUtils Class', () => {
        test('getCurrentISTString should return current time in IST', () => {
            const result = TimestampUtils.getCurrentISTString();
            
            expect(result).toBeDefined();
            expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
        });

        test('istISOToTimestamp should convert IST ISO string back to timestamp', () => {
            const originalTimestamp = Date.now();
            const istString = toISTISOString(originalTimestamp);
            const convertedBack = TimestampUtils.istISOToTimestamp(istString);
            
            // Should be within 1ms due to rounding
            const difference = Math.abs(originalTimestamp - convertedBack);
            expect(difference).toBeLessThan(2);
        });

        test('formatForDisplay should provide multiple format options', () => {
            const timestamp = 1704355800123;
            const result = TimestampUtils.formatForDisplay(timestamp);
            
            expect(result.ist).toBeDefined();
            expect(result.utc).toBeDefined();
            expect(result.readable).toBeDefined();
            expect(result.dateOnly).toBeDefined();
            expect(result.timeOnly).toBeDefined();
            
            // Verify IST format
            expect(result.ist).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
            
            // Verify UTC format
            expect(result.utc).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
            
            // Verify date only format
            expect(result.dateOnly).toMatch(/^\d{4}-\d{2}-\d{2}$/);
            
            // Verify time only format
            expect(result.timeOnly).toMatch(/^\d{2}:\d{2}:\d{2}$/);
        });

        test('isISTBusinessHours should correctly identify business hours', () => {
            // Create timestamps for different hours in IST
            const baseDate = new Date('2024-01-04T00:00:00+05:30'); // Midnight IST
            
            // 8 AM IST - Not business hours
            const morning8AM = new Date(baseDate.getTime() + 8 * 60 * 60 * 1000);
            expect(TimestampUtils.isISTBusinessHours(morning8AM.getTime())).toBe(false);
            
            // 10 AM IST - Business hours
            const morning10AM = new Date(baseDate.getTime() + 10 * 60 * 60 * 1000);
            expect(TimestampUtils.isISTBusinessHours(morning10AM.getTime())).toBe(true);
            
            // 6 PM IST - Not business hours
            const evening6PM = new Date(baseDate.getTime() + 18 * 60 * 60 * 1000);
            expect(TimestampUtils.isISTBusinessHours(evening6PM.getTime())).toBe(false);
        });

        test('getISTDayBounds should return correct start and end of day', () => {
            const timestamp = 1704355800123; // Some time during the day
            const bounds = TimestampUtils.getISTDayBounds(timestamp);
            
            expect(bounds.startOfDay).toBeDefined();
            expect(bounds.endOfDay).toBeDefined();
            expect(bounds.endOfDay).toBeGreaterThan(bounds.startOfDay);
            
            // Verify the bounds span exactly one day (24 hours - 1ms)
            const dayDuration = bounds.endOfDay - bounds.startOfDay;
            expect(dayDuration).toBe(24 * 60 * 60 * 1000 - 1); // 23:59:59.999
        });
    });

    describe('date-fns-tz Integration', () => {
        test('should use date-fns-tz formatInTimeZone correctly', () => {
            const timestamp = Date.now();
            const date = new Date(timestamp);
            
            // Direct date-fns-tz usage
            const directResult = formatInTimeZone(
                date,
                'Asia/Kolkata',
                "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
            );
            
            // Our utility function usage
            const utilityResult = toISTISOString(timestamp);
            
            expect(directResult).toBe(utilityResult);
        });

        test('should handle timezone conversion accurately', () => {
            // Fixed timestamp: 2024-01-04T10:00:00.000Z UTC
            const utcTimestamp = 1704362400000;
            const istString = toISTISOString(utcTimestamp);
            
            // Should be 15:30 IST (UTC + 5:30)
            expect(istString).toMatch(/T15:30:00\.000\+05:30$/);
        });

        test('should maintain consistency across multiple conversions', () => {
            const timestamps = [];
            const istStrings = [];
            
            // Generate multiple timestamps and convert them
            for (let i = 0; i < 10; i++) {
                const timestamp = Date.now() + i * 1000;
                timestamps.push(timestamp);
                istStrings.push(toISTISOString(timestamp));
            }
            
            // Verify all conversions follow the same format
            istStrings.forEach(istString => {
                expect(istString).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
            });
            
            // Verify they're in chronological order
            for (let i = 1; i < istStrings.length; i++) {
                expect(istStrings[i] > istStrings[i-1]).toBe(true);
            }
        });
    });

    describe('Performance and Edge Cases', () => {
        test('should handle edge case timestamps', () => {
            const edgeCases = [
                0,                    // Unix epoch
                1000000000000,        // September 9, 2001
                2147483647000,        // Year 2038 problem
                Date.now()            // Current time
            ];
            
            edgeCases.forEach(timestamp => {
                const result = toISTISOString(timestamp);
                expect(result).toBeDefined();
                expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
            });
        });

        test('should be performant for multiple conversions', () => {
            const startTime = Date.now();
            const iterations = 1000;
            
            for (let i = 0; i < iterations; i++) {
                toISTISOString(Date.now() + i);
            }
            
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            // Should complete 1000 conversions in reasonable time (< 1 second)
            expect(duration).toBeLessThan(1000);
            
            console.log(`⚡ Performance: ${iterations} conversions in ${duration}ms`);
        });

        test('should handle daylight saving time correctly', () => {
            // IST doesn't observe daylight saving time, so this should be consistent
            const winterTimestamp = new Date('2024-01-15T12:00:00Z').getTime();
            const summerTimestamp = new Date('2024-07-15T12:00:00Z').getTime();
            
            const winterIST = toISTISOString(winterTimestamp);
            const summerIST = toISTISOString(summerTimestamp);
            
            // Both should have the same timezone offset since IST doesn't change
            expect(winterIST).toMatch(/\+05:30$/);
            expect(summerIST).toMatch(/\+05:30$/);
        });
    });

    describe('Integration with NotificationLog System', () => {
        test('should produce timestamps compatible with DynamoDB', () => {
            const timestamp = Date.now();
            const istString = toISTISOString(timestamp);
            
            // Should be a valid string that can be stored in DynamoDB
            expect(typeof istString).toBe('string');
            expect(istString.length).toBeGreaterThan(20);
            expect(istString.length).toBeLessThan(40);
            
            // Should be sortable lexicographically 
            const earlierTimestamp = timestamp - 1000;
            const earlierISTString = toISTISOString(earlierTimestamp);
            
            expect(earlierISTString < istString).toBe(true);
        });

        test('should support analytics time range queries', () => {
            const now = Date.now();
            const oneHourAgo = now - 3600000;
            const oneDayAgo = now - 86400000;
            
            const nowIST = toISTISOString(now);
            const oneHourAgoIST = toISTISOString(oneHourAgo);
            const oneDayAgoIST = toISTISOString(oneDayAgo);
            
            // Should maintain chronological order
            expect(oneDayAgoIST < oneHourAgoIST).toBe(true);
            expect(oneHourAgoIST < nowIST).toBe(true);
            
            // Should be useful for filtering operations
            const timeRange = [oneDayAgoIST, oneHourAgoIST, nowIST].sort();
            expect(timeRange[0]).toBe(oneDayAgoIST);
            expect(timeRange[1]).toBe(oneHourAgoIST);
            expect(timeRange[2]).toBe(nowIST);
        });
    });
});

// ===================================================================
// 🏃‍♂️ RUN TESTS
// ===================================================================

export function runTimestampTests() {
    console.log('🧪 Running Timestamp Utility Tests...\n');
    
    try {
        // Run the test demonstration
        console.log('📋 Running timestamp demonstration...');
        demonstrateTimestampHandling();
        
        // Run actual tests would go here
        console.log('\n✅ All timestamp utility tests completed successfully!');
        
    } catch (error) {
        console.error('\n❌ Timestamp tests failed:', error);
        throw error;
    }
}

// Auto-run tests if this file is executed directly
if (require.main === module) {
    runTimestampTests();
} 