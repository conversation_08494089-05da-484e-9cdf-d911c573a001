"use client"

import { TrendingUp } from "lucide-react"
import { <PERSON>, <PERSON>hart, CartesianGrid, XAxis, YAxis } from "recharts"
import { ChartConfig, ChartContainer, ChartLegend, ChartLegendContent, ChartTooltip, ChartTooltipContent } from "./chart"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "./card"
import { BcSalesDashboardDto } from "~/types/api/businessConsoleService/SalesAnalysis"
import { format } from "date-fns"
import { useState } from "react"



interface SalesAnalysisDashBoardProps {
      salesData: BcSalesDashboardDto

}



const chartConfig = {
      desktop: {
            label: "Desktop",
            color: "hsl(120, 90%, 50%)",
      },
      mobile: {
            label: "Mobile",
            color: "hsl(120, 90%, 50%)",
      },
} satisfies ChartConfig

const generateColor = (index: number) => `hsl(${(index * 137) % 360}, 90%, 50%)`

export function SalesAnalysisDashBoard({ salesData }: SalesAnalysisDashBoardProps) {


      console.log(salesData.totalSales, "676776767676776676767867868")

      console.log(" Sales Data:", salesData);


      console.log("Total Sales Data:", salesData?.totalSales);
      // // Extract unique dates from rows
      // const uniqueDates = Array.from(
      //       new Set(
      //           salesData?.rows?.flatMap(row =>
      //               row?.cols?.map(col => col?.deliveryDate) || []
      //           ) || []
      //       )
      //   ).sort();

      // Map data to get total sales per date




      const uniqueDates = Array.from(
            new Set(salesData?.totalSales?.cols?.map(col => col.deliveryDate) || [])
      ).sort();



      let barComponents;
      const chartData = uniqueDates.map(date => {
            const totalSalesQty = salesData?.totalSales?.cols?.reduce((acc, col) => {
                  return col.deliveryDate === date ? acc + (col.salesQty || 0) : acc;
            }, 0);
            return { date, salesQty: totalSalesQty.toFixed(2) };
      });

      barComponents = (
            <>
                  <Bar
                        dataKey="salesQty"
                        stackId="a"
                        fill="hsl(210, 90%, 50%)"
                        radius={[0, 0, 0, 0]}
                        label={{ position: 'top', fill: '#000' }}
                        barSize={chartData.length === 1 ? 30 : undefined} // Fix size only when 1 data point

                  />

            </>
      );
      return (
            <Card className="mt-8">
                  <CardHeader>
                        <CardTitle>Sales Analysis Chart</CardTitle>
                        {/* <CardDescription>January - June 2024</CardDescription> */}
                  </CardHeader>
                  <CardContent>
                        <ChartContainer config={chartConfig}>

                              <BarChart accessibilityLayer data={chartData} >

                                    <CartesianGrid vertical={false} />
                                    <XAxis
                                          dataKey="date"
                                          tickLine={false}
                                          tickMargin={10}
                                          axisLine={false}
                                          tickFormatter={(value) => format(new Date(value), "EEE dd/MM")}
                                    />
                                    <YAxis />

                                    <ChartTooltip content={<ChartTooltipContent hideLabel />} />
                                    <ChartLegend content={<ChartLegendContent />} />
                                    {barComponents}

                                    <ChartTooltip
                                          cursor={false}
                                          content={<ChartTooltipContent indicator="dashed" />}
                                    />
                              </BarChart>
                        </ChartContainer>

                  </CardContent>

            </Card>
      )
}




