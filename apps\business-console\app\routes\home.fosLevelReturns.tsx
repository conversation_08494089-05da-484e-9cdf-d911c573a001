import { useLoaderData, useSubmit, useSearchParams } from "@remix-run/react";
import { LoaderFunction, json } from "@remix-run/node";
import jwt from "jsonwebtoken";
import { useEffect, useState } from "react";
import { withAuth } from "~/utils/auth-utils";

// Environment variables
const METABASE_SECRET_KEY = process.env.METABASE_SECRET_KEY || "";
const METABASE_SITE_URL = process.env.METABASE_SITE_URL || "http://43.205.118.52:4001";

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
      // const sellerId = user.sellerId;
      // Get the date from query parameters
      const url = new URL(request.url);
      // const dateRange = url.searchParams.get("dateRange") || new Date().toISOString().split("T")[0]; // Default to today

      if (!METABASE_SECRET_KEY) {
            throw new Error("Metabase secret key is not configured.");
      }

      const payload = {
            resource: { question: 81 },
            params: {
                  // Pass the selected date to Metabase
            },
            exp: Math.round(Date.now() / 1000) + 10 * 60, // Expires in 10 minutes
      };

      const token = jwt.sign(payload, METABASE_SECRET_KEY);
      const embedUrl = `${METABASE_SITE_URL}/embed/question/${token}#bordered=false&titled=false`;

      return json({ embedUrl });
});

export default function FOSLevelReturns() {
      const { embedUrl } = useLoaderData<typeof loader>();
      const [isLoading, setIsLoading] = useState(true);
      // const [searchParams] = useSearchParams();

      // Handle iframe loading state
      useEffect(() => {
            if (embedUrl) {
                  setIsLoading(false);
            }
      }, [embedUrl]);

      // Scroll synchronization
      useEffect(() => {
            const handleScroll = () => {
                  const iframe = document.getElementById("metabase-iframe") as HTMLIFrameElement;
                  if (iframe) {
                        iframe.contentWindow?.scrollTo(0, window.scrollY);
                  }
            };

            window.addEventListener("scroll", handleScroll);
            return () => window.removeEventListener("scroll", handleScroll);
      }, []);

      // Handle date input change
      // const handleDateRangeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      //       const newDateRange = event.target.value;
      //       submit({ dateRange: newDateRange }, { method: "get", action: "." });
      // };

      return (
            <div className="flex min-h-screen">
                  <main className="flex-1 overflow-y-auto">
                        <div className="p-4 sm:p-6">
                              <div className="flex justify-between items-center mb-4">
                                    <h1 className="text-2xl font-bold text-gray-800">FOS Level Returns Report</h1>
                                    {/* <div>
                                          <label htmlFor="dateRange" className="mr-2 text-gray-700">
                                                Select Delivery Date:
                                          </label>
                                          <input
                                                type="date"
                                                id="dateRange"
                                                name="dateRange"
                                                value={dateRange}
                                                onChange={handleDateRangeChange}
                                                className="border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                max={new Date().toISOString().split("T")[0]} // Optional: Prevent future dates
                                          />
                                    </div> */}
                              </div>

                              <div className="bg-white shadow-md rounded-md overflow-hidden">
                                    {isLoading ? (
                                          <div className="flex justify-center items-center h-96">
                                                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                                          </div>
                                    ) : embedUrl ? (
                                          <iframe
                                                id="metabase-iframe"
                                                src={embedUrl}
                                                title="Metabase Dashboard"
                                                className="w-full h-[600px] border-0"
                                                allowTransparency
                                          />
                                    ) : (
                                          <div className="p-6 text-center text-red-500">
                                                Failed to load the dashboard.
                                          </div>
                                    )}
                              </div>
                        </div>
                  </main>
            </div>
      );
}