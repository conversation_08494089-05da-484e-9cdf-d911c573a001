

import { ActionFunctionArgs } from "@remix-run/node";
import { Form, json, useFetcher, useLoaderData, useSearchParams } from "@remix-run/react";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "~/components/ui/button";
import { Calendar } from "~/components/ui/calendar";
import { Input } from "~/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { getTripSummary } from "~/services/tripsSummary";
import { TripSummary } from "~/types/api/businessConsoleService/tripSummaryDetails";
import { withAuth, withResponse } from "@utils/auth-utils";
import React from "react";
import { DateRange } from "react-day-picker";
import { cn } from "lib/utils";



export const loader = withAuth(async ({ request }) => {
      try {
            const url = new URL(request.url);
            const fromDate = url.searchParams.get("from") || new Date().toISOString().split("T")[0];
            const toDate = url.searchParams.get("to") || fromDate;
            if (!fromDate || !toDate) {
                  throw json(
                        { error: "Date is required" },
                        { status: 400 }
                  );
            }

            const response = await getTripSummary(fromDate, toDate, request);
            return withResponse({ data: response.data }, response.headers);
      } catch (error) {
            console.error('Trip summary error:', error);
            throw new Error(`Error fetching trip details: ${error}`);
      }
});

export default function TripManagement() {
      const data = useLoaderData<{ data: TripSummary[] }>();
      const [date, setDate] = useState<Date | undefined>(new Date());
      const [searchTerm, setSearchTerm] = useState("")
      const fetcher = useFetcher<{ data: TripSummary[] }>()
      const [tripData, setTriData] = useState<TripSummary[] | []>(data.data || [])

      const handleSubmit = (date: Date | undefined) => {

            if (!dateRange.from) return; // Ensure 'from' date exists

            const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
            const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;
            const formData = new FormData();
            formData.append("from", formattedFrom)
            formData.append("to", formattedTo)

            fetcher.submit(formData, { method: "GET" });
      }
      const itemsPerPage = 200
      const [currentPage, setCurrentPage] = useState(1)

      const totalPages = Math.ceil(tripData.length / itemsPerPage);

      const startIndex = (currentPage - 1) * itemsPerPage;

      const currentTrips = tripData.slice(startIndex, startIndex + itemsPerPage);

      useEffect(() => {

            if (fetcher.data) {
                  setTriData(fetcher.data.data)
            }

      }, [fetcher.data?.data])

      const filterTrips = (trip: TripSummary) => {
            return (
                  (trip.sellerName?.toLowerCase() || "").includes(searchTerm.toLowerCase()) ||
                  (trip.tripId?.toString().toLowerCase() || "").includes(searchTerm.toLowerCase()) ||
                  (trip.driverName?.toLowerCase() || "").includes(searchTerm.toLowerCase())
            );
      }


      const handleSetPage = (page: number) => {
            if (page >= 1 && page <= totalPages) {
                  setCurrentPage(page)
            }
      }

      const handleSort = (trip: TripSummary) => {

            return trip.tripStatus === "Dispatched"

      }
      const [searchParams] = useSearchParams();

      const startDate = searchParams.get("from")
      const endDate = searchParams.get("to")

      const [dateRange, setDateRange] = React.useState<DateRange>({
            from: startDate ? new Date(startDate) : new Date(),
            to: endDate ? new Date(endDate) : new Date(),
      });


      const handleDateChange = (range: DateRange | undefined) => {
            if (!range?.from) return; // Ensure 'from' is never undefined

            setDateRange({
                  from: range.from,
                  to: range.to || undefined, // If 'to' is not selected, keep it undefined
            });
      };


      return (
            <div className="container mx-auto w-full" >
                  <div className="flex my-7 space-x-10">
                        <div className=" flex space-x-2">

                              <div className="w-full md:w-auto">
                                    <Popover>
                                          <PopoverTrigger asChild>
                                                <Button
                                                      id="date"
                                                      variant={"outline"}
                                                      className={cn(
                                                            "w-[300px] justify-start text-left font-normal",
                                                            !dateRange.from && "text-muted-foreground"
                                                      )}
                                                >
                                                      <CalendarIcon />
                                                      {dateRange?.from ? (
                                                            dateRange.to ? (
                                                                  <>
                                                                        {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                                                                  </>
                                                            ) : (
                                                                  format(dateRange.from, "LLL dd, y")
                                                            )
                                                      ) : (
                                                            <span>Pick a date</span>
                                                      )}
                                                </Button>

                                          </PopoverTrigger>
                                          <PopoverContent className="w-auto p-0" align="start">
                                                <Calendar
                                                      initialFocus
                                                      selected={dateRange}
                                                      mode="range" // Enable range selection
                                                      onSelect={handleDateChange}
                                                />
                                          </PopoverContent>
                                    </Popover>
                              </div>
                              <Button onClick={() => handleSubmit(date)}  >
                                    Get Trips
                              </Button>
                        </div>

                        <Input placeholder="Search By tripId.Seller,DriverName"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"
                        />
                  </div>
                  <Table>
                        <TableHeader className="bg-gray-100">
                              <TableHead>TripId</TableHead>
                              <TableHead>Seller</TableHead>
                              <TableHead>Driver</TableHead>
                              <TableHead>Orders</TableHead>
                              <TableHead>Total Delivered Order</TableHead>
                              <TableHead>D.Date</TableHead>
                              <TableHead>Status</TableHead>
                        </TableHeader>
                        <TableBody>
                              {currentTrips.length > 0 ? currentTrips.sort((a, b) => {
                                    const Priority: { [key: string]: number } = { Dispatched: 1, Open: 2 };
                                    return (Priority[a.tripStatus] || 3) - (Priority[b.tripStatus] || 3);
                              }).filter((trip) => filterTrips(trip)).map((x) => (
                                    <TableRow key={x.tripId}>
                                          <TableCell> {x.tripId}  </TableCell>
                                          <TableCell>{`${x.sellerId}-${x.sellerName}`}</TableCell>
                                          <TableCell>{x.driverName}</TableCell>
                                          <TableCell>{x.totalOrders}</TableCell>
                                          <TableCell>{x.totalDeliveredOrders}</TableCell>
                                          <TableCell>{x?.deliveryDate ? format(x.deliveryDate, "MM-dd-yyyy") : ""}</TableCell>
                                          <TableCell className={x.tripStatus === "Dispatched" ? "text-red-500" : x.tripStatus === "Open" ? "text-orange-500" : "text-green-600"}>{x.tripStatus}</TableCell>
                                    </TableRow>
                              )

                              ) : (
                                    <TableRow>
                                          <TableCell
                                                colSpan={9}
                                                className="h-24 text-center"
                                          >
                                                No results.
                                          </TableCell>
                                    </TableRow>
                              )
                              }
                        </TableBody>
                  </Table>
                  <div className="flex items-center space-x-2 my-2">
                        <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSetPage(currentPage - 1)}
                              disabled={currentPage === 1}
                        >
                              Previous
                        </Button>
                        <span className="text-gray-700">
                              Page {currentPage} of {totalPages}
                        </span>
                        <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                    handleSetPage(currentPage + 1)
                              }
                              disabled={currentPage === totalPages}
                        >
                              Next
                        </Button>
                  </div>
            </div>
      )
}
