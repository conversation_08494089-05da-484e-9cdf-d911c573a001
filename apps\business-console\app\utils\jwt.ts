import jwt from "jsonwebtoken";

export function isTokenExpired(token: string) {
  try {
    // Decode the token without verifying the signature
    const decoded = jwt.decode(token);
    // console.log("decoded token123", decoded);
    if (!decoded || !decoded.exp) {
      return true;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    const bufferTime = 3600;

    // Check if the token is expired
    return decoded.exp < currentTime + bufferTime - 60;
  } catch (error) {
    // If token decoding fails, treat it as expired
    return true;
  }
}


const JWT_SECRET = "process.env.JWT_SECRET";
if (!JWT_SECRET) {
  throw new Error("JWT_SECRET must be set");
}

export function generateSessionToken(sessionData: {
  access_token: string;
  refresh_token: string;
  data: Record<string, any>;
}): string {
  return jwt.sign(sessionData, JWT_SECRET, { expiresIn: "5m" }); // Token valid for 5 minutes
}