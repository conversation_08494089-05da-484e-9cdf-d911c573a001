import { MyAddonData } from "~/types/api/businessConsoleService/SellerManagement";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import React, { useState, useEffect } from "react";
import { useFetcher, useSearchParams } from "@remix-run/react";

interface EditModalProps {
      isOpen: boolean;
      data: MyAddonData;
      onClose: () => void;
      onSave: (updatedData: MyAddonData) => void;
      header: string;
      sellerId: number
}

const AddonsModal: React.FC<EditModalProps> = ({ isOpen, data, onClose, onSave, header, sellerId }) => {
      const [formData, setFormData] = useState<MyAddonData>(data); // Ensure correct typing
      const [loading, setLoading] = useState(false); // Loading state

      useEffect(() => {
            setFormData(data);
      }, [data]);

      const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
            const { name, value } = e.target;
            setFormData((prevData) => ({
                  ...prevData,
                  [name]: name === "active" ? value === "true" : value // Convert active to boolean
            }));
      };
      const addonFetcher = useFetcher()

      const handleSave = () => {
            const fetcherData = new FormData();
            console.log(formData, "kkkkkkkkkkkkkkkkk")
            fetcherData.append("actionType", "addonsAdd");
            fetcherData.append("sellerId", sellerId as unknown as string);

            fetcherData.append("addonData", JSON.stringify(formData))
            addonFetcher.submit(fetcherData, { method: "POST" })
            onClose()
      };

      if (!isOpen) return null;

      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg">
                        <DialogTitle className="text-xl font-bold text-gray-900 sm:text-2xl">{header}</DialogTitle>

                        <div className="mt-4 space-y-5 max-h-[60vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                              {/* Editable Fields */}
                              <div className="space-y-4">
                                    {[
                                          { name: "name", type: "text", label: "Name" },
                                          { name: "diet", type: "select", label: "Diet" },
                                    ].map((field) => (
                                          <div key={field.name} className="flex flex-col gap-2 sm:flex-row sm:items-center">
                                                <label className="w-full text-sm font-medium text-gray-700 sm:w-1/3">{field.label}</label>
                                                {field.name === "diet" ? (
                                                      <select
                                                            name={field.name}
                                                            value={formData?.[field.name] || ""}
                                                            onChange={handleChange}
                                                            className="w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3"
                                                      >
                                                            <option value="">Select Diet</option>
                                                            <option value="veg">Veg</option>
                                                            <option value="nonveg">Nonveg</option>
                                                            <option value="egg">Egg</option>
                                                            <option value="vegan">vegan</option>
                                                            <option value="na">Na</option>

                                                      </select>
                                                ) : (
                                                      <input
                                                            type={field.type}
                                                            name={field.name}
                                                            value={formData?.[field.name] || ""}
                                                            onChange={handleChange}
                                                            className="w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3"
                                                            placeholder={`Enter ${field.label}`}
                                                      />
                                                )}
                                          </div>
                                    ))}
                              </div>

                              {/* Radio Button Group */}
                              <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                                    <label className="w-full text-sm font-medium text-gray-700 sm:w-1/3">Active Status</label>
                                    <div className="flex gap-6 w-full sm:w-2/3">
                                          <label className="flex items-center gap-2 text-sm text-gray-600 cursor-pointer">
                                                <input
                                                      type="radio"
                                                      name="active"
                                                      value="true"
                                                      checked={formData?.active === true}
                                                      onChange={handleChange}
                                                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                                />
                                                Yes
                                          </label>
                                          <label className="flex items-center gap-2 text-sm text-gray-600 cursor-pointer">
                                                <input
                                                      type="radio"
                                                      name="active"
                                                      value="false"
                                                      checked={formData?.active === false}
                                                      onChange={handleChange}
                                                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                                />
                                                No
                                          </label>
                                    </div>
                              </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end">
                              <button
                                    onClick={onClose}
                                    className="w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto"
                              >
                                    Cancel
                              </button>
                              <button
                                    onClick={handleSave}
                                    disabled={loading} // Disable button while loading
                                    className={`w-full rounded-lg ${loading ? 'bg-gray-400' : 'bg-blue-600'} px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:w-auto`}
                              >
                                    {loading ? 'Saving...' : 'Save'} {/* Show loader text */}
                              </button>
                        </div>
                  </DialogContent>
            </Dialog>
      );
};

export default AddonsModal;