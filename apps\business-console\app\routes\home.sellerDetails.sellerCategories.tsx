import { j<PERSON>, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON><PERSON><PERSON>, useNavigate } from "@remix-run/react";
import { Pencil } from "lucide-react";
import { useEffect, useState } from "react";
import EditModal from "~/components/common/EditModal";
import { Dialog, DialogContent, DialogTitle } from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import ResponsivePagination from "~/components/ui/responsivePagination";
import { ResponsiveTable } from "~/components/ui/responsiveTable";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { useToast } from "~/components/ui/ToastProvider";
import { useDebounce } from "~/hooks/useDebounce";
import { getSeCategories, updateSellerCategory } from "~/services/businessConsoleService";
import { getUserRoles } from "~/services/masterItemCategories";
import { MasterItemCategories } from "~/types/api/businessConsoleService/MasterItemCategory";
import { withAuth, withResponse } from "~/utils/auth-utils";

interface LoaderData {
  googleMapsApiKey: string
  sellerId: number,
  sellerName: string,
  activeTab: string,
  url: string,
  roles: { value: string; label: string }[],
  sellerBId: number;
  sellerCategories: MasterItemCategories[],
  userId: number;
  permission: string[];
  currentPage: number;
}

export const loader = withAuth(async ({ request, user }) => {
  const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || ''
  const url = new URL(request.url);
  const sellerId = Number(url.searchParams.get("sellerId"));
  const sellerName = (url.searchParams.get("sellerName"));
  const sellerBId = Number(url.searchParams.get("sellerBId"));
  const userId = user.userId;
  const permission = user.userDetails.roles;
  const activeTab = url.searchParams.get("activeTab") || 'SellerCategories';
  let sellerCategories: MasterItemCategories[] | [] = [];

  const page = parseInt(url.searchParams.get("page") || "0");
  const pageSize = parseInt(url.searchParams.get("pageSize") || "50");
  const matchBy = url.searchParams.get("matchBy") || "";
  let response;
  try {
    switch (activeTab) {
      case "SellerCategories":
        response = await getSeCategories(sellerId, page, pageSize, matchBy, request)
        sellerCategories = response?.data || []
        break;
    }
    const roleResponse = await getUserRoles(request);
    const roleData = roleResponse.data as string[];
    // Mapping fetched roles to the correct labels
    const roleLabels: Record<string, string> = {
      SellerOwner: "Owner",
      DriverFull: "Driver",
      AgentFull: "Sales Agent",
      SellerManager: "Manager",
      SellerSupervisor: "Supervisor",
      AdvancedBuyer: "Buyer",
      PickerFull: "Warehouse Helper/Picker",
      ContractPriceFull: "ContractPriceFull",
      NetworkManager: "NetworkManager(mNET)",
      MnetManager: "MnetManager(mNET)",
      MnetAdmin: "MnetAdmin(mNET)",
      SalesManager: "SalesManager(mNET)",
      MnetAgent: "Agent(mNET)",
      WhatsappFull: "WhatsappFull(mNET)",
      FmSalesManager: "FmSalesManager",
      SC_Basic: "SellerBasic",
      OC_Manager: "OperationManager",
      AC_Basic: "AccountManager"
    };
    // Transform roleData into an array of `{ value, label }` objects
    const roles = roleData
      .filter((role) => roleLabels[role]) // Filter only the valid roles
      .map((role) => ({ value: role, label: roleLabels[role] }));
    return withResponse({
      googleMapsApiKey,
      sellerId,
      sellerName,
      activeTab,
      url,
      roles,
      sellerBId,
      sellerCategories,
      userId,
      permission,
      page,
    }, response?.headers);
  } catch (error) {
    console.log("loader failed");
    console.error("Error in loader:", error);
    // Return a JSON-based error shape
    return [];
  }
});

interface ActionData {
  success: boolean;
}

export const action = withAuth(async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get("intent");
  const sellerCategory = formData.get("sellerCategory") as string;
  const categoryId = formData.get("categoryId") as unknown as number;
  const sellerId = formData.get("sellerId") as unknown as number;
  if (intent === "editSellerCategory") {
    try {
      const response = await updateSellerCategory(sellerId, categoryId, JSON.parse(sellerCategory), request);
      return withResponse({ success: true }, response.headers);
    }
    catch (error) {
      return json({ success: false }, { status: 400 })
    }
  }
  return json({ success: false }, { status: 400 });
});

const sellerCategoryHeader = [
  "Category Id",
  "Category Name",
  "No. of items",
  "Sequence",
  ""
];

export default function SellerCategories() {
  const { sellerId, sellerName, activeTab, sellerBId, currentPage, sellerCategories } = useLoaderData<LoaderData>();
  const navigate = useNavigate()
  const fetcher = useFetcher<ActionData>()
  const [searchTerm, setSearchTerm] = useState('');
  const [pageSize, setPageSize] = useState("10");
  const [pageNum, setPageNum] = useState(0);

  const [selectedCategory, setSelectedCategory] = useState<MasterItemCategories | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const handlePageSizeChange = (newPageSize: string) => {
    setPageSize(newPageSize)
    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${newPageSize}&matchBy=${searchTerm}`)
  }
  const handlePageChange = (newPageSize: string) => {
    setPageNum(Number(newPageSize))
    navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${Number(newPageSize)}&pageSize=${pageSize}&matchBy=${searchTerm}`)
  }

  const handleEditModal = (row: any) => {
    setSelectedCategory(row);
    setIsEditModalOpen(true);
  };
  const handleSave = (updatedData: any) => {
    console.log("Updated Data:", updatedData);
    const formData = new FormData()
    formData.append("intent", "editSellerCategory");
    formData.append("sellerCategory", JSON.stringify(updatedData));
    formData.append("categoryId", updatedData.id);
    formData.append("sellerId", sellerId.toString());
    fetcher.submit(formData, { method: "put" })
    setIsEditModalOpen(false);
  };

  const debounceSearchTerm = useDebounce(searchTerm, 300);
  const handlePageSearch = (value: string) => {
    setSearchTerm(value);
  }
  useEffect(() => {
    if (debounceSearchTerm.length >= 3) {
      // Perform search when the input has 3 or more characters
      navigate(
        `?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}&matchBy=${encodeURIComponent(debounceSearchTerm)}`
      );
    } else {
      // Reset search when input is less than 3 characters
      navigate(
        `?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}`
      );
    }
  }, [debounceSearchTerm])

  useEffect(() => {
    if (isEditModalOpen === false) {
      setSelectedCategory(null)
    }
  }, [isEditModalOpen])

  const { showToast } = useToast()
  const [isSuccess, setIsSuccess] = useState(false);

  useEffect(() => {
    if (fetcher?.data?.success === true) {
      setIsSuccess(true)
      showToast("Sucessfully updated category sequence", "success")
      //revalidate
      navigate(`?sellerId=${sellerId}&sellerName=${sellerName}&activeTab=${activeTab}&sellerBId=${sellerBId}&page=${currentPage}&pageSize=${pageSize}&matchBy=${searchTerm}`)
    }
    else if (fetcher.data?.success === false) {
      setIsSuccess(false)
      showToast("Failed to Update category sequence", "error")
    }
  }, [fetcher.data])

  return (
    <div>
      <div className="flex justify-between my-4">
        <Input
          placeholder="Search by Category Name"
          value={searchTerm}
          type='search'
          onChange={(e) => handlePageSearch(e.target.value)}
          className="max-w-sm  rounded-full"
        />

        <Select value={pageSize} onValueChange={handlePageSizeChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Categories per page" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 per page</SelectItem>
            <SelectItem value="10">10 per page</SelectItem>
            <SelectItem value="20">20 per page</SelectItem>
            <SelectItem value="50">50 per page</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <ResponsiveTable
        headers={sellerCategoryHeader}
        data={sellerCategories}
        renderRow={(row) => {
          return (
            <tr key={row.id} className="border-b">
              <td className="py-2 px-3 text-center whitespace-normal break-words sticky left-0 bg-white z-10"
              >{row.id}</td>
              <td className="py-2 px-3 text-center sticky left-36 bg-white z-10">
                <div className="flex items-center gap-2 justify-center p-1 rounded-md" >
                  <img
                    src={row?.picture}
                    alt="image"
                    className="h-10 w-10 rounded-full object-cover flex-shrink-0"
                  />
                  <span className="w-40 flex-wrap break-words text-left cursor-pointer" >
                    {row?.name}
                  </span>
                </div>
              </td>
              <td className="py-2 px-3 text-center  whitespace-normal break-words"
              >
                {row?.totalItems}
              </td>
              <td className="py-2 px-3 text-center  whitespace-normal break-words flex flex-row gap-2 justify-center"
              >
                {row?.sequence}
                <Pencil className="w-4 h-4 my-auto cursor-pointer" onClick={() => handleEditModal(row)} />
              </td>
            </tr>
          )
        }}
      />
      <div className="flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap">
        <h2 className="shrink-0">Current Page: {pageNum + 1}</h2>
        <div className="overflow-x-auto">
          <ResponsivePagination
            totalPages={Number(pageSize)}
            currentPage={pageNum}
            onPageChange={(pageNum) => handlePageChange(pageNum.toString())}
          />
        </div>
      </div>
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg">
          <DialogTitle className="text-xl font-bold text-gray-900 sm:text-2xl">Edit Category</DialogTitle>
          <div className="flex flex-col gap-4">
            <label>Category Sequence</label>
            <Input
              id="sequence"
              placeholder="Enter sequence"
              type="number"
              className="w-full h-8"
              required
              value={selectedCategory?.sequence}
              onChange={(e) => selectedCategory && setSelectedCategory({ ...selectedCategory, sequence: Number(e.target.value) })}
            />
          </div>
          <div className="mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end">
            <button
              onClick={() => setIsEditModalOpen(false)}
              className="w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto"
            >
              Cancel
            </button>
            <button
              onClick={() => handleSave(selectedCategory)}
              className="w-full rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:w-auto"
            >
              Save
            </button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
