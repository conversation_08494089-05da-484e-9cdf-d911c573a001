import React, { useState, useEffect, useRef } from "react";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import { networkAgents } from "~/types/api/businessConsoleService/SellerManagement";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { NetworkBuyer } from "~/types/api/businessConsoleService/Network";

interface EditBuyerModalProps {
  isOpen: boolean;
  networkBuyer: Partial<NetworkBuyer>;
  networkAgents: networkAgents[];
  onClose: () => void;
  onSave: (updatedData: any) => void;
}

const EditBuyerModal: React.FC<EditBuyerModalProps> = ({
  isOpen,
  networkBuyer,
  networkAgents,
  onClose,
  onSave,
}) => {
  const [formData, setFormData] = useState(
    {
      networkBuyerId: networkBuyer.networkBuyerId,
      agentUserId: "",
    }
  );

  useEffect(() => {
    setFormData({
      networkBuyerId: networkBuyer.networkBuyerId,
      agentUserId: "",
    });
  }, [networkBuyer]);

  const handleAgentChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      agentUserId: value
    }));
  };

  const handleSave = () => {
    onSave(formData);
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="p-4 bg-white rounded-lg shadow-lg w-full max-w-md sm:max-w-lg md:max-w-xl max-h-[80vh] flex flex-col"
        style={{ width: "95vw" }}
      >
        <DialogTitle className="text-xl font-bold mb-2 text-center">Edit Network Buyer</DialogTitle>

        <div className="space-y-2">
          <label htmlFor="overrideAgentName" className="text-sm font-medium text-gray-700">
            OverrideAgentName
          </label>
          <Select value={formData.agentUserId} onValueChange={handleAgentChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select an agent" />
            </SelectTrigger>
            <SelectContent position="popper" side="bottom" className="max-h-60 overflow-y-auto z-[9999]">
              {networkAgents.map((agent) => (
                <SelectItem key={agent.agentUserId} value={agent.agentUserId.toString()}>
                  {agent.fullName} ({agent.businessName})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col sm:flex-row justify-end gap-2 mt-6">
          <button
            onClick={onClose}
            className="w-full sm:w-auto px-5 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 transition"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="w-full sm:w-auto px-5 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
          >
            Save
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditBuyerModal;