// File: app/routes/home.whatsappconnect.tsx
import {json, type LoaderFunction, type ActionFunction} from '@remix-run/node';
import {useLoaderData} from '@remix-run/react';
import {withAuth} from "~/utils/auth-utils";
import {getFirebaseAdmin} from "~/services/firebase.server";
import {WhatsAppConnectView} from '@components/whatsapp/WhatsAppConnectView';
import {
    getSubscribedApps,
    getWhatsAppBusinessAccounts,
    getWhatsAppPhoneNumbers,
    subscribeApp, unsubscribeApp
} from '~/services/whatsappApi.server';
import type {WhatsAppConnectionData} from "~/types/whatsapp";
import {saveWABToken} from "@services/businessConsoleService";

type LoaderData = {
    FACEBOOK_APP_ID: string;
    connectionState: WhatsAppConnectionData | null;
};

export const loader: LoaderFunction = withAuth(async ({user}) => {
    console.log('WhatsAppConnect loader called', { 
        userId: user?.userId,
        businessName: user?.businessName,
        sellerId: user?.userDetails?.sellerId
    });
    
    if (!user) {
        console.log('WhatsAppConnect loader: No user found, returning default state', {
            FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID
        });
        return json({FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID, connectionState: null});
    }

    const db = getFirebaseAdmin();
    console.log('WhatsAppConnect loader: Fetching connection data from path', {
        collection: 'facebook-connects',
        docId: user.userDetails.sellerId.toString()
    });
    
    const doc = await db.collection('facebook-connects')
        .doc(user.userDetails.sellerId.toString()).get();

    const connectionState = doc.exists ? doc.data() as WhatsAppConnectionData : null;
    console.log('WhatsAppConnect loader: Connection state retrieved', { 
        sellerId: user.userDetails.sellerId, 
        connected: !!connectionState,
        lastUpdated: connectionState?.updatedAt || 'N/A',
        hasWabaId: !!connectionState?.wabaId,
        hasPhoneNumber: !!connectionState?.mNetConnectedPhoneNumber
    });

    return json({
        FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID,
        connectionState
    });
});

export const action: ActionFunction = withAuth(async ({user, request}) => {
    const formData = await request. formData();
    const actionType = formData.get('actionType');
    console.log('WhatsAppConnect action called', { 
        actionType, 
        userId: user.userId,
        sellerId: user.userDetails.sellerId,
        formData: Object.fromEntries(formData.entries()) 
    });
    
    const db = getFirebaseAdmin();
    const docRef = db.collection('facebook-connects').doc(user.userDetails.sellerId.toString());

    switch (actionType) {
        case 'exchange-token': {
            const code = formData.get('code')?.toString();
            if (!code) return json({error: 'Code required'}, {status: 400});
            console.log('WhatsAppConnect action: Exchanging token', { 
                sellerId: user.userDetails.sellerId,
                code: code.substring(0, 10) + '...' // Only log part of the code for security
            });

            const tokenUrl = new URL('https://graph.facebook.com/v21.0/oauth/access_token');
            tokenUrl.searchParams.set('client_id', process.env.FACEBOOK_APP_ID!);
            tokenUrl.searchParams.set('client_secret', process.env.FACEBOOK_APP_SECRET!);
            tokenUrl.searchParams.set('code', code);
            console.log('WhatsAppConnect action: Token URL', { 
                url: tokenUrl.toString().replace(code, '[REDACTED]') 
            });

            const response = await fetch(tokenUrl.toString());
            const tokenData = await response.json();
            console.log('WhatsAppConnect action: Token response', { 
                status: response.status,
                ok: response.ok,
                tokenData: {
                    ...tokenData,
                    access_token: tokenData.access_token ? `${tokenData.access_token.substring(0, 10)}...` : null
                }
            });

            if (!response.ok) {
                console.log('WhatsAppConnect action: Token exchange failed', { 
                    status: response.status, 
                    error: tokenData.error?.message,
                    errorCode: tokenData.error?.code,
                    errorType: tokenData.error?.type
                });
                return json({error: tokenData.error?.message || 'Token exchange failed'}, {status: 500});
            }

            const connectionData: WhatsAppConnectionData = {
                access: tokenData,
                user: {
                    userId: user.userId,
                    userName: user.userName,
                },
                businessName: user.businessName,
                sellerId: user.userDetails.sellerId,
                updatedAt: new Date().toISOString()
            };

            console.log('WhatsAppConnect action: Saving connection data', {
                userId: connectionData.user.userId,
                businessName: connectionData.businessName,
                sellerId: connectionData.sellerId,
                tokenExpires: connectionData.access.expires_in,
                updatedAt: connectionData.updatedAt
            });
            
            await docRef.set(connectionData);
            console.log('WhatsAppConnect action: Token exchange successful and data saved');

            return json({success: true});
        }

        case 'exchange-token-onboarding': {
            const code = formData.get('code')?.toString();
            if (!code) return json({error: 'Code required'}, {status: 400});
            console.log('WhatsAppConnect action: Exchanging token for onboarding', { 
                sellerId: user.userDetails.sellerId,
                code: code.substring(0, 10) + '...' // Only log part of the code for security
            });

            const tokenUrl = new URL('https://graph.facebook.com/v21.0/oauth/access_token');
            tokenUrl.searchParams.set('client_id', process.env.FACEBOOK_APP_ID!);
            tokenUrl.searchParams.set('client_secret', process.env.FACEBOOK_APP_SECRET!);
            tokenUrl.searchParams.set('code', code);
            console.log('WhatsAppConnect action: Token URL for onboarding', { 
                url: tokenUrl.toString().replace(code, '[REDACTED]') 
            });

            const response = await fetch(tokenUrl.toString());
            const tokenData = await response.json();
            console.log('WhatsAppConnect action: Onboarding token response', { 
                status: response.status,
                ok: response.ok,
                tokenData: {
                    ...tokenData,
                    access_token: tokenData.access_token ? `${tokenData.access_token.substring(0, 10)}...` : null
                }
            });

            if (!response.ok) {
                console.log('WhatsAppConnect action: Onboarding token exchange failed', { 
                    status: response.status, 
                    error: tokenData.error?.message,
                    errorCode: tokenData.error?.code,
                    errorType: tokenData.error?.type
                });
                return json({error: tokenData.error?.message || 'Token exchange failed'}, {status: 500});
            }

            const connectionData: WhatsAppConnectionData = {
                access: tokenData,
                user: {
                    userId: user.userId,
                    userName: user.userName,
                },
                businessName: user.businessName,
                sellerId: user.userDetails.sellerId,
                updatedAt: new Date().toISOString(),
                onboardingType: 'whatsapp_business_app_onboarding' // Mark as coexistence onboarding
            };

            console.log('WhatsAppConnect action: Saving onboarding connection data', {
                userId: connectionData.user.userId,
                businessName: connectionData.businessName,
                sellerId: connectionData.sellerId,
                tokenExpires: connectionData.access.expires_in,
                updatedAt: connectionData.updatedAt,
                onboardingType: connectionData.onboardingType
            });
            
            await docRef.set(connectionData);
            console.log('WhatsAppConnect action: Onboarding token exchange successful and data saved');

            return json({success: true});
        }

        case 'fetch-businesses': {
            console.log('WhatsAppConnect action: Fetching business accounts', { 
                sellerId: user.userDetails.sellerId
            });
            
            const doc = await docRef.get();
            if (!doc.exists) {
                console.log('WhatsAppConnect action: Connection data not found', {
                    docPath: `facebook-connects/${user.userDetails.sellerId}`
                });
                return json({error: 'Connection not found'}, {status: 404});
            }

            const connectionData = doc.data() as WhatsAppConnectionData;
            console.log('WhatsAppConnect action: Using access token', {
                token: `${connectionData.access.access_token.substring(0, 10)}...`,
                expiresIn: connectionData.access.expires_in,
                tokenType: connectionData.access.token_type
            });

            if(connectionData.access.access_token.length < 10) {
                console.log('WhatsAppConnect action: Access token is too short, returning error', {
                    token: connectionData.access.access_token
                });
                return json({error: 'Access token is too short'}, {status: 500});
            }
            
            const businessResponse = await getWhatsAppBusinessAccounts(connectionData.access.access_token);
            console.log('WhatsAppConnect action: Business accounts fetched', { 
                count: businessResponse.data?.length,
                businesses: businessResponse.data?.map(b => ({
                    id: b.id,
                    name: b.name
                }))
            });

            return json({
                success: true,
                businessAccounts: businessResponse.data
            });
        }

        case 'fetch-phone-numbers': {
            const businessId = formData.get('businessId')?.toString();
            if (!businessId) return json({error: 'Business ID required'}, {status: 400});
            console.log('WhatsAppConnect action: Fetching phone numbers', { 
                businessId,
                sellerId: user.userDetails.sellerId 
            });

            const doc = await docRef.get();
            if (!doc.exists) {
                console.log('WhatsAppConnect action: Connection data not found', {
                    docPath: `facebook-connects/${user.userDetails.sellerId}`
                });
                return json({error: 'Connection not found'}, {status: 404});
            }

            const connectionData = doc.data() as WhatsAppConnectionData;
            const phoneNumbers = await getWhatsAppPhoneNumbers(
                connectionData.access.access_token,
                businessId
            );
            console.log('WhatsAppConnect action: Phone numbers fetched', { 
                count: phoneNumbers.data?.length,
                phoneNumbers: phoneNumbers.data?.map(p => ({
                    id: p.id,
                    displayPhoneNumber: p.display_phone_number,
                    verifiedName: p.verified_name,
                    qualityRating: p.quality_rating
                })) 
            });

            return json({
                success: true,
                phoneNumbers: phoneNumbers.data,
            });
        }

        case 'connect-phone': {
            const phoneId = formData.get('phoneId')?.toString();
            const selectedPhoneNo = formData.get('phoneNumber')?.toString();
            const phoneNumber = selectedPhoneNo?.replace(/^\+91\s*|\s+/g, '');
            const wabaId = formData.get('wabaId')?.toString();
            const pin = formData.get('pin')?.toString();
            if (!phoneId || !phoneNumber) return json({error: 'Phone details required'}, {status: 400});
            console.log('WhatsAppConnect action: Connecting phone', { 
                phoneId, 
                phoneNumber, 
                wabaId,
                selectedPhoneNo,
                userMobile: user.userDetails.mobileNumber
            });

            const doc = await docRef.get();
            if (!doc.exists) {
                console.log('WhatsAppConnect action: Connection data not found', {
                    docPath: `facebook-connects/${user.userDetails.sellerId}`
                });
                return json({error: 'Connection not found'}, {status: 404});
            }

            const connectionData = doc.data() as WhatsAppConnectionData;
            console.log('WhatsAppConnect action: Using access token for phone connection', {
                token: `${connectionData.access.access_token.substring(0, 10)}...`,
                expiryInfo: {
                    expiresIn: connectionData.access.expires_in,
                    expiryTime: Date.now() + (connectionData.access.expires_in * 1000),
                    expiryDate: new Date(Date.now() + (connectionData.access.expires_in * 1000)).toISOString()
                }
            });

            // Determine if registration is needed by inspecting current phone status
            let shouldRegister = true;
            try {
                const numbersResp = await getWhatsAppPhoneNumbers(
                    connectionData.access.access_token,
                    wabaId || ''
                );
                const phoneInfo = numbersResp.data?.find(p => p.id === phoneId);
                console.log('WhatsAppConnect action: Resolved phone info before registration', {
                    exists: !!phoneInfo,
                    status: phoneInfo?.status,
                    platform_type: phoneInfo?.platform_type,
                    code_verification_status: phoneInfo?.code_verification_status
                });

                // Skip register if already ACTIVE or if this is a coexistence onboarding
                if (
                    connectionData.onboardingType === 'whatsapp_business_app_onboarding' ||
                    phoneInfo?.status === 'ACTIVE'
                ) {
                    shouldRegister = false;
                }
            } catch (e) {
                console.log('WhatsAppConnect action: Failed to fetch phone info, proceeding cautiously', { error: (e as Error).message });
            }

            if (shouldRegister) {
                const requestUrl = `https://graph.facebook.com/v21.0/${phoneId}/register`;
                const payload: Record<string, unknown> = { messaging_product: 'whatsapp' };
                // if (pin && pin.length === 6) {
                    payload.pin = pin || "000000";
                // }
                console.log('WhatsAppConnect action: Making registration request', {
                    url: requestUrl,
                    method: 'POST',
                    payload
                });

                const response = await fetch(
                    requestUrl,
                    {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${connectionData.access.access_token}`,
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(payload),
                    }
                );

                const data = await response.json();
                console.log('WhatsAppConnect action: Registration response', JSON.stringify({
                    status: response.status,
                    ok: response.ok, 
                    data
                }, null, 2));

                if (!response.ok || !data.success) {
                    console.log('WhatsAppConnect action: Phone registration failed', { 
                        status: response.status, 
                        response: data,
                        error: data.error
                    });
                    return json({error: 'Phone registration failed'}, {status: 500});
                }
            } else {
                console.log('WhatsAppConnect action: Skipping registration step (coexistence or already ACTIVE)');
            }

            console.log("wawawawawa 2 wabaid: ", wabaId, "mNetConsolePhoneNumber ", user.userDetails.mobileNumber)

            // Only update Firebase if WhatsApp registration was successful
            const updateData = {
                mNetConnectedPhoneNumberId: phoneId,
                mNetConnectedPhoneNumber: phoneNumber,
                mNetConsolePhoneNumber: user.userDetails.mobileNumber,
                wabaId: wabaId,
                updatedAt: new Date().toISOString()
            };
            
            console.log('WhatsAppConnect action: Updating Firebase document', {
                docPath: `facebook-connects/${user.userDetails.sellerId}`,
                updateData
            });
            
            await docRef.update(updateData);
            console.log('WhatsAppConnect action: Firebase updated with phone connection details');

            const tokenData = {
                token: connectionData.access.access_token,
                wabMobile: phoneNumber,
                wabPhoneNumberId: phoneId,
                expiryTime: Date.now() + (connectionData.access.expires_in * 1000)
            };
            
            console.log('WhatsAppConnect action: Saving WAB token', {
                wabMobile: tokenData.wabMobile,
                wabPhoneNumberId: tokenData.wabPhoneNumberId,
                expiryTimeMs: tokenData.expiryTime,
                expiryDate: new Date(tokenData.expiryTime).toISOString()
            });
            
            await saveWABToken(tokenData, request);
            console.log('WhatsAppConnect action: WAB token saved');

            return json({success: true, actionType: 'connect-phone'});
        }
        //

        case 'check-subscription': {
            const businessId = formData.get('businessId')?.toString();
            if (!businessId) return json({error: 'Business ID required'}, {status: 400});
            console.log('WhatsAppConnect action: Checking subscription status', { 
                businessId,
                sellerId: user.userDetails.sellerId 
            });

            const doc = await docRef.get();
            if (!doc.exists) {
                console.log('WhatsAppConnect action: Connection data not found', {
                    docPath: `facebook-connects/${user.userDetails.sellerId}`
                });
                return json({error: 'Connection not found'}, {status: 404});
            }

            const connectionData = doc.data() as WhatsAppConnectionData;
            const response = await getSubscribedApps(
                connectionData.access.access_token,
                businessId
            );
            console.log('WhatsAppConnect action: Subscription check complete', { 
                appCount: response.data?.length,
                apps: response.data?.map(app => ({
                    id: app.id,
                    name: app.name,
                }))
            });

            return json({
                success: true,
                subscribedApps: response.data
            });
        }

        case 'subscribe': {
            const businessId = formData.get('businessId')?.toString();
            if (!businessId) return json({error: 'Business ID required'}, {status: 400});
            console.log('WhatsAppConnect action: Subscribing app', { 
                businessId,
                sellerId: user.userDetails.sellerId
            });

            const doc = await docRef.get();
            if (!doc.exists) {
                console.log('WhatsAppConnect action: Connection data not found', {
                    docPath: `facebook-connects/${user.userDetails.sellerId}`
                });
                return json({error: 'Connection not found'}, {status: 404});
            }

            const connectionData = doc.data() as WhatsAppConnectionData;
            const response = await subscribeApp(
                connectionData.access.access_token,
                businessId
            );
            console.log('WhatsAppConnect action: App subscription complete', {
                response,
                businessId
            });

            return json({
                success: true,
                actionType: 'subscribe'
            });
        }

        case 'unsubscribe': {
            const businessId = formData.get('businessId')?.toString();
            if (!businessId) return json({error: 'Business ID required'}, {status: 400});
            console.log('WhatsAppConnect action: Unsubscribing app', { 
                businessId,
                sellerId: user.userDetails.sellerId 
            });

            const doc = await docRef.get();
            if (!doc.exists) {
                console.log('WhatsAppConnect action: Connection data not found', {
                    docPath: `facebook-connects/${user.userDetails.sellerId}`
                });
                return json({error: 'Connection not found'}, {status: 404});
            }

            const connectionData = doc.data() as WhatsAppConnectionData;
            const response = await unsubscribeApp(
                connectionData.access.access_token,
                businessId
            );
            console.log('WhatsAppConnect action: App unsubscription complete', {
                response,
                businessId
            });

            return json({
                success: true,
                actionType: 'unsubscribe'
            });
        }


        //


        case 'delink': {
            console.log('WhatsAppConnect action: Delinking connection', { 
                sellerId: user.userDetails.sellerId,
                docPath: `facebook-connects/${user.userDetails.sellerId}`
            });
            await docRef.delete();
            console.log('WhatsAppConnect action: Connection successfully delinked');
            return json({success: true});
        }

        default:
            console.log('WhatsAppConnect action: Invalid action type', { 
                actionType,
                validActions: [
                    'exchange-token', 
                    'fetch-businesses', 
                    'fetch-phone-numbers', 
                    'connect-phone',
                    'check-subscription',
                    'subscribe',
                    'unsubscribe',
                    'delink'
                ]
            });
            return json({error: 'Invalid action'}, {status: 400});
    }
});


export default function WhatsAppConnect() {
    const {FACEBOOK_APP_ID, connectionState} = useLoaderData<LoaderData>();
    // const isTemplatesRoute = location.pathname.endsWith('/templates');


    if (!FACEBOOK_APP_ID) {
        return <div>Error: Facebook App ID not configured</div>;
    }

    return (
        <div className="space-y-6">
            <WhatsAppConnectView
                FACEBOOK_APP_ID={FACEBOOK_APP_ID}
                connectionState={connectionState}
            />

        </div>
    );
}
