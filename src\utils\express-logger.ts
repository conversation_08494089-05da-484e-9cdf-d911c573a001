// TODO: create logger util for while app in better and more efficient way
// TODO: use pino or morgan to have high performance logging etc
// TODO: use winston transport for better logging


import { Request, Response, NextFunction } from 'express';
import { nanoid } from 'nanoid';
import winston, { transports as winstonTransports } from 'winston'; // For logging
import * as context from '@utils/context.utils.js';


// Define log format
const logFormat = winston.format.printf(({ timestamp, requestId,level, message, ...meta }) => {
    return `${timestamp} - [${context.getCorrelationId()}] - [${level}] - ${message} - ${JSON.stringify(meta)}`;
  });

const transports: winstonTransports.StreamTransportInstance[] = [ new winston.transports.Console()]

if(process.env.LOG_TO_FILE === 'true') {
transports.push( new winston.transports.File({ filename: 'logs/catalogue.log' }))
}

// Create a logger instance using <PERSON>
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss',
    }),
    logFormat
  ),
  transports,
});


export default logger
