
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"
import { Switch } from "~/components/ui/switch"
import { Trash2, Plus, Truck } from "lucide-react"
import { json, ActionFunction } from "@remix-run/node"
import { useFetcher, useLoaderData, useRevalidator } from "@remix-run/react"
import { withAuth, withResponse } from "~/utils/auth-utils"
import { LoaderFunction } from "@remix-run/node"
import { useToast } from "~/components/ui/ToastProvider";
import { getDeliveryConfigs, createDcConfig, deleteDeliveryConfig } from "~/services/deliveryConfigService"
import { dclistingResponse, DcBody, ConfigType } from "~/types/api/businessConsoleService/DeliveryConfig"

interface DcConfig {
  id: string;
  buyerPercentage: number;
  sellerPercentage: number;
  minOrderValue: number;
  maxOrderValue: number;
  maxBuyerDeliveryCharge: number;
  maxSellerDeliveryCharge: number;
  percentageOption: 'buyer-pays' | 'seller-pays' | 'custom';
}

interface Loaderdata {
  data: dclistingResponse[];
}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
  const sellerId = user?.userDetails?.sellerId;
  try {
    if (!sellerId) {
      throw new Response("Seller ID is required", { status: 400 });
    }
    const response = await getDeliveryConfigs(sellerId, request);
    return withResponse({ data: response.data?.data }, response.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    throw new Response("Failed to fetch coupons data", {
      status: 500,
    });
  }
});

export const action = withAuth(async ({ request, user }) => {
  const formData = await request.formData();
  const actionType = formData.get("actionType");
  const sellerId = user?.userDetails?.sellerId;

  if (!sellerId) {
    return json(
      { data: null, actionType: actionType, success: false, error: "Seller ID is required" },
      { status: 400 }
    );
  }

  if (actionType === "deleteDeliveryConfig") {
    const configId = Number(formData.get("configId"));
    try {
      const response = await deleteDeliveryConfig(configId, request);
      return withResponse(
        { data: response.data, actionType: actionType, success: true },
        response.headers
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  if (actionType === "updateStatus") {
    const config = JSON.parse(formData.get("config") as string);

    try {
      // Get current config data first, then update only the active status
      const payload: Partial<DcBody> = {
        ...config,
        active: !config.active
      };

      const response = await createDcConfig(payload, request, config.id);
      return withResponse(
        { data: response.data, actionType: actionType, success: true },
        response.headers
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  if (actionType === "addDeliveryConfig") {
    const dcConfigsData = formData.get("dcConfigs");

    if (!dcConfigsData) {
      return json(
        { data: null, actionType: actionType, success: false, error: "No delivery range data provided" },
        { status: 400 }
      );
    }

    try {
      const dcConfigs: DcConfig[] = JSON.parse(dcConfigsData as string);
      console.log("dataa:::", dcConfigs);

      // Create all configurations
      for (const dcConfig of dcConfigs) {
        const payload: Partial<DcBody> = {
          sellerId: sellerId,
          configType: ConfigType.ORDER_VALUE_BASED,
          minOrderValue: dcConfig.minOrderValue,
          maxOrderValue: dcConfig.maxOrderValue,
          buyerPercentage: dcConfig.buyerPercentage,
          sellerPercentage: dcConfig.sellerPercentage,
          active: true
        };

        // Add max charges if specified
        if (dcConfig.maxBuyerDeliveryCharge > 0) {
          payload.maxBuyerDeliveryCharge = dcConfig.maxBuyerDeliveryCharge;
        }
        if (dcConfig.maxSellerDeliveryCharge > 0) {
          payload.maxSellerDeliveryCharge = dcConfig.maxSellerDeliveryCharge;
        }

        await createDcConfig(payload, request);
      }

      return json(
        { data: null, actionType: actionType, success: true },
        { status: 200 }
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  console.log("Invalid action type:", actionType);
  return json(
    { data: null, actionType: actionType, success: false },
    { status: 400 }
  );
});

export function DeliveryConfigDetails({
  config,
  onDelete,
  onStatusToggle,
}: {
  config: dclistingResponse;
  onDelete: (configId: number) => void;
  onStatusToggle: (config: dclistingResponse) => void;
}) {
  return (
    <div className="p-4 rounded-xl shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)] hover:shadow-[0px_4px_12px_0px_rgba(0,0,0,0.15)] transition-shadow duration-200 bg-white border border-neutral-100">
      <div className="mb-3">
        <div className="flex flex-row justify-between items-center">
          <div className="p-1 border border-neutral-600 rounded-md bg-[#FCFCFD]">
            <h3 className="text-base font-semibold text-typography-400">
              Config #{config.id}
            </h3>
          </div>

          <span
            className={`px-2 py-1 rounded-md text-xs font-medium ${config.active
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
              }`}
          >
            {config.active ? "Active" : "Inactive"}
          </span>
        </div>
        <p className="mt-2 text-sm text-typography-500">
          {config.configType === ConfigType.ORDER_VALUE_BASED
            ? "Order value based delivery configuration"
            : config.configType === ConfigType.PERCENTAGE_BASED
              ? "Percentage based delivery configuration" : "Unknown configuration type"}
        </p>
      </div>

      <div className="border-b border-neutral-200" />

      <div className="my-3">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-typography-400 mb-1">Order Range</p>
            <p className="text-sm font-medium text-typography-700">
              ₹{config.minOrderValue} - ₹{config.maxOrderValue}
            </p>
          </div>
          {
            config.maxSellerDeliveryCharge && config.maxSellerDeliveryCharge > 0 ? (
              <div className="text-right">
                <p className="text-xs text-typography-400 mb-1">Max Seller Charge</p>
                <p className="text-sm font-medium text-typography-700">
                  ₹{config.maxSellerDeliveryCharge}
                </p>
              </div>
            ) : config.maxBuyerDeliveryCharge && config.maxBuyerDeliveryCharge > 0 ? (
              <div className="text-right">
                <p className="text-xs text-typography-400 mb-1">Max Buyer Charge</p>
                <p className="text-sm font-medium text-typography-700">
                  ₹{config.maxBuyerDeliveryCharge}
                </p>
              </div>
            ) : null
          }
        </div>
        <div className="mt-4 grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-typography-400 mb-1">Buyer Share</p>
            <p className="text-sm font-medium text-typography-700">
              {config.buyerPercentage}%
            </p>
          </div>
          <div className="text-right">
            <p className="text-xs text-typography-400 mb-1">Seller Share</p>
            <p className="text-sm font-medium text-typography-700">
              {config.sellerPercentage}%
            </p>
          </div>
        </div>
      </div>

      <div className="border-b border-neutral-200" />

      <div className="relative mt-3 flex flex-row gap-3 justify-between items-center">
        <div className="flex items-center gap-2">
          <Switch
            checked={config.active}
            onCheckedChange={() => onStatusToggle(config)}
          />
          <span className="text-sm text-typography-600">
            {config.active ? "Active" : "Inactive"}
          </span>
        </div>

        <button
          className="border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-red-50 hover:border-red-200 transition-colors duration-200"
          onClick={() => onDelete(config.id)}
          aria-label="Delete delivery configuration"
        >
          <Trash2 className="w-4 h-4 text-red-500" />
        </button>
      </div>
    </div>
  );
}

// Component for individual delivery configuration
function DeliveryConfigCard({
  config,
  index,
  isLast,
  onConfirmChanges,
  onDeleteCard,
  onUpdatePercentage,
  onUpdateMaxCharge,
  allConfigs
}: {
  config: DcConfig;
  index: number;
  isLast: boolean;
  onConfirmChanges: (newMaxValue: number) => void;
  onDeleteCard: () => void;
  onUpdatePercentage: (option: 'buyer-pays' | 'seller-pays' | 'custom', customBuyer?: number) => void;
  onUpdateMaxCharge: (field: 'buyer' | 'seller', value: number) => void;
  allConfigs: DcConfig[];
}) {
  const [tempMaxValue, setTempMaxValue] = useState(config.maxOrderValue);

  // Only the last card is editable
  const isEditable = isLast;

  // Calculate validation
  const minAllowedMax = config.minOrderValue + 1;
  const maxAllowedMax = 10000;
  const isValidMaxValue = tempMaxValue >= minAllowedMax && tempMaxValue <= maxAllowedMax;
  const hasChanges = tempMaxValue !== config.maxOrderValue;

  // Show OK button only if max value is less than 10000 and card is editable
  const showOkButton = isEditable && tempMaxValue < 10000;

  // Show delete button only if not the first card and is editable
  const showDeleteButton = isEditable && index > 0;

  // Reset temp value when config changes (from external updates)
  useEffect(() => {
    setTempMaxValue(config.maxOrderValue);
  }, [config.maxOrderValue]);

  const handleConfirmChanges = () => {
    if (isValidMaxValue && hasChanges) {
      onConfirmChanges(tempMaxValue);
    }
  };
  return (
    <Card className="border border-gray-200 hover:border-blue-300 transition-colors shadow-md">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-gray-900 flex items-center justify-between">
          <span>Order Value Range {index + 1}</span>
          <span className="text-blue-600 font-semibold">₹{config.minOrderValue} - ₹{config.maxOrderValue}</span>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label className="text-sm text-gray-900">Minimum</Label>
            <Input
              type="number"
              value={config.minOrderValue}
              disabled
              className="h-8 text-sm bg-gray-50 border-gray-200"
            />
          </div>
          <div>
            <Label className="text-sm text-gray-900">Maximum</Label>
            <div className="flex gap-2">
              <Input
                type="number"
                min="0"
                max="10000"
                value={tempMaxValue}
                onChange={(e) => setTempMaxValue(Number(e.target.value) || 0)}
                disabled={!isEditable}
                className={`h-8 text-sm flex-1 ${!isEditable ? 'bg-gray-50 border-gray-200' : ''}`}
                placeholder="10000"
              />
              {showOkButton && (
                <Button
                  size="sm"
                  onClick={handleConfirmChanges}
                  disabled={!isValidMaxValue || !hasChanges}
                  className="h-8 px-3 text-xs bg-green-600 hover:bg-green-700 disabled:bg-gray-300"
                >
                  OK
                </Button>
              )}
              {showDeleteButton && (
                <Button
                  size="sm"
                  onClick={onDeleteCard}
                  variant="outline"
                  className="h-8 px-3 text-xs border-red-200 text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              )}
            </div>
            {/* Validation message for editable cards */}
            {isEditable && !isValidMaxValue && (
              <p className="text-xs text-red-500 mt-1">
                Enter value between ₹{minAllowedMax} and ₹{maxAllowedMax}
              </p>
            )}
            {isEditable && isValidMaxValue && hasChanges && tempMaxValue < 10000 && (
              <p className="text-xs text-blue-500 mt-1">
                Click OK to apply changes
              </p>
            )}
            {isEditable && tempMaxValue === 10000 && (
              <p className="text-xs text-green-600 mt-1">
                This is the max order value
              </p>
            )}
            {!isEditable && (
              <p className="text-xs text-gray-500 mt-1">
                ✅ Order Value confirmed
              </p>
            )}
          </div>
        </div>

        {isEditable && (
          <p className="text-xs text-gray-500">
            💡 Change 'Maximum' value and click OK to create next range
          </p>
        )}

        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-900">Who pays delivery charge?</Label>
          <div className="space-y-1">
            {/* Customer pays all */}
            <label className={`flex items-center p-2 rounded-lg border border-gray-200 transition-colors ${isEditable ? 'hover:bg-blue-50 cursor-pointer' : 'bg-gray-50 cursor-not-allowed'}`}>
              <div className="relative">
                <input
                  type="radio"
                  name={`percentage-${config.id}`}
                  checked={config.percentageOption === 'buyer-pays'}
                  onChange={() => isEditable && onUpdatePercentage('buyer-pays')}
                  disabled={!isEditable}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border-2 ${config.percentageOption === 'buyer-pays' ? 'border-blue-500 bg-blue-500' : 'border-gray-300'}`}>
                  {config.percentageOption === 'buyer-pays' && (
                    <div className="w-2 h-2 bg-white rounded-full absolute top-1 left-1"></div>
                  )}
                </div>
              </div>
              <div className="ml-3 flex-1">
                <span className={`text-sm font-medium ${!isEditable ? 'text-gray-500' : ''}`}>Customer pays all</span>
                <p className="text-xs text-gray-500">Customer: 100% • You: 0%</p>
              </div>
            </label>

            {/* Seller pays all */}
            <label className={`flex items-center p-2 rounded-lg border border-gray-200 transition-colors ${isEditable ? 'hover:bg-blue-50 cursor-pointer' : 'bg-gray-50 cursor-not-allowed'}`}>
              <div className="relative">
                <input
                  type="radio"
                  name={`percentage-${config.id}`}
                  checked={config.percentageOption === 'seller-pays'}
                  onChange={() => isEditable && onUpdatePercentage('seller-pays')}
                  disabled={!isEditable}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border-2 ${config.percentageOption === 'seller-pays' ? 'border-blue-500 bg-blue-500' : 'border-gray-300'}`}>
                  {config.percentageOption === 'seller-pays' && (
                    <div className="w-2 h-2 bg-white rounded-full absolute top-1 left-1"></div>
                  )}
                </div>
              </div>
              <div className="ml-3 flex-1">
                <span className={`text-sm font-medium ${!isEditable ? 'text-gray-500' : ''}`}>You pay all</span>
                <p className="text-xs text-gray-500">Customer: 0% • You: 100%</p>
              </div>
            </label>

            {/* Custom split */}
            <label className={`flex items-center p-2 rounded-lg border border-gray-200 transition-colors ${isEditable ? 'hover:bg-blue-50 cursor-pointer' : 'bg-gray-50 cursor-not-allowed'}`}>
              <div className="relative">
                <input
                  type="radio"
                  name={`percentage-${config.id}`}
                  checked={config.percentageOption === 'custom'}
                  onChange={() => isEditable && onUpdatePercentage('custom')}
                  disabled={!isEditable}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border-2 ${config.percentageOption === 'custom' ? 'border-blue-500 bg-blue-500' : 'border-gray-300'}`}>
                  {config.percentageOption === 'custom' && (
                    <div className="w-2 h-2 bg-white rounded-full absolute top-1 left-1"></div>
                  )}
                </div>
              </div>
              <div className="ml-3 flex-1">
                <span className="text-sm font-medium">Custom split</span>
                <p className="text-xs text-gray-500">Set your own percentage</p>
              </div>
            </label>
          </div>

          {config.percentageOption === 'custom' && (
            <div className="grid grid-cols-2 gap-2 mt-2 p-2 bg-blue-50 rounded-lg">
              <div>
                <Label className="text-xs text-gray-600">Customer %</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={config.buyerPercentage}
                  onChange={(e) => onUpdatePercentage('custom', Number(e.target.value))}
                  className="h-8 text-sm"
                />
              </div>
              <div>
                <Label className="text-xs text-gray-600">You %</Label>
                <Input
                  type="number"
                  value={config.sellerPercentage}
                  disabled
                  className="h-8 text-sm bg-gray-100"
                />
              </div>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">Maximum charges (optional)</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Input
                type="number"
                min="0"
                value={config.maxBuyerDeliveryCharge || ''}
                onChange={(e) => isEditable && onUpdateMaxCharge('buyer', Number(e.target.value) || 0)}
                disabled={!isEditable || config.maxSellerDeliveryCharge > 0}
                className={`h-8 text-sm ${!isEditable ? 'bg-gray-50 border-gray-200' : ''}`}
                placeholder="Max customer charge"
              />
            </div>
            <div>
              <Input
                type="number"
                min="0"
                value={config.maxSellerDeliveryCharge || ''}
                onChange={(e) => isEditable && onUpdateMaxCharge('seller', Number(e.target.value) || 0)}
                disabled={!isEditable || config.maxBuyerDeliveryCharge > 0}
                className={`h-8 text-sm ${!isEditable ? 'bg-gray-50 border-gray-200' : ''}`}
                placeholder="Max your charge"
              />
            </div>
          </div>
          {isEditable && (
            <p className="text-xs text-gray-500">💡 Set limit for either customer OR you, not both</p>
          )}
        </div>
        <div className="bg-green-50 border border-green-100 p-2 rounded-lg">
          <p className="text-sm font-medium text-green-800">📋 Summary:</p>
          <p className="mt-1 text-xs text-green-600">For order value between ₹{config.minOrderValue} and ₹{config.maxOrderValue}, Customer will pay {`${config.buyerPercentage}% amount`} and You will pay {`${config.sellerPercentage}% amount`} of deliver charge. {config.maxBuyerDeliveryCharge > 0 ? `Customer will be charged upto ₹${config.maxBuyerDeliveryCharge} and You will pay the remaining.` : ''}. {config.maxSellerDeliveryCharge > 0 ? `You will be charged upto ₹${config.maxSellerDeliveryCharge} and Customer will pay the remaining.` : ''}</p>
        </div>
      </CardContent>
    </Card>
  );
}

export default function DeliveryConfig() {
  const { data: deliveryConfigs } = useLoaderData<Loaderdata>();
  const { revalidate } = useRevalidator();
  const [showForm, setShowForm] = useState(false);
  const [dcConfigs, setDcConfigs] = useState<DcConfig[]>([]);

  const fetcher = useFetcher<{
    data: void;
    success: boolean;
    actionType: string;
  }>();

  const { showToast } = useToast();

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "deleteDeliveryConfig"
      ) {
        showToast("Delivery Configuration deleted successfully", "success");
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "deleteDeliveryConfig"
      ) {
        showToast("Failed to delete Delivery Configuration", "error");
      } else if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "addDeliveryConfig"
      ) {
        showToast("Delivery Configuration created successfully", "success");
        setShowForm(false);
        setDcConfigs([]);
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "addDeliveryConfig"
      ) {
        showToast("Failed to create Delivery Configuration", "error");
      } else if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "updateStatus"
      ) {
        showToast("Delivery Configuration status updated successfully", "success");
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "updateStatus"
      ) {
        showToast("Failed to update Delivery Configuration status", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);

  useEffect(() => {
    if (showForm && dcConfigs.length === 0) {
      initializeDcConfigs();
    }
  }, [showForm]);

  const handleDelete = (configId: number) => {
    if (confirm("Are you sure you want to delete this delivery configuration?")) {
      const formData = new FormData();
      formData.append("actionType", "deleteDeliveryConfig");
      formData.append("configId", configId.toString());
      fetcher.submit(formData, { method: "POST" });
    }
  };

  const handleStatusToggle = (config: dclistingResponse) => {
    const formData = new FormData();
    formData.append("actionType", "updateStatus");
    formData.append("config", JSON.stringify(config));
    fetcher.submit(formData, { method: "POST" });
  };

  // Helper function
  const generateConfigId = () => `config_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  // Initialize with first config
  const initializeDcConfigs = () => {
    const firstConfig: DcConfig = {
      id: generateConfigId(),
      minOrderValue: 0,
      maxOrderValue: 10000,
      buyerPercentage: 100,
      sellerPercentage: 0,
      maxBuyerDeliveryCharge: 0,
      maxSellerDeliveryCharge: 0,
      percentageOption: 'buyer-pays'
    };
    setDcConfigs([firstConfig]);
  };

  // Confirm changes to the latest (editable) card
  const confirmLatestCardChanges = (configId: string, newMaxValue: number) => {
    // Find the config
    const configIndex = dcConfigs.findIndex(config => config.id === configId);
    if (configIndex === -1) return;

    const config = dcConfigs[configIndex];

    // Validate: max must be greater than min and <= 10000
    if (newMaxValue <= config.minOrderValue || newMaxValue > 10000) {
      return; // Invalid value, don't update
    }

    // Update the current config with new max value
    const updatedConfigs = [...dcConfigs];
    updatedConfigs[configIndex] = {
      ...config,
      maxOrderValue: newMaxValue
    };

    // If the new max value is not 10000, create a new editable config
    if (newMaxValue < 10000) {
      const nextConfig: DcConfig = {
        id: generateConfigId(),
        minOrderValue: newMaxValue + 1,
        maxOrderValue: 10000,
        buyerPercentage: 100,
        sellerPercentage: 0,
        maxBuyerDeliveryCharge: 0,
        maxSellerDeliveryCharge: 0,
        percentageOption: 'buyer-pays'
      };
      updatedConfigs.push(nextConfig);
    }

    setDcConfigs(updatedConfigs);
  };

  // Delete the latest (editable) card
  const deleteLatestCard = (configId: string) => {
    const configIndex = dcConfigs.findIndex(config => config.id === configId);
    if (configIndex === -1 || configIndex === 0) return; // Can't delete first card

    // Remove the latest card
    const updatedConfigs = dcConfigs.slice(0, configIndex);

    // Update the previous card to have max = 10000 (make it the new latest)
    if (updatedConfigs.length > 0) {
      const lastIndex = updatedConfigs.length - 1;
      updatedConfigs[lastIndex] = {
        ...updatedConfigs[lastIndex],
        maxOrderValue: 10000
      };
    }

    setDcConfigs(updatedConfigs);
  };

  const updateConfigPercentage = (id: string, option: 'buyer-pays' | 'seller-pays' | 'custom', customBuyer?: number) => {
    setDcConfigs(dcConfigs.map(config => {
      if (config.id === id) {
        switch (option) {
          case 'buyer-pays':
            return { ...config, buyerPercentage: 100, sellerPercentage: 0, percentageOption: option };
          case 'seller-pays':
            return { ...config, buyerPercentage: 0, sellerPercentage: 100, percentageOption: option };
          case 'custom':
            let buyerPct = customBuyer || 0;
            if (buyerPct > 100) {
              buyerPct = 100;
            }
            return { ...config, buyerPercentage: buyerPct, sellerPercentage: 100 - buyerPct, percentageOption: option };
          default:
            return config;
        }
      }
      return config;
    }));
  };

  const updateConfigMaxCharge = (id: string, field: 'buyer' | 'seller', value: number) => {
    setDcConfigs(dcConfigs.map(config => {
      if (config.id === id) {
        if (field === 'buyer') {
          return { ...config, maxBuyerDeliveryCharge: value, maxSellerDeliveryCharge: value > 0 ? 0 : config.maxSellerDeliveryCharge };
        } else {
          return { ...config, maxSellerDeliveryCharge: value, maxBuyerDeliveryCharge: value > 0 ? 0 : config.maxBuyerDeliveryCharge };
        }
      }
      return config;
    }));
  };

  const validateDcConfigs = (): string | null => {
    if (dcConfigs.length === 0) return "Please create at least one delivery range";

    // Check if ranges cover 0 to 10000
    const sortedConfigs = [...dcConfigs].sort((a, b) => a.minOrderValue - b.minOrderValue);

    if (sortedConfigs[0].minOrderValue !== 0) {
      return "First range must start from ₹0";
    }

    if (sortedConfigs[sortedConfigs.length - 1].maxOrderValue !== 10000) {
      return "Last range must end at ₹10,000";
    }

    // Check for gaps or overlaps
    for (let i = 0; i < sortedConfigs.length - 1; i++) {
      if (sortedConfigs[i].maxOrderValue + 1 !== sortedConfigs[i + 1].minOrderValue) {
        return "There should be no gaps between delivery ranges";
      }
    }

    return null;
  };

  const handleAddConfig = () => {
    const validationError = validateDcConfigs();
    if (validationError) {
      showToast(validationError, "error");
      return;
    }
    console.log("data::", dcConfigs)

    const formData = new FormData();
    formData.append("actionType", "addDeliveryConfig");
    formData.append("dcConfigs", JSON.stringify(dcConfigs));
    fetcher.submit(formData, { method: "POST" });
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Delivery Charge Configurations</h1>
        <p className="text-gray-600 mt-2">Manage your delivery charge configurations</p>
      </div>

      <div
        aria-labelledby="delivery-configs-list"
        className="pb-20 md:pb-5"
      >
        {deliveryConfigs && deliveryConfigs.length > 0 ? (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {deliveryConfigs.map((config) => (
                <DeliveryConfigDetails
                  key={config.id}
                  config={config}
                  onDelete={handleDelete}
                  onStatusToggle={handleStatusToggle}
                />
              ))}
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-10 text-center">
            <div className="w-full max-w-4xl">
              <div className="text-center mb-8">
                <Truck className="h-16 w-16 text-gray-400 mx-auto" />
              </div>

              {!showForm ? (
                <div className="text-center">
                  <h3 className="text-2xl font-semibold text-foreground mb-2">
                    🚚 Set Up Your Delivery Configurations
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    Create smart delivery charge rules for different order values and decide who pays what portion of delivery costs.
                  </p>
                  <Button
                    onClick={() => setShowForm(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    🚀 Start Setup
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200">
                    <div className="flex flex-row">
                      <p className="font-medium text-blue-800">1. Set Order Ranges: &nbsp;</p>
                      <p className="text-blue-600">Define order value ranges from ₹0 to ₹10,000</p>
                    </div>
                    <div className="flex flex-row">
                      <p className="font-medium text-blue-800">2. Choose Payment Split: &nbsp;</p>
                      <p className="text-blue-600">Decide who pays delivery charges</p>
                    </div>
                    <div className="flex flex-row">
                      <p className="font-medium text-blue-800">3. Set Maximum Charge (Optional): &nbsp;</p>
                      <p className="text-blue-600">Cap maximum delivery charges</p>
                    </div>
                  </div>

                  {/* Progressive card system */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {dcConfigs.map((config, index) => (
                      <DeliveryConfigCard
                        key={config.id}
                        config={config}
                        index={index}
                        isLast={index === dcConfigs.length - 1}
                        onConfirmChanges={(newMaxValue) => confirmLatestCardChanges(config.id, newMaxValue)}
                        onDeleteCard={() => deleteLatestCard(config.id)}
                        onUpdatePercentage={(option, customBuyer) => updateConfigPercentage(config.id, option, customBuyer)}
                        onUpdateMaxCharge={(field, value) => updateConfigMaxCharge(config.id, field, value)}
                        allConfigs={dcConfigs}
                      />
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-3 pt-4">
                    <Button
                      onClick={handleAddConfig}
                      disabled={fetcher.state === "submitting" || fetcher.state === "loading"}
                      className="bg-blue-600 hover:bg-blue-700 text-white flex-1 h-10"
                    >
                      {(fetcher.state === "submitting" || fetcher.state === "loading") ? "Saving..." : "Save All Delivery Configuration"}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowForm(false);
                        setDcConfigs([]);
                      }}
                      className="flex-1 h-10"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
