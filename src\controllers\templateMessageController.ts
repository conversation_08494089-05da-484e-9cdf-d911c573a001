import { Request, Response } from 'express';
import { SendTemplateRequest, SendTemplateResponse } from '@/types/whatsapp.js';
import { templateService } from '@/services/templateService.js';
import { sendWhatsAppTemplateMessage } from '@/services/whatsappService.js';
import { ZodError } from 'zod';
import { MessageCategory } from '@/database/entities/NotificationLog.js';

export class TemplateMessageController {
    public async sendTemplateMessage(req: Request, res: Response) {
        try {
            const { templateId, targetPhoneNumber, wabPhoneNumberId, variables, phoneNumberId, accessToken, campaignId, campaignName, campaignType, messageCategory, customerSegment, tags } = req.body;

            // Basic request validation
            if (!templateId || !targetPhoneNumber) {
                return res.status(400).json({
                    success: false,
                    message: 'templateId and targetPhoneNumber are required'
                });
            }

            // Validate variables structure
            if (!variables || typeof variables !== 'object') {
                return res.status(400).json({
                    success: false,
                    message: 'variables object is required'
                });
            }

            const request: SendTemplateRequest = {
                templateId,
                targetPhoneNumber,
                wabPhoneNumberId,
                variables: {
                    header: variables.header || {},
                    body: variables.body || {},
                    footer: variables.footer || {},
                    button: variables.button || {}
                },
                accessToken
            };

            // Create WhatsApp template message
            const template = templateService.createWhatsAppTemplate(request);

            // Send the template message
            const result = await sendWhatsAppTemplateMessage({
                targetPhoneNumber: request.targetPhoneNumber,
                wabPhoneNumberId: request.wabPhoneNumberId,
                accessToken: request.accessToken,
                template,
                campaignId,
                campaignName,
                campaignType,
                messageCategory,
                customerSegment,
                tags
            });

            return res.json(result);
        } catch (error: any) {
            console.error('Error sending template message:', error);

            // Handle Zod validation errors
            if (error instanceof ZodError) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: error.errors.map(err => ({
                        path: err.path.join('.'),
                        message: err.message
                    }))
                });
            }

            const response: SendTemplateResponse = {
                success: false,
                message: 'Failed to send template message',
                error: error.message || 'Unknown error occurred'
            };
            return res.status(500).json(response);
        }
    }

    public async getAvailableTemplates(req: Request, res: Response) {
        try {
            const templates = templateService.getAvailableTemplates();
            return res.json({
                success: true,
                message: 'Templates fetched successfully',
                data: templates
            });
        } catch (error: any) {
            console.error('Error fetching templates:', error);
            return res.status(500).json({
                success: false,
                message: 'Failed to fetch templates',
                error: error.message || 'Unknown error occurred'
            });
        }
    }
} 
