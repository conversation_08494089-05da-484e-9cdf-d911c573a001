# Ticket Management System

## Overview

A comprehensive ticket management system for admins to track and manage support tickets from buyers and sellers. The system allows viewing tickets in a sortable list, updating ticket statuses, and adding notes to tickets.

## Features

- **Ticket List**: View all tickets sorted by last update date
- **Search**: Filter tickets by various fields including user name, phone, ticket type, etc.
- **Status Updates**: Update ticket status (Open, Work In Progress, Closed)
- **Notes**: Add and view notes for each ticket
- **Time Tracking**: See how long tickets have been unresolved

## Tech Stack

- **Frontend**: React, Remix.js, TypeScript
- **UI Components**: Shadcn UI
- **Backend Integration**: RESTful APIs

## Installation

1. The ticket system is part of the main application. No separate installation is needed.
2. Ensure your development environment is set up according to the main project guidelines.

## Usage

### Accessing the Ticket Management System

Navigate to `/home/<USER>

### Viewing Tickets

The main page displays a table of all tickets with the following information:
- Ticket ID
- User Type (Buyer/Seller)
- User Name
- Phone Number
- Seller Name
- Ticket Type
- Description
- Last Update Date
- Status
- Time Unresolved
- Actions

### Searching and Filtering

Use the search box to filter tickets by:
- User name
- Phone number
- Seller name
- Description
- User type
- Ticket type

### Updating Ticket Status

1. Click the "Update" button on the row of the ticket you want to update
2. In the dialog that appears, select the new status
3. Click "Update" to save the changes

Available statuses:
- OPEN
- WIP (Work In Progress)
- CLOSED

### Managing Ticket Notes

1. Click the "Notes" button on the row of the ticket you want to view or add notes to
2. The dialog shows existing notes
3. Enter a new note in the text field at the bottom
4. Click "Add Note" to save the note

## API Documentation

The ticket system uses the following API endpoints:

### Get All Tickets
```
GET /support/tickets
```
Response: List of `SupportTicketDto` objects

### Update Ticket
```
POST /support/{ticketId}
```
Request Body: `SupportTicketDto`

### Get Ticket Notes
```
GET /support/{ticketId}/notes
```
Response: List of `TicketNoteDto` objects

### Add Note to Ticket
```
POST /support/{ticketId}/notes
```
Request Body: `TicketNoteDto`

## Data Models

### SupportTicketDto
```typescript
interface Ticket {
  ticketId: number;
  userId: number;
  userName: string;
  userMobileNo: string;
  ticketType: string;
  status: "OPEN" | "WIP" | "CLOSED"; // SupportTicketStatus
  orderGroupId: number;
  description: string;
  closingRemarks: string;
  createdDate: string;
  distinctId: string;
  requestedCallBack: boolean;
  userType: string;
  lastModifiedDate: string;
  sellerId: number;
  sellerName: string;
}
```

### TicketNoteDto
```typescript
interface TicketNote {
  ticketNoteId: number;
  ticketId: number;
  note: string;
}
```

## Troubleshooting

### Common Issues

1. **Path Resolution Errors**: If you encounter path resolution errors, check your project's configuration for resolving paths with the `~/` prefix.

2. **API Connection Issues**: Ensure the backend API is running and accessible.

3. **Component Rendering Issues**: Make sure all Shadcn UI components are properly installed and configured.

## Contributing

To extend or modify the ticket management system:

1. Follow the existing code structure and patterns
2. Ensure components are properly typed with TypeScript
3. Use Shadcn UI components for consistent styling
4. Write loaders and actions according to Remix.js best practices

## Future Enhancements

- Pagination for large datasets
- Advanced filtering options
- Bulk actions for tickets
- Ticket priority management
- SLA tracking and notifications
- Export functionality for reporting
