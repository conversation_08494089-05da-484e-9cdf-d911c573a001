import { ActionFunction, json, LoaderFunction } from "@remix-run/node";
import { Form, useActionData, useFetcher, useLoaderData, useNavigate, useSearchParams } from "@remix-run/react";
import * as React from "react";
import { CalendarIcon } from "lucide-react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import {
      Popover,
      PopoverContent,
      PopoverTrigger,
} from "~/components/ui/popover";
import { Calendar } from "~/components/ui/calendar";
import { format, formatDate } from "date-fns";
import {
      Select,
      SelectContent,
      SelectItem,
      SelectTrigger,
      SelectValue,
} from "~/components/ui/select";
import { SalesData, SalesDetails, SellerSalesInfo } from "~/types/api/businessConsoleService/salesinfo";
import { getPrivateSellerSales, getSalesData } from "~/services/salesinfoDetails";
import { withAuth, withResponse } from "@utils/auth-utils";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "~/components/ui/tabs";
import { useState } from "react";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "~/components/ui/pagination";
import { ResponsiveTable } from "~/components/ui/responsiveTable";
import { cn } from "~/lib/utils";
import { DateRange } from "react-day-picker";
export interface LoaderData {
      salesData: SalesDetails,
      sellerRole: string
}
function convertToCSV(data: { salesData: any[] } | undefined | null, headers: string[]): string {
      // Return just headers if data is missing or has no salesData
      if (!data?.salesData?.length) {
            return headers.join(",");
      }

      const headerRow = headers.join(",");
      const dataRows = data.salesData.map(row => {
            return [
                  row.id ?? "",
                  `"${row.name ?? ""}"`,
                  row.bookedWeight?.toFixed(1) ?? "-",
                  row.deliveredWeight?.toFixed(1) ?? "-",
                  row.returnedWeight?.toFixed(1) ?? "-",
                  row.cancelledWeight?.toFixed(1) ?? "-",
                  row.customerCount ?? "-",
                  row.orderCount ?? "-",
                  row.activeShopCount ?? "-"
            ].join(",");
      });

      return [headerRow, ...dataRows].join("\n");
}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
      const url = new URL(request.url);
      const sellerPermission = user.userPermissions?.includes('seller_app.scBasic')
      console.log(user.userPermissions, "4444444444444444")

      console.log(sellerPermission, "5555555555555555")
      const fromDate = url.searchParams.get("from") || new Date().toISOString().split("T")[0];
      const toDate = url.searchParams.get("to") || fromDate;
      // const date = url.searchParams.get("date") || new Date().toISOString().split("T")[0];
      const page = Number(url.searchParams.get("page")) || 0;
      const pageSize = Number(url.searchParams.get("pageSize")) || 10;

      if (sellerPermission) {
            try {
                  const activeTab = url.searchParams.get("activeTab") || "agentWise";
                  let response = null;
                  switch (activeTab) {
                        case 'agentWise':
                              response = await getPrivateSellerSales(page, pageSize, "Agentwise", fromDate, toDate, request)
                              break;
                        case 'localityWise':
                              response = await getPrivateSellerSales(page, pageSize, "Localitywise", fromDate, toDate, request)
                              break;
                        default:
                              console.log("No valid activeTab selected");
                              break;
                  }
                  return withResponse({
                        salesData: response?.data,
                        sellerRole: sellerPermission
                  }, response?.headers)
            } catch (error) {
                  console.error("Seller sales error:", error);
                  throw new Response("Failed to fetch seller sales", { status: 500 });
            }
      }
      else {

            try {
                  const activeTab = url.searchParams.get("activeTab") || "sellerWise";
                  let response = null;
                  switch (activeTab) {
                        case 'sellerWise':
                              response = await getSalesData(0, pageSize, "Sellerwise", fromDate, toDate, request)

                              break;
                        case 'agentWise':
                              response = await getSalesData(page, pageSize, "Agentwise", fromDate, toDate, request)
                              break;
                        case 'localityWise':
                              response = await getSalesData(page, pageSize, "Localitywise", fromDate, toDate, request)
                              break;
                        default:
                              console.log("No valid activeTab selected");
                              break;

                  }
                  return withResponse({
                        salesData: response?.data,
                        sellerRole: sellerPermission
                  }, response?.headers)
            } catch (error) {
                  console.error("Seller sales error:", error);
                  throw new Response("Failed to fetch seller sales", { status: 500 });
            }

      }
});

export const action: ActionFunction = withAuth(async ({ request, user }) => {
      const formData = await request.formData();
      const activeTab = formData.get("activeTab") as string;
      const fromDate = formData.get("from") as string;
      const toDate = formData.get("to") as string;
      const sellerPermission = user.userPermissions?.includes('seller_app.scBasic')

      if (sellerPermission) {
            try {
                  let response = null;
                  switch (activeTab) {
                        case 'agentWise':
                              response = await getPrivateSellerSales(0, 1000, "Agentwise", fromDate, toDate, request)
                              break;
                        case 'localityWise':
                              response = await getPrivateSellerSales(0, 1000, "Localitywise", fromDate, toDate, request)
                              break;
                        default:
                              console.log("No valid activeTab selected");
                              break;
                  }
                  return withResponse({
                        salesData: response?.data,
                        sellerRole: sellerPermission
                  }, response?.headers)
            } catch (error) {
                  console.error("CSV export error:", error);
                  throw new Response("Failed to generate CSV", { status: 500 });
            }
      }
      else {
            try {
                  let response;
                  switch (activeTab) {
                        case 'sellerWise':
                              response = await getSalesData(0, 1000, "Sellerwise", fromDate, toDate, request);
                              break;
                        case 'agentWise':
                              response = await getSalesData(0, 1000, "Agentwise", fromDate, toDate, request);
                              break;
                        case 'localityWise':
                              response = await getSalesData(0, 1000, "Localitywise", fromDate, toDate, request);
                              break;
                        default:
                              throw new Error("Invalid tab selected");
                  }

                  return json({
                        salesData: response?.data,
                        sellerRole: sellerPermission

                  });
            } catch (error) {
                  console.error("CSV export error:", error);
                  throw new Response("Failed to generate CSV", { status: 500 });
            }
      }
});
export default function SellerWiseSales() {

      const navigate = useNavigate();
      const { salesData, sellerRole } = useLoaderData<LoaderData>()
      const [searchParams] = useSearchParams();
      const startDate = searchParams.get("from")
      const endDate = searchParams.get("to")


      const [activeTab, setActiveTab] = React.useState(searchParams.get("activeTab") || (sellerRole ? "agentWise" : "sellerWise")); const [currentPage, setCurrentPage] = useState(0)
      const [searchTerm, setSearchTerm] = useState('')

      const [dateRange, setDateRange] = React.useState<DateRange>({
            from: startDate ? new Date(startDate) : new Date(),
            to: endDate ? new Date(endDate) : new Date(),
      });

      const itemsPerPage = 20;
      const handleTabChange = (newTab: string) => {
            setActiveTab(newTab);

            if (!dateRange.from) return; // Ensure 'from' date exists

            const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
            const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;

            navigate(`?activeTab=${newTab}&page=${0}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}`);
      };

      const handlePageChange = (newPage: number) => {
            if (!dateRange.from) return; // Ensure 'from' date exists

            const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
            const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;
            setCurrentPage(newPage)
            navigate(`?activeTab=${activeTab}&page=${newPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}`);
      };

      const handleViewSales = () => {
            if (!dateRange.from) return; // Ensure 'from' date exists

            const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
            const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom; // Default 'to' as 'from'

            navigate(`?activeTab=${activeTab}&page=${currentPage}&pageSize=${itemsPerPage}&from=${formattedFrom}&to=${formattedTo}`);
      };

      // const handleSearch = (Val: string) => {
      //       setSearchTerm(Val)
      //       navigate(`?activeTab=${activeTab}&page=${currentPage}&searchTerm=${searchTerm}&pageSize=${itemsPerPage}`)
      // }

      const totalDeliveredWt = salesData?.salesData && Array.isArray(salesData.salesData)
            ? salesData.salesData.map((x) => x?.deliveredWeight || 0).reduce((acc, wet) => acc + wet, 0)
            : 0;

      const totalReturnsWt = salesData?.salesData && Array.isArray(salesData.salesData)
            ? salesData.salesData.map((x) => x?.returnedWeight || 0).reduce((a, b) => a + b, 0)
            : 0;

      const totalBookingWt = salesData?.salesData && Array.isArray(salesData.salesData)
            ? salesData.salesData.map((x) => x?.bookedWeight || 0).reduce((a, b) => a + b, 0)
            : 0;

      const totalCancelWt = salesData?.salesData && Array.isArray(salesData.salesData)
            ? salesData.salesData.map((x) => x?.cancelledWeight || 0).reduce((a, b) => a + b, 0)
            : 0;

      const sellerWiseHeaders = [
            "Seller ID",
            "Seller Name",
            "Booked Qty",
            "Delivered Qty",
            "Return Qty",
            "Cancel Qty",
            "Total Shops",
            "Ordered Shops",
            "Active Shops"



      ];
      const sellerAgentWiseHeaders = [

            "Agent ID",
            "Agent Name",
            "Booked Qty",
            "Delivered Qty",
            "Return Qty",
            "Cancel Qty",
            "Ordered Shops",
            "BookedAmt",
            "DeliveredAmt",
           

      ]
      const sellerLocalityWise = [
            "Locality ID",
            "Locality Name",
            "Booked Qty",
            "Delivered Qty",
            "Return Qty",
            "Cancel Qty",
            "Active Shops",
             "BookedAmt",
            "DeliveredAmt"

      ];
      const AgentWiseHeaders = [
            "Agent ID",
            "Agent Name",
            "Booked Qty",
            "Delivered Qty",
            "Return Qty",
            "Cancel Qty",
            "Total Shops",
            "Ordered Shops",
            "Active Shops"

      ];
      const LocalityWise = [
            "Locality ID",
            "Locality Name",
            "Booked Qty",
            "Delivered Qty",
            "Return Qty",
            "Cancel Qty",
            "Total Shops",
            "Ordered Shops",
            "Active Shops"

      ];

      const footerTotals = [
            "",
            "",
            totalBookingWt.toFixed(2),
            totalDeliveredWt.toFixed(2),
            totalReturnsWt.toFixed(2),
            totalCancelWt.toFixed(2)

      ];
      const handleDateChange = (range: DateRange | undefined) => {
            if (!range?.from) return; // Ensure 'from' is never undefined

            setDateRange({
                  from: range.from,
                  to: range.to || undefined, // If 'to' is not selected, keep it undefined
            });
      };

      const actionData = useActionData<typeof action>();


      const fetcher = useFetcher<{ salesData?: any[] }>();
      const [isExporting, setIsExporting] = React.useState(false); // Track when export is triggered

      const handleCSVDownload = () => {
            if (!dateRange.from) return;

            setIsExporting(true);  // Mark as exporting

            const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
            const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;

            fetcher.submit(
                  {
                        activeTab,
                        from: formattedFrom,
                        to: formattedTo,
                  },
                  { method: "post" }
            );
      };

      React.useEffect(() => {
            if (isExporting && fetcher.data?.salesData) {
                  const headers = activeTab === "sellerWise" ? sellerWiseHeaders :
                        activeTab === "agentWise" ? AgentWiseHeaders :
                              LocalityWise;

                  const csvContent = convertToCSV(fetcher?.data?.salesData, headers);
                  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                  const link = document.createElement('a');
                  const url = URL.createObjectURL(blob);

                  link.setAttribute('href', url);
                  link.setAttribute('download', `${activeTab}_sales_${startDate}_to_${endDate}.csv`);
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);

                  setIsExporting(false);  // Reset export state after download
            }
      }, [fetcher.data, isExporting, activeTab, startDate, endDate]);

      const [selectedAgentId, setSelectedAgentId] = useState(0)
      return (
            <div className="container mx-auto w-full p-6">
                  <div className="items-center mb-6 flex justify-between">
                        <h1 className="text-2xl font-bold">Sales Report</h1>
                        <Button
                              variant="outline"
                              onClick={handleCSVDownload}
                              disabled={fetcher.state !== "idle"}
                        >
                              {fetcher.state === "submitting" ? "Exporting..." : "Export to CSV"}
                        </Button>
                  </div>
                  <div className="flex flex-col space-y-5 md:flex-row md:space-x-5 md:space-y-0 my-5">
                        <div className="grid grid-cols-1 gap-3 md:flex md:items-center md:space-x-2">
                              <div className="w-full md:w-auto">
                                    <Popover>
                                          <PopoverTrigger asChild>
                                                <Button
                                                      id="date"
                                                      variant={"outline"}
                                                      className={cn(
                                                            "w-[300px] justify-start text-left font-normal",
                                                            !dateRange.from && "text-muted-foreground"
                                                      )}
                                                >
                                                      <CalendarIcon />
                                                      {dateRange?.from ? (
                                                            dateRange.to ? (
                                                                  <>
                                                                        {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                                                                  </>
                                                            ) : (
                                                                  format(dateRange.from, "LLL dd, y")
                                                            )
                                                      ) : (
                                                            <span>Pick a date</span>
                                                      )}
                                                </Button>

                                          </PopoverTrigger>
                                          <PopoverContent className="w-auto p-0" align="start">
                                                <Calendar
                                                      initialFocus
                                                      selected={dateRange}
                                                      mode="range" // Enable range selection
                                                      onSelect={handleDateChange}
                                                />
                                          </PopoverContent>
                                    </Popover>
                              </div>

                              <Button
                                    type="submit"
                                    className="w-full md:w-auto md:rounded-full"
                                    onClick={() => handleViewSales()}

                              >
                                    View Report
                              </Button>
                        </div>

                  </div>

                  <Tabs value={activeTab} onValueChange={handleTabChange} className="my-5">
                        <TabsList>
                              {!sellerRole && <TabsTrigger value="sellerWise">Seller Wise</TabsTrigger>
                              }                              <TabsTrigger value="agentWise">Agent Wise</TabsTrigger>
                              <TabsTrigger value="localityWise">Locality Wise</TabsTrigger>
                        </TabsList>
                        <TabsContent value="sellerWise">
                              <ResponsiveTable
                                    headers={sellerWiseHeaders}
                                    data={salesData.salesData
                                    }
                                    renderRow={(row) => (
                                          <tr key={row.id} className="border-b">
                                                <td className="py-2 px-3 font-medium text-left">{row.id}</td>
                                                <td className="py-2 px-3 text-blue-600 underline cursor-pointer text-left">
                                                      <span
                                                            onClick={() => {
                                                                  if (!dateRange.from) return; // Ensure 'from' date exists

                                                                  const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
                                                                  const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;
                                                                  navigate(
                                                                        `/home/<USER>"agentWise"}&agentId=undefined&areaId=undefined`
                                                                  )
                                                            }}
                                                      >
                                                            {row.name}
                                                      </span>
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.bookedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.deliveredWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.returnedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.cancelledWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.customerCount || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.orderCount || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.activeShopCount || "-"}
                                                </td>
                                          </tr>
                                    )}
                                    footerTotals={footerTotals}
                                    emptyMessage="No data available for the selected filters."
                              />
                        </TabsContent>
                        <TabsContent value="agentWise">
                              <ResponsiveTable
                                headers={sellerRole?sellerAgentWiseHeaders:AgentWiseHeaders}

                                    data={
                                          salesData.salesData
                                    }
                                    renderRow={(row) => (
                                          <tr key={row.id} className="border-b">
                                                <td className="py-2 px-3 font-medium text-left">{row.id}</td>
                                                <td className="py-2 px-3 text-blue-600 underline cursor-pointer text-left">
                                                    { !sellerRole? <span
                                                            onClick={() => {
                                                                  if (!dateRange.from) return; // Ensure 'from' date exists

                                                                  const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
                                                                  const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;

                                                                  setSelectedAgentId(row.id)
                                                                  navigate(
                                                                        `/home/<USER>"agentWise" : "sellerWise"}&sellerId=undefined&areaId=undefined`
                                                                  )
                                                            }}
                                                      >
                                                            {row.name}
                                                      </span>:
                                                      
                                                      <span>
                                                            {row.name}
                                                      </span>
                                    }
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.bookedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.deliveredWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.returnedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.cancelledWeight.toFixed(1) || "-"}
                                                </td>
                                                {!sellerRole && (
 <td className="py-2 px-3 text-right">
                                                      {row?.customerCount || "-"}
                                                </td>)}
                                                <td className="py-2 px-3 text-right">
                                                      {row?.orderCount || "-"}
                                                </td>
                                                {!sellerRole && (
                                                <td className="py-2 px-3 text-right">
                                                      {row?.activeShopCount || "-"}
                                                </td>)}
                                                {sellerRole && (
                                                      <td className="py-2 px-3 text-right">
                                                            {row?.bookedAmount.toFixed(2) || "-"}
                                                      </td>
                                                )}
                                                {sellerRole && (
                                                      <td className="py-2 px-3 text-right">
                                                            {row?.deliveredAmount.toFixed(2) || "-"}
                                                      </td>
                                                )}
                                          </tr>
                                    )}
                                    footerTotals={footerTotals}
                                    emptyMessage="No data available for the selected filters."
                              />
                        </TabsContent>
                        <TabsContent value="localityWise">
                              <ResponsiveTable
                                    headers={ sellerRole?sellerLocalityWise:LocalityWise}
                                    data={
                                          salesData.salesData
                                    }
                                    renderRow={(row) => (
                                          <tr key={row.id} className="border-b">
                                                <td className="py-2 px-3 font-medium text-left">{row.id}</td>
                                                <td className="py-2 px-3 text-blue-600 underline cursor-pointer text-left">
                                                { !sellerRole? <span
                                                            onClick={() => {
                                                                  if (!dateRange.from) return; // Ensure 'from' date exists

                                                                  const formattedFrom = format(dateRange.from, "yyyy-MM-dd");
                                                                  const formattedTo = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : formattedFrom;
                                                                  navigate(
                                                                        `/home/<USER>"localityWise"}&sellerId=undefined&agentId=${sellerRole ? selectedAgentId : 'undefined'}`
                                                                  )
                                                            }}
                                                      >
                                                            {row.name}
                                                      </span>:
                                                      <span>
                                                            {row.name}
                                                      </span>
                                    }

                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.bookedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.deliveredWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.returnedWeight.toFixed(1) || "-"}
                                                </td>
                                                <td className="py-2 px-3 text-right">
                                                      {row?.cancelledWeight.toFixed(1) || "-"}
                                                </td>
                                         {!sellerRole && (
<td className="py-2 px-3 text-right">
                                                      {row?.customerCount || "-"}
                                                </td>)}
                                                <td className="py-2 px-3 text-right">
                                                      {row?.orderCount || "-"}
                                                </td>
                                                {!sellerRole && (
  <td className="py-2 px-3 text-right">
                                                      {row?.activeShopCount || "-"}
                                                </td>)}
                                                {sellerRole && (
                                                      <td className="py-2 px-3 text-right">
                                                            {row?.bookedAmount.toFixed(2) || "-"}
                                                      </td>
                                                )}
                                                {sellerRole && (
                                                      <td className="py-2 px-3 text-right">
                                                            {row?.deliveredAmount.toFixed(2) || "-"}
                                                      </td>
                                                )}
                                                 
                                          </tr>
                                    )}
                                    footerTotals={footerTotals}
                                    emptyMessage="No data available for the selected filters."
                              />
                        </TabsContent>
                  </Tabs>

                  <div className="flex justify-between items-center mt-6 overflow-hidden ">
                        <Pagination>
                              <PaginationContent>
                                    {currentPage > 0 && (
                                          <PaginationItem>
                                                <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} />
                                          </PaginationItem>
                                    )}
                                    <PaginationItem>
                                          <PaginationLink>{currentPage + 1}</PaginationLink>
                                    </PaginationItem>
                                    <PaginationItem>
                                          <PaginationNext onClick={() => handlePageChange(currentPage + 1)} />
                                    </PaginationItem>
                              </PaginationContent>
                        </Pagination>
                  </div>
            </div>

      );
}
