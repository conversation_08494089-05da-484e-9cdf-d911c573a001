/**
 * IST Timestamps Integration Tests
 * Tests timezone support with real DynamoDB operations
 */

import { WebhookLogService } from '../services/webhookLog.service.js';
import { testWebhooks } from './webhook-types.test.js';

// Utility functions for testing
function getCurrentTimestamps(): { unix: number; iso: string } {
    const unix = Math.floor(Date.now() / 1000);
    const iso = new Date(unix * 1000).toLocaleString('sv-SE', {
        timeZone: 'Asia/Kolkata',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).replace(' ', 'T') + '+05:30';
    return { unix, iso };
}

function convertToISTTimestamp(unixTimestamp: number): string {
    return new Date(unixTimestamp * 1000).toLocaleString('sv-SE', {
        timeZone: 'Asia/Kolkata',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).replace(' ', 'T') + '+05:30';
}

export async function testTimestampUtilities() {
    console.log("🕐 Testing Timestamp Utility Functions...");
    
    // Test timestamp conversion
    const testUnix = Math.floor(Date.now() / 1000);
    const istTimestamp = convertToISTTimestamp(testUnix);
    console.log(`   ✅ Unix: ${testUnix} -> IST: ${istTimestamp}`);
    
    // Test current timestamps
    const current = getCurrentTimestamps();
    console.log(`   ✅ Current Unix: ${current.unix}`);
    console.log(`   ✅ Current IST: ${current.iso}`);
    
    // Validate IST format (YYYY-MM-DDTHH:mm:ss+05:30)
    const istRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\+05:30$/;
    if (!istRegex.test(current.iso)) {
        throw new Error("IST format validation failed");
    }
    console.log("   ✅ IST format validation passed");
    
    console.log("✅ Timestamp utilities tests passed!\n");
}

export async function testWebhookLoggingWithIST() {
    console.log("📝 Testing Webhook Logging with IST Timestamps...");
    
    const webhookService = new WebhookLogService();
    
    // Mock request object
    const mockReq = {
        get: (header: string) => header === 'User-Agent' ? 'IST-Test-Agent' : undefined,
        headers: { 'x-forwarded-for': '*************' },
        connection: { remoteAddress: '*************' },
        socket: { remoteAddress: '*************' }
    } as any;
    
    // Create webhook log
    const webhookLog = await webhookService.logIncomingWebhook(mockReq, testWebhooks.textMessage);
    
    console.log(`   ✅ Webhook logged with ID: ${webhookLog.webhookId}`);
    console.log(`   ✅ Timestamp (Unix): ${webhookLog.timestamp}`);
    console.log(`   ✅ Timestamp (IST): ${webhookLog.timestampISO}`);
    console.log(`   ✅ Received at (Unix): ${webhookLog.receivedAt}`);
    console.log(`   ✅ Received at (IST): ${webhookLog.receivedAtISO}`);
    console.log(`   ✅ Last updated (Unix): ${webhookLog.lastUpdated}`);
    console.log(`   ✅ Last updated (IST): ${webhookLog.lastUpdatedISO}`);
    console.log(`   ✅ Message code: ${webhookLog.messageCode || 'undefined'}`);
    
    // Validate all IST timestamps are properly formatted
    const istFields = [
        webhookLog.timestampISO,
        webhookLog.receivedAtISO,
        webhookLog.lastUpdatedISO
    ];
    
    const istRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\+05:30$/;
    for (const field of istFields) {
        if (!istRegex.test(field)) {
            throw new Error(`Invalid IST format: ${field}`);
        }
    }
    console.log("   ✅ All IST timestamps properly formatted");
    
    console.log("✅ Webhook logging with IST tests passed!\n");
    
    return webhookLog;
}

export async function testStatusUpdatesWithIST() {
    console.log("🔄 Testing Status Updates with IST Timestamps...");
    
    const webhookService = new WebhookLogService();
    
    // First create a webhook log
    const mockReq = {
        get: (header: string) => header === 'User-Agent' ? 'Status-Test-Agent' : undefined,
        headers: { 'x-forwarded-for': '*************' },
        connection: { remoteAddress: '*************' },
        socket: { remoteAddress: '*************' }
    } as any;
    
    const webhookLog = await webhookService.logIncomingWebhook(mockReq, testWebhooks.greetingMessage);
    console.log(`   ✅ Created test webhook: ${webhookLog.webhookId}`);
    
    // Test processing status update
    await webhookService.markAsProcessing(webhookLog.webhookId, webhookLog.timestamp);
    console.log("   ✅ Marked as processing");
    
    // Test completion with IST timestamps
    await webhookService.markAsProcessed(webhookLog.webhookId, webhookLog.timestamp);
    console.log("   ✅ Marked as processed with IST timestamps");
    
    console.log("✅ Status updates with IST tests passed!\n");
    
    return webhookLog;
}

export async function testQueryResponseWithIST() {
    console.log("🔍 Testing Query Response with IST Timestamps...");
    
    const webhookService = new WebhookLogService();
    
    // Test querying to verify IST timestamps in response
    const businessLogs = await webhookService.getWebhooksByBusiness('***********', { limit: 3 });
    
    if (businessLogs.length > 0) {
        const log = businessLogs[0];
        console.log("   ✅ Query response includes IST timestamps:");
        console.log(`      - Timestamp IST: ${log.timestampISO}`);
        console.log(`      - Received at IST: ${log.receivedAtISO}`);
        console.log(`      - Message code: ${log.messageCode || 'undefined'}`);
        
        if (log.processedAtISO) {
            console.log(`      - Processed at IST: ${log.processedAtISO}`);
        }
        
        // Validate IST format in query results
        const istRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\+05:30$/;
        if (!istRegex.test(log.timestampISO) || !istRegex.test(log.receivedAtISO)) {
            throw new Error("Query results contain invalid IST timestamps");
        }
        console.log("   ✅ Query result IST timestamps validated");
    } else {
        console.log("   ⚠️  No logs found for business number (this is okay for first run)");
    }
    
    console.log("✅ Query response with IST tests passed!\n");
}

export async function testTimezoneConsistency() {
    console.log("⏰ Testing Timezone Consistency...");
    
    const webhookService = new WebhookLogService();
    
    // Create multiple webhooks and check timestamp consistency
    const mockReq = {
        get: (header: string) => header === 'User-Agent' ? 'Timezone-Test-Agent' : undefined,
        headers: { 'x-forwarded-for': '*************' },
        connection: { remoteAddress: '*************' },
        socket: { remoteAddress: '*************' }
    } as any;
    
    const log1 = await webhookService.logIncomingWebhook(mockReq, testWebhooks.textMessage);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    const log2 = await webhookService.logIncomingWebhook(mockReq, testWebhooks.locationMessage);
    
    // Check that timestamps are in order
    if (log2.timestamp <= log1.timestamp) {
        throw new Error("Timestamps not in chronological order");
    }
    console.log("   ✅ Unix timestamps in chronological order");
    
    // Check IST timestamps are also in order
    if (log2.timestampISO <= log1.timestampISO) {
        throw new Error("IST timestamps not in chronological order");
    }
    console.log("   ✅ IST timestamps in chronological order");
    
    // Check timezone offset consistency
    if (!log1.timestampISO.endsWith('+05:30') || !log2.timestampISO.endsWith('+05:30')) {
        throw new Error("Inconsistent timezone offset");
    }
    console.log("   ✅ Timezone offset consistent (+05:30)");
    
    console.log("✅ Timezone consistency tests passed!\n");
}

export async function runISTTimestampTests() {
    console.log("🚀 Starting IST Timestamp Integration Tests...\n");
    
    const tests = [
        { name: "Timestamp Utilities", fn: testTimestampUtilities },
        { name: "Webhook Logging with IST", fn: testWebhookLoggingWithIST },
        { name: "Status Updates with IST", fn: testStatusUpdatesWithIST },
        { name: "Query Response with IST", fn: testQueryResponseWithIST },
        { name: "Timezone Consistency", fn: testTimezoneConsistency }
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        try {
            await test.fn();
            passed++;
        } catch (error) {
            console.error(`❌ ${test.name} failed:`, error);
            failed++;
        }
    }
    
    console.log("\n📊 IST Timestamp Test Summary:");
    console.log(`   ✅ Passed: ${passed}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   📈 Success Rate: ${Math.round((passed / tests.length) * 100)}%`);
    
    if (failed > 0) {
        throw new Error(`${failed} tests failed`);
    }
    
    console.log("\n🎉 All IST timestamp tests passed!");
} 