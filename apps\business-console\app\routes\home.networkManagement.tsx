'use client'

import { json } from "@remix-run/node";
import { use<PERSON><PERSON><PERSON>, useLoaderData, useNavigate } from "@remix-run/react";
import * as React from "react";
import { getNetworks } from "~/services/businessConsoleService";
import { Networks } from "~/types/api/businessConsoleService/Network";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Edit, Search } from "lucide-react";
import { Button } from "~/components/ui/button";
import { useEffect, useState } from "react";
import NetworkManagementModal from "~/components/ui/createnetWorkManagement";
import { createNetwork } from "~/services/netWorks";
import { Seller } from "~/types/api/businessConsoleService/SellerManagement";
import { getSellerList } from "~/services/masterItemCategories";
import { Input } from "~/components/ui/input";
import { useDebounce } from "~/hooks/useDebounce";
import SpinnerLoader from "~/components/loader/SpinnerLoader";


export type CreateNetwork = Omit<Networks, "id">;

interface LoaderData {
  netWorkData: Networks[],
  sellerList: Seller[],
  permissions: string[]

}

export interface ActionData {
  success?: boolean,
  error?: string
}

export const loader = withAuth(async ({ request, user }) => {
  try {
    console.log("loader entered");
    const response = await getNetworks(request);
    const responseSeller = await getSellerList(request)
    const permission = user?.userDetails?.roles

    return withResponse({
      netWorkData: response.data,
      sellerList: responseSeller.data,
      permissions: permission
    }, response.headers);

  } catch (error) {
    console.log("loader failed");
    console.error("Error in loader:", error);
    // Return a JSON-based error shape
    throw new Response('failed to getSeller', { status: 500 })

  }

});

export async function action({ request }: { request: Request }) {
  const formData = await request.formData();
  const businessType = formData.get("businessType") as string;


  const ondcDomain = businessType === "Restaurant" ? "RET11" : "RET10"

  const data: any = {
    name: formData.get("name") as string,
    description: formData.get("description") as string,
    isPrivate: formData.get("isPrivate") === "true",
    managerId: Number(formData.get("managerId")),
    networkType: formData.get("networkType") as string,
    domain: formData.get("domain") as string,
    ondcDomain: ondcDomain,
    defaultSellerId: Number(formData.get("defaultSellerId")),
    multiSeller: formData.get("multiSeller") as unknown as boolean
  };
  try {
    const response = await createNetwork(data, request);
    return withResponse({ success: response.statusCode == 200 }, response.headers)
  }
  catch (error) {
    console.log("loader failed");
    console.error("Error in action:", error);

    throw new Response('failed to createSeller', { status: 500 })
  }
}
export default function NetworksPage() {
  const goTo = useNavigate();
  const { netWorkData, sellerList, permissions } = useLoaderData<LoaderData>();
  const salesPermissions = permissions?.includes("FmSalesManager") ?? false
  const dataleng = netWorkData.length;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [networkFilter, setNetworkFilter] = useState<Networks[]>([]);
  const handleSearch = (val: string) => {
    setSearchTerm(val)

  }
  const fetcher = useFetcher<{ networkData: Networks[] }>()
  const loading = fetcher.state !== "idle";
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  useEffect(() => {
    if (netWorkData) {
      setNetworkFilter(netWorkData);
    }
  }, [netWorkData, fetcher?.data]);

  useEffect(() => {
    if (debouncedSearchTerm.length >= 3 && debouncedSearchTerm !== "") {

      const filterdata = netWorkData?.filter(item => item.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()))

      setNetworkFilter(filterdata)
    }
    else {
      setNetworkFilter(netWorkData)
    }

  }, [debouncedSearchTerm, netWorkData])




  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">Network Management</h1> <div className="flex flex-col my-3">
        <div className="relative mx-10 flex flex-col ">
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400' />
          <Input
            type='search'
            placeholder="Search by Network Name"
            value={searchTerm}
            onChange={(e: { target: { value: string; }; }) => handleSearch(e.target.value)}
            className="max-w-sm rounded-full pl-10"
          />
        </div>
      </div>

      <Table>
        {/* Table header */}
        <TableHeader className="bg-gray-100">
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Manager Business ID</TableHead>
            <TableHead>Manager Name</TableHead>
            {/* An extra column for any actions, e.g., Edit */}
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>

        {/* Table body */}
        <TableBody>
          {loading && <SpinnerLoader loading={loading} size={20} />}

          {networkFilter?.length > 0 ? (
            networkFilter.map((network) => (
              <TableRow key={network.id}>
                <TableCell>{network.id}</TableCell>
                <TableCell className={`cursor-pointer text-blue-500 font-bold  items-center`}
                  onClick={() =>
                    goTo(`/home/<USER>
                  }                                    >{network.name}</TableCell>
                <TableCell>{network.description}</TableCell>
                <TableCell>{network.managerBusinessId}</TableCell>
                <TableCell>{network.managerName}</TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {!salesPermissions && <Button className="fixed bottom-5 right-5 rounded-full cursor-pointer" onClick={() => setIsModalOpen(true)}>+ Add Network</Button>
      }      <NetworkManagementModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} sellerList={sellerList} />

    </div>
  );
}