import { ApiResponse } from "~/types/api/Api";
import { Dc<PERSON>ody, DcCong, DcCreateRes } from "~/types/api/businessConsoleService/DeliveryConfig";
import { API_BASE_URL, apiRequest } from "~/utils/api";


export async function getDeliveryConfigs(
  sellerId: number,
  request: Request
): Promise<ApiResponse<DcCong>> {
  try {
    const response = await apiRequest<DcCong>(
      `${API_BASE_URL}/delivery-charge/config/${sellerId}`,
      'GET',
      undefined,
      {},
      true,
      request
    )
    if (response) {
      return response;
    }
    // Optionally, you can throw an error or return a default value here
    throw new Error("No response received from delivery config API");
  }
  catch (err) {
    throw new Error("Failed to fetch deliveryConfig ");
  }
}

export async function createDcConfig(
  dcConfigBody: Partial<DcBody>,
  request?: Request,
  configId?: number,
): Promise<ApiResponse<DcCreateRes>> {
  let ConfigId;
  if (configId && configId !== null && configId > 0) {
    ConfigId = configId
  }

  const url = ConfigId ? `${API_BASE_URL}/delivery-charge/config/${ConfigId}` : `${API_BASE_URL}/delivery-charge/config`
  const response = await apiRequest<DcCreateRes>(
    url,
    ConfigId ? "PUT" : "POST",
    dcConfigBody,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to update addons group");
  }

}

export async function deleteDeliveryConfig(
  configId: number,
  request?: Request
): Promise<ApiResponse<void>> {
  const response = await apiRequest<void>(
    `${API_BASE_URL}/delivery-charge/config/${configId}`,
    "DELETE",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to delete delivery configuration");
  }
}