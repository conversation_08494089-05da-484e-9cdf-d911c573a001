import { Textarea } from "@headlessui/react";
import { Input } from "../ui/input";
import { X } from "lucide-react";

export default function BusinessSetting() {
      return (
            <div className="flex flex-col shadow-md bg-white p-4 w-full border border-gray-300 rounded-xl gap-3 items-start my-4">
                  <h1 className="text-lg font-semibold">Business Settings</h1>

                  {/* Label positioned on the border */}

                  <div className="relative w-full mt-3 border border-gray-300 rounded-md p-2">
                        <label className="absolute left-3 top-0 transform -translate-y-1/2 bg-white px-1 text-sm font-medium text-gray-700">
                              Website *
                        </label>
                        <Input
                              type="text"
                              defaultValue="www.abc.mnetlive.com"
                              className="w-full pr-10 border-none focus:ring-0 outline-none"
                        />
                        <button className="absolute top-1/2 right-3 transform -translate-y-1/2 text-gray-500">
                              <X size={16} />
                        </button>
                  </div>
                  <Textarea
                        placeholder="Description"
                        className="w-full border-gray-500 rounded-md border p-2 text-left align-top h-[120px]"
                  />                 {/* <Textarea placeholder="Description" className="w-full border-gray-500 rounded-md border  p-10 items-start" /> */}
                  <Textarea placeholder="Description" className="w-full border-gray-500 rounded-md border p-2 text-left align-top h-[120px]" />
            </div>
      );
}
