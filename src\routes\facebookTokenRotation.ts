import { Router } from 'express';
import FacebookTokenRotationController from '../controllers/facebookTokenRotationController.js';
import { authenticateToken } from '../middleware/auth.js';
import { logRequestResponse } from '../middleware/requestLogger.middleware.js';

const router = Router();
const controller = new FacebookTokenRotationController();

// Apply middleware to all routes
router.use(logRequestResponse);
router.use(authenticateToken);

/**
 * @route   POST /api/facebook/token-rotation/trigger
 * @desc    Manually trigger token rotation for all expiring tokens
 * @access  Private
 */
router.post('/trigger', controller.triggerManualRotation.bind(controller));

/**
 * @route   POST /api/facebook/token-rotation/:sellerId
 * @desc    Rotate token for a specific seller
 * @access  Private
 * @param   {string} sellerId - The seller ID
 * @body    { revokeOldToken?: boolean }
 */
router.post('/:sellerId', controller.rotateSellerToken.bind(controller));


/**
 * @route   GET /api/facebook/token-rotation/health
 * @desc    Health check endpoint for token rotation service
 * @access  Private
 */
router.get('/health', controller.healthCheck.bind(controller));

/**
 * @route   GET /api/facebook/token-rotation/status
 * @desc    Get cron service status
 * @access  Private
 */
router.get('/status', controller.getCronStatus.bind(controller));

export default router; 