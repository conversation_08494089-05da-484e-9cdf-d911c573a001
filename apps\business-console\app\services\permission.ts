import { ApiResponse } from "~/types/api/Api";
import { Permissions } from "~/types/api/businessConsoleService/Permissions";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getPermissions(
  request?: Request
): Promise<ApiResponse<Permissions[]>> {
  const response = await apiRequest<Permissions[]>(
    `${API_BASE_URL}/bc/mnetadmin/permissions`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch trip summary");
  }
}
