# WhatsApp Webhook Logging System

## Overview
A comprehensive logging system for capturing, analyzing, and monitoring all incoming WhatsApp webhooks. This system provides detailed insights into webhook processing, error tracking, and performance analytics.

## Features

### ✅ Core Functionality
- **Complete Webhook Capture**: Logs all incoming webhook payloads with full raw data preservation
- **Automatic Data Extraction**: Extracts key fields like message type, business/customer numbers, and content
- **Message Classification**: Automatically categorizes messages (GREETING, ORDER_STATUS, FREE_GIFT_CLAIM)
- **Status Tracking**: Tracks webhook processing status from received to processed/failed
- **Error Handling**: Comprehensive error logging with stack traces and retry mechanisms
- **Performance Monitoring**: Tracks processing times and response metrics

### ✅ Analytics & Insights
- **Message Type Analytics**: Breakdown by text, interactive, location, etc.
- **Business Performance**: Per-business webhook statistics and success rates
- **Error Analysis**: Failed webhook tracking and error pattern identification
- **Peak Hour Analysis**: Identifies high-traffic periods
- **Health Monitoring**: System-wide health checks and alerts

### ✅ Query Capabilities
- Filter by business number
- Filter by customer number
- Filter by message type
- Filter by date range
- Filter by processing status
- Search by message content

## Database Schema

### Table: `webhook_logs_{env}`
```
Primary Key:
- webhookId (String) - Partition Key
- timestamp (Number) - Sort Key

Global Secondary Indexes:
1. BusinessNumberIndex: businessNumber + timestamp
2. CustomerNumberIndex: customerNumber + timestamp  
3. MessageTypeIndex: messageType + timestamp
4. StatusIndex: status + timestamp
```

### Key Fields
```typescript
interface WebhookLog {
    // Identifiers
    webhookId: string;
    timestamp: number;
    businessNumber: string;
    customerNumber?: string;
    messageId?: string;
    phoneNumberId?: string;
    
    // Message data
    messageType: MessageType;
    messageCode?: MessageCode;           // GREETING, ORDER_STATUS, FREE_GIFT_CLAIM
    messageDirection: MessageDirection;
    status: WebhookStatus;
    rawPayload: Record<string, any>;
    messageContent?: string;
    contactName?: string;
    
    // Processing metadata
    receivedAt: number;
    processedAt?: number;
    processingDuration?: number;
    
    // Error handling
    errorMessage?: string;
    errorDetails?: Record<string, any>;
    
    // Request metadata
    userAgent?: string;
    sourceIp?: string;
}
```

## Setup Instructions

### 1. Create DynamoDB Table
```bash
npm run create-webhook-table
```

Or programmatically:
```typescript
import { createWebhookLogTable } from './src/scripts/createWebhookLogTable.js';
await createWebhookLogTable();
```

### 2. AWS Configuration
Ensure your AWS credentials are configured with DynamoDB permissions:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "dynamodb:PutItem",
                "dynamodb:GetItem",
                "dynamodb:UpdateItem",
                "dynamodb:Query",
                "dynamodb:Scan"
            ],
            "Resource": [
                "arn:aws:dynamodb:*:*:table/webhook_logs_*",
                "arn:aws:dynamodb:*:*:table/webhook_logs_*/index/*"
            ]
        }
    ]
}
```

### 3. Environment Variables
No new environment variables required. Uses existing AWS configuration.

## Usage Examples

### Basic Webhook Logging (Automatic)
The system automatically logs all incoming webhooks in the controller:

```typescript
// This happens automatically in whatsappWebhookController.ts
const webhookLog = await webhookLogService.logIncomingWebhook(req, body);
await webhookLogService.markAsProcessing(webhookLog.webhookId, webhookLog.timestamp);
// ... process webhook ...
await webhookLogService.markAsProcessed(webhookLog.webhookId, webhookLog.timestamp);
```

### Query Webhook Logs
```typescript
const webhookLogService = new WebhookLogService();

// Get webhooks for a specific business
const businessWebhooks = await webhookLogService.getWebhooksByBusiness('**********', {
    startTimestamp: Date.now() - (24 * 60 * 60 * 1000), // Last 24 hours
    limit: 50
});

// Get failed webhooks for monitoring
const failedWebhooks = await webhookLogService.getRecentFailures(20);

// Get webhooks by customer
const customerWebhooks = await webhookLogService.getWebhooksByCustomer('**********');
```

### Analytics & Monitoring
```typescript
// Get business analytics
const analytics = await webhookLogService.getAnalytics('**********');
console.log('Total webhooks:', analytics.totalWebhooks);
console.log('Error rate:', analytics.errorRate, '%');
console.log('Average processing time:', analytics.averageProcessingTime, 'ms');

// Dashboard statistics
const stats = await webhookLogService.getDashboardStats('**********', 24);
console.log('Success rate:', stats.successRate, '%');

// System health check
const health = await webhookLogService.getSystemHealth();
console.log('System status:', health.status);
```

### Manual Webhook Logging
```typescript
const webhookLogService = new WebhookLogService();

// Log custom webhook
const webhookLog = await webhookLogService.logIncomingWebhook(
    req,
    webhookPayload,
    businessNumber,
    customerNumber
);

// Update status manually
await webhookLogService.markAsFailed(
    webhookLog.webhookId,
    webhookLog.timestamp,
    'Custom error message',
    { customData: 'error details' }
);
```

## API Reference

### WebhookLogService Methods

#### `logIncomingWebhook(req, rawPayload, businessNumber?, customerNumber?)`
Logs an incoming webhook with automatic data extraction.

**Parameters:**
- `req`: Express Request object
- `rawPayload`: Complete webhook payload
- `businessNumber`: Optional business number override
- `customerNumber`: Optional customer number override

**Returns:** `Promise<WebhookLog>`

#### `markAsProcessing(webhookId, timestamp)`
Updates webhook status to PROCESSING.

#### `markAsProcessed(webhookId, timestamp)`
Marks webhook as successfully processed with timing data.

#### `markAsFailed(webhookId, timestamp, errorMessage, errorDetails?)`
Marks webhook as failed with error information.

#### `getWebhooksByBusiness(businessNumber, filter?)`
Retrieves webhooks for a specific business with optional filtering.

#### `getAnalytics(businessNumber?, startTime?, endTime?)`
Gets comprehensive analytics data.

### WebhookLogRepository Methods

#### `createLog(logData)`
Creates a new webhook log entry.

#### `updateLog(webhookId, timestamp, updates)`
Updates an existing webhook log.

#### `getLogsByBusinessNumber(businessNumber, filter?)`
Queries webhooks by business number with filtering.

#### `getLogsByStatus(status, filter?)`
Queries webhooks by processing status.

#### `getAnalytics(businessNumber?, startTime?, endTime?)`
Generates analytics data with aggregations.

## Monitoring & Alerts

### Health Check Endpoint
```typescript
const health = await webhookLogService.getSystemHealth();

// Response format:
{
    status: 'healthy' | 'degraded' | 'unhealthy',
    metrics: {
        totalWebhooksLastHour: number,
        errorRateLastHour: number,
        avgProcessingTime: number
    }
}
```

### Alert Conditions
- **Unhealthy**: Error rate > 20% OR processing time > 5000ms
- **Degraded**: Error rate > 10% OR processing time > 2000ms
- **Healthy**: All metrics within normal ranges

### Dashboard Metrics
```typescript
const stats = await webhookLogService.getDashboardStats(businessNumber, 24);

// Response format:
{
    totalWebhooks: number,
    successRate: number,
    averageResponseTime: number,
    recentErrors: number
}
```

## Performance Considerations

### Indexing Strategy
- **BusinessNumberIndex**: Efficient business-specific queries
- **CustomerNumberIndex**: Customer interaction history
- **MessageTypeIndex**: Message type analytics
- **StatusIndex**: Error monitoring and failed webhook tracking

### Query Optimization
- Use specific indexes for filtering
- Implement pagination with `lastEvaluatedKey`
- Limit query results to prevent timeouts
- Use business-specific queries when possible

### Cost Optimization
- **Pay-per-request billing**: Scales with usage
- **Projected attributes**: All attributes for flexibility
- **Automatic scaling**: No capacity planning required

## Troubleshooting

### Common Issues

1. **Table doesn't exist**
   ```bash
   npm run create-webhook-table
   ```

2. **Permission denied**
   Check AWS IAM permissions for DynamoDB operations.

3. **High error rates**
   Check recent failures:
   ```typescript
   const failures = await webhookLogService.getRecentFailures(50);
   ```

4. **Slow queries**
   Ensure you're using appropriate indexes and filters.

### Debug Mode
Enable detailed logging:
```bash
DEBUG=webhook-log npm start
```

## File Structure
```
src/
├── database/
│   ├── entities/
│   │   └── WebhookLog.ts          # Table schema & configuration
│   └── repository/
│       └── WebhookLogRepository.ts # Database operations
├── services/
│   └── webhookLog.service.ts      # Business logic layer
├── types/
│   └── webhook.types.ts           # Type definitions
├── controllers/
│   └── whatsappWebhookController.ts # Integration point
└── scripts/
    └── createWebhookLogTable.ts   # Table creation script
```

## Testing

### Create Test Data
```typescript
// Create a test webhook log
const testLog = await webhookLogService.logIncomingWebhook(
    mockRequest,
    mockWebhookPayload,
    'testBusiness',
    'testCustomer'
);
```

### Verify Integration
```bash
# Send a test webhook to your endpoint
curl -X POST http://localhost:3000/webhook \
  -H "Content-Type: application/json" \
  -d '{"entry":[{"changes":[{"value":{"messages":[{"type":"text","text":{"body":"test"}}]}}]}]}'
```

## Contributing

When adding new features:
1. Update type definitions in `webhook.types.ts`
2. Add repository methods if needed
3. Update service layer with business logic
4. Add proper error handling
5. Update documentation

## Security Notes

- Raw webhook payloads may contain sensitive data
- Customer phone numbers are stored for analytics
- IP addresses are logged for debugging
- Consider data retention policies for compliance 