import { Rocket, Sparkles } from "lucide-react";

export function ComingSoonOverlay() {
  return (
    <div className="fixed right-8 bottom-8 z-50">
      <div className="relative group cursor-pointer">
        {/* Rectangle Toast Container */}
        <div className="relative bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl px-6 py-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 will-change-transform max-w-sm">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-xl"></div>

          {/* Content */}
          <div className="relative z-10 flex items-center space-x-4">
            {/* Icon */}
            <div className="flex-shrink-0">
              <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 text-white group-hover:from-blue-600 group-hover:to-purple-700 transition-all duration-300">
                <Rocket className="w-5 h-5" />
              </div>
            </div>

            {/* Text Content */}
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="font-bold text-gray-900 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  This feature is coming soon.
                </h3>
                <Sparkles className="w-4 h-4 text-yellow-500" />
              </div>
              <p className="text-sm text-gray-600 leading-relaxed">
                Something amazing is coming your way!
                <span className="font-semibold text-blue-600"> Stay tuned.</span>
              </p>
            </div>
          </div>

          {/* Subtle glow effect on hover */}
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>
      </div>
    </div>
  );
}
