import { Megaphone, Menu, XCircle } from "lucide-react"
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>onte<PERSON>,
    SheetTrigger,
} from "@components/ui/sheet"
import { Button } from "@components/ui/button"
import { Link, Navigate, Outlet, useLoaderData, useLocation } from "@remix-run/react";
// import {
//     AlertDialog,
//     AlertDialogAction,
//     AlertDialogCancel,
//     AlertDialogContent,
//     AlertDialogDescription,
//     AlertDialogFooter,
//     AlertDialogHeader,
//     AlertDialogTitle,
//     AlertDialogTrigger,
// } from "@components/ui/alert-dialog"
import { destroySession, getSession } from "@utils/session.server"
import { LoaderFunction, redirect } from "@remix-run/node"
import { withAuth, withResponse } from "@utils/auth-utils";
import { useState, useEffect } from "react";

interface LoaderData {
    userPermissions: string[];
    userDetails: {
        userDetails: {
            businessName?: string;
        };
    };
}

export const loader: LoaderFunction = withAuth(
    async ({ user }) => {
        return withResponse({
            userPermissions: user?.userPermissions || [],
            userDetails: user || { userDetails: {} },
        });
    }
)

export const action = withAuth(
    async ({ request }) => {
        const session = await getSession(request.headers.get("Cookie"))
        return redirect("/login", {
            headers: {
                "Set-Cookie": await destroySession(session),
            },
        })
    }
);

export default function Seller() {
    const loaderData = useLoaderData<LoaderData>();
    const location = useLocation();
    const [isOpen, setIsOpen] = useState(false);
    const [shouldRedirect, setShouldRedirect] = useState(false);
    // const isWhatsAppEnabled = loaderData.userPermissions.includes("seller_app.whatsappBasic")
    // TODO: Remove this after Demo
    const isWhatsAppEnabled = true;
    const activeSection = location.pathname.split("/")[2];

    useEffect(() => {
        if (location.pathname === "/seller") {
            setShouldRedirect(true);
        } else {
            setShouldRedirect(false);
        }
    }, [location.pathname]);

    // First check if we should redirect to marketing
    if (shouldRedirect && isWhatsAppEnabled) {
        return <Navigate to="/seller/marketing" replace />;
    }

    // Then check if either WhatsApp or NetworkManager is not enabled to show access denied
    if (!isWhatsAppEnabled) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
                <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
                    <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
                    <p className="text-gray-600">
                        {!isWhatsAppEnabled
                            ? "WhatsApp functionality is not enabled for your account."
                            : "Please contact your administrator for access."
                        }
                    </p>
                </div>
            </div>
        );
    }

    const NavContent = () => (
        <div className="flex flex-col h-full">
            <div className="flex flex-col items-start space-y-1 gap-2 p-4">
                <img
                    src="/mnet-logo.svg"
                    alt="mNet Logo"
                    className="h-12 w-auto"
                />
                <div className="flex items-center mt-4 px-4 py-2 border rounded-md w-full">
                    <div className="flex items-center space-x-3">
                        <div className="h-6 w-6 rounded-full bg-primary flex items-center justify-center text-m font-bold text-white">
                            {loaderData?.userDetails?.userDetails?.businessName?.[0]?.toUpperCase() || "B"}
                        </div>
                        <div className="text-gray-900 text-m">
                            {loaderData?.userDetails?.userDetails?.businessName || "Business Name"}
                        </div>
                    </div>
                </div>
            </div>

            <nav className="flex-1 p-4">


                <div className="space-y-2">
                    {/* <Link to="/seller/customerAnalysis" onClick={() => setIsOpen(false)}>
                        <Button
                            variant={activeSection === "customers" ? "secondary" : "ghost"}
                            className="w-full justify-start"
                        >
                            <Megaphone className="h-5 w-5 mr-3" />
                            CustomerAnalysis
                        </Button>
                    </Link> */}
                    {isWhatsAppEnabled && (
                        <Link to="/seller/marketing" onClick={() => setIsOpen(false)}>
                            <Button
                                variant={activeSection === "marketing" ? "secondary" : "ghost"}
                                className="w-full justify-start"
                            >
                                <Megaphone className="h-5 w-5 mr-3" />
                                Marketing
                            </Button>
                        </Link>
                    )}
                </div>
            </nav>

            {/* <div className="p-4">
                <AlertDialog>
                    <AlertDialogTrigger asChild>
                        <Button variant="ghost" className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50">
                            <LogOut className="h-5 w-5 mr-3" />
                            Logout
                        </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure you want to logout?</AlertDialogTitle>
                            <AlertDialogDescription>
                                You will be redirected to the login page.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleLogout}>
                                Logout
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </div> */}
        </div>
    );

    return (
        <div className="min-h-screen bg-white">
            {/* Desktop Sidebar */}
            <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 border-r">
                <NavContent />
            </div>

            {/* Mobile Header */}
            <div className="md:hidden border-b">
                <div className="flex items-center justify-between p-4">
                    <img
                        src="/mnet-logo.svg"
                        alt="mNet Logo"
                        className="h-8 w-auto"
                    />
                    <Sheet open={isOpen} onOpenChange={setIsOpen}>
                        <SheetTrigger asChild>
                            <Button variant="ghost" size="icon">
                                <Menu className="h-6 w-6" />
                            </Button>
                        </SheetTrigger>
                        <SheetContent side="left" className="w-64 p-0">
                            <NavContent />
                        </SheetContent>
                    </Sheet>
                </div>
            </div>

            {/* Main Content */}
            <div className="md:pl-64">
                <main className="flex-1">
                    <Outlet />
                </main>
            </div>
        </div>
    );
}
