import { json, type LoaderFunction, type ActionFunction } from '@remix-run/node';
import { useLoaderData, useActionData, Form, useFetcher } from '@remix-run/react';
// eslint-disable-next-line import/no-unresolved
import { getWhatsAppBusinessProfile, updateWhatsAppBusinessProfile, createWhatsAppQRCode } from '~/services/whatsappApi.server';
import type { WhatsAppBusinessProfile, WhatsAppConnectionData } from "~/types/whatsapp";
// eslint-disable-next-line import/no-unresolved
import { withAuth } from '~/utils/auth-utils';
// eslint-disable-next-line import/no-unresolved
import { getFirebaseAdmin } from '~/services/firebase.server';
import { X } from 'lucide-react';
// eslint-disable-next-line import/no-unresolved
import { Input } from '~/components/ui/input';
import { useEffect, useState } from 'react';
import { Button } from '~/components/ui/button';
import WhatsAppQRCode from '~/components/whatsapp/WhatsAppQRCode';

type LoaderData = {
  businessProfile: WhatsAppBusinessProfile | null;
  connectionState: WhatsAppConnectionData | null;
  phoneNumber: number,
  buinessName: string,
  qrCodeUrl: string,
  error?: string;
};

export const loader: LoaderFunction = withAuth(async ({ user }) => {
  console.log('WhatsApp Profile loader called', {
    userId: user?.userId,
    businessName: user?.businessName,
    sellerId: user?.userDetails?.sellerId
  });

  if (!user) {
    console.log('WhatsApp Profile loader: No user found');
    return json({ businessProfile: null, connectionState: null, error: 'User not authenticated' });
  }

  const db = getFirebaseAdmin();
  console.log('WhatsApp Profile loader: Fetching connection data from path', {
    collection: 'facebook-connects',
    docId: user.userDetails.sellerId.toString()
  });

  const docRef = db.collection('facebook-connects').doc(user.userDetails.sellerId.toString());
  const doc = await docRef.get();
  if (!doc.exists) {
    console.log('WhatsApp Profile loader: No connection data found');
    return json({
      businessProfile: null,
      connectionState: null,
      error: 'WhatsApp not connected. Please connect WhatsApp first.'
    });
  }

  const connectionState = doc.data() as WhatsAppConnectionData;
  console.log('WhatsApp Profile loader: Connection state retrieved', {
    sellerId: user.userDetails.sellerId,
    hasPhoneNumberId: !!connectionState?.mNetConnectedPhoneNumberId,
    hasAccessToken: !!connectionState?.access?.access_token
  });

  // Check if we have the required phone number ID
  if (!connectionState.mNetConnectedPhoneNumberId) {
    console.log('WhatsApp Profile loader: No phone number ID found');
    return json({
      businessProfile: null,
      connectionState,
      error: 'No WhatsApp phone number connected'
    });
  }

  try {
    const phoneNumber = connectionState.mNetConnectedPhoneNumber;
    const buinessName = connectionState.businessName;

    // Check if QR code exists, if not create one
    let qrCodeUrl = '';
    if (connectionState.qrCodeData?.deep_link_url) {
      console.log('WhatsApp Profile loader: Using existing QR code', {
        url: connectionState.qrCodeData.deep_link_url,
        code: connectionState.qrCodeData.code
      });
      qrCodeUrl = connectionState.qrCodeData.deep_link_url;
    } else {
      console.log('WhatsApp Profile loader: Creating new QR code');
      // Use the phone number ID from the connection state
      const phoneNumberId = connectionState.mNetConnectedPhoneNumberId;
      if (!phoneNumberId) {
        console.log('WhatsApp Profile loader: No phone number ID found for QR code creation');
        return json({
          businessProfile: null,
          connectionState,
          phoneNumber: phoneNumber,
          buinessName: buinessName,
          error: 'No phone number ID found for QR code creation'
        });
      }

      // Create a QR code
      const qrCodeResponse = await createWhatsAppQRCode(
        connectionState.access.access_token,
        phoneNumberId,
        `Hi`
      );

      if (qrCodeResponse.error) {
        console.log('WhatsApp Profile loader: Error creating QR code', {
          error: qrCodeResponse.error
        });
        // Continue even if QR code creation fails
      } else {
        // Save the QR code data to connectionState and firebase
        if (qrCodeResponse.data) {
          connectionState.qrCodeData = {
            code: qrCodeResponse.data.code || '',
            prefilled_message: qrCodeResponse.data.prefilled_message || '',
            deep_link_url: qrCodeResponse.data.deep_link_url || '',
            qr_image_url: qrCodeResponse.data.qr_image_url || '',
            created_at: new Date().toISOString()
          };

          await docRef.update({
            qrCodeData: connectionState.qrCodeData
          });

          console.log('WhatsApp Profile loader: QR code created and saved', {
            url: qrCodeResponse.data.deep_link_url,
            code: qrCodeResponse.data.code
          });
          
          if (qrCodeResponse.data.deep_link_url) {
            qrCodeUrl = qrCodeResponse.data.deep_link_url;
          }
        }
      }
    }

    // Fetch the business profile using our new API function
    const profileResponse = await getWhatsAppBusinessProfile(
      connectionState.access.access_token,
      connectionState.mNetConnectedPhoneNumberId
    );

    if (profileResponse.error) {
      console.log('WhatsApp Profile loader: Error fetching profile', {
        error: profileResponse.error
      });
      return json({
        businessProfile: null,
        connectionState,
        phoneNumber: phoneNumber,
        buinessName: buinessName,
        qrCodeUrl,
        error: `Failed to fetch business profile: ${profileResponse.error}`
      });
    }
    console.log(profileResponse.data?.data[0].profile_picture_url, "hsfguihdsuifhdui")
    // Ensure data exists before accessing its properties for logging
    if (profileResponse.data) {
      console.log('WhatsApp Profile loader: Profile fetched successfully', {
        about: profileResponse.data.data[0].about,
        email: profileResponse.data.data[0].email,
        websitesCount: profileResponse.data.data[0].websites?.length || 0
      });
    }

    return json({
      businessProfile: profileResponse.data?.data[0] || null,
      phoneNumber: phoneNumber,
      buinessName: buinessName,
      connectionState,
      qrCodeUrl
    });
  } catch (error) {
    console.error('WhatsApp Profile loader: Exception encountered', error);
    return json({
      businessProfile: null,
      connectionState,
      error: `Exception: ${error instanceof Error ? error.message : String(error)}`
    });
  }
});

type ActionData = {
  success?: boolean;
  error?: string;
  updatedProfile?: WhatsAppBusinessProfile;
};

export const action: ActionFunction = withAuth(async ({ user, request }) => {
  const formData = await request.formData();
  console.log('WhatsApp Profile action called', {
    userId: user?.userId,
    sellerId: user?.userDetails?.sellerId,
    formEntries: Array.from(formData.entries()).map(([key, value]) =>
      ({ key, value: typeof value === 'string' ? value : '[File or complex value]' }))
  });

  if (!user) {
    return json({ error: 'User not authenticated' }, { status: 401 });
  }

  const db = getFirebaseAdmin();
  const docRef = db.collection('facebook-connects').doc(user.userDetails.sellerId.toString());
  const doc = await docRef.get();

  if (!doc.exists) {
    console.log('WhatsApp Profile action: No connection data found');
    return json({ error: 'WhatsApp not connected' }, { status: 404 });
  }

  const connectionState = doc.data() as WhatsAppConnectionData;

  if (!connectionState.mNetConnectedPhoneNumberId) {
    console.log('WhatsApp Profile action: No phone number ID found');
    return json({ error: 'No WhatsApp phone number connected' }, { status: 400 });
  }

  // Extract profile data from form
  const profileData: Partial<WhatsAppBusinessProfile> = {
    about: formData.get('about')?.toString(),
    address: formData.get('address')?.toString(),
    description: formData.get('description')?.toString(),
    email: formData.get('email')?.toString(),
    vertical: formData.get('vertical')?.toString()
  };

  // Process websites (comma-separated list)
  const websitesInput = formData.get('websites')?.toString();
  if (websitesInput) {
    profileData.websites = websitesInput.split(',').map(site => site.trim());
  }

  console.log('WhatsApp Profile action: Updating profile with data', profileData);

  try {
    const updateResponse = await updateWhatsAppBusinessProfile(
      connectionState.access.access_token,
      connectionState.mNetConnectedPhoneNumberId,
      profileData
    );

    if (updateResponse.error) {
      console.log('WhatsApp Profile action: Error updating profile', {
        error: updateResponse.error,
        statusCode: updateResponse.error
      });
      return json({ error: `Failed to update business profile: ${updateResponse.error}` }, { status: 500 });
    }
    // Fetch the updated profile to return it
    const updatedProfileResponse = await getWhatsAppBusinessProfile(
      connectionState.access.access_token,
      connectionState.mNetConnectedPhoneNumberId
    );
    return json({
      success: true,
      updatedProfile: updatedProfileResponse.data || null
    });
  } catch (error) {
    console.error('WhatsApp Profile action: Exception encountered', error);
    return json({
      error: `Exception: ${error instanceof Error ? error.message : String(error)}`
    }, { status: 500 });
  }
});

export default function WhatsAppProfile() {
  const { businessProfile, connectionState, error, phoneNumber, buinessName, qrCodeUrl } = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();

  // Use the most recent profile data
  const profile = actionData?.updatedProfile || businessProfile;

  // State to track form changes
  const [formData, setFormData] = useState<{
    websites: string;
    address: string;
    description: string;
  }>({
    websites: profile?.websites?.join(", ") || "",
    address: profile?.address || "",
    description: profile?.description || "",
  });
  const [isFormDirty, setIsFormDirty] = useState(false);


  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setIsFormDirty(true);
  };

  const fetcher = useFetcher()
  const loading = fetcher.state !== "idle";

  const [whatsappProfile, setWhatsAppProfile] = useState('')

  useEffect(() => {
    if (profile?.profile_picture_url)
      setWhatsAppProfile(profile?.profile_picture_url)

  }, [profile?.profile_picture_url])
  
  useEffect(() => {
    if (actionData?.success)
      setIsFormDirty(false)

  }, [actionData?.success])

  // Error state
  if (error) {
    return (
      <div className="min-h-screen lg:p-6">
        <div className="mx-auto mb-6">
          <div className="mb-4">
            <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h1 className="text-xl md:text-3xl font-bold text-gray-900">WhatsApp Business Profile</h1>
                <p className="mt-2 text-gray-600">Manage your WhatsApp business settings and profile</p>
              </div>
            </div>
          </div>
          <div className="p-6 bg-destructive/10 rounded-lg border border-destructive/20">
            <h2 className="text-xl font-semibold text-destructive">Error</h2>
            <p className="mt-2 text-destructive">{error}</p>
          </div>
        </div>
      </div>
    );
  }
  // Connection state check
  if (!connectionState) {
    return (
      <div className="min-h-screen lg:p-6">
        <div className="mx-auto mb-6">
          <div className="mb-4">
            <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h1 className="text-xl md:text-3xl font-bold text-gray-900">WhatsApp Business Profile</h1>
                <p className="mt-2 text-gray-600">Manage your WhatsApp business settings and profile</p>
              </div>
            </div>
          </div>
          <div className="p-6 bg-yellow-50 rounded-lg border border-yellow-200">
            <h2 className="text-xl font-semibold text-yellow-700">WhatsApp Not Connected</h2>
            <p className="mt-2 text-yellow-600">Please connect your WhatsApp Business Account first.</p>
          </div>
        </div>
      </div>
    );
  }
  // Loading state
  if (!profile) {
    return (
      <div className="min-h-screen lg:p-6">
        <div className="mx-auto mb-6">
          <div className="mb-4">
            <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h1 className="text-xl md:text-3xl font-bold text-gray-900">WhatsApp Business Profile</h1>
                <p className="mt-2 text-gray-600">Manage your WhatsApp business settings and profile</p>
              </div>
            </div>
          </div>
          <div className="p-6 bg-blue-50 rounded-lg border border-blue-200">
            <h2 className="text-xl font-semibold text-blue-700">Loading Profile...</h2>
            <p className="mt-2 text-blue-600">Unable to load WhatsApp Business Profile data.</p>
          </div>
        </div>
      </div>
    );
  }


  return (
    <div className="min-h-screen p-6">
      <div className="mx-auto mb-6">

        {/* Header */}
        <div className="mb-4">
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-xl md:text-3xl font-bold text-gray-900">WhatsApp Business Profile</h1>
              <p className="mt-2 text-gray-600">Manage your WhatsApp business settings and profile</p>
            </div>
          </div>
        </div>

        {/* Success Message */}
        {actionData?.success && (
          <div className="mb-4 p-4 bg-green-50 text-green-700 rounded-lg border border-green-200">
            Profile updated successfully!
          </div>
        )}

        {/* Main Content */}
        <main className="max-w-4xl mx-auto space-y-6">
        {/* Profile Picture */}
        <div className="flex justify-center">
          {whatsappProfile ? (
            <div className="relative w-32 h-32">
              <img
                src={whatsappProfile}
                alt="Profile"
                className="w-full h-full rounded-full object-cover border-4 border-green-100 shadow-md"
              />
            </div>
          ) : (
            <div className="w-32 h-32 rounded-full bg-gradient-to-br from-teal-400 to-teal-600 flex items-center justify-center text-white text-4xl font-bold shadow-lg transition-transform duration-300 hover:scale-105">
              {buinessName.charAt(0).toUpperCase()}
            </div>
          )}
        </div>

        {/* Show QR Code */}
        <div className='flex justify-end'>
          <WhatsAppQRCode 
            url={qrCodeUrl} 
            businessPhoneNumber={phoneNumber} 
            businessLogo={businessProfile?.profile_picture_url || ""} 
          />
        </div>

        {/* Profile Settings */}
        <section className="bg-white rounded-lg border border-border p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4">Profile Settings</h2>
          <div className="space-y-4">
            <div className="relative">
              <label htmlFor="business-name" className="absolute -top-2 left-3 bg-white px-1 text-sm font-medium text-foreground">
                Name*
              </label>
              <Input
                id="business-name"
                value={buinessName}
                type="text"
                className="w-full"
                disabled
              />
            </div>
            <Input
              value={phoneNumber}
              placeholder="Phone Number"
              type="text"
              className="w-full"
              disabled
            />
            <Input
              placeholder="Email (optional)"
              type="email"
              className="w-full"
              disabled
              value={profile.email || ""}
            />
          </div>
        </section>

        {/* Business Settings */}
        <Form method="post" className="bg-white rounded-lg border border-border p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4">Business Settings</h2>
          <div className="space-y-6">
            <div className="relative">
              <label htmlFor="websites" className="absolute -top-2 left-3 bg-white px-1 text-sm font-medium text-foreground">
                Website*
              </label>
              <Input
                type="text"
                id="websites"
                name="websites"
                value={formData.websites}
                onChange={handleInputChange}
                className="w-full"
              />
              {formData.websites && (
                <button
                  type="button"
                  onClick={() => setFormData((prev) => ({ ...prev, websites: "" }))}
                  className="absolute top-1/2 right-3 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                >
                  <X size={16} />
                </button>
              )}
              <p className="text-xs text-muted-foreground mt-1">Comma-separated list of URLs</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <textarea
                  id="address"
                  name="address"
                  rows={3}
                  value={formData.address}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-input rounded-md focus:ring-2 focus:ring-ring focus:border-transparent resize-none"
                  placeholder="Address (optional)"
                  maxLength={256}
                />
                <p className="text-xs text-muted-foreground mt-1">Max 256 characters</p>
              </div>
              <div>
                <textarea
                  id="description"
                  name="description"
                  rows={3}
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-input rounded-md focus:ring-2 focus:ring-ring focus:border-transparent resize-none"
                  placeholder="Description (optional)"
                  maxLength={512}
                />
                <p className="text-xs text-muted-foreground mt-1">Max 512 characters</p>
              </div>
            </div>

            {isFormDirty && (
              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={!isFormDirty || formData.websites === ""}
                >
                  {loading ? "Updating..." : "Update Profile"}
                </Button>
              </div>
            )}
          </div>
        </Form>
        </main>
      </div>
    </div>
  );
}


