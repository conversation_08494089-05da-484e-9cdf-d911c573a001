import { getFirebaseAdmin } from '@utils/firebase.utils.js';
import logger from '@utils/express-logger.js';
import { IServiceResponse } from '@/interfaces/service.interface.js';
import { 
    WhatsAppConnectionData, 
    WhatsAppConnectionDataWithRollback,
    FacebookTokenRefreshResponse, 
    FacebookTokenRevokeResponse, 
    TokenRotationResult,
    RollbackInfo
} from '@/types/whatsapp.js';
import { 
    refreshFacebookSystemUserToken, 
    revokeFacebookSystemUserToken 
} from './whatsappService.js';
import MnetApiGatewayService from './mnetApiGateway.service.js';
import * as cron from 'node-cron';

class FacebookTokenRotationService {
    private readonly db;
    private readonly facebookAppId: string;
    private readonly facebookAppSecret: string;
    private readonly graphApiVersion: string = 'v21.0';
    private readonly mnetApiService: MnetApiGatewayService;

    /**
     * Safely serialize error objects to prevent circular reference issues
     */
    private serializeError(error: unknown): { message: string; stack?: string; errorType: string } {
        if (error instanceof Error) {
            return {
                message: error.message,
                stack: error.stack,
                errorType: error.constructor.name
            };
        }
        
        return {
            message: String(error),
            errorType: error?.constructor?.name || 'Unknown'
        };
    }

    // Cron service properties
    private cronJob: cron.ScheduledTask | null = null;
    private isRunning: boolean = false;
    private lastRunTime: Date | null = null;
    private nextRunTime: Date | null = null;

    constructor() {
        this.db = getFirebaseAdmin();
        this.facebookAppId = process.env.FACEBOOK_APP_ID!;
        this.facebookAppSecret = process.env.FACEBOOK_APP_SECRET!;
        this.mnetApiService = new MnetApiGatewayService();

        if (!this.facebookAppId || !this.facebookAppSecret) {
            throw new Error('Facebook App ID and App Secret are required for token rotation');
        }
    }



    /**
     * Fetch all sellers from the facebook-connects collection
     * @returns Array of WhatsApp connection data
     */
    async getAllSellers(): Promise<WhatsAppConnectionData[]> {
        try {
            logger.info('🔍 Fetching all sellers from facebook-connects collection');
            
            const querySnapshot = await this.db.collection('facebook-connects').get();
            const sellers: WhatsAppConnectionData[] = [];

            querySnapshot.forEach((doc) => {
                const data = doc.data() as WhatsAppConnectionData;
                if (data.access?.access_token && data.access.access_token.length > 10) {
                    sellers.push({
                        ...data,
                        sellerId: parseInt(doc.id) || data.sellerId
                    });
                }
            });

            logger.info(`✅ Found ${sellers.length} sellers with valid access tokens`);
            return sellers;
        } catch (error) {
            const serializedError = this.serializeError(error);
            logger.error('❌ Error fetching sellers from Firebase:', serializedError);
            throw error;
        }
    }

    /**
     * Refresh a Facebook system user access token with retry logic
     * @param currentToken The current access token to refresh
     * @returns New token response
     */
    async refreshSystemUserToken(currentToken: string): Promise<FacebookTokenRefreshResponse> {
        try {
            logger.info('🔄 Refreshing Facebook system user access token');
            
            const tokenData = await refreshFacebookSystemUserToken(
                currentToken,
                this.facebookAppId,
                this.facebookAppSecret,
                this.graphApiVersion
            );

            logger.info('✅ Token refreshed successfully', {
                expiresIn: tokenData.expires_in,
                tokenType: tokenData.token_type,
                newTokenPrefix: tokenData.access_token.substring(0, 10) + '...'
            });

            return tokenData;
        } catch (error) {
            const serializedError = this.serializeError(error);
            logger.error('❌ Error refreshing token:', serializedError);
            throw error;
        }
    }

    /**
     * Revoke a Facebook system user access token
     * @param tokenToRevoke The token to revoke
     * @param accessTokenForAuth The access token used for authentication
     * @returns Revocation response
     */
    async revokeSystemUserToken(tokenToRevoke: string, accessTokenForAuth: string): Promise<FacebookTokenRevokeResponse> {
        try {
            logger.info('🗑️ Revoking Facebook system user access token');

            const revokeData = await revokeFacebookSystemUserToken(
                tokenToRevoke,
                accessTokenForAuth,
                this.facebookAppId,
                this.facebookAppSecret,
                this.graphApiVersion
            );
            
            logger.info('✅ Token revoked successfully');
            return revokeData;
        } catch (error) {
            const serializedError = this.serializeError(error);
            logger.error('❌ Error revoking token:', serializedError);
            throw error;
        }
    }

    /**
     * Type-safe interface for Firebase update data
     */
    private createTokenUpdateData(
        newTokenData: FacebookTokenRefreshResponse, 
        mnetSyncSuccess?: boolean
    ): Partial<WhatsAppConnectionData> {
        // Compute absolute times for issued-at and expiry
        const issuedAtMs = Date.now();
        const expiresAtMs = issuedAtMs + (newTokenData.expires_in * 1000);
        const issuedAtISO = new Date(issuedAtMs).toISOString();
        const expiresAtISO = new Date(expiresAtMs).toISOString();

        const updateData: Partial<WhatsAppConnectionData> = {
            access: {
                access_token: newTokenData.access_token,
                token_type: newTokenData.token_type,
                expires_in: newTokenData.expires_in,
                issued_at_ms: issuedAtMs,
                issued_at_iso: issuedAtISO,
                expires_at_ms: expiresAtMs,
                expires_at_iso: expiresAtISO
            },
            updatedAt: issuedAtISO
        };

        // Add mnet sync status if provided
        if (mnetSyncSuccess !== undefined) {
            updateData.mnetSyncStatus = {
                lastSyncSuccess: mnetSyncSuccess,
                lastSyncAttempt: new Date().toISOString(),
                ...(mnetSyncSuccess && { lastSuccessfulSync: new Date().toISOString() })
            };
        }

        return updateData;
    }

    /**
     * Update seller's token in Firebase with type safety
     * @param sellerId The seller ID
     * @param newTokenData The new token data
     * @param mnetSyncSuccess Whether the mnet sync was successful
     */
    async updateSellerToken(sellerId: string, newTokenData: FacebookTokenRefreshResponse, mnetSyncSuccess?: boolean): Promise<void> {
        try {
            const updateData = this.createTokenUpdateData(newTokenData, mnetSyncSuccess);

            await this.db.collection('facebook-connects').doc(sellerId).update(updateData);
            
            logger.info(`✅ Updated token for seller ${sellerId}`, {
                expiresIn: newTokenData.expires_in,
                tokenType: newTokenData.token_type,
                mnetSyncSuccess
            });
        } catch (error) {
            const serializedError = this.serializeError(error);
            logger.error(`❌ Error updating token for seller ${sellerId}:`, {
                ...serializedError,
                sellerId
            });
            throw error;
        }
    }

    /**
     * Sync rotated token with mnet service (failure-safe)
     * @param sellerData The seller data containing mobile numbers
     * @param newTokenData The new token data
     * @returns Success status of the sync operation
     */
    async syncTokenWithMnetService(sellerData: WhatsAppConnectionData, newTokenData: FacebookTokenRefreshResponse): Promise<boolean> {
        try {
            // Extract business mobile number - try different fields in order of preference
            const bMobile = sellerData.mNetConnectedPhoneNumber;

            if (!bMobile) {
                logger.warn(`⚠️ No mnet connected phone number found for seller ${sellerData.sellerId}`);
                return false;
            }

            // Use a placeholder for customer mobile (as this seems to be business-level token sync)
            const cMobile = 'default';

            // Calculate expiry time in milliseconds
            const currentTimeMs = Date.now();
            const expiryTimeMs = currentTimeMs + (newTokenData.expires_in * 1000);

            logger.info(`🔄 Syncing token with mnet service for seller ${sellerData.sellerId}`, {
                bMobile,
                businessName: sellerData.businessName,
                expiryTimeMs,
                expiryDate: new Date(expiryTimeMs).toISOString()
            });

            const syncResult = await this.mnetApiService.syncFacebookToken(
                bMobile,
                cMobile,
                newTokenData.access_token,
                expiryTimeMs
            );

            if (syncResult.ok) {
                logger.info(`✅ Successfully synced token with mnet service for seller ${sellerData.sellerId}`);
                return true;
            } else {
                logger.warn(`⚠️ Failed to sync token with mnet service for seller ${sellerData.sellerId}: ${syncResult.err}`);
                return false;
            }

        } catch (error) {
            logger.warn(`⚠️ Error syncing token with mnet service for seller ${sellerData.sellerId}:`, error);
            // Don't throw error - this is failure-safe
            return false;
        }
    }

    /**
     * Rotate token for a specific seller with comprehensive error handling and rollback capability
     * @param sellerId The seller ID to rotate token for
     * @param revokeOldToken Whether to revoke the old token
     * @returns Result of the token rotation
     */
    async rotateSellerToken(sellerId: string, revokeOldToken: boolean = true): Promise<IServiceResponse<TokenRotationResult>> {
        let oldTokenBackup: string | null = null;
        let newTokenData: FacebookTokenRefreshResponse | null = null;
        let firebaseUpdateAttempted = false;
        let sellerData: WhatsAppConnectionData | null = null;

        try {
            logger.info(`🔄 Starting token rotation for seller: ${sellerId}`);

            // Validate sellerId
            if (!sellerId || typeof sellerId !== 'string') {
                return {
                    ok: false,
                    err: 'Invalid seller ID provided'
                };
            }

            // Get seller data from Firebase
            const doc = await this.db.collection('facebook-connects').doc(sellerId).get();
            
            if (!doc.exists) {
                return {
                    ok: false,
                    err: `Seller ${sellerId} not found in facebook-connects collection`
                };
            }

            sellerData = doc.data() as WhatsAppConnectionData;
            
            if (!sellerData.access?.access_token || sellerData.access.access_token.length < 10) {
                return {
                    ok: false,
                    err: `No valid access token found for seller ${sellerId}`
                };
            }

            // Backup old token for potential rollback
            oldTokenBackup = sellerData.access.access_token;
            
            // Refresh the token with retry logic
            newTokenData = await this.refreshSystemUserToken(oldTokenBackup!);

            // Update the token in Firebase (without sync status initially)
            await this.updateSellerToken(sellerId, newTokenData);
            firebaseUpdateAttempted = true;

            // Sync token with mnet service (failure-safe)
            const mnetSyncSuccess = await this.syncTokenWithMnetService(sellerData, newTokenData);

            // Update Firebase with sync status
            try {
                await this.updateSellerToken(sellerId, newTokenData, mnetSyncSuccess);
                logger.info(`✅ Updated sync status in Firebase for seller ${sellerId}: ${mnetSyncSuccess ? 'success' : 'failed'}`);
            } catch (syncUpdateError) {
                logger.warn(`⚠️ Failed to update sync status in Firebase for seller ${sellerId}:`, syncUpdateError);
                // Don't fail the rotation if sync status update fails
            }

            // Revoke old token if requested
            if (revokeOldToken && mnetSyncSuccess && false && oldTokenBackup && newTokenData) {
                try {
                    await this.revokeSystemUserToken(oldTokenBackup!, newTokenData!.access_token);
                    logger.info(`🗑️ Successfully revoked old token for seller ${sellerId}`);
                } catch (revokeError) {
                    logger.warn(`⚠️ Failed to revoke old token for seller ${sellerId}:`, revokeError);
                    // Don't fail the rotation if revocation fails
                }
            }

            const result: TokenRotationResult = {
                sellerId,
                success: true,
                oldToken: oldTokenBackup.substring(0, 10) + '...',
                newToken: newTokenData.access_token.substring(0, 10) + '...',
                expiresIn: newTokenData.expires_in,
                mnetSyncSuccess: mnetSyncSuccess
            };

            // Log mnet sync status
            if (mnetSyncSuccess) {
                logger.info(`✅ Successfully rotated and synced token for seller ${sellerId}`);
            } else {
                logger.info(`✅ Successfully rotated token for seller ${sellerId} (mnet sync failed but rotation completed)`);
            }

            return {
                ok: true,
                data: result
            };

        } catch (error) {
            // Safely log error without circular references
            const serializedError = this.serializeError(error);
            logger.error(`❌ Error rotating token for seller ${sellerId}:`, {
                ...serializedError,
                sellerId
            });
            
            // Attempt rollback if Firebase was updated but operation failed
            if (firebaseUpdateAttempted && oldTokenBackup && newTokenData && sellerData) {
                try {
                    logger.warn(`🔄 Attempting rollback for seller ${sellerId} due to operation failure`);
                    
                    // Create rollback data with old token using type-safe approach
                    const rollbackData: Partial<WhatsAppConnectionDataWithRollback> = {
                        access: {
                            access_token: oldTokenBackup,
                            token_type: sellerData.access?.token_type,
                            expires_in: sellerData.access?.expires_in,
                            issued_at_ms: sellerData.access?.issued_at_ms,
                            issued_at_iso: sellerData.access?.issued_at_iso,
                            expires_at_ms: sellerData.access?.expires_at_ms,
                            expires_at_iso: sellerData.access?.expires_at_iso
                        },
                        updatedAt: new Date().toISOString(),
                        rollbackInfo: {
                            reason: error instanceof Error ? error.message : 'Unknown error',
                            timestamp: new Date().toISOString(),
                            attemptedNewToken: newTokenData.access_token.substring(0, 10) + '...'
                        } as RollbackInfo
                    };

                    await this.db.collection('facebook-connects').doc(sellerId).update(rollbackData);
                    logger.info(`✅ Successfully rolled back token for seller ${sellerId}`);
                    
                    return {
                        ok: false,
                        err: `Token rotation failed but rollback successful: ${error instanceof Error ? error.message : 'Unknown error'}`
                    };
                } catch (rollbackError) {
                    // Safely log rollback error without circular references
                    const serializedRollbackError = this.serializeError(rollbackError);
                    logger.error(`❌ CRITICAL: Failed to rollback token for seller ${sellerId}:`, {
                        ...serializedRollbackError,
                        sellerId
                    });
                    return {
                        ok: false,
                        err: `Token rotation failed and rollback also failed. Manual intervention required for seller ${sellerId}. Error: ${error instanceof Error ? error.message : 'Unknown error'}`
                    };
                }
            }
            
            return {
                ok: false,
                err: error instanceof Error ? error.message : 'Unknown error during token rotation'
            };
        }
    }

    /**
     * Check if a token is expired or will expire soon
     * @param expiresIn Token expiration time in seconds
     * @param bufferDays Number of days buffer before considering token expired
     * @returns Whether token needs rotation
     */
    isTokenExpiringSoon(expiresIn: number, bufferDays: number = 15): boolean {

        return true;
        // TODO: implement this logic on token creation time
        const bufferSeconds = bufferDays * 24 * 60 * 60;
        const currentTime = Math.floor(Date.now() / 1000);
        const tokenExpiryTime = currentTime + expiresIn;
        const bufferTime = currentTime + bufferSeconds;
        
        return tokenExpiryTime <= bufferTime;
    }

    /**
     * Get sellers with tokens that need rotation
     * @param bufferDays Number of days buffer before considering token expired
     * @returns Sellers that need token rotation
     */
    async getSellersNeedingRotation(bufferDays: number = 15): Promise<WhatsAppConnectionData[]> {
        try {
            const allSellers = await this.getAllSellers();
            const sellersNeedingRotation = allSellers.filter(seller => 
                this.isTokenExpiringSoon(seller.access.expires_in, bufferDays)
            );

            logger.info(`📊 Found ${sellersNeedingRotation.length} sellers needing token rotation out of ${allSellers.length} total`);

            return sellersNeedingRotation;
        } catch (error) {
            logger.error('❌ Error getting sellers needing rotation:', error);
            throw error;
        }
    }

    /**
     * Rotate tokens for all sellers that need rotation
     * @param revokeOldTokens Whether to revoke old tokens after successful rotation
     * @returns Results of the token rotation process
     */
    async rotateAllExpiringTokens(revokeOldTokens: boolean = true): Promise<IServiceResponse<TokenRotationResult[]>> {
        try {
            logger.info('🚀 Starting Facebook token rotation for expiring tokens');
            
            const sellersNeedingRotation = await this.getSellersNeedingRotation(30);
            const results: TokenRotationResult[] = [];
            
            if (sellersNeedingRotation.length === 0) {
                logger.info('ℹ️ No sellers need token rotation at this time');
                return {
                    ok: true,
                    data: []
                };
            }

            logger.info(`🔄 Processing ${sellersNeedingRotation.length} sellers for token rotation`);

            // Process sellers sequentially to avoid rate limiting
            for await(const seller of sellersNeedingRotation) {
                const sellerId = seller.sellerId.toString();
                const result: TokenRotationResult = {
                    sellerId,
                    success: false
                };

                try {
                    logger.info(`🔄 Rotating token for seller ${sellerId} (${seller.businessName})`);

                    const rotationResult = await this.rotateSellerToken(sellerId, revokeOldTokens);
                    
                    if (rotationResult.ok && rotationResult.data) {
                        Object.assign(result, rotationResult.data);
                        logger.info(`✅ Successfully rotated token for seller ${sellerId}`);
                    } else {
                        result.error = rotationResult.err || 'Unknown error';
                        logger.error(`❌ Failed to rotate token for seller ${sellerId}: ${result.error}`);
                    }

                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                    result.error = errorMessage;
                    logger.error(`❌ Failed to rotate token for seller ${sellerId}:`, error);
                }

                results.push(result);

                // Add a small delay between requests to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            const successfulRotations = results.filter(r => r.success).length;
            const failedRotations = results.filter(r => !r.success).length;

            logger.info(`🎉 Token rotation completed: ${successfulRotations} successful, ${failedRotations} failed`);

            return {
                ok: true,
                data: results
            };

        } catch (error) {
            logger.error('❌ Error during token rotation process:', error);
            return {
                ok: false,
                err: error instanceof Error ? error.message : 'Unknown error during token rotation'
            };
        }
    }

    // ==================== CRON SERVICE METHODS ====================

    /**
     * Start the cron service
     * @param intervalDays Number of days between runs (default: 15)
     */
    startCronService(intervalDays: number = 15): void {
        if (this.cronJob) {
            logger.warn('⚠️ Cron service is already running');
            return;
        }

        // Calculate next run time
        const nextRun = new Date();
        nextRun.setDate(nextRun.getDate() + intervalDays);
        this.nextRunTime = nextRun;

        // Create cron expression for every 15 days at 2 AM
        const cronExpression = '0 2 */15 * *'; // Every 15 days at 2:00 AM
        
        logger.info('🚀 Starting Facebook Token Rotation Cron Service', {
            intervalDays,
            cronExpression,
            nextRunTime: this.nextRunTime.toISOString()
        });

        // Start the cron job
        this.cronJob = cron.schedule(cronExpression, async () => {
            await this.runScheduledTokenRotation();
        }, {
            timezone: "Asia/Kolkata" // IST timezone
        });

        // Run immediately on startup if it's been more than 15 days since last run
        // this.runTokenRotationOnStartup();
    }

    /**
     * Stop the cron service
     */
    stopCronService(): void {
        if (this.cronJob) {
            this.cronJob.stop();
            this.cronJob.destroy();
            this.cronJob = null;
            logger.info('🛑 Facebook Token Rotation Cron Service stopped');
        }
    }

    /**
     * Check if the cron service is running
     */
    isCronServiceRunning(): boolean {
        return this.cronJob !== null && this.cronJob.getStatus() === 'scheduled';
    }

    /**
     * Get service status
     */
    getCronServiceStatus(): {
        isRunning: boolean;
        lastRunTime: Date | null;
        nextRunTime: Date | null;
        isCurrentlyExecuting: boolean;
        cronExpression: string;
    } {
        return {
            isRunning: this.isCronServiceRunning(),
            lastRunTime: this.lastRunTime,
            nextRunTime: this.nextRunTime,
            isCurrentlyExecuting: this.isRunning,
            cronExpression: '0 2 */15 * *' // Every 15 days at 2:00 AM
        };
    }

    /**
     * Run token rotation on startup if needed
     */
    private async runTokenRotationOnStartup(): Promise<void> {
        try {
            logger.info('🔍 Checking if token rotation is needed on startup...');
            
            const sellersNeedingRotation = await this.getSellersNeedingRotation(15);
            
            if (sellersNeedingRotation.length > 0) {
                logger.info(`🔄 Found ${sellersNeedingRotation.length} sellers needing rotation on startup`);
                await this.runScheduledTokenRotation();
            } else {
                logger.info('✅ No sellers need token rotation on startup');
            }
        } catch (error) {
            logger.error('❌ Error during startup token rotation check:', error);
        }
    }

    /**
     * Execute the scheduled token rotation process
     */
    private async runScheduledTokenRotation(): Promise<void> {
        if (this.isRunning) {
            logger.warn('⚠️ Token rotation is already running, skipping this execution');
            return;
        }

        this.isRunning = true;
        const startTime = Date.now();

        try {
            logger.info('🔄 Starting scheduled Facebook token rotation');

            const result = await this.rotateAllExpiringTokens(true);

            if (!result.ok) {
                throw new Error(result.err || 'Token rotation failed');
            }

            const successfulRotations = result.data?.filter(r => r.success).length || 0;
            const failedRotations = result.data?.filter(r => !r.success).length || 0;
            const totalProcessed = result.data?.length || 0;

            const endTime = Date.now();
            const duration = endTime - startTime;

            this.lastRunTime = new Date();

            logger.info('🎉 Scheduled token rotation completed', {
                totalProcessed,
                successful: successfulRotations,
                failed: failedRotations,
                durationMs: duration,
                durationSeconds: Math.round(duration / 1000),
                lastRunTime: this.lastRunTime.toISOString()
            });

            // Log failed rotations for monitoring
            if (failedRotations > 0) {
                const failedResults = result.data?.filter(r => !r.success) || [];
                logger.warn('⚠️ Failed token rotations:', failedResults);
            }

        } catch (error) {
            this.lastRunTime = new Date();
            const endTime = Date.now();
            const duration = endTime - startTime;

            logger.error('❌ Scheduled token rotation failed', {
                error: error instanceof Error ? error.message : 'Unknown error',
                durationMs: duration,
                durationSeconds: Math.round(duration / 1000),
                lastRunTime: this.lastRunTime.toISOString()
            });

            // Don't throw the error to prevent the cron from stopping
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Manually trigger token rotation
     */
    async triggerManualRotation(): Promise<{
        success: boolean;
        message: string;
        data?: any;
    }> {
        if (this.isRunning) {
            return {
                success: false,
                message: 'Token rotation is already running'
            };
        }

        try {
            logger.info('🔄 Manual token rotation triggered');
            await this.runScheduledTokenRotation();
            
            return {
                success: true,
                message: 'Manual token rotation completed successfully'
            };
        } catch (error) {
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error during manual rotation'
            };
        }
    }

    /**
     * Get health status of the service
     */
    async getHealthStatus(): Promise<{
        service: string;
        status: 'healthy' | 'unhealthy';
        cronStatus: {
            isRunning: boolean;
            lastRunTime: string | null;
            nextRunTime: string | null;
            isCurrentlyExecuting: boolean;
            cronExpression: string;
        };
        tokenRotationStatus: {
            totalSellers: number;
            sellersNeedingRotation: number;
            mnetSyncStatus: {
                sellersWithSyncStatus: number;
                sellersWithSuccessfulSync: number;
                sellersWithFailedSync: number;
            };
        };
        environment: string;
    }> {
        try {
            const sellers = await this.getAllSellers();
            const sellersNeedingRotation = await this.getSellersNeedingRotation(15);
            const status = this.getCronServiceStatus();

            // Calculate mnet sync statistics
            const sellersWithSyncStatus = sellers.filter(s => s.mnetSyncStatus).length;
            const sellersWithSuccessfulSync = sellers.filter(s => s.mnetSyncStatus?.lastSyncSuccess === true).length;
            const sellersWithFailedSync = sellers.filter(s => s.mnetSyncStatus?.lastSyncSuccess === false).length;

            return {
                service: 'Facebook Token Rotation Service',
                status: this.isCronServiceRunning() ? 'healthy' : 'unhealthy',
                cronStatus: {
                    isRunning: status.isRunning,
                    lastRunTime: status.lastRunTime?.toISOString() || null,
                    nextRunTime: status.nextRunTime?.toISOString() || null,
                    isCurrentlyExecuting: status.isCurrentlyExecuting,
                    cronExpression: status.cronExpression
                },
                tokenRotationStatus: {
                    totalSellers: sellers.length,
                    sellersNeedingRotation: sellersNeedingRotation.length,
                    mnetSyncStatus: {
                        sellersWithSyncStatus,
                        sellersWithSuccessfulSync,
                        sellersWithFailedSync
                    }
                },
                environment: process.env.NODE_ENV || 'development'
            };
        } catch (error) {
            return {
                service: 'Facebook Token Rotation Service',
                status: 'unhealthy',
                cronStatus: {
                    isRunning: this.isCronServiceRunning(),
                    lastRunTime: this.lastRunTime?.toISOString() || null,
                    nextRunTime: this.nextRunTime?.toISOString() || null,
                    isCurrentlyExecuting: this.isRunning,
                    cronExpression: '0 2 */15 * *'
                },
                tokenRotationStatus: {
                    totalSellers: 0,
                    sellersNeedingRotation: 0,
                    mnetSyncStatus: {
                        sellersWithSyncStatus: 0,
                        sellersWithSuccessfulSync: 0,
                        sellersWithFailedSync: 0
                    }
                },
                environment: process.env.NODE_ENV || 'development'
            };
        }
    }
}

// Create a singleton instance
const facebookTokenRotationService = new FacebookTokenRotationService();

export default facebookTokenRotationService; 