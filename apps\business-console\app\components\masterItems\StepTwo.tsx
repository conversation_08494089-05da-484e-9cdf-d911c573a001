import * as React from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import type { FormState } from "~/types/home/<USER>";
import type { ComboboxItem } from "../ui/searchableCombobox";
import { useFetcher } from "@remix-run/react";

interface StepTwoProps {
  formData: FormState;
  onChange: (data: Partial<FormState>) => void;
  errors?: Record<string, string[] | undefined>;
  mode: 'create' | 'edit' | 'duplicate';
  renderRETInput: (field: string) => boolean;
}

interface CategoryLoaderData {
  categories: Array<{
    id: number;
    name: string;
    picture?: string;
    picturex?: string;
    picturexx?: string;
    level?: number;
    totalItems?: number;
    parentCategories?: Array<{
      id: number;
      name: string;
      picture?: string;
      picturex?: string;
      picturexx?: string;
      level?: number;
      parentCategories?: number[];
      totalItems?: number;
      myItems?: number;
    }>;
  }>;
  categoryPage: number;
}

interface CategoryItem extends ComboboxItem {
  value: string;
  label: string;
  numericId: number;
}

interface ImageItem {
  id: number;
  url: string;
  sequence: number;
  isDefault: boolean;
}

function StepTwo({ formData, onChange, errors, renderRETInput }: StepTwoProps) {
  // Separate state for tags and categories input
  const [tagInput, setTagInput] = React.useState("");
  const [categoryInput, setCategoryInput] = React.useState("");

  // Category state
  const [categoryItems, setCategoryItems] = React.useState<CategoryItem[]>([]);
  const [loadingCategories, setLoadingCategories] = React.useState(false);
  const [categoryPage, setCategoryPage] = React.useState(1);
  const [hasMoreCategories, setHasMoreCategories] = React.useState(true);
  const fetcher = useFetcher<CategoryLoaderData>();

  // Handle image functions
  const [uploadingImage, setUploadingImage] = React.useState(false);
  const [uploadError, setUploadError] = React.useState<string | null>(null);
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const uploadFetcher = useFetcher<{ success: boolean; fileUrl: string; error?: string }>();

  // Search categories with server API
  const searchCategories = async (query: string) => {
    setLoadingCategories(true);
    fetcher.load(`/home/<USER>
  };

  // Load more categories for infinite scroll
  const loadMoreCategories = async () => {
    if (!hasMoreCategories || loadingCategories) return;
    const nextPage = categoryPage + 1;
    fetcher.load(`/home/<USER>
  };

  // Handle fetcher data updates
  React.useEffect(() => {
    if (fetcher.data?.categories) {
      const items: CategoryItem[] = fetcher.data.categories.map(category => ({
        value: String(category.id),
        label: category.name,
        numericId: category.id
      }));

      if (fetcher.data.categoryPage === 0) {
        setCategoryItems(items);
      } else {
        setCategoryItems(prev => [...prev, ...items]);
      }

      setCategoryPage(fetcher.data.categoryPage);
      setHasMoreCategories(items.length === 20);
      setLoadingCategories(false);
    }
  }, [fetcher.data]);

  // Handle file select
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Reset error state
    setUploadError(null);

    // Validate file size (e.g., 5MB limit)
    const MAX_FILE_SIZE = 500 * 1024 * 1024; // 5MB
    if (file.size > MAX_FILE_SIZE) {
      setUploadError("File size exceeds 5MB limit");
      return;
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed");
      return;
    }

    setSelectedFile(file);
  };

  const handleUpload = () => {
    if (!selectedFile) return;
    setUploadingImage(true);
    const formData = new FormData();
    formData.append("_action", "uploadImage");
    formData.append("file", selectedFile, selectedFile.name);
    uploadFetcher.submit(formData, {
      method: "post",
      encType: "multipart/form-data"
    });
  };


  const openFilePicker = () => {
    fileInputRef.current?.click();
  };

  React.useEffect(() => {
    if (uploadFetcher.data) {
      if (uploadFetcher.data.error) {
        setUploadError(uploadFetcher.data.error);
        setUploadingImage(false);
      } else if (uploadFetcher.data.fileUrl) {
        const newImage = {
          id: Date.now(),
          url: uploadFetcher.data.fileUrl,
          sequence: (formData.images || []).length + 1,
          isDefault: false,
        };
        onChange({
          images: [...(formData.images || []), newImage]
        });
        setUploadingImage(false);
        setUploadError(null);
        setSelectedFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    }
  }, [uploadFetcher.data]);

  const handleRemoveImage = (id: number, e?: React.MouseEvent) => {
    // Prevent event propagation and default form submission
    e?.preventDefault();
    e?.stopPropagation();

    onChange({
      images: (formData.images || []).filter((img: ImageItem) => img.id !== id)
    });
  };

  const handleSequenceChange = (id: number, newSequence: number) => {
    const currentImages = [...(formData.images || [])];

    // Find the target image and default image
    const targetImage = currentImages.find(img => img.id === id);
    const defaultImage = currentImages.find(img => img.isDefault);

    if (!targetImage || targetImage.isDefault) {
      return; // Don't modify default image sequence
    }

    // Ensure sequence is at least 2 for non-default images
    const validSequence = Math.max(2, newSequence);

    // Update sequences for all images
    const updatedImages = currentImages.map(img => {
      // Keep default image at sequence 1
      if (img.isDefault) {
        return { ...img, sequence: 1 };
      }

      // Update target image sequence
      if (img.id === id) {
        return { ...img, sequence: validSequence };
      }

      // Adjust other images' sequences to maintain order
      if (img.sequence >= validSequence && img.id !== id) {
        return { ...img, sequence: img.sequence + 1 };
      }

      return img;
    });

    // Sort images: default first, then by sequence
    const sortedImages = updatedImages.sort((a, b) => {
      if (a.isDefault) return -1;
      if (b.isDefault) return 1;
      return a.sequence - b.sequence;
    });

    onChange({
      images: sortedImages
    });
  };

  const handleSetDefault = (id: number, e?: React.MouseEvent) => {
    // Prevent event propagation and default form submission
    e?.preventDefault();
    e?.stopPropagation();

    const currentImages = [...(formData.images || [])];

    // Update all images: set new default and adjust sequences
    const updatedImages = currentImages.map(img => {
      if (img.id === id) {
        return { ...img, isDefault: true, sequence: 1 };
      }
      // If this was previously the default image, give it the next available sequence
      if (img.isDefault) {
        return { ...img, isDefault: false, sequence: 2 };
      }
      // For all other images, increment sequence if it's greater than or equal to 2
      return {
        ...img,
        isDefault: false,
        sequence: img.sequence >= 2 ? img.sequence + 1 : img.sequence
      };
    });

    // Sort images by sequence, ensuring default image stays first
    const sortedImages = updatedImages.sort((a, b) => {
      if (a.isDefault) return -1;
      if (b.isDefault) return 1;
      return a.sequence - b.sequence;
    });

    onChange({
      images: sortedImages
    });
  };

  // Handle translation changes
  const handleTranslationChange = (key: string, value: string) => {
    onChange({
      translations: {
        ...(formData.translations || {}),
        [key]: value,
      }
    });
  };

  // Handle tag functions
  const handleAddTag = () => {
    if (!tagInput.trim()) return;

    // Check for duplicates (case-insensitive)
    const normalizedTag = tagInput.trim().toLowerCase();
    const existingTags = formData.searchTags || [];
    const isDuplicate = existingTags.some((tag: string) => tag.toLowerCase() === normalizedTag);

    if (!isDuplicate) {
      onChange({
        searchTags: [...existingTags, tagInput.trim()]
      });
    }
    setTagInput("");
  };

  const handleRemoveTag = (tag: string) => {
    onChange({
      searchTags: (formData.searchTags || []).filter((t: string) => t !== tag)
    });
  };

  // Handle category functions
  const handleAddCategory = (categoryValue: string) => {
    const existingCategories = formData.assignedCategories || [];
    const categoryItem = categoryItems.find(item => item.value === categoryValue);
    if (!categoryItem) return;

    const categoryId = categoryItem.numericId;
    if (!existingCategories.some(id => id === categoryId)) {
      onChange({
        assignedCategories: [...existingCategories, categoryId],
        // Also update the categories array to maintain name mapping
        categories: [
          ...(formData.categories || []),
          { id: categoryId, name: categoryItem.label }
        ]
      });
    }
    setCategoryInput("");
  };

  // Handle remove category
  const handleRemoveCategory = (categoryId: number) => {
    onChange({
      assignedCategories: (formData.assignedCategories || []).filter(id => id !== categoryId),
      categories: (formData.categories || []).filter(cat => cat.id !== categoryId)
    });
  };

  // Get category name by ID
  const getCategoryName = (categoryId: number) => {
    // First check in the current categoryItems (new categories)
    const newCategory = categoryItems.find(item => item.numericId === categoryId);
    if (newCategory) return newCategory.label;

    // Then check in the existing categories from formData
    const existingCategory = formData.categories?.find(cat => cat.id === categoryId);
    if (existingCategory) return existingCategory.name;

    // If no name found, return the ID
    return categoryId;
  };

  return (
    <div className="grid gap-4 p-4 md:p-6">
      {/* Images section */}
      <div className="col-span-full">
        <Label>Item Images {formData.itemConfig?.ondcDomain === "RET10" ? `(Required: at least 1)` : ""}</Label>
        <div className="flex gap-2 items-center">
          <Input
            type="text"
            readOnly
            value={selectedFile?.name || "No file selected"}
            className="flex-grow"
            onClick={openFilePicker}
            style={{ cursor: 'pointer' }}
          />
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />
          <Button
            type="button"
            onClick={selectedFile ? handleUpload : openFilePicker}
            disabled={uploadingImage}
          >
            {uploadingImage ? (
              <span className="flex items-center gap-2">
                <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                Uploading...
              </span>
            ) : selectedFile ? (
              'Upload'
            ) : (
              'Add Image'
            )}
          </Button>
        </div>
        {uploadError && (
          <p className="text-red-500 mt-1 text-sm">{uploadError}</p>
        )}
        {errors?.images?.[0] && (
          <p className="text-red-500 mt-1 text-sm">{errors.images[0]}</p>
        )}
        <div className="mt-2 space-y-2">
          {(formData.images || [])
            .sort((a, b) => {
              if (a.isDefault) return -1;
              if (b.isDefault) return 1;
              return a.sequence - b.sequence;
            })
            .map((img) => (
              <div key={img.id} className="flex items-center justify-between border p-2">
                <div className="flex items-center gap-2">
                  <span>Seq:</span>
                  {img.isDefault ? (
                    <span className="w-20 px-3 py-2">1</span>
                  ) : (
                    <Input
                      type="number"
                      min="2"
                      value={img.sequence}
                      onChange={(e) => handleSequenceChange(img.id, parseInt(e.target.value))}
                      className="w-20"
                      onBlur={(e) => {
                        // Ensure a valid number on blur
                        if (!e.target.value || parseInt(e.target.value) < 2) {
                          handleSequenceChange(img.id, 2);
                        }
                      }}
                      style={{
                        WebkitAppearance: 'none',
                        MozAppearance: 'textfield'
                      }}
                    />
                  )}
                </div>
                <img src={img.url} alt="" className="w-12 h-12 object-cover" />
                <div className="flex items-center gap-2">
                  <Button variant="destructive" size="sm" onClick={(e) => handleRemoveImage(img.id, e)}>
                    Remove
                  </Button>
                  <Button
                    variant={img.isDefault ? "default" : "outline"}
                    size="sm"
                    onClick={(e) => handleSetDefault(img.id, e)}
                  >
                    {img.isDefault ? "Default" : "Set Default"}
                  </Button>
                </div>
              </div>
            ))}
        </div>
      </div>

      {/* Translations */}
      {renderRETInput("translations") && <div className="grid md:grid-cols-2 gap-2">
        <div>
          <Label>Kannada Name</Label>
          <Input
            value={formData.translations?.kanadaName || ""}
            onChange={(e) => handleTranslationChange("kanadaName", e.target.value)}
          />
        </div>
        <div>
          <Label>Hindi Name</Label>
          <Input
            value={formData.translations?.hindiName || ""}
            onChange={(e) => handleTranslationChange("hindiName", e.target.value)}
          />
        </div>
        <div>
          <Label>Tamil Name</Label>
          <Input
            value={formData.translations?.tamilName || ""}
            onChange={(e) => handleTranslationChange("tamilName", e.target.value)}
          />
        </div>
        <div>
          <Label>Telugu Name</Label>
          <Input
            value={formData.translations?.teluguName || ""}
            onChange={(e) => handleTranslationChange("teluguName", e.target.value)}
          />
        </div>
        <div>
          <Label>Bengali Name</Label>
          <Input
            value={formData.translations?.bengaliName || ""}
            onChange={(e) => handleTranslationChange("bengaliName", e.target.value)}
          />
        </div>
        <div>
          <Label>Malayalam Name</Label>
          <Input
            value={formData.translations?.malyalumName || ""}
            onChange={(e) => handleTranslationChange("malyalumName", e.target.value)}
          />
        </div>
        <div>
          <Label>Marathi Name</Label>
          <Input
            value={formData.translations?.marathiName || ""}
            onChange={(e) => handleTranslationChange("marathiName", e.target.value)}
          />
        </div>
        <div>
          <Label>Gujarati Name</Label>
          <Input
            value={formData.translations?.gujaratiName || ""}
            onChange={(e) => handleTranslationChange("gujaratiName", e.target.value)}
          />
        </div>
        <div>
          <Label>Assami Name</Label>
          <Input
            value={formData.translations?.assamiName || ""}
            onChange={(e) => handleTranslationChange("assamiName", e.target.value)}
          />
        </div>
      </div>}

      {/* Assigned Categories */}
      <div className="col-span-full">
        <Label>Assigned Categories (Required)</Label>
        <div className="flex gap-2 items-center">
          <Input
            placeholder="Search categories"
            value={categoryInput}
            onChange={(e) => {
              setCategoryInput(e.target.value);
              searchCategories(e.target.value);
            }}
          />
          <Button
            type="button"
            onClick={() => {
              if (categoryItems.length > 0) {
                handleAddCategory(categoryItems[0].value);
              }
            }}
          >
            Add Category
          </Button>
        </div>
        {/* {(!formData.assignedCategories || formData.assignedCategories.length < 1) && (
          <p className="text-red-500">At least one category is required.</p>
        )} */}
        <div className="flex gap-2 mt-2 flex-wrap">
          {(formData.assignedCategories || []).map((categoryId) => {
            const categoryName = getCategoryName(categoryId);
            return (
              <div key={categoryId} className="inline-flex items-center space-x-1 bg-gray-200 p-1 rounded">
                <span>{categoryName} - {categoryId}</span>
                <button onClick={() => handleRemoveCategory(categoryId)}>×</button>
              </div>
            );
          })}
        </div>
        {errors?.assignedCategories?.[0] && (
          <p className="text-red-500">{errors.assignedCategories[0]}</p>
        )}
        {loadingCategories && <p>Loading categories...</p>}

        {categoryItems.length > 0 && categoryInput && (
          <div className="mt-2 border rounded-md p-2">
            {categoryItems.map(category => {
              // Don't show already assigned categories
              if (formData.assignedCategories?.some(id => id === category.numericId)) {
                return null;
              }
              return (
                <button
                  key={category.value}
                  className="w-full text-left cursor-pointer hover:bg-gray-100 p-1"
                  onClick={() => handleAddCategory(category.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      handleAddCategory(category.value);
                    }
                  }}
                >
                  {category.label}
                </button>
              );
            })}
            {hasMoreCategories && (
              <Button
                type="button"
                variant="ghost"
                className="w-full mt-2"
                onClick={loadMoreCategories}
              >
                Load More
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Search Tags */}
      <div className="col-span-full">
        <Label>Search Tags (Required)</Label>
        <div className="flex gap-2 items-center">
          <Input
            placeholder="Enter a tag"
            value={tagInput}
            onChange={(e) => setTagInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleAddTag();
              }
            }}
          />
          <Button type="button" onClick={handleAddTag}>
            Add Tag
          </Button>
        </div>
        {/* {(formData.searchTags || []).length < 1 && (
          <p className="text-red-500">At least one tag is required for submission.</p>
        )} */}
        {errors?.searchTags?.[0] && (
          <p className="text-red-500">{errors.searchTags[0]}</p>
        )}
        <div className="flex gap-2 mt-2 flex-wrap">
          {(formData.searchTags || []).map((tag) => (
            <div key={tag} className="inline-flex items-center space-x-1 bg-gray-200 p-1 rounded">
              <span>{tag}</span>
              <button onClick={() => handleRemoveTag(tag)}>×</button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default React.memo(StepTwo);