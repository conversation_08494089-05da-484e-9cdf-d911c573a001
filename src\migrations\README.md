# DynamoDB Migration System 🗄️

This directory contains versioned DynamoDB migrations for the webhook logging system, managed with a professional migration runner.

## 🏗 Migration Structure

```
src/migrations/
├── index.ts                        # Migration runner & CLI
├── 001-create-webhook-log-table.ts # Initial webhook table creation
├── README.md                       # This documentation
└── future-migrations...            # Additional migrations as needed
```

## 🚀 Running Migrations

### Run All Migrations
```bash
npm run migrate run
```

### Run Up to Specific Migration
```bash
npm run migrate run 001
```

### Check Migration Status
```bash
npm run migrate status
```

### List Available Migrations
```bash
npm run migrate list
```

### Validate Migrations
```bash
npm run migrate validate
```

### Rollback Migration (if needed)
```bash
npm run migrate rollback 001
```

## 📋 Available Migrations

### Migration 001: Create WebhookLog Table V2
**File**: `001-create-webhook-log-table.ts`  
**Purpose**: Creates the main webhook_logs table with enhanced features

**Features:**
- ✅ IST timezone support with ISO format fields
- ✅ MessageCode classification (GREETING, ORDER_STATUS)
- ✅ 5 Global Secondary Indexes for efficient querying
- ✅ PAY_PER_REQUEST billing mode
- ✅ Optimized for analytics and real-time queries

**Table Structure:**
```
Primary Key: webhookId (HASH) + timestamp (RANGE)

Indexes:
├── BusinessNumberIndex (businessNumber, timestamp)
├── CustomerNumberIndex (customerNumber, timestamp) 
├── MessageTypeIndex (messageType, timestamp)
├── StatusIndex (status, timestamp)
└── MessageCodeIndex (messageCode, timestamp)

IST Fields:
├── timestampISO (String)
├── receivedAtISO (String)
├── processedAtISO (String)
└── lastUpdatedISO (String)
```

## 🎯 Migration Features

### Version Control
- ✅ Sequential numbering (001, 002, 003...)
- ✅ Metadata tracking (name, description, version, dependencies)
- ✅ Creation timestamps
- ✅ Dependency management

### Execution Safety
- ✅ Idempotent operations (safe to run multiple times)
- ✅ Table existence checking
- ✅ Progress tracking with detailed logging
- ✅ Automatic validation after execution
- ✅ Timeout handling (10 minutes max)

### Professional Logging
```
🚀 Migration 001: Create WebhookLog Table V2
📋 Creating table: webhook_logs_uat
✨ Features:
   - Primary keys: webhookId (HASH), timestamp (RANGE)
   - IST timezone fields: timestampISO, receivedAtISO, processedAtISO, lastUpdatedISO
   - MessageCode classification: GREETING, ORDER_STATUS
   - 5 Global Secondary Indexes for efficient querying
   - PAY_PER_REQUEST billing mode

⏳ Starting table creation...
✅ Table creation initiated successfully!
⌛ Waiting for table and indexes to become active...
   Attempt 1: Table Status = CREATING
   Index BusinessNumberIndex: CREATING
   [... progress updates ...]

🎉 Migration completed successfully!
✅ Migration 001 completed successfully (45230ms)
```

## 🔧 Creating New Migrations

### 1. Create Migration File
```bash
# Follow naming convention: XXX-description.ts
touch src/migrations/002-add-user-analytics-table.ts
```

### 2. Migration Template
```typescript
/**
 * Migration 002: Add User Analytics Table
 * 
 * Description: Creates a table for tracking user behavior analytics
 * Version: 1.0.0
 * Dependencies: ['001'] // Optional: depends on migration 001
 */

import { DynamoDBClient, CreateTableCommand } from '@aws-sdk/client-dynamodb';

export const migrationInfo = {
    id: '002',
    name: 'Add User Analytics Table',
    description: 'Creates a table for tracking user behavior analytics',
    version: '1.0.0',
    dependencies: ['001'], // This migration requires 001 to be completed
    createdAt: '2023-12-31T00:00:00.000Z'
};

export async function up(): Promise<void> {
    const client = new DynamoDBClient({});
    
    try {
        console.log(`🚀 Migration ${migrationInfo.id}: ${migrationInfo.name}`);
        
        // Your migration logic here
        // Create tables, indexes, etc.
        
        console.log('✅ Migration completed successfully');
        
    } catch (error) {
        console.error(`❌ Migration ${migrationInfo.id} failed:`, error);
        throw error;
    } finally {
        client.destroy();
    }
}

export async function down(): Promise<void> {
    const client = new DynamoDBClient({});
    
    try {
        console.log(`🔄 Rolling back migration ${migrationInfo.id}`);
        
        // Your rollback logic here
        // Drop tables, remove indexes, etc.
        
        console.log('✅ Rollback completed successfully');
        
    } catch (error) {
        console.error(`❌ Rollback failed:`, error);
        throw error;
    } finally {
        client.destroy();
    }
}

export async function validate(): Promise<boolean> {
    const client = new DynamoDBClient({});
    
    try {
        console.log(`🔍 Validating migration ${migrationInfo.id}...`);
        
        // Your validation logic here
        // Check if tables exist, indexes are active, etc.
        
        console.log('✅ Migration validation passed');
        return true;
        
    } catch (error) {
        console.log(`❌ Migration validation failed:`, error);
        return false;
    } finally {
        client.destroy();
    }
}
```

### 3. Update Package Scripts (if needed)
The migration runner automatically discovers new migration files, so no package.json updates are required.

## 🛠 Prerequisites

### 1. AWS Configuration
```bash
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret
export AWS_REGION=us-east-1
```

### 2. Environment Variables
```bash
export NODE_ENV=uat          # or production
```

### 3. Permissions
Ensure your AWS credentials have DynamoDB permissions:
- `dynamodb:CreateTable`
- `dynamodb:DescribeTable`
- `dynamodb:UpdateTable`
- `dynamodb:DeleteTable`

## 📊 Migration Commands

### Status Check
```bash
npm run migrate status
```
Output:
```
📊 Migration Status:

   001: Create WebhookLog Table V2 - ✅ Applied
   002: Add User Analytics Table - ⏳ Pending
```

### List All Migrations
```bash
npm run migrate list
```
Output:
```
📋 Available Migrations:

================================================================================
ID  | Name                           | Version | Created
================================================================================
001 | Create WebhookLog Table V2     | 2.0.0   | 2023-12-30
002 | Add User Analytics Table       | 1.0.0   | 2023-12-31
================================================================================
Total: 2 migration(s)
```

### Validation
```bash
npm run migrate validate
```
Output:
```
🔍 Validating all migrations...

✅ Migration 001: Valid
❌ Migration 002: Invalid

📊 Validation Summary: Some migrations invalid
```

## 🔄 Rollback Strategy

### Safe Rollback
Most DynamoDB operations are not easily reversible:
- **Table Creation**: Can be rolled back by deleting the table
- **Index Addition**: Can be rolled back by removing the index
- **Data Migration**: Requires careful planning and backup

### Manual Rollback
For complex rollbacks, the migration will provide instructions:
```
⚠️  Manual rollback required: Delete table from AWS Console
   Table to delete: webhook_logs_uat
```

## 🐛 Troubleshooting

### Common Issues

**AWS Permissions Error**
```bash
# Check your AWS configuration
aws configure list
aws sts get-caller-identity
```

**Table Already Exists**
```bash
# Check migration status
npm run migrate status

# Validate existing tables
npm run migrate validate
```

**Timeout During Creation**
```bash
# Tables with multiple GSIs can take 5-15 minutes
# Check AWS Console for actual progress
```

**Migration File Not Found**
```bash
# Ensure file follows naming convention: XXX-description.ts
# Check file permissions and syntax
```

### Debug Mode
Run individual migrations for debugging:
```typescript
import { up, validate } from './src/migrations/001-create-webhook-log-table.js';

await up();       // Run the migration
await validate(); // Check if successful
```

## 🔐 Best Practices

### Migration Design
- ✅ Make migrations idempotent (safe to run multiple times)
- ✅ Include detailed logging and progress updates
- ✅ Add proper error handling and cleanup
- ✅ Test migrations in UAT before production
- ✅ Keep migrations focused (one logical change per migration)

### Naming Convention
- ✅ Use sequential numbering: `001`, `002`, `003`
- ✅ Descriptive names: `create-webhook-log-table`
- ✅ Consistent format: `XXX-description-with-hyphens.ts`

### Version Control
- ✅ Never modify existing migrations once applied to production
- ✅ Create new migrations for schema changes
- ✅ Document dependencies between migrations
- ✅ Track migration metadata (version, description, dates)

## 🎯 Production Deployment

### Pre-Deployment Checklist
- [ ] Test migrations in UAT environment
- [ ] Validate all existing data compatibility
- [ ] Ensure AWS permissions are configured
- [ ] Document rollback plan for critical changes
- [ ] Schedule maintenance window if needed

### Deployment Process
```bash
# 1. Deploy application code
git pull origin main
npm install
npm run build

# 2. Run migrations
npm run migrate status      # Check current state
npm run migrate run         # Execute pending migrations
npm run migrate validate    # Confirm success

# 3. Start application
npm start
```

## 📈 Future Enhancements

Planned migration system improvements:
- [ ] Persistent migration state tracking in DynamoDB
- [ ] Automatic backup before destructive operations
- [ ] Migration dependency validation
- [ ] Parallel migration execution for independent changes
- [ ] Integration with CI/CD pipelines

---

**🚀 Ready to migrate? Start with:** `npm run migrate status` 