# WhatsApp Status & Error Tracking Migration 📊

This document outlines Migration 002, which adds WhatsApp delivery status tracking and error monitoring capabilities to the existing webhook logging system.

## 🎯 Overview

**Migration ID**: 002  
**Name**: Add WhatsApp Status & Error Tracking  
**Version**: 2.1.0  
**Dependencies**: 001 (Create WebhookLog Table)  

## ✨ Features Added

### 1. WhatsApp Status Tracking
- **Field**: `whatsappStatus` (optional)
- **Values**: `sent`, `delivered`, `read`, `failed`
- **Purpose**: Track message delivery status from WhatsApp webhooks
- **Index**: New `WhatsAppStatusIndex` for efficient queries

### 2. Error Notification Handling
- **Field**: `whatsappErrors` (optional array)
- **Structure**: `{ code: number, title: string, message: string }[]`
- **Purpose**: Capture detailed error information from WhatsApp error webhooks

### 3. New Analytics Capabilities
- Delivery rate calculations
- Error breakdown by code
- System health monitoring with WhatsApp metrics

## 🚀 Running the Migration

### Prerequisites
- Existing `webhook_logs_{env}` table must exist (Migration 001)
- AWS credentials configured
- DynamoDB access permissions

### Apply Migration
```bash
# Apply the migration
npm run migrate:whatsapp-status

# Or run directly
npx tsx src/migrations/002-add-whatsapp-status-tracking.ts up
```

### Validate Migration
```bash
# Validate migration was applied correctly
npm run migrate:validate-whatsapp-status

# Or run directly
npx tsx src/migrations/002-add-whatsapp-status-tracking.ts validate
```

### Rollback (if needed)
```bash
# Remove WhatsApp status tracking capabilities
npm run migrate:rollback-whatsapp-status

# Or run directly
npx tsx src/migrations/002-add-whatsapp-status-tracking.ts down
```

## 📋 Migration Details

### What Gets Added
1. **New Index**: `WhatsAppStatusIndex`
   - Partition Key: `whatsappStatus` (String)
   - Sort Key: `timestamp` (Number)
   - Projection: ALL

2. **New Attribute Definition**: `whatsappStatus` (String)

### What Stays the Same
- ✅ All existing data remains unchanged
- ✅ All existing indexes remain intact
- ✅ All existing functionality continues to work
- ✅ No downtime required

### Backward Compatibility
- New fields are optional - existing webhooks continue to work
- Repository methods gracefully handle missing index
- Service automatically populates new fields for new webhooks

## 🔧 Implementation Details

### Database Schema Changes
```typescript
// New fields in WebhookLog interface
interface WebhookLog {
    // ... existing fields ...
    
    // 🆕 New fields
    whatsappStatus?: WhatsAppMessageStatus;  // sent, delivered, read, failed
    whatsappErrors?: WhatsAppError[];        // Error details array
}
```

### New API Methods
```typescript
// Repository methods
await webhookRepo.getLogsByWhatsAppStatus(WhatsAppMessageStatus.FAILED);

// Service methods  
await webhookService.getDeliveryStats('businessNumber', 24);
await webhookService.getSystemHealth();
await webhookService.getErrorAnalysis('businessNumber');
```

### Webhook Types Handled
1. **Status Update Webhooks**: Logs with `MessageType.STATUS_UPDATE`
2. **Error Notification Webhooks**: Logs with `MessageType.ERROR_NOTIFICATION`  
3. **Regular Message Webhooks**: Enhanced with status fields

## 📊 Usage Examples

### Query Failed Deliveries
```typescript
const failedLogs = await webhookService.getWebhooksByWhatsAppStatus(
    WhatsAppMessageStatus.FAILED, 
    { limit: 50 }
);
```

### Get Delivery Statistics
```typescript
const stats = await webhookService.getDeliveryStats('businessNumber');
console.log(`Delivery Rate: ${stats.deliveryRate}%`);
console.log(`Read Rate: ${stats.readRate}%`);
```

### Monitor System Health
```typescript
const health = await webhookService.getSystemHealth();
console.log(`Status: ${health.status}`);
console.log(`WhatsApp Error Rate: ${health.metrics.whatsappErrorRate}%`);
```

### Error Analysis
```typescript
const analysis = await webhookService.getErrorAnalysis('businessNumber');
analysis.errorsByCode.forEach(error => {
    console.log(`Code ${error.code}: ${error.title} (${error.count} times)`);
});
```

## 🧪 Testing

The migration includes comprehensive test coverage:

```bash
# Run all webhook tests (includes status tracking tests)
npm run test:webhook

# Specific test functions added:
# - testStatusUpdateLogging()
# - testErrorNotificationLogging() 
# - testDeliveryAnalytics()
```

## 🛡️ Safety Features

### Migration Safety
- ✅ **Idempotent**: Can be run multiple times safely
- ✅ **Validation**: Checks if index already exists
- ✅ **Error Handling**: Graceful fallbacks if index not available
- ✅ **Rollback Support**: Can be safely reversed

### Production Safety
- ✅ **Zero Downtime**: No service interruption
- ✅ **Backward Compatible**: Existing code continues to work
- ✅ **Optional Fields**: New fields don't break existing data
- ✅ **Graceful Degradation**: Works with or without new index

## 🔍 Monitoring

### Migration Progress
The migration provides detailed progress information:
```
🚀 Migration 002: Add WhatsApp Status & Error Tracking
🔧 Updating table: webhook_logs_uat
✨ New Features:
   - WhatsApp status tracking: sent, delivered, read, failed
   - Error notification handling with detailed codes
   - WhatsAppStatusIndex for efficient status queries
   - Full backward compatibility with existing data

⏳ Creating WhatsAppStatusIndex...
   Attempt 1: WhatsAppStatusIndex Status = CREATING
   Attempt 2: WhatsAppStatusIndex Status = ACTIVE

🎉 Migration completed successfully!
```

### Post-Migration Validation
```bash
✅ Migration 002 validation passed
   - WhatsAppStatusIndex is ACTIVE
   - Table has 6 total indexes
```

## 🚨 Troubleshooting

### Common Issues

**Index Already Exists**
```
⚠️  WhatsAppStatusIndex already exists!
⏭️  Skipping migration - index already exists.
```
*Solution*: Migration was already applied. This is normal.

**Table Not Found**
```
❌ Table webhook_logs_uat does not exist. Please run migration 001 first.
```
*Solution*: Run the base table creation migration first: `npm run migrate`

**Permission Denied**
```
❌ User is not authorized to perform: dynamodb:UpdateTable
```
*Solution*: Ensure AWS credentials have DynamoDB table modification permissions.

**Index Creation Timeout**
```
❌ Index creation timeout or failed. Final status: CREATING
```
*Solution*: DynamoDB indexes can take time to create. Check AWS console and retry.

### Fallback Behavior
If the WhatsAppStatusIndex doesn't exist, the repository automatically falls back to scan operations:

```
⚠️  WhatsAppStatusIndex not found, falling back to scan. Run migration 002 to add the index.
```

This ensures the application continues to work even if the migration hasn't been applied yet.

## 📈 Performance Impact

### Before Migration
- 5 Global Secondary Indexes
- Basic webhook logging and analytics

### After Migration  
- 6 Global Secondary Indexes (+1 for WhatsApp status)
- Enhanced analytics with delivery tracking
- Efficient status-based queries
- Error monitoring capabilities

### Cost Considerations
- **Minimal Impact**: New index uses on-demand billing
- **Efficient Queries**: Status-based queries avoid full table scans
- **Optional Usage**: New features only used when needed

## 🎉 Summary

Migration 002 safely adds WhatsApp delivery status tracking and error monitoring to your webhook logging system without affecting existing functionality. The migration is production-ready with comprehensive safety features, testing, and monitoring capabilities.

**Next Steps After Migration:**
1. ✅ Validate migration completed successfully
2. ✅ Deploy updated application code  
3. ✅ Monitor delivery statistics
4. ✅ Set up alerts for high error rates
5. ✅ Use new analytics for business insights 