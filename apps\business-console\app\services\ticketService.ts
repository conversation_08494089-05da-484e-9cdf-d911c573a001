import { ApiResponse } from "~/types/api/Api";
import { 
  Ticket, 
  TicketNote, 
  SupportTicketStatus,
  UpdateTicketRequest, 
  AddTicketNoteRequest 
} from "~/types/api/businessConsoleService/Tickets";
import { API_BASE_URL, apiRequest } from "~/utils/api";

interface TicketFilters {
  ticketId?: number | null;
  userId?: number | null;
  status?: SupportTicketStatus[] | null;
  fromDate?: string | null;
  toDate?: string | null;
  pageNo?: number;
  pageSize?: number;
}

/**
 * Get all tickets with optional filtering and pagination
 */
export async function getTickets(
  request?: Request,
  filters?: TicketFilters
): Promise<ApiResponse<Ticket[]>> {
  // Default userId if not provided
  // const userId = filters?.userId ; // Using default user ID if not provided
  
  // Build the query string
  const params = new URLSearchParams();
  // params.append('userId', userId.toString());
  
  if (filters?.ticketId) {
    params.append('ticketId', filters.ticketId.toString());
  }
  
  if (filters?.status && filters.status.length > 0) {
    filters.status.forEach(status => {
      params.append('status', status);
    });
  }
  
  if (filters?.fromDate) {
    params.append('fromDate', filters.fromDate);
  }
  
  if (filters?.toDate) {
    params.append('toDate', filters.toDate);
  }
  
  // Pagination params
  params.append('pageNo', (filters?.pageNo || 0).toString());
  params.append('pageSize', (filters?.pageSize || 10).toString());
  
  const response = await apiRequest<Ticket[]>(
    `${API_BASE_URL}/bc/mnetadmin/tickets?${params.toString()}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch tickets");
  }
}

/**
 * Update a ticket
 */
export async function updateTicket(
  ticketId: number,
  data: UpdateTicketRequest,
  request?: Request
): Promise<ApiResponse<Ticket>> {
  const response = await apiRequest<Ticket>(
    `${API_BASE_URL}/support/${ticketId}`,
    "PUT",
    data,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to update ticket");
  }
}

/**
 * Get notes for a specific ticket
 */
export async function getTicketNotes(
  ticketId: number,
  request?: Request
): Promise<ApiResponse<TicketNote[]>> {
  const response = await apiRequest<TicketNote[]>(
    `${API_BASE_URL}/support/${ticketId}/notes`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch ticket notes");
  }
}

/**
 * Add a note to a ticket
 */
export async function addTicketNote(
  ticketId: number,
  data: AddTicketNoteRequest,
  request?: Request
): Promise<ApiResponse<TicketNote>> {
  const response = await apiRequest<TicketNote>(
    `${API_BASE_URL}/support/${ticketId}/notes`,
    "POST",
    data,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to add ticket note");
  }
} 