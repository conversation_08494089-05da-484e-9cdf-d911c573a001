import * as React from "react";
import { Check, ChevronDown } from "lucide-react";
import { cn } from "~/lib/utils";

interface MultiSelectProps {
      options: { value: string; label: string }[];
      selectedValues: string[];
      onChange: (values: string[]) => void;
      placeholder?: string;
      className?: string;
      height?: boolean
}

const MultiSelect: React.FC<MultiSelectProps> = ({
      options,
      selectedValues,
      onChange,
      placeholder = "Select...",
      className,
      height
}) => {
      const [isOpen, setIsOpen] = React.useState(false);

      const toggleValue = (value: string) => {
            onChange(
                  selectedValues?.includes(value)
                        ? selectedValues.filter((v) => v !== value)
                        : [...selectedValues, value]
            );
      };

      return (
            <div className="relative w-full">
                  {/* Trigger Button */}
                  <button
                        type="button"
                        onClick={() => setIsOpen(!isOpen)}
                        className={cn(
                              "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
                              className
                        )}
                  >
                        <span className="truncate">
                              {selectedValues?.length > 2
                                    ? `${options?.find((opt) => opt?.value === selectedValues[0])?.label}, ${options?.find((opt) => opt?.value === selectedValues[1])?.label
                                    } +${selectedValues?.length - 2} more`
                                    : selectedValues
                                          .map((value) => options?.find((opt) => opt?.value === value)?.label)
                                          .join(", ") || placeholder}
                        </span>
                        <ChevronDown className="h-4 w-4 opacity-50" />
                  </button>

                  {/* Dropdown Menu with Scroll */}
                  {isOpen && (
  <div
    className={cn(
      "absolute z-50 mt-1 w-full max-h-60 overflow-y-auto rounded-md border bg-white shadow-md",
      className
    )}
    onWheel={e => e.stopPropagation()}
    onTouchMove={e => e.stopPropagation()}
  >
    {options?.map((option) => (
      <div
        key={option?.value}
        className={cn(
          "flex cursor-pointer items-center px-4 py-2 text-sm hover:bg-gray-100",
          selectedValues?.includes(option?.value) && "bg-gray-200"
        )}
        onClick={() => toggleValue(option.value)}
      >
        {selectedValues?.includes(option.value) && (
          <Check className="h-4 w-4 text-primary mr-2" />
        )}
        {option?.label}
      </div>
    ))}
  </div>
)}
            </div>
      );
};

export default MultiSelect;
