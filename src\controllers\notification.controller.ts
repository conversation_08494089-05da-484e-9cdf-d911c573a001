import { Request, Response } from 'express';
import { CustomResponse } from '@interfaces/response.interface.js';

// import { logger } from '@/utils/logger.utils';
import FirebaseApiService from '@services/firebase.service.js';
import { messaging } from 'firebase-admin';
import { FcmTokenDetails } from '@/database/entity/dynamoDB/users.entity.js';

class NotificationController {
  public firebaseApiService = new FirebaseApiService();


  public registerUser = async (req: Request, res: Response) => {
    try {
      const { mobileNumber, fcmToken, deviceId, lastUpdated } = req.body;

      if (!mobileNumber || !fcmToken) {
        res.status(400).json({ok: false, message: 'mobileNumber and fcmToken are required' });
        return;
      }
      const response = await this.firebaseApiService.registerUser(mobileNumber, fcmToken, deviceId, lastUpdated);
      if (response.ok) {
        return res.status(200).json(response);
      }

      return res.status(400).json({ok: false, err: response.err});
    } catch (error) {
        const err = error as Error;
        console.error(err.stack);
        return res.status(500).json({ok: false, err:"something went wrong"});
    }
  };

  public sendNotification = async (req: Request, res: Response) => {
    try {
      const { mobileNumbers, title, body, data }: { mobileNumbers: string[], title: string, body: string, data: unknown } = req.body;

      if (!mobileNumbers || !title) {
        res.status(400).json({ok: false, message: 'mobileNumber and fcmToken are required' });
        return;
      }
      const response = await this.firebaseApiService.sendNotification(mobileNumbers, title, body, data);
      if (response.ok) {
        return res.status(200).json(response);
      }

      return res.status(400).json({ok: false, err: response.err});
    } catch (error) {
      const err = error as Error;
      console.error(err.stack);
      return res.status(500).json({ok: false, err:"something went wrong"});
    }
  };
  public logoutUser = async (req: Request, res: Response) => {
    try {
      const { fcmToken, mobileNumber } = req.body;
      if (!fcmToken || !mobileNumber) {
        return res.status(400).json({ ok: false, message: "FCM token and mobile number are required" });
      }

      // Dummy data for the FcmTokenDetails to delete the token from the database
      const fcmTokenDetails: FcmTokenDetails = {
        token: fcmToken,
        lastUpdated: Date.now(),
        lastUpdatedISO: new Date().toISOString(),
        expiresAt: Date.now() + 1000 * 60 * 60 * 24 * 30,
        expiresAtISO: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30).toISOString(),
      };

      await this.firebaseApiService.removeFcmToken(mobileNumber, fcmTokenDetails);
      return res.status(200).json({ ok: true, message: "FCM token removed successfully" });
    } catch (error) {
      const err = error as Error;
      console.error(err.stack);
      return res.status(500).json({ ok: false, err: "something went wrong" });
    }
  }
}

export default NotificationController;
