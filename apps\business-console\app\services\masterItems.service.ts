import { ApiResponse } from "~/types/api/Api";
import {
  MasterItemDto,
  MasterItemRequest,
  MasterItemResponse,
  CategoryResponse,
  GetMasterItemsParams,
  GetCategoriesParams,
} from "~/types/home/<USER>";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getMasterItems(
  request?: Request,
  params: GetMasterItemsParams = {}
): Promise<
  ApiResponse<{
    items: MasterItemDto[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
  }>
> {
  const { page = 1, limit = 10, search = "", type = "" } = params;

  const queryParams = new URLSearchParams({
    pageNo: page.toString(),
    size: limit.toString(),
    ...(search && { matchBy: search }),
    ...(type && { type }),
  });

  console.log(queryParams, "eeeeeeeeeeee");
  console.log(queryParams, "eeeeeeeeeeee");

  const response = await apiRequest<MasterItemResponse>(
    `${API_BASE_URL}/bc/mnetadmin/masteritems?${queryParams}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    // Transform the response to match the expected format
    const items = Array.isArray(response.data.masterItemDtoList)
      ? response.data.masterItemDtoList
      : [];
    const totalItems = response.data.masterItemDtoList?.length || 0;
    const totalPages = response.data.totalPages ? response.data.totalPages : 1;

    return {
      ...response,
      data: {
        items,
        totalItems,
        totalPages,
        currentPage: page,
      },
    };
  } else {
    throw new Error("Failed to fetch master items");
  }
}

export async function createMasterItem(
  userId: string,
  itemData: MasterItemRequest,
  request?: Request
): Promise<ApiResponse<MasterItemRequest>> {
  console.log(itemData, "gettingItemDat................");
  const response = await apiRequest<MasterItemRequest>(
    `${API_BASE_URL}/platform/master_item`,
    "POST",
    itemData,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to create master item");
  }
}

export async function updateMasterItem(
  itemId: number,
  itemData: MasterItemRequest,
  request?: Request
): Promise<ApiResponse<MasterItemRequest>> {
  console.log(itemData, "ooooooooooooo");

  const response = await apiRequest<MasterItemRequest>(
    `${API_BASE_URL}/bc/mnetadmin/masteritem/${itemId}`,
    "PUT",
    itemData,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to update master item");
  }
}

export async function getCategories(
  request?: Request,
  params: GetCategoriesParams = {}
): Promise<ApiResponse<CategoryResponse>> {
  const { level = 1, matchBy = "", pageNo = 0, size = 20, ondcDomain = "" } = params;

  const queryParams = new URLSearchParams({
    level: level.toString(),
    ...(matchBy && { matchBy }),
    ...(ondcDomain && { ondcDomain }),
    pageNo: pageNo.toString(),
    size: size.toString(),
  });

  const response = await apiRequest<CategoryResponse>(
    `${API_BASE_URL}/bc/mnetadmin/categories?${queryParams}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch categories");
  }
}
