/**
 * Migration 005: Add Time Partitioning to WebhookLog Table
 *
 * Adds datePartition and monthPartition fields to WebhookLog table for efficient time-based querying.
 */

import { DynamoDBClient, UpdateTableCommand, DescribeTableCommand, AttributeDefinition } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, ScanCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { formatInTimeZone } from 'date-fns-tz';

const nodeEnv = process.env.SERVER_ENV === 'production' ? 'prod' : 'uat';
const tableName = `webhook_logs_${nodeEnv}`;

export const migrationInfo = {
    id: '005',
    name: 'Add Time Partitioning to WebhookLog Table',
    description: 'Adds datePartition and monthPartition fields for efficient time-based querying',
    version: '1.0.0',
    dependencies: ['001'],
    createdAt: '2024-12-30T00:00:00.000Z'
};

function toDatePartition(timestamp: number): string {
    return formatInTimeZone(new Date(timestamp), 'Asia/Kolkata', 'yyyy-MM-dd');
}
function toMonthPartition(timestamp: number): string {
    return formatInTimeZone(new Date(timestamp), 'Asia/Kolkata', 'yyyy-MM');
}

export async function up(): Promise<void> {
    const client = new DynamoDBClient({});
    const docClient = DynamoDBDocumentClient.from(client);
    try {
        console.log(`🚀 Migration ${migrationInfo.id}: ${migrationInfo.name}`);
        const newIndexes = [
            {
                IndexName: 'DatePartitionIndex',
                KeySchema: [
                    { AttributeName: 'datePartition', KeyType: 'HASH' as const },
                    { AttributeName: 'timestamp', KeyType: 'RANGE' as const }
                ],
                Projection: { ProjectionType: 'ALL' as const }
            },
            {
                IndexName: 'MonthPartitionIndex',
                KeySchema: [
                    { AttributeName: 'monthPartition', KeyType: 'HASH' as const },
                    { AttributeName: 'timestamp', KeyType: 'RANGE' as const }
                ],
                Projection: { ProjectionType: 'ALL' as const }
            },
            {
                IndexName: 'BusinessDateIndex',
                KeySchema: [
                    { AttributeName: 'businessNumber', KeyType: 'HASH' as const },
                    { AttributeName: 'datePartition', KeyType: 'RANGE' as const }
                ],
                Projection: { ProjectionType: 'ALL' as const }
            }
        ];
        let currentTable;
        try {
            const describeCommand = new DescribeTableCommand({ TableName: tableName });
            currentTable = await client.send(describeCommand);
        } catch (error) {
            throw new Error(`Table ${tableName} does not exist. Please run migration 001 first.`);
        }
        const existingIndexes = currentTable.Table?.GlobalSecondaryIndexes?.map(idx => idx.IndexName) || [];
        const indexesToAdd = newIndexes.filter(idx => !existingIndexes.includes(idx.IndexName));
        if (indexesToAdd.length > 0) {
            console.log(`⏳ Adding ${indexesToAdd.length} new indexes one by one...`);
            const newAttributeDefinitions: AttributeDefinition[] = [
                { AttributeName: 'datePartition', AttributeType: 'S' },
                { AttributeName: 'monthPartition', AttributeType: 'S' }
            ];
            const existingAttributes = currentTable.Table?.AttributeDefinitions || [];
            const existingAttributeNames = existingAttributes.map(attr => attr.AttributeName);
            const attributesToAdd = newAttributeDefinitions.filter(
                attr => !existingAttributeNames.includes(attr.AttributeName)
            );
            for (let i = 0; i < indexesToAdd.length; i++) {
                const index = indexesToAdd[i];
                console.log(`   Creating index ${i + 1}/${indexesToAdd.length}: ${index.IndexName}...`);
                const updateTableCommand = new UpdateTableCommand({
                    TableName: tableName,
                    AttributeDefinitions: [
                        ...existingAttributes,
                        ...attributesToAdd
                    ],
                    GlobalSecondaryIndexUpdates: [{
                        Create: {
                            IndexName: index.IndexName,
                            KeySchema: index.KeySchema,
                            Projection: index.Projection
                        }
                    }]
                });
                await client.send(updateTableCommand);
                console.log(`   Waiting for ${index.IndexName} to become active...`);
                let attempts = 0;
                const maxAttempts = 60;
                while (attempts < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 10000));
                    const describeCommand = new DescribeTableCommand({ TableName: tableName });
                    const tableInfo = await client.send(describeCommand);
                    const targetIndex = tableInfo.Table?.GlobalSecondaryIndexes?.find(
                        idx => idx.IndexName === index.IndexName
                    );
                    if (targetIndex?.IndexStatus === 'ACTIVE') {
                        console.log(`   ✅ ${index.IndexName} is now active!`);
                        break;
                    }
                    attempts++;
                    console.log(`     Attempt ${attempts}: ${index.IndexName} status = ${targetIndex?.IndexStatus || 'UNKNOWN'}`);
                    if (attempts >= maxAttempts) {
                        throw new Error(`Timeout waiting for index ${index.IndexName} to become active`);
                    }
                }
            }
            console.log('✅ All indexes created successfully!');
        } else {
            console.log('✅ All required indexes already exist');
        }
        // Data migration for last 30 days
        console.log('⏳ Migrating data (last 30 days)...');
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const cutoffTimestamp = thirtyDaysAgo.getTime();
        let lastEvaluatedKey: Record<string, any> | undefined;
        let processedCount = 0;
        let updatedCount = 0;
        do {
            try {
                const scanParams: any = {
                    TableName: tableName,
                    Limit: 100,
                    ProjectionExpression: 'webhookId, #timestamp, businessNumber, datePartition, monthPartition',
                    FilterExpression: '#timestamp >= :cutoffTimestamp',
                    ExpressionAttributeNames: {
                        '#timestamp': 'timestamp'
                    },
                    ExpressionAttributeValues: {
                        ':cutoffTimestamp': cutoffTimestamp
                    }
                };
                if (lastEvaluatedKey) {
                    scanParams.ExclusiveStartKey = lastEvaluatedKey;
                }
                const result = await docClient.send(new ScanCommand(scanParams));
                const items = result.Items || [];
                for (const item of items) {
                    if (!item.datePartition || !item.monthPartition) {
                        await docClient.send(new UpdateCommand({
                            TableName: tableName,
                            Key: {
                                webhookId: item.webhookId,
                                timestamp: item.timestamp
                            },
                            UpdateExpression: 'SET datePartition = :datePartition, monthPartition = :monthPartition',
                            ExpressionAttributeValues: {
                                ':datePartition': toDatePartition(item.timestamp),
                                ':monthPartition': toMonthPartition(item.timestamp)
                            }
                        }));
                        updatedCount++;
                    }
                    processedCount++;
                }
                lastEvaluatedKey = result.LastEvaluatedKey;
                console.log(`   Processed ${processedCount} items, updated ${updatedCount} items`);
                await new Promise(resolve => setTimeout(resolve, 100));
            } catch (error) {
                console.error('Error during data migration:', error);
                break;
            }
        } while (lastEvaluatedKey);
        console.log(`✅ Data migration completed. Processed: ${processedCount}, Updated: ${updatedCount}`);
        console.log('🎉 Migration completed successfully!');
    } catch (error) {
        console.error('❌ Migration failed:', error);
        throw error;
    }
}

export async function down(): Promise<void> {
    const client = new DynamoDBClient({});
    try {
        console.log(`🔄 Rolling back Migration ${migrationInfo.id}: ${migrationInfo.name}`);
        let currentTable;
        try {
            const describeCommand = new DescribeTableCommand({ TableName: tableName });
            currentTable = await client.send(describeCommand);
        } catch (error) {
            console.log('❌ Table does not exist, nothing to rollback');
            return;
        }
        const indexesToRemove = [
            'DatePartitionIndex',
            'MonthPartitionIndex',
            'BusinessDateIndex'
        ];
        const existingIndexes = currentTable.Table?.GlobalSecondaryIndexes?.map(idx => idx.IndexName) || [];
        const indexesToDelete = indexesToRemove.filter(idx => existingIndexes.includes(idx));
        if (indexesToDelete.length > 0) {
            for (const indexName of indexesToDelete) {
                console.log(`   Removing index: ${indexName}...`);
                const updateTableCommand = new UpdateTableCommand({
                    TableName: tableName,
                    GlobalSecondaryIndexUpdates: [{
                        Delete: { IndexName: indexName }
                    }]
                });
                await client.send(updateTableCommand);
                console.log(`   Waiting for ${indexName} to be deleted...`);
                let attempts = 0;
                const maxAttempts = 60;
                while (attempts < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 10000));
                    const describeCommand = new DescribeTableCommand({ TableName: tableName });
                    const tableInfo = await client.send(describeCommand);
                    const targetIndex = tableInfo.Table?.GlobalSecondaryIndexes?.find(
                        idx => idx.IndexName === indexName
                    );
                    if (!targetIndex) {
                        console.log(`   ✅ ${indexName} deleted successfully!`);
                        break;
                    }
                    attempts++;
                    console.log(`     Attempt ${attempts}: ${indexName} status = ${targetIndex?.IndexStatus}`);
                    if (attempts >= maxAttempts) {
                        throw new Error(`Timeout waiting for index ${indexName} to be deleted`);
                    }
                }
            }
            console.log('✅ All indexes removed successfully!');
        } else {
            console.log('✅ No indexes to remove');
        }
        console.log('🔄 Rollback completed successfully!');
    } catch (error) {
        console.error('❌ Rollback failed:', error);
        throw error;
    }
}

export async function validate(): Promise<boolean> {
    const client = new DynamoDBClient({});
    try {
        console.log(`🔍 Validating Migration ${migrationInfo.id}: ${migrationInfo.name}`);
        const describeCommand = new DescribeTableCommand({ TableName: tableName });
        const tableInfo = await client.send(describeCommand);
        if (!tableInfo.Table) {
            console.log('❌ Table does not exist');
            return false;
        }
        const requiredIndexes = [
            'DatePartitionIndex',
            'MonthPartitionIndex',
            'BusinessDateIndex'
        ];
        const existingIndexes = tableInfo.Table.GlobalSecondaryIndexes?.map(idx => idx.IndexName) || [];
        const missingIndexes = requiredIndexes.filter(idx => !existingIndexes.includes(idx));
        if (missingIndexes.length > 0) {
            console.log(`❌ Missing indexes: ${missingIndexes.join(', ')}`);
            return false;
        }
        const inactiveIndexes = tableInfo.Table.GlobalSecondaryIndexes?.filter(
            idx => requiredIndexes.includes(idx.IndexName!) && idx.IndexStatus !== 'ACTIVE'
        ) || [];
        if (inactiveIndexes.length > 0) {
            console.log(`❌ Inactive indexes: ${inactiveIndexes.map(idx => idx.IndexName).join(', ')}`);
            return false;
        }
        console.log('✅ Migration validation passed!');
        return true;
    } catch (error) {
        console.error('❌ Validation failed:', error);
        return false;
    }
} 