import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import type { FormState } from "~/types/home/<USER>";
import { useDebounce } from "~/hooks/useDebounce";
import * as React from "react";
import { Textarea } from "@headlessui/react";

interface StepThreeProps {
  formData: FormState;
  onChange: (data: Partial<FormState>) => void;
  errors?: Record<string, string[] | undefined>;
  mode: 'create' | 'edit' | 'duplicate';
  renderRETInput: (field: string) => boolean;
}

// Default unit options
const DEFAULT_UNITS = ["Pcs", "kg", "g", "l", "ml", "Box", "unit"];

// Default dietary options
const DEFAULT_DIETARY = ["veg", "nonveg", "egg"];

function StepThree({ formData, onChange, errors, mode, renderRETInput }: StepThreeProps) {
  // Local state for form fields with proper initialization
  const defaultItemConfig: FormState['itemConfig'] = React.useMemo(() => ({
    type: formData.itemConfig?.type || "B2B",
    unit: formData.defaultUnit || "kg",
    minimumOrderQty: mode === 'create' ? 1 : (formData.itemConfig?.minimumOrderQty),
    incrementOrderQty: mode === 'create' ? 1 : (formData.itemConfig?.incrementOrderQty),
    weightFactor: mode === 'create' ? 1 : (formData.itemConfig?.weightFactor),
    packaging: formData.itemConfig?.packaging,
    mrpPerUnit: formData.itemConfig?.mrpPerUnit,
    maximumOrderQty: (formData.itemConfig?.maximumOrderQty),
    maxAvailableQty: (formData.itemConfig?.maxAvailableQty),
    productId: formData.itemConfig?.productId,
    originalProductId: formData.itemConfig?.originalProductId,
    isDefaultVariant: formData.itemConfig?.isDefaultVariant || false,
    sequencePriority: formData.itemConfig?.sequencePriority,
    gstEligible: formData.itemConfig?.gstEligible || "no",
    gstHsnCode: formData.itemConfig?.gstHsnCode,
    gstRate: formData.itemConfig?.gstRate,
    ondcDomain: formData.itemConfig.ondcDomain || 'RET10',
    taxExempt: formData.itemConfig?.taxExempt,
    disabled: mode === 'create' || mode === 'duplicate' ? false : (formData.itemConfig?.disabled || false),
    description: formData.itemConfig?.description,
    diet: formData.itemConfig?.diet,
  }), [formData, mode]);

  // Initialize state from props, but don't update on every prop change
  const [itemConfig, setItemConfig] = React.useState<FormState['itemConfig']>(() => {
    const config = { ...defaultItemConfig, ...formData.itemConfig };

    return {
      ...config,
      packaging: config.packaging,
      // Ensure minimum values for quantities
      minimumOrderQty: config.minimumOrderQty,
      incrementOrderQty: config.incrementOrderQty,
      maximumOrderQty: config.maximumOrderQty,
      maxAvailableQty: config.maxAvailableQty,
      weightFactor: config.weightFactor
    };
  });
  const [b2b, setB2b] = React.useState(() => Boolean(formData.b2b));
  const [b2c, setB2c] = React.useState(() => Boolean(formData.b2c));
  const [groupId, setGroupId] = React.useState(() => formData.groupId);
  const [groupSeq, setGroupSeq] = React.useState(() => formData.groupSeq);

  // Update local state when formData changes significantly
  React.useEffect(() => {
    if (!formData.itemConfig) return;

    const hasSignificantChanges = JSON.stringify(formData.itemConfig) !== JSON.stringify(itemConfig);
    if (hasSignificantChanges) {
      setItemConfig(() => ({
        ...formData.itemConfig!,
        // Ensure minimum values are maintained
        minimumOrderQty: formData.itemConfig.minimumOrderQty,
        incrementOrderQty: formData.itemConfig.incrementOrderQty,
        maximumOrderQty: formData.itemConfig.maximumOrderQty,
        maxAvailableQty: formData.itemConfig.maxAvailableQty,
        weightFactor: formData.itemConfig.weightFactor
      }));
      setB2b(Boolean(formData.b2b));
      setB2c(Boolean(formData.b2c));
      setGroupId(formData.groupId);
      setGroupSeq(formData.groupSeq);
    }
  }, [formData]);

  // Debounce all updates together
  const debouncedValues = useDebounce({
    itemConfig,
    b2b,
    b2c,
    groupId,
    groupSeq
  }, 300); // Increased debounce time to reduce updates

  // Single useEffect to handle all updates
  React.useEffect(() => {
    const { itemConfig, b2b, b2c, groupId, groupSeq } = debouncedValues;
    const newData = {
      itemConfig,
      b2b,
      b2c,
      groupId,
      groupSeq
    };

    // Only call onChange if values have actually changed
    if (JSON.stringify(newData) !== JSON.stringify({
      itemConfig: formData.itemConfig,
      b2b: formData.b2b,
      b2c: formData.b2c,
      groupId: formData.groupId,
      groupSeq: formData.groupSeq
    })) {
      onChange(newData);
    }
  }, [debouncedValues, onChange, formData]);


  // Get all available units including the default unit if it's not in the list
  const allUnits = React.useMemo(() => {
    const units = [...DEFAULT_UNITS];
    if (formData.defaultUnit && !units.includes(formData.defaultUnit)) {
      units.push(formData.defaultUnit);
    }
    return units;
  }, [formData.defaultUnit]);


  const handleUnitChange = (value: string) => {
    const oldUnit = formData.defaultUnit || formData.itemConfig?.unit;
    const oldPackaging = formData.itemConfig?.packaging;

    // If packaging was matching the old unit, update it to match the new unit
    const shouldUpdatePackaging = oldPackaging === oldUnit;

    onChange({
      defaultUnit: value,
      itemConfig: {
        ...(formData.itemConfig || {}),
        unit: value,
        packaging: shouldUpdatePackaging ? value : oldPackaging
      }
    });
  };


  const updateConfigField = React.useCallback(<K extends keyof FormState['itemConfig']>(
    field: K,
    value: string | number | boolean,
    isBlur?: boolean
  ) => {
    setItemConfig((prev: FormState['itemConfig']) => {

      const newValue = { ...prev };

      if (field === "incrementOrderQty" || field === "minimumOrderQty" || field === "maximumOrderQty" || field === "maxAvailableQty" || field === "weightFactor" || field === "mrpPerUnit" || field === "gstRate") {
        newValue[field] = (Number(value) || 0) as any;
      } else {
        newValue[field] = value as any;
      }

      // Handle boolean values directly
      // if (typeof value === 'boolean') {
      //   return { ...prev, [field]: value as any };
      // }

      // handle string values directly
      // if (value === "B2B" || value === "B2C" || value === "yes" || value === "no") {
      //   return { ...prev, [field]: value as any };
      // }

      // // Allow empty string while typing for number fields
      // if (!isBlur && value === '') {
      //   return { ...prev, [field]: 0 };
      // }

      // const numValue = Number(value);
      // const newValue = { ...prev };

      // // Only apply constraints on blur or if value is a valid number
      // if (isBlur || !isNaN(numValue)) {
      //   // Handle numeric fields
      //   if (
      //     field === "minimumOrderQty" ||
      //     field === "maximumOrderQty" ||
      //     field === "maxAvailableQty" ||
      //     field === "incrementOrderQty" ||
      //     field === "weightFactor" ||
      //     field === "mrpPerUnit" ||
      //     field === "gstRate"
      //   ) {
      //     newValue[field] = numValue as any;
      //   } else {
      //     // For non-numeric fields
      //     newValue[field] = value as any;
      //   }

      // // Ensure maximumOrderQty is always >= minimumOrderQty
      // if (field === "minimumOrderQty" || field === "maximumOrderQty") {
      //   newValue.maximumOrderQty = Math.max(
      //     newValue.maximumOrderQty,
      //     newValue.minimumOrderQty
      //   );
      // }

      // // Ensure maxAvailableQty is always >= maximumOrderQty
      // if (field === "maximumOrderQty" || field === "maxAvailableQty" || field === "minimumOrderQty") {
      //   newValue.maxAvailableQty = Math.max(
      //     newValue.minimumOrderQty,
      //     newValue.maxAvailableQty,
      //     newValue.maximumOrderQty
      //   );
      // }

      // // Ensure incrementOrderQty is <= (maximumOrderQty - minimumOrderQty)
      // if (field === "incrementOrderQty" || field === "minimumOrderQty" || field === "maximumOrderQty") {
      //   const maxIncrement = newValue.maximumOrderQty - newValue.minimumOrderQty;
      //   newValue.incrementOrderQty = Math.min(
      //     newValue.incrementOrderQty,
      //     Math.max(1, maxIncrement)
      //   );
      // }

      // Apply min/max constraints on blur
      // if (isBlur) {
      //   if (field === "minimumOrderQty") {
      //     newValue.minimumOrderQty =  numValue;
      //   }
      //   if (field === "maximumOrderQty") {
      //     newValue.maximumOrderQty = numValue;
      //   }
      //   if (field === "maxAvailableQty") {
      //     newValue.maxAvailableQty =  numValue;
      //   }
      //   if (field === "weightFactor") {
      //     newValue.weightFactor =  numValue;
      //   }
      // }

      return newValue;
    })

  }, []);

  return (
    <div className="grid gap-4 p-4 md:p-6">
      <div className="grid gap-4 md:grid-cols-2">
        <div className="col-span-full">
          <Label>Item Name</Label>
          <Input value={String(formData.itemName || "")} readOnly />
          {errors?.itemName?.[0] && (
            <p className="text-red-500">{errors.itemName[0]}</p>
          )}
        </div>

        {renderRETInput("description") &&
          <div className="col-span-full">
            <Label className="block">Item Description</Label>
            <Textarea
              value={itemConfig.description}
              onChange={(e) => updateConfigField("description", e.target.value)}
              rows={4}
              className="w-full mt-1 border rounded-md border-neutral-300"
            />
            {errors?.description?.[0] && (
              <p className="text-red-500">{errors.description[0]}</p>
            )}
          </div>
        }

        {/* b) Type => B2B or B2C */}
        {renderRETInput("type") && <div>
          <Label>Type</Label>
          <RadioGroup
            value={itemConfig.type}
            onValueChange={(val: "B2B" | "B2C") => updateConfigField("type", val)}
            className="grid grid-cols-3 gap-4 mt-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="type-b2b" value="B2B" />
              <Label htmlFor="type-b2b">B2B</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="type-b2c" value="B2C" />
              <Label htmlFor="type-b2c">B2C</Label>
            </div>
          </RadioGroup>
        </div>}

        {/* <div>
          <Label>Business Type</Label>
          <RadioGroup
            value={itemConfig.ondcDomain}
            onValueChange={(val: "RET11" | "RET10") => updateConfigField("ondcDomain", val)}
            className="grid grid-cols-3 gap-4 mt-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="type-RET11" value="RET11" />
              <Label htmlFor="type-RET11">Restaurant</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="type-RET10" value="RET10" />
              <Label htmlFor="type-RET10">Non-Restaurant</Label>
            </div>
          </RadioGroup>
        </div> */}

        {/* Unit Selection */}
        {renderRETInput("unit") && <div>
          <Label>Unit</Label>
          <RadioGroup
            value={formData.defaultUnit || formData.itemConfig?.unit || "Pcs"}
            onValueChange={handleUnitChange}
            className="grid grid-cols-3 gap-4 mt-1"
          >
            {allUnits.map((unit) => (
              <div key={unit} className="flex items-center space-x-2">
                <RadioGroupItem value={unit} id={`unit-${unit}`} />
                <Label htmlFor={`unit-${unit}`} className="cursor-pointer">
                  {unit}
                </Label>
              </div>
            ))}
          </RadioGroup>
          {errors?.defaultUnit?.[0] && (
            <p className="text-red-500 text-sm mt-1">{errors.defaultUnit[0]}</p>
          )}
        </div>}

        {/* g) Packaging - only show when Unit is selected */}
        {renderRETInput("unit") && ((formData.defaultUnit === "unit" || formData.itemConfig?.unit === "unit")) && (
          <div>
            <Label>Packaging</Label>
            <Input
              value={itemConfig.packaging}
              onChange={(e) => updateConfigField("packaging", e.target.value)}
            />
          </div>
        )}

        {renderRETInput("diet") && <div>
          <Label>Dietary</Label>
          <RadioGroup
            value={itemConfig.diet}
            onValueChange={(val) => updateConfigField("diet", val)}
            className="grid grid-cols-3 gap-4 mt-1"
          >
            {DEFAULT_DIETARY.map((diet) => (
              <div key={diet} className="flex items-center space-x-2">
                <RadioGroupItem value={diet} id={`diet-${diet}`} />
                <Label htmlFor={`diet-${diet}`} className="cursor-pointer">
                  {diet}
                </Label>
              </div>
            ))}
          </RadioGroup>
          {errors?.diet?.[0] && (
            <p className="text-red-500 text-sm mt-1">{errors.diet[0]}</p>
          )}
        </div>}

        {/* d) Minimum Order Qty */}
        {renderRETInput("minimumOrderQty") && <div>
          <Label>Minimum Order Qty</Label>
          <Input
            type="number"
            value={itemConfig.minimumOrderQty}
            onChange={(e) => updateConfigField("minimumOrderQty", e.target.value)}
            onBlur={(e) => updateConfigField("minimumOrderQty", e.target.value, true)}
          />
          {errors?.minimumOrderQty?.[0] && (
            <p className="text-red-500 text-sm mt-1">{errors.minimumOrderQty[0]}</p>
          )}
        </div>}

        {/* Maximum Order Qty */}
        {renderRETInput("maximumOrderQty") && <div>
          <Label>Maximum Order Qty</Label>
          <Input
            type="number"
            value={itemConfig.maximumOrderQty}
            onChange={(e) => updateConfigField("maximumOrderQty", e.target.value)}
            onBlur={(e) => updateConfigField("maximumOrderQty", e.target.value, true)}
          />
          {errors?.maximumOrderQty?.[0] && (
            <p className="text-red-500 text-sm mt-1">{errors.maximumOrderQty[0]}</p>
          )}
        </div>}

        {/* Maximum Available Qty */}
        {renderRETInput("maxAvailableQty") && <div>
          <Label>Maximum Available Daily Qty</Label>
          <Input
            type="number"
            value={itemConfig.maxAvailableQty}
            onChange={(e) => updateConfigField("maxAvailableQty", e.target.value)}
            onBlur={(e) => updateConfigField("maxAvailableQty", e.target.value, true)}
          />
          {errors?.maxAvailableQty?.[0] && (
            <p className="text-red-500 text-sm mt-1">{errors.maxAvailableQty[0]}</p>
          )}
        </div>}

        {/* e) Increment Qty */}
        {renderRETInput("incrementOrderQty") && <div>
          <Label>Increment Qty</Label>
          <Input
            type="number"
            value={itemConfig.incrementOrderQty}
            onChange={(e) => updateConfigField("incrementOrderQty", e.target.value)}
            onBlur={(e) => updateConfigField("incrementOrderQty", e.target.value, true)}
          />
          {errors?.incrementOrderQty?.[0] && (
            <p className="text-red-500 text-sm mt-1">{errors.incrementOrderQty[0]}</p>
          )}
        </div>}

        {/* f) Weight Factor */}
        {renderRETInput("weightFactor") && <div>
          <Label>Weight Factor</Label>
          <Input
            type="number"
            step="0.001"
            value={itemConfig.weightFactor}
            onChange={(e) => updateConfigField("weightFactor", e.target.value)}
            onBlur={(e) => updateConfigField("weightFactor", e.target.value, true)}
          />
          {errors?.weightFactor?.[0] && (
            <p className="text-red-500 text-sm mt-1">{errors.weightFactor[0]}</p>
          )}
        </div>}

        {/* h) MRP / Price Per Unit */}
        <div>
          <Label>MRP/Price per unit</Label>
          <Input
            type="number"
            value={itemConfig.mrpPerUnit}
            onChange={(e) => updateConfigField("mrpPerUnit", Number(e.target.value))}
          />
        </div>

        {/* Product ID */}
        {renderRETInput("productId") && <div>
          <Label>Product ID</Label>
          <Input
            value={itemConfig.productId}
            onChange={(e) => updateConfigField("productId", e.target.value)}
          />
        </div>}

        {/* Show variant fields only when mode is not create */}


        {mode !== 'create' && renderRETInput("groupId") && (
          //   <>
          //   {/* j) Select original product/variant => dropdown */}
          //   <div>
          //     <Label>Original Product/Variant</Label>
          //     <Select
          //       onValueChange={(val) => updateConfigField("originalProductId", val)}
          //       value={itemConfig.originalProductId || undefined}
          //     >
          //       <SelectTrigger>
          //         <SelectValue placeholder="Select something" />
          //       </SelectTrigger>
          //       <SelectContent>
          //         <SelectItem value="none">None</SelectItem>
          //         <SelectItem value="100">Product #100</SelectItem>
          //         <SelectItem value="200">Product #200</SelectItem>
          //         <SelectItem value="300">Product #300</SelectItem>
          //       </SelectContent>
          //     </Select>
          //   </div>

          //   {/* k) Is default variant => yes/no switch or checkbox */}
          //   <div className="flex items-center space-x-2">
          //     <Switch
          //       checked={itemConfig.isDefaultVariant}
          //       onCheckedChange={(checked) => updateConfigField("isDefaultVariant", checked)}
          //     />
          //     <Label>Is Default Variant?</Label>
          //   </div>

          //   {/* l) Sequence / Priority => Drop down */}
          //   <div>
          //     <Label>Sequence/Priority</Label>
          //     <Select
          //       onValueChange={(val) => updateConfigField("sequencePriority", val)}
          //       value={itemConfig.sequencePriority || undefined}
          //     >
          //       <SelectTrigger>
          //         <SelectValue placeholder="Select priority" />
          //       </SelectTrigger>
          //       <SelectContent>
          //         <SelectItem value="none">None</SelectItem>
          //         <SelectItem value="1">1</SelectItem>
          //         <SelectItem value="2">2</SelectItem>
          //         <SelectItem value="3">3</SelectItem>
          //       </SelectContent>
          //     </Select>
          //   </div>
          // </>
          <div className="col-span-full md:col-span-2">
            <Label htmlFor="groupId">Group ID</Label>
            <Input
              name="groupId"
              value={String(groupId)}
              onChange={(e) => setGroupId(e.target.value)}
              className="w-full"
            />
            {errors?.groupId?.[0] && (
              <p className="text-red-500">{errors.groupId[0]}</p>
            )}
          </div>
        )}

        {/* m) Is the item GST eligible => yes/no */}
        {renderRETInput("gstEligible") && <div className="col-span-full">
          <Label>Is GST Eligible?</Label>
          <RadioGroup
            value={itemConfig.gstEligible}
            onValueChange={(val: "yes" | "no") => updateConfigField("gstEligible", val)}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="gst-yes" value="yes" />
              <Label htmlFor="gst-yes">Yes</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="gst-no" value="no" />
              <Label htmlFor="gst-no">No</Label>
            </div>
          </RadioGroup>
        </div>}

        {/* Show GST fields only when GST Eligible is "yes" */}
        {itemConfig.gstEligible === "yes" && (
          <>
            {/* n) GST HSN Code */}
            <div>
              <Label>GST HSN Code</Label>
              <Input
                value={itemConfig.gstHsnCode}
                onChange={(e) => updateConfigField("gstHsnCode", e.target.value)}
                placeholder="Enter HSN Code"
              />
            </div>

            {/* o) GST Rate */}
            <div>
              <Label>GST Rate (%)</Label>
              <Input
                type="number"
                value={itemConfig.gstRate}
                onChange={(e) => updateConfigField("gstRate", Number(e.target.value))}
                // onBlur={(e) => updateConfigField("gstRate", Number(e.target.value), true)}
                placeholder="Enter GST Rate"
              />
            </div>
          </>
        )}

        {/* p) Item status => enabled / disabled */}
        <div>
          <Label>Item Status</Label>
          <RadioGroup
            value={itemConfig.disabled ? "disabled" : "enabled"}
            onValueChange={(val: "enabled" | "disabled") => updateConfigField("disabled", val === "disabled")}
            className="grid grid-cols-2 gap-4 mt-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="status-enabled" value="enabled" />
              <Label htmlFor="status-enabled">Enabled</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="status-disabled" value="disabled" />
              <Label htmlFor="status-disabled">Disabled</Label>
            </div>
          </RadioGroup>
          {errors?.disabled?.[0] && (
            <p className="text-red-500 text-sm mt-1">{errors.disabled[0]}</p>
          )}
        </div>
        <div>
          <Label>{itemConfig.ondcDomain === "RET10" ? "Tax Excluded" : "Is GST Eligible?"}</Label>
          <RadioGroup
            value={itemConfig.taxExempt ? "enabled" : "disabled"}
            onValueChange={(val: "enabled" | "disabled") => updateConfigField("taxExempt", val === "enabled")}
            className="grid grid-cols-2 gap-4 mt-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="taxExempt-no" value={itemConfig.ondcDomain === "RET11" ? "enabled" : "disabled"} />
              <Label htmlFor="taxExempt-no">No</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="taxExempt-yes" value={itemConfig.ondcDomain === "RET11" ? "disabled" : "enabled"} />
              <Label htmlFor="taxExempt-yes">Yes</Label>
            </div>
          </RadioGroup>
          {itemConfig.ondcDomain === "RET11" && <div className="mt-1 text-sm text-gray-500">&#9432; Select no incase of MRP based products</div>}
          {errors?.taxExempt?.[0] && (
            <p className="text-red-500 text-sm mt-1">{errors.taxExempt[0]}</p>
          )}
        </div>
      </div>
    </div>
  );
}

export default React.memo(StepThree);