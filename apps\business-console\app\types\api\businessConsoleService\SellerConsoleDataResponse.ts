export interface SellerConsoleDataResponse {
  startDate: string;
  endDate: string;
  totalWeight: number;
  totalAmount: number;
  totalOrders: number;
  returnWeight: number;
  returnAmount: number;
  avgOrderValue: number;
  newCustomerCount: number;
}

export interface BuyerSummary {
  buyerId: number;
  buyerName: string;
  itemCount: number;
  totalWeight: number;
  PendingAmount: number;
  PendingAmountCount: number;
}

export interface OrderItem {
  itemName: string;
  totalWeight: number;
  itemCount: number;
}
