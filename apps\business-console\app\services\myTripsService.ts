import { ApiResponse } from "~/types/api/Api";
import { BcTripSummaryDto } from "~/types/api/businessConsoleService/MyTrips";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getSellerTrips(
  date: string,
  request?: Request
): Promise<ApiResponse<BcTripSummaryDto[]>> {
  const response = await apiRequest<BcTripSummaryDto[]>(
    `${API_BASE_URL}/bc/seller/trips/date/${date}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch trip summary");
  }
}
