// import { DynamoDB } from 'aws-sdk';
import { FcmTokenDetails, User } from '@entity/dynamoDB/users.entity.js';
import AWS from 'aws-sdk';

export class UserRepository {
  private readonly tableName: string;
  private readonly dynamoDb: AWS.DynamoDB.DocumentClient;

  constructor(tableName: string, dynamoDb = new AWS.DynamoDB.DocumentClient()) {
    AWS.config.update({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // Replace with your AWS secret key
      region: process.env.AWS_REGION, // Replace with your DynamoDB region, e.g., 'us-east-1'
    });
    this.tableName = tableName;
    this.dynamoDb = dynamoDb;
  }

  async saveUser(user: User): Promise<void> {
    const params = {
      TableName: this.tableName,
      Item: user,
    };
    await this.dynamoDb.put(params).promise();
  }

  async getUserByMobileNumber(mobileNumber: string): Promise<User | null> {
    const params = {
      TableName: this.tableName,
      Key: { mobileNumber },
    };
    const result = await this.dynamoDb.get(params).promise();
    return result.Item as User | null;
  }

  async addFcmToken(mobileNumber: string, fcmToken: FcmTokenDetails): Promise<void> {
    const params = {
      TableName: this.tableName,
      Key: { mobileNumber },
      UpdateExpression: 'SET fcmTokens = list_append(if_not_exists(fcmTokens, :emptyList), :fcmToken)',
      ExpressionAttributeValues: {
        ':fcmToken': [fcmToken], // Append the new token to the list
        ':emptyList': [], // Initialize with an empty list if the attribute doesn't exist
      },
      ReturnValues: 'UPDATED_NEW',
    };

    try {
      await this.dynamoDb.update(params).promise();
      console.log('FCM token added successfully');
    } catch (error) {
      console.error('Error adding FCM token:', error);
      throw error;
    }
  }

  async removeFcmToken(mobileNumber: string, fcmToken: FcmTokenDetails): Promise<void> {
    await this.removeFcmTokens(mobileNumber, [fcmToken]);
  }

  async removeFcmTokens(mobileNumber: string, fcmTokensToRemove: FcmTokenDetails[]): Promise<void> {
    // First get the current user data
    const user = await this.getUserByMobileNumber(mobileNumber);
    if (!user || !user.fcmTokens) {
      return;
    }

    // Create a Map of tokens to remove for O(1) lookup
    const tokensToRemoveMap = new Map(
      fcmTokensToRemove.map(token => [token.token, token])
    );

    // Filter out all tokens we want to remove in one go
    const updatedTokens = user.fcmTokens.filter(
      token => !tokensToRemoveMap.has(token.token)
    );

    // Update the user with the new token list in a single operation
    const params = {
      TableName: this.tableName,
      Key: { mobileNumber },
      UpdateExpression: 'SET fcmTokens = :fcmTokens',
      ExpressionAttributeValues: {
        ':fcmTokens': updatedTokens,
      },
      ReturnValues: 'UPDATED_NEW',
    };

    await this.dynamoDb.update(params).promise();
  }

  async scanUsers(limit: number, lastEvaluatedKey?: AWS.DynamoDB.Key): Promise<{ items: User[], lastEvaluatedKey?: AWS.DynamoDB.Key }> {
    const params: AWS.DynamoDB.DocumentClient.ScanInput = {
      TableName: this.tableName,
      Limit: limit,
      ExclusiveStartKey: lastEvaluatedKey
    };

    const result = await this.dynamoDb.scan(params).promise();
    
    return {
      items: result.Items as User[],
      lastEvaluatedKey: result.LastEvaluatedKey
    };
  }
}
