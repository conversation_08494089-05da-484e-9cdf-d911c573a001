import React, { useState, ChangeEvent, useEffect } from 'react';
import {
  ConfigType,
  DcBody,
} from '~/types/api/businessConsoleService/DeliveryConfig';
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Switch } from '../ui/switch';
import { Button } from '../ui/button';

interface CreateDeliveryConfigProps {
  isOpen: boolean;
  onSave: (config: DcBody) => void;
  onClose: () => void;
  initialConfig?: DcBody; // <-- Add this line
}

const emptyConfig: DcBody = {
  sellerId: 0,
  configType: ConfigType.PERCENTAGE_BASED,
  buyerPercentage: 0,
  sellerPercentage: 0,
  minOrderValue: 0,
  maxOrderValue: 0,
  maxBuyerDeliveryCharge: 0,
  maxSellerDeliveryCharge: 0,
  active: true,
};

export default function CreateDeliveryConfig({ isOpen, onSave, onClose, initialConfig }: CreateDeliveryConfigProps) {
  const [form, setForm] = useState<DcBody>(initialConfig ?? emptyConfig);

  // Reset form when dialog opens or initialConfig changes
  useEffect(() => {
    if (isOpen) {
      setForm(initialConfig ?? emptyConfig);
    }
  }, [isOpen, initialConfig]);

  // Utility for input changes
  const handleChange =
    (field: keyof DcBody) =>
    (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
      const value =
        e.target.type === 'number' ? Number(e.target.value) : (e.target.value as any);

      setForm((prev) => ({
        ...prev,
        [field]: value,
      }));
    };

  // Reset form on close
  const handleClose = () => {
    setForm(initialConfig ?? emptyConfig);
    onClose();
  };

  const handleSave = () => {
    onSave(form);
    handleClose();
  };

  // UI logic for conditional fields
  const isPercentage = form.configType === ConfigType.PERCENTAGE_BASED;
  const isOrderBased = form.configType === ConfigType.ORDER_VALUE_BASED;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-xl">
        <DialogHeader>
          <DialogTitle>Create Delivery Configuration</DialogTitle>
        </DialogHeader>

        <form
          onSubmit={e => {
            e.preventDefault();
            handleSave();
          }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Seller ID */}
            {/* <div className="flex flex-col">
              <label className="text-xs font-medium mb-1">Seller ID</label>
              <Input
                type="number"
                value={form.sellerId}
                onChange={handleChange('sellerId')}
                min={0}
                required
              />
            </div> */}

            {/* Config Type */}
            <div className="flex flex-col">
              <label className="text-xs font-medium mb-1">Config Type</label>
              <Select
                value={form.configType}
                onValueChange={(v: ConfigType) => setForm((p) => ({ ...p, configType: v }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Config type" />
                </SelectTrigger>
                <SelectContent>
  {Object.values(ConfigType).map((ct) => (
    <SelectItem key={ct} value={ct}>
       {ct}
    </SelectItem>
  ))}
</SelectContent>
              </Select>
            </div>

            {/* Buyer Percentage */}
            <div className="flex flex-col">
              <label className="text-xs font-medium mb-1">Buyer Percentage (%)</label>
              <Input
                type="number"
                value={form.buyerPercentage}
                onChange={handleChange('buyerPercentage')}
                min={0}
                max={100}
                required
              />
            </div>

            {/* Seller Percentage */}
            <div className="flex flex-col">
              <label className="text-xs font-medium mb-1">Seller Percentage (%)</label>
              <Input
                type="number"
                value={form.sellerPercentage}
                onChange={handleChange('sellerPercentage')}
                min={0}
                max={100}
                required
              />
            </div>

            {/* Min/Max Order Value (Order Based only) */}
            {isOrderBased && (
              <>
                <div className="flex flex-col">
                  <label className="text-xs font-medium mb-1">Min Order Value</label>
                  <Input
                    type="number"
                    value={form.minOrderValue}
                    onChange={handleChange('minOrderValue')}
                    min={0}
                  />
                </div>
                <div className="flex flex-col">
                  <label className="text-xs font-medium mb-1">Max Order Value</label>
                  <Input
                    type="number"
                    value={form.maxOrderValue}
                    onChange={handleChange('maxOrderValue')}
                    min={0}
                  />
                </div>
              </>
            )}

            {/* Max Buyer/Seller Delivery Charge (Percentage Based only) */}
            {isPercentage && (
              <>
                <div className="flex flex-col">
                  <label className="text-xs font-medium mb-1">Max Buyer Delivery Charge</label>
                  <Input
                    type="number"
                    value={form.maxBuyerDeliveryCharge}
                    onChange={handleChange('maxBuyerDeliveryCharge')}
                    min={0}
                  />
                </div>
                <div className="flex flex-col">
                  <label className="text-xs font-medium mb-1">Max Seller Delivery Charge</label>
                  <Input
                    type="number"
                    value={form.maxSellerDeliveryCharge}
                    onChange={handleChange('maxSellerDeliveryCharge')}
                    min={0}
                  />
                </div>
              </>
            )}
          </div>

          {/* Active toggle */}
          <div className="flex items-center gap-2 mt-6">
            <Switch
              id="active"
              checked={form.active}
              onCheckedChange={(checked) => setForm((p) => ({ ...p, active: checked }))}
            />
            <label htmlFor="active" className="text-sm font-medium">
              Active
            </label>
          </div>

          <DialogFooter className="mt-6">
            <DialogClose asChild>
              <Button variant="secondary" type="button" onClick={handleClose}>
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit">Save</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}