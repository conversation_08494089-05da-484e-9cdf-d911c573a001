/**
 * Migration 001: Create WebhookLog Table V2
 * 
 * This migration creates the main webhook_logs table with enhanced features:
 * - IST timezone support
 * - MessageCode classification (GREETING, ORDER_STATUS)
 * - Optimized indexes for analytics and querying
 * - Removed unnecessary userAgent and sourceIp fields
 * 
 * Version: 2.0.0
 * Created: 2023-12-30
 * Dependencies: None
 */

import { DynamoDBClient, CreateTableCommand, DescribeTableCommand } from '@aws-sdk/client-dynamodb';
import { KeyType, ScalarAttributeType, BillingMode, ProjectionType } from '@aws-sdk/client-dynamodb';
import dotenv from 'dotenv';

dotenv.config();

export const migrationInfo = {
    id: '001',
    name: 'Create WebhookLog Table V2',
    description: 'Creates the main webhook_logs table with IST timezone support and message classification',
    version: '2.0.0',
    dependencies: [],
    createdAt: '2023-12-30T00:00:00.000Z'
};

const nodeEnv = process.env.SERVER_ENV === 'production' ? 'prod' : 'uat';
const webhookLogsTableName = `webhook_logs_${nodeEnv}`;

const WebhookLogTableV2 = {
    TableName: webhookLogsTableName,
    KeySchema: [
        { AttributeName: 'webhookId', KeyType: KeyType.HASH },      // Partition key
        { AttributeName: 'timestamp', KeyType: KeyType.RANGE }      // Sort key (Unix timestamp)
    ],
    AttributeDefinitions: [
        { AttributeName: 'webhookId', AttributeType: ScalarAttributeType.S },
        { AttributeName: 'timestamp', AttributeType: ScalarAttributeType.N },
        { AttributeName: 'businessNumber', AttributeType: ScalarAttributeType.S },
        { AttributeName: 'customerNumber', AttributeType: ScalarAttributeType.S },
        { AttributeName: 'messageType', AttributeType: ScalarAttributeType.S },
        { AttributeName: 'status', AttributeType: ScalarAttributeType.S },
        { AttributeName: 'messageCode', AttributeType: ScalarAttributeType.S }
    ],
    GlobalSecondaryIndexes: [
        {
            IndexName: 'BusinessNumberIndex',
            KeySchema: [
                { AttributeName: 'businessNumber', KeyType: KeyType.HASH },
                { AttributeName: 'timestamp', KeyType: KeyType.RANGE }
            ],
            Projection: {
                ProjectionType: ProjectionType.ALL
            }
        },
        {
            IndexName: 'CustomerNumberIndex',
            KeySchema: [
                { AttributeName: 'customerNumber', KeyType: KeyType.HASH },
                { AttributeName: 'timestamp', KeyType: KeyType.RANGE }
            ],
            Projection: {
                ProjectionType: ProjectionType.ALL
            }
        },
        {
            IndexName: 'MessageTypeIndex',
            KeySchema: [
                { AttributeName: 'messageType', KeyType: KeyType.HASH },
                { AttributeName: 'timestamp', KeyType: KeyType.RANGE }
            ],
            Projection: {
                ProjectionType: ProjectionType.ALL
            }
        },
        {
            IndexName: 'StatusIndex',
            KeySchema: [
                { AttributeName: 'status', KeyType: KeyType.HASH },
                { AttributeName: 'timestamp', KeyType: KeyType.RANGE }
            ],
            Projection: {
                ProjectionType: ProjectionType.ALL
            }
        },
        {
            IndexName: 'MessageCodeIndex',
            KeySchema: [
                { AttributeName: 'messageCode', KeyType: KeyType.HASH },
                { AttributeName: 'timestamp', KeyType: KeyType.RANGE }
            ],
            Projection: {
                ProjectionType: ProjectionType.ALL
            }
        }
    ],
    BillingMode: BillingMode.PAY_PER_REQUEST
};

export async function up(): Promise<void> {
    const client = new DynamoDBClient({});
    
    try {
        console.log(`🚀 Migration ${migrationInfo.id}: ${migrationInfo.name}`);
        console.log(`📋 Creating table: ${webhookLogsTableName}`);
        console.log('✨ Features:');
        console.log('   - Primary keys: webhookId (HASH), timestamp (RANGE)');
        console.log('   - IST timezone fields: timestampISO, receivedAtISO, processedAtISO, lastUpdatedISO');
        console.log('   - MessageCode classification: GREETING, ORDER_STATUS');
        console.log('   - 5 Global Secondary Indexes for efficient querying');
        console.log('   - PAY_PER_REQUEST billing mode');
        console.log('   - Removed: userAgent, sourceIp fields\n');
        
        // Check if table already exists
        try {
            const existingTable = await client.send(new DescribeTableCommand({
                TableName: webhookLogsTableName
            }));
            
            console.log(`⚠️  Table ${webhookLogsTableName} already exists!`);
            console.log(`   Status: ${existingTable.Table?.TableStatus}`);
            console.log(`   Created: ${existingTable.Table?.CreationDateTime}`);
            console.log(`   Items: ${existingTable.Table?.ItemCount || 'Unknown'}`);
            console.log('\n⏭️  Skipping migration - table already exists.');
            return;
            
        } catch (error: any) {
            if (error.name !== 'ResourceNotFoundException') {
                throw error;
            }
            // Table doesn't exist, proceed with creation
        }

        console.log('📝 Creating table with configuration...');
        console.log('⏳ Starting table creation...');
        
        const createCommand = new CreateTableCommand(WebhookLogTableV2);
        const result = await client.send(createCommand);
        
        console.log(`✅ Table creation initiated successfully!`);
        console.log(`   Table Status: ${result.TableDescription?.TableStatus}`);
        console.log(`   Table ARN: ${result.TableDescription?.TableArn}`);
        
        // Wait for table to become active
        console.log('\n⌛ Waiting for table and indexes to become active...');
        let tableStatus = 'CREATING';
        let attempt = 0;
        const maxAttempts = 60; // 10 minutes max
        
        while (tableStatus === 'CREATING' && attempt < maxAttempts) {
            attempt++;
            await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
            
            const describeResult = await client.send(new DescribeTableCommand({
                TableName: webhookLogsTableName
            }));
            
            tableStatus = describeResult.Table?.TableStatus || 'UNKNOWN';
            const indexes = describeResult.Table?.GlobalSecondaryIndexes || [];
            
            console.log(`   Attempt ${attempt}: Table Status = ${tableStatus}`);
            
            if (indexes.length > 0) {
                indexes.forEach(index => {
                    console.log(`   Index ${index.IndexName}: ${index.IndexStatus}`);
                });
            }
            
            // Check if all indexes are active
            const allIndexesActive = indexes.every(index => index.IndexStatus === 'ACTIVE');
            if (tableStatus === 'ACTIVE' && allIndexesActive) {
                break;
            }
        }
        
        if (tableStatus === 'ACTIVE') {
            console.log('\n🎉 Migration completed successfully!');
            console.log('\n📊 Table Summary:');
            console.log(`   Table Name: ${webhookLogsTableName}`);
            console.log(`   Environment: ${nodeEnv}`);
            console.log(`   Billing Mode: PAY_PER_REQUEST`);
            console.log(`   Global Secondary Indexes: 5`);
            console.log('   ✅ BusinessNumberIndex');
            console.log('   ✅ CustomerNumberIndex');
            console.log('   ✅ MessageTypeIndex');
            console.log('   ✅ StatusIndex');
            console.log('   ✅ MessageCodeIndex');
            
        } else {
            throw new Error(`Table creation timeout or failed. Final status: ${tableStatus}`);
        }
        
    } catch (error) {
        console.error(`\n❌ Migration ${migrationInfo.id} failed:`, error);
        throw error;
    } finally {
        client.destroy();
    }
}

export async function down(): Promise<void> {
    const client = new DynamoDBClient({});
    
    try {
        console.log(`🔄 Rolling back migration ${migrationInfo.id}: ${migrationInfo.name}`);
        console.log(`🗑️  Deleting table: ${webhookLogsTableName}`);
        
        // Note: DynamoDB doesn't have a direct "drop table" command in this SDK version
        // In a real rollback, you would use DeleteTableCommand
        console.log('⚠️  Manual rollback required: Delete table from AWS Console');
        console.log(`   Table to delete: ${webhookLogsTableName}`);
        
    } catch (error) {
        console.error(`❌ Rollback failed for migration ${migrationInfo.id}:`, error);
        throw error;
    } finally {
        client.destroy();
    }
}

export async function validate(): Promise<boolean> {
    const client = new DynamoDBClient({});
    
    try {
        const result = await client.send(new DescribeTableCommand({
            TableName: webhookLogsTableName
        }));
        
        const table = result.Table;
        if (!table) return false;
        
        console.log(`🔍 Validating migration ${migrationInfo.id}...`);
        
        // Check table status
        if (table.TableStatus !== 'ACTIVE') {
            console.log(`❌ Table status: ${table.TableStatus} (expected: ACTIVE)`);
            return false;
        }
        
        // Check required indexes
        const requiredIndexes = ['BusinessNumberIndex', 'CustomerNumberIndex', 'MessageTypeIndex', 'StatusIndex', 'MessageCodeIndex'];
        const existingIndexes = table.GlobalSecondaryIndexes?.map(idx => idx.IndexName) || [];
        
        const missingIndexes = requiredIndexes.filter(name => !existingIndexes.includes(name));
        
        if (missingIndexes.length > 0) {
            console.log(`❌ Missing indexes: ${missingIndexes.join(', ')}`);
            return false;
        }
        
        // Check all indexes are active
        const inactiveIndexes = table.GlobalSecondaryIndexes?.filter(idx => idx.IndexStatus !== 'ACTIVE') || [];
        if (inactiveIndexes.length > 0) {
            console.log(`❌ Inactive indexes: ${inactiveIndexes.map(idx => idx.IndexName).join(', ')}`);
            return false;
        }
        
        console.log('✅ Migration validation passed');
        return true;
        
    } catch (error: any) {
        if (error.name === 'ResourceNotFoundException') {
            console.log('❌ Table does not exist');
            return false;
        }
        throw error;
    } finally {
        client.destroy();
    }
}

// Legacy export for backward compatibility
export { up as createWebhookLogTableV2, validate as validateTableStructure }; 