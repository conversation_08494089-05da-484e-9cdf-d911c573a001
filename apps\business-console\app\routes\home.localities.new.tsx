import { useState, useRef, useEffect, useCallback } from 'react'
import { useActionData, useLoaderData, useNavigate, useSubmit } from '@remix-run/react'
import { ActionFunction, json, LoaderFunction, redirect } from '@remix-run/node'
import { activateSeller<PERSON><PERSON>, createArea, getDistrictsAndStates } from '~/services/businessConsoleService'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@components/ui/card"
import { Button } from "@components/ui/button"
import { SellerArea } from '~/types/api/businessConsoleService/Areas'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { getSession } from "@utils/session.server"
import type { User } from "~/types"
import { ArrowLeft } from 'lucide-react'
import { useToast } from "@hooks/use-toast";
import { Input } from "@components/ui/input";
import { withAuth, withResponse } from "@utils/auth-utils";

interface LoaderData {
    states: string[];
    districts: { [state: string]: string[] };
    googleMapsApiKey: string;
}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
    try {

        const areas = (await getDistrictsAndStates(user.userId, request)).data
        const statesSet = new Set<string>()
        const districtsMap: { [state: string]: Set<string> } = {}

        areas.forEach((area: SellerArea) => {
            statesSet.add(area.state)
            if (!districtsMap[area.state]) {
                districtsMap[area.state] = new Set<string>()
            }
            districtsMap[area.state].add(area.district)
        })

        const states = Array.from(statesSet).sort()
        const districts = Object.fromEntries(
            Object.entries(districtsMap).map(([state, districtsSet]) => [
                state,
                Array.from(districtsSet).sort()
            ])
        )

        const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || ''

        return withResponse({ states, districts, googleMapsApiKey })
    } catch (error) {
        console.error('Failed to fetch districts and states:', error)
        return withResponse({ states: [], districts: {}, googleMapsApiKey: '' })
    }
}
)
const googleMapsPromise = {
    promise: null as Promise<void> | null,
    resolve: null as ((value: void) => void) | null,
    reject: null as ((reason?: any) => void) | null,
};

// Modified script loading function
const loadGoogleMapsScript = (apiKey: string) => {
    if (googleMapsPromise.promise) {
        return googleMapsPromise.promise;
    }

    googleMapsPromise.promise = new Promise((resolve, reject) => {
        googleMapsPromise.resolve = resolve;
        googleMapsPromise.reject = reject;

        if (window.google?.maps?.drawing) {
            resolve();
            return;
        }

        // Create callback function
        window.initMap = () => {
            if (googleMapsPromise.resolve) {
                googleMapsPromise.resolve();
            }
        };

        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=drawing,geometry&v=weekly&callback=initMap`;
        script.async = true;
        script.onerror = () => {
            if (googleMapsPromise.reject) {
                googleMapsPromise.reject(new Error('Failed to load Google Maps script'));
            }
        };
        document.head.appendChild(script);
    });

    return googleMapsPromise.promise;
};

interface MapProps {
    center: google.maps.LatLngLiteral;
    zoom: number;
    onPolygonComplete: (coordinates: google.maps.LatLngLiteral[]) => void;
    apiKey: string;
    existingCoordinates?: google.maps.LatLngLiteral[];
}

function Map({ center, zoom, onPolygonComplete, apiKey, existingCoordinates }: MapProps) {
    const ref = useRef<HTMLDivElement>(null);
    const [isLibraryLoaded, setIsLibraryLoaded] = useState(false);
    const mapRef = useRef<google.maps.Map | null>(null);
    const polygonRef = useRef<google.maps.Polygon | null>(null);
    const drawingManagerRef = useRef<google.maps.drawing.DrawingManager | null>(null);

    // Load the Google Maps script
    useEffect(() => {
        const loadMap = async () => {
            try {
                await loadGoogleMapsScript(apiKey);
                setIsLibraryLoaded(true);
            } catch (error) {
                console.error('Error loading Google Maps:', error);
            }
        };
        loadMap();
    }, [apiKey]);

    // Function to create or update polygon
    const updatePolygon = useCallback((coordinates: google.maps.LatLngLiteral[]) => {
        if (!mapRef.current || !google?.maps) return;

        // Remove existing polygon if it exists
        if (polygonRef.current) {
            polygonRef.current.setMap(null);
        }

        // Create new polygon
        polygonRef.current = new google.maps.Polygon({
            paths: coordinates,
            fillColor: "#4F46E5",
            fillOpacity: 0.3,
            strokeWeight: 2,
            strokeColor: "#4F46E5",
            editable: true,
            draggable: true,
            map: mapRef.current
        });

        // Add listeners for editing
        google.maps.event.addListener(polygonRef.current.getPath(), 'set_at', () => {
            if (!polygonRef.current) return;
            const newCoords = polygonRef.current.getPath().getArray().map(latLng => ({
                lat: latLng.lat(),
                lng: latLng.lng(),
            }));
            onPolygonComplete(newCoords);
        });

        google.maps.event.addListener(polygonRef.current.getPath(), 'insert_at', () => {
            if (!polygonRef.current) return;
            const newCoords = polygonRef.current.getPath().getArray().map(latLng => ({
                lat: latLng.lat(),
                lng: latLng.lng(),
            }));
            onPolygonComplete(newCoords);
        });
    }, [onPolygonComplete]);

    // Initialize map and drawing manager after library is loaded
    useEffect(() => {
        if (!isLibraryLoaded || !ref.current || !google?.maps) return;

        const map = new google.maps.Map(ref.current, {
            center,
            zoom,
            streetViewControl: false,
            mapTypeControl: false,
        });

        mapRef.current = map;

        // Set Bangalore bounds
        const bangaloreBounds = new google.maps.LatLngBounds(
            new google.maps.LatLng(12.864162, 77.438610),
            new google.maps.LatLng(13.139784, 77.711895)
        );
        map.fitBounds(bangaloreBounds);

        // Initialize DrawingManager
        const drawingManager = new google.maps.drawing.DrawingManager({
            drawingMode: google.maps.drawing.OverlayType.POLYGON,
            drawingControl: true,
            drawingControlOptions: {
                position: google.maps.ControlPosition.TOP_CENTER,
                drawingModes: [google.maps.drawing.OverlayType.POLYGON],
            },
            polygonOptions: {
                fillColor: "#4F46E5",
                fillOpacity: 0.3,
                strokeWeight: 2,
                strokeColor: "#4F46E5",
                editable: true,
                draggable: true,
            },
        });

        drawingManagerRef.current = drawingManager;
        drawingManager.setMap(map);

        // Add overlay complete listener
        google.maps.event.addListener(drawingManager, 'overlaycomplete', (event) => {
            if (event.type === google.maps.drawing.OverlayType.POLYGON) {
                const polygon = event.overlay as google.maps.Polygon;

                // Remove the temporary polygon
                polygon.setMap(null);

                drawingManager.setDrawingMode(null);

                const coordinates = polygon.getPath().getArray().map(latLng => ({
                    lat: latLng.lat(),
                    lng: latLng.lng(),
                }));

                // Create the persistent polygon
                updatePolygon(coordinates);
                onPolygonComplete(coordinates);
            }
        });

        // If there are existing coordinates, show them
        if (existingCoordinates && existingCoordinates.length > 0) {
            updatePolygon(existingCoordinates);
        }

        return () => {
            if (drawingManagerRef.current) {
                drawingManagerRef.current.setMap(null);
            }
            if (polygonRef.current) {
                polygonRef.current.setMap(null);
            }
        };
    }, [isLibraryLoaded, center, zoom, onPolygonComplete, existingCoordinates, updatePolygon]);

    return <div ref={ref} className="w-full h-96 rounded-lg" />;
}

interface ActionData {
    error?: string;
    success?: boolean;
}

export const action: ActionFunction = withAuth(async ({ request, user }) => {


    try {
        const formData = await request.formData();
        const name = formData.get("name") as string;
        const state = formData.get("state") as string;
        const district = formData.get("district") as string;
        const polygon = formData.get("polygon") as string;
        const radius = Number(formData.get("radius"));
        const latitude = Number(formData.get("latitude"))
        const longitude = Number(formData.get("longitude"))





        if (!name || !state || !district || !polygon) {
            return json({ error: "All fields are required" }, { status: 400 });
        }

        // First create the area
        const createAreaResponse = await createArea(user.userId, {
            name,
            state,
            district,
            polygon,
            radius: 0,
            latitude: 0,
            longitude: 0
        }, request);

        if (!createAreaResponse.data?.id) {
            throw new Error('Failed to get area ID from creation response');
        }

        // Then activate the seller area
        await activateSellerArea(user.userId, createAreaResponse.data.id, request);

        return redirect('/home/<USER>');
    } catch (error) {
        console.error('Error creating locality:', error);
        return json({
            error: "Failed to create locality. Please try again."
        }, { status: 500 });
    }
});

export default function NewLocality() {
    const navigate = useNavigate();
    const submit = useSubmit();
    const { states, districts, googleMapsApiKey } = useLoaderData<LoaderData>();
    const actionData = useActionData<ActionData>();
    const { toast } = useToast();

    const [selectedState, setSelectedState] = useState<string>('');
    const [selectedDistrict, setSelectedDistrict] = useState<string>('');
    const [localityName, setLocalityName] = useState<string>('');
    const [showMap, setShowMap] = useState(false);
    const [polygonCoordinates, setPolygonCoordinates] = useState<google.maps.LatLngLiteral[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleStateChange = (value: string) => {
        setSelectedState(value);
        setSelectedDistrict('');
        setShowMap(false);
        setPolygonCoordinates([]);
    };

    const handleDistrictChange = (value: string) => {
        setSelectedDistrict(value);
        setShowMap(true);
    };

    const handlePolygonComplete = useCallback((coordinates: google.maps.LatLngLiteral[]) => {
        setPolygonCoordinates(coordinates);
    }, []);

    const handleSave = () => {
        if (!localityName.trim()) {
            toast({
                title: "Error",
                description: "Please enter a locality name",
                variant: "destructive",
            });
            return;
        }

        if (!selectedState || !selectedDistrict) {
            toast({
                title: "Error",
                description: "Please select both state and district",
                variant: "destructive",
            });
            return;
        }

        if (polygonCoordinates.length < 3) {
            toast({
                title: "Error",
                description: "Please draw a valid polygon on the map",
                variant: "destructive",
            });
            return;
        }

        // Make sure the polygon is closed
        if (polygonCoordinates[0].lat !== polygonCoordinates[polygonCoordinates.length - 1].lat ||
            polygonCoordinates[0].lng !== polygonCoordinates[polygonCoordinates.length - 1].lng) {
            polygonCoordinates.push(polygonCoordinates[0]);
        }

        // Convert coordinates to path
        const path = new google.maps.MVCArray(
            polygonCoordinates.map(coord => new google.maps.LatLng(coord.lat, coord.lng))
        );

        // Encode the path using Google's utility
        const encodedPolyline = encodeURIComponent(google.maps.geometry.encoding.encodePath(path));

        const formData = new FormData();
        formData.set("name", localityName);
        formData.set("state", selectedState);
        formData.set("district", selectedDistrict);
        formData.set("polygon", encodedPolyline); // Send the encoded polyline directly

        setIsSubmitting(true);
        submit(formData, { method: "post" });
    };


    // Show error toast if action returns an error
    useEffect(() => {
        if (actionData?.error) {
            toast({
                title: "Error",
                description: actionData.error,
                variant: "destructive",
            });
        }
    }, [actionData, toast]);

    return (
        <div className="h-[calc(100vh-64px)] bg-white p-6 overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
                <button
                    onClick={() => navigate('/home/<USER>')}
                    className="p-2 hover:bg-gray-100 rounded-full"
                >
                    <ArrowLeft size={24} />
                </button>
            </div>

            <Card className="w-full max-w-2xl mx-auto">
                <CardHeader>
                    <CardTitle>Add New Locality</CardTitle>
                    <CardDescription>
                        Enter locality details and draw the boundary on the map
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Locality Name</label>
                            <Input
                                placeholder="Enter locality name"
                                value={localityName}
                                onChange={(e) => setLocalityName(e.target.value)}
                            />
                        </div>


                        <div className="space-y-2">
                            <label className="text-sm font-medium">State</label>
                            <Select value={selectedState} onValueChange={handleStateChange}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select a state" />
                                </SelectTrigger>
                                <SelectContent>
                                    {states.map((state) => (
                                        <SelectItem key={state} value={state}>{state}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm font-medium">District</label>
                            <Select
                                value={selectedDistrict}
                                onValueChange={handleDistrictChange}
                                disabled={!selectedState}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select a district" />
                                </SelectTrigger>
                                <SelectContent>
                                    {selectedState && districts[selectedState]?.map((district) => (
                                        <SelectItem key={district} value={district}>{district}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {showMap && (
                            <div className="space-y-4">
                                <Map
                                    center={{ lat: 12.9716, lng: 77.5946 }}
                                    zoom={11}
                                    onPolygonComplete={handlePolygonComplete}
                                    apiKey={googleMapsApiKey}
                                    existingCoordinates={polygonCoordinates}
                                />
                                <Button
                                    onClick={handleSave}
                                    disabled={isSubmitting || polygonCoordinates.length < 3 || !localityName.trim()}
                                    className="w-full"
                                >
                                    {isSubmitting ? "Creating..." : "Save Locality"}
                                </Button>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}


