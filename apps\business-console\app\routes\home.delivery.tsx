import { useState } from "react";
import { format, isToday, isTomorrow, parseISO } from "date-fns";
import { AlertTriangle, Truck } from "lucide-react";

import { Button } from "@components/ui/button";
import { Card, CardContent } from "@components/ui/card";
import { Switch } from "@components/ui/switch";
import { Label } from "@components/ui/label";

type Slot = {
    date: string;
    timeSlot: string;
    orders: number;
    tonnage: number;
    trucks: number;
    deliveryPercentage: number;
    credit: number;
    cashCollected: number;
    routePlanningPending?: boolean;
    isOrdering: boolean;
};

const mockData: Slot[] = [
    { date: "2024-10-23", timeSlot: "07:00 AM - 10:00 AM", orders: 50, tonnage: 5000, trucks: 5, deliveryPercentage: 55, credit: 7600, cashCollected: 76000, isOrdering: false },
    { date: "2024-10-24", timeSlot: "10:00 AM - 01:00 PM", orders: 30, tonnage: 3000, trucks: 3, deliveryPercentage: 60, credit: 5000, cashCollected: 50000, isOrdering: false },
    { date: "2024-10-25", timeSlot: "01:00 PM - 04:00 PM", orders: 40, tonnage: 4000, trucks: 4, deliveryPercentage: 70, credit: 6000, cashCollected: 60000, routePlanningPending: true, isOrdering: false },
];

export default function HomeDelivery() {
    const [slots, setSlots] = useState<Slot[]>(mockData);

    const toggleOrdering = (index: number) => {
        setSlots(prevSlots =>
            prevSlots.map((slot, i) =>
                i === index ? { ...slot, isOrdering: !slot.isOrdering } : slot
            )
        );
    };

    if (slots.length === 0) {
        return <div>No delivery slots available.</div>;
    }

    return (
        <div className="container mx-auto p-4">
            <h1 className="text-2xl font-bold mb-6">HomeDelivery Slots</h1>
            <div className="space-y-6">
                {slots.map((slot, index) => (
                    <DeliverySlot key={index} slot={slot} onToggleOrdering={() => toggleOrdering(index)} />
                ))}
            </div>
        </div>
    );
}

type DeliverySlotProps = {
    slot: Slot;
    onToggleOrdering: () => void;
};

function DeliverySlot({ slot, onToggleOrdering }: DeliverySlotProps) {
    const slotDate = parseISO(slot.date);
    let dateDisplay;
    if (isToday(slotDate)) {
        dateDisplay = "Today";
    } else if (isTomorrow(slotDate)) {
        dateDisplay = "Tomorrow";
    } else {
        dateDisplay = format(slotDate, "MMM d, yyyy");
    }

    return (
        <Card>
            <CardContent className="p-4">
                <div className="flex flex-wrap justify-between items-center mb-4">
                    <h2 className="text-lg font-semibold">
                        Slot: {dateDisplay} {slot.timeSlot}
                    </h2>
                    <div className="flex items-center space-x-2 mt-2 sm:mt-0">
                        <Switch
                            id={`ordering-mode-${slot.date}`}
                            checked={slot.isOrdering}
                            onCheckedChange={onToggleOrdering}
                        />
                        <Label htmlFor={`ordering-mode-${slot.date}`}>Ordering</Label>
                    </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                    <StatSquare label="Orders" value={slot.orders} />
                    <StatSquare label="Tonnage" value={`${slot.tonnage} KG`} />
                    <StatSquare label="Trucks" value={slot.trucks} icon={<Truck className="w-4 h-4" />} />
                    <StatSquare label="HomeDelivery%" value={`${slot.deliveryPercentage}%`} />
                    <StatSquare label="Credit" value={`₹${slot.credit.toLocaleString()}`} />
                    <StatSquare label="Cash collected" value={`₹${slot.cashCollected.toLocaleString()}`} />
                </div>
                {slot.routePlanningPending && (
                    <WarningSquare label="Route planning Pending" className="mt-4" />
                )}
                <div className="flex justify-end mt-4">
                    <Button variant="outline">View/Edit Items</Button>
                </div>
            </CardContent>
        </Card>
    );
}

type StatSquareProps = {
    label: string;
    value: string | number;
    icon?: React.ReactNode;
};

function StatSquare({ label, value, icon = null }: StatSquareProps) {
    return (
        <div className="bg-muted p-3 rounded">
            <div className="text-sm text-muted-foreground mb-1 flex items-center gap-1">
                {icon}
                {label}
            </div>
            <div className="font-semibold">{value}</div>
        </div>
    );
}

type WarningSquareProps = {
    label: string;
    className?: string;
};

function WarningSquare({ label, className = "" }: WarningSquareProps) {
    return (
        <div className={`bg-red-100 p-3 rounded flex items-center space-x-2 ${className}`}>
            <AlertTriangle className="text-red-500 w-5 h-5" />
            <span className="text-sm font-medium text-red-700">{label}</span>
        </div>
    );
}
