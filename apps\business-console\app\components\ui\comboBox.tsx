import { useState } from "react";
import { Popover, <PERSON>over<PERSON>ontent, PopoverTrigger } from "./popover";
import { Button } from "./button";
import { cn } from "lib/utils";
import { ChevronsUpDown, Check } from "lucide-react";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "./command";

interface Option {
      id: string;
      name: string;
}

interface ComboBoxProps {
      options: Option[];
      value: string | undefined;
      selections: string[];
      onChange: (value: string[]) => void;
}

export function ComboBox({ selections, onChange, options, value }: ComboBoxProps) {
      const [open, setOpen] = useState(false);
      const [searchTerm, setSearchTerm] = useState(""); // Local state for searching

      const filteredFrameworks = options.filter((option) =>
            option.name.toLowerCase().includes(searchTerm.toLowerCase())
      );

      const handleSelect = (value: string) => {
            const updatedSelections = selections.includes(value)
                  ? selections.filter((item) => item !== value)
                  : [...selections, value];
            onChange(updatedSelections);
      };

      return (
            <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild>
                        <Button variant="outline" role="combobox" aria-expanded={open} className="w-[200px] justify-between">
                              {selections.length > 0
                                    ? `Selected (${selections.length})`
                                    : "Select frameworks..."}
                              <ChevronsUpDown className="opacity-50" />
                        </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[200px] p-0">
                        <Command>
                              <CommandInput
                                    placeholder="Search frameworks..."
                                    value={searchTerm}
                                    onValueChange={(term) => setSearchTerm(term)} // Update local search term
                              />
                              <CommandList>
                                    <CommandEmpty>No Items Found</CommandEmpty>
                                    <CommandGroup>
                                          {filteredFrameworks.map((framework: Option) => (
                                                <CommandItem
                                                      key={framework.id}
                                                      onSelect={() => handleSelect(framework.id)} // Correct selection handling
                                                >
                                                      <span>{framework.name}</span>
                                                      {selections.includes(framework.id) && (
                                                            <Check
                                                                  className={cn(
                                                                        "ml-auto",
                                                                        selections.includes(framework.id) ? "opacity-100" : "opacity-0"
                                                                  )}
                                                            />
                                                      )}
                                                </CommandItem>
                                          ))}
                                    </CommandGroup>
                              </CommandList>
                        </Command>
                  </PopoverContent>
            </Popover>
      );
}


