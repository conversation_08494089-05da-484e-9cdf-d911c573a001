import { ApiResponse } from "~/types/api/Api";
import { API_BASE_URL, apiRequest } from "~/utils/api";
import type { Customer, CustomerGroup, SearchParams, SendMessagePayload, Template } from "~/schemas/marketing.schemas";
import { getSellerItems } from "./myItems";

// Mock data for templates
const mockTemplates: Template[] = [
  {
    id: 1,
    name: "Shop_Open",
    type: "hybrid",
    phone: "+91 - 979823210",
    preview: "🏪 Order Window Now Open!\n\nYour favorite shop has just opened their order window! ✨\n✅ Fresh produce\n✅ Top-quality items\n✅ Delivered straight to your doorstep\n\n⏰ Don't wait—place your order before it closes!",
    lastUsed: "3 weeks ago",
    variables: [],
  },
  {
    id: 2,
    name: "Item_Discount",
    type: "hybrid",
    phone: "+91 - 979823210",
    preview: "🎉 Exclusive Offer Just for You!\n\nGrab a limited-time deal on {{Item_Name}} now! 🎁\nSave more while enjoying the best quality products.",
    lastUsed: "3 weeks ago",
    variables: ["Item_Name"],
  },
];

// Mock data for customer groups
const mockCustomerGroups: CustomerGroup[] = [
  { id: "all", name: "All Customers", count: 1400 },
  { id: "loyal", name: "Loyal Customers", count: 100 },
  { id: "inactive", name: "Inactive Customers", count: 400 },
  { id: "lost", name: "Lost Customers", count: 400 },
  { id: "custom", name: "Custom Selection", count: 0 },
];

// Mock data for customers
const mockCustomers: Customer[] = [
  {
    id: 1,
    name: "John Doe",
    phone: "+91 9876543210",
    email: "<EMAIL>",
    address: "123 Main St",
    lastOrder: "2024-03-15",
  },
  {
    id: 2,
    name: "Jane Smith",
    phone: "+91 9876543211",
    email: "<EMAIL>",
    address: "456 Oak St",
    lastOrder: "2024-03-18",
  },
];

export async function getTemplates(params: SearchParams, request?: Request): Promise<ApiResponse<Template[]>> {
  // Mock pagination and search
  let templates = [...mockTemplates];
  
  if (params.search) {
    templates = templates.filter(template => 
      template.name.toLowerCase().includes(params.search!.toLowerCase()) ||
      template.preview.toLowerCase().includes(params.search!.toLowerCase())
    );
  }

  const start = (params.page - 1) * params.pageSize;
  const end = start + params.pageSize;
  const paginatedTemplates = templates.slice(start, end);

  return {
    data: paginatedTemplates,
    statusCode: 200,
    totalCount: templates.length,
    currentPage: params.page,
    pageSize: params.pageSize,
  };
}

export async function getCustomerGroups(request?: Request): Promise<ApiResponse<CustomerGroup[]>> {
  return {
    data: mockCustomerGroups,
    statusCode: 200,
  };
}

export async function getCustomers(params: SearchParams, request?: Request): Promise<ApiResponse<Customer[]>> {
  let customers = [...mockCustomers];
  
  if (params.search) {
    customers = customers.filter(customer => 
      customer.name.toLowerCase().includes(params.search!.toLowerCase()) ||
      customer.phone.includes(params.search!) ||
      customer.email?.toLowerCase().includes(params.search!.toLowerCase())
    );
  }

  const start = (params.page - 1) * params.pageSize;
  const end = start + params.pageSize;
  const paginatedCustomers = customers.slice(start, end);

  return {
    data: paginatedCustomers,
    statusCode: 200,
    totalCount: customers.length,
    currentPage: params.page,
    pageSize: params.pageSize,
  };
}

export async function sendMessage(data: SendMessagePayload, request?: Request): Promise<ApiResponse<void>> {
  // Mock API call
  console.log("Sending message:", data);
  
  return {
    statusCode: 200,
    data: undefined,
  };
}

// Re-export item service for convenience
export { getSellerItems }; 