// import { logger } from '@utils/logger.utils';
// import { config } from '@/utils/config.utils';
import { UserRepository } from '@repository/dynamoDB/user.repository.js';
import { FcmTokenDetails, User, DeviceId } from '@entity/dynamoDB/users.entity.js';
import { IServiceResponse } from '@/interfaces/service.interface.js';
import * as admin from 'firebase-admin';
import { initializeApp, cert, getApps } from 'firebase-admin/app';
import { getMessaging, Messaging } from 'firebase-admin/messaging';
import logger from '@utils/express-logger.js'
import { NotificationLogService } from './notificationLogService.js';
import { NotificationStatus } from '../database/entities/NotificationLog.js';



class FirebaseApiService {
    private readonly userRepository: UserRepository;
    private messaging:  Messaging;
    private notificationLogService: NotificationLogService;

    constructor() {
        this.userRepository = new UserRepository('users');
        this.notificationLogService = new NotificationLogService();

**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        // Initialize Firebase Admin SDK
        initializeApp({
            credential: cert({
                projectId: process.env.SELLER_APP_NOTIFICATION_FIREBASE_PROJECT_ID,
                clientEmail: process.env.SELLER_APP_NOTIFICATION_FIREBASE_CLIENT_EMAIL,
                privateKey: privateKey,
            })
        })

        this.messaging = getMessaging(); 
    }

    async registerUser(mobileNumber: string, token: string, deviceId: DeviceId, lastUpdated: number): Promise<IServiceResponse<{expiresAt: number}>> {
        try {
            const existingUser = await this.userRepository.getUserByMobileNumber(mobileNumber);
            logger.info(`existingUser  ${existingUser}`);

            const expiresAt = Date.now() + 60 * 60 * 24 * 30 * 1000;
            const expiresAtISO = new Date(expiresAt).toISOString();
            const deviceIdV2 = `${deviceId._h}-${deviceId._i}-${deviceId._j}-${deviceId._k}`;
            
            const fcmToken: FcmTokenDetails = {
                token,
                lastUpdated: lastUpdated,
                lastUpdatedISO: new Date(lastUpdated).toISOString(),
                deviceId,
                deviceIdV2,
                expiresAt,
                expiresAtISO,
            }

            if (existingUser) {
                // Remove any existing tokens with the same deviceId
                if (existingUser.fcmTokens) {
                    const tokensToRemove = existingUser.fcmTokens.filter(t => {
                        if (!t.deviceId) return false;
                        return t.deviceId._j === deviceId._j;
                    });

                    if (tokensToRemove.length > 0) {
                        logger.info(`Removing ${tokensToRemove.length} existing tokens for deviceId._j: ${deviceId._j}`);
                        await this.userRepository.removeFcmTokens(mobileNumber, tokensToRemove);
                    }
                }
                
                // Add the new token
                const user = await this.userRepository.addFcmToken(mobileNumber, fcmToken);
                return {
                    ok: true,
                    data: {
                        expiresAt
                    }
                };
            } else {
                const newUser: User = {
                    mobileNumber,
                    fcmTokens: [fcmToken],
                };
                await this.userRepository.saveUser(newUser);
                return {
                    ok: true,
                    data: {
                        expiresAt
                    }
                };
            }
        } catch (err) {
            const error = err as Error;
            logger.error(error);
            logger.error(`Error registering user:' ${JSON.stringify(error.message)}`);
            return { ok: false };
        }
    }

    async sendNotification(mobileNumbers: string[], title = 'Notification', body: string, data: unknown): Promise<IServiceResponse> {
        if (mobileNumbers.length === 0) {
            return {
                ok: true,
            };
        }

        for await (const mobileNumber of mobileNumbers) {
            try {
                await this.sendNotificationToEachUser(mobileNumber, body, title, data);
            } catch (err) {
                const error = err as unknown;
                logger.error(`Error sending notification to ${mobileNumber}: ${JSON.stringify(err)}`);
            }
        }

        return {
            ok: true,
        };

    }
    catch(error: any) {
        logger.error(`Error sending notification:' ${JSON.stringify(error)}`);
        return {
            ok: false,
        };
    }

    async sendNotificationToEachUser(
        mobileNumber: string,
        body: string,
        title = 'Notification',
        data: unknown,
    ): Promise<IServiceResponse> {
        const user = await this.userRepository.getUserByMobileNumber(mobileNumber);
        if (!user) {
            throw new Error('User not found');
        }

        if (!user.fcmTokens || user.fcmTokens.length === 0) {
            throw new Error('No FCM tokens available for this user');
        }

        try {
            const failedTokens: FcmTokenDetails[] = [];

            // Send notifications to each token
            await Promise.all(
                user.fcmTokens.map(async (token) => {
                    let notificationLog;
                    try {
                        logger.info(`Sending notification to ${JSON.stringify({mobileNumber, token})}`);
                        
                        const message: admin.messaging.Message = {
                            token: token.token,
                            notification: {
                                title,
                                body
                            },
                            android: {
                                notification: {
                                    // sound: (data as Record<string, any>)?.ondcDomain === "RET11" ? "urgent_order.mp3" : "urgent_order.mp3",
                                    priority: "high",
                                    // channelId: "urgent-orders",
                                    channelId: (data as Record<string, any>)?.ondcDomain === "RET11" ? "urgent-orders" : "order-alerts",
                                }
                            },
                            data: Object.entries(data as Record<string, any>).reduce((acc, [key, value]) => ({
                                ...acc,
                                [key]: String(value)
                            }), {})
                        };
                        
                        // Create initial notification log
                        notificationLog = await this.notificationLogService.logFirebaseNotification(
                            mobileNumber, // businessId
                            mobileNumber, // mobileNumber
                            token.token, // recipient
                            JSON.stringify({ title, body }), // inputPayload
                            JSON.stringify(message), // providerRequest
                            null, // providerResponse
                            NotificationStatus.PENDING, // status
                            '' // errorMessage (optional)
                        );
                        
                        const response = await this.messaging.send(message);

                        // Update notification log with success
                        await this.notificationLogService.updateNotificationStatus(
                            notificationLog.notificationId,
                            notificationLog.timestamp,
                            NotificationStatus.SENT,
                            response,
                            '' // Empty string for success case
                        );

                        logger.info(`Notification sent successfully to ${JSON.stringify({mobileNumber, token, response})}`);
                    } catch (error: any) {
                        logger.error(`Failed to send notification ${JSON.stringify({mobileNumber, token, error})}`);
                        
                        // Update notification log with failure
                        if (notificationLog) {
                            // Convert error to a plain object that DynamoDB can store
                            const errorObject: Record<string, any> = {
                                message: error.message || 'Unknown error',
                                code: error.code || 'UNKNOWN',
                                name: error.name,
                                // Convert Firebase specific error info to plain object
                                errorInfo: error.errorInfo ? {
                                    code: error.errorInfo.code,
                                    message: error.errorInfo.message,
                                    ...Object.fromEntries(
                                        Object.entries(error.errorInfo)
                                            .filter(([_, v]) => typeof v !== 'function')
                                    )
                                } : undefined
                            };

                            await this.notificationLogService.updateNotificationStatus(
                                notificationLog.notificationId,
                                notificationLog.timestamp,
                                NotificationStatus.FAILED,
                                errorObject,
                                error.message || 'Unknown error occurred'
                            );
                        }
                        
                        // Only add token to failedTokens if it's an invalid token error
                        if (error.code === 'messaging/invalid-registration-token' || 
                            error.code === 'messaging/registration-token-not-registered' || 
                            error.code === 'messaging/unregistered') {
                            failedTokens.push(token);
                            logger.info(`Token marked for deletion due to invalid token error: ${error.code}`);
                        }
                    }
                })
            );

            // Remove all failed tokens in one operation
            if (failedTokens.length > 0) {
                logger.info(`Removing ${failedTokens.length} failed tokens for user ${mobileNumber}`);
                await this.userRepository.removeFcmTokens(mobileNumber, failedTokens);
            }

            const successCount = user.fcmTokens.length - failedTokens.length;
            logger.info(`Notification sent successfully to ${successCount} tokens`);

            return {
                ok: true,
                data: { msg: `Notification sent successfully to ${successCount} tokens` },
            };
        } catch (error: any) {
            logger.error(`Error sending notification:' ${JSON.stringify(error)}`);
            return {
                ok: false,
            };
        }
    }

    async removeFcmToken(mobileNumber: string, fcmToken: FcmTokenDetails): Promise<void> {
        try {
            logger.info(`Removing FcmToken for mobile: ${JSON.stringify({mobileNumber, fcmToken})}`);
            await this.userRepository.removeFcmToken(mobileNumber, fcmToken);
        } catch (error) {
            logger.error(`Error removing FCM token:' ${JSON.stringify(error)}`);
        }
    }
}

export default FirebaseApiService;
