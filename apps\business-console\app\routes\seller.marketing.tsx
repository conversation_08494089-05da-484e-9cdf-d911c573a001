// import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@components/ui/tabs";
import { Outlet, useLocation } from "@remix-run/react";

export default function MarketingLayout() {
  const location = useLocation();
  const activeTab = location.pathname.split("/").pop() || "templates";

  return (
    <div className="container mx-auto p-4 space-y-4">
      <div className="flex flex-col space-y-4">
        <div className="flex items-center gap-4">
          {/* {activeTab === "templates" && (
            <Button variant="ghost" size="icon" className="md:hidden">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          )} */}
          <h1 className="text-2xl font-bold">
            {activeTab === "templates" ? "Template Management" : "Marketing"}
          </h1>
        </div>

        {/* <Tabs value={activeTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-4">
            <Link to="campaigns">
              <TabsTrigger value="campaigns" className="w-full">
                Campaigns
              </TabsTrigger>
            </Link>
            <Link to="templates">
              <TabsTrigger value="templates" className="w-full">
                Templates
              </TabsTrigger>
            </Link>
            <Link to="analytics">
              <TabsTrigger value="analytics" className="w-full">
                Analytics
              </TabsTrigger>
            </Link>
          </TabsList>
        </Tabs> */}

        <Outlet />
      </div>
    </div>
  );
} 