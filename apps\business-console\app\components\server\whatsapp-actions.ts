// File: app/server/whatsapp-actions.ts
import { json } from "@remix-run/node";
import { getFirebaseAdmin } from "~/services/firebase.server";
import {User} from "~/types";

export async function handleDelink(user: User) {
    try {
        const db = getFirebaseAdmin();
        await db.collection('facebook-connects').doc(user.userDetails.sellerId.toString()).delete();
        return json({
            success: true,
            message: 'Successfully delinked WhatsApp Business account',
            timestamp: new Date().toISOString()
        });
    } catch (error: any) {
        return json({
            error: error.message,
            stage: 'delink',
            timestamp: new Date().toISOString()
        }, {status: 500});
    }
}

export async function handleTestDataInsertion(user: User) {
    try {
        const db = getFirebaseAdmin();
        const testData = {
            access: {
                access_token: `test_token_${Date.now()}`,
                token_type: "Bearer",
                expires_in: 3600,
                test_data: true,
                inserted_at: new Date().toISOString()
            },
            userId: user.userId,
            userName: user.userName,
            businessName: user.businessName,
            buyerId: user.buyerId,
            sellerId: user.userDetails.sellerId,
            updatedAt: new Date().toISOString(),
            isTestData: true
        };

        await db.collection('facebook-connects').doc(user.userDetails.sellerId.toString()).set(testData);

        return json({
            success: true,
            message: 'Test data inserted successfully',
            data: testData,
            timestamp: new Date().toISOString()
        });
    } catch (error: any) {
        return json({
            error: error.message,
            stage: 'test_data_insertion',
            timestamp: new Date().toISOString()
        }, {status: 500});
    }
}

export async function handleFacebookAuth(formData: FormData, user: User) {
    const code = formData.get('code');

    if (!code) {
        return json({
            error: 'Code is required',
            stage: 'validation',
            timestamp: new Date().toISOString()
        }, {status: 400});
    }

    try {
        const tokenUrl = new URL('https://graph.facebook.com/v21.0/oauth/access_token');
        tokenUrl.searchParams.append('client_id', process.env.FACEBOOK_APP_ID!);
        tokenUrl.searchParams.append('client_secret', process.env.FACEBOOK_APP_SECRET!);
        tokenUrl.searchParams.append('code', code.toString());

        const response = await fetch(tokenUrl.toString());

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.access_token) {
            const db = getFirebaseAdmin();
            await db.collection('facebook-connects').doc(user.userDetails.sellerId.toString()).set({
                access: data,
                userId: user.userId,
                userName: user.userName,
                businessName: user.businessName,
                buyerId: user.buyerId,
                sellerId: user.userDetails.sellerId,
                updatedAt: new Date().toISOString()
            });
        }

        return json(data);
    } catch (error: any) {
        return json({
            error: error.message,
            stage: 'facebook_api',
            timestamp: new Date().toISOString()
        }, {status: 500});
    }
}
