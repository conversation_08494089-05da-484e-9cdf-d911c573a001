import { <PERSON>, json, useFetcher, use<PERSON>oaderD<PERSON>, useNavigate } from "@remix-run/react";
import { ChevronRight, Edit, ListFilter, Pencil } from "lucide-react";
import { useEffect, useState } from "react";
import { CategoryItem } from "~/components/masterItems/searchableCategories";
import AddCategoryItem from "~/components/ui/addCategoryItem";
import AddMasterCategory from "~/components/ui/addMasterCategory";
import { Card, CardContent } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "~/components/ui/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { useDebounce } from "~/hooks/useDebounce";
import { getSelectedChildCategory, getSelectedMasterCatItems, getSelectedMasterItemCategories, getUploadedUrl, updateAttributes, updateMasterItem, UpdateMasterItemCategory } from "~/services/masterItemCategories";
import s3Service from "~/services/s3.service";
import { ItemsList, MasterItemCategories } from "~/types/api/businessConsoleService/MasterItemCategory";
import { withAuth, withResponse } from "~/utils/auth-utils";
interface LoaderData {
      selectedId: number,
      name: string,
      items: ItemsList[],
      categoryDetail: MasterItemCategories,
      parentCategory: MasterItemCategories[],
      categoryList: MasterItemCategories[],



}

export const loader = withAuth(async ({ user, request }) => {
      const url = new URL(request.url);
      const selectediCId = Number(url.searchParams.get("selectedICId"));
      const level = Number(url.searchParams.get("Level"))

      const name = (url.searchParams.get("name"));
      const activeTab = url.searchParams.get("activeTab") || level === 1 && "Items"
      const searchTerm = url.searchParams.get("searchTerm") || "";
      const page = Number(url.searchParams.get("page")) || 0;
      const pageSize = Number(url.searchParams.get("pageSize")) || 10;


      try {
            let response: any;
            let categoryList: MasterItemCategories[] | [] = [];
            let items: ItemsList[] | [] = []
            const categoryDetail = await getSelectedMasterItemCategories(selectediCId, request);
            switch (activeTab) {

                  case "Items":
                        response = await getSelectedMasterCatItems(
                              selectediCId,
                              request,
                              page,
                              pageSize,
                              searchTerm
                        );
                        if (response.data?.length) {
                              items = response.data;
                        }
                        break;


                  case "childCategories":
                        response = await getSelectedChildCategory(selectediCId,
                              request,
                              page,
                              pageSize,
                              searchTerm);
                        if (response.data?.length) {
                              categoryList = response.data;
                        }
                        break;
            }
            return withResponse({
                  categoryDetail: categoryDetail.data,
                  categoryList,
                  parentCategory: categoryDetail.data.parentCategories,
                  items,
                  selectedId: selectediCId,
                  name: name,
            }, categoryDetail?.headers);
      }
      catch (error) {
            if (error instanceof Response && error.status === 404) {
                  throw json({ error: "MasterItemCategory pg Not found" }, { status: 404 });
            }
            throw new Response("Failed to fetch MasterItemCategory ", { status: 500 });
      }
})
export const action = withAuth(async ({ user, request }) => {
      const formData = await request.formData();
      const intent = formData.get("_intent");
      const categoryName = formData.get("categoryName") as string;
      const categoryLevel = formData.get("categoryLevel") as unknown as number;
      const picture = formData.get("imageUrl") as string;
      const parentId = formData.get("parentId");
      const icId = formData.get("icId") as unknown as number;
      const mode = formData.get("mode") as string;
      const requestBody = formData.get("requestBody");
      const sequence = formData.get("sequence") as unknown as number;





      let parsedParentCat = [];
      if (typeof parentId === "string" && parentId.trim() !== "") {
            try {
                  parsedParentCat = JSON.parse(parentId);
                  console.log(parsedParentCat, "9090999000")
            } catch (error) {
                  console.error("Error parsing parentId:", error);
                  parsedParentCat = []; // Fallback to an empty array
            }
      } else {
            console.warn("parentId is not a valid JSON string or is empty.");
      }


      if (intent === "edit") {
            let parsedRequestBody: any;

            if (requestBody && typeof requestBody === "string") {
                  try {
                        parsedRequestBody = JSON.parse(requestBody) as ItemsList;

                  } catch (error) {
                        console.error("Error parsing requestBody:", error);
                        throw json({ error: "Invalid requestBody format" }, { status: 400 });
                  }
            }
            const itemId = formData.get("itemId");
            if (!itemId) {
                  throw json({ error: "Item ID is required for editing" }, { status: 400 });
            }
            const response = await updateMasterItem(Number(itemId), parsedRequestBody, request);

            return withResponse({ success: true, intent }, response.headers);
      }



      if (formData.get("_action") === "uploadImage") {
            try {
                  const file = formData.get("file");
                  console.log("Received file:", {
                        type: file?.constructor.name,
                        isBlob: file instanceof Blob,
                        size: file instanceof Blob ? file.size : 'N/A',
                        contentType: file instanceof Blob ? file.type : 'N/A'
                  });

                  if (!file || !(file instanceof Blob)) {
                        return json({ success: false, error: "No file provided" }, { status: 400 });
                  }

                  // Validate file size
                  const MAX_FILE_SIZE = 500 * 1024 * 1024; // 5MB
                  if (file.size > MAX_FILE_SIZE) {
                        return json({
                              success: false,
                              error: "File size exceeds 5MB limit"
                        }, { status: 400 });
                  }

                  // Read file as buffer
                  const arrayBuffer = await file.arrayBuffer();
                  const buffer = Buffer.from(arrayBuffer);

                  const fileUrl = await s3Service.uploadFile({
                        file: buffer,
                        fileName: (file as File).name || 'image.jpg',
                        contentType: file.type || 'image/jpeg',
                  });

                  return json({ success: true, fileUrl });
            } catch (error) {
                  console.error("File upload error:", error);
                  if (error instanceof Error) {
                        return json({
                              success: false,
                              error: error.message || "Failed to upload file"
                        }, { status: 500 });
                  }
                  return json({
                        success: false,
                        error: "An unexpected error occurred while uploading the file"
                  }, { status: 500 });
            }
      }
      else if (intent === "_createCategory") {
            try {
                  const response = await UpdateMasterItemCategory(categoryName, categoryLevel, sequence, picture, parsedParentCat, request, mode, icId);
                  return withResponse({ data: response.data }, response.headers);

            } catch (error) {
                  return json({ error: "Something went wrong" }, { status: 500 });
            }
      }

      return json({ error: "No valid action found" }, { status: 400 });
});

export default function SelectedCategory() {
      const navigate = useNavigate()
      const { categoryList, items, selectedId, name, categoryDetail, parentCategory } = useLoaderData<LoaderData>()
      const [activeTab, setActiveTab] = useState(categoryDetail.level === 1 ? 'Items' : "parentCategories")
      const [searchTerm, setSearchTerm] = useState('')
      const [currentPage, setCurrentPage] = useState(0)
      const fetcher = useFetcher()
      const debouncedSearchTerm = useDebounce(searchTerm, 500);

      useEffect(() => {
            if (debouncedSearchTerm.length >= 3) {
                  navigate(
                        `/home/<USER>
                  );
            } else if (debouncedSearchTerm === "") {
                  // Clear searchTerm and reset to base URL to show all data
                  navigate(
                        `/home/<USER>
                  );
            }
      }, [debouncedSearchTerm, navigate, selectedId, name, activeTab, currentPage]);

      const handleTabChange = (newTab: string) => {
            setActiveTab(newTab);
            navigate(`/home/<USER>
      };
      const handlePageChange = (newPage: number) => {
            setCurrentPage(newPage)
            navigate(`/home/<USER>
      };



      const handleSubmit = (selectedLevel: number, categoryName: string, sequence: number, uploadedImageUrl: string, parentId: CategoryItem[], closeDialog: () => void, mode?: string, icId?: number) => {
            const formData = new FormData();
            formData.append("categoryName", categoryName)
            formData.append("categoryLevel", selectedLevel.toString())
            formData.append("sequence", sequence.toString())

            formData.append("imageUrl", uploadedImageUrl)
            formData.append("parentId", JSON.stringify(parentId))
            formData.append("icId", icId as unknown as string);
            formData.append("mode", mode as string);
            formData.append("_intent", "_createCategory")
            fetcher.submit(formData, { method: "POST" })
            closeDialog()
      }


      const handleEdit = (itemId: number, parentId: CategoryItem[], requestBody: ItemsList, closeDialog: () => void) => {
            const formData = new FormData();

            formData.append("parentId", JSON.stringify(parentId))
            formData.append("requestBody", JSON.stringify(requestBody))

            formData.append("itemId", itemId as unknown as string);
            formData.append("_intent", "edit")
            fetcher.submit(formData, { method: "PUT" })
            closeDialog()
      }
      return (
            <div className="container mx-auto p-6">
                  <div className="flex items-center gap-2 mb-6">
                        <span className="font-semibold cursor-pointer" onClick={() => navigate("/home/<USER>")}> Categories
                        </span>
                        <ChevronRight />
                        <span className="font-semibold">{name}</span>
                  </div>
                  <Card>
                        <CardContent className="flex flex-col md:flex-row justify-between my-3 gap-4">

                              <div className="flex flex-col md:flex-row gap-5">
                                    <img
                                          src={categoryDetail?.picture}
                                          alt="ItemImage"
                                          className="h-10 w-10 self-center md:self-auto mt-4"
                                    />
                                    <div>
                                          {categoryDetail?.name && <p className=" text-md flex gap-1">
                                                Cat.Name: <p className="font-bold text-md">{categoryDetail?.name}</p>
                                          </p>}
                                          {categoryDetail?.level && <p className="text-md flex gap-1">
                                                Cat.Level: <p className="font-bold text-md">{categoryDetail?.level === 1 ? "L1" : categoryDetail.level === 2 ? "L2" : categoryDetail.level === 3 ? "L3" : "-"}</p>

                                          </p>}
                                          {categoryDetail?.parentCategories?.length >= 0 && <p className="text-md flex gap-1">
                                                Parent Categories:
                                                <div className="flex flex-col gap-1">
                                                      {categoryDetail?.parentCategories?.length > 0 ? (
                                                            categoryDetail.parentCategories.map((parent, index) => (
                                                                  <span key={index} className="font-bold text-md"> {index + 1}.{parent.name}</span>
                                                            ))
                                                      ) : (
                                                            "-"
                                                      )}
                                                </div>
                                          </p>}
                                    </div>
                              </div>
                              <div className="flex flex-col gap-3 items-start md:items-end mr-0 md:mr-20">
                                    <p className="font-semibold text-md flex gap-2 items-center">

                                          <AddMasterCategory buttonName="Edit" categoryDetails={categoryDetail}
                                                handleSubmit={handleSubmit} mode="Edit" />
                                    </p>
                                    {categoryDetail?.totalItems > 0 && <p className="text-md flex gap-1">
                                          Total Items:<p className="font-semibold text-md">{categoryDetail?.totalItems} </p>

                                    </p>}
                                    {/* <p className="text-md flex gap-1">
                                          Total Sellers:<p className="font-semibold text-md">{data} </p>
                                    </p> */}
                              </div>
                        </CardContent>
                  </Card>

                  <Tabs value={activeTab} onValueChange={handleTabChange} className="my-5">
                        <div className="flex justify-between">
                              <TabsList>
                                    {categoryDetail.level === 1 && <TabsTrigger value="Items">Items</TabsTrigger>}
                                    <TabsTrigger value="parentCategories">Parent Categories</TabsTrigger>
                                    <TabsTrigger value="childCategories">Child Category</TabsTrigger>


                              </TabsList>
                              {activeTab !== "parentCategories" && <div className="flex justify-center gap-10">
                                    <Input
                                          placeholder="Search "
                                          value={searchTerm}
                                          onChange={(e) => setSearchTerm(e.target.value)}

                                    />

                              </div>}
                        </div>
                        <TabsContent value="parentCategories">
                              <Table>
                                    <TableHeader>
                                          <TableRow>
                                                <TableHead className="font-bold" >Category Id</TableHead>
                                                <TableHead className="cursor-pointer font-bold " >Category Name</TableHead>
                                                <TableHead className="font-bold " >Category Level</TableHead>
                                                <TableHead className="font-bold " >Parent Category</TableHead>
                                                <TableHead className="font-bold " >Actions</TableHead>
                                          </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                          {parentCategory?.length === 0 ? (
                                                <TableRow>
                                                      <TableCell className="py-4 text-center align-middle" colSpan={100}>
                                                            No Categories Found
                                                      </TableCell>
                                                </TableRow>
                                          ) : (
                                                parentCategory?.map((y: MasterItemCategories) =>

                                                (


                                                      <TableRow key={y.id}>
                                                            <TableCell>{y?.id}</TableCell>
                                                            <TableCell className="flex gap-2">
                                                                  <img src={y?.picture} alt="" className="w-12 h-12 object-cover" />
                                                                  {y?.name}
                                                            </TableCell>
                                                            <TableCell>
                                                                  {y?.level === 1 ? "L1" : y?.level === 2 ? "L2" : y?.level === 3 ? "L3" : "-"}
                                                            </TableCell>
                                                            <TableCell>
                                                                  <div className="flex flex-col gap-1">
                                                                        {y?.parentCategories?.length > 0
                                                                              ? y?.parentCategories.map((parent) => (
                                                                                    <span key={parent.id}>{parent.name}</span>
                                                                              ))
                                                                              : "-"}
                                                                  </div>
                                                            </TableCell>
                                                            <TableCell>
                                                                  <AddMasterCategory
                                                                        buttonName="Edit"
                                                                        categoryDetails={y}
                                                                        handleSubmit={handleSubmit}
                                                                        mode="Edit"
                                                                  />
                                                            </TableCell>
                                                      </TableRow>
                                                ))

                                          )}
                                    </TableBody>
                              </Table>
                        </TabsContent>
                        <TabsContent value="childCategories">
                              <Table>
                                    <TableHeader>
                                          <TableRow>
                                                <TableHead className="font-bold" >Category Id</TableHead>
                                                <TableHead className="cursor-pointer font-bold " >Category Name</TableHead>
                                                <TableHead className="font-bold " >Category Level</TableHead>
                                                <TableHead className="font-bold " >Parent Category</TableHead>
                                                <TableHead className="font-bold " >Actions</TableHead>
                                          </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                          {categoryList?.length === 0 ?
                                                <TableRow>
                                                      <TableCell className="py-4 text-center align-middle" colSpan={100}>
                                                            No Categories Found
                                                      </TableCell>
                                                </TableRow>

                                                : categoryList?.map((x: MasterItemCategories) => {
                                                      return (
                                                            <TableRow key={x.id}>
                                                                  <TableCell>{x?.id}</TableCell>
                                                                  <TableCell className="flex gap-2"> <img src={x?.picture} alt="" className="w-12 h-12 object-cover" /> {x?.name}</TableCell>
                                                                  <TableCell>   {x?.level === 1 ? "L1" : x?.level === 2 ? "L2" : x?.level === 3 ? "L3" : "-"}
                                                                  </TableCell>
                                                                  <TableCell>
                                                                        <div className=" flex flex-col gap-1">{x?.parentCategories?.length > 0
                                                                              ? x?.parentCategories.map((parent) => <span >{parent.name}</span>)
                                                                              : "-"}
                                                                        </div>

                                                                  </TableCell>
                                                                  <TableCell><AddMasterCategory buttonName="Edit" categoryDetails={x}
                                                                        handleSubmit={handleSubmit} mode="Edit" /></TableCell>

                                                            </TableRow>
                                                      )
                                                })


                                          }

                                    </TableBody>
                              </Table>
                        </TabsContent>
                        <TabsContent value="Items">
                              <Table>
                                    <TableHeader>
                                          <TableRow>
                                                <TableHead className="font-bold">Item Id</TableHead>
                                                <TableHead className="font-bold">Item Name</TableHead>
                                                <TableHead className="font-bold">Item Details</TableHead>
                                                {/* <TableHead className="font-bold "><p>Number of Orders</p>
                                                      <p>(Number of Sellers)</p></TableHead> */}
                                                <TableHead className="font-bold">Status</TableHead>
                                                <TableHead className="font-bold">Categories</TableHead>

                                          </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                          {items.length === 0 ?
                                                <TableRow>
                                                      <TableCell className="py-4 text-center align-middle" colSpan={100}>
                                                            No Items Found
                                                      </TableCell>
                                                </TableRow>
                                                :
                                                items?.map((x: ItemsList) => {
                                                      return (
                                                            <TableRow key={x.id}>
                                                                  <TableCell>{x?.id}</TableCell>
                                                                  <TableCell className="items-center">
                                                                        <div className="flex items-center gap-1">

                                                                              <img
                                                                                    src={
                                                                                          x?.picture && x.picture.includes(",")
                                                                                                ? x.picture.split(",")[0] // Use the first image in the split array
                                                                                                : x.picture
                                                                                    }
                                                                                    alt=""
                                                                                    className="w-12 h-12 object-cover"
                                                                              />  {x?.name}
                                                                        </div>
                                                                        <div className="text-xs mt-2">
                                                                              <span className="font-medium">Packaging:</span> {x.packaging || '-'}
                                                                        </div>
                                                                  </TableCell>

                                                                  <TableCell>
                                                                        <div className="space-y-1">
                                                                              <div className="text-xs">
                                                                                    <span className="font-medium">Min Qty:</span> {x.minimumOrderQty || '-'}
                                                                              </div>
                                                                              <div className="text-xs">
                                                                                    <span className="font-medium">Increment Qty:</span> {x.incrementOrderQty || '-'}
                                                                              </div>
                                                                              <div className="text-xs">
                                                                                    <span className="font-medium">MRP:</span> {x.mrp || '-'}
                                                                              </div>
                                                                              <div className="text-xs">
                                                                                    <span className="font-medium">Source:</span> {x.source || '-'}
                                                                              </div>
                                                                        </div>

                                                                  </TableCell>
                                                                  <TableCell ><span className={`px-2 py-1 rounded-full text-xs ${x.disabled ? 'bg-red-100 text-red-800' : 'bg-green-200 text-green-800'}`}>{x?.disabled === true ? "Disabled" : "Active"}</span>
                                                                  </TableCell>

                                                                  <TableCell className="flex gap-2 align-middle" >  <div className="flex flex-col ">{x?.categories?.length > 0
                                                                        ? x?.categories.map((parent) => <span >{parent.name}</span>)
                                                                        : "-"}
                                                                  </div>
                                                                        <AddCategoryItem buttonName={"Edit"} handleSubmit={handleEdit
                                                                        } itemDetails={x} categoryDetails={x.categories} level={categoryDetail.level} mode={"Edit"} />

                                                                  </TableCell>

                                                            </TableRow>
                                                      )
                                                })}

                                    </TableBody>

                              </Table>
                        </TabsContent>
                        {activeTab !== "parentCategories" && <div className="flex justify-between items-center mt-6 overflow-hidden ">
                              <Pagination>
                                    <PaginationContent>
                                          {currentPage > 0 && (
                                                <PaginationItem>
                                                      <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} className="cursor-pointer" />
                                                </PaginationItem>
                                          )}
                                          <PaginationItem>
                                                <PaginationLink className="cursor-pointer">{currentPage + 1}</PaginationLink>
                                          </PaginationItem>
                                          <PaginationItem>
                                                <PaginationNext onClick={() => handlePageChange(currentPage + 1)} className="cursor-pointer" />
                                          </PaginationItem>
                                    </PaginationContent>
                              </Pagination>
                        </div>}
                  </Tabs>
            </div>
      )
}
