import { Form } from '@remix-run/react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardContent } from '@components/ui/card';
import { RadioGroup, RadioGroupItem } from '@components/ui/radio-group';
import { PhoneNumber } from "~/types/whatsapp";

interface PhoneNumberSelectorProps {
    numbers: PhoneNumber[];
    selectedId?: string;
    onSubmit: (phoneId: string, phoneNumber: string) => void;
}

export function PhoneNumberSelector({ numbers, selectedId, onSubmit }: PhoneNumberSelectorProps) {
    const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();
        const formData = new FormData(event.currentTarget);
        const phoneId = formData.get('phoneId')?.toString();

        if (phoneId) {
            const selectedPhone = numbers.find(n => n.id === phoneId);
            if (selectedPhone) {
                onSubmit(phoneId, selectedPhone.display_phone_number);
            }
        }
    };

    return (
        <Card className="w-full max-w-3xl">
            <CardHeader>
                <CardTitle>Select WhatsApp Phone Number</CardTitle>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit}>
                    <RadioGroup name="phoneId" defaultValue={selectedId}>
                        {numbers.map(number => (
                            <div key={number.id} className="flex items-center space-x-2 p-4 hover:bg-gray-50">
                                <RadioGroupItem value={number.id} id={number.id} />
                                <label htmlFor={number.id} className="flex-1">
                                    <div className="font-medium">{number.display_phone_number}</div>
                                    <div className="text-sm text-gray-500">
                                        <span className={`inline-block px-2 py-1 rounded ${number.code_verification_status === 'VERIFIED'
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-yellow-100 text-yellow-800'
                                            }`}>
                                            {number.code_verification_status}
                                        </span>
                                        <span className="ml-2">Quality: {number.quality_rating}</span>
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        Verified Name: {number.verified_name}
                                    </div>
                                </label>
                            </div>
                        ))}
                    </RadioGroup>
                    <button
                        type="submit"
                        className="mt-4 w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                    >
                        Select Number
                    </button>
                </form>
            </CardContent>
        </Card>
    );
}
