import { useEffect, useState } from "react";
import { useFetcher } from "@remix-run/react";
import { Input } from "./input";
import { Button } from "./button";
import { useToast } from "./ToastProvider";
import MultiSelect from "./multiSelect";
import { Label } from "./label";
import { Dialog, DialogContent, DialogTitle } from "./dialog";
import SpinnerLoader from "../loader/SpinnerLoader";

interface RoleOption {
      value: string;
      label: string;
}

export interface User {
      id: number;
      firstName: string;
      lastName: string;
      email: string;
      mobileNumber: string;
      address: string;
      roles: string[];
      businessId: number;
}

interface CreateUserProps {
      isOpen: boolean;
      onClose: () => void;
      sellerId: number;
      roles: RoleOption[];
      sellerBId: number;
      user?: any


}

export default function CreateUser({
      isOpen,
      onClose,
      sellerId,
      roles,
      user,
      sellerBId
}: CreateUserProps) {
      const fetcher = useFetcher();
      const { showToast } = useToast();

      // State to manage the form data
      const [formData, setFormData] = useState({
            firstName: "",
            lastName: "",
            email: "",
            mobileNumber: "",
            address: "",
            password: "",
            businessId: sellerBId, // Default to sellerId
            roles: [] as string[],
      });

      // Multi-select state for roles
      const [selectedRoles, setSelectedRoles] = useState<string[]>([]);

      // Prefill the form when in edit mode
      useEffect(() => {
            if (user) {
                  console.log(user, "2222222222222222222")
                  setFormData({
                        firstName: user?.firstName,
                        lastName: user?.lastName,
                        email: user?.email,
                        mobileNumber: user?.mobileNumber,
                        address: user?.address,
                        password: "", // Keep it empty when editing
                        businessId: user?.businessId, // Use user's business ID
                        roles: user?.roles,
                  });
                  setSelectedRoles(user.roles);
            }
      }, [user]);

      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            const { name, value } = e.target;
            setFormData((prev) => ({ ...prev, [name]: value }));
      };


      const handleSubmit = async (e: React.FormEvent) => {
            e.preventDefault();

            const userData = {
                  ...formData,
                  roles: selectedRoles,
                  businessId: sellerBId, // Send sellerBId as businessId
            };

            const formDataToSend = new FormData();
            console.log(sellerBId, sellerId, "999999999999999999")
            formDataToSend.append("sellerId", sellerId as unknown as string);
            formDataToSend.append("userId", user?.userId as unknown as string);

            formDataToSend.append("intent", user ? "editUser" : "createUser");
            formDataToSend.append("userData", JSON.stringify(userData));

            fetcher.submit(formDataToSend, {
                  method: "POST",
                  encType: "multipart/form-data",
            });

            // showToast(user ? "User successfully updated" : "User successfully created", "success");
            // onClose();
      };

      const isLoading = fetcher.state !== "idle"

      useEffect(() => {

            if (fetcher.data && isOpen === true) {
                  if (fetcher.data) {
                        showToast(user ? "User successfully updated" : "User successfully created", "success");
                        onClose();
                        setFormData({
                              firstName: "",
                              lastName: "",
                              email: "",
                              mobileNumber: "",
                              address: "",
                              password: "",
                              businessId: sellerBId, // Default to sellerId
                              roles: [] as string[]
                        })
                  }
                  else {
                        showToast(user ? "User  update Failed" : "User creation failed ", "error");

                  }
            }
      }, [fetcher.data])


      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="max-h-[60vh] overflow-y-auto">
                        {isLoading && (
                              <div className="absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm">
                                    <SpinnerLoader loading={isLoading} />
                              </div>
                        )}

                        <DialogTitle className="font-bold">
                              {user ? "Edit User" : "Add New User"}
                        </DialogTitle>
                        <form onSubmit={handleSubmit} className="space-y-4 ">
                              <div className="flex flex-col md:flex-row gap-2">
                                    <Label className="flex-1 flex flex-col gap-y-2 font-semibold">
                                          First Name
                                          <Input
                                                name="firstName"
                                                placeholder="First Name"
                                                onChange={handleChange}
                                                value={formData.firstName}
                                                required
                                          />
                                    </Label>
                                    <Label className="flex-1 flex flex-col gap-y-2">
                                          Last Name
                                          <Input
                                                name="lastName"
                                                placeholder="Last Name"
                                                onChange={handleChange}
                                                value={formData.lastName}
                                                required
                                          />
                                    </Label>
                              </div>

                              <div className="flex flex-col md:flex-row gap-2">
                                    {!user && <Label className="flex-1 flex flex-col gap-y-2">
                                          Email
                                          <Input
                                                type="email"
                                                name="email"
                                                placeholder="Email"
                                                onChange={handleChange}
                                                value={formData.email}
                                                required
                                          />
                                    </Label>}
                                    <Label className="flex-1 flex flex-col gap-y-2">
                                          Mobile Number
                                          <Input
                                                type="number"
                                                name="mobileNumber"
                                                placeholder="Mobile Number"
                                                onChange={(e) => {
                                                      const value = e.target.value.replace(/\D/g, "");
                                                      if (value.length <= 10) {
                                                            setFormData((prev) => ({
                                                                  ...prev,
                                                                  mobileNumber: value


                                                            }))
                                                      }

                                                }}
                                                value={formData.mobileNumber}
                                                required
                                          />
                                    </Label>
                              </div>

                              <div className="flex flex-col md:flex-row gap-2">
                                    {!user && <Label className="flex-1 flex flex-col gap-y-2">
                                          Address
                                          <Input
                                                name="address"
                                                placeholder="Address"
                                                onChange={handleChange}
                                                value={formData.address}
                                                required
                                          />
                                    </Label>}
                                    <Label className="flex-1 flex flex-col gap-y-2">
                                          Select Roles
                                          <MultiSelect
                                                options={roles}
                                                selectedValues={selectedRoles ? selectedRoles : []}
                                                onChange={setSelectedRoles}
                                                placeholder="Select Roles"
                                                className="w-full"
                                                height={true}
                                          />
                                    </Label>
                              </div>

                              <div className="flex justify-end">
                                    <Button type="submit" className="rounded-full">
                                          {user ? "Update User" : "Save User"}
                                    </Button>
                              </div>
                        </form>
                  </DialogContent>
            </Dialog>
      );
}
