import { PlaceOrder } from './../components/PlaceOrder';
'use client'

import React, { useEffect, useState } from 'react'
import { ArrowLeft, Bell, Edit, EditIcon, ImageOff, MapPin, Phone, Save, SaveIcon, Search, Send, ShieldOff, Star, Truck } from 'lucide-react'
import { Button } from "@components/ui/button"
import { Input } from "@components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { Badge } from "@components/ui/badge"
import { Form, Outlet, useActionData, useFetcher, useNavigate, useParams } from "@remix-run/react"
import { ActionFunction, json, LoaderFunction, TypedResponse } from "@remix-run/node"
import { useLoaderData } from "@remix-run/react"
import { getSession } from "@utils/session.server"
import type { User } from "~/types"
import { addContractPrice, getAllDashboardDataFor, getBuyerDetails, getBuyerOrders, getContractPricesForBuyer, getDashboardDataForBuyer, updateContractPrice, updateContractStatus, updateContractValidity } from "@services/businessConsoleService"
import type { BuyerDetailsResponse } from "~/types/api/businessConsoleService/BuyerDetailsResponse"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@components/ui/dialog";
import { Alert, AlertDescription } from "@components/ui/alert";
import { ContractPrice, OrderSummary } from "~/types/api/businessConsoleService/BuyerOrdersResponse";
import { Switch } from '~/components/ui/switch'
import { withAuth, withResponse } from "@utils/auth-utils";
import SpinnerLoader from '~/components/loader/SpinnerLoader'
import CustomerDashboard, { calculateTargetDate } from '~/components/ui/bar-dashboard'
import { DashboardGroupBy } from '~/types/home'
import { SellerConsoleDataResponse } from '~/types/api/businessConsoleService/SellerConsoleDataResponse'

interface LoaderData {
    user: User;
    customerDetails: BuyerDetailsResponse;
    orders: OrderSummary[];
    contractPrices: ContractPrice[];
    currentPage: number;
    userPerMissions: string[],
    customerId: number;
    customerDashBoardData: SellerConsoleDataResponse[];
    dashBoardGroupBy: DashboardGroupBy;
    totalPages: number;
}

interface SendTemplateMessageRequest {
    phoneNo: string;
    templateName: string;
    templateValues: string[];
}

interface ActionData {
    success?: boolean;
    error?: string;
    data?: unknown;
}

export const loader = withAuth(async ({ user, request }) => {
    const params = new URL(request.url).pathname.split('/');
    const customerId = parseInt(params[params.length - 1]);
    const permisson = user?.userPermissions || []
    if (isNaN(customerId)) {
        throw json({ error: "Invalid customer ID" }, { status: 400 });
    }
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "0");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "10");
    const activeTab = url.searchParams.get("activeTab") || "orders";
    const dashboardGroupBy = DashboardGroupBy.Daily;

    try {
        const [customerDetailsResponse, ordersResponse, contractPricesResponse] = await Promise.all([
            getBuyerDetails(user.userId, customerId, request),
            getBuyerOrders(user.userId, customerId, page, pageSize, request),
            getContractPricesForBuyer(customerId, page, pageSize, request),
        ]);


        const responseHeaders = new Headers();
        // [customerDetailsResponse, ordersResponse, contractPricesResponse, customerDashBoardReponse].forEach(response => {
        //     if (response.headers?.has('Set-Cookie')) {
        //         responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);
        //     }
        // });

        const responseData = {
            user,
            customerDetails: customerDetailsResponse.data,
            orders: ordersResponse.data,
            contractPrices: contractPricesResponse.data,
            currentPage: page,
            userPerMissions: permisson,
            customerId: customerId,
            customerDashBoardData: null,
            dashBoardGroupBy: dashboardGroupBy,
            totalPages: Math.ceil(
                (activeTab === "orders"
                    ? ordersResponse.data.length
                    : contractPricesResponse.data.length) / pageSize
            ),
            responseHeaders
        };

        return withResponse(responseData);
    } catch (error) {
        console.error("Customer details error:", error);
        if (error instanceof Response && error.status === 404) {
            throw json({ error: "Customer not found" }, { status: 404 });
        }
        throw new Response("Failed to fetch customer data", { status: 500 });
    }
});

export const action = withAuth(async ({ user, request }) => {
    const formData = await request.formData();
    const intent = formData.get("intent") as string;


    if (intent === "ContractPrice") {
        {
            const sellerItemId = formData.get("siItemId") as string;
            const buyerId = formData.get("buyerId") as string;
            try {
                const response = await addContractPrice(
                    Number(sellerItemId),
                    Number(buyerId),
                    request
                );
                return withResponse({ success: true, data: response });
            } catch (error) {
                throw json(
                    { error: "Failed to create addContractPrice" },
                    { status: 500 }
                );
            }
        }
    } else if (intent === "ContractUpdateStatus") {
        {
            const cPriceId = formData.get("cPriceId") as string;
            const cPriceEnable = formData.get("cPriceEnable") as unknown as boolean;

            try {
                const response = await updateContractStatus(
                    cPriceEnable,
                    Number(cPriceId),
                    request
                );
                return withResponse({ success: true, data: response });
            } catch (error) {
                throw json(
                    { error: "Failed to Updating ContractPriceStatus" },
                    { status: 500 }
                );
            }
        }
    } else if (intent === "ContractUpdatePrice") {
        {
            const cPriceId = formData.get("cPriceId") as string;
            const cPrice = formData.get("cPrice") as unknown as number;

            try {
                const response = await updateContractPrice(
                    Number(cPriceId),
                    cPrice,
                    request
                );
                return withResponse({ success: true, data: response });
            } catch (error) {
                throw json(
                    { error: "Failed to update contract price" },
                    { status: 500 }
                );
            }
        }
    }
    else if (intent === "UpdateContractPriceValidity") {
        {
            const buyerId = formData.get("buyerId") as unknown as number;
            const selectedCDate = formData.get("selectedCDate") as string;

            try {
                const response = await updateContractValidity(
                    buyerId,
                    selectedCDate,
                    request
                );
                return withResponse({ success: true, data: response });
            } catch (error) {
                throw new Response("Failed to update contract price", { status: 500 });

            }
        }
    }

    else if (intent === "customerdashboard") {

        const dashboardGroupBy = formData.get("dashboardGroupBy") as DashboardGroupBy;
        const summaryDate = new Date(formData.get("summaryDate") as string);

        const BuyerId = formData.get("BuyerId");
        if (!Object.values(DashboardGroupBy).includes(dashboardGroupBy)) {
            throw json({ error: "Invalid groupBy parameter" }, { status: 400 });
        }



        const response = await getDashboardDataForBuyer(Number(BuyerId), summaryDate, dashboardGroupBy, request);
        return withResponse(
            {
                data: response.data,
                dashboardGroupBy: dashboardGroupBy
            },
            response.headers
        );
    }
    else {
        {
            const phoneNo = formData.get("phoneNo");
            const amount = formData.get("amount");
            const customerName = formData.get("customerName");

            if (!phoneNo || !amount || !customerName) {
                throw json(
                    { error: "Missing required fields" },
                    { status: 400 }
                );
            }

            try {
                const url = new URL(request.url);
                const messageRequest: SendTemplateMessageRequest = {
                    phoneNo: phoneNo.toString(),
                    templateName: 'pending_dues_buyer_1',
                    templateValues: [customerName.toString()]
                };

                const response = await fetch(
                    `https://console.mnetlive.com/api/whatsapp/send-template-message`,
                    {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${process.env.AUTH_TOKEN}`
                        },
                        body: JSON.stringify(messageRequest)
                    }
                );

                if (!response.ok) {
                    const errorData = await response.json()
                        .catch(() => ({ message: 'Failed to send message' }));
                    throw new Error(errorData.message || 'Failed to send message');
                }

                const responseData = await response.json();
                return withResponse({ success: true, data: responseData });
            } catch (error) {
                console.error('WhatsApp message error:', error);
                throw json(
                    { error: "Failed to send payment reminder" },
                    { status: 500 }
                );
            }
        }
    }
});


export default function CustomerDetailsPage() {
    const params = useParams()
    const buyerId = parseInt(params.customerId as string);

    const { user, customerDetails, orders, contractPrices, currentPage, userPerMissions, customerId, customerDashBoardData, dashBoardGroupBy, totalPages } = useLoaderData<LoaderData>()
    const actionData = useActionData() as ActionData
    const [searchTerm, setSearchTerm] = useState('')
    const [sortBy, setSortBy] = useState('id')
    const [sortOrder, setSortOrder] = useState('asc')
    const [activeTab, setActiveTab] = useState('dashboard')
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [uniqueId, setUnqueId] = useState(0);
    const [isEdit, setIsEdit] = useState(false);
    const [cPrice, setCPrice] = useState(0)
    const fetcher = useFetcher<{ data: SellerConsoleDataResponse[], dashboardGroupBy: DashboardGroupBy }>()
    const navigate = useNavigate()
    const [cPriceEnable, setCPriceEnable] = useState(false)
    const [cPriceEditable, setCPriceEditable] = useState(false)
    const previewMessage = `Hi ${customerDetails.ownerName},
        You have pending dues with us. Please open the app to review and clear them as soon as possible. Ignore if already paid.
        Thank you !`;

    const [customergraphData, setCustomerGraphData] = useState<SellerConsoleDataResponse[]>(customerDashBoardData);
    const [dashboardGroup, setIsDashBoardGroup] = useState<DashboardGroupBy>(dashBoardGroupBy)
    const [pageSize, setPageSize] = useState("10")
    const [selectedCDate, setSelectedCDate] = useState<Date>(new Date())

    useEffect(() => {
        if (fetcher.data?.data && activeTab === "dashboard") {
            setCustomerGraphData(fetcher.data.data);
            setIsDashBoardGroup(fetcher.data.dashboardGroupBy)
        }
    }, [fetcher.state, activeTab === "dashboard"])

    useEffect(() => {
        if (userPerMissions) {
            const isContractPrice = userPerMissions.includes("seller_app.contracPriceEnabled")
            const isContractPriceEditable = userPerMissions.includes("seller_app.contracPriceEditable")

            setCPriceEditable(isContractPriceEditable);
            setCPriceEnable(isContractPrice)
        }
    }, [userPerMissions])
    const handlefilter = (contract: ContractPrice) => {
        return contract.itemName.toLowerCase().includes(searchTerm.toLowerCase())
    }

    const handlePageSizeChange = (newPageSize: string) => {
        navigate(`/home/<USER>/${customerId}?page=${currentPage}&pageSize=${newPageSize}&activeTab=${activeTab}`);
        setPageSize(newPageSize);
    }

    const filteredOrders = orders
        .filter(order =>
            order.orderGroupId.toString().includes(searchTerm) ||
            order.orderStatus.toLowerCase().includes(searchTerm.toLowerCase())
        )
        .sort((a, b) => {
            const getValue = (order: typeof orders[0]) => {
                switch (sortBy) {
                    case 'id':
                        return order.orderGroupId
                    case 'deliveryDate':
                        return order.deliveryDate
                    case 'status':
                        return order.orderStatus
                    case 'totalAmount':
                        return order.orderAmount
                    default:
                        return order.orderGroupId
                }
            }

            const aValue = getValue(a)
            const bValue = getValue(b)

            if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
            if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
            return 0
        })

    const handleAddContractPrice = (siItemId: number, buyerId: number) => {
        setUnqueId(siItemId)
        const formData = new FormData();
        formData.append("intent", "ContractPrice")
        formData.append("siItemId", siItemId.toString())
        formData.append("buyerId", buyerId.toString())
        fetcher.submit(formData, { method: "POST" });
    }
    const handleUpdatePrice = (siItemId: number, cPriceId: number, cPrice: number) => {
        setUnqueId(siItemId)
        const formData = new FormData();
        formData.append("intent", "ContractUpdatePrice")
        formData.append("cPriceId", cPriceId.toString())
        formData.append("cPrice", cPrice.toString())
        fetcher.submit(formData, { method: "POST" });
        setIsEdit(false)
        setCPrice(0)
    }
    const handleUpdateStatus = (siItemId: number, contractPriceId: number, contractPriceEnabled: boolean) => {
        setUnqueId(siItemId)
        const formData = new FormData();
        formData.append("intent", "ContractUpdateStatus")
        formData.append("cPriceId", contractPriceId.toString())
        formData.append("cPriceEnable", contractPriceEnabled.toString())

        fetcher.submit(formData, { method: "POST" });
        setIsEdit(false);
    }

    const handleEdit = (itemId: number) => {
        setUnqueId(itemId)
        setIsEdit(true)
    }

    const handleGraph = (value: string, summaryDate: Date) => {
        fetcher.submit(
            {
                dashboardGroupBy: value,
                summaryDate: summaryDate.toISOString(),
                intent: "customerdashboard",
                BuyerId: customerId.toString()
            },
            { method: "post" }
        );
    }

    const handleTabChange = (value: string) => {
        setActiveTab(value);
        navigate(`/home/<USER>/${customerId}?page=0&pageSize=${pageSize}&activeTab=${value}`);
    }

    const renderPagination = (currentPage: number, totalPages: number) => {
        return (
            <div className="flex items-center justify-between space-x-2 py-4">
                <div className="flex-1 text-sm text-muted-foreground">
                    {/* Page {currentPage + 1} of {totalPages} */}
                </div>
                <div className="flex items-center space-x-2">
                    <Form method="get">
                        <input type="hidden" name="page" value={currentPage - 1} />
                        <input type="hidden" name="pageSize" value={pageSize} />
                        <input type="hidden" name="activeTab" value={activeTab} />
                        <Button
                            variant="outline"
                            size="sm"
                            type="submit"
                            disabled={currentPage <= 0}
                        >
                            Previous
                        </Button>
                    </Form>
                    <Form method="get">
                        <input type="hidden" name="page" value={currentPage + 1} />
                        <input type="hidden" name="pageSize" value={pageSize} />
                        <input type="hidden" name="activeTab" value={activeTab} />
                        <Button
                            variant="outline"
                            size="sm"
                            type="submit"
                        >
                            Next
                        </Button>
                    </Form>
                </div>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-6">
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent>
                    {actionData?.success ? (
                        <>
                            <Alert className="mt-4">
                                <AlertDescription className="text-green-600">
                                    Payment reminder sent successfully!
                                </AlertDescription>
                            </Alert>
                            <div className="flex justify-end mt-4">
                                <Button
                                    onClick={() => setIsDialogOpen(false)}
                                >
                                    Dismiss
                                </Button>
                            </div>
                        </>
                    ) : (
                        <>
                            <DialogHeader>
                                <DialogTitle>Send Payment Reminder</DialogTitle>
                                <DialogDescription>
                                    Preview of the message that will be sent:
                                </DialogDescription>
                            </DialogHeader>

                            <div className="bg-muted p-4 rounded-lg my-4">
                                <p className="whitespace-pre-wrap">{previewMessage}</p>
                            </div>

                            <Form
                                method="post"
                                onSubmit={() => setIsSubmitting(true)}
                                onChange={() => {
                                    if (actionData) {
                                        setIsSubmitting(false)
                                    }
                                }}
                            >
                                <input type="hidden" name="phoneNo" value={customerDetails.mobileNumber} />
                                <input type="hidden" name="amount" value={customerDetails.pendingAmount.toString()} />
                                <input type="hidden" name="customerName" value={customerDetails.ownerName} />

                                <div className="flex justify-end gap-2">
                                    <Button
                                        variant="outline"
                                        onClick={() => setIsDialogOpen(false)}
                                        type="button"
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        type="submit"
                                        disabled={isSubmitting}
                                    >
                                        {isSubmitting ? 'Sending...' : 'Send Reminder'}
                                    </Button>
                                </div>
                            </Form>

                            {actionData?.error && (
                                <Alert className="mt-4">
                                    <AlertDescription className="text-red-600">
                                        {actionData.error}
                                    </AlertDescription>
                                </Alert>
                            )}
                        </>
                    )}
                </DialogContent>
            </Dialog>

            <div className="flex items-center gap-2 mb-6">
                <Button variant="ghost" size="sm" onClick={() => navigate(-1)}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Customers
                </Button>
                <span className="text-muted-foreground">/</span>
                <span className="font-semibold">{customerDetails.buyerName}</span>
            </div>

            <Card className="mb-6">
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>{customerDetails.buyerName}</span>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <p className="font-semibold">{customerDetails.ownerName}</p>
                            <p className="flex items-center mt-1 text-sm">
                                <Phone className="h-4 w-4 mr-2" />
                                {customerDetails.mobileNumber}
                            </p>
                            <p className="flex items-start mt-1 text-sm">
                                <MapPin className="h-4 w-4 mr-2 mt-1" />
                                {customerDetails.address}
                            </p>
                        </div>
                        <div className="flex flex-col gap-2 md:col-span-2 justify-end">
                            <div className="flex w-full flex-wrap gap-2 justify-end">
                                {/* <div
                                tabIndex={0}
                                role='button'
                                onKeyDown={() => {}}
                                onClick={() => {
                                    setActiveTab("orders")
                                }}>
                                <PlaceOrder   buyerDetails={{buyerId: customerDetails.buyerId, mobileNumber: customerDetails.mobileNumber}}  />
                                </div> */}
                                {user?.userDetails?.mobileNumber === "5555555550" ? <Button variant="outline" size="sm"
                                    onClick={() => setIsDialogOpen(true)}
                                >
                                    <Bell className="h-4 w-4 mr-2" />
                                    Send Payment reminder
                                </Button> : null}
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">₹ {customerDetails.totalAmount.toLocaleString()}</div>
                        <p className="text-xs text-muted-foreground">from {customerDetails.totalOrders} orders</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">₹ {customerDetails.pendingAmount.toLocaleString()}</div>
                        <p className="text-xs text-muted-foreground">total pending amount</p>
                    </CardContent>
                </Card>
            </div>

            <Tabs value={activeTab} onValueChange={handleTabChange}>
                <TabsList>
                    <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                    <TabsTrigger value="orders">Orders</TabsTrigger>
                    {/* <TabsTrigger value="payments">Payments</TabsTrigger> */}
                    {/* <TabsTrigger value="notifications">Notifications</TabsTrigger>
                    <TabsTrigger value="support">Support Tickets</TabsTrigger> */}
                    {cPriceEnable && <TabsTrigger value="contractprice">Contract Prices</TabsTrigger>}


                </TabsList>
                {activeTab !== "contractprice" && <div className="flex justify-between items-center my-4">
                    <Input
                        placeholder="Search by order ID, status, or seller"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="max-w-sm"
                    />

                    <Select value={pageSize} onValueChange={handlePageSizeChange}>
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="Items per page" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="5">5 per page</SelectItem>
                            <SelectItem value="10">10 per page</SelectItem>
                            <SelectItem value="20">20 per page</SelectItem>
                            <SelectItem value="50">50 per page</SelectItem>
                        </SelectContent>
                    </Select>
                </div>}
                {/* {activeTab === "contractprice" && <div className="flex justify-between items-center my-4">
                    <Input
                        placeholder="Search by item name"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="max-w-sm"
                    />
                </div>} */}
                {/* <TabsContent value="dashboard">
                    <CustomerDashboard data={customergraphData || []} handleGraph={handleGraph} dashboardGroupBy={dashboardGroup} />
                </TabsContent> */}
                <TabsContent value="orders">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Order ID</TableHead>
                                <TableHead>Order Date</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Delivery Date</TableHead>
                                <TableHead>Amount</TableHead>
                                <TableHead>Payment Status</TableHead>
                                {/* <TableHead>Actions</TableHead> */}
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {orders.map((order) => (
                                <TableRow key={order.orderGroupId}>
                                    <TableCell>{order.orderGroupId}</TableCell>
                                    <TableCell>
                                        {new Date(order.orderDateTime).toLocaleDateString('en-IN', {
                                            day: 'numeric',
                                            month: 'short',
                                            year: '2-digit'
                                        })}
                                    </TableCell>
                                    <TableCell>
                                        <Badge variant={order.orderStatus === 'Delivered' ? 'default' : 'secondary'}>
                                            {order.orderStatus}
                                        </Badge>

                                    </TableCell>
                                    <TableCell>{order.deliveryDate}</TableCell>
                                    <TableCell>₹ {order.orderAmount.toLocaleString()}</TableCell>
                                    <TableCell>
                                        {order.paymentStatus ? 'Paid' : 'Pending'}
                                    </TableCell>
                                    {/* <TableCell>
                                        <Button variant="ghost" size="sm">View</Button>

                                    </TableCell> */}
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                    {renderPagination(currentPage, totalPages)}
                </TabsContent>
                <TabsContent value="payments">
                    <p>Payments content (to be implemented with payment data)</p>
                </TabsContent>
                <TabsContent value="notifications">
                    <p>Notifications content (to be implemented)</p>
                </TabsContent>
                <TabsContent value="support">
                    <p>Support Tickets content (to be implemented)</p>
                </TabsContent>

                {cPriceEnable && <TabsContent value="contractprice">
                    {/* {fetcher.state !== "idle" && <SpinnerLoader size={8} loading={true} />} */}

                    <div className="flex flex-col md:flex-row justify-between items-center gap-4 my-4">
                        {/* Search Input */}
                        <Input
                            placeholder="Search by item name"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full md:max-w-xs rounded-full"
                        />

                        {/* Contract Price Validity Form */}
                        <Form className="flex flex-col md:flex-row items-center gap-4 w-full md:w-auto" method="post">
                            <input type="hidden" name="intent" value="UpdateContractPriceValidity" />
                            <input type="hidden" name="buyerId" value={buyerId.toString()} />

                            <input type="hidden" name="selectedDate" value={selectedCDate.toString()} />

                            <div className="flex flex-col md:flex-row items-center gap-2">
                                <label htmlFor="dashboardGroupBy" className="text-sm font-medium text-gray-700">
                                    Contract Price Validity:
                                </label>
                                <Input
                                    value={selectedCDate.toISOString().split('T')[0]}
                                    onChange={(e) => {
                                        const date = new Date(e.target.value);
                                        setSelectedCDate(date);
                                    }}
                                    type="date"
                                    className="w-full md:max-w-xs bg-orange-100 border border-orange-300 rounded-md focus:ring-2 focus:ring-orange-400 focus:outline-none custom-date-input cursor-pointer"
                                />
                            </div>
                            <Button
                                className="bg-orange-400 hover:bg-orange-500 text-white font-bold px-4 py-2 rounded-md transition-all"
                                size="sm"
                                type="submit"
                            >
                                SAVE
                            </Button>
                        </Form>

                        {/* Items Per Page Select */}
                        <Select value={pageSize} onValueChange={handlePageSizeChange}>
                            <SelectTrigger className="w-full md:w-[180px] border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none">
                                <SelectValue placeholder="Items per page" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="5">5 per page</SelectItem>
                                <SelectItem value="10">10 per page</SelectItem>
                                <SelectItem value="20">20 per page</SelectItem>
                                <SelectItem value="50">50 per page</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Item Image</TableHead>
                                <TableHead>Item Name</TableHead>
                                <TableHead>Price</TableHead>
                                <TableHead>Enabled</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {contractPrices.filter((contract) => handlefilter(contract)).map((price) => (
                                <TableRow key={price.sellerItemId}>
                                    <TableCell>
                                        <img
                                            src={price.picture}
                                            alt={price.itemName}
                                            className="h-10 w-10"
                                        />
                                    </TableCell>
                                    <TableCell>{price.itemName}</TableCell>
                                    {price.newItem === false && <TableCell className='flex gap-2'>

                                        {isEdit && uniqueId === price.sellerItemId ? <>
                                            <Input placeholder={price.cbItemPrice.toString()}


                                                onChange={(e) => {
                                                    const value = Number(e.target.value);
                                                    if (value > 0 || e.target.value === "") {

                                                        setCPrice(value);
                                                    }
                                                }}
                                                type="number"
                                                className='w-20 h-10 ' />
                                            <Save height={20} width={20} onClick={() => handleUpdatePrice(price.sellerItemId, price.contractPriceId, cPrice)} className='my-3 cursor-pointer ' />
                                        </>


                                            :
                                            <> {`₹ ${price.cbItemPrice}`}

                                                {cPriceEditable && <EditIcon height={20} width={20} onClick={() => handleEdit(price.sellerItemId)} />}

                                            </>
                                        }
                                    </TableCell>}
                                    <TableCell>{price.newItem ? <Button type="button" onClick={() => handleAddContractPrice(price.sellerItemId, buyerId)}>
                                        {price.sellerItemId === uniqueId && fetcher.state !== "idle"
                                            ? "ADDING..."
                                            : "ADD"}
                                    </Button> : <Switch checked={price.enabled}
                                        onCheckedChange={() => handleUpdateStatus(price.sellerItemId, price.contractPriceId, !(price.enabled))} />}
                                    </TableCell>


                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                    {renderPagination(currentPage, totalPages)}
                </TabsContent>}
            </Tabs>
        </div>
    )
}

