// app/services/__mocks__/businessConsole.mockData.ts

import {SellerConsoleDataResponse} from "~/types/api/businessConsoleService/SellerConsoleDataResponse";

export const mockDashboardDataDaily: SellerConsoleDataResponse[] = [
    {
        "totalAmount": 0,
        "totalOrders": 0,
        "returnAmount": 0,
        "returnWeight": 0,
        "pendingAmount": 0,
        "pendingAmountCount": 0,
        "employeeCount": 40,
        "itemCount": 0,
        "periodLabel": "Sunday"
    },
    {
        "totalAmount": 62222,
        "totalOrders": 61,
        "buyerSummary": [
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 2,
                "totalWeight": 30
            },
            {
                "buyerName": "PNT-Bhupathi",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "KAD-Archana N",
                "itemCount": 2,
                "totalWeight": 300
            },
            {
                "buyerName": "KAD-MNB farm Fresh Fruits Vegetable",
                "itemCount": 2,
                "totalWeight": 15
            },
            {
                "buyerName": "Muniraj",
                "itemCount": 2,
                "totalWeight": 28.5
            },
            {
                "buyerName": "Shamugan",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Hsk Guru fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "Sri Renuka yallammadevi fnv varthur",
                "itemCount": 3,
                "totalWeight": 63
            },
            {
                "buyerName": "KAD-Syed Zaheer",
                "itemCount": 1,
                "totalWeight": 30
            },
            {
                "buyerName": "WHF-Santosh Kumar",
                "itemCount": 3,
                "totalWeight": 33
            },
            {
                "buyerName": "WHF-Shanmugam",
                "itemCount": 2,
                "totalWeight": 64.5
            },
            {
                "buyerName": "HSK-Babu",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "HSK-Green nature",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "Jayachandran N",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Mamatha.v",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Hsk Sri kanteshwara fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "KAD-Kalyani",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "Tip top mini mart",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD - Kalpana Provision Store",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "KRP-Gangamma",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HRM-Vinod",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Kk fruits",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "KAD-Mohammed Hussain",
                "itemCount": 4,
                "totalWeight": 80
            },
            {
                "buyerName": "GDP-Gururaj",
                "itemCount": 1,
                "totalWeight": 90
            },
            {
                "buyerName": "KAD-Bindhu",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "New jetta retailer",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "GDP-Amaresh",
                "itemCount": 2,
                "totalWeight": 162
            },
            {
                "buyerName": "Sunil",
                "itemCount": 5,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Manjunath Bengaluru Horticulture 03",
                "itemCount": 1,
                "totalWeight": 60
            },
            {
                "buyerName": "PNT-JADDADI  PRADEEP SHETTY",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "RAVI KIRAN.T",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Syed vegetables kadugodi",
                "itemCount": 2,
                "totalWeight": 15
            },
            {
                "buyerName": "Gajula Sanjeeva Kumar",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "HSK-Sindhur anrun",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Divakar reddy",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Yashavanth",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-Chandrashekhar",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD - Bappi",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-A S fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "GDP-Gowtham provisions store",
                "itemCount": 2,
                "totalWeight": 54
            },
            {
                "buyerName": "KAD SLV fruits and vegetables Seegehalli",
                "itemCount": 1,
                "totalWeight": 30
            },
            {
                "buyerName": "KRP Manjunath Reddy",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Arif",
                "itemCount": 2,
                "totalWeight": 65
            },
            {
                "buyerName": "KAD-Siddiq",
                "itemCount": 3,
                "totalWeight": 50
            },
            {
                "buyerName": "HSK-Thirvathamma",
                "itemCount": 4,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP-Freshly grow( banahalli)",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "PNT- Sri THIRUMALAI HOT CHIPS",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "om sakthi wholesale shop",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "hsk Darshan",
                "itemCount": 3,
                "totalWeight": 45
            },
            {
                "buyerName": "Yogesh",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "Venkat",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP-Dileep vegetables",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP-Prashanth",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-S Raheem",
                "itemCount": 2,
                "totalWeight": 75
            },
            {
                "buyerName": "HSK-Nature fresh fruit and vegetables",
                "itemCount": 1,
                "totalWeight": 108
            },
            {
                "buyerName": "HSK-Sharadha",
                "itemCount": 2,
                "totalWeight": 33
            },
            {
                "buyerName": "TNS-Lokesh Lokesh",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Harish N",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "TNS-Harishkumar",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Fruits taj kadugodi",
                "itemCount": 2,
                "totalWeight": 64
            }
        ],
        "returnAmount": 96404.5,
        "returnWeight": 2078.5,
        "pendingAmount": 14761.5,
        "pendingAmountCount": 9,
        "employeeCount": 40,
        "itemCount": 0,
        "orderItems": [
            {
                "itemName": "Apple Shimla 125count (20Kg/box)",
                "totalWeight": 0
            },
            {
                "itemName": "Papaya (400 gms- 1 kg)",
                "totalWeight": 200
            },
            {
                "itemName": "Banana Robusta Premium(18kg Crate)",
                "totalWeight": 1173
            },
            {
                "itemName": "Yalakki Crate Medium",
                "totalWeight": 64
            },
            {
                "itemName": "Shimla Apple (10Kg/Box)",
                "totalWeight": 0
            },
            {
                "itemName": "Poovan crate Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Apple Shimla 175count (20Kg/box)",
                "totalWeight": 0
            },
            {
                "itemName": "Yalakki Looms Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Poovan Looms Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Nagpur Orange medium polo (20Kg/Crate)",
                "totalWeight": 40
            },
            {
                "itemName": "Yalakki Looms Medium",
                "totalWeight": 168
            },
            {
                "itemName": "Yalakki Crate Premium",
                "totalWeight": 45
            },
            {
                "itemName": "Banana Robusta (Baby)",
                "totalWeight": 0
            }
        ],
        "periodLabel": "Monday"
    },
    {
        "totalAmount": 62259,
        "totalOrders": 61,
        "buyerSummary": [
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 2,
                "totalWeight": 30
            },
            {
                "buyerName": "PNT-Bhupathi",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "KAD-Archana N",
                "itemCount": 2,
                "totalWeight": 300
            },
            {
                "buyerName": "KAD-MNB farm Fresh Fruits Vegetable",
                "itemCount": 2,
                "totalWeight": 15
            },
            {
                "buyerName": "Muniraj",
                "itemCount": 2,
                "totalWeight": 28.5
            },
            {
                "buyerName": "Shamugan",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Hsk Guru fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "Sri Renuka yallammadevi fnv varthur",
                "itemCount": 3,
                "totalWeight": 63
            },
            {
                "buyerName": "KAD-Syed Zaheer",
                "itemCount": 1,
                "totalWeight": 30
            },
            {
                "buyerName": "WHF-Santosh Kumar",
                "itemCount": 3,
                "totalWeight": 33
            },
            {
                "buyerName": "WHF-Shanmugam",
                "itemCount": 2,
                "totalWeight": 64.5
            },
            {
                "buyerName": "HSK-Babu",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "HSK-Green nature",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "Jayachandran N",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Mamatha.v",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Hsk Sri kanteshwara fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "KAD-Kalyani",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "Tip top mini mart",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD - Kalpana Provision Store",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "KRP-Gangamma",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HRM-Vinod",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Kk fruits",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "KAD-Mohammed Hussain",
                "itemCount": 4,
                "totalWeight": 80
            },
            {
                "buyerName": "GDP-Gururaj",
                "itemCount": 1,
                "totalWeight": 90
            },
            {
                "buyerName": "KAD-Bindhu",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "New jetta retailer",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "GDP-Amaresh",
                "itemCount": 2,
                "totalWeight": 162
            },
            {
                "buyerName": "Sunil",
                "itemCount": 5,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Manjunath Bengaluru Horticulture 03",
                "itemCount": 1,
                "totalWeight": 60
            },
            {
                "buyerName": "PNT-JADDADI  PRADEEP SHETTY",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "RAVI KIRAN.T",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Syed vegetables kadugodi",
                "itemCount": 2,
                "totalWeight": 15
            },
            {
                "buyerName": "Gajula Sanjeeva Kumar",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "HSK-Sindhur anrun",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Divakar reddy",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Yashavanth",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-Chandrashekhar",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD - Bappi",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-A S fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "GDP-Gowtham provisions store",
                "itemCount": 2,
                "totalWeight": 54
            },
            {
                "buyerName": "KAD SLV fruits and vegetables Seegehalli",
                "itemCount": 1,
                "totalWeight": 30
            },
            {
                "buyerName": "KRP Manjunath Reddy",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Arif",
                "itemCount": 2,
                "totalWeight": 65
            },
            {
                "buyerName": "KAD-Siddiq",
                "itemCount": 3,
                "totalWeight": 50
            },
            {
                "buyerName": "HSK-Thirvathamma",
                "itemCount": 4,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP-Freshly grow( banahalli)",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "PNT- Sri THIRUMALAI HOT CHIPS",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "om sakthi wholesale shop",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "hsk Darshan",
                "itemCount": 3,
                "totalWeight": 45
            },
            {
                "buyerName": "Yogesh",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "Venkat",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP-Dileep vegetables",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP-Prashanth",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-S Raheem",
                "itemCount": 2,
                "totalWeight": 75
            },
            {
                "buyerName": "HSK-Nature fresh fruit and vegetables",
                "itemCount": 1,
                "totalWeight": 108
            },
            {
                "buyerName": "HSK-Sharadha",
                "itemCount": 2,
                "totalWeight": 33
            },
            {
                "buyerName": "TNS-Lokesh Lokesh",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Harish N",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "TNS-Harishkumar",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Fruits taj kadugodi",
                "itemCount": 2,
                "totalWeight": 64
            }
        ],
        "returnAmount": 96404.5,
        "returnWeight": 2078.5,
        "pendingAmount": 14761.5,
        "pendingAmountCount": 9,
        "employeeCount": 40,
        "itemCount": 0,
        "orderItems": [
            {
                "itemName": "Apple Shimla 125count (20Kg/box)",
                "totalWeight": 0
            },
            {
                "itemName": "Papaya (400 gms- 1 kg)",
                "totalWeight": 200
            },
            {
                "itemName": "Banana Robusta Premium(18kg Crate)",
                "totalWeight": 1173
            },
            {
                "itemName": "Yalakki Crate Medium",
                "totalWeight": 64
            },
            {
                "itemName": "Shimla Apple (10Kg/Box)",
                "totalWeight": 0
            },
            {
                "itemName": "Poovan crate Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Apple Shimla 175count (20Kg/box)",
                "totalWeight": 0
            },
            {
                "itemName": "Yalakki Looms Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Poovan Looms Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Nagpur Orange medium polo (20Kg/Crate)",
                "totalWeight": 40
            },
            {
                "itemName": "Yalakki Looms Medium",
                "totalWeight": 168
            },
            {
                "itemName": "Yalakki Crate Premium",
                "totalWeight": 45
            },
            {
                "itemName": "Banana Robusta (Baby)",
                "totalWeight": 0
            }
        ],
        "periodLabel": "Tuesday"
    },
    {
        "totalAmount": 264276.4,
        "totalOrders": 82,
        "buyerSummary": [
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 2,
                "totalWeight": 36
            },
            {
                "buyerName": "Muniyappa",
                "itemCount": 4,
                "totalWeight": 705
            },
            {
                "buyerName": "KRP-Munireddy g a G A Munireddy",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "HSK venkateshwara fruits",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Archana N",
                "itemCount": 3,
                "totalWeight": 470
            },
            {
                "buyerName": "RMN- Halli Kai thota",
                "itemCount": 3,
                "totalWeight": 36
            },
            {
                "buyerName": "HSK-Green mart",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-MNB farm Fresh Fruits Vegetable",
                "itemCount": 3,
                "totalWeight": 33
            },
            {
                "buyerName": "Muniraj",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "Shamugan",
                "itemCount": 1,
                "totalWeight": 180
            },
            {
                "buyerName": "Manjunath YB",
                "itemCount": 1,
                "totalWeight": 108
            },
            {
                "buyerName": "Vasudevan vegetables kodthi main road",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "RMN-Prakasha Gowda",
                "itemCount": 1,
                "totalWeight": 126
            },
            {
                "buyerName": "WHF-Shanmugam",
                "itemCount": 2,
                "totalWeight": 316
            },
            {
                "buyerName": "HSK-Babu",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "Slv uncle",
                "itemCount": 1,
                "totalWeight": 90
            },
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 1,
                "totalWeight": 65.3
            },
            {
                "buyerName": "Devendra M",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "HSK-Green nature",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Slv pooja store ( bahan alli)",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "Jayachandran N",
                "itemCount": 4,
                "totalWeight": 394
            },
            {
                "buyerName": "KAD-C V Varadaraju",
                "itemCount": 2,
                "totalWeight": 121
            },
            {
                "buyerName": "RMN- HASIRU",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "KAD-Kalyani",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Tip top mini mart",
                "itemCount": 1,
                "totalWeight": 180
            },
            {
                "buyerName": "MHP-Ganesh Fruits",
                "itemCount": 3,
                "totalWeight": 267
            },
            {
                "buyerName": "Gangadharappa MS Gangu",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Sri Tirumala",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "KRP-Gangamma",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Kk fruits",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "GDP-Gururaj",
                "itemCount": 3,
                "totalWeight": 361
            },
            {
                "buyerName": "Fresh one veg & fruits",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Bindhu",
                "itemCount": 1,
                "totalWeight": 90
            },
            {
                "buyerName": "KRP Chamundeswari fruits and veg",
                "itemCount": 3,
                "totalWeight": 74
            },
            {
                "buyerName": "WHF-narayana",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "Sunil",
                "itemCount": 5,
                "totalWeight": 130
            },
            {
                "buyerName": "RMN-SLV vegetables",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "KAD-Syed vegetables kadugodi",
                "itemCount": 3,
                "totalWeight": 70
            },
            {
                "buyerName": "MHP-samim",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "Gajula Sanjeeva Kumar",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "RMN-Puttaranagappa",
                "itemCount": 3,
                "totalWeight": 384.5
            },
            {
                "buyerName": "RMN- PAVITHRA",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "RMN- Manjunath",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "Divakar reddy",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Yashavanth",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "PNT-R K provision store",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "Rekha A @TNS ROUT@",
                "itemCount": 1,
                "totalWeight": 54
            },
            {
                "buyerName": "Sri Krishna fruit and vegetables",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP-Manjamma",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP-Ragavendra",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "HSK-A S fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "TNS-Shasadri Vegtables",
                "itemCount": 4,
                "totalWeight": 1383.2
            },
            {
                "buyerName": "KRP Manjunath Reddy",
                "itemCount": 2,
                "totalWeight": 36
            },
            {
                "buyerName": "RMN-Sri chowdeswari fruits and vegetables",
                "itemCount": 2,
                "totalWeight": 107
            },
            {
                "buyerName": "Sajjad  (GDP)",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Siddiq",
                "itemCount": 2,
                "totalWeight": 15
            },
            {
                "buyerName": "RMN-SSB Home Needs",
                "itemCount": 3,
                "totalWeight": 70
            },
            {
                "buyerName": "KRP-Freshly grow( banahalli)",
                "itemCount": 3,
                "totalWeight": 68
            },
            {
                "buyerName": "HSK Sri ayyappa nandini parlour",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-Thirvathamma",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "PNT- Sri THIRUMALAI HOT CHIPS",
                "itemCount": 2,
                "totalWeight": 83
            },
            {
                "buyerName": "hsk Darshan",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "Yogesh",
                "itemCount": 3,
                "totalWeight": 97.8
            },
            {
                "buyerName": "Freshop Essentials",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "KRP-Prashanth",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "manjunath manju",
                "itemCount": 2,
                "totalWeight": 180
            },
            {
                "buyerName": "KAD-Vijay D",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-S Raheem",
                "itemCount": 2,
                "totalWeight": 68
            },
            {
                "buyerName": "HSK-Nature fresh fruit and vegetables",
                "itemCount": 1,
                "totalWeight": 126
            },
            {
                "buyerName": "HSK-Sharadha",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "A1 Fruit and Vegitables",
                "itemCount": 1,
                "totalWeight": 20
            },
            {
                "buyerName": "PNT-Siddeshwara condiments and vegetables",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "RMN-Prabhu",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Harender Hormavu",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "PNT-THE SAMOSA GARDEN (BALGERE)",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Harish N",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "RMN-Mehboob",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "KAD-Parashuram",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "MHP-Ratnamma",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "TNS-Harishkumar",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "TNS-deepak Nishad",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "Sri Sai Ram vegetables and provision store (Banjara layout)(",
                "itemCount": 3,
                "totalWeight": 80
            }
        ],
        "returnAmount": 100508.24,
        "returnWeight": 1983.02,
        "pendingAmount": 127681.4,
        "pendingAmountCount": 21,
        "employeeCount": 40,
        "itemCount": 47,
        "orderItems": [
            {
                "itemName": "Apple Shimla 125count (20Kg/box)",
                "totalWeight": 0
            },
            {
                "itemName": "Apple Shimla 100count (20Kg/box)",
                "totalWeight": 2
            },
            {
                "itemName": "Papaya (400 gms- 1 kg)",
                "totalWeight": 473
            },
            {
                "itemName": "Banana Robusta Premium(18kg Crate)",
                "totalWeight": 5501
            },
            {
                "itemName": "Yalakki Crate Medium",
                "totalWeight": 0
            },
            {
                "itemName": "Shimla Apple (10Kg/Box)",
                "totalWeight": 1
            },
            {
                "itemName": "Poovan crate Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Apple Shimla 175count (20Kg/box)",
                "totalWeight": 1
            },
            {
                "itemName": "Yalakki Looms Premium",
                "totalWeight": 145.9
            },
            {
                "itemName": "Poovan Looms Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Nagpur Orange medium polo (20Kg/Crate)",
                "totalWeight": 235
            },
            {
                "itemName": "Yalakki Looms Medium",
                "totalWeight": 464.9
            },
            {
                "itemName": "Yalakki Crate Premium",
                "totalWeight": 410
            }
        ],
        "periodLabel": "Wednesday"
    },
    {
        "totalAmount": 331973.4,
        "totalOrders": 60,
        "buyerSummary": [
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 2,
                "totalWeight": 33
            },
            {
                "buyerName": "Muniyappa",
                "itemCount": 4,
                "totalWeight": 1122
            },
            {
                "buyerName": "PNT-Bhupathi",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "KRP-Munireddy g a G A Munireddy",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "KAD-Archana N",
                "itemCount": 6,
                "totalWeight": 368.4
            },
            {
                "buyerName": "KAD-MNB farm Fresh Fruits Vegetable",
                "itemCount": 3,
                "totalWeight": 115
            },
            {
                "buyerName": "Hsk Guru fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 30
            },
            {
                "buyerName": "RMN-Prakasha Gowda",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "Rekha store",
                "itemCount": 2,
                "totalWeight": 72.7
            },
            {
                "buyerName": "Selva",
                "itemCount": 3,
                "totalWeight": 384.7
            },
            {
                "buyerName": "MHP-Mahesh fruit and vegetables",
                "itemCount": 1,
                "totalWeight": 180
            },
            {
                "buyerName": "KAD - Loki",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Jayachandran N",
                "itemCount": 5,
                "totalWeight": 399
            },
            {
                "buyerName": "KAD-C V Varadaraju",
                "itemCount": 1,
                "totalWeight": 75
            },
            {
                "buyerName": "Mahadeva G R",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "KAD-Kalyani",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "Tip top mini mart",
                "itemCount": 1,
                "totalWeight": 54
            },
            {
                "buyerName": "MHP-Ganesh Fruits",
                "itemCount": 1,
                "totalWeight": 90
            },
            {
                "buyerName": "KRP-Gangamma",
                "itemCount": 3,
                "totalWeight": 15
            },
            {
                "buyerName": "KAD-Kk fruits",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "KRP-Jai P.S",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "KAD-Mohammed Hussain",
                "itemCount": 3,
                "totalWeight": 100
            },
            {
                "buyerName": "GDP-Gururaj",
                "itemCount": 2,
                "totalWeight": 988
            },
            {
                "buyerName": "KRP Chamundeswari fruits and veg",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "WHF-narayana",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Sunil",
                "itemCount": 2,
                "totalWeight": 140
            },
            {
                "buyerName": "Gattivinayaka provision store",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "MHP-Anasari",
                "itemCount": 3,
                "totalWeight": 69
            },
            {
                "buyerName": "RAVI KIRAN.T",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "PNT-JADDADI  PRADEEP SHETTY",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "KAD-Syed vegetables kadugodi",
                "itemCount": 2,
                "totalWeight": 105
            },
            {
                "buyerName": "RMN-Puttaranagappa",
                "itemCount": 4,
                "totalWeight": 273.5
            },
            {
                "buyerName": "RMN- PAVITHRA",
                "itemCount": 1,
                "totalWeight": 360
            },
            {
                "buyerName": "RMN- Manjunath",
                "itemCount": 2,
                "totalWeight": 27
            },
            {
                "buyerName": "MHP - shiva fruit",
                "itemCount": 2,
                "totalWeight": 69
            },
            {
                "buyerName": "TNS-hemlal yadav",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "HSK-Chandrashekhar",
                "itemCount": 2,
                "totalWeight": 30
            },
            {
                "buyerName": "MHP-Zam zam Fruits and vegetables",
                "itemCount": 2,
                "totalWeight": 150
            },
            {
                "buyerName": "TNS-Shasadri Vegtables",
                "itemCount": 2,
                "totalWeight": 866
            },
            {
                "buyerName": "RMN-Sri chowdeswari fruits and vegetables",
                "itemCount": 3,
                "totalWeight": 119.5
            },
            {
                "buyerName": "KAD-Arif",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Siddiq",
                "itemCount": 2,
                "totalWeight": 33
            },
            {
                "buyerName": "PNT- Sri THIRUMALAI HOT CHIPS",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "hsk Darshan",
                "itemCount": 2,
                "totalWeight": 445
            },
            {
                "buyerName": "Yogesh",
                "itemCount": 6,
                "totalWeight": 277.6
            },
            {
                "buyerName": "Freshop Essentials",
                "itemCount": 2,
                "totalWeight": 33
            },
            {
                "buyerName": "Mahadev provision store",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP-Dileep vegetables",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "PNT-Sri Lakshmi Narasimha  fnv varthur",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Vijay D",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-S Raheem",
                "itemCount": 2,
                "totalWeight": 36
            },
            {
                "buyerName": "HSK-Sharadha",
                "itemCount": 2,
                "totalWeight": 283
            },
            {
                "buyerName": "Durugappa Durugappa",
                "itemCount": 2,
                "totalWeight": 276
            },
            {
                "buyerName": "PNT - subramani",
                "itemCount": 3,
                "totalWeight": 101
            },
            {
                "buyerName": "RMN-Prabhu",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Lakshminarayana",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "RMN-Mehboob",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "MHP-Super Mart",
                "itemCount": 3,
                "totalWeight": 110
            },
            {
                "buyerName": "TNS-Guru murthy",
                "itemCount": 1,
                "totalWeight": 90
            },
            {
                "buyerName": "TNS-deepak Nishad",
                "itemCount": 1,
                "totalWeight": 9.9
            }
        ],
        "returnAmount": 58091.8,
        "returnWeight": 1156.1,
        "pendingAmount": 189314.6,
        "pendingAmountCount": 23,
        "employeeCount": 40,
        "itemCount": 46,
        "orderItems": [
            {
                "itemName": "Yalakki Crate Medium",
                "totalWeight": 75
            },
            {
                "itemName": "Papaya",
                "totalWeight": 337
            },
            {
                "itemName": "Shimla Apple (10Kg/Box)",
                "totalWeight": 3
            },
            {
                "itemName": "Banana Robusta Premium(15kg Crate)",
                "totalWeight": 495
            },
            {
                "itemName": "Yalakki Looms Premium",
                "totalWeight": 70.5
            },
            {
                "itemName": "Apple Shimla 100count (20Kg/box)",
                "totalWeight": 0
            },
            {
                "itemName": "Apple Shimla 200count (20Kg/box)",
                "totalWeight": 1
            },
            {
                "itemName": "Yalakki Looms Medium",
                "totalWeight": 575.8
            },
            {
                "itemName": "Banana Robusta Premium(18kg Crate)",
                "totalWeight": 5976
            },
            {
                "itemName": "Banana Robusta (Baby)",
                "totalWeight": 0
            },
            {
                "itemName": "Yalakki Crate Premium",
                "totalWeight": 795
            }
        ],
        "periodLabel": "Thursday"
    },
    {
        "totalAmount": 139236.2,
        "totalOrders": 48,
        "buyerSummary": [
            {
                "buyerName": "RMN-Puttaranagappa",
                "itemCount": 3,
                "totalWeight": 84
            },
            {
                "buyerName": "TNS-Muttu nayaka",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "HSK-Sindhur anrun",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Muniyappa",
                "itemCount": 3,
                "totalWeight": 694
            },
            {
                "buyerName": "MHP - shiva fruit",
                "itemCount": 2,
                "totalWeight": 41
            },
            {
                "buyerName": "RMN- Manjunath",
                "itemCount": 2,
                "totalWeight": 47
            },
            {
                "buyerName": "GDP-Devendra",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "KRP-Munireddy g a G A Munireddy",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Yashavanth",
                "itemCount": 1,
                "totalWeight": 30
            },
            {
                "buyerName": "HSK-Green mart",
                "itemCount": 2,
                "totalWeight": 33
            },
            {
                "buyerName": "RAITHRA SANTHE",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "TNS-Shoba m",
                "itemCount": 2,
                "totalWeight": 33
            },
            {
                "buyerName": "KRP-shivakumar",
                "itemCount": 2,
                "totalWeight": 42
            },
            {
                "buyerName": "MHP-Zam zam Fruits and vegetables",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "TNS-Shasadri Vegtables",
                "itemCount": 3,
                "totalWeight": 686
            },
            {
                "buyerName": "KAD-MNB farm Fresh Fruits Vegetable",
                "itemCount": 4,
                "totalWeight": 0
            },
            {
                "buyerName": "Muniraj",
                "itemCount": 1,
                "totalWeight": 21.5
            },
            {
                "buyerName": "Shamugan",
                "itemCount": 1,
                "totalWeight": 540
            },
            {
                "buyerName": "RMN-Prakasha Gowda",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-Babu",
                "itemCount": 2,
                "totalWeight": 76
            },
            {
                "buyerName": "Lakshmi candiments kannahalli",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "PNT- Sri THIRUMALAI HOT CHIPS",
                "itemCount": 1,
                "totalWeight": 24
            },
            {
                "buyerName": "HSK-Thirvathamma",
                "itemCount": 1,
                "totalWeight": 30
            },
            {
                "buyerName": "MHP-Mahesh fruit and vegetables",
                "itemCount": 2,
                "totalWeight": 36
            },
            {
                "buyerName": "Day by day fresh ( GDP)",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "HSK-Green nature",
                "itemCount": 2,
                "totalWeight": 119.9
            },
            {
                "buyerName": "WHF-N H enterprise malsandra road",
                "itemCount": 2,
                "totalWeight": 84
            },
            {
                "buyerName": "KRP-Dileep vegetables",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Jayachandran N",
                "itemCount": 4,
                "totalWeight": 153.5
            },
            {
                "buyerName": "KRP-Prashanth",
                "itemCount": 1,
                "totalWeight": 54
            },
            {
                "buyerName": "KAD-Vijay D",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-S Raheem",
                "itemCount": 4,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-Sharadha",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Durugappa Durugappa",
                "itemCount": 1,
                "totalWeight": 54
            },
            {
                "buyerName": "Tip top mini mart",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "KRP-Jai P.S",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "GDP-Gururaj",
                "itemCount": 1,
                "totalWeight": 360
            },
            {
                "buyerName": "Harender Hormavu",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Srichaudeswari vegitables",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Lakshminarayana",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "TNS-Shasadri vegetable s",
                "itemCount": 2,
                "totalWeight": 36
            },
            {
                "buyerName": "WHF-narayana",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Sunil",
                "itemCount": 3,
                "totalWeight": 87
            },
            {
                "buyerName": "KAD-Manjunath Bengaluru Horticulture 03",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "RAVI KIRAN.T",
                "itemCount": 2,
                "totalWeight": 33
            },
            {
                "buyerName": "Sri Sai Ram vegetables and provision store (Banjara layout)(",
                "itemCount": 1,
                "totalWeight": 20
            },
            {
                "buyerName": "MHP-samim",
                "itemCount": 1,
                "totalWeight": 5
            }
        ],
        "returnAmount": 25351.8,
        "returnWeight": 677.1,
        "pendingAmount": 71627,
        "pendingAmountCount": 16,
        "employeeCount": 40,
        "itemCount": 46,
        "orderItems": [
            {
                "itemName": "Yalakki Crate Medium",
                "totalWeight": 45
            },
            {
                "itemName": "Shimla Apple (10Kg/Box)",
                "totalWeight": 0
            },
            {
                "itemName": "Apple Shimla 175count (20Kg/box)",
                "totalWeight": 0
            },
            {
                "itemName": "Apple Shimla 100count (20Kg/box)",
                "totalWeight": 1
            },
            {
                "itemName": "Yalakki Looms Premium",
                "totalWeight": 48
            },
            {
                "itemName": "Poovan Looms Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Papaya (400 gms- 1 kg)",
                "totalWeight": 179
            },
            {
                "itemName": "Yalakki Looms Medium",
                "totalWeight": 168.9
            },
            {
                "itemName": "Banana Robusta Premium(18kg Crate)",
                "totalWeight": 3147
            },
            {
                "itemName": "Yalakki Crate Premium",
                "totalWeight": 170
            },
            {
                "itemName": "Banana Robusta (Baby)",
                "totalWeight": 36
            }
        ],
        "periodLabel": "Friday"
    }
];

export const mockDashboardDataWeekly: SellerConsoleDataResponse[] = [

    {
        "totalAmount": 0,
        "totalOrders": 0,
        "returnAmount": 0,
        "returnWeight": 0,
        "pendingAmount": 0,
        "pendingAmountCount": 0,
        "employeeCount": 40,
        "itemCount": 0,
        "periodLabel": "Week 1"
    },
    {
        "totalAmount": 326535.4,
        "totalOrders": 108,
        "buyerSummary": [
            {
                "buyerName": "KAD-Kk fruits",
                "itemCount": 1,
                "totalWeight": 33
            },
            {
                "buyerName": "manjunath manju",
                "itemCount": 2,
                "totalWeight": 180
            },
            {
                "buyerName": "RMN-Mehboob",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "Yogesh",
                "itemCount": 4,
                "totalWeight": 97.8
            },
            {
                "buyerName": "KRP-Freshly grow( banahalli)",
                "itemCount": 3,
                "totalWeight": 68
            },
            {
                "buyerName": "Tip top mini mart",
                "itemCount": 1,
                "totalWeight": 180
            },
            {
                "buyerName": "KAD-Mohammed Hussain",
                "itemCount": 4,
                "totalWeight": 80
            },
            {
                "buyerName": "Yashavanth",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "hsk Darshan",
                "itemCount": 4,
                "totalWeight": 45
            },
            {
                "buyerName": "Jayachandran N",
                "itemCount": 4,
                "totalWeight": 394
            },
            {
                "buyerName": "MHP-Ganesh Fruits",
                "itemCount": 3,
                "totalWeight": 267
            },
            {
                "buyerName": "KAD-C V Varadaraju",
                "itemCount": 2,
                "totalWeight": 121
            },
            {
                "buyerName": "Muniyappa",
                "itemCount": 4,
                "totalWeight": 705
            },
            {
                "buyerName": "KRP-Munireddy g a G A Munireddy",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "New jetta retailer",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-Sindhur anrun",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "WHF-Shanmugam",
                "itemCount": 3,
                "totalWeight": 380.5
            },
            {
                "buyerName": "KRP Chamundeswari fruits and veg",
                "itemCount": 3,
                "totalWeight": 74
            },
            {
                "buyerName": "KAD-Syed vegetables kadugodi",
                "itemCount": 4,
                "totalWeight": 85
            },
            {
                "buyerName": "Venkat",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Devendra M",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "KAD-Syed Zaheer",
                "itemCount": 1,
                "totalWeight": 30
            },
            {
                "buyerName": "KRP Manjunath Reddy",
                "itemCount": 4,
                "totalWeight": 36
            },
            {
                "buyerName": "MHP-Ratnamma",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "TNS-deepak Nishad",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "KAD-Manjunath Bengaluru Horticulture 03",
                "itemCount": 1,
                "totalWeight": 60
            },
            {
                "buyerName": "Manjunath YB",
                "itemCount": 1,
                "totalWeight": 108
            },
            {
                "buyerName": "GDP-Gowtham provisions store",
                "itemCount": 2,
                "totalWeight": 54
            },
            {
                "buyerName": "Slv pooja store ( bahan alli)",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "om sakthi wholesale shop",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "RMN- HASIRU",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "PNT-R K provision store",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "KAD-S Raheem",
                "itemCount": 2,
                "totalWeight": 143
            },
            {
                "buyerName": "KAD-Parashuram",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Muniraj",
                "itemCount": 3,
                "totalWeight": 28.5
            },
            {
                "buyerName": "RMN-SLV vegetables",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Sri Sai Ram vegetables and provision store (Banjara layout)(",
                "itemCount": 3,
                "totalWeight": 80
            },
            {
                "buyerName": "KRP-Ragavendra",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "RMN-Puttaranagappa",
                "itemCount": 3,
                "totalWeight": 384.5
            },
            {
                "buyerName": "HSK Sri ayyappa nandini parlour",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-Nature fresh fruit and vegetables",
                "itemCount": 1,
                "totalWeight": 234
            },
            {
                "buyerName": "TNS-Lokesh Lokesh",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Sunil",
                "itemCount": 6,
                "totalWeight": 130
            },
            {
                "buyerName": "HSK-Green mart",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Sri Tirumala",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "RMN-Prabhu",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "RMN- Halli Kai thota",
                "itemCount": 3,
                "totalWeight": 36
            },
            {
                "buyerName": "KRP-Manjamma",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "Fresh one veg & fruits",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Harish N",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "Rekha A @TNS ROUT@",
                "itemCount": 1,
                "totalWeight": 54
            },
            {
                "buyerName": "RMN- Manjunath",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "Fruits taj kadugodi",
                "itemCount": 2,
                "totalWeight": 64
            },
            {
                "buyerName": "KAD-MNB farm Fresh Fruits Vegetable",
                "itemCount": 3,
                "totalWeight": 48
            },
            {
                "buyerName": "KAD - Kalpana Provision Store",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "HSK venkateshwara fruits",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "PNT-Siddeshwara condiments and vegetables",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "WHF-narayana",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 2,
                "totalWeight": 83.3
            },
            {
                "buyerName": "KAD-Siddiq",
                "itemCount": 4,
                "totalWeight": 65
            },
            {
                "buyerName": "Vasudevan vegetables kodthi main road",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "TNS-Shasadri Vegtables",
                "itemCount": 4,
                "totalWeight": 1383.2
            },
            {
                "buyerName": "Gajula Sanjeeva Kumar",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "Sri Krishna fruit and vegetables",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Archana N",
                "itemCount": 3,
                "totalWeight": 770
            },
            {
                "buyerName": "KAD-Arif",
                "itemCount": 2,
                "totalWeight": 65
            },
            {
                "buyerName": "PNT-JADDADI  PRADEEP SHETTY",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "RAVI KIRAN.T",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Kalyani",
                "itemCount": 1,
                "totalWeight": 33
            },
            {
                "buyerName": "Shamugan",
                "itemCount": 1,
                "totalWeight": 180
            },
            {
                "buyerName": "WHF-Santosh Kumar",
                "itemCount": 3,
                "totalWeight": 33
            },
            {
                "buyerName": "RMN-SSB Home Needs",
                "itemCount": 3,
                "totalWeight": 70
            },
            {
                "buyerName": "KRP-Dileep vegetables",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-Green nature",
                "itemCount": 2,
                "totalWeight": 15
            },
            {
                "buyerName": "KAD-Bindhu",
                "itemCount": 1,
                "totalWeight": 90
            },
            {
                "buyerName": "HSK-Sharadha",
                "itemCount": 2,
                "totalWeight": 33
            },
            {
                "buyerName": "Gangadharappa MS Gangu",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Mamatha.v",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Slv uncle",
                "itemCount": 1,
                "totalWeight": 90
            },
            {
                "buyerName": "PNT-Bhupathi",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "KAD SLV fruits and vegetables Seegehalli",
                "itemCount": 1,
                "totalWeight": 30
            },
            {
                "buyerName": "HSK-Thirvathamma",
                "itemCount": 4,
                "totalWeight": 0
            },
            {
                "buyerName": "Freshop Essentials",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "HRM-Vinod",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-Babu",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "GDP-Amaresh",
                "itemCount": 2,
                "totalWeight": 162
            },
            {
                "buyerName": "KAD-Vijay D",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Sajjad  (GDP)",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP-Gangamma",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "MHP-samim",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "A1 Fruit and Vegitables",
                "itemCount": 1,
                "totalWeight": 20
            },
            {
                "buyerName": "HSK-Chandrashekhar",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD - Bappi",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Sri Renuka yallammadevi fnv varthur",
                "itemCount": 3,
                "totalWeight": 63
            },
            {
                "buyerName": "KRP-Prashanth",
                "itemCount": 2,
                "totalWeight": 36
            },
            {
                "buyerName": "Hsk Sri kanteshwara fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "PNT- Sri THIRUMALAI HOT CHIPS",
                "itemCount": 2,
                "totalWeight": 83
            },
            {
                "buyerName": "GDP-Gururaj",
                "itemCount": 3,
                "totalWeight": 451
            },
            {
                "buyerName": "HSK-A S fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "PNT-THE SAMOSA GARDEN (BALGERE)",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Divakar reddy",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "RMN-Sri chowdeswari fruits and vegetables",
                "itemCount": 2,
                "totalWeight": 107
            },
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 2,
                "totalWeight": 66
            },
            {
                "buyerName": "Harender Hormavu",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "RMN-Prakasha Gowda",
                "itemCount": 1,
                "totalWeight": 126
            },
            {
                "buyerName": "RMN- PAVITHRA",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "Hsk Guru fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "TNS-Harishkumar",
                "itemCount": 2,
                "totalWeight": 51
            }
        ],
        "returnAmount": 196912.74,
        "returnWeight": 4061.52,
        "pendingAmount": 142442.9,
        "pendingAmountCount": 30,
        "employeeCount": 40,
        "itemCount": 0,
        "orderItems": [
            {
                "itemName": "Banana Robusta (Baby)",
                "totalWeight": 0
            },
            {
                "itemName": "Banana Robusta Premium(18kg Crate)",
                "totalWeight": 6674
            },
            {
                "itemName": "Yalakki Crate Medium",
                "totalWeight": 64
            },
            {
                "itemName": "Apple Shimla 175count (20Kg/box)",
                "totalWeight": 1
            },
            {
                "itemName": "Yalakki Looms Medium",
                "totalWeight": 632.9
            },
            {
                "itemName": "Poovan Looms Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Apple Shimla 100count (20Kg/box)",
                "totalWeight": 2
            },
            {
                "itemName": "Yalakki Crate Premium",
                "totalWeight": 455
            },
            {
                "itemName": "Papaya (400 gms- 1 kg)",
                "totalWeight": 673
            },
            {
                "itemName": "Yalakki Looms Premium",
                "totalWeight": 145.9
            },
            {
                "itemName": "Poovan crate Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Shimla Apple (10Kg/Box)",
                "totalWeight": 1
            },
            {
                "itemName": "Apple Shimla 125count (20Kg/box)",
                "totalWeight": 0
            },
            {
                "itemName": "Nagpur Orange medium polo (20Kg/Crate)",
                "totalWeight": 275
            }
        ],
        "periodLabel": "Week 2"
    }
];

export const mockDashboardDataMonthly: SellerConsoleDataResponse[] = [
    {
        "totalAmount": 0,
        "totalOrders": 0,
        "returnAmount": 0,
        "returnWeight": 0,
        "pendingAmount": 0,
        "pendingAmountCount": 0,
        "employeeCount": 40,
        "itemCount": 0,
        "periodLabel": "January"
    },
    {
        "totalAmount": 0,
        "totalOrders": 0,
        "returnAmount": 0,
        "returnWeight": 0,
        "pendingAmount": 0,
        "pendingAmountCount": 0,
        "employeeCount": 40,
        "itemCount": 0,
        "periodLabel": "February"
    },
    {
        "totalAmount": 0,
        "totalOrders": 0,
        "returnAmount": 0,
        "returnWeight": 0,
        "pendingAmount": 0,
        "pendingAmountCount": 0,
        "employeeCount": 40,
        "itemCount": 0,
        "periodLabel": "March"
    },
    {
        "totalAmount": 0,
        "totalOrders": 0,
        "returnAmount": 0,
        "returnWeight": 0,
        "pendingAmount": 0,
        "pendingAmountCount": 0,
        "employeeCount": 40,
        "itemCount": 0,
        "periodLabel": "April"
    },
    {
        "totalAmount": 0,
        "totalOrders": 0,
        "returnAmount": 0,
        "returnWeight": 0,
        "pendingAmount": 0,
        "pendingAmountCount": 0,
        "employeeCount": 40,
        "itemCount": 0,
        "periodLabel": "May"
    },
    {
        "totalAmount": 0,
        "totalOrders": 0,
        "returnAmount": 0,
        "returnWeight": 0,
        "pendingAmount": 0,
        "pendingAmountCount": 0,
        "employeeCount": 40,
        "itemCount": 0,
        "periodLabel": "June"
    },
    {
        "totalAmount": 0,
        "totalOrders": 0,
        "returnAmount": 0,
        "returnWeight": 0,
        "pendingAmount": 0,
        "pendingAmountCount": 0,
        "employeeCount": 40,
        "itemCount": 0,
        "periodLabel": "July"
    },
    {
        "totalAmount": 0,
        "totalOrders": 0,
        "returnAmount": 0,
        "returnWeight": 0,
        "pendingAmount": 0,
        "pendingAmountCount": 0,
        "employeeCount": 40,
        "itemCount": 0,
        "periodLabel": "August"
    },
    {
        "totalAmount": 0,
        "totalOrders": 0,
        "returnAmount": 0,
        "returnWeight": 0,
        "pendingAmount": 0,
        "pendingAmountCount": 0,
        "employeeCount": 40,
        "itemCount": 0,
        "periodLabel": "September"
    },
    {
        "totalAmount": 326535.4,
        "totalOrders": 108,
        "buyerSummary": [
            {
                "buyerName": "PNT-THE SAMOSA GARDEN (BALGERE)",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Devendra M",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "RMN- Manjunath",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "RMN-Puttaranagappa",
                "itemCount": 3,
                "totalWeight": 384.5
            },
            {
                "buyerName": "KAD-Syed Zaheer",
                "itemCount": 1,
                "totalWeight": 30
            },
            {
                "buyerName": "RMN- HASIRU",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "RMN- Halli Kai thota",
                "itemCount": 3,
                "totalWeight": 36
            },
            {
                "buyerName": "KAD - Bappi",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Siddiq",
                "itemCount": 4,
                "totalWeight": 65
            },
            {
                "buyerName": "KAD-Bindhu",
                "itemCount": 1,
                "totalWeight": 90
            },
            {
                "buyerName": "Sri Renuka yallammadevi fnv varthur",
                "itemCount": 3,
                "totalWeight": 63
            },
            {
                "buyerName": "Sri Tirumala",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "Slv pooja store ( bahan alli)",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "Gajula Sanjeeva Kumar",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "Sri Sai Ram vegetables and provision store (Banjara layout)(",
                "itemCount": 3,
                "totalWeight": 80
            },
            {
                "buyerName": "KAD-Kk fruits",
                "itemCount": 1,
                "totalWeight": 33
            },
            {
                "buyerName": "MHP-Ratnamma",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "manjunath manju",
                "itemCount": 2,
                "totalWeight": 180
            },
            {
                "buyerName": "WHF-Santosh Kumar",
                "itemCount": 3,
                "totalWeight": 33
            },
            {
                "buyerName": "HSK-Chandrashekhar",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-Thirvathamma",
                "itemCount": 4,
                "totalWeight": 0
            },
            {
                "buyerName": "New jetta retailer",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "Vasudevan vegetables kodthi main road",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "RMN-Prakasha Gowda",
                "itemCount": 1,
                "totalWeight": 126
            },
            {
                "buyerName": "MHP-Ganesh Fruits",
                "itemCount": 3,
                "totalWeight": 267
            },
            {
                "buyerName": "KRP-Gangamma",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "PNT- Sri THIRUMALAI HOT CHIPS",
                "itemCount": 2,
                "totalWeight": 83
            },
            {
                "buyerName": "KRP Manjunath Reddy",
                "itemCount": 4,
                "totalWeight": 36
            },
            {
                "buyerName": "KAD-Parashuram",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Manjunath YB",
                "itemCount": 1,
                "totalWeight": 108
            },
            {
                "buyerName": "RMN-Sri chowdeswari fruits and vegetables",
                "itemCount": 2,
                "totalWeight": 107
            },
            {
                "buyerName": "KRP-Munireddy g a G A Munireddy",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "MHP-samim",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "Sajjad  (GDP)",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Kalyani",
                "itemCount": 1,
                "totalWeight": 33
            },
            {
                "buyerName": "KAD-Mohammed Hussain",
                "itemCount": 4,
                "totalWeight": 80
            },
            {
                "buyerName": "TNS-Lokesh Lokesh",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Vijay D",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Gangadharappa MS Gangu",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Mamatha.v",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "PNT-Bhupathi",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "GDP-Gururaj",
                "itemCount": 3,
                "totalWeight": 451
            },
            {
                "buyerName": "HSK-Green mart",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Fruits taj kadugodi",
                "itemCount": 2,
                "totalWeight": 64
            },
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 2,
                "totalWeight": 66
            },
            {
                "buyerName": "Hsk Sri kanteshwara fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "GDP-Gowtham provisions store",
                "itemCount": 2,
                "totalWeight": 54
            },
            {
                "buyerName": "KRP-Ragavendra",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "RMN- PAVITHRA",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "HSK-Green nature",
                "itemCount": 2,
                "totalWeight": 15
            },
            {
                "buyerName": "TNS-Harishkumar",
                "itemCount": 2,
                "totalWeight": 51
            },
            {
                "buyerName": "KAD-Arif",
                "itemCount": 2,
                "totalWeight": 65
            },
            {
                "buyerName": "KAD-Hussain",
                "itemCount": 2,
                "totalWeight": 83.3
            },
            {
                "buyerName": "Venkat",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP Chamundeswari fruits and veg",
                "itemCount": 3,
                "totalWeight": 74
            },
            {
                "buyerName": "Muniraj",
                "itemCount": 3,
                "totalWeight": 28.5
            },
            {
                "buyerName": "RMN-Mehboob",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "Tip top mini mart",
                "itemCount": 1,
                "totalWeight": 180
            },
            {
                "buyerName": "Slv uncle",
                "itemCount": 1,
                "totalWeight": 90
            },
            {
                "buyerName": "Harender Hormavu",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD-Manjunath Bengaluru Horticulture 03",
                "itemCount": 1,
                "totalWeight": 60
            },
            {
                "buyerName": "Muniyappa",
                "itemCount": 4,
                "totalWeight": 705
            },
            {
                "buyerName": "KAD - Kalpana Provision Store",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "KRP-Prashanth",
                "itemCount": 2,
                "totalWeight": 36
            },
            {
                "buyerName": "GDP-Amaresh",
                "itemCount": 2,
                "totalWeight": 162
            },
            {
                "buyerName": "Freshop Essentials",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "A1 Fruit and Vegitables",
                "itemCount": 1,
                "totalWeight": 20
            },
            {
                "buyerName": "WHF-narayana",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "KAD-Archana N",
                "itemCount": 3,
                "totalWeight": 770
            },
            {
                "buyerName": "PNT-JADDADI  PRADEEP SHETTY",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Sri Krishna fruit and vegetables",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Fresh one veg & fruits",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Jayachandran N",
                "itemCount": 4,
                "totalWeight": 394
            },
            {
                "buyerName": "RMN-SLV vegetables",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "RMN-Prabhu",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "Yashavanth",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "HSK-Babu",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "Divakar reddy",
                "itemCount": 1,
                "totalWeight": 18
            },
            {
                "buyerName": "KAD-C V Varadaraju",
                "itemCount": 2,
                "totalWeight": 121
            },
            {
                "buyerName": "RMN-SSB Home Needs",
                "itemCount": 3,
                "totalWeight": 70
            },
            {
                "buyerName": "om sakthi wholesale shop",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "RAVI KIRAN.T",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-A S fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "Sunil",
                "itemCount": 6,
                "totalWeight": 130
            },
            {
                "buyerName": "Harish N",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "TNS-deepak Nishad",
                "itemCount": 2,
                "totalWeight": 18
            },
            {
                "buyerName": "KAD-Syed vegetables kadugodi",
                "itemCount": 4,
                "totalWeight": 85
            },
            {
                "buyerName": "hsk Darshan",
                "itemCount": 4,
                "totalWeight": 45
            },
            {
                "buyerName": "KRP-Manjamma",
                "itemCount": 3,
                "totalWeight": 0
            },
            {
                "buyerName": "KRP-Dileep vegetables",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK Sri ayyappa nandini parlour",
                "itemCount": 1,
                "totalWeight": 0
            },
            {
                "buyerName": "HSK-Nature fresh fruit and vegetables",
                "itemCount": 1,
                "totalWeight": 234
            },
            {
                "buyerName": "KRP-Freshly grow( banahalli)",
                "itemCount": 3,
                "totalWeight": 68
            },
            {
                "buyerName": "KAD-MNB farm Fresh Fruits Vegetable",
                "itemCount": 3,
                "totalWeight": 48
            },
            {
                "buyerName": "HSK-Sharadha",
                "itemCount": 2,
                "totalWeight": 33
            },
            {
                "buyerName": "PNT-R K provision store",
                "itemCount": 1,
                "totalWeight": 36
            },
            {
                "buyerName": "Shamugan",
                "itemCount": 1,
                "totalWeight": 180
            },
            {
                "buyerName": "Hsk Guru fruits and vegetables",
                "itemCount": 1,
                "totalWeight": 15
            },
            {
                "buyerName": "Rekha A @TNS ROUT@",
                "itemCount": 1,
                "totalWeight": 54
            },
            {
                "buyerName": "KAD-S Raheem",
                "itemCount": 2,
                "totalWeight": 143
            },
            {
                "buyerName": "TNS-Shasadri Vegtables",
                "itemCount": 4,
                "totalWeight": 1383.2
            },
            {
                "buyerName": "HSK venkateshwara fruits",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "Yogesh",
                "itemCount": 4,
                "totalWeight": 97.8
            },
            {
                "buyerName": "HSK-Sindhur anrun",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "KAD SLV fruits and vegetables Seegehalli",
                "itemCount": 1,
                "totalWeight": 30
            },
            {
                "buyerName": "WHF-Shanmugam",
                "itemCount": 3,
                "totalWeight": 380.5
            },
            {
                "buyerName": "HRM-Vinod",
                "itemCount": 2,
                "totalWeight": 0
            },
            {
                "buyerName": "PNT-Siddeshwara condiments and vegetables",
                "itemCount": 1,
                "totalWeight": 18
            }
        ],
        "returnAmount": 196912.74,
        "returnWeight": 4061.52,
        "pendingAmount": 142442.9,
        "pendingAmountCount": 30,
        "employeeCount": 40,
        "itemCount": 0,
        "orderItems": [
            {
                "itemName": "Yalakki Crate Medium",
                "totalWeight": 64
            },
            {
                "itemName": "Yalakki Crate Premium",
                "totalWeight": 455
            },
            {
                "itemName": "Banana Robusta Premium(18kg Crate)",
                "totalWeight": 6674
            },
            {
                "itemName": "Nagpur Orange medium polo (20Kg/Crate)",
                "totalWeight": 275
            },
            {
                "itemName": "Papaya (400 gms- 1 kg)",
                "totalWeight": 673
            },
            {
                "itemName": "Yalakki Looms Premium",
                "totalWeight": 145.9
            },
            {
                "itemName": "Banana Robusta (Baby)",
                "totalWeight": 0
            },
            {
                "itemName": "Shimla Apple (10Kg/Box)",
                "totalWeight": 1
            },
            {
                "itemName": "Poovan crate Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Apple Shimla 125count (20Kg/box)",
                "totalWeight": 0
            },
            {
                "itemName": "Apple Shimla 100count (20Kg/box)",
                "totalWeight": 2
            },
            {
                "itemName": "Apple Shimla 175count (20Kg/box)",
                "totalWeight": 1
            },
            {
                "itemName": "Poovan Looms Premium",
                "totalWeight": 0
            },
            {
                "itemName": "Yalakki Looms Medium",
                "totalWeight": 632.9
            }
        ],
        "periodLabel": "October"
    }
];
