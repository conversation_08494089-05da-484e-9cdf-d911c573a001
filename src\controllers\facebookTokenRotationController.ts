import { Request, Response } from 'express';
import facebookTokenRotationService from '../services/facebookTokenRotationService.js';
import logger from '@utils/express-logger.js';

class FacebookTokenRotationController {
    /**
     * Rotate token for a specific seller
     * POST /api/facebook/token-rotation/:sellerId
     */
    async rotateSellerToken(req: Request, res: Response): Promise<void> {
        try {
            const { sellerId } = req.params;
            const { revokeOldToken = true } = req.body;

            if (!sellerId) {
                res.status(400).json({
                    success: false,
                    error: 'Seller ID is required',
                    timestamp: new Date().toISOString()
                });
                return;
            }

            logger.info('🔄 Token rotation request received', {
                sellerId,
                revokeOldToken,
                requestId: req.headers['x-request-id'] || 'unknown'
            });

            const result = await facebookTokenRotationService.rotateSellerToken(sellerId, revokeOldToken);

            if (!result.ok) {
                res.status(404).json({
                    success: false,
                    error: result.err,
                    timestamp: new Date().toISOString()
                });
                return;
            }

            res.status(200).json({
                success: true,
                message: 'Token rotation completed successfully',
                data: result.data,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            logger.error('❌ Error in rotateSellerToken controller:', error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Internal server error',
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Health check endpoint for token rotation service
     * GET /api/facebook/token-rotation/health
     */
    async healthCheck(req: Request, res: Response): Promise<void> {
        try {
            const healthStatus = await facebookTokenRotationService.getHealthStatus();

            res.status(200).json({
                success: true,
                data: healthStatus,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            logger.error('❌ Error in healthCheck controller:', error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Service unhealthy',
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Manually trigger token rotation for all expiring tokens
     * POST /api/facebook/token-rotation/trigger
     */
    async triggerManualRotation(req: Request, res: Response): Promise<void> {
        try {
            logger.info('🔄 Manual token rotation trigger request received');

            const result = await facebookTokenRotationService.triggerManualRotation();

            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: result.message,
                    timestamp: new Date().toISOString()
                });
            } else {
                res.status(400).json({
                    success: false,
                    error: result.message,
                    timestamp: new Date().toISOString()
                });
            }

        } catch (error) {
            logger.error('❌ Error in triggerManualRotation controller:', error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Internal server error',
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Get cron service status
     * GET /api/facebook/token-rotation/status
     */
    async getCronStatus(req: Request, res: Response): Promise<void> {
        try {
            const status = facebookTokenRotationService.getCronServiceStatus();

            res.status(200).json({
                success: true,
                data: {
                    service: 'Facebook Token Rotation Cron Service',
                    status: status.isRunning ? 'running' : 'stopped',
                    lastRunTime: status.lastRunTime?.toISOString() || null,
                    nextRunTime: status.nextRunTime?.toISOString() || null,
                    isCurrentlyExecuting: status.isCurrentlyExecuting
                },
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            logger.error('❌ Error in getCronStatus controller:', error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Internal server error',
                timestamp: new Date().toISOString()
            });
        }
    }
}

export default FacebookTokenRotationController; 