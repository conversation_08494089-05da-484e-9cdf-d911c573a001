import { ActionFunctionArgs, json, redirect } from "@remix-run/node";
import { updateTicket } from "~/services/ticketService";
import { SupportTicketStatus } from "~/types/api/businessConsoleService/Tickets";

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const ticketId = Number(formData.get("ticketId"));
  const status = formData.get("status") as SupportTicketStatus;
  
  if (!ticketId || !status) {
    return json({ 
      success: false, 
      error: "Missing required fields" 
    });
  }
  
  // Validate that status is one of the allowed values
  if (status !== "OPEN" && status !== "WIP" && status !== "CLOSED") {
    return json({ 
      success: false, 
      error: "Invalid status value. Must be OPEN, WIP, or CLOSED" 
    });
  }
  
  try {
    // We need to get the current ticket first, then update just the status
    // This is a simplified version - in a real app you'd fetch the ticket first
    const ticketData = {
      ticketId,
      status,
      // These fields would typically come from the fetched ticket data
      userId: 0,
      userName: "",
      userMobileNo: "",
      ticketType: "",
      orderGroupId: 0,
      description: "",
      closingRemarks: "",
      createdDate: new Date().toISOString(),
      distinctId: "",
      requestedCallBack: false,
      userType: "",
      lastModifiedDate: new Date().toISOString(),
      sellerId: 0,
      sellerName: ""
    };
    
    const result = await updateTicket(ticketId, ticketData, request);
    
    if (result.statusCode >= 400) {
      return json({ 
        success: false, 
        error: "Failed to update ticket status" 
      });
    }
    
    return json({ 
      success: true,
      message: "Ticket status updated successfully"
    });
  } catch (error) {
    console.error("Error updating status:", error);
    return json({ 
      success: false, 
      error: "Failed to update status" 
    });
  }
};

// Redirect direct visits to this route back to the tickets page
export const loader = () => redirect("/home/<USER>"); 