import { z } from 'zod';
import { templates } from '@/services/templateService.js';

// Helper function to get template by ID
const getTemplate = (templateId: string) => {
    return templates.find(template => template.templateId === templateId);
};

// Helper function to extract variable names from template string
const extractVariables = (text: string): string[] => {
    const matches = text.match(/{{([^}]+)}}/g) || [];
    return matches.map(match => match.replace(/[{}]/g, ''));
};

// Component Variables Schema
export const ComponentVariablesSchema = z.object({
    header: z.record(z.string()).optional(),
    body: z.record(z.string()).optional(),
    button: z.record(z.string()).optional(),
    footer: z.record(z.string()).optional()
});

// Template Request Schema
export const SendTemplateRequestSchema = z.object({
    templateId: z.string().refine(
        (id) => getTemplate(id) !== undefined,
        (id) => ({ message: `Template with ID '${id}' not found` })
    ),
    targetPhoneNumber: z.string().min(10),
    wabPhoneNumberId: z.string().optional(),
    variables: ComponentVariablesSchema,
    accessToken: z.string().optional()
}).refine((data) => {
    const template = getTemplate(data.templateId);
    if (!template) return true; // Already handled by templateId validation

    const errors: string[] = [];

    // Validate header variables if template has header
    if (template.headerVariables && template.headerVariables.length > 0) {
        const headerVars = template.headerVariables;
        const missingHeaderVars = headerVars.filter(
            varName => !data.variables.header?.[varName]
        );
        if (missingHeaderVars.length > 0) {
            errors.push(`Missing header variables: ${missingHeaderVars.join(', ')}`);
        }
    }

    // Validate body variables
    const missingBodyVars = template.bodyVariables?.filter(
        varName => !data.variables.body?.[varName]
    );
    if (missingBodyVars && missingBodyVars.length > 0) {
        errors.push(`Missing body variables: ${missingBodyVars.join(', ')}`);
    }

    // Validate button variables
    const missingButtonVars = template.buttonsVariables?.filter(
        varName => !data.variables.button?.[varName.value]
    );
    if (missingButtonVars && missingButtonVars.length > 0) {
        errors.push(`Missing button variables: ${missingButtonVars.join(', ')}`);
    }

    // Return validation result
    return errors.length === 0 ? true : { message: errors.join('; ') };
}, {
    message: "Template variables validation failed"
});

export type ValidatedTemplateRequest = z.infer<typeof SendTemplateRequestSchema>; 
