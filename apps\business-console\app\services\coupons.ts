import { ApiResponse } from "~/types/api/Api";
import { API_BASE_URL, apiRequest } from "~/utils/api";
import {
  CouponDetailsType,
  CouponPreloadType,
  AddCouponType,
  CouponType,
} from "~/types/api/businessConsoleService/coupons";

export async function getCoupons(
  request?: Request
): Promise<ApiResponse<CouponDetailsType[]>> {
  const url = new URL(`${API_BASE_URL}/inventory/seller/coupons`);

  const response = await apiRequest<CouponDetailsType[]>(
    url.toString(),
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch coupons data");
  }
}

export async function getCouponPreload(
  request?: Request
): Promise<ApiResponse<CouponPreloadType>> {
  const response = Promise.resolve({
    data: couponPreloadData,
    status: 200,
  });

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch coupons data");
  }
}

export async function addCoupon(
  payload: AddCouponType,
  request?: Request
): Promise<ApiResponse<CouponDetailsType>> {
  const url = new URL(`${API_BASE_URL}/inventory/seller/coupon`);

  const response = await apiRequest<CouponDetailsType>(
    url.toString(),
    "POST",
    payload,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to add coupon");
  }
}

export async function getCoupon(
  couponId: number,
  request?: Request
): Promise<ApiResponse<CouponType>> {
  const url = new URL(`${API_BASE_URL}/inventory/seller/coupon/${couponId}`);

  const response = await apiRequest<CouponType>(
    url.toString(),
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch coupon data");
  }
}

export async function editCoupon(
  couponId: number,
  payload: CouponType,
  request?: Request
): Promise<ApiResponse<CouponType>> {
  const url = new URL(`${API_BASE_URL}/inventory/seller/coupon/${couponId}`);

  const response = await apiRequest<CouponType>(
    url.toString(),
    "PUT",
    payload,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to update coupon");
  }
}

export async function deleteCoupon(
  couponId: number,
  request?: Request
): Promise<ApiResponse<void>> {
  const url = new URL(`${API_BASE_URL}/inventory/seller/coupon/${couponId}`);
  const response = await apiRequest<void>(
    url.toString(),
    "DELETE",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to delete coupon");
  }
}

const couponPreloadData: CouponPreloadType = {
  couponTypes: [
    {
      label: "New Customer",
      value: "newCustomer",
    },
    {
      label: "High Order Value",
      value: "highOrderValue",
    },
  ],
  couponTypesConfigurations: [
    {
      label: "Free Item",
      value: "freeItem",
      configuration: {
        discount: {
          label: "Discount",
          value: "discount",
          description:
            "Enter the discount value which would be provided to the customer.",
          properties: [
            {
              label: "Item",
              value: "item",
              operatorConfig: {
                input: "itemSearch",
              },
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
        filter: {
          label: "Filter/ Condition",
          value: "condition",
          description:
            "Enter the discount will be only available to the users who satisfy the following conditions.",
          properties: [
            {
              label: "Min Order Amount",
              value: "minOrderAmount",
              operatorConfig: {
                input: "dropdown",
                options: [
                  {
                    label: "Greater Than",
                    value: "GT",
                  },
                  {
                    label: "Greater Than or Equals To",
                    value: "GTE",
                  },
                  {
                    label: "Equals To",
                    value: "EQ",
                  },
                ],
              },
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Min Quantity",
              value: "minQuantity",
              operatorConfig: {
                input: "dropdown",
                options: [
                  {
                    label: "Greater Than",
                    value: "GT",
                  },
                  {
                    label: "Greater Than or Equals To",
                    value: "GTE",
                  },
                  {
                    label: "Equals To",
                    value: "EQ",
                  },
                ],
              },
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
        validity: {
          label: "Validity",
          value: "validity",
          description:
            "The coupon code will only be valid till the configured.",
          properties: [
            {
              label: "Valid From",
              value: "validFrom",
              valueConfig: {
                input: "date",
              },
            },
            {
              label: "Valid To",
              value: "validTo",
              valueConfig: {
                input: "date",
              },
            },
            {
              label: "Max Total Usage",
              value: "maxUseTotal",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "First N Orders",
              value: "firstNOrders",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Max Usage Per Buyer",
              value: "maxUsePerBuyer",
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
      },
    },
    {
      label: "Fixed Amount",
      value: "fixedAmount",
      configuration: {
        discount: {
          label: "Discount",
          value: "discount",
          description:
            "Enter the discount value which would be provided to the customer.",
          properties: [
            {
              label: "Amount",
              value: "discountFixed",
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
        filter: {
          label: "Filter/ Condition",
          value: "condition",
          description:
            "Enter the discount will be only available to the users who satisfy the following conditions.",
          properties: [
            {
              label: "Min Order Amount",
              value: "minOrderAmount",
              operatorConfig: {
                input: "dropdown",
                options: [
                  {
                    label: "Greater Than",
                    value: "GT",
                  },
                  {
                    label: "Greater Than or Equals To",
                    value: "GTE",
                  },
                  {
                    label: "Equals To",
                    value: "EQ",
                  },
                ],
              },
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Min Quantity",
              value: "minQuantity",
              operatorConfig: {
                input: "dropdown",
                options: [
                  {
                    label: "Greater Than",
                    value: "GT",
                  },
                  {
                    label: "Greater Than or Equals To",
                    value: "GTE",
                  },
                  {
                    label: "Equals To",
                    value: "EQ",
                  },
                ],
              },
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
        validity: {
          label: "Validity",
          value: "validity",
          description:
            "The coupon code will only be valid till the configured.",
          properties: [
            {
              label: "Valid From",
              value: "validFrom",
              valueConfig: {
                input: "date",
              },
            },
            {
              label: "Valid To",
              value: "validTo",
              valueConfig: {
                input: "date",
              },
            },
            {
              label: "Max Total Usage",
              value: "maxUseTotal",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "First N Orders",
              value: "firstNOrders",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Max Usage Per Buyer",
              value: "maxUsePerBuyer",
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
      },
    },
    {
      label: "Percentage",
      value: "percentage",
      configuration: {
        discount: {
          label: "Discount",
          value: "discount",
          description:
            "Enter the discount value which would be provided to the customer.",
          properties: [
            {
              label: "Percentage",
              value: "discountPercent",
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
        filter: {
          label: "Filter/ Condition",
          value: "condition",
          description:
            "Enter the discount will be only available to the users who satisfy the following conditions.",
          properties: [
            {
              label: "Min Order Amount",
              value: "minOrderAmount",
              operatorConfig: {
                input: "dropdown",
                options: [
                  {
                    label: "Greater Than",
                    value: "GT",
                  },
                  {
                    label: "Greater Than or Equals To",
                    value: "GTE",
                  },
                  {
                    label: "Equals To",
                    value: "EQ",
                  },
                ],
              },
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Min Quantity",
              value: "minQuantity",
              operatorConfig: {
                input: "dropdown",
                options: [
                  {
                    label: "Greater Than",
                    value: "GT",
                  },
                  {
                    label: "Greater Than or Equals To",
                    value: "GTE",
                  },
                  {
                    label: "Equals To",
                    value: "EQ",
                  },
                ],
              },
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
        validity: {
          label: "Validity",
          value: "validity",
          description:
            "The coupon code will only be valid till the configured.",
          properties: [
            {
              label: "Valid From",
              value: "validFrom",
              valueConfig: {
                input: "date",
              },
            },
            {
              label: "Valid To",
              value: "validTo",
              valueConfig: {
                input: "date",
              },
            },
            {
              label: "Max Total Usage",
              value: "maxUseTotal",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "First N Orders",
              value: "firstNOrders",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Max Usage Per Buyer",
              value: "maxUsePerBuyer",
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
      },
    },
    {
      label: "New Customer",
      value: "newCustomer",
      configuration: {
        discount: {
          label: "Discount",
          value: "discount",
          description:
            "Enter the discount value which would be provided to the customer.",
          properties: [
            {
              label: "Amount",
              value: "discountFixed",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Percentage",
              value: "discountPercent",
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [
            {
              input: "text",
              type: "fixed",
              label: "Max Discount Value",
              value: "maxDiscountAmount",
            },
            {
              input: "segment",
              type: "propertyMethod",
              label: "MaximumMinimum",
              value: "hybDiscStrategy",
              options: [
                { label: "Minimum Of", value: "MIN" },
                { label: "Maximum Of", value: "MAX" },
              ],
            },
          ],
        },
        filter: {
          label: "Filter/ Condition",
          value: "condition",
          description:
            "Enter the discount will be only available to the users who satisfy the following conditions.",
          properties: [
            {
              label: "Min Order Amount",
              value: "minOrderAmount",
              operatorConfig: {
                input: "dropdown",
                options: [
                  {
                    label: "Greater Than",
                    value: "GT",
                  },
                  {
                    label: "Greater Than or Equals To",
                    value: "GTE",
                  },
                  {
                    label: "Equals To",
                    value: "EQ",
                  },
                ],
              },
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Min Quantity",
              value: "minQuantity",
              operatorConfig: {
                input: "dropdown",
                options: [
                  {
                    label: "Greater Than",
                    value: "GT",
                  },
                  {
                    label: "Greater Than or Equals To",
                    value: "GTE",
                  },
                  {
                    label: "Equals To",
                    value: "EQ",
                  },
                ],
              },
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
        validity: {
          label: "Validity",
          value: "validity",
          description:
            "The coupon code will only be valid till the configured.",
          properties: [
            {
              label: "Valid From",
              value: "validFrom",
              valueConfig: {
                input: "date",
              },
            },
            {
              label: "Valid To",
              value: "validTo",
              valueConfig: {
                input: "date",
              },
            },
            {
              label: "Max Total Usage",
              value: "maxUseTotal",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "First N Orders",
              value: "firstNOrders",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Max Usage Per Buyer",
              value: "maxUsePerBuyer",
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
      },
    },
    {
      label: "High Order Value",
      value: "highOrderValue",
      configuration: {
        discount: {
          label: "Discount",
          value: "discount",
          description:
            "Enter the discount value which would be provided to the customer.",
          properties: [
            {
              label: "Amount",
              value: "discountFixed",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Percentage",
              value: "discountPercent",
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [
            {
              input: "text",
              type: "fixed",
              label: "Max Discount Value",
              value: "maxDiscountAmount",
            },
            {
              input: "segment",
              type: "propertyMethod",
              label: "MaximumMinimum",
              value: "hybDiscStrategy",
              options: [
                { label: "Minimum Of", value: "MIN" },
                { label: "Maximum Of", value: "MAX" },
              ],
            },
          ],
        },
        filter: {
          label: "Filter/ Condition",
          value: "condition",
          description:
            "Enter the discount will be only available to the users who satisfy the following conditions.",
          properties: [
            {
              label: "Min Order Amount",
              value: "minOrderAmount",
              operatorConfig: {
                input: "dropdown",
                options: [
                  {
                    label: "Greater Than",
                    value: "GT",
                  },
                  {
                    label: "Greater Than or Equals To",
                    value: "GTE",
                  },
                  {
                    label: "Equals To",
                    value: "EQ",
                  },
                ],
              },
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Min Quantity",
              value: "minQuantity",
              operatorConfig: {
                input: "dropdown",
                options: [
                  {
                    label: "Greater Than",
                    value: "GT",
                  },
                  {
                    label: "Greater Than or Equals To",
                    value: "GTE",
                  },
                  {
                    label: "Equals To",
                    value: "EQ",
                  },
                ],
              },
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
        validity: {
          label: "Validity",
          value: "validity",
          description:
            "The coupon code will only be valid till the configured.",
          properties: [
            {
              label: "Valid From",
              value: "validFrom",
              valueConfig: {
                input: "date",
              },
            },
            {
              label: "Valid To",
              value: "validTo",
              valueConfig: {
                input: "date",
              },
            },
            {
              label: "Max Total Usage",
              value: "maxUseTotal",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "First N Orders",
              value: "firstNOrders",
              valueConfig: {
                input: "text",
              },
            },
            {
              label: "Max Usage Per Buyer",
              value: "maxUsePerBuyer",
              valueConfig: {
                input: "text",
              },
            },
          ],
          propertyConfig: [],
        },
      },
    },
  ],
};
