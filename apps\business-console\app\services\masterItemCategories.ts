import { BusinessData } from "./../types/api/businessConsoleService/SellerManagement";
import { CategoryItem } from "~/components/masterItems/searchableCategories";
import { useToast } from "~/components/ui/ToastProvider";
import { ApiResponse } from "~/types/api/Api";
import { SellerArea } from "~/types/api/businessConsoleService/Areas";
import {
  CreateSellerRequest,
  CreateUserRequest,
  ItemsList,
  MasterItemCategories,
  SelectedSeller,
  Seller,
  SellerAreas,
  SellerItemCategories,
} from "~/types/api/businessConsoleService/MasterItemCategory";
import {
  Agent,
  AgentUser,
  NetWorkDetails,
  SellerUser,
} from "~/types/api/businessConsoleService/netWorkinfo";
import {
  networkAgents,
  SellerConfig,
  smSellerArea,
} from "~/types/api/businessConsoleService/SellerManagement";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getMasterItemCategory(
  request: Request,
  pageNo: number,
  pageSize: number,
  searchBy: boolean,
  level: number | undefined,
  matchBy?: string
): Promise<ApiResponse<MasterItemCategories[]>> {
  // Base URL for the API
  const baseUrl = `${API_BASE_URL}/bc/mnetadmin/categories`;

  // Initialize query parameters
  const queryParams = new URLSearchParams();

  // Handle scenarios based on `searchBy` and `level`
  if (searchBy && level && level > 0) {
    queryParams.append("level", level.toString());
    queryParams.append("matchBy", matchBy || "");
  } else if (searchBy && level === 0) {
    queryParams.append("matchBy", matchBy || "");
  } else if (!searchBy && level && level > 0) {
    queryParams.append("level", level.toString());
  }

  // Add pagination parameters (these are common across all cases)
  queryParams.append("pageNo", pageNo.toString());
  queryParams.append("size", pageSize.toString());

  // Construct the final URL
  const url = `${baseUrl}?${queryParams.toString()}`;

  try {
    const response = await apiRequest<MasterItemCategories[]>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch getMasterItemCategory");
    }
  } catch (error) {
    throw new Error(`Error in getMasterItemCategory: `);
  }
}

export async function UpdateMasterItemCategory(
  ondcDomain: "RET10" | "RET11",
  categoryName: string,
  categoryLevel: number,
  sequence: number,
  picture: string,
  parentCat: CategoryItem[],
  request?: Request,
  mode?: string,
  icId?: number
): Promise<ApiResponse<MasterItemCategories>> {
  const parentCategories = parentCat.map((item) => ({
    id: item.numericId, // Map numericId to id
    name: item.value, // Map value to name
  }));

  const responseBody = {
    ondcDomain: ondcDomain,
    name: categoryName,
    level: categoryLevel,
    picture: picture,
    sequence: sequence,
    parentCategories: parentCategories,
  };

  console.log(responseBody, "99999999");
  console.log(responseBody.parentCategories, "787888777777");

  const url =
    mode === "Edit"
      ? `${API_BASE_URL}/bc/mnetadmin/update_icategory/${icId}`
      : `${API_BASE_URL}/bc/mnetadmin/create_icategory`;
  const method = mode === "Edit" ? "PUT" : "POST";

  const response = await apiRequest<MasterItemCategories>(
    url,
    method,
    responseBody,
    {},
    true,
    request
  );

  if (response !== undefined) {
    if (response !== undefined) {
      // Assuming useToast is accessible via some React component
      const { showToast } = useToast();
      showToast("Operation successful!", "success");
      return response;
    }
    return response;
  } else {
    const { showToast } = useToast();
    showToast(`Operation failed: `);
    throw new Error(
      response ? `API call failed with status ` : "Failed to createMasterItem"
    );
  }
}

export async function getNetWorkItemCategories(
  netWorkId: number,
  request?: Request
): Promise<ApiResponse<MasterItemCategories[]>> {
  const response = await apiRequest<MasterItemCategories[]>(
    `${API_BASE_URL}/bc/mnetadmin/network/${netWorkId}/categories`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch getNetWorkItemCategories");
  }
}

export async function addNetworkItemCategory(
  netWorkId: number,
  categoryId: number,
  request?: Request
): Promise<ApiResponse<{ res: ok }>> {
  const response = await apiRequest(
    `${API_BASE_URL}/bc/mnetbusiness/create_nicategory?networkId=${netWorkId}&itemCategoryId=${categoryId}`,
    "POST",
    undefined,
    {},
    true,
    request
  );

  if (response !== null) {
    console.log("Response:", response.data);
    return response;
  } else {
    console.error("Failed with status:");
    throw new Error(
      response
        ? `API call failed with status `
        : "Failed to fetch addNetworkItemCategory"
    );
  }
}
export async function getSelectedNetworkDetails(
  netWorkId: number,
  request?: Request
): Promise<ApiResponse<NetWorkDetails>> {
  const response = await apiRequest<NetWorkDetails>(
    `${API_BASE_URL}/bc/mnetadmin/network/${netWorkId}/details`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch getSelectedNetworkDetails");
  }
}
export async function getSellerItemCategories(
  sellerItemId: number,
  request?: Request
): Promise<ApiResponse<SellerItemCategories[]>> {
  const response = await apiRequest<SellerItemCategories[]>(
    `${API_BASE_URL}/bc/mnetbusiness/seller_itemCategories?sellerItemId=${sellerItemId}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch trip summary");
  }
}

export async function getSellerList(
  request?: Request
): Promise<ApiResponse<Seller[]>> {
  const response = await apiRequest<Seller[]>(
    `${API_BASE_URL}/bc/mnetadmin/sellers`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch getSellerList");
  }
}
export async function getSelectedSeller(
  sellerId: number,
  request?: Request
): Promise<ApiResponse<SelectedSeller>> {
  const response = await apiRequest<SelectedSeller>(
    `${API_BASE_URL}/bc/mnetadmin/config/seller/${sellerId}/details`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch getSelectedSeller");
  }
}

export async function getSelectedNetWorkAgent(
  netWorkId: number,
  request?: Request
): Promise<ApiResponse<networkAgents[]>> {
  const response = await apiRequest<networkAgents[]>(
    `${API_BASE_URL}/bc/mnetadmin/network/${netWorkId}/agents`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch getSellerList");
  }
}
export async function getSelectedMasterItemCategories(
  iCId: number,
  request?: Request
): Promise<ApiResponse<MasterItemCategories>> {
  const response = await apiRequest<MasterItemCategories>(
    `${API_BASE_URL}/bc/mnetadmin/itemcategories/${iCId}/details`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch getSelectedMasterItemCategories");
  }
}

export async function getSellerUser(
  sellerId: number,
  request?: Request
): Promise<ApiResponse<SellerUser[]>> {
  const response = await apiRequest<SellerUser[]>(
    `${API_BASE_URL}/bc/seller/${sellerId}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch getSellerUser");
  }
}
export async function getUserRoles(
  request?: Request
): Promise<ApiResponse<string[]>> {
  const response = await apiRequest<string[]>(
    `${API_BASE_URL}/bc/userroles`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch getSellerUser");
  }
}
export async function getSelectedMasterCatItems(
  iCId: number,
  request: Request,
  pageNo: number,
  pageSize: number,
  matchBy: string
): Promise<ApiResponse<ItemsList>> {
  const url =
    matchBy != ""
      ? `${API_BASE_URL}/bc/mnetadmin/category/${iCId}/items?pageNo=${pageNo}&size=${pageSize}&matchBy=${matchBy}`
      : `${API_BASE_URL}/bc/mnetadmin/category/${iCId}/items?pageNo=${pageNo}&size=${pageSize}`;
  const response = await apiRequest<ItemsList>(
    url,
    "GET",
    undefined,
    {},
    true,
    request
  );
  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch getSelectedMasterCatItems");
  }
}
export async function getSelectedChildCategory(
  iCId: number,
  request: Request,
  pageNo: number,
  pageSize: number,
  matchBy: string
): Promise<ApiResponse<MasterItemCategories>> {
  const url =
    matchBy != ""
      ? `${API_BASE_URL}/bc/mnetadmin/category/${iCId}/child_categories?pageNo=${pageNo}&size=${pageSize}&matchBy=${matchBy}`
      : `${API_BASE_URL}/bc/mnetadmin/category/${iCId}/child_categories?pageNo=${pageNo}&size=${pageSize}`;
  const response = await apiRequest<MasterItemCategories>(
    url,
    "GET",
    undefined,
    {},
    true,
    request
  );
  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch getSelectedChildCategory");
  }
}
export async function getSelectedSellerAreas(
  sellerId: number,
  request?: Request
): Promise<ApiResponse<SellerAreas>> {
  const response = await apiRequest<SellerAreas>(
    `${API_BASE_URL}/bc/seller/${sellerId}/sareas`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch getSelectedSellerAreas");
  }
}

export async function updateUser(
  userId: number,
  request: Request
): Promise<ApiResponse<smSellerArea>> {
  const response = await apiRequest<smSellerArea>(
    `${API_BASE_URL}/bc/mnetadmin/user/${userId}/toggle-status`,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to create ContractPrice");
  }
}
export async function updateArea(
  areaId: number,
  request: Request
): Promise<ApiResponse<smSellerArea>> {
  const response = await apiRequest<smSellerArea>(
    `${API_BASE_URL}/bc/seller/sellerarea/${areaId}/toggle-status`,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to create ContractPrice");
  }
}
export async function updateAttributes(
  type: string,
  id: number,
  attr: any,
  value: any,
  request?: Request
): Promise<ApiResponse<SellerConfig>> {
  const response = await apiRequest<SellerConfig>(
    `${API_BASE_URL}/bc/mnetadmin/config/${type}/${id}/attr/${attr}/${value}`,
    "POST",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to update fields");
  }
}
export async function updateSellerAttributes(
  type: string,
  id: number,
  attr: any,
  value: any,
  request?: Request
): Promise<ApiResponse<BusinessData>> {
  const response = await apiRequest<BusinessData>(
    `${API_BASE_URL}/bc/mnetadmin/seller/config/${type}/${id}/attr/${attr}/${value}`,
    "POST",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to update fields");
  }
}

export async function updateSellerStatus(
  sellerId: number,
  request?: Request
): Promise<ApiResponse<CreateSellerRequest>> {
  const response = await apiRequest<CreateSellerRequest>(
    `${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/toggle-status`,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to create Seller");
  }
}
export async function createSellerArea(
  sellerId: number | null,
  areaId: number,
  request?: Request
): Promise<ApiResponse<smSellerArea>> {
  const response = await apiRequest<smSellerArea>(
    `${API_BASE_URL}/platform/seller_area`,
    "POST",
    { sellerId, areaId },
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to create Seller");
  }
}
export async function createSeller(
  requestBody: any,
  request?: Request
): Promise<ApiResponse<CreateSellerRequest>> {

  try {
    const response = await apiRequest<CreateSellerRequest>(
      `${API_BASE_URL}/bc/mnetadmin/seller`,
      "POST",
      requestBody,
      {},
      true,
      request
    );

    if (response.statusCode !== 200) {
      throw new Error("No response received from API");
    }
    return response;

  }
  catch (error) {
    console.error("Error while creating seller:", error);
    throw error;
  }
}

interface SellerRequest {
  sucess: boolean
}
export async function createSellerPushMenu(
  sellerId: number,
  request?: Request
): Promise<ApiResponse<SellerRequest>> {
  try {
    const response = await apiRequest<SellerRequest>(
      `${API_BASE_URL}/bc/catalog/seller/${sellerId}/createcatalog`,
      "POST",
      undefined,
      {},
      true,
      request
    );

    if (response.statusCode !== 200) {
      throw new Error("No response received from API");
    }
    return response;

  }
  catch (error) {
    console.error("Error while creating seller:", error);
    throw error;
  }
}

export async function createUser(
  sellerId: number,
  requestBody: CreateUserRequest,
  request?: Request
): Promise<ApiResponse<CreateUserRequest>> {
  const response = await apiRequest<CreateUserRequest>(
    `${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/createuser`,
    "POST",
    requestBody,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to create Seller");
  }
}
export async function editUser(
  userId: number,
  requestBody: CreateUserRequest,
  request?: Request
): Promise<ApiResponse<CreateUserRequest>> {
  const response = await apiRequest<CreateUserRequest>(
    `${API_BASE_URL}/bc/mnetadmin/user/${userId}`,
    "PUT",
    requestBody,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to create Seller");
  }
}
export async function getUploadedUrl(
  sellerId: number,
  filetype: string,
  group: string,
  request?: Request
): Promise<{ presignedUrl: string }> {
  try {
    const response = await apiRequest(
      `${API_BASE_URL}/bc/presignedurl/seller/${sellerId}/filename/${filetype}/group/${group}`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response && response?.data?.presignedUrl) {
      return response?.data?.presignedUrl;
    } else {
      throw new Error("Unexpected response format from API.");
    }
  } catch (error) {
    console.error("Error fetching presigned URL:", error); // Log any errors for debugging
    throw new Error("Failed to fetch presigned URL");
  }
}

export async function updateMasterItem(
  itemId: number,
  requestBody: ItemsList,
  request?: Request
): Promise<ApiResponse<ItemsList>> {
  const response = await apiRequest<ItemsList>(
    `${API_BASE_URL}/bc/mnetadmin/masteritem/${itemId}`,
    "PUT",
    requestBody,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to update master item");
  }
}

export async function createAgent(
  netWorkId: number,
  reQuestBody: Agent,
  request?: Request
): Promise<ApiResponse<Agent>> {
  const response = await apiRequest<Agent>(
    `${API_BASE_URL}/bc/mnetadmin/network/${netWorkId}/agent`,
    "POST",
    reQuestBody,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch trip summary");
  }
}
