// import categoryList from "../resources/dmartCategory.js";

// // src/index.ts

// import axios from 'axios';
// import ExcelJS from 'exceljs';
// import AWS from 'aws-sdk';
// import { Category, ProductListResponse, ProductListItem, ProductDetails } from './interfaces';
// import * as dotenv from 'dotenv';
// import path from 'path';
// import Bottleneck from 'bottleneck';
// import winston from 'winston';

// // Load environment variables from .env file
// dotenv.config();

// // AWS Configuration
// AWS.config.update({
//   accessKeyId: process.env.AWS_ACCESS_KEY_ID,
//   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
//   region: process.env.AWS_REGION,
// });

// const dynamoDB = new AWS.DynamoDB.DocumentClient();
// const DYNAMODB_TABLE = process.env.DYNAMODB_TABLE_NAME || 'Products';

// // Excel Setup
// const excelFilePath = path.join(__dirname, '..', 'products.xlsx');
// const workbook = new ExcelJS.Workbook();
// let worksheet: ExcelJS.Worksheet;

// // Initialize Logger
// const logger = winston.createLogger({
//   level: 'info',
//   format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
//   transports: [
//     new winston.transports.File({ filename: 'dmart-scraper.log' }),
//     new winston.transports.Console(),
//   ],
// });

// // Initialize Excel Sheet
// async function initializeExcel() {
//   try {
//     await workbook.xlsx.readFile(excelFilePath);
//     worksheet = workbook.getWorksheet('Products');
//     if (!worksheet) {
//       worksheet = workbook.addWorksheet('Products');
//       setupWorksheetColumns();
//     }
//   } catch (error) {
//     // If file does not exist, create a new one
//     worksheet = workbook.addWorksheet('Products');
//     setupWorksheetColumns();
//     logger.info('Created new Excel sheet.');
//   }
// }

// // Define Excel Columns
// function setupWorksheetColumns() {
//   worksheet.columns = [
//     { header: 'Category', key: 'category', width: 20 },
//     { header: 'Product Name', key: 'name', width: 30 },
//     { header: 'Manufacturer', key: 'manufacturer', width: 20 },
//     { header: 'SEO Token NTK', key: 'seo_token_ntk', width: 50 },
//     { header: 'Number of SKUs', key: 'numberOfskus', width: 15 },
//     { header: 'Price', key: 'price', width: 15 },
//     { header: 'Description', key: 'description', width: 50 },
//     // Add more columns based on product details
//   ];

//   logger.info('Initialized Excel sheet columns.');
// }

// // Save Excel Workbook
// async function saveExcel() {
//   await workbook.xlsx.writeFile(excelFilePath);
//   logger.info('Saved Excel file.');
// }

// // DynamoDB Insertion
// async function saveToDynamoDB(product: any) {
//   const params = {
//     TableName: DYNAMODB_TABLE,
//     Item: product,
//     ConditionExpression: 'attribute_not_exists(seo_token_ntk)', // Prevent duplicate entries
//   };

//   try {
//     await dynamoDB.put(params).promise();
//     logger.info(`Saved to DynamoDB: ${product.seo_token_ntk}`);
//   } catch (error: any) {
//     if (error.code === 'ConditionalCheckFailedException') {
//       logger.warn(`Duplicate entry skipped: ${product.seo_token_ntk}`);
//     } else {
//       logger.error(`DynamoDB Error for ${product.seo_token_ntk}: ${error}`);
//       throw error;
//     }
//   }
// }

// // Retry Helper with Exponential Backoff
// async function retry<T>(fn: () => Promise<T>, retries = 5, delayMs = 1000): Promise<T> {
//   try {
//     return await fn();
//   } catch (error) {
//     if (retries > 0) {
//       logger.warn(`Retrying... Attempts left: ${retries}`);
//       await delay(delayMs);
//       return retry(fn, retries - 1, delayMs * 2);
//     } else {
//       throw error;
//     }
//   }
// }

// // Delay Helper
// function delay(ms: number) {
//   return new Promise((resolve) => setTimeout(resolve, ms));
// }

// // Bottleneck for Rate Limiting
// const limiter = new Bottleneck({
//   minTime: 300, // Minimum time between requests in ms
// });

// // Fetch Product List for a Category with Retry
// async function fetchProductList(seoToken: string): Promise<ProductListItem[]> {
//   const products: ProductListItem[] = [];
//   let page = 1;
//   const size = 40;
//   let totalRecords = 0;

//   while (true) {
//     const url = `https://digital.dmart.in/api/v2/plp/${seoToken}?page=${page}&size=${size}&channel=web`;

//     try {
//       const response = await retry(() =>
//         axios.get<ProductListResponse>(url, {
//           headers: {
//             accept: 'application/json, text/plain, */*',
//             'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
//             d_info: 'w-20241125_173859',
//             origin: 'https://www.dmart.in',
//             priority: 'u=1, i',
//             'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
//             'sec-ch-ua-mobile': '?0',
//             'sec-ch-ua-platform': '"macOS"',
//             'sec-fetch-dest': 'empty',
//             'sec-fetch-mode': 'cors',
//             'sec-fetch-site': 'same-site',
//             storeid: '10713',
//             'user-agent':
//               'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
//             'x-request-id': 'OGI2YzQ1Y2UtMDBiYi00MzdlLWI0MDYtMGYzMDhlYTU5MDJmfHxTLTIwMjQxMTI1XzE3Mzg1OXx8LTEwMDI=',
//           },
//         })
//       );

//       const data = response.data;
//       totalRecords = data.totalRecords;
//       products.push(...data.products);

//       logger.info(`Fetched page ${page} for seoToken ${seoToken}. Total Records: ${totalRecords}`);

//       if (products.length >= totalRecords) {
//         break;
//       }

//       page += 1;
//     } catch (error) {
//       logger.error(`Error fetching product list for seoToken ${seoToken} on page ${page}: ${error}`);
//       // Decide to continue or halt
//       throw error;
//     }
//   }

//   return products;
// }

// // Fetch Product Details with Retry
// async function fetchProductDetails(seoTokenNtk: string): Promise<ProductDetails | null> {
//   const url = `https://www.dmart.in/_next/data/06eXfO2lNULF2bwVwj_VJ/product/${seoTokenNtk}.json?token=${seoTokenNtk}`;

//   try {
//     const response = await retry(() =>
//       axios.get<ProductDetails>(url, {
//         headers: {
//           Accept: '*/*',
//           'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
//           Connection: 'keep-alive',
//           Cookie:
//             'd_info=%22w-20241125_173859%22; _ga=GA1.1.1693067929.1735388978; reqId=%22OGI2YzQ1Y2UtMDBiYi00MzdlLWI0MDYtMGYzMDhlYTU5MDJmfHxTLTIwMjQxMTI1XzE3Mzg1OXx8LTEwMDI=%22; recentUserPincodeSearch=%5B%7B%22uniqueId%22%3A%22%22%2C%22pincode%22%3A%22562125%22%2C%22apiMode%22%3A%22%22%2C%22area%22%3A%22Sarjapur%22%2C%22primaryText%22%3A%22562125%22%2C%22secondaryText%22%3A%22Sarjapur%2C%20Bangalore%22%7D%5D; guest=%7B%22preferredPIN%22%3A%22562125%22%2C%22preferredStore%22%3A%2210713%22%2C%22preferredCity%22%3A%22Bangalore%22%2C%22preferredArea%22%3A%22Sarjapur%22%2C%22isLoggedIn%22%3Afalse%2C%22isPinSet%22%3A%22true%22%7D; _ga_3TR7GSPBGF=GS1.1.1735399211.3.1.1735399948.0.0.0',
//           'Sec-Fetch-Dest': 'empty',
//           'Sec-Fetch-Mode': 'cors',
//           'Sec-Fetch-Site': 'same-origin',
//           'User-Agent':
//             'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
//           'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
//           'sec-ch-ua-mobile': '?0',
//           'sec-ch-ua-platform': '"macOS"',
//           'x-nextjs-data': '1',
//         },
//       })
//     );

//     return response.data;
//   } catch (error) {
//     logger.error(`Error fetching product details for seo_token_ntk ${seoTokenNtk}: ${error}`);
//     // Decide to continue or halt
//     throw error;
//   }
// }

// // Process a Single Product
// async function processProduct(categoryName: string, product: ProductListItem) {
//   const { seo_token_ntk, name, manufacturer, numberOfskus } = product;

//   // Check if the product is already in DynamoDB to prevent duplication
//   const getParams = {
//     TableName: DYNAMODB_TABLE,
//     Key: {
//       seo_token_ntk: seo_token_ntk,
//     },
//   };

//   try {
//     const data = await dynamoDB.get(getParams).promise();
//     if (data.Item) {
//       logger.info(`Product already exists in DynamoDB: ${seo_token_ntk}`);
//       return; // Skip processing
//     }
//   } catch (error) {
//     logger.error(`Error checking DynamoDB for ${seo_token_ntk}: ${error}`);
//     throw error;
//   }

//   // Fetch Product Details
//   const details = await fetchProductDetails(seo_token_ntk);
//   if (!details) {
//     logger.warn(`No details fetched for ${seo_token_ntk}`);
//     return;
//   }

//   // Extract specific fields from details as needed
//   const price = details.price || 'N/A';
//   const description = details.description || 'N/A';
//   // Add more fields as required

//   // Prepare data for Excel and DynamoDB
//   const productData = {
//     category: categoryName,
//     name: name,
//     manufacturer: manufacturer,
//     seo_token_ntk: seo_token_ntk,
//     numberOfskus: numberOfskus,
//     price: price,
//     description: description,
//     // Add more fields as needed
//   };

//   // Write to Excel
//   worksheet.addRow(productData);

//   // Save to Excel after adding the row
//   await saveExcel();

//   // Save to DynamoDB
//   await saveToDynamoDB(productData);

//   logger.info(`Processed product: ${name}`);
// }

// // Main Execution Function
// async function main() {
//   await initializeExcel();

//   for (const category of catArray) {
//     logger.info(`Processing category: ${category.name}`);

//     let productList: ProductListItem[] = [];

//     try {
//       productList = await limiter.schedule(() => fetchProductList(category.seoToken));
//     } catch (error) {
//       logger.error(`Failed to fetch product list for category ${category.name}: ${error}`);
//       continue; // Move to the next category
//     }

//     logger.info(`Total products fetched for ${category.name}: ${productList.length}`);

//     for (const product of productList) {
//       try {
//         await limiter.schedule(() => processProduct(category.name, product));
//         // Optional: Add additional delays if necessary
//       } catch (error) {
//         logger.error(`Error processing product ${product.seo_token_ntk}: ${error}`);
//         // Decide whether to continue or halt
//         // For this script, we'll continue with the next product
//       }
//     }
//   }

//   // Final save
//   await saveExcel();
//   logger.info('All categories processed successfully.');
// }

// // Execute the script
// main()
//   .then(() => {
//     logger.info('Script execution completed.');
//   })
//   .catch((error) => {
//     logger.error('Script execution failed:', error);
//   });
