import { Card } from "./card"
import { Form } from "@remix-run/react"
import { Input } from "./input"
import { Button } from "./button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select"
import { useEffect, useState } from "react"
import { Switch } from "./switch"
import { MasterItemCategories } from "~/types/api/businessConsoleService/MasterItemCategory"
interface ItemCategoryProps {
      data: MasterItemCategories
      handleUpdate: (handleUpdate: string, val: any, id: number, type: string) => void,
      loading: boolean,
      onHandleUpload: (attributetype: string, file: File, fileType: string, groupType: string) => void,

}
const ItemCategory: React.FC<ItemCategoryProps> = ({ data, handleUpdate, loading, onHandleUpload }) => {
      const [selectedLevel, setSelectedLevel] = useState(data?.level.toString())
      const levels = [
            { id: "1", name: "1" },
            { id: "2", name: "2" },
            { id: "3", name: "3" },
      ];
      const [imagePreview, setImagePreview] = useState<string | null>(data?.picture);
      const [imagePreviewx, setImagePreviewx] = useState<string | null>(data?.picturex);
      const [imagePreviewxx, setImagePreviewxx] = useState<string | null>(data?.picturexx);
      const [fileName, setFileName] = useState("")
      const [categoryName, setCategoryName] = useState(data?.name)
      const [update, setUpdate] = useState(false)


      useEffect(() => {
            setUpdate(loading == true ? true : false)
      }, [loading])

      const handleImageUpload = (type: string, event: React.ChangeEvent<HTMLInputElement>) => {
            const file = event.target.files?.[0];
            if (file) {
                  const sanitizedFileName = file.name.replace(/\s+/g, "_");
                  setFileName(sanitizedFileName);
                  const reader = new FileReader();
                  reader.onload = () => {
                        if (type === "picture") {
                              setImagePreview(reader.result as string);
                              onHandleUpload("uploadS3", file, sanitizedFileName, "itemCategory")
                        } else if (type === "picturex") {
                              setImagePreviewx(reader.result as string);
                              onHandleUpload("uploadS3", file, sanitizedFileName, "itemCategory")

                        } else if (type === "picturexx") {
                              setImagePreviewxx(reader.result as string);
                              onHandleUpload("uploadS3", file, sanitizedFileName, "itemCategory")

                        } else {
                              console.error("Unknown image type:", type);
                        }
                  };
                  reader.onerror = (error) => {
                        console.error("FileReader error:", error);
                  };
                  reader.readAsDataURL(file);

            } else {
                  console.error("No file selected");
            }
      };
      const onHandleUpdateName = () => {
            handleUpdate("name", categoryName, data?.id, "itemCategory")

      }
      const onHandleUpdateLevel = (value: string) => {
            setSelectedLevel(value)
            handleUpdate("level", value, data?.id, "itemCategory")
      }

      return (
            <Card className="w-full md:w-1/2 max-w-xl p-6 border rounded-lg shadow-md space-y-6 bg-white">
                  <div key={data.id} className="space-y-6">
                        <Switch onClick={() => setUpdate(!update)} />
                        <div className="my-4">
                              <div className="flex items-center space-x-4">
                                    <p className="text-lg font-semibold text-gray-700">ID:</p>
                                    <p className="text-lg text-gray-900">{data.id}</p>
                              </div>
                              <div className="flex items-center space-x-4 mt-6">
                                    <p className="text-lg font-semibold text-gray-700">Name:</p>
                                    <Input
                                          placeholder={"Enter Name"}
                                          value={categoryName}
                                          onChange={(e) => setCategoryName(e.target.value)}

                                          disabled={!update}
                                    />

                                    {update && <Button onClick={onHandleUpdateName} disabled={loading}>{loading ? "updating" : "update"}</Button>}
                              </div>
                              <div className="flex items-center space-x-4  mt-6">
                                    <p className="text-lg font-semibold text-gray-700">Image     :</p>
                                    <div className="flex flex-col items-center">
                                          <label
                                                htmlFor="image-upload1"
                                                className="flex items-center justify-center w-20 h-20  border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 overflow-hidden"
                                          >
                                                {imagePreview ? (
                                                      <img
                                                            src={imagePreview}
                                                            alt="Uploaded Preview"
                                                            className="object-cover w-full h-full" />
                                                ) : (
                                                      <span className="text-gray-500"> upload an image</span>
                                                )}
                                          </label>
                                          <input
                                                type="file"
                                                id="image-upload1"
                                                accept="image/*"
                                                className="hidden "
                                                onChange={(e) => handleImageUpload("picture", e as React.ChangeEvent<HTMLInputElement>)}
                                                disabled={!update}


                                          />
                                    </div>
                                    {update && <Button>Upload</Button>}
                              </div>
                              <div className="flex items-center space-x-4  mt-6">
                                    <p className="text-lg font-semibold text-gray-700">Picturex :</p>
                                    <div className="flex flex-col items-center">
                                          <label
                                                htmlFor="image-upload2"
                                                className="flex items-center justify-center w-20 h-20  border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 overflow-hidden"
                                          >
                                                {imagePreviewx ? (
                                                      <img
                                                            src={imagePreviewx}
                                                            alt="Uploaded Previewx"
                                                            className="object-cover w-full h-full" />
                                                ) : (
                                                      <span className="text-gray-500"> upload an image</span>
                                                )}
                                          </label>
                                          <input
                                                type="file"
                                                id="image-upload2"
                                                accept="image/*"
                                                className="hidden "
                                                onChange={(e) => handleImageUpload("picturex", e as React.ChangeEvent<HTMLInputElement>)}
                                                disabled={!update}

                                          />
                                    </div>
                                    {update && <Button>Upload</Button>}
                              </div>
                              <div className="flex items-center space-x-4  mt-6">
                                    <p className="text-lg font-semibold text-gray-700">Picturexx:</p>
                                    <div className="flex flex-col items-center">
                                          <label
                                                htmlFor="image-upload3"
                                                className="flex items-center justify-center w-20 h-20  border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 overflow-hidden"
                                          >
                                                {imagePreviewxx ? (
                                                      <img
                                                            src={imagePreviewxx}
                                                            alt="Uploaded Previewxx"
                                                            className="object-cover w-full h-full" />
                                                ) : (
                                                      <span className="text-gray-500"> upload an image</span>
                                                )}
                                          </label>
                                          <input
                                                type="file"
                                                id="image-upload3"
                                                accept="image/*"
                                                className="hidden "
                                                onChange={(e) => handleImageUpload("picturexx", e as React.ChangeEvent<HTMLInputElement>)}
                                                disabled={!update}
                                          />
                                    </div>
                                    {update && <Button>Upload</Button>}

                              </div>
                              <div className="flex items-center space-x-4  mt-6">
                                    <p className="text-lg font-semibold text-gray-700">Level:</p>
                                    <Select
                                          value={selectedLevel.toString()}
                                          onValueChange={onHandleUpdateLevel}
                                          disabled={!update}
                                    >
                                          <SelectTrigger>
                                                <SelectValue placeholder="Select a Levels" />
                                          </SelectTrigger>
                                          <SelectContent>
                                                {levels.map((level) => (
                                                      <SelectItem key={level.id} value={level.id}>
                                                            {level.name}
                                                      </SelectItem>
                                                ))}
                                          </SelectContent>
                                    </Select>
                              </div>
                        </div>
                  </div>
            </Card>
      )

}

export default ItemCategory;