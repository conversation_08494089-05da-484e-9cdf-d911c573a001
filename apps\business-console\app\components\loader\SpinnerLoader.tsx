import React, { useEffect, useState } from "react";

interface SpinnerLoaderProps {
  loading: boolean;
  size?: number;
  globalSpinner?: boolean;
}

const SpinnerLoader: React.FC<SpinnerLoaderProps> = ({ loading, size, globalSpinner = false }) => {
  const [active, setActive] = useState<boolean>(loading);

  useEffect(() => {
    setActive(loading);
  }, [loading]);

  return (
    active && (
      globalSpinner ? (
        <div
          role="progressbar"
          aria-valuetext={active ? "Loading" : undefined}
          aria-hidden={!active}
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 pointer-events-none z-50 p-4"
        >
          <div className="p-6 text-center">
            <div className="w-20 h-20 border-4 border-transparent border-t-blue-400 rounded-full animate-spin flex items-center justify-center">
              <div className="w-16 h-16 border-4 border-transparent border-t-red-400 rounded-full animate-spin"></div>
            </div>
          </div>
        </div>
      ) : (
        <div
          role="progressbar"
          aria-valuetext={active ? "Loading" : undefined}
          aria-hidden={!active}
          className={`fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 pointer-events-none z-50 p-4`}
        >
          <div className="p-6 text-center">
            <div className={`${size ? `w-${size} h-${size}` : "w-16 h-16"}  border-4 border-gray-200 border-t-4 border-t-primary rounded-full animate-spin mx-auto mb-4`}></div>
          </div>
        </div>
      )
    )
  );
};

export default SpinnerLoader;
