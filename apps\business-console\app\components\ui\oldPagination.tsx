import React from "react";
import { Form } from "@remix-run/react";
interface PaginationProps {
  currentPage: number; // Current page index (starts from 0 internally)
  hasMoreData: boolean; // Whether there's more data beyond the current page
  tabValue : string;
  sortBy : string;
  searchBy : string;
  sortByOrder : string;
}
const Pagination: React.FC<PaginationProps> = ({ currentPage, hasMoreData,tabValue,sortBy,searchBy,sortByOrder }) => {
  const displayPage = currentPage + 1; // Adjust for user-friendly display
  const maxVisiblePages = 5; // Maximum number of pages to display around the current page
  const lastPage = hasMoreData ? displayPage + 2 : displayPage;
  // Generate dynamic page numbers
  const getPageNumbers = (): (number | string)[] => {
    const pageNumbers: (number | string)[] = [];
    // Show the first page and ellipsis if necessary
    if (currentPage > 2) {
      pageNumbers.push(1);
      if (currentPage > 3) pageNumbers.push("...");
    }
    // Add pages around the current page
    for (
      let i = Math.max(1, displayPage - 2);
      i <=  Math.min(displayPage + 2, currentPage + maxVisiblePages,lastPage);
      i++
    ) {
      pageNumbers.push(i);
    }
    // Show ellipsis and the next button if there's more data
    if (hasMoreData) {
      if (currentPage + 3 < displayPage + maxVisiblePages) pageNumbers.push("...");
    }
    return pageNumbers;
  };
  return (
    <div className="flex justify-center items-center gap-2 mt-4">
      {/* Previous Button */}
      {currentPage > 0 && (
        <Form method="get" className="inline">
          <input type="hidden" name="page" value={currentPage - 1} />
          <input type="hidden" name="tabValue" value={tabValue} />
          <input type="hidden" name="sortBy" value={sortBy} />
          <input type="hidden" name="search" value={searchBy} />
          <input type="hidden" name="search" value={sortByOrder} />
          <button
            type="submit"
            className="px-3 py-1  rounded-full border border-grey-200 hover:bg-primary-50"
          >
            &lt;
          </button>
        </Form>
      )}
      {/* Page Numbers */}
      {getPageNumbers().map((page, index) =>
        page === "..." ? (
          <span key={index} className="px-3 py-1 text-gray-500">
            ...
          </span>
        ) : (
          <Form method="get" key={index} className="inline">
            <input type="hidden" name="page" value={(page as number) - 1} />
            <input type="hidden" name="tabValue" value={tabValue} />
            <input type="hidden" name="sortBy" value={sortBy} />
            <input type="hidden" name="search" value={searchBy} />
            <input type="hidden" name="search" value={sortByOrder} />
            <button
              type="submit"
              className={`px-3 py-1 rounded-full ${
                page === displayPage
                  ? "bg-primary text-white"
                  : "border border-grey-200 hover:bg-primary-50"
              }`}
            >
              {page}
            </button>
          </Form>
        )
      )}
      {/* Next Button */}
      {hasMoreData && (
        <Form method="get" className="inline">
          <input type="hidden" name="page" value={currentPage + 1} />
          <input type="hidden" name="tabValue" value={tabValue} />
            <input type="hidden" name="sortBy" value={sortBy} />
            <input type="hidden" name="search" value={searchBy} />
            <input type="hidden" name="search" value={sortByOrder} />
          <button
            type="submit"
            className="px-3 py-1 rounded-full border border-grey-200 hover:bg-primary-50"
          >
            &gt;
          </button>
        </Form>
      )}
    </div>
  );
};
export default Pagination;