import { useLoaderData } from "@remix-run/react";
import { useState } from "react"
import { Input } from "~/components/ui/input"
import {
      Table,
      TableBody,
      TableCell,
      TableHead,
      TableHeader,
      TableRow,
} from "~/components/ui/table";
import { getPermissions } from "~/services/permission";
import { Permissions } from "~/types/api/businessConsoleService/Permissions";
import { withAuth, withResponse } from "~/utils/auth-utils";


export const loader = withAuth(async ({ request }) => {
      try {

            const response = await getPermissions(request);
            return withResponse({ data: response.data }, response.headers);
      } catch (error) {
            console.error('Trip summary error:', error);
            throw new Error(`Error fetching trip details: ${error}`);
      }
});



export default function Permision() {
      const loaderData = useLoaderData<{ data: Permissions[] }>()
      const [searchTerm, setSearchTerm] = useState('')

      function handleSearch(x: Permissions) {
            return x.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  x.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  x.id.toString().toLowerCase().includes(searchTerm.toLowerCase())
      }
      return (
            <div className="container mx-auto p-6">
                  <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold">Permission </h1>
                  </div>
                  <div className="flex justify-between mb-4">
                        <Input
                              placeholder="Search By Id, Permission ,Type"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"
                        />
                  </div>
                  <div className="rounded-md border">
                        <Table>
                              <TableHeader>
                                    <TableRow>
                                          <TableHead>ID</TableHead>
                                          <TableHead>PerMission</TableHead>
                                          <TableHead>Resource</TableHead>
                                    </TableRow>
                              </TableHeader>
                              <TableBody>
                                    {loaderData?.data?.length > 0 ?

                                          loaderData?.data.filter((x) => handleSearch(x))?.map((x) => {
                                                return (
                                                      <TableRow key={x.id}>
                                                            <TableCell>{x?.id}</TableCell>
                                                            <TableCell>{x?.name}</TableCell>
                                                            <TableCell>{x?.resource}</TableCell>
                                                      </TableRow>
                                                )
                                          })


                                          :
                                          <TableRow>
                                                <TableCell
                                                      colSpan={9}
                                                      className="h-24 text-center"
                                                >
                                                      No results.
                                                </TableCell>
                                          </TableRow>}


                              </TableBody>

                        </Table>
                  </div>

            </div>
      )
}