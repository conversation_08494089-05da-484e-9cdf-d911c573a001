# WebhookLog DynamoDB Query Optimization Guide

## Overview
This guide provides strategies for efficiently querying millions of webhook log records in DynamoDB while managing costs and performance.

## Current WebhookLog Table Design Analysis

### ✅ Good Practices Already Implemented
- Multiple Global Secondary Indexes (GSIs) for different access patterns
- Composite keys with business-specific partitioning
- Proper data types and attribute definitions
- WhatsApp status tracking and error handling

### ⚠️ Areas for Improvement
- Missing time-based partitioning strategy
- Potential hot partition issues with large datasets
- No date-based query optimization

## Optimization Strategies Applied

### 1. **Time-Based Partitioning**

#### Why It's Important
- **Hot Partition Prevention**: Distributes webhook data across multiple partitions
- **Cost Reduction**: Smaller, focused queries instead of large scans
- **Performance**: Faster queries on specific time ranges

#### Implementation
```typescript
// Added these fields to WebhookLog interface
datePartition?: string;    // Format: YYYY-MM-DD
monthPartition?: string;   // Format: YYYY-MM

// When creating webhook logs
const timestamp = Date.now();
const date = new Date(timestamp);
const datePartition = date.toISOString().split('T')[0];  // YYYY-MM-DD
const monthPartition = date.toISOString().slice(0, 7);   // YYYY-MM
```

### 2. **New Optimized GSIs**

#### A. DatePartitionIndex
```typescript
{
    IndexName: 'DatePartitionIndex',
    KeySchema: [
        { AttributeName: 'datePartition', KeyType: 'HASH' },
        { AttributeName: 'timestamp', KeyType: 'RANGE' }
    ],
    Projection: { ProjectionType: 'ALL' }
}
```

#### B. MonthPartitionIndex
```typescript
{
    IndexName: 'MonthPartitionIndex',
    KeySchema: [
        { AttributeName: 'monthPartition', KeyType: 'HASH' },
        { AttributeName: 'timestamp', KeyType: 'RANGE' }
    ],
    Projection: { ProjectionType: 'ALL' }
}
```

#### C. BusinessDateIndex
```typescript
{
    IndexName: 'BusinessDateIndex',
    KeySchema: [
        { AttributeName: 'businessNumber', KeyType: 'HASH' },
        { AttributeName: 'datePartition', KeyType: 'RANGE' }
    ],
    Projection: { ProjectionType: 'ALL' }
}
```

### 3. **Optimized Query Methods**

#### A. Business + Date Range Queries (Most Efficient)
```typescript
// Use BusinessDateIndex for business-specific queries
const result = await webhookRepository.getLogsByBusinessAndDateRange(
    businessNumber, 
    '2024-01-01', 
    '2024-01-31'
);
```

#### B. Daily Webhook Analytics
```typescript
// Very efficient for daily reports
const result = await webhookRepository.getLogsByDate('2024-01-15');
```

#### C. Monthly Webhook Analytics
```typescript
// Efficient for monthly reports
const result = await webhookRepository.getLogsByMonth('2024-01');
```

#### D. Paginated Queries
```typescript
// Cost-effective pagination
const result = await webhookRepository.queryWithPagination(
    'BusinessDateIndex',
    'businessNumber = :businessNumber AND datePartition BETWEEN :startDate AND :endDate',
    {
        ':businessNumber': '1234567890',
        ':startDate': '2024-01-01',
        ':endDate': '2024-01-31'
    },
    undefined,
    100
);
```

### 4. **Cost Optimization Techniques**

#### A. Use Projection Expressions
```typescript
// Only fetch needed attributes for webhook analytics
const result = await dynamoDB.query({
    TableName: 'webhook_logs_prod',
    IndexName: 'BusinessDateIndex',
    KeyConditionExpression: 'businessNumber = :businessNumber',
    ProjectionExpression: 'webhookId, messageType, status, timestamp, errorMessage',
    ExpressionAttributeValues: {
        ':businessNumber': '1234567890'
    }
});
```

#### B. Batch Queries for Date Ranges
```typescript
// Query each date partition separately (more efficient than large range queries)
const result = await webhookRepository.batchQueryByDateRange(
    '2024-01-01',
    '2024-01-31',
    100 // limit per partition
);
```

#### C. Analytics Without Full Data Transfer
```typescript
// Get aggregated webhook analytics for a business
const analytics = await webhookRepository.getBusinessWebhookAnalytics(
    businessNumber,
    '2024-01-01',
    '2024-01-31'
);

// Returns:
// {
//     totalWebhooks: 1500,
//     statusBreakdown: { 'PROCESSED': 1400, 'FAILED': 100 },
//     messageTypeBreakdown: { 'TEXT': 800, 'INTERACTIVE': 700 },
//     dailyCounts: { '2024-01-01': 50, '2024-01-02': 45 },
//     errorRate: 6.67
// }
```

### 5. **Webhook-Specific Optimizations**

#### A. Error Rate Monitoring
```typescript
// Track webhook processing errors efficiently
const failedWebhooks = await webhookRepository.getLogsByStatus('FAILED', {
    startTimestamp: Date.now() - (24 * 60 * 60 * 1000), // Last 24 hours
    limit: 100
});
```

#### B. WhatsApp Status Tracking
```typescript
// Monitor WhatsApp message delivery status
const deliveredMessages = await webhookRepository.getLogsByWhatsAppStatus('DELIVERED');
const readMessages = await webhookRepository.getLogsByWhatsAppStatus('READ');
```

#### C. Message Type Analytics
```typescript
// Analyze webhook types for business insights
const textMessages = await webhookRepository.getLogsByMessageType('TEXT');
const interactiveMessages = await webhookRepository.getLogsByMessageType('INTERACTIVE');
```

## Recommended Query Patterns by Use Case

### 1. **Real-time Webhook Dashboard**
```typescript
// Use BusinessDateIndex for last 7 days
const recentWebhooks = await webhookRepository.getLogsByBusinessAndDateRange(
    businessNumber, 
    getDateString(-7), 
    getDateString(0)
);
```

### 2. **Daily Webhook Reports**
```typescript
// Use DatePartitionIndex
const dailyWebhooks = await webhookRepository.getLogsByDate('2024-01-15');
```

### 3. **Monthly Webhook Analytics**
```typescript
// Use MonthPartitionIndex
const monthlyWebhooks = await webhookRepository.getLogsByMonth('2024-01');
```

### 4. **Error Monitoring**
```typescript
// Use StatusIndex with date filtering
const failedWebhooks = await webhookRepository.getLogsByStatus('FAILED', {
    startTimestamp: Date.now() - (24 * 60 * 60 * 1000),
    limit: 50
});
```

### 5. **Customer Interaction History**
```typescript
// Use CustomerNumberIndex with pagination
const customerWebhooks = await webhookRepository.queryWithPagination(
    'CustomerNumberIndex',
    'customerNumber = :customerNumber',
    { ':customerNumber': phoneNumber },
    undefined,
    100
);
```

## Migration Strategy

### 1. **Run Migration Script**
```bash
# Check migration statistics first
npm run migrate:data:stats

# Run migration for last 30 days
npm run migrate:data
```

### 2. **Migration Process**
- Only processes records from the last 30 days to minimize costs
- Adds `datePartition` and `monthPartition` fields to existing records
- Processes both NotificationLog and WebhookLog tables
- Provides detailed progress logging

### 3. **Post-Migration Verification**
```typescript
// Verify migration was successful
const stats = await migrationUtility.getMigrationStats();
console.log('Records needing update:', stats.webhookLogs.needsUpdate);
// Should be 0 after successful migration
```

## Cost Estimation for Webhook Logs

### Read Capacity Units (RCU)
- **Strongly Consistent Read**: 4KB per RCU
- **Eventually Consistent Read**: 8KB per RCU

### Example Cost Calculation
```typescript
// Query with 1000 webhook items, average 3KB per item
const totalKB = 1000 * 3 = 3000KB
const rcuNeeded = Math.ceil(3000 / 4) = 750 RCU

// Cost: 750 RCU * $0.00025 per RCU = $0.1875 per query
```

## Best Practices for Webhook Logs

1. **Always use indexes** - Never scan the main table
2. **Implement pagination** - Limit result sets to 100-1000 items
3. **Use time-based queries** - Leverage date partitions for range queries
4. **Monitor error rates** - Track failed webhooks separately
5. **Archive old data** - Use TTL or move to cheaper storage after 90 days
6. **Cache frequently accessed data** - Use Redis for real-time dashboards
7. **Batch operations** - Use BatchGetItem/BatchWriteItem for bulk operations
8. **Projection expressions** - Only fetch needed attributes

## Performance Monitoring

### Key Metrics to Track
- **Webhook Processing Time**: Average time from received to processed
- **Error Rate**: Percentage of failed webhooks
- **Message Type Distribution**: Breakdown by text, interactive, etc.
- **Business Performance**: Per-business webhook success rates
- **Peak Hours**: Identify high-traffic periods

### CloudWatch Alerts
```typescript
// Set up alerts for:
// - Error rate > 10%
// - Processing time > 5000ms
// - Webhook volume spikes > 200% normal
```

## Integration with Notification System

### Webhook-Notification Sync
```typescript
// Sync webhook status updates to notification logs
const syncResult = await webhookNotificationSyncService.processStatusUpdates(
    webhookId, 
    statusUpdates
);
```

### Cross-Table Analytics
```typescript
// Combine webhook and notification data for comprehensive analytics
const businessAnalytics = {
    webhooks: await webhookRepository.getBusinessWebhookAnalytics(businessNumber, startDate, endDate),
    notifications: await notificationRepository.getBusinessAnalytics(businessId, startDate, endDate)
};
```

This optimization approach will significantly reduce your DynamoDB costs while improving webhook query performance for millions of records. The time-based partitioning strategy is especially effective for webhook logs since they are naturally time-ordered and frequently queried by date ranges. 