import { DownloadIcon, QrCodeIcon } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { Dialog, DialogContent, DialogTrigger } from "../ui/dialog";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { useEffect, useRef, useState } from "react";

export default function WhatsAppQRCode({ url, businessPhoneNumber, businessLogo }: { url: string, businessPhoneNumber: number, businessLogo: string }) {
  const qrRef = useRef<HTMLDivElement | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [format, setFormat] = useState<"png" | "pdf">("png");

  useEffect(() => {
    if (!isDialogOpen) return;

    import("qr-code-styling").then(({ default: QRCodeStyling }) => {
      const qrCode = new QRCodeStyling({
        width: 160,
        height: 160,
        data: url,
        type: "svg",
        image: "/whatsppIcon.svg",
        dotsOptions: {
          color: "#000",
          type: "extra-rounded",
        },
        cornersDotOptions: {
          color: "#000",
          type: "extra-rounded",
        },
        cornersSquareOptions: {
          color: "#000",
          type: "extra-rounded",
        },
        imageOptions: {
          crossOrigin: "anonymous",
          margin: 2,
        },
        backgroundOptions: {
          color: "#ffffff",
        },
        qrOptions: {
          errorCorrectionLevel: "H",
        },
      });

      if (qrRef.current) {
        qrRef.current.innerHTML = "";
        qrCode.append(qrRef.current);
      }
    });
  }, [isDialogOpen]);

  async function handleDownload() {
    const node = document.getElementById("qrcode-card");
    if (!node) return;
  
    const { toPng } = await import("html-to-image");
    const { PDFDocument, rgb } = await import("pdf-lib");
  
    try {
      const dataUrl = await toPng(node, {
        pixelRatio: 6,
      });
  
      if (format === "png") {
        const link = document.createElement("a");
        link.download = `qrcode${businessPhoneNumber}.png`;
        link.href = dataUrl;
        link.click();
      } else if (format === "pdf") {
        // Convert base64 image to Uint8Array
        const pdfDoc = await PDFDocument.create();
        const pngImageBytes = await fetch(dataUrl).then(res => res.arrayBuffer());
        const pngImage = await pdfDoc.embedPng(pngImageBytes);
  
        const imgDims = pngImage.scale(1); // scale factor 1 = original size
  
        const page = pdfDoc.addPage([imgDims.width, imgDims.height]);
        page.drawImage(pngImage, {
          x: 0,
          y: 0,
          width: imgDims.width,
          height: imgDims.height,
        });
  
        const pdfBytes = await pdfDoc.save();
  
        const blob = new Blob([pdfBytes], { type: "application/pdf" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `qrcode${businessPhoneNumber}.pdf`;
        link.click();
        URL.revokeObjectURL(url);
      }
    } catch (err) {
      console.error("Failed to export QR code", err);
    }
  }

  return (
    <Dialog onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button
          type="button"
          className="text-base font-semibold text-primary border border-primary bg-primary-50 rounded-md hover:bg-primary-100 hover:text-primary"
        >
          <QrCodeIcon /> Show QR Code
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-neutral-100 p-0 max-h-screen overflow-y-auto">
        <div id="qrcode-card" className="mx-auto w-[360px] h-[500px] min-w-[360px] min-h-[500px] p-[6%] bg-[linear-gradient(180deg,_#00A390_0%,_#DFF2EF_100%)]">
          <div className="w-full h-full pt-[15%]">
            <div className="relative w-full h-full flex flex-col justify-end bg-white rounded-xl shadow-[0.58px_5.78px_12.72px_5.2px_#0000000D]">
              <p className="text-center text-sm font-bold text-[#1F3251]">Scan the QR code to</p>
              <p className="text-center mb-2 text-sm font-bold text-[#1F3251]">Order online via Whatsapp</p>
              <div className="w-[56%] mb-4 mx-auto border-t-[2.5px] rounded-full border-[#2C774433]  "></div>
              <div className="mx-auto mb-4 w-[160px] h-[160px]" ref={qrRef}></div>
              <p className="mb-3 text-center text-xs font-bold text-[#1F3251]">{`+91 - ${businessPhoneNumber}`}</p>
              <div className="flex flex-row justify-between items-center gap-2 m-2">
                <div className="text-[7px] text-gray-500">
                  <p>Want mNET for your business?</p>
                  <p>Email: <span className="text-primary"><EMAIL></span></p>
                </div>
                <div>
                  <svg width="130" height="26" viewBox="0 0 120 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1.50212 13.3709C1.36722 13.3709 1.262 13.3331 1.18645 13.2576C1.1163 13.182 1.08123 13.0768 1.08123 12.9419V8.03684C1.08123 7.89655 1.119 7.79132 1.19455 7.72117C1.27009 7.64563 1.37532 7.60786 1.51022 7.60786H3.44471C4.05986 7.60786 4.53472 7.76164 4.86928 8.06922C5.20383 8.3714 5.37111 8.79769 5.37111 9.34809C5.37111 9.90389 5.20383 10.3356 4.86928 10.6431C4.53472 10.9453 4.05986 11.0964 3.44471 11.0964H1.92302V12.9419C1.92302 13.0768 1.88794 13.182 1.81779 13.2576C1.74764 13.3331 1.64242 13.3709 1.50212 13.3709ZM1.92302 10.4246H3.3233C3.72801 10.4246 4.03288 10.3356 4.23794 10.1575C4.44838 9.97404 4.55361 9.70423 4.55361 9.34809C4.55361 8.99735 4.44838 8.73294 4.23794 8.55487C4.03288 8.3714 3.72801 8.27967 3.3233 8.27967H1.92302V10.4246ZM7.71164 13.3871C7.31233 13.3871 6.96698 13.3034 6.67559 13.1361C6.3896 12.9689 6.16566 12.7314 6.00378 12.4239C5.8419 12.1163 5.76096 11.7547 5.76096 11.3392C5.76096 11.0209 5.80682 10.7376 5.89856 10.4894C5.99029 10.2411 6.1198 10.028 6.28707 9.84993C6.45975 9.66646 6.6648 9.52886 6.90223 9.43713C7.14505 9.34 7.41486 9.29143 7.71164 9.29143C8.10555 9.29143 8.44551 9.37507 8.7315 9.54235C9.02289 9.70963 9.24952 9.94706 9.4114 10.2546C9.57329 10.5568 9.65423 10.9183 9.65423 11.3392C9.65423 11.6522 9.60836 11.9355 9.51663 12.1891C9.42489 12.4427 9.29269 12.6586 9.12002 12.8367C8.95274 13.0147 8.74769 13.1523 8.50486 13.2495C8.26744 13.3412 8.00303 13.3871 7.71164 13.3871ZM7.71164 12.7557C7.93288 12.7557 8.12714 12.7018 8.29442 12.5938C8.46169 12.4859 8.5912 12.3267 8.68293 12.1163C8.78006 11.9058 8.82863 11.6468 8.82863 11.3392C8.82863 10.8752 8.7261 10.5244 8.52105 10.287C8.316 10.0496 8.0462 9.93087 7.71164 9.93087C7.485 9.93087 7.28805 9.98483 7.12077 10.0927C6.95349 10.1953 6.82129 10.3518 6.72416 10.5622C6.63242 10.7727 6.58656 11.0317 6.58656 11.3392C6.58656 11.7979 6.68908 12.1487 6.89413 12.3915C7.09918 12.6343 7.37169 12.7557 7.71164 12.7557ZM11.942 13.3709C11.8286 13.3709 11.7288 13.3439 11.6425 13.2899C11.5615 13.2306 11.4968 13.1388 11.4482 13.0147L10.2584 9.86611C10.2152 9.75819 10.2017 9.66376 10.2179 9.58282C10.2341 9.49648 10.2746 9.42903 10.3393 9.38047C10.4095 9.3319 10.4985 9.30762 10.6064 9.30762C10.7036 9.30762 10.7818 9.3319 10.8412 9.38047C10.9059 9.42364 10.9599 9.50997 11.003 9.63948L12.0634 12.6262H11.8691L12.9699 9.6071C13.0077 9.50458 13.0589 9.42903 13.1237 9.38047C13.1884 9.3319 13.2721 9.30762 13.3746 9.30762C13.4771 9.30762 13.5608 9.3319 13.6255 9.38047C13.6957 9.42903 13.7469 9.50458 13.7793 9.6071L14.8558 12.6262H14.6778L15.7543 9.62329C15.7975 9.49918 15.8514 9.41554 15.9162 9.37237C15.9863 9.3292 16.0646 9.30762 16.1509 9.30762C16.2588 9.30762 16.3398 9.3346 16.3937 9.38856C16.4531 9.44252 16.4855 9.51267 16.4908 9.59901C16.5016 9.67995 16.4882 9.76898 16.4504 9.86611L15.2605 13.0147C15.212 13.1334 15.1445 13.2225 15.0582 13.2818C14.9772 13.3412 14.8828 13.3709 14.7749 13.3709C14.6616 13.3709 14.5618 13.3412 14.4754 13.2818C14.3945 13.2225 14.3297 13.1334 14.2812 13.0147L13.148 9.94706H13.5527L12.4276 13.0147C12.3844 13.1334 12.3197 13.2225 12.2333 13.2818C12.1524 13.3412 12.0553 13.3709 11.942 13.3709ZM19.1288 13.3871C18.6971 13.3871 18.3248 13.3061 18.0118 13.1442C17.7043 12.977 17.4641 12.7422 17.2915 12.44C17.1242 12.1325 17.0406 11.7682 17.0406 11.3473C17.0406 10.9372 17.1242 10.5784 17.2915 10.2708C17.4587 9.96324 17.6881 9.72312 17.9795 9.55044C18.2709 9.37777 18.6027 9.29143 18.975 9.29143C19.2502 9.29143 19.4958 9.3373 19.7116 9.42903C19.9275 9.51537 20.1109 9.64488 20.262 9.81755C20.4185 9.98483 20.5345 10.1899 20.6101 10.4327C20.691 10.6755 20.7315 10.948 20.7315 11.2502C20.7315 11.3419 20.7045 11.4121 20.6505 11.4607C20.5966 11.5038 20.5156 11.5254 20.4077 11.5254H17.6881V11.0236H20.1811L20.0435 11.1369C20.0435 10.8671 20.003 10.6378 19.9221 10.4489C19.8465 10.26 19.7305 10.117 19.574 10.0199C19.4229 9.91738 19.2341 9.86611 19.0074 9.86611C18.7538 9.86611 18.538 9.92547 18.3599 10.0442C18.1872 10.1629 18.055 10.3275 17.9633 10.5379C17.8715 10.7484 17.8257 10.9939 17.8257 11.2745V11.3231C17.8257 11.7979 17.9363 12.1568 18.1575 12.3996C18.3842 12.637 18.7106 12.7557 19.1369 12.7557C19.2988 12.7557 19.4688 12.7341 19.6469 12.691C19.8303 12.6478 20.003 12.5749 20.1649 12.4724C20.2566 12.4185 20.3376 12.3942 20.4077 12.3996C20.4833 12.405 20.5426 12.4293 20.5858 12.4724C20.6343 12.5156 20.664 12.5696 20.6748 12.6343C20.6856 12.6991 20.6748 12.7665 20.6424 12.8367C20.6101 12.9014 20.5534 12.9608 20.4725 13.0147C20.289 13.1388 20.0758 13.2333 19.833 13.298C19.5902 13.3574 19.3555 13.3871 19.1288 13.3871ZM22.0355 13.3709C21.9006 13.3709 21.7981 13.3358 21.7279 13.2656C21.6578 13.1901 21.6227 13.0876 21.6227 12.9581V9.72042C21.6227 9.58552 21.6578 9.48299 21.7279 9.41284C21.7981 9.34269 21.8952 9.30762 22.0193 9.30762C22.1488 9.30762 22.246 9.34269 22.3107 9.41284C22.3809 9.48299 22.4159 9.58552 22.4159 9.72042V10.3113H22.335C22.4213 9.98753 22.5805 9.742 22.8125 9.57473C23.0446 9.40745 23.3414 9.31032 23.7029 9.28334C23.8 9.27794 23.8756 9.30222 23.9295 9.35619C23.9889 9.41015 24.0213 9.49378 24.0267 9.6071C24.0375 9.72042 24.0132 9.81215 23.9538 9.8823C23.8945 9.94706 23.8027 9.98483 23.6786 9.99562L23.5248 10.0118C23.1741 10.0442 22.907 10.1548 22.7235 10.3437C22.54 10.5325 22.4483 10.7915 22.4483 11.1207V12.9581C22.4483 13.0876 22.4132 13.1901 22.3431 13.2656C22.2783 13.3358 22.1758 13.3709 22.0355 13.3709ZM26.4404 13.3871C26.0087 13.3871 25.6364 13.3061 25.3234 13.1442C25.0159 12.977 24.7757 12.7422 24.6031 12.44C24.4358 12.1325 24.3521 11.7682 24.3521 11.3473C24.3521 10.9372 24.4358 10.5784 24.6031 10.2708C24.7703 9.96324 24.9997 9.72312 25.2911 9.55044C25.5824 9.37777 25.9143 9.29143 26.2866 9.29143C26.5618 9.29143 26.8074 9.3373 27.0232 9.42903C27.239 9.51537 27.4225 9.64488 27.5736 9.81755C27.7301 9.98483 27.8461 10.1899 27.9216 10.4327C28.0026 10.6755 28.0431 10.948 28.0431 11.2502C28.0431 11.3419 28.0161 11.4121 27.9621 11.4607C27.9082 11.5038 27.8272 11.5254 27.7193 11.5254H24.9997V11.0236H27.4927L27.3551 11.1369C27.3551 10.8671 27.3146 10.6378 27.2336 10.4489C27.1581 10.26 27.0421 10.117 26.8856 10.0199C26.7345 9.91738 26.5456 9.86611 26.319 9.86611C26.0654 9.86611 25.8495 9.92547 25.6715 10.0442C25.4988 10.1629 25.3666 10.3275 25.2749 10.5379C25.1831 10.7484 25.1373 10.9939 25.1373 11.2745V11.3231C25.1373 11.7979 25.2479 12.1568 25.4691 12.3996C25.6958 12.637 26.0222 12.7557 26.4485 12.7557C26.6104 12.7557 26.7804 12.7341 26.9584 12.691C27.1419 12.6478 27.3146 12.5749 27.4765 12.4724C27.5682 12.4185 27.6491 12.3942 27.7193 12.3996C27.7948 12.405 27.8542 12.4293 27.8974 12.4724C27.9459 12.5156 27.9756 12.5696 27.9864 12.6343C27.9972 12.6991 27.9864 12.7665 27.954 12.8367C27.9216 12.9014 27.865 12.9608 27.784 13.0147C27.6006 13.1388 27.3874 13.2333 27.1446 13.298C26.9018 13.3574 26.6671 13.3871 26.4404 13.3871ZM30.4398 13.3871C30.0944 13.3871 29.7896 13.3061 29.5252 13.1442C29.2661 12.977 29.0638 12.7395 28.9181 12.432C28.7724 12.119 28.6996 11.7547 28.6996 11.3392C28.6996 10.913 28.7724 10.5487 28.9181 10.2465C29.0638 9.94436 29.2661 9.70963 29.5252 9.54235C29.7896 9.37507 30.0944 9.29143 30.4398 9.29143C30.7851 9.29143 31.0819 9.37777 31.3301 9.55044C31.5838 9.71772 31.7537 9.94436 31.8401 10.2303H31.751V7.964C31.751 7.82909 31.7861 7.72657 31.8563 7.65642C31.9264 7.58627 32.0262 7.5512 32.1557 7.5512C32.2853 7.5512 32.3851 7.58627 32.4552 7.65642C32.5308 7.72657 32.5685 7.82909 32.5685 7.964V12.9581C32.5685 13.0876 32.5335 13.1901 32.4633 13.2656C32.3932 13.3358 32.2933 13.3709 32.1638 13.3709C32.0343 13.3709 31.9345 13.3358 31.8644 13.2656C31.7942 13.1901 31.7591 13.0876 31.7591 12.9581V12.2458L31.8482 12.4239C31.7618 12.7152 31.5919 12.95 31.3382 13.128C31.09 13.3007 30.7905 13.3871 30.4398 13.3871ZM30.6502 12.7557C30.8715 12.7557 31.0657 12.7018 31.233 12.5938C31.4003 12.4859 31.5298 12.3267 31.6215 12.1163C31.7187 11.9058 31.7672 11.6468 31.7672 11.3392C31.7672 10.8752 31.6647 10.5244 31.4597 10.287C31.2546 10.0496 30.9848 9.93087 30.6502 9.93087C30.4236 9.93087 30.2267 9.98483 30.0594 10.0927C29.8921 10.1953 29.7599 10.3518 29.6628 10.5622C29.571 10.7727 29.5252 11.0317 29.5252 11.3392C29.5252 11.7979 29.6277 12.1487 29.8327 12.3915C30.0378 12.6343 30.3103 12.7557 30.6502 12.7557ZM37.9713 13.3871C37.626 13.3871 37.3265 13.3007 37.0729 13.128C36.8192 12.95 36.6493 12.7152 36.5629 12.4239L36.652 12.2782V12.9581C36.652 13.0876 36.6169 13.1901 36.5467 13.2656C36.4766 13.3358 36.3795 13.3709 36.2554 13.3709C36.1259 13.3709 36.026 13.3358 35.9559 13.2656C35.8857 13.1901 35.8507 13.0876 35.8507 12.9581V7.964C35.8507 7.82909 35.8857 7.72657 35.9559 7.65642C36.026 7.58627 36.1259 7.5512 36.2554 7.5512C36.3849 7.5512 36.4847 7.58627 36.5548 7.65642C36.6304 7.72657 36.6682 7.82909 36.6682 7.964V10.2303H36.571C36.6574 9.94436 36.8273 9.71772 37.081 9.55044C37.3346 9.37777 37.6314 9.29143 37.9713 9.29143C38.3275 9.29143 38.635 9.37507 38.894 9.54235C39.1531 9.70963 39.3527 9.94436 39.493 10.2465C39.6387 10.5487 39.7115 10.913 39.7115 11.3392C39.7115 11.7547 39.6387 12.119 39.493 12.432C39.3527 12.7395 39.1504 12.977 38.8859 13.1442C38.6269 13.3061 38.3221 13.3871 37.9713 13.3871ZM37.769 12.7557C37.9956 12.7557 38.1926 12.7018 38.3598 12.5938C38.5271 12.4859 38.6566 12.3267 38.7483 12.1163C38.8401 11.9058 38.8859 11.6468 38.8859 11.3392C38.8859 10.8752 38.7834 10.5244 38.5784 10.287C38.3787 10.0496 38.1089 9.93087 37.769 9.93087C37.5477 9.93087 37.3508 9.98483 37.1781 10.0927C37.0108 10.1953 36.8813 10.3518 36.7896 10.5622C36.6978 10.7727 36.652 11.0317 36.652 11.3392C36.652 11.7979 36.7545 12.1487 36.9595 12.3915C37.1646 12.6343 37.4344 12.7557 37.769 12.7557ZM41.4925 14.8278C41.3954 14.8278 41.3144 14.8008 41.2497 14.7469C41.1903 14.6983 41.1553 14.6309 41.1445 14.5445C41.1391 14.4636 41.1553 14.3745 41.193 14.2774L41.7434 13.039V13.3871L40.2298 9.86611C40.1921 9.76898 40.1786 9.67725 40.1894 9.59091C40.2001 9.50458 40.2406 9.43713 40.3108 9.38856C40.3809 9.3346 40.4753 9.30762 40.5941 9.30762C40.6966 9.30762 40.7775 9.3319 40.8369 9.38047C40.8962 9.42903 40.9502 9.51267 40.9988 9.63139L42.221 12.6505H41.9943L43.2327 9.63139C43.2813 9.50728 43.338 9.42364 43.4027 9.38047C43.4675 9.3319 43.5565 9.30762 43.6698 9.30762C43.767 9.30762 43.8425 9.3346 43.8965 9.38856C43.9558 9.43713 43.9909 9.50458 44.0017 9.59091C44.0179 9.67186 44.0044 9.76089 43.9612 9.85802L41.9377 14.5121C41.8837 14.6362 41.8217 14.7199 41.7515 14.7631C41.6868 14.8062 41.6004 14.8278 41.4925 14.8278Z" fill="#00A38F" />
                    <line x1="52.162" y1="0.603285" x2="52.162" y2="20.8386" stroke="#7AC9BD" stroke-width="0.578151" stroke-linecap="round" />
                    <path d="M52.7403 11.5898L52.7403 9.85156L53.7442 10.7207L52.7403 11.5898Z" fill="#7AC9BD" stroke="#7AC9BD" stroke-width="0.578151" />
                    <path d="M68.7946 12.806H68.2764C67.13 12.806 66.6746 12.2721 66.6746 11.267V8.00052C66.6746 6.60283 66.0464 6.00607 65.1198 6.00607C64.2718 6.00607 63.6907 6.60283 63.6907 7.76495V12.806H63.1254C62.1988 12.806 61.5864 12.2721 61.5864 11.267V8.00052C61.5864 6.60283 60.9425 6.00607 60.0159 6.00607C59.1993 6.00607 58.5868 6.64995 58.5868 7.81207V12.806H58.0215C57.1578 12.806 56.4982 12.2407 56.4982 11.267V7.98481C56.4982 5.67628 57.7074 4.13726 60.2672 4.13726C61.3665 4.13726 62.2302 4.6398 62.8427 5.45642C63.3295 4.6712 64.2561 4.13726 65.2769 4.13726C67.2242 4.13726 68.7946 5.31508 68.7946 7.71784V12.806Z" fill="#EA1F27" />
                    <path d="M80.3854 12.806H79.5374C78.705 12.806 77.7942 12.5548 76.4593 9.96356L74.4963 6.14741C74.1979 5.55065 73.9623 5.14233 73.7739 4.89106V12.806H72.9259C72.2977 12.806 71.6224 12.3349 71.6224 11.3926V2.04858H72.6903C74.1037 2.04858 75.0145 2.80239 75.7369 4.24719L77.6214 8.01622C77.8413 8.45594 78.0926 9.09982 78.2496 9.46102V2.04858H79.2076C79.7572 2.04858 80.3854 2.59824 80.3854 3.28923V12.806Z" fill="#00A38F" />
                    <path d="M89.8626 10.7959L89.8469 11.5497C89.8312 12.4134 89.3915 12.8846 87.5069 12.8846H86.3605C83.7379 12.8846 82.3559 11.4398 82.3559 8.69151V6.69706C82.3559 5.67628 82.4344 4.52987 82.5601 3.66613C82.7328 2.37837 83.3296 2.04858 84.209 2.04858H89.8312V2.77098C89.8312 3.52479 89.3758 3.90169 88.3864 3.90169H84.6959C84.5859 4.68691 84.5388 5.7391 84.5388 6.41438H88.6848V7.18389C88.6848 7.89059 88.245 8.17327 87.3813 8.17327H84.5388V8.80144C84.5388 10.6703 85.1513 11.0315 86.816 11.0315H87.5069C88.2922 11.0315 89.0931 10.9686 89.8626 10.7959Z" fill="#00A38F" />
                    <path d="M98.8073 2.72387C98.8073 3.24211 98.2262 3.9331 97.5195 3.9331H95.5408V12.806H94.6771C93.9233 12.806 93.3736 12.3192 93.3736 11.3612V3.9331H90.2799V3.22641C90.2799 2.70817 90.7039 2.04858 91.5833 2.04858H98.8073V2.72387Z" fill="#00A38F" />
                    <path d="M59.058 17.0272C59.058 17.8804 58.5345 18.5243 57.7388 18.5243C56.9588 18.5243 56.4982 17.9537 56.4982 17.1371V14.7029H56.7233C56.8489 14.7029 56.985 14.7972 56.985 15.0275V16.1739C57.142 15.8284 57.4928 15.619 57.8801 15.619C58.524 15.619 59.058 16.1163 59.058 17.0272ZM58.5711 17.0795C58.5711 16.3205 58.2099 16.064 57.7545 16.064C57.3043 16.064 56.985 16.4723 56.985 17.0795C56.985 17.781 57.3148 18.0794 57.8016 18.0794C58.2675 18.0794 58.5711 17.6815 58.5711 17.0795Z" fill="#A6ADB4" />
                    <path d="M61.9541 16.1791C61.7395 17.0115 61.2684 17.8961 61.1166 18.205C60.6716 19.1054 60.4099 19.3933 60.012 19.3933C59.7974 19.3933 59.6509 19.2729 59.6509 19.1525V18.9745H59.8759C60.0958 18.9745 60.2371 18.9222 60.5617 18.3097C60.122 17.6344 59.6561 16.5874 59.4833 15.6714H59.7555C59.9492 15.6714 60.0173 15.8232 60.0487 15.954C60.1743 16.5142 60.457 17.2837 60.8077 17.8595C61.1061 17.3465 61.5092 16.2838 61.6034 15.6714H61.7919C61.9175 15.6714 62.0117 15.7185 62.0117 15.886C62.0117 15.9436 61.9751 16.1006 61.9541 16.1791Z" fill="#A6ADB4" />
                    <path d="M66.1837 17.4198C66.1837 18.0846 65.6759 18.4981 65.0111 18.4981H64.5924C63.9328 18.4981 63.671 18.184 63.671 17.7024V15.0327C63.8752 14.9647 64.2835 14.8861 64.5348 14.8861H65.0059C65.7231 14.8861 66.1157 15.2997 66.1157 15.8179C66.1157 16.1582 65.99 16.4618 65.4823 16.6084C66.0424 16.7602 66.1837 17.1633 66.1837 17.4198ZM64.1422 15.373V16.4304H64.8698C65.3671 16.4304 65.6445 16.2891 65.6445 15.865C65.6445 15.4724 65.3828 15.3102 64.9012 15.3102H64.6081C64.4877 15.3102 64.3149 15.3311 64.1422 15.373ZM64.9012 16.8282H64.1474V17.6815C64.1474 17.938 64.3044 18.0689 64.6185 18.0689H64.9902C65.4247 18.0689 65.7021 17.8647 65.7021 17.4512C65.7021 16.9958 65.477 16.8282 64.9012 16.8282Z" fill="#A6ADB4" />
                    <path d="M68.3109 16.2629H68.2847C68.1957 16.1844 68.0439 16.1215 67.8555 16.1215C67.4838 16.1215 67.3424 16.3048 67.3424 16.9015V18.472H67.1383C66.9655 18.472 66.8661 18.3359 66.8661 18.1526V16.8649C66.8661 16.0901 67.243 15.6766 67.7874 15.6766C68.1695 15.6766 68.3109 15.8441 68.3109 16.0326V16.2629Z" fill="#A6ADB4" />
                    <path d="M69.4117 14.9594C69.4117 15.1374 69.2703 15.284 69.0923 15.284C68.9143 15.284 68.7678 15.1374 68.7678 14.9594C68.7678 14.7814 68.9143 14.6296 69.0923 14.6296C69.2703 14.6296 69.4117 14.7814 69.4117 14.9594ZM69.9561 18.2155C69.9561 18.3934 69.8461 18.4981 69.6786 18.4981C69.129 18.4981 68.8411 18.2259 68.8411 17.6658V15.6714H68.9876C69.1499 15.6714 69.3174 15.797 69.3174 16.043V17.5978C69.3174 17.9485 69.4902 18.0584 69.7519 18.0584H69.9561V18.2155Z" fill="#A6ADB4" />
                    <path d="M72.7691 18.472H72.6435C72.3922 18.472 72.2928 18.3725 72.2928 18.1631V16.8387C72.2928 16.2786 72.0572 16.0692 71.6227 16.0692C71.2982 16.0692 70.9265 16.3362 70.9265 16.8649V18.472H70.7904C70.5548 18.472 70.4554 18.3725 70.4554 18.1631V14.7029H70.5967C70.8113 14.7029 70.937 14.7972 70.937 15.0589V16.1111C71.1411 15.7342 71.4343 15.619 71.6908 15.619C72.3556 15.619 72.7691 15.9854 72.7691 16.823V18.472Z" fill="#A6ADB4" />
                    <path d="M75.7086 18.5243C75.2008 18.5243 74.6407 18.2207 74.6407 17.8176V17.5873H74.6669C74.8815 17.8752 75.2898 18.0741 75.7452 18.0741C76.3577 18.0741 76.5514 17.849 76.5514 17.4931C76.5514 17.1214 76.3577 16.9853 75.6248 16.823C74.9286 16.6712 74.5883 16.4409 74.5883 15.9331C74.5883 15.3259 75.1014 14.86 75.9389 14.86C76.5985 14.86 76.9597 15.195 76.9597 15.5195V15.7342H76.9335C76.7084 15.4515 76.4153 15.3102 75.9127 15.3102C75.3893 15.3102 75.0909 15.5143 75.0909 15.8598C75.0909 16.1948 75.2479 16.3048 75.9546 16.467C76.4572 16.5822 77.0487 16.8597 77.0487 17.4407C77.0487 18.0322 76.6194 18.5243 75.7086 18.5243Z" fill="#A6ADB4" />
                    <path d="M80.1587 17.0167C80.1587 17.959 79.6614 18.5243 78.9023 18.5243C78.159 18.5243 77.6041 17.9537 77.6041 17.1214C77.6041 16.1948 78.1119 15.6242 78.9023 15.6242C79.6457 15.6242 80.1587 16.1844 80.1587 17.0167ZM79.6771 17.1266C79.6771 16.4356 79.3839 16.0744 78.8343 16.0744C78.3841 16.0744 78.0857 16.4409 78.0857 17.0219C78.0857 17.6658 78.3998 18.0741 78.9494 18.0741C79.4206 18.0741 79.6771 17.6763 79.6771 17.1266Z" fill="#A6ADB4" />
                    <path d="M82.0989 18.2102C82.0989 18.4039 81.9732 18.4929 81.7167 18.4929C81.1671 18.4929 80.8687 18.2259 80.8687 17.6606V14.7029H81.0153C81.2089 14.7029 81.3503 14.8233 81.3503 15.0694V17.603C81.3503 17.9537 81.5283 18.0532 81.79 18.0532H82.0989V18.2102Z" fill="#A6ADB4" />
                    <path d="M84.8069 17.268C84.8069 18.0898 84.3828 18.5243 83.6343 18.5243C82.9485 18.5243 82.4931 18.0479 82.4931 17.2261V15.6714H82.6397C82.8543 15.6714 82.9695 15.797 82.9695 16.043V17.1999C82.9695 17.7705 83.2574 18.0741 83.6657 18.0741C84.1054 18.0741 84.3305 17.8228 84.3305 17.2208V15.6714H84.4614C84.6917 15.6714 84.8069 15.7918 84.8069 16.043V17.268Z" fill="#A6ADB4" />
                    <path d="M86.0634 15.1008V15.6766H87.0266V15.8336C87.0266 16.0064 86.9114 16.1215 86.6601 16.1215H86.0686V17.5088C86.0686 17.9066 86.2257 18.0741 86.5293 18.0741C86.702 18.0741 86.8695 18.0584 86.969 17.9956H86.9952V18.1631C86.9952 18.3987 86.8119 18.5243 86.4874 18.5243C85.8645 18.5243 85.5922 18.1998 85.5922 17.5559V15.7342C85.5922 15.5719 85.6341 15.4149 85.7126 15.2997C85.7807 15.2002 85.9377 15.1008 86.0058 15.1008H86.0634Z" fill="#A6ADB4" />
                    <path d="M88.1679 14.9594C88.1679 15.1374 88.0265 15.284 87.8486 15.284C87.6706 15.284 87.524 15.1374 87.524 14.9594C87.524 14.7814 87.6706 14.6296 87.8486 14.6296C88.0265 14.6296 88.1679 14.7814 88.1679 14.9594ZM88.7123 18.2155C88.7123 18.3934 88.6024 18.4981 88.4348 18.4981C87.8852 18.4981 87.5973 18.2259 87.5973 17.6658V15.6714H87.7439C87.9061 15.6714 88.0736 15.797 88.0736 16.043V17.5978C88.0736 17.9485 88.2464 18.0584 88.5081 18.0584H88.7123V18.2155Z" fill="#A6ADB4" />
                    <path d="M91.6353 17.0167C91.6353 17.959 91.138 18.5243 90.3789 18.5243C89.6356 18.5243 89.0807 17.9537 89.0807 17.1214C89.0807 16.1948 89.5885 15.6242 90.3789 15.6242C91.1223 15.6242 91.6353 16.1844 91.6353 17.0167ZM91.1537 17.1266C91.1537 16.4356 90.8605 16.0744 90.3109 16.0744C89.8607 16.0744 89.5623 16.4409 89.5623 17.0219C89.5623 17.6658 89.8764 18.0741 90.4261 18.0741C90.8972 18.0741 91.1537 17.6763 91.1537 17.1266Z" fill="#A6ADB4" />
                    <path d="M94.6067 18.472H94.434C94.2403 18.472 94.1356 18.3725 94.1356 18.1631V16.8754C94.1356 16.2943 93.921 16.0692 93.4237 16.0692C93.0049 16.0692 92.7641 16.3048 92.7641 16.8754V18.472H92.5861C92.4134 18.472 92.293 18.3673 92.293 18.1474V16.8649C92.293 16.0797 92.7641 15.619 93.4812 15.619C94.1827 15.619 94.6067 16.0221 94.6067 16.8597V18.472Z" fill="#A6ADB4" />
                    <path d="M97.2452 17.6239C97.2452 18.2102 96.7898 18.5243 96.1511 18.5243C95.68 18.5243 95.3031 18.3044 95.3031 17.9694V17.7653H95.3293C95.4863 17.938 95.8004 18.0794 96.1616 18.0794C96.5752 18.0794 96.7584 17.9485 96.7584 17.671C96.7584 17.4407 96.6589 17.3412 96.0203 17.2051C95.4968 17.0952 95.2508 16.9172 95.2508 16.4985C95.2508 16.043 95.6643 15.619 96.3605 15.619C96.7741 15.619 97.1772 15.8075 97.1772 16.1477V16.3571H97.151C97.0306 16.2105 96.727 16.0692 96.3972 16.0692C95.9208 16.0692 95.7324 16.2315 95.7324 16.467C95.7324 16.6555 95.8371 16.734 96.4181 16.8597C96.973 16.9801 97.2452 17.2366 97.2452 17.6239Z" fill="#A6ADB4" />
                    <path d="M101.512 15.9802C101.512 16.6503 100.978 17.1423 100.292 17.1423C99.9726 17.1423 99.7213 17.0847 99.58 16.9905V18.472H99.402C99.224 18.472 99.0984 18.3725 99.0984 18.1579V15.0484C99.313 14.9647 99.7213 14.8861 100.004 14.8861H100.281C100.993 14.8861 101.512 15.284 101.512 15.9802ZM101.03 16.0326C101.03 15.5667 100.784 15.3311 100.292 15.3311H100.02C99.8993 15.3311 99.6899 15.3573 99.58 15.3834V16.5822C99.7736 16.6869 100.025 16.7183 100.297 16.7183C100.784 16.7183 101.03 16.4566 101.03 16.0326Z" fill="#A6ADB4" />
                    <path d="M104.144 16.2577C103.986 16.9015 103.536 18.0218 103.097 18.5243H102.966C102.877 18.5243 102.798 18.4929 102.725 18.3882C102.191 17.5978 101.83 16.6084 101.688 15.6714H101.924C102.112 15.6714 102.186 15.8022 102.212 15.9436C102.338 16.5822 102.552 17.3151 102.987 17.9851C103.4 17.357 103.746 16.1948 103.829 15.6714H103.997C104.133 15.6714 104.201 15.7656 104.201 15.8965C104.201 16.0273 104.164 16.1791 104.144 16.2577Z" fill="#A6ADB4" />
                    <path d="M105.208 15.1008V15.6766H106.171V15.8336C106.171 16.0064 106.056 16.1215 105.805 16.1215H105.213V17.5088C105.213 17.9066 105.37 18.0741 105.674 18.0741C105.847 18.0741 106.014 18.0584 106.114 17.9956H106.14V18.1631C106.14 18.3987 105.957 18.5243 105.632 18.5243C105.009 18.5243 104.737 18.1998 104.737 17.5559V15.7342C104.737 15.5719 104.779 15.4149 104.857 15.2997C104.925 15.2002 105.082 15.1008 105.151 15.1008H105.208Z" fill="#A6ADB4" />
                    <path d="M107.402 18.1055C107.402 18.3202 107.239 18.472 107.03 18.472C106.821 18.472 106.653 18.3202 106.653 18.1055C106.653 17.8909 106.821 17.7339 107.03 17.7339C107.239 17.7339 107.402 17.8909 107.402 18.1055Z" fill="#A6ADB4" />
                    <path d="M111.531 18.1108C111.531 18.3254 111.426 18.4981 110.835 18.4981H110.536C109.709 18.4981 109.353 18.0375 109.353 17.2208V14.9071H109.526C109.714 14.9071 109.83 14.9856 109.83 15.263V17.0952C109.83 17.8543 109.987 18.0479 110.636 18.0479H110.835C111.091 18.0479 111.363 18.0165 111.531 17.9694V18.1108Z" fill="#A6ADB4" />
                    <path d="M112.431 15.1008V15.6766H113.395V15.8336C113.395 16.0064 113.28 16.1215 113.028 16.1215H112.437V17.5088C112.437 17.9066 112.594 18.0741 112.897 18.0741C113.07 18.0741 113.238 18.0584 113.337 17.9956H113.363V18.1631C113.363 18.3987 113.18 18.5243 112.855 18.5243C112.233 18.5243 111.96 18.1998 111.96 17.5559V15.7342C111.96 15.5719 112.002 15.4149 112.081 15.2997C112.149 15.2002 112.306 15.1008 112.374 15.1008H112.431Z" fill="#A6ADB4" />
                    <path d="M116.337 16.9905C116.337 18.0636 115.944 18.5243 115.07 18.5243C114.374 18.5243 113.787 17.8909 113.787 17.1109C113.787 16.153 114.238 15.619 115.012 15.619C115.353 15.619 115.661 15.7761 115.855 16.1582V14.7029H115.981C116.216 14.7029 116.337 14.8286 116.337 15.0903V16.9905ZM115.85 17.1214C115.85 16.6136 115.698 16.064 115.028 16.064C114.541 16.064 114.269 16.4356 114.269 17.069C114.269 17.6292 114.615 18.0794 115.112 18.0794C115.62 18.0794 115.85 17.8071 115.85 17.1214Z" fill="#A6ADB4" />
                    <path d="M117.83 18.1055C117.83 18.3202 117.668 18.472 117.459 18.472C117.249 18.472 117.082 18.3202 117.082 18.1055C117.082 17.8909 117.249 17.7339 117.459 17.7339C117.668 17.7339 117.83 17.8909 117.83 18.1055Z" fill="#A6ADB4" />
                  </svg>
                </div>
              </div>
              <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2">
                <div className="w-[136px] h-[136px] rounded-full bg-white">
                  <img
                    src={businessLogo}
                    alt="Business Logo"
                    className="w-full h-full rounded-full object-cover border-8 border-white mx-auto"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white mx-3 py-2 px-3 rounded-lg">
          <p className="mb-1 font-medium">Save as:</p>
          <RadioGroup name="saveAs" value={format} className="flex items-center space-x-4" onValueChange={(val) => setFormat(val as "png" | "pdf")}>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="png" id="png" />
              <label htmlFor="png">PNG</label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="pdf" id="pdf" />
              <label htmlFor="pdf">PDF</label>
            </div>
          </RadioGroup>
        </div>

        <div className="bg-white p-2 rounded-lg">
          <Button type="button" className="w-full" onClick={handleDownload}>
            <DownloadIcon /> Download QR Code
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

