import { <PERSON>alog, DialogContent, DialogTitle } from "@radix-ui/react-dialog"
import { useFetcher } from "@remix-run/react"
import { useEffect } from "react"
import { useToast } from "./ToastProvider"
import { Transaction } from "~/types/api/businessConsoleService/payments";
import { markAsUntransferable } from "node:worker_threads";
import SpinnerLoader from "../loader/SpinnerLoader";

interface MarkAsPaidProps {
      heading: string;
      isOpen: boolean;
      content: string;
      isClosed: () => void;
      row: Transaction | null;
}

export default function MarkAsPaid({ heading, isOpen, content, isClosed, row }: MarkAsPaidProps) {
      const fetcher = useFetcher();
      const { showToast } = useToast();
      const isLoading = fetcher.state !== "idle";

      useEffect(() => {
            if (fetcher.data) {
                  if (fetcher.data !== undefined && isOpen === true) {
                        showToast("Mark As Paid", "success");
                        isClosed();
                  } else if (fetcher.data === undefined) {
                        showToast("Fail To create Agent", "error");
                  }
            }
      }, [fetcher.data, isOpen, isClosed, showToast]);



      const handleSubmit = () => {
            const formData = new FormData();
            formData.append("intent", "markAsPaid");
            formData.append("amount", row?.amount as unknown as string);
            formData.append("depositId", row?.id as unknown as string);
            formData.append("note", "Mark as Paid");

            fetcher.submit(formData, { method: "post" })
      }


      return (
            <Dialog open={isOpen} onOpenChange={isClosed}>
                  {/* Overlay Background */}
                  {/* <div className="fixed inset-0 white-bg-black/50 backdrop-blur-sm" /> */}

                  {/* Dialog Content */}
                  {isLoading && (
                        <div className="absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm">
                              <SpinnerLoader loading={isLoading} />
                        </div>
                  )}
                  <DialogContent className="fixed inset-0 flex items-center justify-center">
                        <div className="bg-orange-50 p-6 rounded-lg shadow-lg w-[400px]">
                              <DialogTitle className="text-lg font-bold">{heading}</DialogTitle>
                              <form onSubmit={() => handleSubmit()} className="space-y-4">
                                    <p className="text-red-400 font-bold text-sm">{content}</p>
                                    <div className="flex justify-end space-x-2">
                                          <button
                                                type="button"
                                                onClick={isClosed}
                                                className="px-4 py-2 bg-gray-300 rounded"
                                          >
                                                Cancel
                                          </button>
                                          <button
                                                type="submit"
                                                className="px-4 py-2 bg-blue-600 text-white rounded"
                                          >
                                                {isLoading ? "Submitting..." : "Submit"}
                                          </button>
                                    </div>
                              </form>
                        </div>
                  </DialogContent>
            </Dialog>
      );
}
