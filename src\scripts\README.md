# WhatsApp Template <PERSON><PERSON> Script

This script sends WhatsApp template messages to multiple recipients using data from an Excel file.

## Prerequisites

1. Node.js installed
2. Required npm packages:
   - xlsx
   - firebase-admin
   - express
   - typescript

## Setup

1. Install dependencies:
```bash
npm install xlsx firebase-admin express typescript
```

2. Create input directory:
```bash
mkdir -p src/scripts/input
```

3. Create output directory:
```bash
mkdir -p src/scripts/output
```

## Excel File Format

### Input File (whatsapp_templates.xlsx)

The input Excel file should have the following columns:

- sellerId: The ID of the seller in Firebase
- templateId: The WhatsApp template ID to use
- targetPhoneNumber: The recipient's phone number (with country code)
- customer_name: Customer's name (template variable)
- pending_amount: Pending amount (template variable)
- due_date: Due date (template variable)
- status: Initial status (optional)

### Output File (whatsapp_templates_results.xlsx)

The output Excel file will contain all input columns plus:

- messageId: The ID of the sent message (if successful)
- error: Error message (if failed)
- timestamp: When the message was processed

## Usage

1. Place your input Excel file in the `src/scripts/input` directory
2. Run the script:
```bash
ts-node src/scripts/whatsappTemplateSender.ts
```

3. Check the results in `src/scripts/output/whatsapp_templates_results.xlsx`

## Error Handling

The script handles various errors:

- Firebase connection errors
- Missing seller data
- Invalid template IDs
- Invalid phone numbers
- API errors

All errors are logged to the console and recorded in the output Excel file.

## Example

See `src/scripts/input/whatsapp_templates.xlsx` for a sample input file using the `early_pending_payment_special_offer` template. 