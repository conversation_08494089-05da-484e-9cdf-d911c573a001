import { AttributeValue } from '@aws-sdk/client-dynamodb';
import dotenv from 'dotenv';

dotenv.config();

const serverEnv = process.env.SERVER_ENV === 'production' ? 'prod' : 'uat';

const notificationLogsTableName = `notification_logs_${serverEnv}`;

export enum NotificationChannel {
    WHATSAPP = 'WHATSAPP',
    FIREBASE = 'FIREBASE'
}

export enum NotificationStatus {
    PENDING = 'PENDING',
    SENT = 'SENT',
    FAILED = 'FAILED',
    DELIVERED = 'DELIVERED',
    READ = 'READ'
}

export enum CampaignType {
    PROMOTIONAL = 'PROMOTIONAL',
    TRANSACTIONAL = 'TRANSACTIONAL',
    NOTIFICATION = 'NOTIFICATION',
    MARKETING = 'MARKETING',
    SUPPORT = 'SUPPORT'
}

export enum MessageCategory {
    ORDER_UPDATE = 'ORDER_UPDATE',
    PAYMENT_REMINDER = 'PAYMENT_REMINDER',
    PROMOTIONAL_OFFER = 'PROMOTIONAL_OFFER',
    DELIVERY_UPDATE = 'DELIVERY_UPDATE',
    WELCOME_MESSAGE = 'WELCOME_MESSAGE',
    SUPPORT_MESSAGE = 'SUPPORT_MESSAGE',
    GENERAL = 'GENERAL',
    DIRECT_ORDER_PROMOTION = 'DIRECT_ORDER_PROMOTION',
    DISCOUNT_OFFER = 'DISCOUNT_OFFER',
    ACQUISITION_CAMPAIGN = 'ACQUISITION_CAMPAIGN',
    RETENTION_OFFER = 'RETENTION_OFFER',
}

export enum CustomerSegment {
    NEW_CUSTOMER = 'NEW_CUSTOMER',
    RETURNING_CUSTOMER = 'RETURNING_CUSTOMER',
    VIP_CUSTOMER = 'VIP_CUSTOMER',
    HIGH_VALUE = 'HIGH_VALUE',
    LOW_ENGAGEMENT = 'LOW_ENGAGEMENT',
    DORMANT = 'DORMANT',
    GENERAL = 'GENERAL'
}

export interface NotificationLog {
    notificationId: string;           // Partition key
    timestamp: number;                // Sort key
    timestampISO: string;             // ISO timestamp in IST timezone for readability
    businessId: string;              // GSI partition key
    channel: NotificationChannel;
    status: NotificationStatus;
    recipient: string;                 // Phone number for WhatsApp, device token for Firebase
    mobileNumber: string;              // Mobile number for all notifications
    
    // 🆕 Time-based partitioning fields
    datePartition?: string;           // Format: YYYY-MM-DD for daily partitioning
    monthPartition?: string;          // Format: YYYY-MM for monthly partitioning
    
    // Campaign and Marketing fields
    campaignId?: string;              // Campaign identifier for grouping messages
    campaignName?: string;            // Human-readable campaign name
    campaignType?: CampaignType;      // Type of campaign
    messageCategory?: MessageCategory; // Category for analytics and filtering
    customerSegment?: CustomerSegment; // Customer segment for targeting analytics
    tags?: string[];                  // Flexible tags for custom categorization
    
    // WhatsApp Message Tracking
    whatsappMessageId?: string;       // WhatsApp message ID from API response
    whatsappStatus?: NotificationStatus; // Current WhatsApp delivery status
    
    // Webhook Integration
    webhookLogId?: string;            // Reference to webhook log entry
    
    // Payload information
    inputPayload: Record<string, any>;
    providerRequest: Record<string, any>;
    providerResponse: Record<string, any>;
    
    // Metadata
    errorMessage?: Record<string, any> | string;
    retryCount: number;
    lastUpdated: number;
    lastUpdatedISO: string;           // ISO timestamp in IST timezone
    
    // Analytics metadata with ISO timestamps in IST timezone
    sentAt?: number;                  // When message was actually sent
    sentAtISO?: string;               // ISO timestamp in IST timezone
    deliveredAt?: number;             // When message was delivered
    deliveredAtISO?: string;          // ISO timestamp in IST timezone
    readAt?: number;                  // When message was read
    readAtISO?: string;               // ISO timestamp in IST timezone
    failedAt?: number;                // When message failed
    failedAtISO?: string;             // ISO timestamp in IST timezone
}

// DynamoDB table definition
export const NotificationLogTable = {
    TableName: notificationLogsTableName,
    KeySchema: [
        { AttributeName: 'notificationId', KeyType: 'HASH' },
        { AttributeName: 'timestamp', KeyType: 'RANGE' }
    ],
    AttributeDefinitions: [
        { AttributeName: 'notificationId', AttributeType: 'S' },
        { AttributeName: 'timestamp', AttributeType: 'N' },
        { AttributeName: 'businessId', AttributeType: 'S' },
        { AttributeName: 'mobileNumber', AttributeType: 'S' },
        { AttributeName: 'campaignId', AttributeType: 'S' },
        { AttributeName: 'whatsappMessageId', AttributeType: 'S' },
        { AttributeName: 'messageCategory', AttributeType: 'S' },
        { AttributeName: 'customerSegment', AttributeType: 'S' },
        { AttributeName: 'whatsappStatus', AttributeType: 'S' },
        { AttributeName: 'datePartition', AttributeType: 'S' },
        { AttributeName: 'monthPartition', AttributeType: 'S' }
    ],
    GlobalSecondaryIndexes: [
        {
            IndexName: 'BusinessIndex',
            KeySchema: [
                { AttributeName: 'businessId', KeyType: 'HASH' },
                // { AttributeName: 'timestamp', KeyType: 'RANGE' }
            ],
            Projection: {
                ProjectionType: 'ALL'
            }
        },
        {
            IndexName: 'MobileNumberIndex',
            KeySchema: [
                { AttributeName: 'mobileNumber', KeyType: 'HASH' },
                // { AttributeName: 'timestamp', KeyType: 'RANGE' }
            ],
            Projection: {
                ProjectionType: 'ALL'
            }
        },
        {
            IndexName: 'CampaignIndex',
            KeySchema: [
                { AttributeName: 'campaignId', KeyType: 'HASH' },
                { AttributeName: 'timestamp', KeyType: 'RANGE' }
            ],
            Projection: {
                ProjectionType: 'ALL'
            }
        },
        {
            IndexName: 'WhatsAppMessageIndex',
            KeySchema: [
                { AttributeName: 'whatsappMessageId', KeyType: 'HASH' }
            ],
            Projection: {
                ProjectionType: 'ALL'
            }
        },
        {
            IndexName: 'MessageCategoryIndex',
            KeySchema: [
                { AttributeName: 'messageCategory', KeyType: 'HASH' },
                { AttributeName: 'timestamp', KeyType: 'RANGE' }
            ],
            Projection: {
                ProjectionType: 'ALL'
            }
        },
        {
            IndexName: 'CustomerSegmentIndex',
            KeySchema: [
                { AttributeName: 'customerSegment', KeyType: 'HASH' },
                { AttributeName: 'timestamp', KeyType: 'RANGE' }
            ],
            Projection: {
                ProjectionType: 'ALL'
            }
        },
        {
            IndexName: 'WhatsAppStatusIndex',
            KeySchema: [
                { AttributeName: 'whatsappStatus', KeyType: 'HASH' },
                { AttributeName: 'timestamp', KeyType: 'RANGE' }
            ],
            Projection: {
                ProjectionType: 'ALL'
            }
        },
        {
            IndexName: 'DatePartitionIndex',
            KeySchema: [
                { AttributeName: 'datePartition', KeyType: 'HASH' },
                { AttributeName: 'timestamp', KeyType: 'RANGE' }
            ],
            Projection: {
                ProjectionType: 'ALL'
            }
        },
        {
            IndexName: 'MonthPartitionIndex',
            KeySchema: [
                { AttributeName: 'monthPartition', KeyType: 'HASH' },
                { AttributeName: 'timestamp', KeyType: 'RANGE' }
            ],
            Projection: {
                ProjectionType: 'ALL'
            }
        },
        {
            IndexName: 'BusinessDateIndex',
            KeySchema: [
                { AttributeName: 'businessId', KeyType: 'HASH' },
                { AttributeName: 'datePartition', KeyType: 'RANGE' }
            ],
            Projection: {
                ProjectionType: 'ALL'
            }
        }
    ],
    BillingMode: 'PAY_PER_REQUEST'
}; 