import { NotificationLogService } from './notificationLogService.js';
import { WebhookLogService } from './webhookLog.service.js';
import { NotificationStatus } from '../database/entities/NotificationLog.js';
import { WhatsAppMessageStatus } from '../types/webhook.types.js';
import { WhatsAppStatus, WhatsAppError } from '../types/whatsapp-webhook.types.js';
import { 
    WebhookNotificationUpdate,
    NotificationSyncResult
} from '../types/notification.types.js';

/**
 * Service to sync webhook status updates with notification logs
 * Handles WhatsApp message status updates and provides analytics aggregation
 */
export class WebhookNotificationSyncService {
    private notificationLogService: NotificationLogService;
    private webhookLogService: WebhookLogService;

    constructor() {
        this.notificationLogService = new NotificationLogService();
        this.webhookLogService = new WebhookLogService();
    }

    /**
     * Process WhatsApp status updates from webhook and sync to notification logs
     */
    async processStatusUpdates(webhookId: string, statusUpdates: WhatsAppStatus[]): Promise<NotificationSyncResult> {
        const syncId = `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const startTime = Date.now();
        
        const result: NotificationSyncResult = {
            syncId,
            timestamp: startTime,
            totalUpdatesProcessed: statusUpdates.length,
            successfulUpdates: 0,
            failedUpdates: 0,
            skippedUpdates: 0,
            processedNotifications: [],
            failedNotifications: [],
            processingDuration: 0,
            averageUpdateTime: 0
        };

        console.log(`🔄 Starting webhook sync for ${statusUpdates.length} status updates (ID: ${syncId})`);

        try {
            // Process each status update
            for (const statusUpdate of statusUpdates) {
                try {
                    const updateResult = await this.processSingleStatusUpdate(webhookId, statusUpdate);
                    
                    if (updateResult.successfulUpdates > 0) {
                        result.successfulUpdates++;
                        result.processedNotifications.push(...updateResult.processedNotifications);
                    } else if (updateResult.skippedUpdates > 0) {
                        result.skippedUpdates++;
                    } else {
                        result.failedUpdates++;
                        result.failedNotifications.push(...updateResult.failedNotifications);
                    }
                    
                } catch (error) {
                    result.failedUpdates++;
                    result.failedNotifications.push({
                        whatsappMessageId: statusUpdate.id,
                        error: error instanceof Error ? error.message : String(error)
                    });
                    console.error(`❌ Error processing status update for message ${statusUpdate.id}:`, error);
                }
            }

            // Calculate performance metrics
            const endTime = Date.now();
            result.processingDuration = endTime - startTime;
            result.averageUpdateTime = result.totalUpdatesProcessed > 0 
                ? result.processingDuration / result.totalUpdatesProcessed 
                : 0;

            console.log(`✅ Webhook sync completed: ${result.successfulUpdates}/${result.totalUpdatesProcessed} successful (${result.processingDuration}ms)`);

            return result;

        } catch (error) {
            console.error('❌ Critical error in webhook sync:', error);
            throw error;
        }
    }

    /**
     * Process a single WhatsApp status update
     */
    private async processSingleStatusUpdate(
        webhookId: string, 
        statusUpdate: WhatsAppStatus
    ): Promise<NotificationSyncResult> {
        try {
            // Map WhatsApp status to NotificationStatus
            const notificationStatus = this.mapWhatsAppStatusToNotificationStatus(statusUpdate.status);
            
            // Create webhook notification update
            const webhookUpdate: WebhookNotificationUpdate = {
                whatsappMessageId: statusUpdate.id,
                status: notificationStatus,
                timestamp: parseInt(statusUpdate.timestamp) * 1000, // Convert to milliseconds
                recipientId: statusUpdate.recipient_id,
                conversationId: statusUpdate.conversation?.id,
                error: statusUpdate.errors && statusUpdate.errors.length > 0 ? {
                    code: statusUpdate.errors[0].code || 0,
                    title: statusUpdate.errors[0].title || 'Unknown error',
                    message: statusUpdate.errors[0].message || 'Unknown error occurred',
                    errorData: statusUpdate.errors[0].error_data
                } : undefined,
                pricing: statusUpdate.pricing ? {
                    billable: statusUpdate.pricing.billable,
                    pricingModel: statusUpdate.pricing.pricing_model,
                    category: statusUpdate.pricing.category
                } : undefined
            };

            // Update notification log
            const syncResult = await this.notificationLogService.updateStatusFromWebhook(webhookUpdate);
            
            // Link with webhook log if update was successful
            if (syncResult.successfulUpdates > 0 && syncResult.processedNotifications.length > 0) {
                try {
                    // Get the notification details to link with webhook
                    const notificationId = syncResult.processedNotifications[0];
                    await this.linkNotificationWithWebhook(notificationId, webhookId, statusUpdate.timestamp);
                } catch (linkError) {
                    console.warn(`⚠️  Failed to link notification with webhook log:`, linkError);
                    // Don't fail the entire sync for linking errors
                }
            }

            return syncResult;

        } catch (error) {
            console.error(`❌ Error processing status update for message ${statusUpdate.id}:`, error);
            throw error;
        }
    }

    /**
     * Helper method to map WhatsApp status to NotificationStatus
     */
    private mapWhatsAppStatusToNotificationStatus(whatsappStatus: string): NotificationStatus {
        switch (whatsappStatus) {
            case 'sent':
                return NotificationStatus.SENT;
            case 'delivered':
                return NotificationStatus.DELIVERED;
            case 'read':
                return NotificationStatus.READ;
            case 'failed':
                return NotificationStatus.FAILED;
            default:
                console.warn(`⚠️  Unknown WhatsApp status: ${whatsappStatus}, defaulting to FAILED`);
                return NotificationStatus.FAILED;
        }
    }

    /**
     * Link notification with webhook log for cross-reference
     */
    private async linkNotificationWithWebhook(
        notificationId: string,
        webhookId: string,
        timestampStr: string
    ): Promise<void> {
        try {
            const timestamp = parseInt(timestampStr) * 1000; // Convert to milliseconds
            await this.notificationLogService.linkWithWebhookLog(notificationId, timestamp, webhookId);
        } catch (error) {
            console.error(`❌ Error linking notification ${notificationId} with webhook ${webhookId}:`, error);
            throw error;
        }
    }

    /**
     * Health check for the sync service
     */
    async healthCheck(): Promise<{
        status: 'healthy' | 'degraded' | 'unhealthy';
        services: {
            notificationLog: boolean;
            webhookLog: boolean;
        };
        lastSyncTime?: number;
        pendingRetries?: number;
    }> {
        try {
            const services = {
                notificationLog: true, // Assume healthy if no errors
                webhookLog: true
            };

            // Test basic service connectivity
            try {
                await this.notificationLogService.getBusinessNotificationLogs('health-check', Date.now() - 1000, Date.now());
            } catch {
                services.notificationLog = false;
            }

            const allHealthy = Object.values(services).every(healthy => healthy);
            const status = allHealthy ? 'healthy' : 'degraded';

            return {
                status,
                services,
                lastSyncTime: Date.now(),
                pendingRetries: 0
            };

        } catch (error) {
            console.error('❌ Health check failed:', error);
            return {
                status: 'unhealthy',
                services: {
                    notificationLog: false,
                    webhookLog: false
                }
            };
        }
    }
} 