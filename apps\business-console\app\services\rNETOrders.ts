import { ApiResponse } from "~/types/api/Api";
import { rNETOrder } from "~/types/api/businessConsoleService/rNETOrder";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getrNETOrders(
  request?: Request,
  queryParams?: string,
  role?: string
): Promise<ApiResponse<{ orders: rNETOrder[], totalElements: number, pageSize: number, currentPage: number }>> {

  const response = await apiRequest<{ orders: rNETOrder[], totalElements: number, pageSize: number, currentPage: number }>(
    `${API_BASE_URL}/bc/${role ? role : "mnetadmin"}/orders${queryParams ? '?' + queryParams.toString() : ''}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to fetch orders.");
  }
}

export async function cancelrNETOrder(
  request: Request,
  tripId: number,
  orderGroupId: number
): Promise<ApiResponse<rNETOrder>> {

  const response = await apiRequest<rNETOrder>(
    `${API_BASE_URL}/bc/mnetadmin/trips/${tripId}/orders/${orderGroupId}`,
    "DELETE",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to cancel order.");
  }
}

export async function updateLiveOrderStatus(
  request: Request,
  orderGroupId: number,
  status: string,
  queryParams?: string
): Promise<ApiResponse<rNETOrder>> {

  const response = await apiRequest<rNETOrder>(
    `${API_BASE_URL}/bc/mnetadmin/orders/${orderGroupId}/status/${status}${queryParams ? '?' + queryParams.toString() : ''}`,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to update order status.");
  }
}

export async function markDelivered(
  request: Request,
  tripId: number,
  orderGroupId: number,
  queryParams?: string
): Promise<ApiResponse<rNETOrder>> {

  const response = await apiRequest<rNETOrder>(
    `${API_BASE_URL}/bc/mnetadmin/trips/${tripId}/orders/${orderGroupId}?deliveryCode=0000&creditAmount=0&boxesGiven=0&boxesTaken=0`,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to mark order as delivered.");
  }
}

export async function updateLogisticProvider(
  request: Request,
  orderGroupId: number,
  logisticProvider: string
): Promise<ApiResponse<rNETOrder>> {

  const response = await apiRequest<rNETOrder>(
    `${API_BASE_URL}/bc/mnetadmin/orders/orderGroup/${orderGroupId}/update-handling-provider?handlingProvider=${logisticProvider}`,
    "POST",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to update logistic provider.");
  }
}

export async function updateLogisticDetails(
  request: Request,
  orderGroupId: number,
  logisticDetails: any
): Promise<ApiResponse<rNETOrder>> {

  const response = await apiRequest<rNETOrder>(
    `${API_BASE_URL}/bc/mnetadmin/orders/orderGroup/${orderGroupId}/update-logistic-details`,
    "POST",
    logisticDetails,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to update logistic details.");
  }
}
