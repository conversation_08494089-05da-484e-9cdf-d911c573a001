import { ApiResponse } from "~/types/api/Api";
import { CreateStockTransactionInput, InvStockTransaction } from "~/types/api/businessConsoleService/ItemStock";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getStockTransactions(
      isSeller?:boolean,
       stockId?: number,
       sellerId?: number,
      deliveryDate?:string,
      request?: Request
): Promise<ApiResponse<InvStockTransaction[]>> {
          if(!stockId){
                if (!stockId || typeof stockId !== "number" || stockId <= 0) {
                      throw new Response("Invalid sellerId provided");
                    }
                  }
      try {

        
            const url = isSeller ? `${API_BASE_URL}/bc/seller/stock/${stockId}/transactions/date/${deliveryDate}` : `${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/stock/${stockId}/transactions/date/${deliveryDate}`;
        const response = await apiRequest<InvStockTransaction[]>(
          url,
          "GET",
          undefined,
          {},
          true,
          request
        );
    
        if (response) {
          return response;
        } else {
              throw new Response("Failed to fetch MyStocks");
        }
      } catch (error: any) {
        // Log error or handle it accordingly
        throw new Error(`Error in MyStocks: ${error.message}`);
      }
    }

    export interface ActionData {
      error?: string;
      success?: boolean;
      message?: string;
    }

export async function createStockTransactions(
      isSeller?:boolean,
       stockId?: number,
       sellerId?: number,
       stockCreationbody?: CreateStockTransactionInput,
      request?: Request
): Promise<ApiResponse<InvStockTransaction[]>> {
          if(!stockId){
                if (!stockId || typeof stockId !== "number" || stockId <= 0) {
                      throw new Response("Invalid sellerId provided");
                    }
                  }
      try {
   
            const url = isSeller ? `${API_BASE_URL}/bc/seller/stock/${stockId}/transaction` : `${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/stock/${stockId}/transaction`;
        const response = await apiRequest<InvStockTransaction[]>(
          url,
          "POST",
          stockCreationbody,
          {},
          true,
          request
        );
    
        if (response) {
          return response;
        } else {
              throw new Response("Failed to fetch MyStocks");
        }
      } catch (error: any) {
        // Log error or handle it accordingly
        throw new Response("Failed to create stock transactions: " + error);
      }
    } 




