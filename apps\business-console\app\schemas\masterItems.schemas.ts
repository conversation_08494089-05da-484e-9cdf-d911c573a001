import { z } from "zod";
import type { FormState } from "~/types/home/<USER>";

// Step 1 validation schema
export const stepOneSchema = z.object({
  ondcDomain: z.enum(["RET11", "RET10"]),
  itemName: z.string().nonempty("Item name is required"),
  brandName: z.string().optional(),
  groupId: z.string().optional(),
});

// Step 2 validation schema
export const stepTwoSchema = z.object({
  ondcDomain: z.enum(["RET11", "RET10"]),
  images: z.array(z.object({
    id: z.number(),
    url: z.string().nonempty("Image URL is required"),
    sequence: z.number(),
    isDefault: z.boolean()
  })).optional(),
  translations: z.object({
    kanadaName: z.string().optional(),
    hindiName: z.string().optional(),
    tamilName: z.string().optional(),
    teluguName: z.string().optional(),
    bengaliName: z.string().optional(),
    malyalumName: z.string().optional(),
    marathiName: z.string().optional(),
    gujaratiName: z.string().optional(),
    assamiName: z.string().optional(),
  }),
  searchTags: z.array(z.string()).min(1, "At least one search tag is required"),
  assignedCategories: z.array(z.number()).min(1, "At least one category must be assigned")
}).superRefine((data, ctx) => {
  if (data.ondcDomain === "RET10" && (!data.images || data.images.length === 0)) {
    ctx.addIssue({
      path: ["images"],
      code: z.ZodIssueCode.custom,
      message: "At least one image is required for RET10",
    });
  }
});


// Step 3 validation schema
export const stepThreeSchema = z.object({
  itemConfig: z.object({
    type: z.enum(["B2B", "B2C"]),
    unit: z.string().nonempty("Unit is required"),
    minimumOrderQty: z.number().nonnegative("Minimum order quantity must be at least 0"),
    incrementOrderQty: z.number().nonnegative("Increment order quantity must be at least 0"),
    weightFactor: z.number().min(0.001, "Weight factor must be at least 0.001"),
    packaging: z.string().optional(),
    mrpPerUnit: z.number().min(0.0001, "MRP per unit must be at least 0.0001"),
    maximumOrderQty: z.number().nonnegative("Maximum order quantity must be at least 0"),
    maxAvailableQty: z.number().nonnegative("Maximum available quantity must be at least 0"),
    productId: z.string().optional(),
    originalProductId: z.string(),
    isDefaultVariant: z.boolean(),
    sequencePriority: z.string(),
    gstEligible: z.enum(["yes", "no"]),
    gstHsnCode: z.string().optional(),
    gstRate: z.number().min(0, "GST rate must be at least 0").optional(),
    disabled: z.boolean(),
    ondcDomain: z.enum(["RET11", "RET10"]),
    taxExempt: z.boolean(),
    description: z.string().optional(),
    diet: z.string().optional(),
  }),
}).superRefine((data, ctx) => {
  const domain = data.itemConfig.ondcDomain;

  if (domain === "RET11") {
    const { description, diet } = data.itemConfig;

    if (description && description.length < 25) {
      ctx.addIssue({
        path: ["itemConfig", "description"],
        code: z.ZodIssueCode.custom,
        message: "Description must be at least 25 characters",
      });
    } else if (description && description.length > 1024) {
      ctx.addIssue({
        path: ["itemConfig", "description"],
        code: z.ZodIssueCode.custom,
        message: "Description must be at most 1024 characters",
      });
    }

    if (!diet || diet.trim() === "") {
      ctx.addIssue({
        path: ["itemConfig", "diet"],
        code: z.ZodIssueCode.custom,
        message: "Dietary is required",
      });
    }
  }
});

// Full item schema for API validation
export const itemSchema = z.object({
  id: z.number().optional(),
  ondcDomain: z.enum(["RET11", "RET10"]),
  defaultUnit: z.string().nonempty("Default unit is required"),
  name: z.string().nonempty("Name is required"),
  picture: z.string().optional(),
  nameInKannada: z.string().optional(),
  nameInTelugu: z.string().optional(),
  nameInTamil: z.string().optional(),
  nameInMalayalam: z.string().optional(),
  nameInHindi: z.string().optional(),
  nameInAssame: z.string().optional(),
  nameInGujarati: z.string().optional(),
  nameInMarathi: z.string().optional(),
  nameInBangla: z.string().optional(),
  defaultWeightFactor: z.number().optional(),
  gstHsnCode: z.string().optional(),
  gstRate: z.number().optional(),
  source: z.string().nonempty("Source is required"),
  sourceKey: z.string().nonempty("Source key is required"),
  productId: z.string().optional(),
  brandName: z.string().optional(),
  packaging: z.string().optional(),
  mrp: z.number().min(1, "MRP must be at least 1"),
  b2b: z.boolean(),
  b2c: z.boolean(),
  groupId: z.string().optional(),
  groupSeq: z.number().optional(),
  searchTag: z.string().optional(),
  categories: z
    .array(
      z.object({
        id: z.number(),
        name: z.string().optional(),
        picture: z.string().optional(),
        picturex: z.string().optional(),
        picturexx: z.string().optional(),
        level: z.number().optional(),
        totalItems: z.number().optional(),
        parentCategories: z.array(z.string()).optional(),
      })
    )
    .optional(),
});

// Full item schema for API validation
export const editItemSchema = itemSchema.extend({
  defaultUnit: z.string().optional(),
  name: z.string().optional(),
  picture: z.string().optional(),
  mrp: z.number().optional(),
  b2b: z.boolean().optional(),
  b2c: z.boolean().optional(),
  source: z.string().optional(),
  sourceKey: z.string().optional(),
});

export const getInitialFormState = (): FormState => ({
  itemName: "",
  images: [],
  translations: {
    kanadaName: "",
    hindiName: "",
    tamilName: "",
    teluguName: "",
    bengaliName: "",
    malyalumName: "",
    marathiName: "",
    gujaratiName: "",
    assamiName: "",
  },
  searchTags: [],
  assignedCategories: [],
  itemConfig: {
    type: "B2B" as const,
    unit: "kg",
    minimumOrderQty: 0,
    incrementOrderQty: 1,
    weightFactor: 1,
    packaging: undefined,
    mrpPerUnit: 0,
    maximumOrderQty: 10,
    maxAvailableQty: 100,
    productId: undefined,
    originalProductId: "",
    isDefaultVariant: false,
    sequencePriority: "",
    gstEligible: "no" as const,
    gstHsnCode: undefined,
    gstRate: undefined,
    disabled: false,
    ondcDomain: "RET10",
    taxExempt: false
  },
  // Optional fields with empty defaults
  brandName: undefined,
  defaultUnit: "kg",
  name: "",
  picture: "",
  mrp: 0,
  b2b: false,
  b2c: false,
  groupId: undefined,
  groupSeq: undefined,
  searchTag: "",
});

export const getInitialRestaurantFormState = (): FormState => ({
  itemName: "",
  images: [],
  translations: {
    kanadaName: "",
    hindiName: "",
    tamilName: "",
    teluguName: "",
    bengaliName: "",
    malyalumName: "",
    marathiName: "",
    gujaratiName: "",
    assamiName: "",
  },
  searchTags: [],
  assignedCategories: [],
  itemConfig: {
    type: "B2C",
    unit: "unit",
    minimumOrderQty: 1,
    incrementOrderQty: 1,
    weightFactor: 1,
    packaging: undefined,
    mrpPerUnit: 0,
    maximumOrderQty: 100,
    maxAvailableQty: 1000,
    productId: undefined,
    originalProductId: "",
    isDefaultVariant: false,
    sequencePriority: "",
    gstEligible: "no" as const,
    gstHsnCode: undefined,
    gstRate: undefined,
    disabled: false,
    ondcDomain: "RET11",
    taxExempt: false,
    description: "",
    diet: "",
  },
  // Optional fields with empty defaults
  brandName: undefined,
  defaultUnit: "unit",
  name: "",
  picture: "",
  mrp: 0,
  b2b: false,
  b2c: false,
  groupId: undefined,
  groupSeq: undefined,
  searchTag: "",
});