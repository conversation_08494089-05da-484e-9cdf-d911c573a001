import React from "react";

interface PaginationProps {
      totalPages: number;
      currentPage: number;
      onPageChange: (page: number) => void;
      maxVisiblePages?: number;
}

const ResponsivePagination: React.FC<PaginationProps> = ({
      totalPages,
      currentPage,
      onPageChange,
      maxVisiblePages = 5,
}) => {
      const getPageNumbers = () => {
            const pages: Array<number | "ellipsis"> = [];
            const halfVisible = Math.floor(maxVisiblePages / 2);

            let startPage = Math.max(0, currentPage - halfVisible);
            let endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                  startPage = Math.max(0, endPage - maxVisiblePages + 1);
            }

            if (startPage > 0) {
                  pages.push(0);
                  if (startPage > 1) pages.push("ellipsis");
            }

            for (let i = startPage; i <= endPage; i++) {
                  pages.push(i);
            }

            if (endPage < totalPages - 1) {
                  if (endPage < totalPages - 2) pages.push("ellipsis");
                  pages.push(totalPages - 1);
            }

            return pages;
      };

      return (
            <div className="flex items-center justify-end space-x-2 py-4 pr-5">
                  <button
                        className="px-3 py-1 border rounded disabled:opacity-50"
                        onClick={() => onPageChange(currentPage - 1)}
                        disabled={currentPage === 0}
                  >
                        Prev
                  </button>

                  {getPageNumbers().map((page, index) =>
                        page === "ellipsis" ? (
                              <span key={`ellipsis-${index}`} className="px-3">...</span>
                        ) : (
                              <button
                                    key={page}
                                    className={`px-3 py-1 border rounded ${currentPage === page ? "bg-blue-500 text-white" : ""
                                          }`}
                                    onClick={() => onPageChange(page)}
                              >
                                    {page + 1}
                              </button>
                        )
                  )}

                  <button
                        className="px-3 py-1 border rounded disabled:opacity-50"
                        onClick={() => onPageChange(currentPage + 1)}
                        disabled={currentPage === totalPages - 1}
                  >
                        Next
                  </button>
            </div>
      );
};

export default ResponsivePagination;
