import * as React from "react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { useFetcher } from "@remix-run/react";

interface Category {
      id: number;
      name: string;
      picture?: string;
      picturex?: string;
      picturexx?: string;
      level?: number;
      totalItems?: number;
      parentCategories?: Array<{
            id: number;
            name: string;
            picture?: string;
            picturex?: string;
            picturexx?: string;
            level?: number;
            parentCategories?: number[];
            totalItems?: number;
            myItems?: number;
      }>;
}

export interface CategoryItem {
      value: string;
      label: string;
      numericId: number;
}

interface SearchableCategoriesProps {
      label?: string;
      apiUrl: string;
      selectedCategories: CategoryItem[];
      onCategoryAdd: (categoryId: number, categoryName: string) => void;
      onCategoryRemove: (categoryId: number) => void;
      required?: boolean;
      error?: string;
      level?: number
}

export function SearchableCategories({
      label = "Categories",
      apiUrl,
      selectedCategories,
      onCategoryAdd,
      onCategoryRemove,
      required = false,
      error,
      level
}: SearchableCategoriesProps) {
      // State for search input
      const [categoryInput, setCategoryInput] = React.useState("");

      // Category state
      const [categoryItems, setCategoryItems] = React.useState<CategoryItem[]>([]);
      const [loadingCategories, setLoadingCategories] = React.useState(false);
      const [categoryPage, setCategoryPage] = React.useState(1);
      const [hasMoreCategories, setHasMoreCategories] = React.useState(true);
      const fetcher = useFetcher<{ categories: Category[]; categoryPage: number }>();

      // Search categories with server API
      const searchCategories = async (query: string) => {
            setLoadingCategories(true);
            fetcher.load(`${apiUrl}?searchCategories=${query}&categoryPage=0&level=${level}`);
      };

      // Load more categories for infinite scroll
      const loadMoreCategories = async () => {
            if (!hasMoreCategories || loadingCategories) return;
            const nextPage = categoryPage + 1;
            fetcher.load(`${apiUrl}?searchCategories=${categoryInput}&categoryPage=${nextPage}&level=${level}`);
      };

      // Handle fetcher data updates
      React.useEffect(() => {
            if (fetcher.data?.categories) {
                  const items: CategoryItem[] = fetcher.data.categories.map(category => ({
                        value: String(category.id),
                        label: category.name,
                        numericId: category.id
                  }));

                  if (fetcher.data.categoryPage === 0) {
                        setCategoryItems(items);
                  } else {
                        setCategoryItems(prev => [...prev, ...items]);
                  }

                  setCategoryPage(fetcher.data.categoryPage);
                  setHasMoreCategories(items.length === 20);
                  setLoadingCategories(false);
            }
      }, [fetcher.data]);

      // Handle category functions
      const handleAddCategory = (categoryValue: string) => {
            const categoryItem = categoryItems.find(item => item.value === categoryValue);
            if (!categoryItem) return;

            const categoryId = categoryItem.numericId;
            if (!selectedCategories.some(cat => cat.numericId === categoryId)) {
                  onCategoryAdd(categoryId, categoryItem.label);
            }
            setCategoryInput("");
      };

      // Get category name by ID
      const getCategoryName = (categoryId: number) => {
            // First check in the current categoryItems (new categories)
            const newCategory = categoryItems.find(item => item.numericId === categoryId);
            if (newCategory) return newCategory.label;

            // Then check in the selectedCategories
            const selectedCategory = selectedCategories.find(item => item.numericId === categoryId);
            if (selectedCategory) return selectedCategory.label;

            // If no name found, return the ID
            return String(categoryId);
      };

      return (
            <div className="w-full">
                  <Label>{label} {required && "(Required)"}</Label>
                  <div className="flex gap-2 items-center">
                        <Input
                              placeholder="Search categories"
                              value={categoryInput}
                              onChange={(e) => {
                                    setCategoryInput(e.target.value);
                                    searchCategories(e.target.value);
                              }}
                        />

                  </div>
                  {required && selectedCategories.length < 1 && (
                        <p className="text-red-500">At least one category is required.</p>
                  )}
                  <div className="flex gap-2 mt-2 flex-wrap">
                        {selectedCategories.map((category) => {
                              const categoryName = getCategoryName(category.numericId);
                              return (
                                    <div key={category.numericId} className="inline-flex items-center space-x-1 bg-gray-200 p-1 rounded">
                                          <span>{categoryName}</span>
                                          <button
                                                onClick={() => onCategoryRemove(category.numericId)}
                                                className="hover:text-red-500"
                                          >
                                                ×
                                          </button>
                                    </div>
                              );
                        })}
                  </div>
                  {error && (
                        <p className="text-red-500">{error}</p>
                  )}
                  {loadingCategories && <p>Loading categories...</p>}
                  {categoryItems.length > 0 && categoryInput && (
                        <div className="mt-2 border rounded-md p-2">
                              {categoryItems.map(category => {
                                    // Don't show already assigned categories
                                    if (selectedCategories.some(cat => cat.numericId === category.numericId)) {
                                          return null;
                                    }
                                    return (
                                          <button
                                                key={category.value}
                                                className="w-full text-left cursor-pointer hover:bg-gray-100 p-1"
                                                onClick={() => handleAddCategory(category.value)}
                                                onKeyDown={(e) => {
                                                      if (e.key === 'Enter' || e.key === ' ') {
                                                            handleAddCategory(category.value);
                                                      }
                                                }}
                                          >
                                                {category.label}
                                          </button>
                                    );
                              })}
                              {hasMoreCategories && (
                                    <Button
                                          type="button"
                                          variant="ghost"
                                          className="w-full mt-2"
                                          onClick={loadMoreCategories}
                                    >
                                          Load More
                                    </Button>
                              )}
                        </div>
                  )}
            </div>
      );
} 