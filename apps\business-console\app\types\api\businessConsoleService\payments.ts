export interface Transaction {
  id: number;
  initiatedTime: string;
  completedTime: string;
  lastUpdatedTime: string;
  status: string;
  businessId: number;
  businessName: string;
  amount: number;
  note: string;
  correctionNote: string;
  orderGroupId: number;
  channel: string;
  bankRefId: string;
  bankRRN: string;
  bankTxnStatus: string;
  bankTxnInitTime: string;
  bankTxnCompleteTime: string;
  payerName: string;
  payerMobile: string;
  payerVA: string;
  payerAmount: number;
  buyerName: string;
  buyerMobile: string;
  buyerId: number;
  buyerApp: number;
  sellerName: string;
  appSellerId: number;
  orderStatus: string;
  deliveryDate: string;
  codAmount: number;
}
