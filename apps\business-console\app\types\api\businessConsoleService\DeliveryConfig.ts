
export enum ConfigType{
  PERCENTAGE_BASED="PERCENTAGE_BASED",
  ORDER_VALUE_BASED="ORDER_VALUE_BASED"
}
export interface DcBody{
  sellerId:number,
  configType:ConfigType,
  buyerPercentage:number,
  sellerPercentage:number,
  minOrderValue:number,
  maxOrderValue:number,
  maxBuyerDeliveryCharge:number,
  maxSellerDeliveryCharge:number,
  active:boolean
}
export interface dclistingResponse{
  
    id:number,
    sellerId:number,
    configType:ConfigType,
    buyerPercentage:number,
    sellerPercentage:number,
    minOrderValue:number,
    maxOrderValue:number,
    maxBuyerDeliveryCharge:number,
    maxSellerDeliveryCharge:number,
    active:boolean,
    createdAt: string,
    updatedAt:string,
    version: number
  
}
export interface DcCong{
  success:boolean,
  data:dclistingResponse[],
  error: {
    code: string,
    message: string
  }
}
export interface DcCreateRes{
  success:boolean,
  data:dclistingResponse,
  error: {
    code: string,
    message: string
  }
}





