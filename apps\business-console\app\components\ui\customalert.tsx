import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "./alert-dialog";


interface Props {
      title: string,
      message: string,
      onClose: () => void,
      onContinue: () => void

}



export default function Customalert({ title, message, onClose, onContinue }: Props) {
      return (
            <AlertDialog>
                  <AlertDialogContent>
                        <AlertDialogHeader>
                              <AlertDialogTitle>{title}</AlertDialogTitle>
                              <AlertDialogDescription>
                                    {message}
                              </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                              <AlertDialogCancel onClick={() => onClose()}>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => onContinue()}>Continue</AlertDialogAction>
                        </AlertDialogFooter>
                  </AlertDialogContent>
            </AlertDialog>
      )



}