// File: app/services/whatsappApi.server.ts
import type { ApiResponse } from "~/types/api/Api";
import type {
    DebugTokenResponse,
    PhoneNumber, TemplatesResponse,
    WhatsAppBusinessAccount,
    WhatsAppPhoneNumber, WhatsAppSubscribedApp, WhatsAppTemplate, WhatsAppBusinessProfile,
    BusinessProfileResponse
} from "~/types/whatsapp";
import {REQUIRED_TEMPLATES} from "~/constants/whatsappTemplates";

const FACEBOOK_API_VERSION = 'v21.0';
const FACEBOOK_GRAPH_API = `https://graph.facebook.com/${FACEBOOK_API_VERSION}`;

/**
 * Logger utility for API requests and responses
 */
interface LogDetails {
  url: string;
  method?: string;
  headers?: Record<string, string>;
  body?: unknown;
  status?: number;
  responseData?: unknown;
  error?: string;
  timestamp: string;
}

function logApiCall(type: 'REQUEST' | 'RESPONSE' | 'ERROR', details: LogDetails) {
  const timestamp = new Date().toISOString();
  
  // In production, you might want to use a proper logging service
//   if (process.env.NODE_ENV === 'development') {
    console.log(`[WhatsApp API][${type}][${timestamp}]`, {
      ...details,
      timestamp,
    });
//   }
  
  // For production, consider using a structured logging service
  // or sending logs to a monitoring system
}

export async function getWhatsAppBusinessAccounts(
    accessToken: string
): Promise<ApiResponse<WhatsAppBusinessAccount[]>> {
    const debugUrl = `${FACEBOOK_GRAPH_API}/debug_token?input_token=${accessToken}&access_token=${process.env.WHATSAPP_TOKEN}`;
    
    try {
        // Log request
        logApiCall('REQUEST', {
          url: debugUrl,
          method: 'GET',
          timestamp: new Date().toISOString()
        });
        
        // First, get all business IDs from debug_token
        const debugResponse = await fetch(debugUrl);
        
        const debugResponseStatus = debugResponse.status;
        const debugData: DebugTokenResponse = await debugResponse.json();
        
        // Log response
        logApiCall('RESPONSE', {
          url: debugUrl,
          method: 'GET',
          status: debugResponseStatus,
          responseData: debugData,
          timestamp: new Date().toISOString()
        });

        if (!debugResponse.ok) {
            throw new Error(`Debug token HTTP error! status: ${debugResponse.status}`);
        }

        if (!debugData.data.is_valid) {
            throw new Error('Invalid access token');
        }

        // Get all WhatsApp business account IDs from granular scopes
        const businessIds = debugData.data.granular_scopes
            .filter(scope => scope.scope === 'whatsapp_business_management')
            .flatMap(scope => scope.target_ids || []);

        if (businessIds.length === 0) {
            throw new Error('No WhatsApp Business accounts found');
        }

        // Fetch details for each business account
        const businessAccounts = await Promise.all(
            businessIds.map(async (id) => {
                const businessUrl = `${FACEBOOK_GRAPH_API}/${id}?access_token=${accessToken}`;
                
                // Log request
                logApiCall('REQUEST', {
                  url: businessUrl,
                  method: 'GET',
                  timestamp: new Date().toISOString()
                });
                
                const response = await fetch(businessUrl);
                const responseStatus = response.status;
                const responseData = await response.json();
                
                // Log response
                logApiCall('RESPONSE', {
                  url: businessUrl,
                  method: 'GET',
                  status: responseStatus,
                  responseData,
                  timestamp: new Date().toISOString()
                });

                if (!response.ok) {
                    throw new Error(`Business fetch HTTP error! status: ${response.status}`);
                }

                return responseData;
            })
        );

        return {
            data: businessAccounts,
            statusCode: 200
        };
    } catch (error) {
        // Log error
        logApiCall('ERROR', {
          url: debugUrl,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        return {
            error: error instanceof Error ? error.message : String(error),
            statusCode: 500,
            timestamp: new Date().toISOString()
        };
    }
}

export async function getWhatsAppPhoneNumbers(
    accessToken: string,
    businessAccountId?: string
): Promise<ApiResponse<WhatsAppPhoneNumber[]>> {
    // If no business account ID provided, use the default one from environment
    const accountId = businessAccountId || process.env.WHATSAPP_BUSINESS_ACCOUNT_ID;
    const url = `${FACEBOOK_GRAPH_API}/${accountId}/phone_numbers?access_token=${accessToken}`;
    
    try {
        // Log request
        logApiCall('REQUEST', {
          url,
          method: 'GET',
          timestamp: new Date().toISOString()
        });

        const response = await fetch(url);
        const responseStatus = response.status;
        const data = await response.json();
        
        // Log response
        logApiCall('RESPONSE', {
          url,
          method: 'GET',
          status: responseStatus,
          responseData: data,
          timestamp: new Date().toISOString()
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return { 
            data: data.data,
            statusCode: 200
        };
    } catch (error) {
        // Log error
        logApiCall('ERROR', {
          url,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        return {
            error: error instanceof Error ? error.message : String(error),
            statusCode: 500,
            timestamp: new Date().toISOString()
        };
    }
}

export async function getBusinessPhoneNumbers(accessToken: string, businessId: string) {
    const url = `https://graph.facebook.com/v21.0/${businessId}/phone_numbers?access_token=${accessToken}`;
    
    try {
        // Log request
        logApiCall('REQUEST', {
          url,
          method: 'GET',
          timestamp: new Date().toISOString()
        });
        
        const response = await fetch(url);
        const responseStatus = response.status;
        const data = await response.json();
        
        // Log response
        logApiCall('RESPONSE', {
          url,
          method: 'GET',
          status: responseStatus,
          responseData: data,
          timestamp: new Date().toISOString()
        });
        
        return data.data as PhoneNumber[];
    } catch (error) {
        // Log error
        logApiCall('ERROR', {
          url,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        throw error;
    }
}

export async function getSubscribedApps(
    accessToken: string,
    businessId: string
): Promise<ApiResponse<WhatsAppSubscribedApp[]>> {
    const url = `${FACEBOOK_GRAPH_API}/${businessId}/subscribed_apps?access_token=${accessToken}`;
    
    try {
        // Log request
        logApiCall('REQUEST', {
          url,
          method: 'GET',
          timestamp: new Date().toISOString()
        });
        
        const response = await fetch(url);
        const responseStatus = response.status;
        const data = await response.json();
        
        // Log response
        logApiCall('RESPONSE', {
          url,
          method: 'GET',
          status: responseStatus,
          responseData: data,
          timestamp: new Date().toISOString()
        });


        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return { 
            data: data.data,
            statusCode: 200
        };
    } catch (error) {
        // Log error
        logApiCall('ERROR', {
          url,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        console.error(error);
        return {
            error: error instanceof Error ? error.message : String(error),
            statusCode: 500,
            timestamp: new Date().toISOString()
        };
    }
}

export async function subscribeApp(
    accessToken: string,
    businessId: string
): Promise<ApiResponse<{ success: boolean }>> {
    const url = `${FACEBOOK_GRAPH_API}/${businessId}/subscribed_apps`;
    const headers = {
        'Authorization': `Bearer ${accessToken}`,
    };
    
    try {
        // Log request
        logApiCall('REQUEST', {
          url,
          method: 'POST',
          headers,
          timestamp: new Date().toISOString()
        });
        
        const response = await fetch(
            url,
            {
                method: 'POST',
                headers
            }
        );
        
        const responseStatus = response.status;
        const data = await response.json();
        
        // Log response
        logApiCall('RESPONSE', {
          url,
          method: 'POST',
          status: responseStatus,
          responseData: data,
          timestamp: new Date().toISOString()
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return { 
            data,
            statusCode: 200
        };
    } catch (error) {
        // Log error
        logApiCall('ERROR', {
          url,
          method: 'POST',
          headers,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        return {
            error: error instanceof Error ? error.message : String(error),
            statusCode: 500,
            timestamp: new Date().toISOString()
        };
    }
}

export async function unsubscribeApp(
    accessToken: string,
    businessId: string
): Promise<ApiResponse<{ success: boolean }>> {
    const url = `${FACEBOOK_GRAPH_API}/${businessId}/subscribed_apps?access_token=${accessToken}`;
    
    try {
        // Log request
        logApiCall('REQUEST', {
          url,
          method: 'DELETE',
          timestamp: new Date().toISOString()
        });
        
        const response = await fetch(
            url,
            {
                method: 'DELETE'
            }
        );
        
        const responseStatus = response.status;
        const data = await response.json();
        
        // Log response
        logApiCall('RESPONSE', {
          url,
          method: 'DELETE',
          status: responseStatus,
          responseData: data,
          timestamp: new Date().toISOString()
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return { 
            data,
            statusCode: 200
        };
    } catch (error) {
        // Log error
        logApiCall('ERROR', {
          url,
          method: 'DELETE',
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        return {
            error: error instanceof Error ? error.message : String(error),
            statusCode: 500,
            timestamp: new Date().toISOString()
        };
    }
}

export async function getWhatsAppTemplates(
    accessToken: string,
    businessId: string
): Promise<ApiResponse<WhatsAppTemplate[]>> {
    const url = `${FACEBOOK_GRAPH_API}/${businessId}/message_templates?limit=200`;
    const headers = {
        Authorization: `Bearer ${accessToken}`,
    };
    
    try {
        // Log request
        logApiCall('REQUEST', {
          url,
          method: 'GET',
          headers,
          timestamp: new Date().toISOString()
        });
        
        const response = await fetch(
            url,
            {
                headers
            }
        );
        
        const responseStatus = response.status;
        const data: TemplatesResponse = await response.json();
        
        // Log response
        logApiCall('RESPONSE', {
          url,
          method: 'GET',
          status: responseStatus,
          responseData: data,
          timestamp: new Date().toISOString()
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return {
            data: data.data,
            timestamp: new Date().toISOString(),
            statusCode: 200
        };
    } catch (error) {
        // Log error
        logApiCall('ERROR', {
          url,
          method: 'GET',
          headers,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        return {
            error: error instanceof Error ? error.message : String(error),
            statusCode: 500,
            timestamp: new Date().toISOString()
        };
    }
}

export async function registerTemplate(
    accessToken: string,
    businessId: string,
    template: typeof REQUIRED_TEMPLATES[0]
): Promise<ApiResponse<{ id: string; status: string; category: string }>> {
    const url = `${FACEBOOK_GRAPH_API}/${businessId}/message_templates`;
    const headers = {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
    };
    
    try {
        // Log request
        logApiCall('REQUEST', {
          url,
          method: 'POST',
          headers,
          body: template,
          timestamp: new Date().toISOString()
        });
        
        const response = await fetch(
            url,
            {
                method: 'POST',
                headers,
                body: JSON.stringify(template)
            }
        );
        
        const responseStatus = response.status;
        const data = await response.json();
        
        // Log response
        logApiCall('RESPONSE', {
          url,
          method: 'POST',
          status: responseStatus,
          responseData: data,
          timestamp: new Date().toISOString()
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        console.log(data);
        return { 
            data,
            statusCode: 200
        };
    } catch (error) {
        // Log error
        logApiCall('ERROR', {
          url,
          method: 'POST',
          headers,
          body: template,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        
        return {
            error: error instanceof Error ? error.message : String(error),
            statusCode: 500,
            timestamp: new Date().toISOString()
        };
    }
}

/**
 * Get WhatsApp Business Profile
 * @param accessToken - The access token for authorization
 * @param phoneNumberId - The WhatsApp phone number ID
 * @returns ApiResponse containing the business profile data
 */
export async function getWhatsAppBusinessProfile(
    accessToken: string,
    phoneNumberId: string
): Promise<ApiResponse<BusinessProfileResponse>> {
    const url = `${FACEBOOK_GRAPH_API}/${phoneNumberId}/whatsapp_business_profile?fields=about,address,description,email,messaging_product,vertical,websites,profile_picture_url`;
    const headers = {
        'Authorization': `Bearer ${accessToken}`,
    };
    
    try {
        // Log request
        logApiCall('REQUEST', {
          url,
          method: 'GET',
          headers,
          timestamp: new Date().toISOString()
        });
        
        const response = await fetch(url, { headers });
        
        const responseStatus = response.status;
        const data = await response.json();
        
        // Log response
        logApiCall('RESPONSE', {
          url,
          method: 'GET',
          status: responseStatus,
          responseData: data,
          timestamp: new Date().toISOString()
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return {
            data: data,
            statusCode: 200
        };
    } catch (error) {
        // Log error
        logApiCall('ERROR', {
          url,
          method: 'GET',
          headers,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        return {
            error: error instanceof Error ? error.message : String(error),
            statusCode: 500,
            timestamp: new Date().toISOString()
        };
    }
}

/**
 * Update WhatsApp Business Profile
 * @param accessToken - The access token for authorization
 * @param phoneNumberId - The WhatsApp phone number ID
 * @param profileData - Business profile data to update
 * @returns ApiResponse containing the update status
 */
export async function updateWhatsAppBusinessProfile(
    accessToken: string,
    phoneNumberId: string,
    profileData: Partial<WhatsAppBusinessProfile>
): Promise<ApiResponse<{ success: boolean }>> {
    const url = `${FACEBOOK_GRAPH_API}/${phoneNumberId}/whatsapp_business_profile`;
    const headers = {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
    };
    
    // Ensure messaging_product is set to "whatsapp"
    const payload = {
        ...profileData,
        messaging_product: 'whatsapp'
    };
    
    try {
        // Log request
        logApiCall('REQUEST', {
          url,
          method: 'POST',
          headers,
          body: payload,
          timestamp: new Date().toISOString()
        });
        
        const response = await fetch(
            url,
            {
                method: 'POST',
                headers,
                body: JSON.stringify(payload)
            }
        );
        
        const responseStatus = response.status;
        const data = await response.json();
        
        // Log response
        logApiCall('RESPONSE', {
          url,
          method: 'POST',
          status: responseStatus,
          responseData: data,
          timestamp: new Date().toISOString()
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return { 
            data: { success: true },
            statusCode: 200
        };
    } catch (error) {
        // Log error
        logApiCall('ERROR', {
          url,
          method: 'POST',
          headers,
          body: payload,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        return {
            error: error instanceof Error ? error.message : String(error),
            statusCode: 500,
            timestamp: new Date().toISOString()
        };
    }
}

/**
 * Create a WhatsApp QR code
 * @param accessToken - The access token for authorization
 * @param businessId - The WhatsApp business account ID
 * @param prefilledMessage - Optional message to prefill when scanning the QR code
 * @returns ApiResponse containing the QR code data
 */
export async function createWhatsAppQRCode(
    accessToken: string,
    businessId: string,
    prefilledMessage: string = "Hi"
): Promise<ApiResponse<{
    code: string;
    prefilled_message: string;
    deep_link_url: string;
    qr_image_url: string;
}>> {
    const url = `${FACEBOOK_GRAPH_API}/${businessId}/message_qrdls`;
    const headers = {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
    };
    
    const payload = {
        prefilled_message: prefilledMessage,
        generate_qr_image: "SVG"
    };
    
    try {
        // Log request
        logApiCall('REQUEST', {
          url,
          method: 'POST',
          headers,
          body: payload,
          timestamp: new Date().toISOString()
        });
        
        const response = await fetch(
            url,
            {
                method: 'POST',
                headers,
                body: JSON.stringify(payload)
            }
        );
        
        const responseStatus = response.status;
        const data = await response.json();
        
        // Log response
        logApiCall('RESPONSE', {
          url,
          method: 'POST',
          status: responseStatus,
          responseData: data,
          timestamp: new Date().toISOString()
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return { 
            data,
            error: undefined,
            timestamp: new Date().toISOString()
        };
    } catch (error) {
        // Log error
        logApiCall('ERROR', {
          url,
          method: 'POST',
          headers,
          body: payload,
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString()
        });
        
        return {
            error: error instanceof Error ? error.message : String(error),
            data: undefined,
            timestamp: new Date().toISOString()
        };
    }
}

