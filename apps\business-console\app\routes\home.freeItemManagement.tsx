import type { LoaderFunction, ActionFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useFetcher } from "@remix-run/react";
import React, { useMemo, useState, useEffect } from "react";
import { Search } from "lucide-react";
import { FreeItem, FreeItemFormData } from "~/types/api/businessConsoleService/FreeItem";
import { MOCK_FREE_ITEMS } from "~/types/api/mockData/mockdata";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import FreeItemCard from "~/components/FreeItems/FreeItemCard";
import FreeItemModal from "~/components/FreeItems/FreeItemModal";

// --- Loader: returns mock data ---
export const loader: LoaderFunction = async () => {
  return json({ items: MOCK_FREE_ITEMS });
};

// --- Action: handles create/update in-memory (mock) ---
let freeItems = [...MOCK_FREE_ITEMS]; // In-memory mock, resets on server restart

export const action: ActionFunction = async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get("_intent");
  const data: any = Object.fromEntries(formData);

  if (intent === "create" || intent === "update") {
    const item: FreeItem = {
      id: data.id || Math.random().toString(36).substring(2, 11),
      discountType: data.discountType,
      discountPercentage: data.discountPercentage ? Number(data.discountPercentage) : undefined,
      discountFlat: data.discountFlat ? Number(data.discountFlat) : undefined,
      discountUpto: data.discountUpto ? Number(data.discountUpto) : undefined,
      discountMinOrderQty: data.discountMinOrderQty ? Number(data.discountMinOrderQty) : undefined,
      freeItemQty: data.freeItemQty ? Number(data.freeItemQty) : undefined,
      freeItemId: data.freeItemId ? data.freeItemId : undefined,
      validFrom: new Date(data.validFrom),
      validTo: new Date(data.validTo),
      discountDisabled: data.discountDisabled === "true",
      createdAt: data.createdAt ? new Date(data.createdAt) : new Date(),
      updatedAt: new Date(),
    };
    if (intent === "update") {
      freeItems = freeItems.map((i) => (i.id === item.id ? item : i));
    } else {
      freeItems.push(item);
    }
    return json({ success: true, item });
  }
  return json({ success: false });
};




const Index: React.FC = () => {
  const { items: initialItems } = useLoaderData<{ items: FreeItem[] }>();
  const fetcher = useFetcher<any>();
  const [items, setItems] = useState<FreeItem[]>(initialItems);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [currentItem, setCurrentItem] = useState<FreeItemFormData | undefined>(undefined);

  // Update local state when fetcher returns a new item
  useEffect(() => {
    if (fetcher.data?.success && fetcher.data.item) {
      setItems((prev) => {
        const exists = prev.some((i) => i.id === fetcher.data.item.id);
        if (exists) {
          return prev.map((i) => (i.id === fetcher.data.item.id ? fetcher.data.item : i));
        } else {
          return [...prev, fetcher.data.item];
        }
      });
      setModalOpen(false);
    }
  }, [fetcher.data]);

  const filteredItems = useMemo(() => {
    return items.filter((item) => {
      const searchLower = searchQuery.toLowerCase();
      if (item.discountType && item.discountType.toLowerCase().includes(searchLower)) return true;
      if (item.discountPercentage && item.discountPercentage.toString().includes(searchQuery)) return true;
      if (item.discountFlat && item.discountFlat.toString().includes(searchQuery)) return true;
      if (item.discountMinOrderQty && item.discountMinOrderQty.toString().includes(searchQuery)) return true;
      return false;
    });
  }, [items, searchQuery]);

  const handleCreateClick = () => {
    setCurrentItem(undefined);
    setModalOpen(true);
  };

  const handleEditItem = (item: FreeItem) => {
    setCurrentItem(item);
    setModalOpen(true);
  };

  const handleSubmitItem = (data: FreeItemFormData) => {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) formData.append(key, String(value));
    });
    formData.append("_intent", data.id ? "update" : "create");
    fetcher.submit(formData, { method: "post" });
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-center mb-8">
        <h1 className="text-3xl font-bold mb-4 md:mb-0">Free Item Offers</h1>
        <div className="w-full md:w-auto flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search offers..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button
            onClick={handleCreateClick}
            className="whitespace-nowrap"
          >
            Create Free Item Offer
          </Button>
        </div>
      </div>

      {filteredItems.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredItems.map((item) => (
            <FreeItemCard
              key={item.id}
              item={item}
              onEdit={handleEditItem}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-20 bg-muted bg-opacity-50 rounded-lg">
          <h3 className="text-xl font-medium text-gray-600">No offers found</h3>
          <p className="text-gray-500 mt-2">
            {searchQuery ? "Try adjusting your search" : "Create your first free item offer"}
          </p>
          {!searchQuery && (
            <Button
              onClick={handleCreateClick}
              className="mt-4"
              variant="default"
            >
              Create Offer
            </Button>
          )}
        </div>
      )}

      <FreeItemModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        onSubmit={handleSubmitItem}
        initialData={currentItem}
      />
    </div>
  );
};

export default Index;