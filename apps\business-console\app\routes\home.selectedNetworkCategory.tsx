import { json, LoaderFunction } from "@remix-run/node";
import { Form, useActionData, useFetcher, useLoaderData, useNavigate } from "@remix-run/react";
import { error } from "console";
import { ArrowLeft } from "lucide-react";
import { useEffect, useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Card } from "~/components/ui/card";
import CommonItemCategoryList from "~/components/ui/commonItemCategoryList";
import { Input } from "~/components/ui/input";
import ItemCategory from "~/components/ui/itemCategory";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { addNetworkItemCategory, getMasterItemCategory, getNetWorkItemCategories, getSelectedMasterItemCategories, getSelectedNetworkDetails, updateAttributes, } from "~/services/masterItemCategories";
import { MasterItemCategories, SelectedSeller } from "~/types/api/businessConsoleService/MasterItemCategory";
import { NetWorkDetails } from "~/types/api/businessConsoleService/netWorkinfo";
import { withAuth, withResponse } from "~/utils/auth-utils";
interface LoaderData {
      data: NetWorkDetails,
      netWorkId: number,
      name: string,

}
export const loader = withAuth(async ({ user, request }) => {

      const url = new URL(request.url);
      const netWorkId = Number(url.searchParams.get("networkId"));
      const name = (url.searchParams.get("name"));
      try {
            const SelectedNetworkDetails = await getSelectedNetworkDetails(netWorkId, request);
            return withResponse({
                  data: SelectedNetworkDetails.data,
                  netWorkId: netWorkId,
                  name: name
            }, SelectedNetworkDetails.headers)
      }
      catch (error) {
            if (error instanceof Response && error.status === 404) {
                  throw json({ error: "MasterItemCategory pg Not found" }, { status: 404 });
            }
            throw new Response("Failed to fetch MasterItemCategory ", { status: 500 });
      }

})
export const action = withAuth(async ({ user, request }) => {
      const formData = await request.formData()
      const intent = formData.get("intent")
      const categoryId = formData.get("categoryId") as unknown as number
      const netWorkId = formData.get("netWorkId") as unknown as number;
      const attribute = formData.get("attribute");
      const updateValue = formData.get("value");
      const type = formData.get("updateType") as string;

      if (intent === "NetworkItemCategory") {

            const [netWorkItemresponse, masterCategoryResponse] = await Promise.all([
                  getNetWorkItemCategories(netWorkId, request),
                  getMasterItemCategory(request)

            ])
            const responseHeaders = new Headers();

            [netWorkItemresponse, masterCategoryResponse].forEach(response => {
                  if (response.headers?.has('Set-Cookie')) {
                        responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);
                  }
            });
            return withResponse({
                  data: netWorkItemresponse.data,
                  mCategories: masterCategoryResponse.data
            }, responseHeaders)
      }

      if (intent === "addingCategory") {

            const addingResponse = await addNetworkItemCategory(netWorkId, categoryId, request)
            return withResponse(
                  {
                        data: addingResponse.data
                  }, addingResponse.headers)
      }
      if (attribute === "name") {

            const updatedResponse = await updateAttributes(type, netWorkId, attribute, updateValue, request)
            return withResponse({
                  data: updatedResponse.data,
            }, updatedResponse.headers)
      }
      if (attribute === "managerName") {
            const updatedResponse = await updateAttributes(type, netWorkId, attribute, updateValue, request)
            return withResponse({
                  data: updatedResponse.data,
            }, updatedResponse.headers)
      }
})
export default function SelectedNetworkCategory() {
      const navigate = useNavigate()
      const { data, netWorkId, name } = useLoaderData<LoaderData>()
      const networkItemCategory = useActionData<{ data: MasterItemCategories[], mCategories: MasterItemCategories[] }>();
      const [selectedNetworkItemCategory, setSelectedNetworkItemCategory] = useState(networkItemCategory?.data)
      const [activeTab, setActiveTab] = useState('config');
      const [networkName, setNetworkName] = useState(data?.name);
      const [managerName, setMangerName] = useState(data?.managerName);
      const [description, setDescription] = useState(data?.description);
      const fetcher = useFetcher<{ data: MasterItemCategories[], mCategories: MasterItemCategories[] }>();
      const [mItemCategoryList, setMItemCategoryList] = useState<MasterItemCategories[]>()
      const [update, setUpdate] = useState(false)

      const handleTabChange = (newTab: string) => {
            const formData = new FormData();
            setActiveTab(newTab);
            if (newTab === 'netWorkItemDetails') {
                  console.log("networkItemCateGoryCalled")
                  formData.append("intent", "NetworkItemCategory");
                  formData.append("netWorkId", netWorkId.toString());
                  fetcher.submit(formData, { method: "POST" });
            }

      };
      const addNcItem = (categoryId: number) => {
            const formData = new FormData();
            formData.append("intent", "addingCategory");
            formData.append("categoryId", categoryId as unknown as string)
            formData.append("netWorkId", netWorkId as unknown as string)
            fetcher.submit(formData, { method: "POST" })

      }

      const handleUpdate = (attributetype: string, val: any, id: number, type: string) => {
            const formData = new FormData()
            formData.append("updateType", type)

            formData.append("netWorkId", netWorkId as unknown as string)
            formData.append("attribute", attributetype)
            formData.append("value", val)
            formData.append("name", name)
            fetcher.submit(formData, { method: "POST" })

      }

      useEffect(() => {
            if (fetcher.data?.data && activeTab === "netWorkItemDetails") {
                  setSelectedNetworkItemCategory(fetcher.data.data);
                  setMItemCategoryList(fetcher.data.mCategories)
                  console.log(networkItemCategory)
            }
      }, [fetcher.data, activeTab]);

      useEffect(() => {
            if (fetcher.data?.data && fetcher.state !== "idle") {
                  setUpdate(false)
            }
      }, [fetcher.data]);
      return (
            <div className="container mx-auto p-6">
                  <div className="flex items-center gap-2 mb-6">
                        <Button variant="ghost" size="sm" onClick={() => navigate(-1)}>
                              <ArrowLeft className="h-4 w-4 mr-2" />
                              Back to NetWorks
                        </Button>
                        <span className="text-muted-foreground">/</span>
                        <span className="font-semibold">{name}</span>
                  </div>

                  <Tabs value={activeTab} onValueChange={handleTabChange} className="mb-6">
                        <TabsList>
                              <TabsTrigger value="config">Config</TabsTrigger>
                              <TabsTrigger value="netWorkItemDetails">NetWork Item Category</TabsTrigger>
                        </TabsList>
                        <TabsContent value="config">
                              <Card className="w-full max-w-xl p-6 border rounded-lg shadow-md space-y-6 bg-white">
                                    <div key={data.id} className="space-y-6">
                                          <div className="flex justify-between items-center">
                                                <p className="text-xl font-bold">Details</p>
                                                <Switch onClick={() => setUpdate(!update)} />
                                          </div>
                                          <div className="space-y-4">
                                                <div className="flex flex-wrap items-center space-y-2 md:space-y-0 md:space-x-4">
                                                      <p className="text-lg font-semibold text-gray-700">ID:</p>
                                                      <p className="text-lg text-gray-900">{data.id}</p>
                                                </div>
                                                <div className="flex flex-wrap items-center md:space-x-4">
                                                      <p className="w-full md:w-auto text-lg font-semibold text-gray-700">Name:</p>
                                                      <Input
                                                            className="flex-1 w-full md:w-auto"
                                                            placeholder="Enter Name"
                                                            value={networkName}
                                                            onChange={(e) => setNetworkName(e.target.value)}
                                                      />
                                                      {update && (
                                                            <Button
                                                                  className="mt-2 md:mt-0 w-full md:w-auto"
                                                                  onClick={() => handleUpdate("name", networkName, data?.id, "network")}
                                                            >
                                                                  Update
                                                            </Button>
                                                      )}
                                                </div>
                                                <div className="flex flex-wrap items-center md:space-x-4">
                                                      <p className="w-full md:w-auto text-lg font-semibold text-gray-700">Manager Name:</p>
                                                      <Input
                                                            className="flex-1 w-full md:w-auto"
                                                            placeholder="Enter Manager Name"
                                                            value={managerName}
                                                            onChange={(e) => setMangerName(e.target.value)}
                                                      />
                                                      {update && (
                                                            <Button
                                                                  className="mt-2 md:mt-0 w-full md:w-auto"
                                                                  onClick={() => handleUpdate("managerName", managerName, data?.id, "network")}
                                                            >
                                                                  Update
                                                            </Button>
                                                      )}
                                                </div>
                                          </div>
                                    </div>
                              </Card>
                        </TabsContent>
                        <TabsContent value="netWorkItemDetails">
                              <CommonItemCategoryList data={selectedNetworkItemCategory} mItemCategoryList={mItemCategoryList} addItem={addNcItem} />
                        </TabsContent>
                  </Tabs>
            </div>
      )
}
