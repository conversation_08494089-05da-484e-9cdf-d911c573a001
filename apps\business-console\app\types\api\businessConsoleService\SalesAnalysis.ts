export interface BcSalesDashboardDto {
  date: string;
  dataType: string;
  days: number;
  rows: BcSalesDashboardRowDto[];
  totalSales: BcSalesDashboardRowDto;
}

export interface BcSalesDashboardRowDto {
  id: number;
  name: string;
  lastOrderedDate: string;
  agentUserId: number;
  agentName: string;
  buyerMobileNumber: string;
  cols: BcSalesDashboardColDto[];
}

export interface BcSalesDashboardColDto {
  deliveryDate: string;
  salesQty: number;
  salesAmount: number;
  visits: number;
  calls: number;
  searchCount: number;
}

export interface SearchItems {
  id: number | null;
  name: string;
}
