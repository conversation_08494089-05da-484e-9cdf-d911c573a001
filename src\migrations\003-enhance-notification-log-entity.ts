/**
 * Migration 003: Enhance NotificationLog Entity for Campaign Analytics & WhatsApp Integration
 * 
 * Description: Enhances the NotificationLog entity with campaign management, WhatsApp tracking,
 *              analytics capabilities, and ISO timestamps for comprehensive marketing analytics
 * Version: 2.0.0
 * Dependencies: [] // Independent migration - no dependencies
 */

import { 
    DynamoDBClient, 
    UpdateTableCommand, 
    DescribeTableCommand,
    waitUntilTableExists,
    AttributeDefinition,
    KeySchemaElement
} from '@aws-sdk/client-dynamodb';
import { 
    DynamoDBDocumentClient, 
    ScanCommand, 
    UpdateCommand,
    ScanCommandOutput 
} from '@aws-sdk/lib-dynamodb';
import { formatInTimeZone } from 'date-fns-tz';
import { CampaignType, MessageCategory, CustomerSegment } from '../database/entities/NotificationLog.js';

const serverEnv = process.env.SERVER_ENV === 'production' ? 'prod' : 'uat';
const tableName = `notification_logs_${serverEnv}`;

export const migrationInfo = {
    id: '003',
    name: 'Enhance NotificationLog Entity for Campaign Analytics & WhatsApp Integration',
    description: 'Adds campaign management, WhatsApp tracking, analytics capabilities, and ISO timestamps',
    version: '2.0.0',
    dependencies: [], // Independent migration
    createdAt: '2024-12-30T00:00:00.000Z'
};

/**
 * Convert Unix timestamp to IST ISO string
 */
function toISTISOString(timestamp: number): string {
    return formatInTimeZone(
        new Date(timestamp),
        'Asia/Kolkata',
        "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
    );
}

export async function up(): Promise<void> {
    const client = new DynamoDBClient({});
    const docClient = DynamoDBDocumentClient.from(client);
    
    try {
        console.log(`🚀 Migration ${migrationInfo.id}: ${migrationInfo.name}`);
        console.log(`📋 Enhancing table: ${tableName}`);
        console.log('✨ Features:');
        console.log('   - Campaign management (campaignId, campaignName, campaignType)');
        console.log('   - Message categorization (messageCategory, customerSegment, tags)');
        console.log('   - WhatsApp integration (whatsappMessageId, whatsappStatus)');
        console.log('   - Webhook linking (webhookLogId)');
        console.log('   - Analytics timestamps (sentAt, deliveredAt, readAt, failedAt)');
        console.log('   - ISO timestamp fields for all time-based data');
        console.log('   - 5 new Global Secondary Indexes for efficient querying');
        console.log('');

        // Step 1: Add new Global Secondary Indexes
        console.log('⏳ Step 1: Adding new Global Secondary Indexes...');

        const newIndexes = [
            {
                IndexName: 'CampaignIndex',
                KeySchema: [
                    { AttributeName: 'campaignId', KeyType: 'HASH' as const },
                    { AttributeName: 'timestamp', KeyType: 'RANGE' as const }
                ],
                Projection: { ProjectionType: 'ALL' as const }
            },
            {
                IndexName: 'WhatsAppMessageIndex',
                KeySchema: [
                    { AttributeName: 'whatsappMessageId', KeyType: 'HASH' as const }
                ],
                Projection: { ProjectionType: 'ALL' as const }
            },
            {
                IndexName: 'MessageCategoryIndex',
                KeySchema: [
                    { AttributeName: 'messageCategory', KeyType: 'HASH' as const },
                    { AttributeName: 'timestamp', KeyType: 'RANGE' as const }
                ],
                Projection: { ProjectionType: 'ALL' as const }
            },
            {
                IndexName: 'CustomerSegmentIndex',
                KeySchema: [
                    { AttributeName: 'customerSegment', KeyType: 'HASH' as const },
                    { AttributeName: 'timestamp', KeyType: 'RANGE' as const }
                ],
                Projection: { ProjectionType: 'ALL' as const }
            },
            {
                IndexName: 'WhatsAppStatusIndex',
                KeySchema: [
                    { AttributeName: 'whatsappStatus', KeyType: 'HASH' as const },
                    { AttributeName: 'timestamp', KeyType: 'RANGE' as const }
                ],
                Projection: { ProjectionType: 'ALL' as const }
            }
        ];

        // Check if table exists and get current structure
        let currentTable;
        try {
            const describeCommand = new DescribeTableCommand({ TableName: tableName });
            currentTable = await client.send(describeCommand);
        } catch (error) {
            throw new Error(`Table ${tableName} does not exist. Please run migration 001 first.`);
        }

        // Check which indexes already exist
        const existingIndexes = currentTable.Table?.GlobalSecondaryIndexes?.map(idx => idx.IndexName) || [];
        const indexesToAdd = newIndexes.filter(idx => !existingIndexes.includes(idx.IndexName));

        if (indexesToAdd.length > 0) {
            console.log(`   Adding ${indexesToAdd.length} new indexes one by one (DynamoDB limitation)...`);

            // Add new attribute definitions first (all at once)
            const newAttributeDefinitions: AttributeDefinition[] = [
                { AttributeName: 'campaignId', AttributeType: 'S' },
                { AttributeName: 'whatsappMessageId', AttributeType: 'S' },
                { AttributeName: 'messageCategory', AttributeType: 'S' },
                { AttributeName: 'customerSegment', AttributeType: 'S' },
                { AttributeName: 'whatsappStatus', AttributeType: 'S' },
                { AttributeName: 'timestamp', AttributeType: 'N' } // Add timestamp for GSI range keys
            ];

            // Get existing attribute definitions
            const existingAttributes = currentTable.Table?.AttributeDefinitions || [];
            const existingAttributeNames = existingAttributes.map(attr => attr.AttributeName);
            
            // Only add new attributes that don't already exist
            const attributesToAdd = newAttributeDefinitions.filter(
                attr => !existingAttributeNames.includes(attr.AttributeName)
            );

            // Add indexes one by one
            for (let i = 0; i < indexesToAdd.length; i++) {
                const index = indexesToAdd[i];
                console.log(`   Creating index ${i + 1}/${indexesToAdd.length}: ${index.IndexName}...`);

                const updateTableCommand = new UpdateTableCommand({
                    TableName: tableName,
                    AttributeDefinitions: [
                        ...existingAttributes,
                        ...attributesToAdd
                    ],
                    GlobalSecondaryIndexUpdates: [{
                        Create: {
                            IndexName: index.IndexName,
                            KeySchema: index.KeySchema,
                            Projection: index.Projection
                        }
                    }]
                });

                await client.send(updateTableCommand);

                // Wait for this index to become active before creating the next one
                console.log(`   Waiting for ${index.IndexName} to become active...`);
                let attempts = 0;
                const maxAttempts = 60; // 10 minutes max

                while (attempts < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
                    
                    const describeCommand = new DescribeTableCommand({ TableName: tableName });
                    const tableInfo = await client.send(describeCommand);
                    
                    const targetIndex = tableInfo.Table?.GlobalSecondaryIndexes?.find(
                        idx => idx.IndexName === index.IndexName
                    );

                    if (targetIndex?.IndexStatus === 'ACTIVE') {
                        console.log(`   ✅ ${index.IndexName} is now active!`);
                        break;
                    }

                    attempts++;
                    console.log(`     Attempt ${attempts}: ${index.IndexName} status = ${targetIndex?.IndexStatus || 'UNKNOWN'}`);
                    
                    if (attempts >= maxAttempts) {
                        throw new Error(`Timeout waiting for index ${index.IndexName} to become active`);
                    }
                }
            }

            console.log('✅ All indexes created successfully!');
        } else {
            console.log('✅ All required indexes already exist');
        }

        // Step 2: Migrate existing data to add new fields
        console.log('⏳ Step 2: Migrating existing records to add new fields...');
        
        let processedCount = 0;
        let updatedCount = 0;
        let lastEvaluatedKey: Record<string, any> | undefined = undefined;

        do {
            const scanCommand = new ScanCommand({
                TableName: tableName,
                ExclusiveStartKey: lastEvaluatedKey,
                Limit: 25 // Process in small batches
            });

            const response: ScanCommandOutput = await docClient.send(scanCommand);
            
            if (response.Items) {
                // Process records in parallel
                const updatePromises = response.Items.map(async (record) => {
                    processedCount++;
                    
                    const updateExpressions: string[] = [];
                    const expressionAttributeNames: Record<string, string> = {};
                    const expressionAttributeValues: Record<string, any> = {};

                    // Add ISO timestamps
                    if (record.timestamp && !record.timestampISO) {
                        updateExpressions.push('#timestampISO = :timestampISO');
                        expressionAttributeNames['#timestampISO'] = 'timestampISO';
                        expressionAttributeValues[':timestampISO'] = toISTISOString(record.timestamp);
                    }

                    if (record.lastUpdated && !record.lastUpdatedISO) {
                        updateExpressions.push('#lastUpdatedISO = :lastUpdatedISO');
                        expressionAttributeNames['#lastUpdatedISO'] = 'lastUpdatedISO';
                        expressionAttributeValues[':lastUpdatedISO'] = toISTISOString(record.lastUpdated);
                    }

                    // Add analytics timestamp ISOs if the base timestamps exist
                    if (record.sentAt && !record.sentAtISO) {
                        updateExpressions.push('#sentAtISO = :sentAtISO');
                        expressionAttributeNames['#sentAtISO'] = 'sentAtISO';
                        expressionAttributeValues[':sentAtISO'] = toISTISOString(record.sentAt);
                    }

                    if (record.deliveredAt && !record.deliveredAtISO) {
                        updateExpressions.push('#deliveredAtISO = :deliveredAtISO');
                        expressionAttributeNames['#deliveredAtISO'] = 'deliveredAtISO';
                        expressionAttributeValues[':deliveredAtISO'] = toISTISOString(record.deliveredAt);
                    }

                    if (record.readAt && !record.readAtISO) {
                        updateExpressions.push('#readAtISO = :readAtISO');
                        expressionAttributeNames['#readAtISO'] = 'readAtISO';
                        expressionAttributeValues[':readAtISO'] = toISTISOString(record.readAt);
                    }

                    if (record.failedAt && !record.failedAtISO) {
                        updateExpressions.push('#failedAtISO = :failedAtISO');
                        expressionAttributeNames['#failedAtISO'] = 'failedAtISO';
                        expressionAttributeValues[':failedAtISO'] = toISTISOString(record.failedAt);
                    }

                    // Add default campaign fields
                    if (record.campaignId && !record.campaignType) {
                        updateExpressions.push('#campaignType = :campaignType');
                        expressionAttributeNames['#campaignType'] = 'campaignType';
                        expressionAttributeValues[':campaignType'] = CampaignType.MARKETING;
                    }

                    if (!record.messageCategory) {
                        updateExpressions.push('#messageCategory = :messageCategory');
                        expressionAttributeNames['#messageCategory'] = 'messageCategory';
                        expressionAttributeValues[':messageCategory'] = MessageCategory.GENERAL;
                    }

                    if (!record.customerSegment) {
                        updateExpressions.push('#customerSegment = :customerSegment');
                        expressionAttributeNames['#customerSegment'] = 'customerSegment';
                        expressionAttributeValues[':customerSegment'] = CustomerSegment.GENERAL;
                    }

                    // Only update if there are changes to make
                    if (updateExpressions.length > 0) {
                        const updateCommand = new UpdateCommand({
                            TableName: tableName,
                            Key: {
                                notificationId: record.notificationId,
                                // Add timestamp if using composite key
                                // timestamp: record.timestamp
                            },
                            UpdateExpression: `SET ${updateExpressions.join(', ')}`,
                            ExpressionAttributeNames: expressionAttributeNames,
                            ExpressionAttributeValues: expressionAttributeValues
                        });

                        await docClient.send(updateCommand);
                        updatedCount++;
                    }
                });

                await Promise.all(updatePromises);
                console.log(`   Processed ${processedCount} records, updated ${updatedCount} records`);
            }

            lastEvaluatedKey = response.LastEvaluatedKey;
        } while (lastEvaluatedKey);

        console.log(`✅ Data migration completed: ${updatedCount}/${processedCount} records updated`);
        console.log('🎉 Migration completed successfully!');
        
    } catch (error) {
        console.error(`❌ Migration ${migrationInfo.id} failed:`, error);
        throw error;
    } finally {
        client.destroy();
    }
}

export async function down(): Promise<void> {
    const client = new DynamoDBClient({});
    
    try {
        console.log(`🔄 Rolling back migration ${migrationInfo.id}`);
        console.log('⚠️  Note: This rollback will remove indexes but not revert data changes');
        console.log('   ISO timestamp fields and default campaign values will remain');
        console.log('   Use a backup restore for complete data rollback');
        
        // Remove the Global Secondary Indexes
        const indexesToRemove = [
            'CampaignIndex',
            'WhatsAppMessageIndex', 
            'MessageCategoryIndex',
            'CustomerSegmentIndex',
            'WhatsAppStatusIndex'
        ];

        console.log(`🗑️  Removing ${indexesToRemove.length} Global Secondary Indexes...`);

        const updateTableCommand = new UpdateTableCommand({
            TableName: tableName,
            GlobalSecondaryIndexUpdates: indexesToRemove.map(indexName => ({
                Delete: {
                    IndexName: indexName
                }
            }))
        });

        await client.send(updateTableCommand);
        console.log('✅ Rollback completed successfully');
        
    } catch (error) {
        console.error(`❌ Rollback failed:`, error);
        throw error;
    } finally {
        client.destroy();
    }
}

export async function validate(): Promise<boolean> {
    const client = new DynamoDBClient({});
    const docClient = DynamoDBDocumentClient.from(client);
    
    try {
        console.log(`🔍 Validating migration ${migrationInfo.id}...`);
        
        // Check if table exists and has correct structure
        const describeCommand = new DescribeTableCommand({ TableName: tableName });
        const tableInfo = await client.send(describeCommand);
        
        if (!tableInfo.Table) {
            console.log(`❌ Table ${tableName} does not exist`);
            return false;
        }

        // Check for new indexes
        const requiredIndexes = [
            'CampaignIndex',
            'WhatsAppMessageIndex',
            'MessageCategoryIndex', 
            'CustomerSegmentIndex',
            'WhatsAppStatusIndex'
        ];

        const existingIndexes = tableInfo.Table.GlobalSecondaryIndexes?.map(idx => idx.IndexName) || [];
        const missingIndexes = requiredIndexes.filter(idx => !existingIndexes.includes(idx));

        if (missingIndexes.length > 0) {
            console.log(`❌ Missing indexes: ${missingIndexes.join(', ')}`);
            return false;
        }

        // Check if all indexes are active
        const inactiveIndexes = tableInfo.Table.GlobalSecondaryIndexes?.filter(
            idx => idx.IndexStatus !== 'ACTIVE'
        ) || [];

        if (inactiveIndexes.length > 0) {
            console.log(`❌ Inactive indexes: ${inactiveIndexes.map(idx => idx.IndexName).join(', ')}`);
            return false;
        }

        // Sample a few records to check if data migration worked
        console.log('🔍 Validating data migration...');
        const scanCommand = new ScanCommand({
            TableName: tableName,
            Limit: 10
        });

        const response: ScanCommandOutput = await docClient.send(scanCommand);
        
        if (response.Items && response.Items.length > 0) {
            let recordsWithISO = 0;
            let recordsWithCampaignFields = 0;

            for (const record of response.Items) {
                if (record.timestampISO && record.lastUpdatedISO) {
                    recordsWithISO++;
                }
                if (record.messageCategory && record.customerSegment) {
                    recordsWithCampaignFields++;
                }
            }

            console.log(`📊 Sample validation (${response.Items.length} records):`);
            console.log(`   Records with ISO timestamps: ${recordsWithISO}/${response.Items.length}`);
            console.log(`   Records with campaign fields: ${recordsWithCampaignFields}/${response.Items.length}`);
        }

        console.log('✅ Migration validation passed');
        return true;
        
    } catch (error) {
        console.log(`❌ Migration validation failed:`, error);
        return false;
    } finally {
        client.destroy();
    }
} 