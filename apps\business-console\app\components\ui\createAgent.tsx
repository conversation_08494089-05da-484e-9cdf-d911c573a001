import { useEffect, useState } from "react";
import { useFetcher } from "@remix-run/react";
import { Dialog, DialogContent, DialogTitle } from "./dialog";
import { Input } from "./input";
import { <PERSON><PERSON> } from "./button";
import { useToast } from "./ToastProvider";
import { Seller } from "~/types/api/businessConsoleService/MasterItemCategory";
import SpinnerLoader from "../loader/SpinnerLoader";

export interface RoleOption {
      [x: string]: any;
      value: string;
      label: string;
}

interface NetWorkModalProps {
      isOpen: boolean;
      onClose: () => void;
      NetWorkId: number;
      roles: RoleOption[],
      sellerList: Seller[]
}

export default function CreateAgent({ isOpen, onClose, NetWorkId, roles, sellerList }: NetWorkModalProps) {
      const fetcher = useFetcher();
      const [managerId, setManagerId] = useState<number | "">("");

      const [formData, setFormData] = useState({
            firstName: "",
            lastName: "",
            email: "",
            mobileNumber: "",
            address: "",
            password: "",
            businessId: managerId,
            roles: [] as string[],
      });

      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {


            const { name, value } = e.target;
            setFormData((prev) => ({
                  ...prev,
                  [name]: value,
            }));
      };

      const handleManagerChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
            const selectedManagerId = e.target.value ? Number(e.target.value) : 0;
            setManagerId(selectedManagerId);

            setFormData((prev) => ({
                  ...prev,
                  businessId: selectedManagerId,
            }));
      };


      const isLoading = fetcher.state !== "idle";
      const { showToast } = useToast()

      useEffect(() => {
            if (fetcher.data && isOpen === true) {


                  if (fetcher.data !== undefined && isOpen === true) {
                        showToast("AgentCreated SuccessFully", "success")
                        onClose()
                        setFormData({
                              firstName: "",
                              lastName: "",
                              email: "",
                              mobileNumber: "",
                              address: "",
                              password: "",
                              businessId: managerId,
                              roles: [] as string[],
                        })
                  }
                  else if (fetcher.data === undefined) {
                        showToast("Fail To create Agent", "error"); // Show error in parent
                  }
            }


      }, [fetcher.data])


      const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();
            const data = new FormData();
            Object.entries(formData).forEach(([key, value]) => {
                  data.append(key, Array.isArray(value) ? value.join(",") : value.toString());
            });
            data.append("netWorkId", NetWorkId.toString());
            data.append("_intent", "createAgent");
            data.append("roles", "AgentFull")
            fetcher.submit(data, { method: "POST" });
      };

      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <SpinnerLoader loading={isLoading} />
                  <DialogContent>
                        {isLoading && (
                              <div className="absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm">
                                    <SpinnerLoader loading={isLoading} />
                              </div>
                        )}
                        <DialogTitle>Add New Agent</DialogTitle>
                        <form onSubmit={handleSubmit} className="space-y-4">

                              <div className="flex gap-2">
                                    <div className="w-full flex-col flex gap-y-2">
                                          <label className="block text-sm font-medium text-gray-700">First Name</label>
                                          <Input value={formData.firstName} name="firstName" placeholder="First Name" onChange={handleChange} required />
                                    </div>
                                    <div className="w-full flex-col flex gap-y-2">
                                          <label className="block text-sm font-medium text-gray-700">Last Name</label>
                                          <Input name="lastName" value={formData.lastName} placeholder="Last Name" onChange={handleChange} required />
                                    </div>
                              </div>

                              <div className="flex gap-2">
                                    <div className="w-full flex-col flex gap-y-2">
                                          <label className="block text-sm font-medium text-gray-700">Email</label>
                                          <Input type="email" name="email" placeholder="Email" onChange={handleChange} required
                                                value={formData.email}
                                          />
                                    </div>
                                    <div className="w-full flex-col flex gap-y-2">
                                          <label className="block text-sm font-medium text-gray-700">Mobile Number</label>
                                          <Input name="mobileNumber" placeholder="Mobile Number" type="number" onChange={(e) => {
                                                const value = e.target.value.replace(/\D/g, "");
                                                if (value.length <= 10) {
                                                      setFormData((prev) => ({
                                                            ...prev,
                                                            mobileNumber: value
                                                      }
                                                      ))
                                                }
                                          }}

                                                value={formData.mobileNumber}

                                                required />
                                    </div>
                              </div>

                              <div className="w-full flex-col flex gap-y-2">
                                    <label className="block text-sm font-medium text-gray-700">Address</label>
                                    <Input name="address" placeholder="Address" onChange={handleChange} required
                                          value={formData.address}
                                    />
                              </div>

                              {/* <div className="flex gap-2">
                                    <div className="w-full flex-col flex gap-y-2">
                                          <label className="block text-sm font-medium text-gray-700">Select Seller</label>
                                          <select
                                                className="border p-2 rounded w-full"
                                                value={managerId}
                                                onChange={handleManagerChange}
                                          >
                                                <option value="">Select Seller</option>
                                                {sellerList?.map((seller) => (
                                                      <option key={seller.businessId} value={seller.businessId}>
                                                            {seller.name}
                                                      </option>
                                                ))}
                                          </select>
                                    </div>
                              </div> */}
                              <div className=" flex justify-end items-end">
                                    <Button type="submit" loading={isLoading} >Save Agent</Button>
                              </div>
                        </form>
                  </DialogContent>
            </Dialog>
      );
}