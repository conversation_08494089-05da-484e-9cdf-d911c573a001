import { NextFunction, Request } from 'express';
import { CustomResponse } from '@/interfaces/response.interface.js';


interface ResponseObject {
  ok: boolean;
  err: any;
  data: any;
  code?: number;
}


// TODO: clean the code
function sendResponse(data: ResponseObject, isEncryption: boolean) {
  return  data;
}

export default (req: Request, res: CustomResponse, next: NextFunction) => {
  let isEncryption = false;
  
  res.invalid = ({ msg, code = 400 }: {msg: string, code : number}) => {
    const responseData = sendResponse({ ok: false, err: msg || 'Invalid Parameters', data: null }, isEncryption);
    res.resBody = responseData;
    return res.status(code).json(responseData);
  };

  res.failure = ({ msg, code = 200 }: {msg: string, code : number}) => {
    const responseData = sendResponse(
      {
        ok: false,
        err: msg || "Something is wrong! We're looking into it.",
        data: null,
      },
      isEncryption,
    );

    res.resBody = responseData;
    return res.status(code).json(responseData);
  };

  res.unauthorized = ({ msg }: {msg: string, code : number}) => {
    const responseData = sendResponse({ ok: false, err: msg || 'Authentication Failed', data: null }, isEncryption);
    res.resBody = responseData;

    return res.status(401).json(responseData);
  };

  res.success = ({ data = {}, code = 200 }) => {
    const responseData = sendResponse({ ok: true, err: null, data }, isEncryption);
    res.resBody = responseData;

    return res.status(code).json(responseData);
  };

  next();
};
