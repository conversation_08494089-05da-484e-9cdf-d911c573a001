import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select"

interface PageSizeSelectorProps {
    value: string;
    onValueChange: (value: string) => void;
    options?: Array<number>;
    className?: string;
}

export function PageSizeSelector({ 
    value, 
    onValueChange, 
    options = [5, 10, 20, 50],
    className = "w-[180px]"
}: PageSizeSelectorProps) {
    return (
        <Select value={value} onValueChange={onValueChange}>
            <SelectTrigger className={className}>
                <SelectValue placeholder="Items per page" />
            </SelectTrigger>
            <SelectContent>
                {options.map((size) => (
                    <SelectItem key={size} value={size.toString()}>
                        {size} per page
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>
    )
} 