/**
 * NotificationLogService Tests
 * 
 * Tests for enhanced service layer methods including campaign logging,
 * webhook synchronization, analytics generation, and ISO timestamp handling.
 */

import { NotificationLogService } from '../../services/notificationLogService.js';
import { NotificationLogRepository } from '../../database/repository/NotificationLogRepository.js';
import { 
    NotificationLog, 
    NotificationChannel, 
    NotificationStatus, 
    CampaignType, 
    MessageCategory, 
    CustomerSegment 
} from '../../database/entities/NotificationLog.js';
import { 
    CampaignNotificationRequest, 
    WebhookNotificationUpdate, 
    CampaignAnalytics, 
    CustomerEngagementAnalytics 
} from '../../types/notification.types.js';

// Mock testing functions since no test framework is configured
const describe = (name: string, fn: () => void) => {
    console.log(`\n🧪 ${name}`);
    fn();
};

const test = (name: string, fn: () => Promise<void>) => {
    console.log(`  ⚙️ ${name}`);
    fn().then(() => console.log(`  ✅ ${name} - PASSED`))
         .catch(err => console.error(`  ❌ ${name} - FAILED:`, err));
};

const beforeEach = (fn: () => void) => {
    // Initialize before each test
    fn();
};

const expect = (actual: any) => ({
    toBe: (expected: any) => {
        if (actual !== expected) {
            throw new Error(`Expected ${expected}, got ${actual}`);
        }
    },
    toEqual: (expected: any) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
            throw new Error(`Expected ${JSON.stringify(expected)}, got ${JSON.stringify(actual)}`);
        }
    },
    toBeDefined: () => {
        if (actual === undefined || actual === null) {
            throw new Error(`Expected value to be defined, got ${actual}`);
        }
    },
    toHaveLength: (length: number) => {
        if (!actual || actual.length !== length) {
            throw new Error(`Expected length ${length}, got ${actual?.length}`);
        }
    },
    toHaveProperty: (prop: string) => {
        if (!actual || !(prop in actual)) {
            throw new Error(`Expected object to have property ${prop}`);
        }
    },
    toContain: (item: any) => {
        if (!actual || !actual.includes(item)) {
            throw new Error(`Expected array to contain ${item}`);
        }
    },
    toMatch: (pattern: RegExp) => {
        if (!pattern.test(actual)) {
            throw new Error(`Expected ${actual} to match ${pattern}`);
        }
    },
    toBeGreaterThan: (value: number) => {
        if (actual <= value) {
            throw new Error(`Expected ${actual} to be greater than ${value}`);
        }
    },
    toBeLessThan: (value: number) => {
        if (actual >= value) {
            throw new Error(`Expected ${actual} to be less than ${value}`);
        }
    },
    rejects: {
        toThrow: async (expectedMessage?: string) => {
            try {
                await actual;
                throw new Error('Expected promise to reject');
            } catch (error: any) {
                if (expectedMessage && !error.message.includes(expectedMessage)) {
                    throw new Error(`Expected error message to contain "${expectedMessage}", got "${error.message}"`);
                }
            }
        }
    }
});

// Mock functions
const mockFn = () => {
    const calls: any[][] = [];
    const mockFunction: any = (...args: any[]) => {
        calls.push(args);
        return mockFunction._returnValue;
    };
    mockFunction.mock = { calls };
    mockFunction._returnValue = undefined;
    mockFunction.mockResolvedValueOnce = (value: any) => { 
        mockFunction._returnValue = Promise.resolve(value); 
        return mockFunction; 
    };
    mockFunction.mockRejectedValueOnce = (error: any) => { 
        mockFunction._returnValue = Promise.reject(error); 
        return mockFunction; 
    };
    mockFunction.mockClear = () => { 
        calls.length = 0; 
        return mockFunction; 
    };
    return mockFunction;
};

const objectContaining = (obj: any) => obj;
const any = (constructor: any) => constructor;

// Mock repository
const mockRepository = {
    createLog: mockFn(),
    updateLog: mockFn(),
    getLog: mockFn(),
    getLogsByBusiness: mockFn(),
    findByWhatsAppMessageId: mockFn(),
    updateByWhatsAppMessageId: mockFn(),
    getByCampaignId: mockFn(),
    getByCustomerSegment: mockFn(),
    getByMessageCategory: mockFn(),
    getByWhatsAppStatus: mockFn(),
    queryWithFilters: mockFn(),
    getCampaignAnalyticsData: mockFn()
};

describe('NotificationLogService', () => {
    let service: NotificationLogService;
    let mockNotificationLog: NotificationLog;

    beforeEach(() => {
        service = new NotificationLogService();
        
        // Clear all mocks
        Object.values(mockRepository).forEach(fn => fn.mockClear());
        
        const timestamp = Date.now();
        mockNotificationLog = {
            notificationId: 'test-notification-123',
            timestamp,
            timestampISO: '2024-01-15T10:30:00.000+05:30',
            businessId: 'business-123',
            mobileNumber: '+919876543210',
            channel: NotificationChannel.WHATSAPP,
            recipient: '+919876543210',
            inputPayload: { templateName: 'test_template' },
            providerRequest: { messaging_product: 'whatsapp' },
            providerResponse: { messages: [{ id: 'wamid.test123' }] },
            status: NotificationStatus.SENT,
            errorMessage: undefined,
            retryCount: 0,
            lastUpdated: timestamp,
            lastUpdatedISO: '2024-01-15T10:30:00.000+05:30'
        };
    });

    describe('Basic Notification Logging', () => {
        test('logWhatsAppNotification should create WhatsApp notification log', async () => {
            mockRepository.createLog.mockResolvedValueOnce(mockNotificationLog);
            
            const result = await service.logWhatsAppNotification(
                'business-123',
                '+919876543210',
                '+919876543210',
                { templateName: 'test' },
                { messaging_product: 'whatsapp' },
                { messages: [{ id: 'wamid.123' }] },
                NotificationStatus.SENT
            );
            
            expect(result).toEqual(mockNotificationLog);
            expect(mockRepository.createLog.mock.calls.length).toBe(1);
            const createCall = mockRepository.createLog.mock.calls[0][0];
            expect(createCall.businessId).toBe('business-123');
            expect(createCall.mobileNumber).toBe('+919876543210');
            expect(createCall.channel).toBe(NotificationChannel.WHATSAPP);
            expect(createCall.status).toBe(NotificationStatus.SENT);
        });

        test('logFirebaseNotification should create Firebase notification log', async () => {
            const firebaseLog = { ...mockNotificationLog, channel: NotificationChannel.FIREBASE };
            mockRepository.createLog.mockResolvedValueOnce(firebaseLog);
            
            const result = await service.logFirebaseNotification(
                'business-123',
                '+919876543210',
                'firebase-token',
                { title: 'Test Notification' },
                { to: 'firebase-token' },
                { messageId: 'firebase-123' },
                NotificationStatus.SENT
            );
            
            expect(result.channel).toBe(NotificationChannel.FIREBASE);
            expect(mockRepository.createLog.mock.calls.length).toBe(1);
        });

        test('updateNotificationStatus should update status with WhatsApp message ID and analytics timestamps', async () => {
            const timestamp = Date.now();
            
            await service.updateNotificationStatus(
                'test-notification',
                timestamp,
                NotificationStatus.DELIVERED,
                { messageStatus: 'delivered' },
                undefined,
                'wamid.test123'
            );
            
            expect(mockRepository.updateLog.mock.calls.length).toBe(1);
            const updateCall = mockRepository.updateLog.mock.calls[0];
            expect(updateCall[0]).toBe('test-notification');
            expect(updateCall[1]).toBe(timestamp);
            const updateData = updateCall[2];
            expect(updateData.status).toBe(NotificationStatus.DELIVERED);
            expect(updateData.whatsappMessageId).toBe('wamid.test123');
            expect(updateData.whatsappStatus).toBe(NotificationStatus.DELIVERED);
            expect(typeof updateData.deliveredAt).toBe('number');
        });

        test('updateNotificationStatus should set appropriate analytics timestamp based on status', async () => {
            const timestamp = Date.now();
            
            // Test different status updates
            const statusTests = [
                { status: NotificationStatus.SENT, expectedField: 'sentAt' },
                { status: NotificationStatus.DELIVERED, expectedField: 'deliveredAt' },
                { status: NotificationStatus.READ, expectedField: 'readAt' },
                { status: NotificationStatus.FAILED, expectedField: 'failedAt' }
            ];
            
            for (const test of statusTests) {
                mockRepository.updateLog.mockClear();
                
                await service.updateNotificationStatus(
                    'test-notification',
                    timestamp,
                    test.status
                );
                
                const updateCall = mockRepository.updateLog.mock.calls[0];
                const updateData = updateCall[2];
                expect(updateData).toHaveProperty(test.expectedField);
                expect(typeof updateData[test.expectedField]).toBe('number');
            }
        });
    });

    describe('Campaign Notification Logging', () => {
        test('logCampaignNotification should create campaign notification with all fields', async () => {
            const campaignRequest: CampaignNotificationRequest & {
                recipient: string;
                providerRequest: any;
                providerResponse?: any;
                status: NotificationStatus;
                whatsappMessageId?: string;
            } = {
                businessId: 'business-123',
                campaignId: 'summer_sale_2024',
                campaignName: 'Summer Sale Campaign',
                campaignType: CampaignType.PROMOTIONAL,
                messageCategory: MessageCategory.PROMOTIONAL_OFFER,
                customerSegment: CustomerSegment.HIGH_VALUE,
                tags: ['summer', 'sale', 'premium'],
                recipients: ['+919876543210'],
                templateName: 'summer_offer',
                templateValues: ['John', '20% OFF'],
                recipient: '+919876543210',
                providerRequest: { messaging_product: 'whatsapp' },
                status: NotificationStatus.SENT,
                whatsappMessageId: 'wamid.campaign123'
            };
            
            const campaignLog = {
                ...mockNotificationLog,
                campaignId: 'summer_sale_2024',
                campaignName: 'Summer Sale Campaign',
                campaignType: CampaignType.PROMOTIONAL,
                messageCategory: MessageCategory.PROMOTIONAL_OFFER,
                customerSegment: CustomerSegment.HIGH_VALUE,
                tags: ['summer', 'sale', 'premium'],
                whatsappMessageId: 'wamid.campaign123'
            };
            
            mockRepository.createLog.mockResolvedValueOnce(campaignLog);
            
            const result = await service.logCampaignNotification(campaignRequest);
            
            expect(result).toEqual(campaignLog);
            expect(mockRepository.createLog.mock.calls.length).toBe(1);
            const createCall = mockRepository.createLog.mock.calls[0][0];
            expect(createCall.businessId).toBe('business-123');
            expect(createCall.campaignId).toBe('summer_sale_2024');
            expect(createCall.campaignName).toBe('Summer Sale Campaign');
            expect(createCall.campaignType).toBe(CampaignType.PROMOTIONAL);
            expect(createCall.messageCategory).toBe(MessageCategory.PROMOTIONAL_OFFER);
            expect(createCall.customerSegment).toBe(CustomerSegment.HIGH_VALUE);
            expect(createCall.whatsappMessageId).toBe('wamid.campaign123');
            expect(createCall.whatsappStatus).toBe(NotificationStatus.SENT);
            expect(createCall.channel).toBe(NotificationChannel.WHATSAPP);
        });

        test('logCampaignNotification should handle campaign with minimal data', async () => {
            const minimalCampaignRequest = {
                businessId: 'business-123',
                campaignId: 'basic_campaign',
                campaignName: 'Basic Campaign',
                campaignType: CampaignType.MARKETING,
                messageCategory: MessageCategory.GENERAL,
                recipients: ['+919876543210'],
                recipient: '+919876543210',
                providerRequest: { messaging_product: 'whatsapp' },
                status: NotificationStatus.PENDING
            };
            
            mockRepository.createLog.mockResolvedValueOnce(mockNotificationLog);
            
            await service.logCampaignNotification(minimalCampaignRequest);
            
            expect(mockRepository.createLog.mock.calls.length).toBe(1);
            const createCall = mockRepository.createLog.mock.calls[0][0];
            expect(createCall.campaignId).toBe('basic_campaign');
            expect(createCall.customerSegment).toBe(CustomerSegment.GENERAL);
            expect(createCall.status).toBe(NotificationStatus.PENDING);
        });
    });

    describe('Webhook Status Synchronization', () => {
        test('updateStatusFromWebhook should find and update notification by WhatsApp message ID', async () => {
            const webhookUpdate: WebhookNotificationUpdate = {
                whatsappMessageId: 'wamid.test123',
                status: NotificationStatus.DELIVERED,
                timestamp: Date.now(),
                recipientId: '+919876543210'
            };
            
            mockRepository.findByWhatsAppMessageId.mockResolvedValueOnce(mockNotificationLog);
            mockRepository.updateByWhatsAppMessageId.mockResolvedValueOnce(true);
            
            const result = await service.updateStatusFromWebhook(webhookUpdate);
            
            expect(result.successfulUpdates).toBe(1);
            expect(result.failedUpdates).toBe(0);
            expect(result.skippedUpdates).toBe(0);
            expect(result.processedNotifications).toContain(mockNotificationLog.notificationId);
            
            expect(mockRepository.findByWhatsAppMessageId.mock.calls[0][0]).toBe('wamid.test123');
            expect(mockRepository.updateByWhatsAppMessageId.mock.calls.length).toBe(1);
            const updateCall = mockRepository.updateByWhatsAppMessageId.mock.calls[0];
            expect(updateCall[0]).toBe('wamid.test123');
            const updateData = updateCall[1];
            expect(updateData.whatsappStatus).toBe(NotificationStatus.DELIVERED);
            expect(updateData.status).toBe(NotificationStatus.DELIVERED);
            expect(typeof updateData.deliveredAt).toBe('number');
        });

        test('updateStatusFromWebhook should handle notification not found', async () => {
            const webhookUpdate: WebhookNotificationUpdate = {
                whatsappMessageId: 'wamid.nonexistent',
                status: NotificationStatus.DELIVERED,
                timestamp: Date.now(),
                recipientId: '+919876543210'
            };
            
            mockRepository.findByWhatsAppMessageId.mockResolvedValueOnce(null);
            
            const result = await service.updateStatusFromWebhook(webhookUpdate);
            
            expect(result.successfulUpdates).toBe(0);
            expect(result.skippedUpdates).toBe(1);
            expect(result.failedNotifications).toHaveLength(1);
            expect(result.failedNotifications[0].whatsappMessageId).toBe('wamid.nonexistent');
            expect(result.failedNotifications[0].error).toBe('Notification not found for WhatsApp message ID');
        });

        test('updateStatusFromWebhook should handle failed status with error message', async () => {
            const webhookUpdate: WebhookNotificationUpdate = {
                whatsappMessageId: 'wamid.test123',
                status: NotificationStatus.FAILED,
                timestamp: Date.now(),
                recipientId: '+919876543210',
                error: {
                    code: 131000,
                    title: 'MESSAGE_UNDELIVERABLE',
                    message: 'Message could not be delivered'
                }
            };
            
            mockRepository.findByWhatsAppMessageId.mockResolvedValueOnce(mockNotificationLog);
            mockRepository.updateByWhatsAppMessageId.mockResolvedValueOnce(true);
            
            await service.updateStatusFromWebhook(webhookUpdate);
            
            expect(mockRepository.updateByWhatsAppMessageId.mock.calls.length).toBe(1);
            const updateCall = mockRepository.updateByWhatsAppMessageId.mock.calls[0];
            expect(updateCall[0]).toBe('wamid.test123');
            const updateData = updateCall[1];
            expect(updateData.status).toBe(NotificationStatus.FAILED);
            expect(typeof updateData.failedAt).toBe('number');
            expect(updateData.errorMessage).toBe('Message could not be delivered');
        });

        test('updateStatusFromWebhook should calculate performance metrics', async () => {
            const webhookUpdate: WebhookNotificationUpdate = {
                whatsappMessageId: 'wamid.test123',
                status: NotificationStatus.DELIVERED,
                timestamp: Date.now(),
                recipientId: '+919876543210'
            };
            
            mockRepository.findByWhatsAppMessageId.mockResolvedValueOnce(mockNotificationLog);
            mockRepository.updateByWhatsAppMessageId.mockResolvedValueOnce(true);
            
            const result = await service.updateStatusFromWebhook(webhookUpdate);
            
            expect(result.syncId).toMatch(/^sync_\d+_[a-z0-9]+$/);
            expect(typeof result.timestamp).toBe('number');
            expect(typeof result.processingDuration).toBe('number');
            expect(typeof result.averageUpdateTime).toBe('number');
            expect(result.totalUpdatesProcessed).toBe(1);
        });
    });

    describe('Campaign Analytics', () => {
        test('getCampaignAnalytics should generate comprehensive analytics', async () => {
            const campaignId = 'summer_sale_2024';
            const mockAnalyticsData = {
                totalMessages: 100,
                statusCounts: {
                    [NotificationStatus.PENDING]: 5,
                    [NotificationStatus.SENT]: 90,
                    [NotificationStatus.DELIVERED]: 85,
                    [NotificationStatus.READ]: 45,
                    [NotificationStatus.FAILED]: 5
                },
                segmentCounts: {
                    [CustomerSegment.HIGH_VALUE]: 60,
                    [CustomerSegment.GENERAL]: 40,
                    [CustomerSegment.NEW_CUSTOMER]: 0,
                    [CustomerSegment.RETURNING_CUSTOMER]: 0,
                    [CustomerSegment.VIP_CUSTOMER]: 0,
                    [CustomerSegment.LOW_ENGAGEMENT]: 0,
                    [CustomerSegment.DORMANT]: 0
                },
                timeSeriesData: [
                    { timestamp: Date.now() - 3600000, count: 50, status: NotificationStatus.SENT },
                    { timestamp: Date.now(), count: 50, status: NotificationStatus.SENT }
                ]
            };
            
            const mockNotifications = [
                {
                    ...mockNotificationLog,
                    campaignId,
                    campaignName: 'Summer Sale Campaign',
                    campaignType: CampaignType.PROMOTIONAL,
                    messageCategory: MessageCategory.PROMOTIONAL_OFFER,
                    sentAt: Date.now() - 1000,
                    deliveredAt: Date.now() - 500,
                    readAt: Date.now()
                }
            ];
            
            mockRepository.getCampaignAnalyticsData.mockResolvedValueOnce(mockAnalyticsData);
            mockRepository.getByCampaignId.mockResolvedValueOnce(mockNotifications);
            
            const result = await service.getCampaignAnalytics(campaignId);
            
            expect(result.campaignId).toBe(campaignId);
            expect(result.campaignName).toBe('Summer Sale Campaign');
            expect(result.totalMessages).toBe(100);
            expect(result.deliveryRate).toEqual(85); // 85/100 * 100
            expect(typeof result.readRate).toBe('number');
            expect(result.failureRate).toEqual(5); // 5/100 * 100
            
            // Check time series data includes ISO timestamps
            expect(result.timeSeriesData[0]).toHaveProperty('timestampISO');
            expect(result.timeSeriesData[0].timestampISO).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
            
            // Check campaign dates include ISO timestamps
            expect(result.startDateISO).toBeDefined();
            expect(result.endDateISO).toBeDefined();
        });

        test('getCampaignAnalytics should handle campaign with no notifications', async () => {
            mockRepository.getByCampaignId.mockResolvedValueOnce([]);
            
            await expect(service.getCampaignAnalytics('non-existent')).rejects.toThrow(
                'Campaign non-existent not found or has no notifications'
            );
        });

        test('getCampaignAnalytics should calculate segment breakdown correctly', async () => {
            const campaignId = 'segment_test';
            const mockAnalyticsData = {
                totalMessages: 50,
                statusCounts: {
                    [NotificationStatus.PENDING]: 0,
                    [NotificationStatus.SENT]: 50,
                    [NotificationStatus.DELIVERED]: 40,
                    [NotificationStatus.READ]: 20,
                    [NotificationStatus.FAILED]: 0
                },
                segmentCounts: {
                    [CustomerSegment.HIGH_VALUE]: 30,
                    [CustomerSegment.GENERAL]: 20,
                    [CustomerSegment.NEW_CUSTOMER]: 0,
                    [CustomerSegment.RETURNING_CUSTOMER]: 0,
                    [CustomerSegment.VIP_CUSTOMER]: 0,
                    [CustomerSegment.LOW_ENGAGEMENT]: 0,
                    [CustomerSegment.DORMANT]: 0
                },
                timeSeriesData: []
            };
            
            const mockNotifications = [
                { ...mockNotificationLog, campaignId, customerSegment: CustomerSegment.HIGH_VALUE, status: NotificationStatus.DELIVERED },
                { ...mockNotificationLog, campaignId, customerSegment: CustomerSegment.GENERAL, status: NotificationStatus.READ }
            ];
            
            mockRepository.getCampaignAnalyticsData.mockResolvedValueOnce(mockAnalyticsData);
            mockRepository.getByCampaignId.mockResolvedValueOnce(mockNotifications);
            
            const result = await service.getCampaignAnalytics(campaignId);
            
            expect(result.segmentBreakdown).toHaveProperty(CustomerSegment.HIGH_VALUE);
            expect(result.segmentBreakdown).toHaveProperty(CustomerSegment.GENERAL);
            expect(result.segmentBreakdown[CustomerSegment.HIGH_VALUE]?.count).toBe(30);
            expect(result.segmentBreakdown[CustomerSegment.GENERAL]?.count).toBe(20);
        });
    });

    describe('Customer Engagement Analytics', () => {
        test('getCustomerEngagement should generate customer analytics', async () => {
            const mobileNumber = '+919876543210';
            const mockNotifications = [
                {
                    ...mockNotificationLog,
                    mobileNumber,
                    status: NotificationStatus.DELIVERED,
                    campaignId: 'campaign1',
                    campaignName: 'Campaign 1',
                    messageCategory: MessageCategory.PROMOTIONAL_OFFER,
                    deliveredAt: Date.now() - 1000,
                    readAt: Date.now()
                },
                {
                    ...mockNotificationLog,
                    mobileNumber,
                    status: NotificationStatus.READ,
                    campaignId: 'campaign2',
                    messageCategory: MessageCategory.GENERAL
                }
            ];
            
            mockRepository.queryWithFilters.mockResolvedValueOnce(mockNotifications);
            
            const result = await service.getCustomerEngagement(mobileNumber);
            
            expect(result.customerId).toBe(mobileNumber);
            expect(result.mobileNumber).toBe(mobileNumber);
            expect(result.totalMessagesReceived).toBe(2);
            expect(result.totalMessagesDelivered).toBe(2);
            expect(result.totalMessagesRead).toBe(2);
            expect(result.deliveryRate).toBe(100);
            expect(result.readRate).toBe(100);
            
            expect(result.campaignParticipation).toHaveLength(2);
            expect(result.campaignParticipation[0].campaignId).toBe('campaign1');
            expect(result.campaignParticipation[0].timestampISO).toBeDefined();
            
            expect(result.preferredMessageCategories).toContain(MessageCategory.PROMOTIONAL_OFFER);
            
            // Check ISO timestamps
            expect(result.lastMessageTimestampISO).toBeDefined();
            expect(result.lastDeliveryTimestampISO).toBeDefined();
            expect(result.lastReadTimestampISO).toBeDefined();
        });

        test('getCustomerEngagement should handle customer with no notifications', async () => {
            mockRepository.queryWithFilters.mockResolvedValueOnce([]);
            
            await expect(service.getCustomerEngagement('+919999999999')).rejects.toThrow(
                'No notifications found for customer +919999999999'
            );
        });
    });

    describe('Utility Methods', () => {
        test('incrementRetryCount should update retry count', async () => {
            mockRepository.getLog.mockResolvedValueOnce(mockNotificationLog);
            
            await service.incrementRetryCount('test-notification', 123456789);
            
            expect(mockRepository.updateLog.mock.calls.length).toBe(1);
            const updateCall = mockRepository.updateLog.mock.calls[0];
            expect(updateCall[0]).toBe('test-notification');
            expect(updateCall[1]).toBe(123456789);
            expect(updateCall[2].retryCount).toBe(1); // mockNotificationLog.retryCount + 1
        });

        test('incrementRetryCount should throw error if notification not found', async () => {
            mockRepository.getLog.mockResolvedValueOnce(null);
            
            await expect(service.incrementRetryCount('non-existent', 123456789))
                .rejects.toThrow('Notification log not found');
        });

        test('linkWithWebhookLog should update webhook log ID', async () => {
            await service.linkWithWebhookLog('test-notification', 123456789, 'webhook-123');
            
            expect(mockRepository.updateLog.mock.calls.length).toBe(1);
            const updateCall = mockRepository.updateLog.mock.calls[0];
            expect(updateCall[0]).toBe('test-notification');
            expect(updateCall[1]).toBe(123456789);
            expect(updateCall[2].webhookLogId).toBe('webhook-123');
        });

        test('getBusinessNotificationLogs should call repository method', async () => {
            const mockLogs = [mockNotificationLog];
            mockRepository.getLogsByBusiness.mockResolvedValueOnce(mockLogs);
            
            const result = await service.getBusinessNotificationLogs(
                'business-123',
                Date.now() - 86400000,
                Date.now()
            );
            
            expect(result).toEqual(mockLogs);
            expect(mockRepository.getLogsByBusiness.mock.calls.length).toBe(1);
            const getCall = mockRepository.getLogsByBusiness.mock.calls[0];
            expect(getCall[0]).toBe('business-123');
            expect(typeof getCall[1]).toBe('number');
            expect(typeof getCall[2]).toBe('number');
        });
    });

    describe('Error Handling', () => {
        test('getCampaignAnalytics should handle repository errors', async () => {
            mockRepository.getCampaignAnalyticsData.mockRejectedValueOnce(new Error('Database error'));
            
            await expect(service.getCampaignAnalytics('campaign-123'))
                .rejects.toThrow('Database error');
        });

        test('getCustomerEngagement should handle repository errors', async () => {
            mockRepository.queryWithFilters.mockRejectedValueOnce(new Error('Query error'));
            
            await expect(service.getCustomerEngagement('+919876543210'))
                .rejects.toThrow('Query error');
        });

        test('updateStatusFromWebhook should handle update failures', async () => {
            const webhookUpdate: WebhookNotificationUpdate = {
                whatsappMessageId: 'wamid.test123',
                status: NotificationStatus.DELIVERED,
                timestamp: Date.now(),
                recipientId: '+919876543210'
            };
            
            mockRepository.findByWhatsAppMessageId.mockResolvedValueOnce(mockNotificationLog);
            mockRepository.updateByWhatsAppMessageId.mockResolvedValueOnce(false);
            
            const result = await service.updateStatusFromWebhook(webhookUpdate);
            
            expect(result.failedUpdates).toBe(1);
            expect(result.successfulUpdates).toBe(0);
            expect(result.failedNotifications[0].notificationId).toBe(mockNotificationLog.notificationId);
            expect(result.failedNotifications[0].whatsappMessageId).toBe('wamid.test123');
            expect(result.failedNotifications[0].error).toBe('Failed to update notification');
        });
    });

    describe('ISO Timestamp Handling', () => {
        test('service should handle ISO timestamp conversion internally', async () => {
            // Test that the service uses the private toISTISOString method correctly
            // This is tested indirectly through the analytics methods which should include ISO timestamps
            expect(service).toBeDefined();
        });
    });
}); 