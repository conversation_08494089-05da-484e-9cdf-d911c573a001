import { initializeApp, cert, getApps, getApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';

/**
 * Initialize Firebase Admin SDK with environment variables for business console
 * This function ensures Firebase is only initialized once for the business console project
 * @returns Firestore instance for mnet-business-console project
 */
export function getFirebaseAdmin() {
    const appName = 'mnet-business-console';
    
    try {
        // Check if the business console app already exists
        const existingApp = getApp(appName);
        return getFirestore(existingApp);
    } catch (error) {
        // App doesn't exist, create it
        const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/@/g, '\n');
        
        const businessConsoleApp = initializeApp({
            credential: cert({
                projectId: process.env.FIREBASE_PROJECT_ID,
                clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
                privateKey: privateKey,
            }),
        }, appName);
        
        return getFirestore(businessConsoleApp);
    }
} 