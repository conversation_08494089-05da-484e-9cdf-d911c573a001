import { useEffect } from "react";
import { CommonMessage, Message } from "~/types/iframe";

/**
 * Adds a listener for `message` events with type-safe messages.
 *
 * @template TMessages - The union type of expected messages.
 * @param onMessage - Callback invoked with the message event when a message is received.
 */
const useMessageListener = <TMessages extends Message<string, CommonMessage>>(
  onMessage: (message: TMessages, event: MessageEvent) => void
) => {
  useEffect(() => {
    if (typeof window !== "undefined") {
      const handleMessage = (event: MessageEvent) => {
        try {
          const message = event.data as TMessages;
          onMessage(message, event);
        } catch (error) {
          console.error("Failed to process message", error);
        }
      };

      window.addEventListener("message", handleMessage);

      return () => {
        window.removeEventListener("message", handleMessage);
      };
    }
  }, [onMessage]);
};

export default useMessageListener;

/*

interface Message<TType extends string, TPayload> {
  type: TType;
  payload: TPayload;
}


type IframeLoadedMessage = Message<"IFRAME_LOADED", { success: boolean }>;
type CustomEventMessage = Message<"CUSTOM_EVENT", { data: string }>;

type AppMessage = IframeLoadedMessage | CustomEventMessage;

import { useEffect } from "react";

const usePostMessage = <TType extends string, TPayload>(
  message: Message<TType, TPayload> | null,
  targetOrigin: string = "*"
) => {
  useEffect(() => {
    if (message && typeof window !== "undefined") {
      window.parent.postMessage(message, targetOrigin);
    }
  }, [message, targetOrigin]);
};

export default usePostMessage;





import { useEffect } from "react";

const useMessageListener = <TMessages extends Message<string, any>>(
  onMessage: (message: TMessages, event: MessageEvent) => void
) => {
  useEffect(() => {
    if (typeof window !== "undefined") {
      const handleMessage = (event: MessageEvent) => {
        try {
          const message = event.data as TMessages;
          onMessage(message, event);
        } catch (error) {
          console.error("Failed to process message", error);
        }
      };

      window.addEventListener("message", handleMessage);

      return () => {
        window.removeEventListener("message", handleMessage);
      };
    }
  }, [onMessage]);
};

export default useMessageListener;







import React from "react";
import usePostMessage from "./hooks/usePostMessage";
import useMessageListener from "./hooks/useMessageListener";

const YourComponent = ({ iframeConfig }: { iframeConfig: any }) => {
  usePostMessage<"IFRAME_LOADED", { success: boolean }>(
    iframeConfig?.parent
      ? { type: "IFRAME_LOADED", payload: { success: true } }
      : null,
    "*"
  );

  useMessageListener<AppMessage>((message, event) => {
    if (message.type === "IFRAME_LOADED") {
      console.log("Iframe loaded with success:", message.payload.success);
    } else if (message.type === "CUSTOM_EVENT") {
      console.log("Custom event data:", message.payload.data);
    } else {
      console.warn("Unknown message type:", message);
    }
  });

  return <div>Your Component Content</div>;
};

export default YourComponent;





*/
