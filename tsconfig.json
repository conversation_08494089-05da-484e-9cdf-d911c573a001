{
  "compilerOptions": {
    "target": "es2022",
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "rootDir": "./src",
    "outDir": "./dist",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@services/*": ["services/*"],
      "@utils/*": ["utils/*"],
      "@middleware/*": ["middleware/*"],
      "@routes/*": ["routes/*"],
      "@controllers/*": ["controllers/*"],
      "@database/*": ["database/*"],
      "@entity/*": ["database/entity/*"],
      "@repository/*": ["database/repository/*"],
      "@constants": ["constants"],
      "@interfaces/*": ["interfaces/*"],
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules"]
}
