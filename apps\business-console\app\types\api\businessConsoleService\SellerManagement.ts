export interface Seller {
  id: number;
  name: string;
  enabled: boolean;
  businessId: number;
  walletBalance: number;
}

export interface smSellerArea {
  sellerAreaId: number;
  disabled: boolean;
  area: {
    id: number;
    name: string;
    district: string;
    state: string;
    polygon: string;
  };
}

export interface SellerConfig {
  id: number;
  name: string;
  enabled: boolean;
  auto_accept: boolean;
  auto_pack: boolean;
  auto_pickup: boolean;
  auto_dispatch: boolean;
  listing_seq: number;
  mininum_required_balance: number;
  allow_cod: boolean;
  minimum_order_qty: number;
  minimum_order_value: number;
  wa_enable: boolean;
  approx_pricing: boolean;
  approxPriceVisibility: boolean;
  strikeoff_enabled: boolean;
  item_pick_enabled: boolean;
  contract_price_enabled: boolean;
  delivery_type: string;
  fav_items_enabled: boolean;
  category_level: number;
  business_id: number;
  dispatch_time: string;
  delivery_time: string;
  approxDelDateVisibility: boolean;
  instantDeliveryTime?: number;
  booking_close_time: number;
  booking_open_time: number;
  is_pay_later_enabled: boolean;
  distanceBasedDel: boolean;
  deliveryDistance?: number | null;
  auto_activate: boolean;
  advance_booking_days: number;
  miSources?: string | null;
  t1Open?: number | null;
  t1Close?: number | null;
  t2Open?: number | null;
  t2Close?: number | null;
  t3Open?: number | null;
  t3Close?: number | null;
  menuId?: string | null;
  pos?: string | null;
  posCustId?: string | null;
  posRetName?: string | null;
  posRetContactNo?: string | null;
  posRetAddress?: string | null;
  outletId?: number | null;
  ondcDomain?: string | null;
  defaultOrderPrepTime?: number | null;
  spCustId?: string | null;
  spId?: number | null;
  sourceSystem?: string | null;
  logisticProvider?: string | null;
  packagingCharge?: number | null;
  packagingChargeType?: "PERCENTAGE" | "FIXED";
  packagingApplicableOn?: "NONE" | "ITEM" | "ORDER";
  platformFee:number|null,
  platformFeePerc:number|null,
  platformFeePkg:number|null,
  platform:"mNET"|"fM"
}

export interface Suppliers {
  supplierItemId: number,
  supplierName: string
}
export interface StateAndDistricts {
  district: string;
  state: string;
}

export interface MasterLocalities {
  id: number;
  name: string;
  district: string;
  state: string;
  polygon?: string;
}

export interface networkAgents {
  agentUserId: number;
  fullName: string;
  businessName: string;
  status: boolean;
}

export interface BankConfig {
  baBranch: String;
  baNumber: String;
  baVerifiedBy: String;
  lubyName: String;
  baIfsc: String;
  baName: String;
  lubyId: number;
  bid: number;
  baVerified: boolean;
  baBank: string;
}

export interface BusinessData {
  businessId: number;
  bankConfig: BankConfig;
  pcBasicPc: number;
  pcBasicPmFixed: number;
  pcBasicPmMin: number;
  salesCommPc: number;
  salesCommPkg: number;
  salesCommPmFixed: number;
  salesCommPmMin: number;
  agentCommPc: number;
  agentCommPkg: number;
}

export interface MyAddonGroupData {
  id?: number;
  internalName: string,
  displayName: string,
  active: boolean,
  description: string
}
export interface MyAddonData {
  id: number;
  name: string,
  diet: string,
  active: boolean
}
export interface MyVariationData {
  id?: number;
  internalName: string,
  displayName: string,
  groupName: string,
  active: boolean
}
export interface MyAddOnGroupAddOn {
  id: number,
  myAddOnGroupId: number;
  myAddOnId: number;
  myAddOnName: string;
  price: number;
  seq: number;
  active: boolean;
  groupName: string
}