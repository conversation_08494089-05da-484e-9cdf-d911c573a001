import React, { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import { MyVariationData } from "~/types/api/businessConsoleService/SellerManagement";
import { Form, useActionData, useFetcher } from "@remix-run/react";
import { Loader2, SquareX } from "lucide-react";
import { SelectedItem } from "~/types/api/businessConsoleService/MyItemList";
interface SelectedItemAddonsProps {
      isOpen: boolean;
      items: MyVariationData[] | [];
      onClose: () => void;
      header: string;
      groupData?: SelectedItem,
      sellerId?: number,
      groupId?: number,
      isEdit?: boolean

}

const SelectedItemVariation: React.FC<SelectedItemAddonsProps> = ({
      isOpen,
      items,
      onClose,
      header,
      groupData,
      sellerId,
      groupId,
      isEdit
}) => {
      const [selectedId, setSelectedId] = useState<string | number | null>(null);
      const [searchTerm, setSearchTerm] = useState('');
      const [filteredVariation, setFilteredVariation] = useState<MyVariationData[]>(items)
      const [choosenAddon, setChoosenAddon] = useState<boolean>(false);
      const [choossenAddonName, setChoosenAddonName] = useState<string>('')
      const [formData, setFormData] = useState({
            id: groupData?.id,
            sId: groupData?.sId || selectedId,
            name: groupData?.name || "",
            groupName: groupData?.groupName || "",
            price: groupData?.price || 0,
            strikeoffPrice: groupData?.strikeoffPrice || 0,
            seq: groupData?.seq || 0,
      });
      useEffect(() => {
            if (searchTerm.length >= 3) {
                  setFilteredVariation(
                        items.filter(item =>
                              item.displayName.toLowerCase().includes(searchTerm.toLowerCase())
                        )
                  );
            } else {
                  setFilteredVariation(items);
            }
      }, [searchTerm, items]);
      useEffect(() => {
            if (!isOpen) {
                  setSelectedId(null);
                  setSearchTerm('');
                  setChoosenAddon(false);
                  setChoosenAddonName('');
            }
      }, [!isOpen]);

      useEffect(() => {
            if (groupData) {
                  setFormData({
                        id: groupData?.id || 0,
                        sId: groupData.sId || '',
                        name: groupData.name || '',
                        groupName: groupData.groupName || '',
                        price: groupData.price || 0,
                        strikeoffPrice: groupData.strikeoffPrice || 0,
                        seq: groupData.seq || 0,
                  });
                  setSelectedId(groupData.sId || null);
                  setChoosenAddon(!!groupData.sId);
                  setChoosenAddonName(groupData.name || '');
            }
      }, [groupData]);

      const handleSelect = (variation: MyVariationData) => {
            setSelectedId(variation?.id ?? 0);
            setChoosenAddon(true)
            setChoosenAddonName(variation.displayName)

      }
      const deselectAddongroupMap = () => {
            setSelectedId(null)
            setChoosenAddon(false)
      }

      const fetcher = useFetcher();
      const loading = fetcher.state != "idle";
      const actionData = useActionData<{ sucess?: boolean; error?: string }>();

      useEffect(() => {
            if (actionData?.sucess) {

                  console.log(actionData.sucess, "ppppppppp")
                  onClose();
            }
      }, [actionData, onClose]);

      if (!isOpen) return null;
      return (
            <Dialog open={isOpen} onOpenChange={onClose}>

                  <DialogContent className="w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl">
                        <DialogTitle className="text-2xl font-bold text-gray-900 mb-4">{header}</DialogTitle>
                        <div className="space-y-6">
                              {choosenAddon === false && selectedId === null && <>
                                    <div>
                                          <input
                                                placeholder="Search by Addon Name"
                                                type="search"
                                                className="w-full p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors"
                                                autoFocus
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                          />
                                    </div>
                                    <div className="mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                          <ul className="space-y-2">
                                                {filteredVariation.length === 0 ? (
                                                      <p className="p-4 text-gray-500 text-center">No add-ons-group found</p>
                                                ) : (
                                                      filteredVariation.map((item) => (
                                                            <li key={item.id} className="flex items-center gap-3">
                                                                  <input
                                                                        type="checkbox"
                                                                        id={`item-${item.id}`}
                                                                        name="selectedItem"
                                                                        value={item.id}
                                                                        checked={selectedId === item.id}
                                                                        onChange={() => handleSelect(item)}
                                                                        className="h-5 w-5 text-blue-600 focus:ring-blue-500 rounded"
                                                                  />
                                                                  <label
                                                                        htmlFor={`item-${item.id}`}
                                                                        className={`cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id ? 'bg-blue-50 border-blue-200' : 'border-gray-200'} text-gray-800 hover:bg-gray-50 transition-colors`}
                                                                  >
                                                                        {item?.displayName} <span className="text-gray-500">({item?.internalName})</span>
                                                                  </label>
                                                            </li>
                                                      ))
                                                )}
                                          </ul>
                                    </div>
                              </>
                              }

                              {choosenAddon && selectedId && <div className="space-y-4">
                                    {selectedId && (
                                          <div className="flex items-center justify-between bg-blue-50 p-3 rounded-lg">
                                                <p className="font-medium text-gray-800 truncate max-w-[80%]">{choossenAddonName}</p>
                                                <SquareX
                                                      color="red"
                                                      className="cursor-pointer hover:scale-110 transition-transform"
                                                      onClick={() => deselectAddongroupMap()}
                                                />
                                          </div>
                                    )}

                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                          <div>
                                                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                                                      Name
                                                </label>
                                                <input
                                                      type="text"
                                                      name="name"
                                                      id="name"
                                                      value={formData.name}
                                                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                                                      placeholder="Enter name"
                                                />
                                          </div>
                                          <div>
                                                <label htmlFor="seq" className="block text-sm font-medium text-gray-700 mb-1">
                                                      Sequence
                                                </label>
                                                <input
                                                      type="number"
                                                      id="seq"
                                                      name="seq"
                                                      value={formData.seq}
                                                      onChange={(e) => setFormData({ ...formData, seq: Number(e.target.value) })}
                                                      min="0"
                                                      required
                                                      className="w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
                                                />
                                          </div>
                                          <div>
                                                <label htmlFor="groupName" className="block text-sm font-medium text-gray-700">
                                                      groupName                                                </label>
                                                <input
                                                      type="text"
                                                      name="groupName"
                                                      id="groupName"
                                                      value={formData.groupName}
                                                      onChange={(e) => setFormData({ ...formData, groupName: e.target.value })}
                                                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                                                />
                                          </div>
                                          <div>
                                                <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                                                      price
                                                </label>
                                                <input
                                                      type="number"
                                                      name="price"
                                                      id="price"
                                                      value={formData.price}
                                                      onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
                                                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                                                      min="0"
                                                />
                                          </div>
                                          <div>
                                                <label htmlFor="strikeoffPrice" className="block text-sm font-medium text-gray-700">
                                                      strikeoffPrice
                                                </label>
                                                <input
                                                      type="number"
                                                      name="strikeoffPrice"
                                                      id="strikeoffPrice"
                                                      value={formData.strikeoffPrice}
                                                      onChange={(e) => setFormData({ ...formData, strikeoffPrice: Number(e.target.value) })}
                                                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                                                      min="0"
                                                />
                                          </div>



                                    </div>
                              </div>}
                        </div>
                        <Form method="POST" className="mt-6 flex flex-col sm:flex-row gap-3 justify-end">
                              <input type="hidden" name="sId" value={selectedId?.toString()} readOnly />
                              <input type="hidden" name="itemId" value={groupId?.toString()} readOnly />
                              <input type="hidden" name="groupName" value={formData.groupName?.toString()} readOnly />
                              <input type="hidden" name="name" value={formData.name?.toString()} readOnly />
                              <input type="hidden" name="seq" value={formData.seq?.toString()} readOnly />
                              <input type="hidden" name="price" value={formData.price?.toString()} readOnly />
                              <input type="hidden" name="strikeoffPrice" value={formData.strikeoffPrice?.toString()} readOnly />
                              <input type="hidden" name="sellerId" value={sellerId?.toString()} readOnly />
                              <input type="hidden" name="addonName" value={choossenAddonName?.toString()} readOnly />
                              <input type="hidden" name="actionType" value={"actionItemVariation"} readOnly />
                              <input type="hidden" name="mode" value={isEdit ? "EditMode" : ""} />
                              {isEdit && <input type="hidden" name="selectedId" value={groupData?.id?.toString()} readOnly />
                              }
                              <button
                                    onClick={onClose}
                                    className="w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium"
                              >
                                    Cancel
                              </button>
                              <button
                                    type="submit"
                                    disabled={selectedId === null || loading}
                                    className={`w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${selectedId === null ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
                              >

                                    {loading ? (
                                          <>
                                                <Loader2 className="animate-spin h-5 w-5 mr-2" />
                                                Submitting...
                                          </>
                                    ) : (
                                          'Confirm'
                                    )}

                              </button>
                        </Form>
                  </DialogContent>
            </Dialog>
      );
};

export default SelectedItemVariation;
