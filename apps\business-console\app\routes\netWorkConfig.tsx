import { useFetcher } from "@remix-run/react";
import { Dayjs } from "dayjs";
import { Edit, Pencil, Save, Trash, X } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { Button } from "~/components/ui/button";
import ImageUploadComponent from "~/components/ui/imageUploadComponent";
import { Label } from "~/components/ui/label";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { NetworkConfig } from "~/types/api/businessConsoleService/Network";

interface NetworkConfigProps {
  networkConfig: NetworkConfig[];
  networkName: string;
  onAttributeUpdate: (attribute: string, value: any, domainId: number) => void;
}

export default function NetWorkConfig({
  networkConfig,
  networkName,
  onAttributeUpdate,
}: NetworkConfigProps) {
  const [businessConfigEditable, setBusinessConfigEditable] = useState(false);
  const [editingStartPage, setEditingStartPage] = useState(false);
  const [pendingConfig, setPendingConfig] = useState<Partial<NetworkConfig>>({});
  const keyMapping: Record<keyof NetworkConfig, string> = {
    id: "id",
    domain: "domain",
    businessLogo: "business_logo",
    homePageBanner: "home_page_banner",
    pwaAppIcon: "pwa_app_icon",
    footerAppIcon: "footer_app_icon",
    networkId: "networkId",
    multiSeller: "multi_seller",
    defaultSellerId: "defaultSellerId",
    wabEnabled: "wab_enabled",
    wabMobileNumber: "wab_mobile_number",
    defaultStartPage: "default_start_page",
    imageBaseUrl: "imageBaseUrl",
    networkType: "",
    wabDatasetId: "wabDatasetId",
  };

  // Modified: Reset states on cancel
  const handleBusinessConfig = useCallback(() => {
    setBusinessConfigEditable((prevState) => {
      if (prevState) {
        setPendingConfig({});
        setVisibleSaveButtons({});
      }
      return !prevState;
    });
  }, []);

  useEffect(() => {
    setConfig(networkConfig);
  }, [networkConfig]);

  const [BasicConfig, setBasicConfig] = useState(false);

  // Modified: Reset states on cancel
  const BasicConFigEdit = useCallback(() => {
    setBasicConfig((prev) => {
      if (prev) {
        setPendingConfig({});
        setVisibleSaveButtons({});
      }
      return !prev;
    });
  }, []);

  const [visibleSaveButtons, setVisibleSaveButtons] = useState<
    Record<string, boolean>
  >({});
  const [config, setConfig] = useState<NetworkConfig[]>(networkConfig);
  const [showsave, setShowsave] = useState(false);

  const handleConfigChange = (
    key: keyof NetworkConfig,
    value: string | number | boolean | Dayjs | null
  ) => {
    console.log(value, key, "Config Change Triggered");

    setPendingConfig((prev) => {
      let newValue: string | number | boolean | "" = value as any;

      if (typeof value === "string" && (value === "yes" || value === "no")) {
        newValue = value === "yes";
      } else if (typeof value === "string" && !isNaN(Number(value))) {
        newValue = Number(value);
      } else if (value === null || value === undefined) {
        newValue = "";
      }

      return {
        ...prev,
        [key]: newValue,
      };
    });

    setVisibleSaveButtons((prev) => ({
      ...prev,
      [key]: true,
    }));
  };

  const fetcher = useFetcher();

  const handleSave = async (
    key: keyof NetworkConfig,
    value?: string | number | boolean
  ) => {
    try {
      setConfig((prevConfig) => ({
        ...prevConfig,
        [key]: value !== undefined ? value : pendingConfig[key] ?? "",
      }));

      setVisibleSaveButtons((prev) => ({
        ...prev,
        [key]: false,
      }));
      setShowsave(false);

      let hasError = false;

      const newValue: string | number | boolean =
        value !== undefined ? value : pendingConfig[key] ?? "";

      try {
        await onAttributeUpdate(
          keyMapping[key as keyof NetworkConfig],
          typeof newValue === "string" ? encodeURIComponent(newValue) : newValue,
          networkConfig[0]?.id ?? 0
        );
      } catch (error) {
        console.error(`Failed to update ${key}:`, error);
        hasError = true;
      }

      if (hasError) {
        alert("Some settings could not be saved. Please try again.");
      } else {
        setPendingConfig({});
      }
    } catch (error) {
      console.error("Error saving config:", error);
      alert("Failed to save settings. Please try again later.");
    }
  };

  const handleImageUpload = (key: keyof NetworkConfig, url: string) => {
    setPendingConfig((prev) => ({
      ...prev,
      [key]: url,
    }));

    setVisibleSaveButtons((prev) => ({
      ...prev,
      [key]: true,
    }));
  };

  const [editingFooter, setEditingFooter] = useState(false);
  const [editingBanners, setEditingBanners] = useState(false);
  const [showSaveFooter, setShowSaveFooter] = useState(false);
  const [showSaveBanners, setShowSaveBanners] = useState(false);
  const [editingPlaystore, setEditingPlaystore] = useState(false);
  const [showSavePlaystore, setShowSavePlaystore] = useState(false);
  const [editingBusinessLogo, setEditingBusinessLogo] = useState(false);
  const [showSaveBusinessLogo, setShowSaveBusinessLogo] = useState(false);

  const Loading = fetcher.state !== "idle";

  return (
    <div className="flex flex-col gap-4 border bg-white rounded-xl border-neutral-200 my-4 p-4">
      {Loading && <SpinnerLoader loading={Loading} />}
      <div className="flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow">
        <div className="flex flex-row justify-between">
          <div className="text-lg font-semibold text-typography-700">
            Basic Configurations
          </div>
          <button
            className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800"
            onClick={() => BasicConFigEdit()}
          >
            {BasicConfig ? (
              <>
                <X className="h-4 w-4" />
                Cancel
              </>
            ) : (
              <>
                <Edit className="h-4 w-4" />
                Edit
              </>
            )}
          </button>
        </div>
        <div className="flex gap-2">
          <div className="p-2 border border-neutral-50 rounded-sm">
            {!editingBusinessLogo ? (
              networkConfig[0]?.businessLogo ? (
                <div className="flex flex-row gap-1 items-center">
                  <img
                    src={networkConfig[0]?.businessLogo}
                    className="w-32 rounded-md border border-neutral-200 transition-transform hover:scale-105"
                    alt="Business Logo"
                  />
                  <button onClick={() => setEditingBusinessLogo(true)}>
                    <Pencil className="h-5 w-5 text-blue-600 hover:text-blue-800" />
                  </button>
                  <button
                    onClick={() => {
                      handleSave("businessLogo", '""');
                    }}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash className="h-5 w-5" />
                  </button>
                </div>
              ) : (
                <div className="text-gray-500 flex items-center gap-2">
                  No Business Logo
                  <button onClick={() => setEditingBusinessLogo(true)}>
                    <Pencil className="h-5 w-5 text-blue-600 hover:text-blue-800" />
                  </button>
                </div>
              )
            ) : (
              <div className="flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm">
                <div className="bg-white border border-dashed border-neutral-300 rounded-md p-4">
                  <ImageUploadComponent
                    onChange={(url: string | string[]) => {
                      setShowSaveBusinessLogo(true);
                      const businessLogoUrl = Array.isArray(url) ? url[0] : url;
                      handleImageUpload("businessLogo", businessLogoUrl);
                      setEditingBusinessLogo(false);
                    }}
                  />
                </div>
                <div className="flex flex-row items-center justify-center gap-4">
                  <Button
                    className="bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm"
                    onClick={() => {
                      setEditingBusinessLogo(false);
                      setShowSaveBusinessLogo(false);
                      // Modified: Reset pendingConfig and visibleSaveButtons
                      setPendingConfig((prev) => ({ ...prev, businessLogo: undefined }));
                      setVisibleSaveButtons((prev) => ({ ...prev, businessLogo: false }));
                    }}
                  >
                    <X className="h-5 w-5" /> Cancel
                  </Button>
                </div>
              </div>
            )}
            {showSaveBusinessLogo && (
              <div className="flex flex-row items-center justify-center mt-2">
                <Button
                  className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm"
                  onClick={() => {
                    handleSave("businessLogo");
                    setEditingBusinessLogo(false);
                    setShowSaveBusinessLogo(false);
                  }}
                >
                  <Save className="h-5 w-5" /> Save
                </Button>
              </div>
            )}
          </div>
          <div className="flex flex-col gap-0">
            <div className="text-sm text-typography-300">
              NetWork Id : {networkConfig[0]?.networkId}
            </div>
            <div className="text-md text-typography-600">
              NetWork Name : {networkName}
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-0">
          <div className="text-sm text-typography-300">
            Domain ID : {networkConfig[0]?.id}
          </div>
          <div className="flex flex-col gap-2 text-md text-typography-400">
            <div className="flex flex-row gap-4 items-center">
              <div className="text-md text-typography-500 w-sm">
                Domain URL :
              </div>
              <span className="font-semibold text-typography-800 flex gap-2 items-center">
                <input
                  type="text"
                  value={(pendingConfig.domain ?? networkConfig[0]?.domain) || ""}
                  onChange={(e) => handleConfigChange("domain", e.target.value)}
                  className="border border-neutral-400 rounded-md p-1 px-2"
                  disabled={!BasicConfig}
                />
              </span>
              {BasicConfig && visibleSaveButtons["domain"] && (
                <button
                  className="flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold"
                  onClick={() => handleSave("domain" as keyof NetworkConfig)}
                >
                  <Save className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow">
        <div className="flex w-full justify-between">
          <div className="abstractart-typography-700">
            Business Configurations
          </div>
          <button
            className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800"
            onClick={() => handleBusinessConfig()}
          >
            {businessConfigEditable ? (
              <>
                <X className="h-4 w-4" />
                Cancel
              </>
            ) : (
              <>
                <Edit className="h-4 w-4" />
                Edit
              </>
            )}
          </button>
        </div>
        {[
          { label: "Are Multiple Sellers Allowed?  :", key: "multiSeller" },
          { label: "Is WhatsApp Enabled?  :", key: "wabEnabled" },
        ].map(({ label, key }) => (
          <div
            key={key}
            className="flex gap-4 items-center text-md text-typography-400"
          >
            <Label className="w-[400px] text-md text-typography-400">
              {label}
            </Label>
            <RadioGroup
              value={
                (pendingConfig[key as keyof NetworkConfig] ??
                  config[0]?.[key as keyof NetworkConfig])
                  ? "yes"
                  : "no"
              }
              onValueChange={(val) =>
                handleConfigChange(key as keyof NetworkConfig, val)
              }
              className="flex gap-4 items-center text-md font-semibold text-typography-800"
              disabled={!businessConfigEditable}
            >
              <div className="flex items-center gap-2">
                <RadioGroupItem id={`${key}-yes`} value="yes" />
                <Label htmlFor={`${key}-yes`}>Yes</Label>
              </div>
              <div className="flex items-center gap-2">
                <RadioGroupItem id={`${key}-no`} value="no" />
                <Label htmlFor={`${key}-no`}>No</Label>
              </div>
            </RadioGroup>
            {businessConfigEditable && visibleSaveButtons[key] && (
              <div className="flex items-center gap-2">
                <button
                  className="flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold"
                  onClick={() => handleSave(key as keyof NetworkConfig)}
                >
                  <Save className="h-5 w-5" />
                </button>
              </div>
            )}
          </div>
        ))}

        {/* Modified: WhatsApp number input with 10-digit numeric validation */}
        <div className="flex flex-col gap-2 text-md text-typography-400">
          <div className="flex gap-4 items-center">
            <div className="text-md text-typography-500 w-[400px]">
              WhatsApp Contact Number
            </div>
            <span className="font-semibold text-typography-800 flex gap-2 items-center">
              <input
                type="tel"
                value={
                  (pendingConfig.wabMobileNumber ??
                    config[0]?.wabMobileNumber ??
                    "")
                }
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d{0,10}$/.test(value)) {
                    handleConfigChange("wabMobileNumber", value);
                  }
                }}
                className="border border-neutral-400 rounded-md p-1 px-2"
                disabled={!businessConfigEditable}
                maxLength={10}
                placeholder="Enter 10-digit number"
                pattern="[0-9]{10}"
                onKeyDown={(e) => {
                  if (
                    !/[\d]/.test(e.key) &&
                    !["Backspace", "ArrowLeft", "ArrowRight", "Delete", "Tab"].includes(e.key)
                  ) {
                    e.preventDefault();
                  }
                }}
              />
            </span>
            {businessConfigEditable && visibleSaveButtons["wabMobileNumber"] && (
              <button
                className="flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold"
                onClick={() => {
                  const value = pendingConfig.wabMobileNumber ?? config[0]?.wabMobileNumber ?? "";
                  if (/^\d{10}$/.test(value)) {
                    handleSave("wabMobileNumber" as keyof NetworkConfig);
                  } else {
                    alert("Please enter a valid 10-digit WhatsApp number.");
                  }
                }}
              >
                <Save className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-2 text-md text-typography-400">
          <div className="flex gap-4 items-center">
            <div className="text-md text-typography-500 w-[400px]">
              WhatsApp Pixel Id
            </div>
            <span className="font-semibold text-typography-800 flex gap-2 items-center">
              <input

                value={
                  (pendingConfig.wabDatasetId ??
                    config[0]?.wabDatasetId ??
                    "")
                }
                onChange={(e) => {
                  const value = e.target.value;

                  handleConfigChange("wabDatasetId", value);

                }}
                className="border border-neutral-400 rounded-md p-1 px-2"
                disabled={!businessConfigEditable}
                placeholder="Enter whatsapp pixel id"
                onKeyDown={(e) => {
                  if (
                    !/[\d]/.test(e.key) &&
                    !["Backspace", "ArrowLeft", "ArrowRight", "Delete", "Tab"].includes(e.key)
                  ) {
                    e.preventDefault();
                  }
                }}
              />
            </span>
            {businessConfigEditable && visibleSaveButtons["wabDatasetId"] && (
              <button
                className="flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold"
                onClick={() => {
                  const value = pendingConfig.wabDatasetId ?? config[0]?.wabDatasetId ?? "";
                  handleSave("wabDatasetId" as keyof NetworkConfig);


                }}
              >
                <Save className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow">
        <div className="flex flex-row justify-between">
          <div className="text-lg font-semibold text-typography-700">
            App Configurations
          </div>
        </div>

        <div className="text-md text-typography-600 flex gap-4">
          Home Page:
          {!editingStartPage ? (
            <span className="font-semibold text-typography-800">
              {networkConfig[0]?.defaultStartPage === "chooseitems"
                ? "Item SRP Page"
                : "Trips Page"}
            </span>
          ) : (
            <select
              value={
                (pendingConfig.defaultStartPage ??
                  networkConfig[0]?.defaultStartPage) || "chooseitems"
              }
              onChange={(e) =>
                handleConfigChange("defaultStartPage", e.target.value)
              }
              className="border border-gray-300 rounded-md px-2 py-1"
            >
              <option value="chooseitems">Item SRP Page</option>
              <option value="home">Trips Page</option>
            </select>
          )}
          <button onClick={() => setEditingStartPage(!editingStartPage)}>
            {!editingStartPage ? (
              <Pencil className="h-5 w-5 text-blue-600 hover:text-blue-800" />
            ) : (
              <X className="h-5 w-5 text-gray-600 hover:text-gray-800" />
            )}
          </button>
          {visibleSaveButtons.defaultStartPage && (
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm"
              onClick={() => {
                handleSave("defaultStartPage");
                setEditingStartPage(false);
              }}
            >
              <Save className="h-5 w-5" /> Save
            </Button>
          )}
        </div>

        <div className="text-md text-typography-600 flex flex-row gap-2">
          <div className="flex items-center gap-2">LoginImage:</div>
          {!editingBanners ? (
            networkConfig[0]?.homePageBanner ? (
              networkConfig[0]?.homePageBanner?.split(",").map((banner, index) => (
                <div key={index} className="flex gap-4 items-center">
                  <div>{index + 1}</div>
                  <div className="p-2 border border-neutral-50 rounded-md shadow-sm">
                    <img
                      src={banner}
                      className="h-32 rounded-md border border-neutral-200 transition-transform hover:scale-105"
                      alt={`Banner ${index + 1}`}
                    />
                  </div>
                  <button onClick={() => setEditingBanners(true)}>
                    <Pencil className="h-5 w-5 text-blue-600 hover:text-blue-800" />
                  </button>
                  <button
                    onClick={() => {
                      handleSave("homePageBanner", '""');
                    }}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash className="h-5 w-5" />
                  </button>
                </div>
              ))
            ) : (
              <div className="flex flex-row gap-2">
                <div className="text-md text-typography-200">No Banner Active</div>
                <button onClick={() => setEditingBanners(true)}>
                  <Pencil className="h-5 w-5 text-blue-600 hover:text-blue-800" />
                </button>
              </div>
            )
          ) : (
            <div className="flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm">
              <div className="bg-white border border-dashed border-neutral-300 rounded-md p-4">
                <ImageUploadComponent
                  multiple={false}
                  onChange={(urls: string | string[]) => {
                    setShowSaveBanners(true);
                    const bannerUrls = Array.isArray(urls) ? urls.join(",") : urls;
                    handleImageUpload("homePageBanner", bannerUrls);
                    setEditingBanners(false);
                  }}
                />
              </div>
              <div className="flex flex-row justify-center items-center gap-4">
                <Button
                  className="bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm"
                  onClick={() => {
                    setEditingBanners(false);
                    setShowSaveBanners(false);
                    // Modified: Reset pendingConfig and visibleSaveButtons
                    setPendingConfig((prev) => ({ ...prev, homePageBanner: undefined }));
                    setVisibleSaveButtons((prev) => ({ ...prev, homePageBanner: false }));
                  }}
                >
                  <X className="h-5 w-5" /> Cancel
                </Button>
              </div>
            </div>
          )}
          {showSaveBanners && (
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm"
              onClick={() => {
                handleSave("homePageBanner");
                setEditingBanners(false);
                setShowSaveBanners(false);
              }}
            >
              <Save className="h-5 w-5" /> Save
            </Button>
          )}
        </div>

        <div className="text-md text-typography-600 flex gap-4">
          <div>Playstore App Icon :</div>
          <div className="p-2 border border-neutral-50 rounded-md shadow-sm">
            {!editingPlaystore ? (
              networkConfig[0]?.pwaAppIcon ? (
                <div className="flex flex-row gap-1 items-center">
                  <img
                    src={networkConfig[0]?.pwaAppIcon}
                    className="h-32 rounded-md border border-neutral-200 transition-transform hover:scale-105"
                    alt="Playstore Icon"
                  />
                  <button onClick={() => setEditingPlaystore(true)}>
                    <Pencil className="h-5 w-5 text-blue-600 hover:text-blue-800" />
                  </button>
                  <button
                    onClick={() => {
                      handleSave("pwaAppIcon", '""');
                    }}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash className="h-5 w-5" />
                  </button>
                </div>
              ) : (
                <div className="text-gray-500 flex items-center gap-2">
                  No Playstore Icon
                  <button onClick={() => setEditingPlaystore(true)}>
                    <Pencil className="h-5 w-5 text-blue-600 hover:text-blue-800" />
                  </button>
                </div>
              )
            ) : (
              <div className="flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm">
                <div className="bg-white border border-dashed border-neutral-300 rounded-md p-4">
                  <ImageUploadComponent
                    onChange={(url: string | string[]) => {
                      setShowSavePlaystore(true);
                      const playstoreUrl = Array.isArray(url) ? url[0] : url;
                      handleImageUpload("pwaAppIcon", playstoreUrl);
                      setEditingPlaystore(false);
                    }}
                  />
                </div>
                <div className="flex flex-row justify-center items-center gap-4">
                  <Button
                    className="bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm"
                    onClick={() => {
                      setEditingPlaystore(false);
                      setShowSavePlaystore(false);
                      // Modified: Reset pendingConfig and visibleSaveButtons
                      setPendingConfig((prev) => ({ ...prev, pwaAppIcon: undefined }));
                      setVisibleSaveButtons((prev) => ({ ...prev, pwaAppIcon: false }));
                    }}
                  >
                    <X className="h-5 w-5" /> Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
          {showSavePlaystore && (
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm"
              onClick={() => {
                handleSave("pwaAppIcon");
                setEditingPlaystore(false);
                setShowSavePlaystore(false);
              }}
            >
              <Save className="h-5 w-5" /> Save
            </Button>
          )}
        </div>

        <div className="text-md text-typography-600 flex gap-4">
          <div>In-App Footer Logo :</div>
          <div className="p-2 border border-neutral-50 rounded-md shadow-sm">
            {!editingFooter ? (
              networkConfig[0]?.footerAppIcon ? (
                <div className="flex flex-row gap-1 items-center">
                  <img
                    src={networkConfig[0]?.footerAppIcon}
                    className="h-32 rounded-md border border-neutral-200 transition-transform hover:scale-105"
                    alt="Footer Logo"
                  />
                  <button onClick={() => setEditingFooter(true)}>
                    <Pencil className="h-5 w-5 text-blue-600 hover:text-blue-800" />
                  </button>
                  <button
                    onClick={() => {
                      handleSave("footerAppIcon", '""');
                    }}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash className="h-5 w-5" />
                  </button>
                </div>
              ) : (
                <div className="text-gray-500 flex items-center gap-2">
                  No Footer Logo
                  <button onClick={() => setEditingFooter(true)}>
                    <Pencil className="h-5 w-5 text-blue-600 hover:text-blue-800" />
                  </button>
                </div>
              )
            ) : (
              <div className="flex flex-col gap-4 bg-gray-50 p-4 rounded-lg shadow-sm">
                <div className="bg-white border border-dashed border-neutral-300 rounded-md p-4">
                  <ImageUploadComponent
                    onChange={(url: string | string[]) => {
                      setShowSaveFooter(true);
                      const footerUrl = Array.isArray(url) ? url[0] : url;
                      handleImageUpload("footerAppIcon", footerUrl);
                      setEditingFooter(false);
                    }}
                  />
                </div>
                <div className="flex flex-row justify-center items-center gap-4">
                  <Button
                    className="bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm"
                    onClick={() => {
                      setEditingFooter(false);
                      setShowSaveFooter(false);
                      // Modified: Reset pendingConfig and visibleSaveButtons
                      setPendingConfig((prev) => ({ ...prev, footerAppIcon: undefined }));
                      setVisibleSaveButtons((prev) => ({ ...prev, footerAppIcon: false }));
                    }}
                  >
                    <X className="h-5 w-5" /> Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
          {showSaveFooter && (
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 shadow-sm"
              onClick={() => {
                handleSave("footerAppIcon");
                setEditingFooter(false);
                setShowSaveFooter(false);
              }}
            >
              <Save className="h-5 w-5" /> Save
            </Button>
          )}
        </div>
      </div>
    </div >


  )
}