import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, useSearch<PERSON>ara<PERSON>, useNavigation } from "@remix-run/react";
import { json } from "@remix-run/node";
import { useState, useEffect } from "react";
import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { TicketStatusBadge } from "~/components/ui/ticket-status-badge";
import { TicketNotesDialog } from "~/components/ui/ticket-notes-dialog";
import { TicketStatusDialog } from "~/components/ui/ticket-status-dialog";
import { Ticket, TicketNote, SupportTicketStatus } from "~/types/api/businessConsoleService/Tickets";
import { getTickets, getTicketNotes } from "~/services/ticketService";
import { getTimeSinceCreation } from "~/utils/ticketUtils";
import { useToast } from "~/hooks/use-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { DatePicker } from "~/components/ui/date-picker";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const url = new URL(request.url);
    
    // Extract all filter parameters from URL search params
    const ticketId = url.searchParams.get('ticketId') ? Number(url.searchParams.get('ticketId')) : null;
    const userId = url.searchParams.get('userId') ? Number(url.searchParams.get('userId')) : null;
    const status = url.searchParams.get('status') ? 
      url.searchParams.getAll('status') as SupportTicketStatus[] : 
      null;
    const fromDate = url.searchParams.get('fromDate') || null;
    const toDate = url.searchParams.get('toDate') || null;
    const pageNo = Number(url.searchParams.get('page') || '0');
    const pageSize = Number(url.searchParams.get('pageSize') || '10');
    
    // Pass all parameters to the API
    const ticketsResponse = await getTickets(
      request, 
      {
        ticketId,
        userId,
        status,
        fromDate,
        toDate,
        pageNo,
        pageSize,
      }
    );
    
    return json({ 
      tickets: ticketsResponse.data || [], 
      totalCount: ticketsResponse?.data?.length || 0,
      pageNo,
      pageSize
    });
  } catch (error) {
    console.error("Error loading tickets:", error);
    return json({ 
      tickets: [], 
      totalCount: 0, 
      pageNo: 0, 
      pageSize: 10, 
      error: "Failed to load tickets" 
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const { _action } = Object.fromEntries(formData);
  
  try {
    // Get notes for a ticket
    if (_action === "getNotes") {
      const ticketId = Number(formData.get("ticketId"));
      
      if (!ticketId) {
        return json({ success: false, error: "Missing ticket ID" });
      }
      
      const notesResponse = await getTicketNotes(ticketId, request);
      return json({ 
        success: true, 
        action: "getNotes", 
        notes: notesResponse.data
      });
    }
    
    return json({ success: false, error: "Invalid action" });
  } catch (error) {
    console.error("Error processing action:", error);
    return json({ success: false, error: "Failed to process action" });
  }
};

export default function TicketsPage() {
  const { tickets, totalCount, pageNo, pageSize } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const { toast } = useToast();
  
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [notesDialogOpen, setNotesDialogOpen] = useState(false);
  const [ticketNotes, setTicketNotes] = useState<TicketNote[]>([]);
  const [isLoadingNotes, setIsLoadingNotes] = useState(false);
  
  // Listen for URL change or other state changes that should trigger refresh
  useEffect(() => {
    if (!notesDialogOpen) {
      setTicketNotes([]);
    }
  }, [notesDialogOpen]);

  // Apply client-side search filter to tickets (for quick filtering without API call)
  const filteredTickets = tickets.filter((ticket) => {
    const query = searchQuery.toLowerCase();
    return (
      ticket.userName.toLowerCase().includes(query) ||
      ticket.userMobileNo.toLowerCase().includes(query) ||
      ticket.sellerName?.toLowerCase().includes(query) ||
      ticket.description.toLowerCase().includes(query) ||
      ticket.userType.toLowerCase().includes(query) ||
      ticket.ticketType?.toLowerCase().includes(query)
    );
  });
  
  const handleOpenNotesDialog = async (ticket: Ticket) => {
    setSelectedTicket(ticket);
    setNotesDialogOpen(true);
    await fetchTicketNotes(ticket.ticketId);
  };
  
  const fetchTicketNotes = async (ticketId: number) => {
    setIsLoadingNotes(true);
    
    try {
      // Get notes for the selected ticket
      const form = new FormData();
      form.append("_action", "getNotes");
      form.append("ticketId", ticketId.toString());
      
      const response = await fetch("/home/<USER>", {
        method: "POST",
        body: form
      });
      
      const data = await response.json();
      
      if (data.success && data.notes) {
        setTicketNotes(data.notes);
        if (notesDialogOpen) {
          toast({
            title: "Notes Loaded",
            description: "Ticket notes loaded successfully",
            variant: "default"
          });
        }
      } else {
        setTicketNotes([]);
        toast({
          title: "Error",
          description: data.error || "Failed to load notes",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error fetching notes:", error);
      toast({
        title: "Error",
        description: "Failed to load ticket notes",
        variant: "destructive"
      });
      setTicketNotes([]);
    } finally {
      setIsLoadingNotes(false);
    }
  };
  
  const handleOpenStatusDialog = (ticket: Ticket) => {
    setSelectedTicket(ticket);
    setStatusDialogOpen(true);
  };
  
  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    if (value === "ALL") {
      searchParams.delete("status");
    } else {
      searchParams.set("status", value);
    }
    searchParams.set("page", "0"); // Reset to first page
    setSearchParams(searchParams);
  };
  
  // Handle date filter changes
  const handleFromDateChange = (date: Date | null) => {
    if (date) {
      searchParams.set("fromDate", date.toISOString().split('T')[0]);
    } else {
      searchParams.delete("fromDate");
    }
    searchParams.set("page", "0"); // Reset to first page
    setSearchParams(searchParams);
  };
  
  const handleToDateChange = (date: Date | null) => {
    if (date) {
      searchParams.set("toDate", date.toISOString().split('T')[0]);
    } else {
      searchParams.delete("toDate");
    }
    searchParams.set("page", "0"); // Reset to first page
    setSearchParams(searchParams);
  };
  
  // Handle pagination
  const handlePageChange = (newPage: number) => {
    searchParams.set("page", newPage.toString());
    setSearchParams(searchParams);
  };
  
  // Handle page size change
  const handlePageSizeChange = (newSize: string) => {
    searchParams.set("pageSize", newSize);
    searchParams.set("page", "0"); // Reset to first page
    setSearchParams(searchParams);
  };
  
  // Calculate total pages
  const totalPages = Math.ceil(totalCount / pageSize);
  
  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Support Tickets</h1>
      </div>
      
      <div className="flex flex-col space-y-4 mb-6">
        {/* Filters row */}
        <div className="flex flex-wrap gap-4 items-end">
          {/* <div className="w-full md:w-auto">
            <Input
              placeholder="Search tickets..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
            />
          </div> */}
          
          <div className="w-full md:w-auto">
            <label htmlFor="status-filter" className="block text-sm font-medium mb-1">Status</label>
            <Select
              defaultValue={searchParams.get("status") || "ALL"}
              onValueChange={handleStatusFilterChange}
            >
              <SelectTrigger className="w-[180px]" id="status-filter">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Statuses</SelectItem>
                <SelectItem value="OPEN">Open</SelectItem>
                <SelectItem value="WIP">In Progress</SelectItem>
                <SelectItem value="CLOSED">Closed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="w-full md:w-auto">
            <label htmlFor="from-date" className="block text-sm font-medium mb-1">From Date</label>
            <DatePicker
              date={searchParams.get("fromDate") ? new Date(searchParams.get("fromDate")!) : null}
              onSelect={handleFromDateChange}
              id="from-date"
            />
          </div>
          
          <div className="w-full md:w-auto">
            <label htmlFor="to-date" className="block text-sm font-medium mb-1">To Date</label>
            <DatePicker
              date={searchParams.get("toDate") ? new Date(searchParams.get("toDate")!) : null}
              onSelect={handleToDateChange}
              id="to-date"
            />
          </div>
        </div>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Ticket ID</TableHead>
              <TableHead>User Type</TableHead>
              <TableHead>User Name</TableHead>
              <TableHead>Phone</TableHead>
              <TableHead>Seller</TableHead>
              <TableHead>Ticket Type</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Last Update</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Time Unresolved</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTickets.length > 0 ? (
              filteredTickets.map((ticket) => (
                <TableRow key={ticket.ticketId}>
                  <TableCell>#{ticket.ticketId}</TableCell>
                  <TableCell>{ticket.userType}</TableCell>
                  <TableCell>{ticket.userName}</TableCell>
                  <TableCell>{ticket.userMobileNo}</TableCell>
                  <TableCell>{ticket.sellerName}</TableCell>
                  <TableCell>{ticket.ticketType}</TableCell>
                  <TableCell className="max-w-[200px] truncate">
                    {ticket.description}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span>{new Date(ticket.lastModifiedDate).toLocaleDateString()}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <TicketStatusBadge status={ticket.status} />
                  </TableCell>
                  <TableCell>
                    {getTimeSinceCreation(ticket.createdDate)}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleOpenNotesDialog(ticket)}
                      >
                        Notes
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleOpenStatusDialog(ticket)}
                      >
                        Update
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={11}
                  className="h-24 text-center"
                >
                  No tickets found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* Pagination controls */}
      {totalPages > 0 && (
        <div className="flex justify-between items-center mt-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              Showing {(pageNo) * pageSize} to {Math.min((pageNo + 1) * pageSize, totalCount)} of {totalCount} items
            </span>
            <Select
              value={pageSize.toString()}
              onValueChange={handlePageSizeChange}
            >
              <SelectTrigger className="w-[80px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm text-gray-600">per page</span>
          </div>
          
          <div className="flex gap-1">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handlePageChange(0)}
              disabled={pageNo === 0}
            >
              First
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handlePageChange(pageNo - 1)}
              disabled={pageNo === 0}
            >
              Previous
            </Button>
            <span className="flex items-center px-3 text-sm">
              Page {pageNo + 1} of {totalPages + 1}
            </span>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handlePageChange(pageNo + 1)}
              disabled={pageNo >= totalPages}
            >
              Next
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handlePageChange(totalPages - 1)}
              disabled={pageNo >= totalPages}
            >
              Last
            </Button>
          </div>
        </div>
      )}
      
      {/* Ticket Notes Dialog */}
      {selectedTicket && (
        <TicketNotesDialog
          isOpen={notesDialogOpen}
          onClose={() => setNotesDialogOpen(false)}
          ticketId={selectedTicket.ticketId}
          ticketNotes={ticketNotes}
        />
      )}
      
      {/* Ticket Status Update Dialog */}
      {selectedTicket && (
        <TicketStatusDialog
          isOpen={statusDialogOpen}
          onClose={() => {
            setStatusDialogOpen(false);
          }}
          ticket={selectedTicket}
        />
      )}
    </div>
  );
} 