/**
 * Get time difference in a readable format
 */
export function getTimeSinceCreation(date: string): string {
  const now = new Date();
  const createdAt = new Date(date);
  const diffMs = now.getTime() - createdAt.getTime();
  
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  
  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''}`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''}`;
  } else {
    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
  }
}

/**
 * Format date to locale string
 */
export function formatDate(date: string): string {
  return new Date(date).toLocaleString();
}

/**
 * Sort tickets by updated date in ascending order
 */
export function sortTicketsByUpdatedAt<T extends { updatedAt: string }>(tickets: T[]): T[] {
  return [...tickets].sort((a, b) => 
    new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
  );
} 