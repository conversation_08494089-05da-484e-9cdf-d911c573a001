// File: app/components/whatsapp/ResponseSection.tsx
interface ResponseSectionProps {
    title: string;
    data: any;
    success?: boolean;
    error?: string;
}

export function ResponseSection({ title, data, success, error }: ResponseSectionProps) {
    const getBgColor = () => {
        if (success) return 'bg-green-50';
        if (error) return 'bg-red-50';
        return 'bg-gray-50';
    };

    return (
        <div>
            <h3 className="text-lg font-semibold">{title}:</h3>
            <div className={`${getBgColor()} p-4 rounded-md overflow-x-auto mt-2`}>
                {(success !== undefined || error || data.stage || data.timestamp) && (
                    <div className="mb-2 space-y-1">
                        {success !== undefined && (
                            <div>
                                <span className="font-medium">Status:</span> {success ? 'Success' : 'Failed'}
                            </div>
                        )}
                        {data.stage && (
                            <div>
                                <span className="font-medium">Stage:</span> {data.stage}
                            </div>
                        )}
                        {data.timestamp && (
                            <div>
                                <span className="font-medium">Timestamp:</span> {data.timestamp}
                            </div>
                        )}
                    </div>
                )}
                <pre className="text-sm whitespace-pre-wrap">
                    {JSON.stringify(data, null, 2)}
                </pre>
            </div>
        </div>
    );
}
