import { ActionFunction, json, redirect } from "@remix-run/node";
import { useActionData, Form, useNavigate } from "@remix-run/react";
import { ArrowLeft } from "lucide-react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { createSeller } from "~/services/masterItemCategories";
import { withResponse } from "~/utils/auth-utils";

export const action: ActionFunction = async ({ request }) => {
      const formData = await request.formData();
      const seller = {
            name: formData.get("name"),
            address: formData.get("address"),
            email: formData.get("email"),
            customerSupportNumber: formData.get("customerSupportNumber"),
            owner: {
                  firstName: formData.get("owner.firstName"),
                  lastName: formData.get("owner.lastName"),
                  email: formData.get("owner.email"),
                  mobileNumber: formData.get("owner.mobileNumber"),
                  address: formData.get("owner.address"),
                  password: formData.get("owner.password"),
                  businessId: Number(formData.get("owner.businessId")),
                  roles: [formData.get("owner.roles")],
            },
            areaId: Number(formData.get("areaId")),
            latitude: formData.get("latitude"),
            longitude: formData.get("longitude"),
      };

      try {
            const response = await createSeller(seller, request);

            if (response && response.data) {
                  return redirect(request.headers.get("referer") || "/success");
            }

            return withResponse({ data: response.data }, response.headers);
      } catch (error) {
            if (error instanceof Response && error.status === 404) {
                  throw json({ error: "create Seller page Not found" }, { status: 404 });
            }
            throw new Response("Failed to create Seller", { status: 500 });
      }
};


export default function CreateSeller() {
      const actionData = useActionData();
      const [formData, setFormData] = useState({
            name: "",
            address: "",
            email: "",
            customerSupportNumber: "",
            owner: {
                  firstName: "",
                  lastName: "",
                  email: "",
                  mobileNumber: "",
                  address: "",
                  password: "",
                  businessId: 0,
                  roles: "",
            },
            areaId: 0,
            latitude: "",
            longitude: "",
      });

      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            const { name, value } = e.target;
            if (name.startsWith("owner.")) {
                  const field = name.split(".")[1];
                  setFormData((prev) => ({
                        ...prev,
                        owner: {
                              ...prev.owner,
                              [field]: value,
                        },
                  }));
            } else {
                  setFormData((prev) => ({ ...prev, [name]: value }));
            }
      };
      const navigate = useNavigate()

      return (
            <Form method="post" className="space-y-4 p-4 border rounded max-w-2xl bg-slate-100">
                  <div className="flex items-center gap-2 mb-1">
                        <Button variant="ghost" size="sm" onClick={() => navigate("/home/<USER>")}>
                              <ArrowLeft className="h-4 w-4 mr-2" />
                              Back to Sellers
                        </Button>
                  </div>

                  <div >
                        <h3>Seller Details</h3>
                        <div className="space-y-3 mt-2"> {/* Added space-y-3 here */}
                              <div className="flex gap-3 ">
                                    <Input type="text" name="name" placeholder="Name" onChange={handleChange} required className="max-w-sm" />
                                    <Input type="text" name="address" placeholder="Address" onChange={handleChange} required className="max-w-sm" />
                              </div>
                              <div className="flex gap-3">
                                    <Input type="email" name="email" placeholder="Email" onChange={handleChange} required className="max-w-sm" />
                                    <Input type="text" name="customerSupportNumber" placeholder="Customer Support Number" onChange={handleChange} required className="max-w-sm" />
                              </div>
                        </div>
                  </div>
                  <h3>Owner Details</h3>

                  <div className="flex gap-3">
                        <Input type="text" name="owner.firstName" placeholder="First Name" onChange={handleChange} required className="max-w-sm" />
                        <Input type="text" name="owner.lastName" placeholder="Last Name" onChange={handleChange} required className="max-w-sm" />

                  </div>
                  <div className="flex gap-3">

                        <Input type="email" name="owner.email" placeholder="Owner Email" onChange={handleChange} required className="max-w-sm" />
                        <Input type="text" name="owner.mobileNumber" placeholder="Mobile Number" onChange={handleChange} required className="max-w-sm" />
                  </div>
                  <div className="flex gap-3">
                        <Input type="text" name="owner.address" placeholder="Owner Address" onChange={handleChange} required className="max-w-sm" />
                        <Input type="password" name="owner.password" placeholder="Password" onChange={handleChange} required className="max-w-sm" />
                  </div>
                  <div className="flex gap-3">
                        <Input type="number" name="owner.businessId" placeholder="Business ID" onChange={handleChange} required className="max-w-sm" />
                        <Input type="text" name="owner.roles" placeholder="Roles (comma separated)" onChange={handleChange} required className="max-w-sm" />
                  </div>
                  <h3>Location</h3>
                  <div className="flex gap-3">

                        <Input type="number" name="areaId" placeholder="Area ID" onChange={handleChange} required className="max-w-sm" />
                        <Input type="text" name="latitude" placeholder="Latitude" onChange={handleChange} required className="max-w-sm" />
                        <Input type="text" name="longitude" placeholder="Longitude" onChange={handleChange} required className="max-w-sm" />
                  </div>
                  <div className="flex justify-end">
                        <Button type="submit" className="rounded-full">Create Seller</Button>
                  </div>

            </Form>
      );
}
