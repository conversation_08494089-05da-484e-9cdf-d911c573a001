import ExcelJS from 'exceljs';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function createSampleExcel() {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('WhatsApp Templates');

    // Add headers
    worksheet.columns = [
        { header: 'sellerId', key: 'sellerId', width: 15 },
        { header: 'templateId', key: 'templateId', width: 30 },
        { header: 'targetPhoneNumber', key: 'targetPhoneNumber', width: 20 },
        { header: 'customer_name', key: 'customer_name', width: 20 },
        { header: 'pending_amount', key: 'pending_amount', width: 15 },
        { header: 'due_date', key: 'due_date', width: 15 },
        { header: 'status', key: 'status', width: 10, },
        { header: 'business_name', key: 'business_name', width: 30 },
        { header: 'paid_amount', key: 'paid_amount', width: 30 },
        { header: 'order_id', key: 'order_id', width: 20 }
    ];

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
    };

    // Add sample data
    const sampleData = [
        {
            sellerId: '58',
            templateId: 'b2b_payment_success_v2',
            targetPhoneNumber: '+919807666322',
            customer_name: 'John Doe',
            pending_amount: '5000',
            due_date: '2024-03-31',
            status: 'PENDING',
            business_name: 'Business Name',
            paid_amount: '200',
            order_id: '123456'
        },
        // {
        //     sellerId: '58',
        //     templateId: 'early_pending_payment_special_offer',
        //     targetPhoneNumber: '+919876543211',
        //     customer_name: 'Jane Smith',
        //     pending_amount: '7500',
        //     due_date: '2024-03-31',
        //     status: 'PENDING'
        // },
        // {
        //     sellerId: '58',
        //     templateId: 'early_pending_payment_special_offer',
        //     targetPhoneNumber: '+919876543212',
        //     customer_name: 'Bob Johnson',
        //     pending_amount: '3000',
        //     due_date: '2024-03-31',
        //     status: 'PENDING'
        // }
    ];

    // Add rows
    sampleData.forEach(data => {
        worksheet.addRow(data);
    });

    // Create directory if it doesn't exist
    const inputDir = path.join(process.cwd(), 'src', 'scripts', 'input');
    if (!fs.existsSync(inputDir)) {
        fs.mkdirSync(inputDir, { recursive: true });
    }

    // Save the file
    const filePath = path.join(inputDir, 'whatsapp_templates.xlsx');
    await workbook.xlsx.writeFile(filePath);
    console.log(`Sample Excel file created at: ${filePath}`);
}

createSampleExcel()
    .then(() => console.log('Excel file creation completed'))
    .catch(error => console.error('Error creating Excel file:', error)); 