import * as React from "react";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import type { FormState } from "~/routes/home.masterItems";
import { SearchableCombobox, type ComboboxItem } from "../ui/searchableCombobox";
import { useFetcher } from "@remix-run/react";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";

interface StepOneProps {
  formData: FormState;
  onChange: (data: Partial<FormState>) => void;
  errors?: Record<string, string[] | undefined>;
  mode: 'create' | 'edit' | 'duplicate';
  renderRETInput: (field: string) => boolean;
}

interface BrandLoaderData {
  brands: Array<{ id: string; name: string }>;
  brandPage: number;
}

function StepOne({ formData, onChange, errors, mode, renderRETInput }: StepOneProps) {
  const [brandItems, setBrandItems] = React.useState<ComboboxItem[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [page, setPage] = React.useState(1);
  const [hasMore, setHasMore] = React.useState(true);
  const fetcher = useFetcher<BrandLoaderData>();

  // Search brands with server API
  const searchBrands = async (query: string) => {
    if (fetcher.state === "loading") {
      return;
    }
    setLoading(true);
    try {
      fetcher.load(`/home/<USER>
    } catch (error) {
      console.error("Search error:", error);
    }
  };

  // Load more brands for infinite scroll
  const loadMoreBrands = async () => {
    if (!hasMore || loading || fetcher.state === "loading") return;
    const nextPage = page + 1;
    try {
      fetcher.load(`/home/<USER>
    } catch (error) {
      console.error("Load more error:", error);
    }
  };

  // Handle fetcher data updates
  React.useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data?.brands) {
      const items: ComboboxItem[] = fetcher.data.brands.map(brand => ({
        value: brand.id,
        label: brand.name
      }));

      if (fetcher.data.brandPage === 1) {
        setBrandItems(items);
      } else {
        setBrandItems(prev => [...prev, ...items]);
      }

      setPage(fetcher.data.brandPage);
      setHasMore(items.length === 10);
      setLoading(false);
    }
  }, [fetcher.state, fetcher.data]);

  return (
    <div className="grid gap-4 p-4 md:p-6">
      <div className="grid gap-4 md:grid-cols-2">


        <div className="col-span-full md:col-span-2">
          <Label>Business Type</Label>
          <RadioGroup
            value={formData.itemConfig.ondcDomain}
            onValueChange={(val: "RET11" | "RET10") => onChange({ itemConfig: { ...formData.itemConfig, ondcDomain: val } })}
            className="grid grid-cols-3 gap-4 mt-1"
            disabled={mode === "edit"}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="type-RET11" value="RET11" />
              <Label htmlFor="type-RET11">Restaurant</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="type-RET10" value="RET10" />
              <Label htmlFor="type-RET10">Non-Restaurant</Label>
            </div>
          </RadioGroup>
        </div>

        <div className="col-span-full md:col-span-2">
          <Label htmlFor="itemName">Item Name (Required)</Label>
          <Input
            name="itemName"
            value={String(formData.itemName || "")}
            onChange={(e) => onChange({ itemName: e.target.value })}
            required
            className="w-full"
          />
          {errors?.itemName?.[0] && (
            <p className="text-red-500">{errors.itemName[0]}</p>
          )}
        </div>

        {renderRETInput("brandName") && <div className="col-span-full md:col-span-2">
          <Label htmlFor="brandName">Brand Name</Label>
          <Input
            name="brandName"
            value={String(formData.brandName || "")}
            onChange={(e) => onChange({ brandName: e.target.value })}
            required
            className="w-full"
          />
          {errors?.brandName?.[0] && (
            <p className="text-red-500">{errors.brandName[0]}</p>
          )}
        </div>}

        {mode === 'create' && (
          <div className="col-span-full md:col-span-2">
            <Label htmlFor="groupId">Group ID</Label>
            <Input
              name="groupId"
              value={String(formData.groupId || "")}
              onChange={(e) => onChange({ groupId: e.target.value })}
              className="w-full"
            />
            {errors?.groupId?.[0] && (
              <p className="text-red-500">{errors.groupId[0]}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default React.memo(StepOne);