export interface MasterItemDto {
    id?: number;
    defaultUnit: string;
    name: string;
    picture: string;
    nameInKannada?: string;
    nameInTelugu?: string;
    nameInTamil?: string;
    nameInMalayalam?: string;
    nameInHindi?: string;
    nameInAssame?: string;
    nameInGujarati?: string;
    nameInMarathi?: string;
    nameInBangla?: string;
    defaultWeightFactor?: number;
    gstHsnCode?: string;
    gstRate?: number;
    source?: string;
    sourceKey?: string;
    productId?: string;
    brandName?: string;
    packaging?: string;
    mrp:number;
}

export interface CreateSupportTicketRequest {
    userId: number;
    requestedCallBack: boolean;
    status?: string;
    ticketType?: string;
    description?: string;
}
