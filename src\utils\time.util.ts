/**
 * Time Utility for Webhook and Notification Services
 * 
 * Simplified time handling utility for consistent timestamp management.
 * 
 * Features:
 * - Current timestamp generation
 * - IST timezone ISO string formatting
 * - Time range creation for filtering
 * 
 * @version 2.0.0 - Simplified
 */

import { formatInTimeZone } from 'date-fns-tz';
import { subHours } from 'date-fns';

// Constants
export const IST_TIMEZONE = 'Asia/Kolkata';
export const ISO_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSxxx";

/**
 * Time range for filtering operations
 */
export interface TimeRange {
    startTimestamp: number;  // Unix milliseconds
    endTimestamp: number;    // Unix milliseconds
}

export class TimeUtil {
    /**
     * Get current timestamp in milliseconds
     */
    static getCurrentTimestamp(): number {
        return Date.now();
    }

    /**
     * Convert timestamp (milliseconds) to IST ISO string
     */
    static toISTISO(timestampMs: number): string {
        return formatInTimeZone(new Date(timestampMs), IST_TIMEZONE, ISO_FORMAT);
    }

    /**
     * Create time range for filtering (e.g., last 24 hours)
     */
    static createTimeRange(hoursBack: number): TimeRange {
        const endTimestamp = Date.now();
        const startTimestamp = subHours(new Date(endTimestamp), hoursBack).getTime();
        
        return {
            startTimestamp,
            endTimestamp
        };
    }

    /**
     * Format current time for logging with IST timezone
     */
    static formatForLogging(): string {
        const now = Date.now();
        return `${formatInTimeZone(new Date(now), IST_TIMEZONE, 'yyyy-MM-dd HH:mm:ss')} IST`;
    }
} 