import { json } from "@remix-run/node";
import { getSuppliers } from '~/services/businessConsoleService';
import { withAuth, withResponse } from "~/utils/auth-utils";

export const loader = withAuth(async ({ request }) => {
  const url = new URL(request.url);
  const itemId = Number(url.searchParams.get("itemId"));
  if (!itemId ) {
    return json({ error: "itemId" }, { status: 400 });
  }

  try {
    const supplierData = await getSuppliers(itemId, request);
    return json({supplierData });
  } catch (error) {
    console.error("Error fetching supplier:", error);
    return json(
      { error: "Error fetching supplier" },
      { status: 500 }
    );
  }
});
