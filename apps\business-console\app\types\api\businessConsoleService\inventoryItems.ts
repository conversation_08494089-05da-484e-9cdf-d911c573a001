import { Category } from "~/types/home/<USER>";

export type InventoryResponse = {
  inventoryItems: InventoryItem[];
  addEnabled: boolean;
  deleteEnabled: boolean;
};

export type InventoryItem = {
  inventoryId: number;
  sellerItemId: number;
  itemImage: string;
  itemName: string;
  packaging: string;
  itemRegionalName: string;
  itemRating: number;
  itemTag: string;
  unit: string;
  avgPrice: number;
  shopCount: number;
  soldQty: number;
  revenue: number;
  isConfirmed: boolean;
  price: number;
  strikeOffPrice: number;
  discPerc: number;
  totalQty: number;
  isDistributionAllowed: boolean;
  isPriceEditAllowed: boolean;
  isQtyEditAllowed: boolean;
  isBookingTimeOver: boolean;
  isConfirmationAllowed: boolean;
  masterItemId: number;
  totalBoxes: number;
  boxQty: number;
  holdBooking: boolean;
  strikeoffEnabled: boolean;
  freeItem: boolean;
  supplierItem: boolean;
  supplierName: string;
  groupId: string;
};

export type SellerCategoriesResponse = {
  categories: Category[];
  addEnabled: boolean;
  deleteEnabled: boolean;
};
