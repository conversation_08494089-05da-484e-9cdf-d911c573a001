/**
 * NotificationLog Entity Enhancement Migration Script
 * 
 * This script performs the migration to enhance the NotificationLog entity
 * with campaign management, WhatsApp tracking, and analytics capabilities.
 * 
 * Usage:
 *   npm run migrate:notification-log
 *   tsx src/migrations/notification-log-migration.ts
 */

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { 
    DynamoDBDocumentClient, 
    ScanCommand, 
    UpdateCommand, 
    BatchWriteCommand,
    ScanCommandOutput,
    UpdateCommandInput,
    BatchWriteCommandInput
} from '@aws-sdk/lib-dynamodb';
import { formatInTimeZone } from 'date-fns-tz';
import fs from 'fs';
import path from 'path';

// Import types
import { 
    NotificationLog, 
    NotificationStatus, 
    CampaignType, 
    MessageCategory, 
    CustomerSegment 
} from '../database/entities/NotificationLog.js';

class NotificationLogMigration {
    private dynamoClient: DynamoDBDocumentClient;
    private tableName: string;
    private backupDir: string;

    constructor() {
        const client = new DynamoDBClient({
            region: process.env.AWS_REGION || 'us-east-1'
        });
        this.dynamoClient = DynamoDBDocumentClient.from(client);
        
        const serverEnv = process.env.SERVER_ENV === 'production' ? 'prod' : 'uat';
        this.tableName = `notification_logs_${serverEnv}`;
        
        this.backupDir = path.join(process.cwd(), 'backups');
        
        // Ensure backup directory exists
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
        }
    }

    /**
     * Convert Unix timestamp to IST ISO string
     */
    private toISTISOString(timestamp: number): string {
        return formatInTimeZone(
            new Date(timestamp),
            'Asia/Kolkata',
            "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
        );
    }

    /**
     * Step 1: Backup existing data
     */
    async backupExistingData(): Promise<string> {
        console.log('🔄 Starting data backup...');
        
        const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        const backupFile = path.join(this.backupDir, `notification_logs_backup_${timestamp}.json`);
        
        try {
            const allRecords: Record<string, any>[] = [];
            let lastEvaluatedKey: Record<string, any> | undefined = undefined;
            let itemCount = 0;

            do {
                const scanCommand: ScanCommand = new ScanCommand({
                    TableName: this.tableName,
                    ExclusiveStartKey: lastEvaluatedKey,
                    Limit: 100 // Process in batches to avoid memory issues
                });

                const response: ScanCommandOutput = await this.dynamoClient.send(scanCommand);
                
                if (response.Items) {
                    allRecords.push(...response.Items);
                    itemCount += response.Items.length;
                    console.log(`📊 Backed up ${itemCount} records so far...`);
                }

                lastEvaluatedKey = response.LastEvaluatedKey;
            } while (lastEvaluatedKey);

            // Write backup to file
            fs.writeFileSync(backupFile, JSON.stringify(allRecords, null, 2));
            
            console.log(`✅ Backup completed: ${allRecords.length} records saved to ${backupFile}`);
            return backupFile;
        } catch (error) {
            console.error('❌ Backup failed:', error);
            throw error;
        }
    }

    /**
     * Step 2: Migrate existing records to add new fields
     */
    async migrateExistingRecords(): Promise<{ success: number; failed: number }> {
        console.log('🔄 Starting data migration...');
        
        let success = 0;
        let failed = 0;
        let lastEvaluatedKey: any = undefined;

        try {
            do {
                const scanCommand = new ScanCommand({
                    TableName: this.tableName,
                    ExclusiveStartKey: lastEvaluatedKey,
                    Limit: 25 // Smaller batches for updates
                });

                const response = await this.dynamoClient.send(scanCommand);
                
                if (response.Items) {
                    // Process records in parallel batches
                    const updatePromises = response.Items.map(record => 
                        this.migrateRecord(record)
                            .then(() => success++)
                            .catch(error => {
                                console.error(`❌ Failed to migrate record ${record.notificationId}:`, error);
                                failed++;
                            })
                    );

                    await Promise.all(updatePromises);
                    console.log(`📊 Processed batch: ${success} successful, ${failed} failed`);
                }

                lastEvaluatedKey = response.LastEvaluatedKey;
            } while (lastEvaluatedKey);

            console.log(`✅ Migration completed: ${success} successful, ${failed} failed`);
            return { success, failed };
        } catch (error) {
            console.error('❌ Migration failed:', error);
            throw error;
        }
    }

    /**
     * Migrate a single record
     */
    private async migrateRecord(record: Record<string, any>): Promise<void> {
        const updates: Record<string, any> = {};
        const updateExpressions: string[] = [];
        const expressionAttributeNames: Record<string, string> = {};
        const expressionAttributeValues: Record<string, any> = {};

        // Add ISO timestamps
        if (record.timestamp && !record.timestampISO) {
            updateExpressions.push('#timestampISO = :timestampISO');
            expressionAttributeNames['#timestampISO'] = 'timestampISO';
            expressionAttributeValues[':timestampISO'] = this.toISTISOString(record.timestamp);
        }

        if (record.lastUpdated && !record.lastUpdatedISO) {
            updateExpressions.push('#lastUpdatedISO = :lastUpdatedISO');
            expressionAttributeNames['#lastUpdatedISO'] = 'lastUpdatedISO';
            expressionAttributeValues[':lastUpdatedISO'] = this.toISTISOString(record.lastUpdated);
        }

        // Add analytics timestamp ISOs
        if (record.sentAt && !record.sentAtISO) {
            updateExpressions.push('#sentAtISO = :sentAtISO');
            expressionAttributeNames['#sentAtISO'] = 'sentAtISO';
            expressionAttributeValues[':sentAtISO'] = this.toISTISOString(record.sentAt);
        }

        if (record.deliveredAt && !record.deliveredAtISO) {
            updateExpressions.push('#deliveredAtISO = :deliveredAtISO');
            expressionAttributeNames['#deliveredAtISO'] = 'deliveredAtISO';
            expressionAttributeValues[':deliveredAtISO'] = this.toISTISOString(record.deliveredAt);
        }

        if (record.readAt && !record.readAtISO) {
            updateExpressions.push('#readAtISO = :readAtISO');
            expressionAttributeNames['#readAtISO'] = 'readAtISO';
            expressionAttributeValues[':readAtISO'] = this.toISTISOString(record.readAt);
        }

        if (record.failedAt && !record.failedAtISO) {
            updateExpressions.push('#failedAtISO = :failedAtISO');
            expressionAttributeNames['#failedAtISO'] = 'failedAtISO';
            expressionAttributeValues[':failedAtISO'] = this.toISTISOString(record.failedAt);
        }

        // Add default values for new campaign fields (only if campaignId exists but type is missing)
        if (record.campaignId && !record.campaignType) {
            updateExpressions.push('#campaignType = :campaignType');
            expressionAttributeNames['#campaignType'] = 'campaignType';
            expressionAttributeValues[':campaignType'] = CampaignType.MARKETING;
        }

        if (!record.messageCategory) {
            updateExpressions.push('#messageCategory = :messageCategory');
            expressionAttributeNames['#messageCategory'] = 'messageCategory';
            expressionAttributeValues[':messageCategory'] = MessageCategory.GENERAL;
        }

        if (!record.customerSegment) {
            updateExpressions.push('#customerSegment = :customerSegment');
            expressionAttributeNames['#customerSegment'] = 'customerSegment';
            expressionAttributeValues[':customerSegment'] = CustomerSegment.GENERAL;
        }

        // Only update if there are changes to make
        if (updateExpressions.length > 0) {
            const updateCommand = new UpdateCommand({
                TableName: this.tableName,
                Key: {
                    notificationId: record.notificationId,
                    // timestamp: record.timestamp // Uncomment if using composite key
                },
                UpdateExpression: `SET ${updateExpressions.join(', ')}`,
                ExpressionAttributeNames: expressionAttributeNames,
                ExpressionAttributeValues: expressionAttributeValues
            });

            await this.dynamoClient.send(updateCommand);
        }
    }

    /**
     * Step 3: Validate migration results
     */
    async validateMigration(): Promise<{ total: number; withISO: number; withCampaignFields: number }> {
        console.log('🔄 Validating migration results...');
        
        let total = 0;
        let withISO = 0;
        let withCampaignFields = 0;
        let lastEvaluatedKey: any = undefined;

        try {
            do {
                const scanCommand = new ScanCommand({
                    TableName: this.tableName,
                    ExclusiveStartKey: lastEvaluatedKey,
                    Limit: 100
                });

                const response = await this.dynamoClient.send(scanCommand);
                
                if (response.Items) {
                    for (const record of response.Items) {
                        total++;
                        
                        if (record.timestampISO && record.lastUpdatedISO) {
                            withISO++;
                        }
                        
                        if (record.messageCategory && record.customerSegment) {
                            withCampaignFields++;
                        }
                    }
                }

                lastEvaluatedKey = response.LastEvaluatedKey;
            } while (lastEvaluatedKey);

            const results = { total, withISO, withCampaignFields };
            console.log('📊 Validation Results:');
            console.log(`   Total records: ${results.total}`);
            console.log(`   With ISO timestamps: ${results.withISO} (${((results.withISO / results.total) * 100).toFixed(1)}%)`);
            console.log(`   With campaign fields: ${results.withCampaignFields} (${((results.withCampaignFields / results.total) * 100).toFixed(1)}%)`);
            
            return results;
        } catch (error) {
            console.error('❌ Validation failed:', error);
            throw error;
        }
    }

    /**
     * Main migration execution
     */
    async executeMigration(): Promise<void> {
        console.log('🚀 Starting NotificationLog Entity Enhancement Migration');
        console.log(`📋 Table: ${this.tableName}`);
        console.log(`📁 Backup Directory: ${this.backupDir}`);
        console.log('='.repeat(60));

        try {
            // Step 1: Backup existing data
            const backupFile = await this.backupExistingData();
            console.log('='.repeat(60));

            // Step 2: Migrate existing records
            const migrationResults = await this.migrateExistingRecords();
            console.log('='.repeat(60));

            // Step 3: Validate migration
            const validationResults = await this.validateMigration();
            console.log('='.repeat(60));

            // Summary
            console.log('🎉 Migration Summary:');
            console.log(`✅ Backup file: ${backupFile}`);
            console.log(`📊 Migration: ${migrationResults.success} successful, ${migrationResults.failed} failed`);
            console.log(`🔍 Validation: ${validationResults.withISO}/${validationResults.total} records have ISO timestamps`);
            console.log(`🎯 Campaign fields: ${validationResults.withCampaignFields}/${validationResults.total} records have campaign fields`);
            
            if (migrationResults.failed > 0) {
                console.log('⚠️  Some records failed to migrate. Check logs for details.');
            } else {
                console.log('✅ All records migrated successfully!');
            }

            console.log('🏁 Migration completed successfully!');
            
        } catch (error) {
            console.error('💥 Migration failed:', error);
            console.log('🚨 Recovery: Use the backup file to restore data if needed');
            throw error;
        }
    }

    /**
     * Rollback migration using backup file
     */
    async rollback(backupFile: string): Promise<void> {
        console.log(`🔄 Starting rollback from ${backupFile}...`);
        
        try {
            // Read backup file
            const backupData = JSON.parse(fs.readFileSync(backupFile, 'utf-8'));
            console.log(`📁 Loaded ${backupData.length} records from backup`);

            // Restore records in batches
            const batchSize = 25; // DynamoDB batch write limit
            let restored = 0;

            for (let i = 0; i < backupData.length; i += batchSize) {
                const batch = backupData.slice(i, i + batchSize);
                
                const batchWriteCommand = new BatchWriteCommand({
                    RequestItems: {
                        [this.tableName]: batch.map((record: any) => ({
                            PutRequest: {
                                Item: record
                            }
                        }))
                    }
                });

                await this.dynamoClient.send(batchWriteCommand);
                restored += batch.length;
                console.log(`📊 Restored ${restored}/${backupData.length} records`);
            }

            console.log('✅ Rollback completed successfully!');
        } catch (error) {
            console.error('❌ Rollback failed:', error);
            throw error;
        }
    }
}

// CLI execution
async function main() {
    const migration = new NotificationLogMigration();
    
    const args = process.argv.slice(2);
    const command = args[0];
    
    try {
        switch (command) {
            case 'backup':
                await migration.backupExistingData();
                break;
            case 'migrate':
                await migration.migrateExistingRecords();
                break;
            case 'validate':
                await migration.validateMigration();
                break;
            case 'rollback':
                const backupFile = args[1];
                if (!backupFile) {
                    console.error('❌ Please provide backup file path for rollback');
                    process.exit(1);
                }
                await migration.rollback(backupFile);
                break;
            case 'full':
            default:
                await migration.executeMigration();
                break;
        }
    } catch (error) {
        console.error('💥 Command failed:', error);
        process.exit(1);
    }
}

// Execute if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export { NotificationLogMigration }; 