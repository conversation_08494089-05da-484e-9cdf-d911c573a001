/**
 * NotificationLogRepository Tests
 * 
 * Tests for enhanced repository methods including campaign tracking,
 * WhatsApp message lookup, webhook integration, and analytics.
 */

// Mock testing functions since no test framework is configured
const describe = (name: string, fn: () => void) => {
    console.log(`\n🧪 ${name}`);
    fn();
};

const test = (name: string, fn: () => Promise<void>) => {
    console.log(`  ⚙️ ${name}`);
    fn().then(() => console.log(`  ✅ ${name} - PASSED`))
         .catch(err => console.error(`  ❌ ${name} - FAILED:`, err));
};

const beforeEach = (fn: () => void) => {
    // Initialize before each test
    fn();
};

const afterEach = (fn: () => void) => {
    // Cleanup after each test
    fn();
};

const expect = (actual: any) => ({
    toBe: (expected: any) => {
        if (actual !== expected) {
            throw new Error(`Expected ${expected}, got ${actual}`);
        }
    },
    toEqual: (expected: any) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
            throw new Error(`Expected ${JSON.stringify(expected)}, got ${JSON.stringify(actual)}`);
        }
    },
    toBeDefined: () => {
        if (actual === undefined || actual === null) {
            throw new Error(`Expected value to be defined, got ${actual}`);
        }
    },
    toMatch: (pattern: RegExp) => {
        if (!pattern.test(actual)) {
            throw new Error(`Expected ${actual} to match ${pattern}`);
        }
    },
    toHaveBeenCalledTimes: (times: number) => {
        if (actual.mock?.calls?.length !== times) {
            throw new Error(`Expected ${times} calls, got ${actual.mock?.calls?.length || 0}`);
        }
    }
});

// Mock functions
const mockFn = () => {
    const calls: any[][] = [];
    const mockFunction: any = (...args: any[]) => {
        calls.push(args);
        return mockFunction._returnValue;
    };
    mockFunction.mock = { calls };
    mockFunction._returnValue = undefined;
    mockFunction.mockResolvedValueOnce = (value: any) => { 
        mockFunction._returnValue = Promise.resolve(value); 
        return mockFunction; 
    };
    mockFunction.mockClear = () => { 
        calls.length = 0; 
        return mockFunction; 
    };
    mockFunction.mockReturnValue = (value: any) => {
        mockFunction._returnValue = value;
        return mockFunction;
    };
    mockFunction.mockImplementation = (impl: any) => {
        mockFunction._implementation = impl;
        return mockFunction;
    };
    return mockFunction;
};

const mockSend = mockFn();

// Mock DynamoDB
const mockDynamoDB = {
    DynamoDBDocumentClient: {
        from: mockFn().mockReturnValue({
            send: mockSend
        })
    },
    PutCommand: mockFn().mockImplementation((params: any) => ({ params })),
    UpdateCommand: mockFn().mockImplementation((params: any) => ({ params })),
    QueryCommand: mockFn().mockImplementation((params: any) => ({ params })),
    GetCommand: mockFn().mockImplementation((params: any) => ({ params })),
    ScanCommand: mockFn().mockImplementation((params: any) => ({ params }))
};

import { NotificationLogRepository } from '../../database/repository/NotificationLogRepository.js';
import { 
    NotificationLog, 
    NotificationChannel, 
    NotificationStatus, 
    CampaignType, 
    MessageCategory, 
    CustomerSegment 
} from '../../database/entities/NotificationLog.js';
import { NotificationQueryFilters } from '../../types/notification.types.js';

describe('NotificationLogRepository', () => {
    let repository: NotificationLogRepository;
    let mockLog: Omit<NotificationLog, 'notificationId' | 'timestamp' | 'timestampISO' | 'lastUpdatedISO'>;

    beforeEach(() => {
        repository = new NotificationLogRepository();
        mockSend.mockClear();
        
        mockLog = {
            businessId: 'business-123',
            mobileNumber: '+919876543210',
            channel: NotificationChannel.WHATSAPP,
            recipient: '+919876543210',
            inputPayload: { templateName: 'test_template' },
            providerRequest: { messaging_product: 'whatsapp' },
            providerResponse: { messages: [] }, // Changed from null to empty object
            status: NotificationStatus.PENDING,
            errorMessage: undefined,
            retryCount: 0,
            lastUpdated: Date.now()
        };
    });

    afterEach(() => {
        // Clear all mocks
        mockSend.mockClear();
    });

    describe('Core CRUD Operations', () => {
        test('createLog should create notification with auto-generated fields and ISO timestamps', async () => {
            mockSend.mockResolvedValueOnce({});
            
            const result = await repository.createLog(mockLog);
            
            expect(result.notificationId).toBeDefined();
            expect(result.timestamp).toBeDefined();
            expect(result.timestampISO).toBeDefined();
            expect(result.lastUpdatedISO).toBeDefined();
            expect(result.timestampISO).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
            expect(mockSend).toHaveBeenCalledTimes(1);
        });

        test('updateLog should update with automatic ISO timestamp conversion', async () => {
            mockSend.mockResolvedValueOnce({});
            
            const updates = { 
                status: NotificationStatus.SENT,
                sentAt: Date.now()
            };
            
            await repository.updateLog('test-id', 123456789, updates);
            
            const command = mockSend.mock.calls[0][0];
            expect(command.params.Key).toEqual({
                notificationId: 'test-id'
            });
            expect(mockSend).toHaveBeenCalledTimes(1);
        });

        test('getLog should retrieve notification by ID and timestamp', async () => {
            const mockResponse = {
                Item: {
                    notificationId: 'test-id',
                    timestamp: 123456789,
                    businessId: 'business-123'
                }
            };
            mockSend.mockResolvedValueOnce(mockResponse);
            
            const result = await repository.getLog('test-id', 123456789);
            
            expect(result).toEqual(mockResponse.Item);
            expect(mockSend).toHaveBeenCalledTimes(1);
        });
    });

    describe('WhatsApp Message Tracking', () => {
        test('findByWhatsAppMessageId should query WhatsAppMessageIndex', async () => {
            const mockWhatsAppLog = {
                notificationId: 'test-notification',
                whatsappMessageId: 'wamid.test123',
                status: NotificationStatus.SENT
            };
            
            mockSend.mockResolvedValueOnce({
                Items: [mockWhatsAppLog]
            });
            
            const result = await repository.findByWhatsAppMessageId('wamid.test123');
            
            expect(result).toEqual(mockWhatsAppLog);
            
            const command = mockSend.mock.calls[0][0];
            expect(command.params.IndexName).toBe('WhatsAppMessageIndex');
            expect(command.params.KeyConditionExpression).toBe('whatsappMessageId = :messageId');
            expect(command.params.ExpressionAttributeValues).toEqual({
                ':messageId': 'wamid.test123'
            });
        });

        test('updateByWhatsAppMessageId should find and update notification', async () => {
            const mockNotification = {
                notificationId: 'test-notification',
                timestamp: Date.now(),
                whatsappMessageId: 'wamid.test123'
            };
            
            // Mock findByWhatsAppMessageId response
            mockSend.mockResolvedValueOnce({
                Items: [mockNotification]
            });
            
            // Mock updateLog response
            mockSend.mockResolvedValueOnce({});
            
            const updates = { status: NotificationStatus.DELIVERED };
            const result = await repository.updateByWhatsAppMessageId('wamid.test123', updates);
            
            expect(result).toBe(true);
            expect(mockSend).toHaveBeenCalledTimes(2); // findBy + update calls
        });

        test('updateByWhatsAppMessageId should return false if notification not found', async () => {
            mockSend.mockResolvedValueOnce({ Items: [] });
            
            const result = await repository.updateByWhatsAppMessageId('non-existent', {});
            
            expect(result).toBe(false);
            expect(mockSend).toHaveBeenCalledTimes(1);
        });
    });

    describe('Campaign Analytics Methods', () => {
        test('getByCampaignId should query CampaignIndex', async () => {
            const mockCampaignLogs = [
                { campaignId: 'campaign-123', notificationId: 'notif-1' },
                { campaignId: 'campaign-123', notificationId: 'notif-2' }
            ];
            
            mockSend.mockResolvedValueOnce({
                Items: mockCampaignLogs
            });
            
            const result = await repository.getByCampaignId('campaign-123');
            
            expect(result).toEqual(mockCampaignLogs);
            
            const command = mockSend.mock.calls[0][0];
            expect(command.params.IndexName).toBe('CampaignIndex');
            expect(command.params.KeyConditionExpression).toBe('campaignId = :campaignId');
            expect(command.params.ScanIndexForward).toBe(false); // Most recent first
        });

        test('getByCustomerSegment should query CustomerSegmentIndex with time range', async () => {
            const startTime = Date.now() - 86400000; // 24 hours ago
            const endTime = Date.now();
            
            mockSend.mockResolvedValueOnce({ Items: [] });
            
            await repository.getByCustomerSegment(
                CustomerSegment.HIGH_VALUE, 
                startTime, 
                endTime, 
                50
            );
            
            const command = mockSend.mock.calls[0][0];
            expect(command.params.IndexName).toBe('CustomerSegmentIndex');
            expect(command.params.KeyConditionExpression).toBe('customerSegment = :segment');
            expect(command.params.FilterExpression).toBe('(#ts BETWEEN :startTime AND :endTime)');
            expect(command.params.Limit).toBe(50);
        });

        test('getByMessageCategory should query MessageCategoryIndex', async () => {
            mockSend.mockResolvedValueOnce({ Items: [] });
            
            await repository.getByMessageCategory(MessageCategory.PROMOTIONAL_OFFER);
            
            const command = mockSend.mock.calls[0][0];
            expect(command.params.IndexName).toBe('MessageCategoryIndex');
            expect(command.params.KeyConditionExpression).toBe('messageCategory = :category');
            expect(command.params.ExpressionAttributeValues).toEqual({
                ':category': MessageCategory.PROMOTIONAL_OFFER
            });
        });

        test('getByWhatsAppStatus should query WhatsAppStatusIndex', async () => {
            mockSend.mockResolvedValueOnce({ Items: [] });
            
            await repository.getByWhatsAppStatus(NotificationStatus.DELIVERED);
            
            const command = mockSend.mock.calls[0][0];
            expect(command.params.IndexName).toBe('WhatsAppStatusIndex');
            expect(command.params.KeyConditionExpression).toBe('whatsappStatus = :status');
            expect(command.params.ExpressionAttributeValues).toEqual({
                ':status': NotificationStatus.DELIVERED
            });
        });
    });

    describe('Business Query Methods', () => {
        test('getLogsByBusiness should query BusinessIndex with time range', async () => {
            const startTime = Date.now() - 86400000;
            const endTime = Date.now();
            const mockBusinessLogs = [
                { businessId: 'business-123', timestamp: startTime + 1000 },
                { businessId: 'business-123', timestamp: endTime - 1000 }
            ];
            
            mockSend.mockResolvedValueOnce({
                Items: mockBusinessLogs
            });
            
            const result = await repository.getLogsByBusiness('business-123', startTime, endTime);
            
            expect(result).toEqual(mockBusinessLogs);
            
            const command = mockSend.mock.calls[0][0];
            expect(command.params.IndexName).toBe('BusinessIndex');
            expect(command.params.KeyConditionExpression).toBe('businessId = :businessId AND #ts BETWEEN :startTime AND :endTime');
            expect(command.params.ExpressionAttributeValues).toEqual({
                ':businessId': 'business-123',
                ':startTime': startTime,
                ':endTime': endTime
            });
        });

        test('queryWithFilters should build complex query with filters', async () => {
            const filters: NotificationQueryFilters = {
                businessId: 'business-123',
                status: NotificationStatus.DELIVERED,
                campaignId: 'campaign-123',
                startTimestamp: Date.now() - 86400000,
                endTimestamp: Date.now(),
                limit: 50
            };
            
            mockSend.mockResolvedValueOnce({ Items: [] });
            
            await repository.queryWithFilters(filters);
            
            expect(mockSend).toHaveBeenCalledTimes(1);
            // Additional query structure validation could be added here
        });
    });

    describe('Analytics Data Methods', () => {
        test('getCampaignAnalyticsData should aggregate campaign statistics', async () => {
            const mockAnalyticsData = {
                totalMessages: 100,
                statusCounts: {
                    [NotificationStatus.SENT]: 90,
                    [NotificationStatus.DELIVERED]: 85,
                    [NotificationStatus.READ]: 45,
                    [NotificationStatus.FAILED]: 5
                },
                segmentCounts: {
                    [CustomerSegment.HIGH_VALUE]: 60,
                    [CustomerSegment.GENERAL]: 40
                },
                timeSeriesData: []
            };
            
            // Mock multiple DynamoDB calls for analytics aggregation
            mockSend.mockResolvedValueOnce({ Items: [] }); // Base campaign query
            
            const result = await repository.getCampaignAnalyticsData('campaign-123');
            
            expect(result).toBeDefined();
            expect(mockSend).toHaveBeenCalledTimes(1);
        });
    });

    describe('Error Handling', () => {
        test('should handle DynamoDB errors gracefully', async () => {
            mockSend.mockRejectedValueOnce(new Error('DynamoDB connection error'));
            
            try {
                await repository.getLog('test-id', 123456789);
                throw new Error('Should have thrown an error');
            } catch (error: any) {
                expect(error.message).toBe('DynamoDB connection error');
            }
        });

        test('should handle missing items gracefully', async () => {
            mockSend.mockResolvedValueOnce({ Item: undefined });
            
            const result = await repository.getLog('non-existent', 123456789);
            
            expect(result).toBe(null);
        });
    });

    describe('Performance and Optimization', () => {
        test('should use proper projection expressions for queries', async () => {
            mockSend.mockResolvedValueOnce({ Items: [] });
            
            await repository.getByCampaignId('campaign-123');
            
            const command = mockSend.mock.calls[0][0];
            // Verify that only necessary fields are projected
            expect(command.params.IndexName).toBe('CampaignIndex');
        });

        test('should respect query limits', async () => {
            mockSend.mockResolvedValueOnce({ Items: [] });
            
            await repository.getByCustomerSegment(
                CustomerSegment.HIGH_VALUE,
                Date.now() - 86400000,
                Date.now(),
                25
            );
            
            const command = mockSend.mock.calls[0][0];
            expect(command.params.Limit).toBe(25);
        });
    });
}); 