import { useEffect, useState } from "react";
import { Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts";
import { OrderItem } from "~/types/api/businessConsoleService/SellerConsoleDataResponse";
import { DataPoint } from "./bar-dashboard";
import { formatWeight } from "~/utils/format";


interface responsivePiechartProps {
      tonnageReports: OrderItem[],
      selectedGraphData: DataPoint | undefined

}
const COLORS = ['#afded7', '#f8c8cd', '#cfc2ed', '#AFEFED', '#90c8e7', '#FDDFB4', '#E789B7', '#FFD384', '#00a390', '#7551ce']

const ResponsivePieChart = ({ tonnageReports, selectedGraphData }: responsivePiechartProps) => {
      const [chartSize, setChartSize] = useState(400);

      useEffect(() => {
            const updateSize = () => {
                  const container = document.getElementById("pie-chart-container");
                  if (container) {
                        setChartSize(container.offsetWidth);
                  }
            };
            updateSize();
            window.addEventListener("resize", updateSize);
            return () => window.removeEventListener("resize", updateSize);
      }, []);

      const chartData = Array.isArray(tonnageReports) ? tonnageReports : [];
      const totalValue = selectedGraphData?.totalweight ?? 0;
      const consolidatePieData = (data: OrderItem[] = []) => {
            if (!data || data.length === 0) return [];
            const sortedData = [...data].sort((a, b) => b.totalWeight - a.totalWeight);
            const topFive = sortedData.slice(0, 4);
            const others = sortedData.slice(4).reduce((acc, curr) => {
                  acc.totalWeight += curr.totalWeight;
                  return acc;
            }, { itemName: 'Others', totalWeight: 0 });
            const unsortedcombined = [...topFive, others]
            const combined = [...unsortedcombined].sort((a, b) => b.totalWeight - a.totalWeight);
            // Alternate between topFive and others
            const result: OrderItem[] = [];
            let left = 0;
            let right = combined.length - 1;

            while (left <= right) {
                  if (left === right) {
                        result.push(combined[left]); // Add the remaining element if odd-length
                  } else {
                        result.push(combined[left], combined[right]);
                  }
                  left++;
                  right--;
            }

            return result.map((item, index) => ({
                  ...item,
                  color: COLORS[index % COLORS.length],
            }));
      };
      return (
            <div id="pie-chart-container" className="w-full flex justify-center items-center">
                  <ResponsiveContainer width="100%" height={chartSize < 500 ? 300 : 400}>
                        <PieChart>
                              <Pie
                                    data={consolidatePieData(chartData)}
                                    cx="50%"
                                    cy="50%"
                                    innerRadius={chartSize < 500 ? 40 : 60}
                                    outerRadius={chartSize < 500 ? 70 : 100}
                                    paddingAngle={2}
                                    dataKey="totalWeight"
                                    labelLine={false}
                                    label={(props) => {
                                          const { cx, cy, midAngle, outerRadius, fill, payload, value } = props;
                                          const RADIAN = Math.PI / 180;
                                          const sin = Math.sin(-RADIAN * midAngle);
                                          const cos = Math.cos(-RADIAN * midAngle);
                                          const sx = cx + (outerRadius + 10) * cos;
                                          const sy = cy + (outerRadius + 10) * sin;
                                          const mx = cx + (outerRadius + 20) * cos;
                                          const my = cy + (outerRadius + 30) * sin;
                                          const ex = mx + (cos >= 0 ? 1 : -1) * 20;
                                          const ey = my;
                                          const textAnchor = cos >= 0 ? "start" : "end";

                                          const percentage = parseFloat(((value / totalValue) * 100).toFixed(2));
                                          if (percentage < 4) return null; // Hide labels for small values

                                          // Adjust label truncation based on screen size
                                          const maxLabelLength = chartSize < 500 ? 12 : 20;
                                          const truncate = (str: string, maxLength: number) => {
                                                return str.length > maxLength ? `${str.slice(0, maxLength)}...` : str;
                                          };

                                          return (
                                                <g>
                                                      <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
                                                      <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
                                                      <text
                                                            x={ex + (cos >= 0 ? 1 : -1) * 10}
                                                            y={ey}
                                                            textAnchor={textAnchor}
                                                            fill="#333"
                                                            className="text-[10px] sm:text-xs md:text-sm lg:text-base font-medium"
                                                      >
                                                            {truncate(payload.itemName, maxLabelLength)}
                                                      </text>
                                                      <text
                                                            x={ex + (cos >= 0 ? 1 : -1) * 10}
                                                            y={ey}
                                                            dy={14}
                                                            textAnchor={textAnchor}
                                                            fill="#666"
                                                            className="text-[9px] sm:text-xs md:text-sm"
                                                      >
                                                            {`${formatWeight(value)} | ${percentage}%`}
                                                      </text>
                                                </g>
                                          );
                                    }}
                              >
                                    {consolidatePieData(chartData).map((entry, index) => (
                                          <Cell key={`cell-${index}`} fill={entry.color} />
                                    ))}
                              </Pie>
                        </PieChart>
                  </ResponsiveContainer>
            </div>
      );
};

export default ResponsivePieChart;
