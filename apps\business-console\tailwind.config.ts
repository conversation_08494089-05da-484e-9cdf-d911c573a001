import { type Config } from "tailwindcss"
import { fontFamily } from "tailwindcss/defaultTheme"

export default {
	// darkMode: ["class"],
	content: ["app/**/*.{ts,tsx}", "components/**/*.{ts,tsx}"],
	theme: {
		container: {
			center: true,
			padding: "2rem",
			screens: {
				"2xl": "1400px",
			},
		},
		extend: {
			fontWeight: {
				light: '300',
				normal: '400',
				medium: '500',
				semibold: '600',
				bold: '700',
			},
			colors: {
				'fm-green': '#2c7744',
				'fm-light': '#f0f7f4',
				'fm-hover': '#1e5631',
				border: "hsl(var(--border))",
				input: "hsl(var(--input))",
				ring: "hsl(var(--ring))",
				background: "hsl(var(--background))",
				foreground: "hsl(var(--foreground))",
				primary: {
					DEFAULT: "hsl(var(--primary))",
					foreground: "hsl(var(--primary-foreground))",
					50: "hsl(var(--primary-50))",
					100: "hsl(var(--primary-100))",
					200: "hsl(var(--primary-200))",
					300: "hsl(var(--primary-300))",
					400: "hsl(var(--primary-400))",
					500: "hsl(var(--primary-500))",
					600: "hsl(var(--primary-600))",
					700: "hsl(var(--primary-700))",
					800: "hsl(var(--primary-800))",
					900: "hsl(var(--primary-900))",
				},
				secondary: {
					DEFAULT: "hsl(var(--secondary))",
					foreground: "hsl(var(--secondary-foreground))",
					50: "hsl(var(--secondary-50))",
					100: "hsl(var(--secondary-100))",
					200: "hsl(var(--secondary-200))",
					300: "hsl(var(--secondary-300))",
					400: "hsl(var(--secondary-400))",
					500: "hsl(var(--secondary-500))",
					600: "hsl(var(--secondary-600))",
					700: "hsl(var(--secondary-700))",
					800: "hsl(var(--secondary-800))",
					900: "hsl(var(--secondary-900))",
				},
				complementary: {
					50: "hsl(var(--complementary-50))",
					100: "hsl(var(--complementary-100))",
					200: "hsl(var(--complementary-200))",
					300: "hsl(var(--complementary-300))",
					400: "hsl(var(--complementary-400))",
					500: "hsl(var(--complementary-500))",
					600: "hsl(var(--complementary-600))",
					700: "hsl(var(--complementary-700))",
					800: "hsl(var(--complementary-800))",
					900: "hsl(var(--complementary-900))",
				  },
				  // Typography Colors
				  typography: {
					50: "hsl(var(--typography-50))",
					100: "hsl(var(--typography-100))",
					200: "hsl(var(--typography-200))",
					300: "hsl(var(--typography-300))",
					400: "hsl(var(--typography-400))",
					500: "hsl(var(--typography-500))",
					600: "hsl(var(--typography-600))",
					700: "hsl(var(--typography-700))",
					800: "hsl(var(--typography-800))",
					900: "hsl(var(--typography-900))",
				  },
				  // Neutral Colors
				  neutral: {
					50: "hsl(var(--neutral-50))",
					100: "hsl(var(--neutral-100))",
					200: "hsl(var(--neutral-200))",
					300: "hsl(var(--neutral-300))",
					400: "hsl(var(--neutral-400))",
					500: "hsl(var(--neutral-500))",
					600: "hsl(var(--neutral-600))",
					700: "hsl(var(--neutral-700))",
					800: "hsl(var(--neutral-800))",
					900: "hsl(var(--neutral-900))",
				  },
				  // Blue Colors
				  blue: {
					50: "hsl(var(--blue-50))",
					100: "hsl(var(--blue-100))",
					200: "hsl(var(--blue-200))",
					300: "hsl(var(--blue-300))",
					400: "hsl(var(--blue-400))",
					500: "hsl(var(--blue-500))",
					600: "hsl(var(--blue-600))",
					700: "hsl(var(--blue-700))",
					800: "hsl(var(--blue-800))",
					900: "hsl(var(--blue-900))",
				  },
				  // Orange Colors
				  orange: {
					50: "hsl(var(--orange-50))",
					100: "hsl(var(--orange-100))",
					200: "hsl(var(--orange-200))",
					300: "hsl(var(--orange-300))",
					400: "hsl(var(--orange-400))",
					500: "hsl(var(--orange-500))",
					600: "hsl(var(--orange-600))",
					700: "hsl(var(--orange-700))",
					800: "hsl(var(--orange-800))",
					900: "hsl(var(--orange-900))",
				  },
				  // Pink Colors
				  pink: {
					50: "hsl(var(--pink-50))",
					100: "hsl(var(--pink-100))",
					200: "hsl(var(--pink-200))",
					300: "hsl(var(--pink-300))",
					400: "hsl(var(--pink-400))",
					500: "hsl(var(--pink-500))",
					600: "hsl(var(--pink-600))",
					700: "hsl(var(--pink-700))",
					800: "hsl(var(--pink-800))",
					900: "hsl(var(--pink-900))",
				  },
				  // Yellow Colors
				  yellow: {
					50: "hsl(var(--yellow-50))",
					100: "hsl(var(--yellow-100))",
					200: "hsl(var(--yellow-200))",
					300: "hsl(var(--yellow-300))",
					400: "hsl(var(--yellow-400))",
					500: "hsl(var(--yellow-500))",
					600: "hsl(var(--yellow-600))",
					700: "hsl(var(--yellow-700))",
					800: "hsl(var(--yellow-800))",
					900: "hsl(var(--yellow-900))",
				  },
				  // Green Colors
				  green: {
					50: "hsl(var(--green-50))",
					100: "hsl(var(--green-100))",
					200: "hsl(var(--green-200))",
					300: "hsl(var(--green-300))",
					400: "hsl(var(--green-400))",
					500: "hsl(var(--green-500))",
					600: "hsl(var(--green-600))",
					700: "hsl(var(--green-700))",
					800: "hsl(var(--green-800))",
					900: "hsl(var(--green-900))",
				  },
				  // Red Colors
				  red: {
					50: "hsl(var(--red-50))",
					100: "hsl(var(--red-100))",
					200: "hsl(var(--red-200))",
					300: "hsl(var(--red-300))",
					400: "hsl(var(--red-400))",
					500: "hsl(var(--red-500))",
					600: "hsl(var(--red-600))",
					700: "hsl(var(--red-700))",
					800: "hsl(var(--red-800))",
					900: "hsl(var(--red-900))",
				  },

				destructive: {
					DEFAULT: "hsl(var(--destructive))",
					foreground: "hsl(var(--destructive-foreground))",
				},
				muted: {
					DEFAULT: "hsl(var(--muted))",
					foreground: "hsl(var(--muted-foreground))",
				},
				accent: {
					DEFAULT: "hsl(var(--accent))",
					foreground: "hsl(var(--accent-foreground))",
				},
				popover: {
					DEFAULT: "hsl(var(--popover))",
					foreground: "hsl(var(--popover-foreground))",
				},
				card: {
					DEFAULT: "hsl(var(--card))",
					foreground: "hsl(var(--card-foreground))",
				},
			},
			borderRadius: {
				lg: "var(--radius)",
				md: "calc(var(--radius) - 2px)",
				sm: "calc(var(--radius) - 4px)",
			},
			fontFamily: {
				sans: ["Nunito","Poppins", ...fontFamily.sans],
			},
			keyframes: {
				"accordion-down": {
					from: { height: "0" },
					to: { height: "var(--radix-accordion-content-height)" },
				},
				"accordion-up": {
					from: { height: "var(--radix-accordion-content-height)" },
					to: { height: "0" },
				},
			},
			animation: {
				"accordion-down": "accordion-down 0.2s ease-out",
				"accordion-up": "accordion-up 0.2s ease-out",
			},
		},
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config
