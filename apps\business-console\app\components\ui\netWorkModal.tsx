
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "./dialog";
import { Input } from "./input";
import { Button } from "./button";
import { useFetcher } from "@remix-run/react";
import { useToast } from "./ToastProvider";
import SpinnerLoader from "../loader/SpinnerLoader";

interface NetWorkModalProps {
      isOpen: boolean;
      onClose: () => void;
      sellerList: { id: number; name: string }[];
      netWorkId: number
}
export function NetWorkModal({ isOpen, onClose, sellerList, netWorkId }: NetWorkModalProps) {
      const [sellerId, setSellerId] = useState<number | "">("");

      const fetcher = useFetcher()
      const handleSave = () => {
            const formData = new FormData()
            formData.append("_intent", "createNetWork");
            formData.append("netWorkId", netWorkId as unknown as string)
            formData.append("sellerId", sellerId as unknown as string)
            fetcher.submit(formData, { method: "POST" })
      };

      const { showToast } = useToast()
      const isLoading = fetcher.state !== "idle"

      useEffect(() => {
            if (fetcher.data && isOpen === true) {
                  if (fetcher.data) {
                        showToast("seller to Network SuccessFully", "success")
                        onClose()
                  }
                  else {
                        showToast("sellerMapping Failed", "error")

                  }

            }

      }, [fetcher.data])

      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="max-h-[50vh]  max-w-sm overflow-y-auto">
                        {isLoading && (
                              <div className="absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm">
                                    <SpinnerLoader loading={isLoading} />
                              </div>
                        )}
                        <DialogHeader>
                              <DialogTitle>Map Seller to Network</DialogTitle>
                        </DialogHeader>
                        <div className="flex max-w-s flex-col gap-4">

                              <select
                                    className="border p-2 rounded"
                                    value={sellerId}
                                    onChange={(e) => setSellerId(e.target.value ? Number(e.target.value) : "")}
                              >
                                    <option value="">Select Seller</option>
                                    {sellerList.map((seller) => (
                                          <option key={seller.id} value={seller.id}>
                                                {seller.name}
                                          </option>
                                    ))}
                              </select>

                              {/* Buttons */}
                              <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={onClose}>
                                          Cancel
                                    </Button>
                                    <Button onClick={handleSave} disabled={!sellerId} loading={isLoading}>
                                          Save
                                    </Button>
                              </div>
                        </div>
                  </DialogContent>
            </Dialog>
      );
}
