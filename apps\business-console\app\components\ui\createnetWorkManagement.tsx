import { useEffect, useState } from "react";
import { useFetcher } from "@remix-run/react";
import { Dialog, DialogContent, DialogTitle } from "./dialog";
import { Seller } from "~/types/api/businessConsoleService/MasterItemCategory";
import SpinnerLoader from "../loader/SpinnerLoader";
import { useToast } from "./ToastProvider";

interface NetworkManagementProps {
      isOpen: boolean;
      onClose: () => void;
      sellerList: Seller[];
}

const NetworkManagementModal: React.FC<NetworkManagementProps> = ({
      isOpen,
      onClose,
      sellerList,
}) => {
      const fetcher = useFetcher();
      const { showToast } = useToast();
      const [managerId, setManagerId] = useState<number | "">("");
      const [sellerId, setSellerId] = useState<number | "">("");
      const [formData, setFormData] = useState({
            name: "",
            description: "",
            isPrivate: false,
            managerId: "" as number | "",
            domain: "",
            networkType: [] as string[],
            businessType: "",
            defaultSellerId: "" as number | "",
            multiSeller: "" as boolean | "",
      });
      const [networkError, setNetworkError] = useState(false);
      const isLoading = fetcher.state !== "idle";
      useEffect(() => {
            if (fetcher.data) {
                  if ((fetcher.data as any)?.success) {
                        showToast("Network Created Successfully", "success");
                        onClose();
                  } else {
                        if ((fetcher.data as any)?.success === false) {
                              showToast("Network Creation Failed", "error");
                        }
                  }
            }
      }, [fetcher.state, fetcher.data, onClose]);

      const handleChange = (
            e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
      ) => {
            const { name, value, type } = e.target;
            const updatedValue =
                  type === "checkbox" && e.target instanceof HTMLInputElement
                        ? e.target.checked
                        : value;

            setFormData((prev) => ({
                  ...prev,
                  [name]: updatedValue,
            }));
      };

      const handleManagerChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
            const selectedValue = e.target.value ? parseInt(e.target.value, 10) : "";
            setManagerId(selectedValue);
            setFormData((prev) => ({
                  ...prev,
                  managerId: selectedValue,
            }));
      };

      const handleSellerChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
            const selectedValue = e.target.value ? parseInt(e.target.value, 10) : "";
            setSellerId(selectedValue);
            setFormData((prev) => ({
                  ...prev,
                  defaultSellerId: selectedValue,
            }));
      };

      const handleNetworkTypeChange = (type: string) => {
            setFormData((prev) => {
                  const newNetworkType = [type]; // Set only the selected type
                  setNetworkError(newNetworkType.length === 0);
                  return { ...prev, networkType: newNetworkType };
            });
      };

      const handleBusinessTypeChange = (type: string) => {
            setFormData((prev) => ({
                  ...prev,
                  businessType: prev.businessType === type ? "" : type,
            }));
      };

      const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();

            if (formData.networkType.length === 0) {
                  setNetworkError(true);
                  return;
            }

            if (formData.multiSeller === "" || formData.multiSeller === undefined) {
                  showToast("Please select an option for MultiSeller", "error");
                  return;
            }

            fetcher.submit(formData, { method: "post" });
      };

      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="max-h-[90vh] w-full max-w-2xl rounded-lg bg-white p-6 shadow-xl">
                        {isLoading && (
                              <div className="absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-sm">
                                    <SpinnerLoader loading={isLoading} />
                              </div>
                        )}
                        <DialogTitle className="text-2xl font-semibold text-gray-800">
                              Create Network
                        </DialogTitle>
                        <form onSubmit={handleSubmit} className="space-y-6">
                              <div className="max-h-[60vh] overflow-y-auto pr-4">
                                    <div className="space-y-4">
                                          <div>
                                                <label className="block text-sm font-medium text-gray-700">
                                                      Name
                                                </label>
                                                <input
                                                      type="text"
                                                      name="name"
                                                      value={formData.name}
                                                      onChange={handleChange}
                                                      required
                                                      className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                                      placeholder="Enter network name"
                                                />
                                          </div>

                                          <div>
                                                <label className="block text-sm font-medium text-gray-700">
                                                      Description
                                                </label>
                                                <textarea
                                                      name="description"
                                                      value={formData.description}
                                                      onChange={handleChange}
                                                      required
                                                      className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                                      rows={4}
                                                      placeholder="Describe the network"
                                                />
                                          </div>

                                          <div>
                                                <label className="flex items-center space-x-2">
                                                      <input
                                                            type="checkbox"
                                                            name="isPrivate"
                                                            checked={formData.isPrivate}
                                                            onChange={handleChange}
                                                            className="h-4 w-4 rounded text-blue-600 focus:ring-blue-500"
                                                      />
                                                      <span className="text-sm text-gray-700">Private Network</span>
                                                </label>
                                          </div>

                                          <div>
                                                <label className="block text-sm font-medium text-gray-700">
                                                      Manager
                                                </label>
                                                <select
                                                      name="managerId"
                                                      value={managerId}
                                                      onChange={handleManagerChange}
                                                      required
                                                      className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                                >
                                                      <option value="">Select Manager</option>
                                                      {sellerList?.map((seller) => (
                                                            <option
                                                                  key={seller?.id}
                                                                  value={seller?.businessId?.toString()}
                                                            >
                                                                  {seller?.name}
                                                            </option>
                                                      ))}
                                                </select>
                                          </div>

                                          <div>
                                                <label className="block text-sm font-medium text-gray-700">
                                                      Domain URL
                                                </label>
                                                <input
                                                      type="text"
                                                      name="domain"
                                                      value={formData.domain}
                                                      onChange={handleChange}
                                                      required
                                                      className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                                      placeholder="https://example.com"
                                                />
                                          </div>

                                          <div>
                                                <label className="block text-sm font-medium text-gray-700">
                                                      Seller
                                                </label>
                                                <select
                                                      name="sellerId"
                                                      value={sellerId}
                                                      onChange={handleSellerChange}
                                                      required
                                                      className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                                >
                                                      <option value="">Select Seller</option>
                                                      {sellerList?.map((seller) => (
                                                            <option key={seller?.id} value={seller?.id?.toString()}>
                                                                  {seller?.name}
                                                            </option>
                                                      ))}
                                                </select>
                                          </div>

                                          <div>
                                                <label className="block text-sm font-medium text-gray-700">
                                                      Network Type
                                                </label>
                                                <div className="mt-2 flex space-x-6">
                                                      {["B2B", "B2C"].map((type) => (
                                                            <label key={type} className="flex items-center space-x-2">
                                                                  <input
                                                                        type="radio"
                                                                        name="networkType"
                                                                        value={type}
                                                                        checked={formData.networkType.includes(type)}
                                                                        onChange={() => handleNetworkTypeChange(type)}
                                                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                                                  />
                                                                  <span className="text-sm text-gray-700">{type}</span>
                                                            </label>
                                                      ))}
                                                </div>
                                                {networkError && (
                                                      <p className="mt-1 text-sm text-red-500">
                                                            Please select a network type.
                                                      </p>
                                                )}
                                          </div>

                                          <div>
                                                <label className="block text-sm font-medium text-gray-700">
                                                      Business Type
                                                </label>
                                                <div className="mt-2 flex space-x-6">
                                                      {["Restaurant", "Non-Restaurant"].map((type) => (
                                                            <label key={type} className="flex items-center space-x-2">
                                                                  <input
                                                                        type="checkbox"
                                                                        disabled={type == "Restaurant" && formData.networkType.includes("B2B")}
                                                                        checked={formData.businessType === type}
                                                                        onChange={() => handleBusinessTypeChange(type)}
                                                                        className="h-4 w-4 rounded text-blue-600 focus:ring-blue-500"
                                                                  />
                                                                  <span className="text-sm text-gray-700">{type}</span>
                                                            </label>
                                                      ))}
                                                </div>
                                          </div>

                                          <div>
                                                <label className="block text-sm font-medium text-gray-700">
                                                      MultiSeller
                                                </label>
                                                <div className="mt-2 flex space-x-6">
                                                      {[
                                                            { label: "Yes", value: true },
                                                            { label: "No", value: false },
                                                      ].map((option) => (
                                                            <label
                                                                  key={option.label}
                                                                  className="flex items-center space-x-2"
                                                            >
                                                                  <input
                                                                        type="radio"
                                                                        name="multiSeller"
                                                                        value={option.value.toString()}
                                                                        checked={formData.multiSeller === option.value}
                                                                        onChange={() =>
                                                                              setFormData((prev) => ({
                                                                                    ...prev,
                                                                                    multiSeller: option.value,
                                                                              }))
                                                                        }
                                                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                                                  />
                                                                  <span className="text-sm text-gray-700">
                                                                        {option.label}
                                                                  </span>
                                                            </label>
                                                      ))}
                                                </div>
                                                {(formData.multiSeller === "" ||
                                                      formData.multiSeller === undefined) && (
                                                            <p className="mt-1 text-sm text-red-500">
                                                                  Please select an option for MultiSeller.
                                                            </p>
                                                      )}
                                          </div>
                                    </div>
                              </div>

                              <div className="flex justify-end space-x-3 pt-4">
                                    <button
                                          type="button"
                                          onClick={onClose}
                                          className="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-300 focus:outline-none focus:ring focus:ring-gray-200 focus:ring-opacity-50"
                                    >
                                          Cancel
                                    </button>
                                    <button
                                          type="submit"
                                          disabled={isLoading}
                                          className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700 focus:outline-none focus:ring focus:ring-blue-200 focus:ring-opacity-50 disabled:bg-blue-400"
                                    >
                                          {isLoading ? "Creating..." : "Create"}
                                    </button>
                              </div>
                        </form>
                  </DialogContent>
            </Dialog>
      );
};

export default NetworkManagementModal;