import { LoaderFunction } from "@remix-run/node";
import { getSearchFilters } from "~/services/tripsSummary";
import { withAuth, withResponse } from "~/utils/auth-utils";

export const loader: LoaderFunction = withAuth(async ({ request }) => {
  console.log("apicalled...........................");
  try {
    const url = new URL(request.url);
    const type = url.searchParams.get("type") || "";
    const matchBy = url.searchParams.get("matchBy")
      ? Number(url.searchParams.get("matchBy"))
      : undefined;
    const pageNo = url.searchParams.get("pageNo")
      ? Number(url.searchParams.get("pageNo"))
      : undefined;
    const pageSize = url.searchParams.get("size")
      ? Number(url.searchParams.get("size"))
      : undefined;

    const response = await getSearchFilters(
      type,
      matchBy,
      pageNo,
      pageSize,
      request
    );

    return withResponse({ sellers: response?.data }, response?.headers);
  } catch (error) {
    console.error("Error fetching getSearchFilters:", error);
    throw new Response("Failed to fetch getSearchFilters", { status: 500 });
  }
});
