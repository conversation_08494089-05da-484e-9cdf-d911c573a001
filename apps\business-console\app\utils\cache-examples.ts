/**
 * Cache Service Usage Examples
 * Demonstrates different patterns for using the cache service
 */

import { CacheService, themeCache, apiCache, createDomainCache } from './cache';
import type { NetworkTheme } from '../types/api/common';

// Example 1: Basic usage
export async function exampleBasicUsage() {
  const cache = new CacheService<string>({
    ttl: 5 * 60 * 1000, // 5 minutes
    maxSize: 50,
  });

  // Set a value
  cache.set('user:123', '<PERSON>', 10 * 60 * 1000); // 10 minutes TTL

  // Get a value
  const user = cache.get('user:123');
  console.log(user); // "<PERSON>" or null if expired

  // Check if exists
  if (cache.has('user:123')) {
    console.log('User exists in cache');
  }
}

// Example 2: Using getOrSet for API calls
export async function exampleGetOrSet() {
  const userCache = new CacheService<{ name: string; email: string }>({
    ttl: 15 * 60 * 1000, // 15 minutes
  });

  const user = await userCache.getOrSet(
    'user:456',
    async () => {
      // This function only runs if cache miss
      const response = await fetch('/api/users/456');
      return response.json();
    },
    30 * 60 * 1000 // 30 minutes TTL for this specific call
  );

  console.log(user);
}

// Example 3: Using pre-configured caches
export async function examplePreConfiguredCaches() {
  // Use theme cache (already configured)
  const theme = await themeCache.getOrSet(
    'theme:domain.com',
    async () => {
      const response = await fetch('/api/theme');
      return response.json();
    }
  );
  console.log('Theme data:', theme);

  // Use API cache for general API responses
  const apiData = await apiCache.getOrSet(
    'api:products',
    async () => {
      const response = await fetch('/api/products');
      return response.json();
    }
  );
  console.log('API data:', apiData);
}

// Example 4: Domain-specific caching
export async function exampleDomainCache() {
  const domainCache = createDomainCache<NetworkTheme>('example.com', {
    ttl: 20 * 60 * 1000, // 20 minutes
    maxSize: 10,
  });

  const theme = await domainCache.getOrSet(
    'theme',
    async () => {
      const response = await fetch('/api/theme');
      return response.json();
    }
  );
  console.log('Domain theme:', theme);
}

// Example 5: Cache statistics and management
export function exampleCacheManagement() {
  const cache = new CacheService<number>({
    ttl: 5 * 60 * 1000,
    maxSize: 100,
  });

  // Add some data
  for (let i = 0; i < 50; i++) {
    cache.set(`key:${i}`, i);
  }

  // Get statistics
  const stats = cache.getStats();
  console.log('Cache stats:', stats);
  // Output: { total: 50, valid: 50, expired: 0, maxSize: 100 }

  // Clear cache
  cache.clear();

  // Delete specific key
  cache.delete('key:1');

  // Destroy cache (stops cleanup interval)
  cache.destroy();
}

// Example 6: Error handling with cache
export async function exampleErrorHandling() {
  const cache = new CacheService<string>({
    ttl: 10 * 60 * 1000,
  });

  try {
    const data = await cache.getOrSet(
      'api:risky-endpoint',
      async () => {
        const response = await fetch('/api/risky-endpoint');
        if (!response.ok) {
          throw new Error('API call failed');
        }
        return response.text();
      }
    );
    console.log('Success:', data);
  } catch (error) {
    console.error('Cache or API error:', error);
    // Handle error gracefully
  }
}

// Example 7: Custom TTL per item
export function exampleCustomTTL() {
  const cache = new CacheService<string>({
    ttl: 5 * 60 * 1000, // Default 5 minutes
  });

  // Short-lived data
  cache.set('temp:session', 'temp-data', 60 * 1000); // 1 minute

  // Long-lived data
  cache.set('config:app', 'app-config', 60 * 60 * 1000); // 1 hour

  // Use default TTL
  cache.set('user:profile', 'user-data'); // Uses default 5 minutes
}

// Example 8: Cache with complex objects
export function exampleComplexObjects() {
  interface UserProfile {
    id: number;
    name: string;
    email: string;
    preferences: {
      theme: string;
      language: string;
    };
  }

  const userCache = new CacheService<UserProfile>({
    ttl: 30 * 60 * 1000, // 30 minutes
    maxSize: 200,
  });

  const user: UserProfile = {
    id: 123,
    name: 'John Doe',
    email: '<EMAIL>',
    preferences: {
      theme: 'dark',
      language: 'en',
    },
  };

  userCache.set('user:123', user);
  const cachedUser = userCache.get('user:123');
  console.log(cachedUser?.preferences.theme); // "dark"
} 