import React, { useRef, useEffect, useState, useCallback } from "react";
import {
      GoogleMap,
      LoadScript,
      Polygon,
      InfoWindow,
      Marker,
} from "@react-google-maps/api";
import path from "path";

interface MapPolygonProps {
      polygonCoordinates: google.maps.LatLngLiteral[]; // Should be in the 2D array format
      markers?: { lat: number; lng: number; name?: string }[]; // Optional markers
      center: { lat: number; lng: number };
      zoom?: number;
}

const mapContainerStyle = {
      width: "100%",
      height: "500px",
};

const MapPolygon: React.FC<MapPolygonProps> = ({
      polygonCoordinates,
      markers = [],
      center,
      zoom = 12,
}) => {
      const mapRef = useRef<google.maps.Map | null>(null);
      const [map, setMap] = useState<google.maps.Map | null>(null);
      const [mapLoaded, setMapLoaded] = useState(false);
      const [infoWindow, setInfoWindow] = useState<{
            position: { lat: number; lng: number };
            name: string;
      } | null>(null);

      const fitBounds = () => {
            if (map && polygonCoordinates.length) {
                  const bounds = new google.maps.LatLngBounds();
                  polygonCoordinates.forEach((polygon) =>
                        bounds.extend(polygon)
                  );
                  markers.forEach((marker) =>
                        bounds.extend(new google.maps.LatLng(marker.lat, marker.lng))
                  );
                  map.unbindAll();
                  map.fitBounds(bounds);

            }
            const poly = new google.maps.Polygon({
                  paths: polygonCoordinates,
                  strokeColor: "red",
                  strokeOpacity: 0.8,
                  strokeWeight: 2,
                  fillColor: "blue",
                  fillOpacity: 0.4

            })

            poly.setMap(map)

      };

      useEffect(() => {
            if (mapLoaded) {
                  fitBounds();
            }
      }, [fitBounds, mapLoaded]);

      const onLoad = useCallback((mapInstance: google.maps.Map) => {
            setMap(mapInstance);
            setMapLoaded(true);
      }, []);

      const onUnmount = useCallback(() => {
            setMap(null);
            setMapLoaded(false);
            map?.unbindAll()
      }, []);


      return (
            <>
                  {
                        polygonCoordinates.length && <LoadScript googleMapsApiKey="AIzaSyDBh6D6NIEiH08bj01ybByaayfM1T7W6XY">
                              <GoogleMap
                                    mapContainerStyle={mapContainerStyle}
                                    zoom={zoom}
                                    center={center}
                                    onLoad={onLoad}
                                    onUnmount={onUnmount}
                                    options={{
                                          mapTypeControl: false,
                                          streetViewControl: false,
                                          zoomControl: true,
                                    }}
                              >
                                    {/* Render Polygons */}
                                    {/* {polygonCoordinates.length > 0 &&
                              polygonCoordinates.map((coords, index) => (
                                    <Polygon
                                          key={index}
                                          paths={coords}
                                          options={{
                                                fillColor: "red",
                                                fillOpacity: 0.4,
                                                strokeColor: "green",
                                                strokeOpacity: 1,
                                                strokeWeight: 2,
                                                zIndex: 1000,
                                          }}
                                    />
                              ))} */}

                                    {/* Render Markers */}
                                    {markers.map((marker, index) => (
                                          <Marker
                                                key={index}
                                                position={{ lat: marker.lat, lng: marker.lng }}
                                                onClick={() =>
                                                      setInfoWindow({
                                                            position: { lat: marker.lat, lng: marker.lng },
                                                            name: marker.name || "Marker",
                                                      })
                                                }
                                          />
                                    ))}

                                    {/* Render InfoWindow */}
                                    {infoWindow && (
                                          <InfoWindow
                                                position={infoWindow.position}
                                                onCloseClick={() => setInfoWindow(null)}
                                          >
                                                <div>{infoWindow.name}</div>
                                          </InfoWindow>
                                    )}
                              </GoogleMap>
                        </LoadScript>

                  }
            </>
      );
};

export default MapPolygon;
