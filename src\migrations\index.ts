/**
 * DynamoDB Migration Runner
 * Manages and executes database migrations with proper version tracking
 */

import { readdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface Migration {
    id: string;
    name: string;
    description: string;
    version: string;
    dependencies: string[];
    createdAt: string;
}

interface MigrationModule {
    migrationInfo: Migration;
    up: () => Promise<void>;
    down: () => Promise<void>;
    validate: () => Promise<boolean>;
}

export class MigrationRunner {
    private migrations: Map<string, MigrationModule> = new Map();
    private executedMigrations: Set<string> = new Set();

    async loadMigrations(): Promise<void> {
        console.log("🔍 Loading available migrations...");
        
        const migrationFiles = readdirSync(__dirname)
            .filter(file => file.match(/^\d{3}-.*\.ts$/) || file.match(/^\d{3}-.*\.js$/))
            .sort();

        for (const file of migrationFiles) {
            try {
                const migrationPath = join(__dirname, file);
                const migration = await import(migrationPath) as MigrationModule;
                
                if (!migration.migrationInfo || !migration.up) {
                    console.warn(`⚠️  Skipping invalid migration file: ${file}`);
                    continue;
                }

                this.migrations.set(migration.migrationInfo.id, migration);
                console.log(`   ✅ Loaded migration ${migration.migrationInfo.id}: ${migration.migrationInfo.name}`);
                
            } catch (error) {
                console.error(`❌ Failed to load migration ${file}:`, error);
            }
        }

        console.log(`📦 Loaded ${this.migrations.size} migration(s)\n`);
    }

    async runMigrations(targetMigration?: string): Promise<void> {
        await this.loadMigrations();
        
        const sortedMigrations = Array.from(this.migrations.entries())
            .sort(([a], [b]) => a.localeCompare(b));

        console.log("🚀 Starting migration execution...\n");
        console.log("=".repeat(60));
        console.log("DynamoDB Migration Runner");
        console.log("=".repeat(60));

        let executed = 0;
        let skipped = 0;
        let failed = 0;

        for (const [id, migration] of sortedMigrations) {
            // Stop if we've reached the target migration
            if (targetMigration && id > targetMigration) {
                break;
            }

            console.log(`\n📦 Migration ${id}: ${migration.migrationInfo.name}`);
            console.log(`📄 ${migration.migrationInfo.description}`);
            console.log(`📅 Created: ${migration.migrationInfo.createdAt}`);
            console.log("-".repeat(50));

            try {
                const startTime = Date.now();
                await migration.up();
                const endTime = Date.now();
                const duration = endTime - startTime;

                // Validate the migration
                const isValid = await migration.validate();
                if (!isValid) {
                    throw new Error("Migration validation failed");
                }

                console.log(`✅ Migration ${id} completed successfully (${duration}ms)`);
                this.executedMigrations.add(id);
                executed++;

            } catch (error) {
                if (error instanceof Error && error.message.includes('already exists')) {
                    console.log(`⏭️  Migration ${id} skipped - already applied`);
                    skipped++;
                } else {
                    console.error(`❌ Migration ${id} failed:`, error);
                    failed++;
                    
                    // Stop on first failure
                    console.log("\n🛑 Migration execution stopped due to failure");
                    break;
                }
            }
            
            console.log("-".repeat(50));
        }

        // Final summary
        console.log("\n" + "=".repeat(60));
        console.log("🏁 MIGRATION RESULTS");
        console.log("=".repeat(60));
        console.log(`✅ Executed: ${executed}`);
        console.log(`⏭️  Skipped: ${skipped}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`📊 Total Migrations: ${this.migrations.size}`);

        if (failed > 0) {
            throw new Error(`${failed} migration(s) failed`);
        }

        console.log("\n🎉 All migrations completed successfully!");
        console.log("🚀 Database is ready for use!");
    }

    async runSpecificMigration(migrationId: string): Promise<void> {
        await this.loadMigrations();
        
        const migration = this.migrations.get(migrationId);
        if (!migration) {
            throw new Error(`Migration ${migrationId} not found`);
        }

        console.log("🚀 Running specific migration...\n");
        console.log("=".repeat(60));
        console.log("DynamoDB Migration Runner - Single Migration");
        console.log("=".repeat(60));

        console.log(`\n📦 Migration ${migrationId}: ${migration.migrationInfo.name}`);
        console.log(`📄 ${migration.migrationInfo.description}`);
        console.log(`📅 Created: ${migration.migrationInfo.createdAt}`);
        console.log("-".repeat(50));

        try {
            const startTime = Date.now();
            await migration.up();
            const endTime = Date.now();
            const duration = endTime - startTime;

            // Validate the migration
            const isValid = await migration.validate();
            if (!isValid) {
                throw new Error("Migration validation failed");
            }

            console.log(`✅ Migration ${migrationId} completed successfully (${duration}ms)`);
            this.executedMigrations.add(migrationId);

            console.log("\n" + "=".repeat(60));
            console.log("🏁 MIGRATION RESULTS");
            console.log("=".repeat(60));
            console.log(`✅ Executed: 1`);
            console.log(`⏭️  Skipped: 0`);
            console.log(`❌ Failed: 0`);

            console.log("\n🎉 Migration completed successfully!");
            console.log("🚀 Database is ready for use!");

        } catch (error) {
            console.error(`❌ Migration ${migrationId} failed:`, error);
            throw error;
        }
    }

    async rollbackMigration(migrationId: string): Promise<void> {
        await this.loadMigrations();
        
        const migration = this.migrations.get(migrationId);
        if (!migration) {
            throw new Error(`Migration ${migrationId} not found`);
        }

        console.log(`🔄 Rolling back migration ${migrationId}: ${migration.migrationInfo.name}`);
        console.log("-".repeat(50));

        try {
            await migration.down();
            console.log(`✅ Migration ${migrationId} rolled back successfully`);
        } catch (error) {
            console.error(`❌ Rollback failed for migration ${migrationId}:`, error);
            throw error;
        }
    }

    async validateMigrations(): Promise<boolean> {
        await this.loadMigrations();
        
        console.log("🔍 Validating all migrations...\n");
        
        const sortedMigrations = Array.from(this.migrations.entries())
            .sort(([a], [b]) => a.localeCompare(b));

        let allValid = true;

        for (const [id, migration] of sortedMigrations) {
            try {
                const isValid = await migration.validate();
                if (isValid) {
                    console.log(`✅ Migration ${id}: Valid`);
                } else {
                    console.log(`❌ Migration ${id}: Invalid`);
                    allValid = false;
                }
            } catch (error) {
                console.log(`❌ Migration ${id}: Validation failed - ${error}`);
                allValid = false;
            }
        }

        console.log(`\n📊 Validation Summary: ${allValid ? 'All migrations valid' : 'Some migrations invalid'}`);
        return allValid;
    }

    async listMigrations(): Promise<void> {
        await this.loadMigrations();
        
        console.log("📋 Available Migrations:\n");
        console.log("=".repeat(80));
        console.log("ID  | Name                           | Version | Created");
        console.log("=".repeat(80));

        const sortedMigrations = Array.from(this.migrations.entries())
            .sort(([a], [b]) => a.localeCompare(b));

        for (const [id, migration] of sortedMigrations) {
            const name = migration.migrationInfo.name.padEnd(30);
            const version = migration.migrationInfo.version.padEnd(7);
            const created = migration.migrationInfo.createdAt.split('T')[0];
            
            console.log(`${id} | ${name} | ${version} | ${created}`);
        }

        console.log("=".repeat(80));
        console.log(`Total: ${this.migrations.size} migration(s)`);
    }

    async getStatus(): Promise<void> {
        await this.loadMigrations();
        
        console.log("📊 Migration Status:\n");
        
        const sortedMigrations = Array.from(this.migrations.entries())
            .sort(([a], [b]) => a.localeCompare(b));

        for (const [id, migration] of sortedMigrations) {
            try {
                const isValid = await migration.validate();
                const status = isValid ? '✅ Applied' : '⏳ Pending';
                console.log(`   ${id}: ${migration.migrationInfo.name} - ${status}`);
            } catch (error) {
                console.log(`   ${id}: ${migration.migrationInfo.name} - ❌ Error`);
            }
        }
        
        console.log();
    }
}

// CLI interface
export async function runMigrationCommand(command: string, ...args: string[]): Promise<void> {
    const runner = new MigrationRunner();
    
    switch (command) {
        case 'run':
        case 'up':
            const targetMigration = args[0];
            await runner.runMigrations(targetMigration);
            break;
            
        case 'run-only':
            const specificMigrationId = args[0];
            if (!specificMigrationId) {
                throw new Error('Migration ID required for run-only');
            }
            await runner.runSpecificMigration(specificMigrationId);
            break;
            
        case 'rollback':
        case 'down':
            const migrationId = args[0];
            if (!migrationId) {
                throw new Error('Migration ID required for rollback');
            }
            await runner.rollbackMigration(migrationId);
            break;
            
        case 'validate':
            const isValid = await runner.validateMigrations();
            if (!isValid) {
                throw new Error('Some migrations are invalid');
            }
            break;
            
        case 'list':
            await runner.listMigrations();
            break;
            
        case 'status':
            await runner.getStatus();
            break;
            
        default:
            console.log("🔧 DynamoDB Migration Runner\n");
            console.log("Available commands:");
            console.log("  run [target]     - Run all migrations (or up to target)");
            console.log("  run-only <id>    - Run only a specific migration");
            console.log("  rollback <id>    - Rollback specific migration");
            console.log("  validate         - Validate all migrations");
            console.log("  list             - List all available migrations");
            console.log("  status           - Show migration status");
            console.log("\nExamples:");
            console.log("  npm run migrate run");
            console.log("  npm run migrate run 001");
            console.log("  npm run migrate run-only 004");
            console.log("  npm run migrate rollback 001");
            console.log("  npm run migrate status");
    }
}

// Run CLI if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const [command, ...args] = process.argv.slice(2);
    
    runMigrationCommand(command || 'help', ...args)
        .then(() => {
            console.log("\n✅ Migration command completed successfully!");
            process.exit(0);
        })
        .catch((error) => {
            console.error("\n💥 Migration command failed:", error);
            process.exit(1);
        });
} 