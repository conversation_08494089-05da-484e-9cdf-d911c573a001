import type { LoaderFunction } from "@remix-run/node";
import { Form, useActionData, useFetcher, useLoaderData, useNavigate } from "@remix-run/react";
import type { MyAddonData } from "~/types/api/businessConsoleService/SellerManagement";
import { createVariationAddonsGroup, deleteAddonVariation, getAddons, getSelectedVarAddonGroup } from "~/services/businessConsoleService";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { ResponsiveTable } from "~/components/ui/responsiveTable";
import { Button } from "~/components/ui/button";
import { Pencil, Trash } from "lucide-react";
import React, { useEffect, useState } from "react";
import { AddOnGroup } from "~/types/api/businessConsoleService/MyItemList";
import SelectedVariationAddons from "~/components/common/selectedVariationAddons";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { Input } from "~/components/ui/input";
import { useDebounce } from "~/hooks/useDebounce";
interface LoaderData {
      selectedVarGruoupData: AddOnGroup[],
      variationName: string,
      sellerId: number,
      variationId: number
}
interface ActionData {
      selectedAddonsData: MyAddonData[],
      sucessMessage: string,
      ErrorMessage: string,
      sucess?: boolean,
      error?: string

}
export const loader: LoaderFunction = withAuth(async ({ request }) => {
      const url = new URL(request.url);
      const page = parseInt(url.searchParams.get("page") || "0");
      const pageSize = parseInt(url.searchParams.get("pageSize") || "50");
      const matchBy = url.searchParams.get("matchBy") || "";
      const sellerId = Number(url.searchParams.get("sellerId"));
      const variationId = Number(url.searchParams.get("variationId"));
      const variationName = url.searchParams.get("variationName");
      try {
            const addonsGroupsResponse = await getSelectedVarAddonGroup(sellerId, variationId, page, pageSize, matchBy, request);
            const selectedVarGruoupData = addonsGroupsResponse.data;
            return withResponse({ selectedVarGruoupData, variationName, sellerId, variationId }, addonsGroupsResponse.headers);
      } catch (error) {
            console.error("Error loading addons groups:", error);
            throw new Response('Failed to load addons', { status: 500 });
      }
});

export const action = withAuth(async ({ request }) => {
      const formData = await request.formData();
      const sellerId = Number(formData.get("sellerId"))
      const pageSize = Number(formData.get("pageSize") || "50");
      const page = Number(formData.get("page") || "0");
      const matchBy = formData.get("matchBy") as string
      const actionType = formData.get("actionType") as string

      if (actionType === "getAddons") {
            try {
                  const addonsList = await getAddons(sellerId, page, pageSize, matchBy, request);
                  const selectedAddonsData = addonsList.data;
                  return withResponse({ selectedAddonsData }, addonsList.headers);
            } catch (error) {
                  console.error("Error loading addons groups:", error);
                  throw new Response('Failed to load addons', { status: 500 });
            }
      }
      else if (
            actionType === "actionAddonforVariation"
      ) {
            // const addonGroupId = Number(formData.get("addonGroupId"));
            const minSelect = Number(formData.get("minSelect"));
            const varient = (formData.get("varient")) as unknown as boolean;
            const maxSelect = Number(formData.get("maxSelect"));
            const addonId = Number(formData.get("addonId"));
            const name = formData.get("name") as string
            const description = formData.get("description") as string
            const variationId = Number(formData.get("variationId"))
            const seq = Number(formData.get("seq"));
            const mode = formData.get("mode") as string;
            const itemVariationId = Number(formData.get("itemVariationId"));

            const payload: AddOnGroup = {
                  sId: addonId.toString(),
                  minSelect: minSelect,
                  maxSelect: maxSelect,
                  name: name,
                  description: description,
                  seq: seq,
                  varient: varient

            }
            const editpayload: AddOnGroup = {
                  id: itemVariationId,
                  sId: addonId.toString(),
                  minSelect: minSelect,
                  maxSelect: maxSelect,
                  name: name,
                  description: description,
                  seq: seq,
                  varient: varient

            }


            const finalPayload = mode == "EditMode" ? editpayload : payload;
            try {
                  const addonsList = await createVariationAddonsGroup(sellerId, variationId, finalPayload, request);
                  return withResponse({ sucess: addonsList.statusCode === 200 }, addonsList.headers);
            } catch (error) {
                  console.error("Error loading addons groups:", error);
                  throw new Response('Failed to load addons', { status: 500 });
            }
      }
      else if (
            actionType == "addonsVarDelete"
      ) {
            const addonVarId = Number(formData.get("addonVarId"));
            const addonGId = Number(formData.get("addonGId"));


            try {
                  const addonsList = await deleteAddonVariation(sellerId, addonVarId, addonGId, request);
                  const selectedAddonsData = addonsList.data;
                  return withResponse({ selectedAddonsData }, addonsList.headers);
            } catch (error) {
                  console.error("Error loading addons groups:", error);
                  throw new Response('Failed to load addons', { status: 500 });
            }
      }
})
const SelectedVariation: React.FC = () => {
      const { selectedVarGruoupData, variationName, sellerId, variationId } = useLoaderData<LoaderData>();
      const navigate = useNavigate();
      const addonMapfetcher = useFetcher<ActionData>()

      console.log(selectedVarGruoupData, "LLLLLLLLLLLLLL")

      const [isAddselectedGroupAddonsOpen, setIsAddselectedGroupAddonsOpen] = useState(false);
      const [selectedGdata, setSelectedGdata] = useState<AddOnGroup>();
      const actionData = useActionData<ActionData>();
      const [isEditopen, setIsEditOpen] = useState(false)

      const [selectedAddonsData, setSelectedAddonsData] = useState<MyAddonData[]>()
      const selectedGroupHeader = [
            "Id",
            "name",
            "description",
            "seq",
            "minSelect",
            "maxSelect",
            "",
            ""
      ];
      useEffect(() => {
            if (addonMapfetcher.state === "idle") {

                  if (actionData?.selectedAddonsData) {
                        setSelectedAddonsData(actionData.selectedAddonsData)
                        setIsAddselectedGroupAddonsOpen(true)
                        setIsEditOpen(false)

                  }
                  else {
                        setSelectedAddonsData([])

                        setIsAddselectedGroupAddonsOpen(false)
                        setIsEditOpen(false)

                  }
            }

      }, [actionData])

      const handleSelectedGroupData = (row: AddOnGroup) => {
            setSelectedGdata(row);
            setIsEditOpen(true);
            setIsAddselectedGroupAddonsOpen(true)
      }
      const handleDelete = (addonsmapData: AddOnGroup) => {
            const formData = new FormData();
            formData.append("actionType", "addonsVarDelete");
            formData.append("addonGId", addonsmapData?.id.toString())
            formData.append("addonVarId", variationId.toString())
            formData.append("sellerId", sellerId.toString())

            addonMapfetcher.submit(formData, { method: 'post' })
      }

      const [searchTerm, setSearchTerm] = useState('');
      const [filteredVariations, setFilteredVariations] = useState<AddOnGroup[]>([])
      const loading = addonMapfetcher.state !== "idle"
      const debouncedSearchTerm = useDebounce(searchTerm, 500);

      useEffect(() => {
            if (debouncedSearchTerm.length >= 2 && debouncedSearchTerm !== "") {
                  const filtered = selectedVarGruoupData.filter((item) =>
                        [item.name, item.description].some(
                              (field) => field?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
                        )
                  )
                  setFilteredVariations(filtered)
            }
            else {
                  setFilteredVariations(selectedVarGruoupData)
            }

      }, [debouncedSearchTerm, selectedVarGruoupData]);
      return (
            <div className="h-full">
                  {loading && <SpinnerLoader loading={loading} size={20} />}

                  <h1 className=" mb-4 font-bold cursor-pointer  hover:text-blue-400  text-black   " onClick={() => navigate(-1)}> <span className="text-2xl">MyItemVariation / </span> <span className="text-xl">{variationName} </span> </h1>
                  <div className="flex flex-wrap mb-2 ">

                        <Input
                              placeholder="Search by  Name or Description"
                              value={searchTerm}
                              type='search'
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm mt-2 rounded-full "
                        />
                  </div>


                  <ResponsiveTable
                        headers={selectedGroupHeader}
                        data={filteredVariations}
                        renderRow={(row) => (
                              <tr key={row.id} className="border-b">
                                    <td className="py-2 px-3 text-center whitespace-normal break-words ">{row.id}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words ">{row?.name}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.description}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.seq}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.minSelect}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.minSelect}</td>

                                    <td className="py-2 px-3 text-center cursor-pointer">
                                          <Button
                                                variant="ghost"
                                                size="sm"
                                                className="text-red-500 hover:text-red-900"
                                                onClick={() => {
                                                      if (confirm("Are you sure you want to delete this ?")) {
                                                            handleDelete(row)
                                                      }
                                                }}
                                                style={{ alignSelf: "flex-end" }}
                                          >
                                                <Trash size={20} />
                                          </Button>
                                    </td>
                                    <td className="py-2 px-3 text-center cursor-pointer">
                                          <Pencil color='blue' size={20} onClick={() => handleSelectedGroupData(row)} />
                                    </td>
                              </tr>
                        )}
                  />
                  <Form method="post" >
                        <input name="sellerId" value={sellerId} hidden />
                        <input name="matchBy" value={""} hidden />
                        <input name="actionType" value={"getAddons"} hidden />

                        <Button
                              className="fixed bottom-5 right-5 rounded-full cursor-pointer"
                              type="submit"
                        >
                              + Create Addon for Item Variation
                        </Button>
                  </Form>
                  <SelectedVariationAddons
                        isOpen={isAddselectedGroupAddonsOpen}
                        items={selectedAddonsData || []}
                        onClose={() => setIsAddselectedGroupAddonsOpen(false)}
                        header={isEditopen ? `Edit Addon for${variationName?.slice(0, 15)}` : `Create Addon for ${variationName?.slice(0, 15)} `} groupData={selectedGdata}
                        sellerId={sellerId}
                        groupId={variationId}
                        isEdit={isEditopen}
                  />
            </div>
      );
};
export default SelectedVariation;
