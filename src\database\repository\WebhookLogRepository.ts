import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, UpdateCommand, QueryCommand, GetCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { WebhookLog, webhookLogTableConfig, MessageCode } from '../entities/WebhookLog.js';
import { 
    WebhookLogFilter, 
    WebhookAnalytics, 
    MessageType, 
    WebhookStatus, 
    WhatsAppMessageStatus 
} from '../../types/webhook.types.js';
import { WhatsAppError } from '../../types/whatsapp-webhook.types.js';
import { nanoid } from 'nanoid';
import { TimeUtil } from '../../utils/time.util.js';

export class WebhookLogRepository {
    private dynamoDB: DynamoDBDocumentClient;

    constructor() {
        const client = new DynamoDBClient({});
        this.dynamoDB = DynamoDBDocumentClient.from(client);
    }

    /**
     * Create a new webhook log entry
     */
    async createLog(logData: WebhookLog): Promise<WebhookLog> {
        await this.dynamoDB.send(new PutCommand({
            TableName: webhookLogTableConfig.TableName,
            Item: logData
        }));

        return logData;
    }

    /**
     * Update an existing webhook log
     */
    async updateLog(webhookId: string, timestamp: number, updates: Partial<WebhookLog>): Promise<void> {
        const updateExpression = [];
        const expressionAttributeNames: Record<string, string> = {};
        const expressionAttributeValues: Record<string, any> = {};

        Object.entries(updates).forEach(([key, value]) => {
            if (value !== undefined) {
                updateExpression.push(`#${key} = :${key}`);
                expressionAttributeNames[`#${key}`] = key;
                expressionAttributeValues[`:${key}`] = value;
            }
        });

        // Always update lastUpdated with current timestamp using TimeUtil
        const currentTimestamp = TimeUtil.getCurrentTimestamp();
        updateExpression.push('#lastUpdated = :lastUpdated', '#lastUpdatedISO = :lastUpdatedISO');
        expressionAttributeNames['#lastUpdated'] = 'lastUpdated';
        expressionAttributeNames['#lastUpdatedISO'] = 'lastUpdatedISO';
        expressionAttributeValues[':lastUpdated'] = currentTimestamp;
        expressionAttributeValues[':lastUpdatedISO'] = TimeUtil.toISTISO(currentTimestamp);

        if (updateExpression.length === 2) {
            // Only lastUpdated fields to update
            return;
        }

        await this.dynamoDB.send(new UpdateCommand({
            TableName: webhookLogTableConfig.TableName,
            Key: {
                webhookId,
                timestamp
            },
            UpdateExpression: `SET ${updateExpression.join(', ')}`,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues
        }));
    }

    /**
     * Get a specific webhook log by ID and timestamp
     */
    async getLog(webhookId: string, timestamp: number): Promise<WebhookLog | null> {
        const result = await this.dynamoDB.send(new GetCommand({
            TableName: webhookLogTableConfig.TableName,
            Key: {
                webhookId,
                timestamp
            }
        }));

        return (result.Item as WebhookLog) || null;
    }

    /**
     * Get webhook logs by business number with optional filtering
     */
    async getLogsByBusinessNumber(businessNumber: string, filter?: WebhookLogFilter): Promise<WebhookLog[]> {
        const expressionAttributeValues: Record<string, any> = {
            ':businessNumber': businessNumber
        };
        const expressionAttributeNames: Record<string, string> = {};
        let keyConditionExpression = 'businessNumber = :businessNumber';
        let filterExpression = '';

        // Add timestamp filtering
        if (filter?.startTimestamp && filter?.endTimestamp) {
            keyConditionExpression += ' AND #timestamp BETWEEN :startTimestamp AND :endTimestamp';
            expressionAttributeValues[':startTimestamp'] = filter.startTimestamp;
            expressionAttributeValues[':endTimestamp'] = filter.endTimestamp;
            expressionAttributeNames['#timestamp'] = 'timestamp';
        }

        // Add additional filters
        const filterConditions = [];
        if (filter?.messageType) {
            filterConditions.push('messageType = :messageType');
            expressionAttributeValues[':messageType'] = filter.messageType;
        }
        if (filter?.messageCode) {
            filterConditions.push('messageCode = :messageCode');
            expressionAttributeValues[':messageCode'] = filter.messageCode;
        }
        if (filter?.status) {
            filterConditions.push('#status = :status');
            expressionAttributeValues[':status'] = filter.status;
            expressionAttributeNames['#status'] = 'status';
        }
        if (filter?.customerNumber) {
            filterConditions.push('customerNumber = :customerNumber');
            expressionAttributeValues[':customerNumber'] = filter.customerNumber;
        }

        if (filterConditions.length > 0) {
            filterExpression = filterConditions.join(' AND ');
        }

        const queryParams: any = {
            TableName: webhookLogTableConfig.TableName,
            IndexName: 'BusinessNumberIndex',
            KeyConditionExpression: keyConditionExpression,
            ExpressionAttributeValues: expressionAttributeValues,
            ScanIndexForward: false, // Sort by timestamp descending
            Limit: filter?.limit || 100
        };

        if (Object.keys(expressionAttributeNames).length > 0) {
            queryParams.ExpressionAttributeNames = expressionAttributeNames;
        }

        if (filterExpression) {
            queryParams.FilterExpression = filterExpression;
        }

        if (filter?.lastEvaluatedKey) {
            queryParams.ExclusiveStartKey = filter.lastEvaluatedKey;
        }

        const result = await this.dynamoDB.send(new QueryCommand(queryParams));
        return (result.Items || []) as WebhookLog[];
    }

    /**
     * Get webhook logs by customer number
     */
    async getLogsByCustomerNumber(customerNumber: string, filter?: WebhookLogFilter): Promise<WebhookLog[]> {
        const expressionAttributeValues: Record<string, any> = {
            ':customerNumber': customerNumber
        };
        const expressionAttributeNames: Record<string, string> = {};
        let keyConditionExpression = 'customerNumber = :customerNumber';

        if (filter?.startTimestamp && filter?.endTimestamp) {
            keyConditionExpression += ' AND #timestamp BETWEEN :startTimestamp AND :endTimestamp';
            expressionAttributeValues[':startTimestamp'] = filter.startTimestamp;
            expressionAttributeValues[':endTimestamp'] = filter.endTimestamp;
            expressionAttributeNames['#timestamp'] = 'timestamp';
        }

        const result = await this.dynamoDB.send(new QueryCommand({
            TableName: webhookLogTableConfig.TableName,
            IndexName: 'CustomerNumberIndex',
            KeyConditionExpression: keyConditionExpression,
            ExpressionAttributeValues: expressionAttributeValues,
            ExpressionAttributeNames: Object.keys(expressionAttributeNames).length > 0 ? expressionAttributeNames : undefined,
            ScanIndexForward: false,
            Limit: filter?.limit || 100
        }));

        return (result.Items || []) as WebhookLog[];
    }

    /**
     * Get webhook logs by message type
     */
    async getLogsByMessageType(messageType: MessageType, filter?: WebhookLogFilter): Promise<WebhookLog[]> {
        const expressionAttributeValues: Record<string, any> = {
            ':messageType': messageType
        };
        const expressionAttributeNames: Record<string, string> = {};
        let keyConditionExpression = 'messageType = :messageType';

        if (filter?.startTimestamp && filter?.endTimestamp) {
            keyConditionExpression += ' AND #timestamp BETWEEN :startTimestamp AND :endTimestamp';
            expressionAttributeValues[':startTimestamp'] = filter.startTimestamp;
            expressionAttributeValues[':endTimestamp'] = filter.endTimestamp;
            expressionAttributeNames['#timestamp'] = 'timestamp';
        }

        const result = await this.dynamoDB.send(new QueryCommand({
            TableName: webhookLogTableConfig.TableName,
            IndexName: 'MessageTypeIndex',
            KeyConditionExpression: keyConditionExpression,
            ExpressionAttributeValues: expressionAttributeValues,
            ExpressionAttributeNames: Object.keys(expressionAttributeNames).length > 0 ? expressionAttributeNames : undefined,
            ScanIndexForward: false,
            Limit: filter?.limit || 100
        }));

        return (result.Items || []) as WebhookLog[];
    }

    /**
     * Get webhook logs by status
     */
    async getLogsByStatus(status: WebhookStatus, filter?: WebhookLogFilter): Promise<WebhookLog[]> {
        const expressionAttributeValues: Record<string, any> = {
            ':status': status
        };
        const expressionAttributeNames: Record<string, string> = {
            '#status': 'status'
        };
        let keyConditionExpression = '#status = :status';

        if (filter?.startTimestamp && filter?.endTimestamp) {
            keyConditionExpression += ' AND #timestamp BETWEEN :startTimestamp AND :endTimestamp';
            expressionAttributeValues[':startTimestamp'] = filter.startTimestamp;
            expressionAttributeValues[':endTimestamp'] = filter.endTimestamp;
            expressionAttributeNames['#timestamp'] = 'timestamp';
        }

        const result = await this.dynamoDB.send(new QueryCommand({
            TableName: webhookLogTableConfig.TableName,
            IndexName: 'StatusIndex',
            KeyConditionExpression: keyConditionExpression,
            ExpressionAttributeValues: expressionAttributeValues,
            ExpressionAttributeNames: expressionAttributeNames,
            ScanIndexForward: false,
            Limit: filter?.limit || 100
        }));

        return (result.Items || []) as WebhookLog[];
    }

    /**
     * 🆕 Get webhook logs by WhatsApp status
     */
    async getLogsByWhatsAppStatus(whatsappStatus: WhatsAppMessageStatus, filter?: WebhookLogFilter): Promise<WebhookLog[]> {
        try {
            const expressionAttributeValues: Record<string, any> = {
                ':whatsappStatus': whatsappStatus
            };
            const expressionAttributeNames: Record<string, string> = {};
            let keyConditionExpression = 'whatsappStatus = :whatsappStatus';

            if (filter?.startTimestamp && filter?.endTimestamp) {
                keyConditionExpression += ' AND #timestamp BETWEEN :startTimestamp AND :endTimestamp';
                expressionAttributeValues[':startTimestamp'] = filter.startTimestamp;
                expressionAttributeValues[':endTimestamp'] = filter.endTimestamp;
                expressionAttributeNames['#timestamp'] = 'timestamp';
            }

            const result = await this.dynamoDB.send(new QueryCommand({
                TableName: webhookLogTableConfig.TableName,
                IndexName: 'WhatsAppStatusIndex',
                KeyConditionExpression: keyConditionExpression,
                ExpressionAttributeValues: expressionAttributeValues,
                ExpressionAttributeNames: Object.keys(expressionAttributeNames).length > 0 ? expressionAttributeNames : undefined,
                ScanIndexForward: false,
                Limit: filter?.limit || 100
            }));

            return (result.Items || []) as WebhookLog[];
            
        } catch (error: any) {
            // If WhatsAppStatusIndex doesn't exist yet, fall back to scan with filter
            if (error.name === 'ValidationException' && error.message?.includes('WhatsAppStatusIndex')) {
                console.warn('⚠️  WhatsAppStatusIndex not found, falling back to scan. Run migration 002 to add the index.');
                
                // Fall back to scan with filter
                const expressionAttributeValues: Record<string, any> = {
                    ':whatsappStatus': whatsappStatus
                };
                
                const result = await this.dynamoDB.send(new ScanCommand({
                    TableName: webhookLogTableConfig.TableName,
                    FilterExpression: 'whatsappStatus = :whatsappStatus',
                    ExpressionAttributeValues: expressionAttributeValues,
                    Limit: filter?.limit || 100
                }));

                return (result.Items || []) as WebhookLog[];
            }
            throw error;
        }
    }

    /**
     * Get webhook logs by message code
     */
    async getLogsByMessageCode(messageCode: MessageCode, filter?: WebhookLogFilter): Promise<WebhookLog[]> {
        const expressionAttributeValues: Record<string, any> = {
            ':messageCode': messageCode
        };
        const expressionAttributeNames: Record<string, string> = {};
        let keyConditionExpression = 'messageCode = :messageCode';

        if (filter?.startTimestamp && filter?.endTimestamp) {
            keyConditionExpression += ' AND #timestamp BETWEEN :startTimestamp AND :endTimestamp';
            expressionAttributeValues[':startTimestamp'] = filter.startTimestamp;
            expressionAttributeValues[':endTimestamp'] = filter.endTimestamp;
            expressionAttributeNames['#timestamp'] = 'timestamp';
        }

        const result = await this.dynamoDB.send(new QueryCommand({
            TableName: webhookLogTableConfig.TableName,
            IndexName: 'MessageCodeIndex',
            KeyConditionExpression: keyConditionExpression,
            ExpressionAttributeValues: expressionAttributeValues,
            ExpressionAttributeNames: Object.keys(expressionAttributeNames).length > 0 ? expressionAttributeNames : undefined,
            ScanIndexForward: false,
            Limit: filter?.limit || 100
        }));

        return (result.Items || []) as WebhookLog[];
    }

    /**
     * Enhanced analytics data with WhatsApp status and error tracking
     */
    async getAnalytics(businessNumber?: string, startTime?: number, endTime?: number): Promise<WebhookAnalytics> {
        let scanParams: any = {
            TableName: webhookLogTableConfig.TableName
        };

        // Use business index if businessNumber is provided
        if (businessNumber) {
            const expressionAttributeValues: Record<string, any> = {
                ':businessNumber': businessNumber
            };
            let keyConditionExpression = 'businessNumber = :businessNumber';

            if (startTime && endTime) {
                keyConditionExpression += ' AND #timestamp BETWEEN :startTime AND :endTime';
                expressionAttributeValues[':startTime'] = startTime;
                expressionAttributeValues[':endTime'] = endTime;
            }

            scanParams = {
                TableName: webhookLogTableConfig.TableName,
                IndexName: 'BusinessNumberIndex',
                KeyConditionExpression: keyConditionExpression,
                ExpressionAttributeValues: expressionAttributeValues,
                ExpressionAttributeNames: startTime && endTime ? { '#timestamp': 'timestamp' } : undefined
            };
        }

        const result = businessNumber 
            ? await this.dynamoDB.send(new QueryCommand(scanParams))
            : await this.dynamoDB.send(new ScanCommand(scanParams));
        
        const logs = (result.Items || []) as WebhookLog[];
        
        // Calculate analytics
        const totalWebhooks = logs.length;
        const messageTypeBreakdown: Record<MessageType, number> = {} as Record<MessageType, number>;
        const messageCodeBreakdown: Record<MessageCode, number> = {} as Record<MessageCode, number>;
        const statusBreakdown: Record<WebhookStatus, number> = {} as Record<WebhookStatus, number>;
        const whatsappStatusBreakdown: Record<WhatsAppMessageStatus, number> = {} as Record<WhatsAppMessageStatus, number>;
        const hourlyBreakdown: Record<number, number> = {};
        const errorBreakdown: Map<number, { count: number; title: string }> = new Map();
        
        let totalProcessingTime = 0;
        let processedCount = 0;
        let errorCount = 0;

        logs.forEach(log => {
            // Message type breakdown
            messageTypeBreakdown[log.messageType] = (messageTypeBreakdown[log.messageType] || 0) + 1;
            
            // Message code breakdown
            if (log.messageCode) {
                messageCodeBreakdown[log.messageCode] = (messageCodeBreakdown[log.messageCode] || 0) + 1;
            }
            
            // Status breakdown
            statusBreakdown[log.status] = (statusBreakdown[log.status] || 0) + 1;
            
            // 🆕 WhatsApp status breakdown
            if (log.whatsappStatus) {
                whatsappStatusBreakdown[log.whatsappStatus] = (whatsappStatusBreakdown[log.whatsappStatus] || 0) + 1;
            }
            
            // 🆕 WhatsApp error breakdown
            if (log.whatsappErrors && log.whatsappErrors.length > 0) {
                log.whatsappErrors.forEach(error => {
                    const existing = errorBreakdown.get(error.code) || { count: 0, title: error.title };
                    errorBreakdown.set(error.code, { count: existing.count + 1, title: error.title });
                });
            }
            
            // Processing time calculation
            if (log.processingDuration) {
                totalProcessingTime += log.processingDuration;
                processedCount++;
            }
            
            // Error count
            if (log.status === WebhookStatus.FAILED) {
                errorCount++;
            }
            
            // Hourly breakdown
            const hour = new Date(log.timestamp).getHours();
            hourlyBreakdown[hour] = (hourlyBreakdown[hour] || 0) + 1;
        });

        const peakHours = Object.entries(hourlyBreakdown)
            .map(([hour, count]) => ({ hour: parseInt(hour), count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 5);

        // Convert error breakdown to array
        const errorBreakdownArray = Array.from(errorBreakdown.entries()).map(([code, data]) => ({
            code,
            count: data.count,
            title: data.title
        }));

        return {
            totalWebhooks,
            messageTypeBreakdown,
            messageCodeBreakdown,
            whatsappStatusBreakdown,
            statusBreakdown,
            averageProcessingTime: processedCount > 0 ? totalProcessingTime / processedCount : 0,
            errorRate: totalWebhooks > 0 ? (errorCount / totalWebhooks) * 100 : 0,
            peakHours,
            errorBreakdown: errorBreakdownArray
        };
    }

    /**
     * Get recent failed webhooks for monitoring
     */
    async getRecentFailures(limit: number = 50): Promise<WebhookLog[]> {
        return this.getLogsByStatus(WebhookStatus.FAILED, { limit });
    }

    /**
     * Batch update webhook statuses
     */
    async batchUpdateStatus(updates: Array<{ 
        webhookId: string; 
        timestamp: number; 
        status: WebhookStatus; 
        errorMessage?: string;
        whatsappStatus?: WhatsAppMessageStatus;
    }>): Promise<void> {
        const promises = updates.map(update => 
            this.updateLog(update.webhookId, update.timestamp, {
                status: update.status,
                errorMessage: update.errorMessage,
                whatsappStatus: update.whatsappStatus,
                processedAt: Date.now()
            })
        );

        await Promise.all(promises);
    }

    /**
     * 🆕 Optimized query by business and date range using BusinessDateIndex
     * This is the most cost-effective way to query business webhook data
     */
    async getLogsByBusinessAndDateRange(
        businessNumber: string, 
        startDate: string, 
        endDate: string,
        limit?: number
    ): Promise<WebhookLog[]> {
        try {
            const result = await this.dynamoDB.send(new QueryCommand({
                TableName: webhookLogTableConfig.TableName,
                IndexName: 'BusinessDateIndex',
                KeyConditionExpression: 'businessNumber = :businessNumber AND datePartition BETWEEN :startDate AND :endDate',
                ExpressionAttributeValues: {
                    ':businessNumber': businessNumber,
                    ':startDate': startDate,
                    ':endDate': endDate
                },
                Limit: limit || 1000,
                ScanIndexForward: false // Most recent first
            }));

            return (result.Items || []) as WebhookLog[];
        } catch (error) {
            console.error('Error querying webhooks by business and date range:', error);
            return [];
        }
    }

    /**
     * 🆕 Query by date partition for daily webhook analytics
     * Very efficient for daily reports
     */
    async getLogsByDate(datePartition: string, limit?: number): Promise<WebhookLog[]> {
        try {
            const result = await this.dynamoDB.send(new QueryCommand({
                TableName: webhookLogTableConfig.TableName,
                IndexName: 'DatePartitionIndex',
                KeyConditionExpression: 'datePartition = :datePartition',
                ExpressionAttributeValues: {
                    ':datePartition': datePartition
                },
                Limit: limit || 1000,
                ScanIndexForward: false
            }));

            return (result.Items || []) as WebhookLog[];
        } catch (error) {
            console.error('Error querying webhooks by date partition:', error);
            return [];
        }
    }

    /**
     * 🆕 Query by month partition for monthly webhook analytics
     * Efficient for monthly reports
     */
    async getLogsByMonth(monthPartition: string, limit?: number): Promise<WebhookLog[]> {
        try {
            const result = await this.dynamoDB.send(new QueryCommand({
                TableName: webhookLogTableConfig.TableName,
                IndexName: 'MonthPartitionIndex',
                KeyConditionExpression: 'monthPartition = :monthPartition',
                ExpressionAttributeValues: {
                    ':monthPartition': monthPartition
                },
                Limit: limit || 1000,
                ScanIndexForward: false
            }));

            return (result.Items || []) as WebhookLog[];
        } catch (error) {
            console.error('Error querying webhooks by month partition:', error);
            return [];
        }
    }

    /**
     * 🆕 Paginated query with cost optimization for webhooks
     * Returns both items and last evaluated key for pagination
     */
    async queryWithPagination(
        indexName: string,
        keyConditionExpression: string,
        expressionAttributeValues: Record<string, any>,
        expressionAttributeNames?: Record<string, string>,
        limit: number = 100,
        lastEvaluatedKey?: Record<string, any>
    ): Promise<{ items: WebhookLog[], lastEvaluatedKey?: Record<string, any> }> {
        try {
            const result = await this.dynamoDB.send(new QueryCommand({
                TableName: webhookLogTableConfig.TableName,
                IndexName: indexName,
                KeyConditionExpression: keyConditionExpression,
                ExpressionAttributeValues: expressionAttributeValues,
                ExpressionAttributeNames: expressionAttributeNames,
                Limit: limit,
                ExclusiveStartKey: lastEvaluatedKey,
                ScanIndexForward: false
            }));

            return {
                items: (result.Items || []) as WebhookLog[],
                lastEvaluatedKey: result.LastEvaluatedKey
            };
        } catch (error) {
            console.error('Error in paginated webhook query:', error);
            return { items: [] };
        }
    }

    /**
     * 🆕 Batch query multiple date partitions for webhook range queries
     * More efficient than single large queries
     */
    async batchQueryByDateRange(
        startDate: string,
        endDate: string,
        limitPerPartition: number = 100
    ): Promise<WebhookLog[]> {
        try {
            const dates = this.generateDateRange(startDate, endDate);
            const allResults: WebhookLog[] = [];

            // Query each date partition separately
            for (const date of dates) {
                const result = await this.getLogsByDate(date, limitPerPartition);
                allResults.push(...result);
            }

            // Sort by timestamp descending
            return allResults.sort((a, b) => b.timestamp - a.timestamp);
        } catch (error) {
            console.error('Error in batch webhook date range query:', error);
            return [];
        }
    }

    /**
     * 🆕 Generate date range between two dates
     */
    private generateDateRange(startDate: string, endDate: string): string[] {
        const dates: string[] = [];
        const start = new Date(startDate);
        const end = new Date(endDate);

        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
            dates.push(d.toISOString().split('T')[0]); // YYYY-MM-DD format
        }

        return dates;
    }

    /**
     * 🆕 Get aggregated webhook analytics for a business without loading all data
     * Uses projection to minimize data transfer
     */
    async getBusinessWebhookAnalytics(businessNumber: string, startDate: string, endDate: string): Promise<{
        totalWebhooks: number;
        statusBreakdown: Record<string, number>;
        messageTypeBreakdown: Record<string, number>;
        dailyCounts: Record<string, number>;
        errorRate: number;
    }> {
        try {
            const logs = await this.getLogsByBusinessAndDateRange(businessNumber, startDate, endDate, 10000);
            
            const analytics = {
                totalWebhooks: logs.length,
                statusBreakdown: {} as Record<string, number>,
                messageTypeBreakdown: {} as Record<string, number>,
                dailyCounts: {} as Record<string, number>,
                errorRate: 0
            };

            let errorCount = 0;

            logs.forEach(log => {
                // Status breakdown
                analytics.statusBreakdown[log.status] = (analytics.statusBreakdown[log.status] || 0) + 1;
                
                // Message type breakdown
                analytics.messageTypeBreakdown[log.messageType] = (analytics.messageTypeBreakdown[log.messageType] || 0) + 1;
                
                // Daily counts
                const date = log.datePartition || new Date(log.timestamp).toISOString().split('T')[0];
                analytics.dailyCounts[date] = (analytics.dailyCounts[date] || 0) + 1;
                
                // Error counting
                if (log.status === 'FAILED' || log.errorMessage) {
                    errorCount++;
                }
            });

            analytics.errorRate = logs.length > 0 ? (errorCount / logs.length) * 100 : 0;

            return analytics;
        } catch (error) {
            console.error('Error getting business webhook analytics:', error);
            return {
                totalWebhooks: 0,
                statusBreakdown: {},
                messageTypeBreakdown: {},
                dailyCounts: {},
                errorRate: 0
            };
        }
    }
} 