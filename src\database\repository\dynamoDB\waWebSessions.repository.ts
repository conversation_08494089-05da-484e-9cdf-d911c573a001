// src/repositories/sessionRepository.ts
import { WaWebSession } from '@entity/dynamoDB/waWebSession.entity.js'
import AWS, { DynamoDB } from 'aws-sdk';

export class WaWebSessionRepository {
    private readonly tableName: string;
    private readonly db: AWS.DynamoDB.DocumentClient;

  constructor(tableName: string, dynamoDb = new AWS.DynamoDB.DocumentClient()) {
      AWS.config.update({
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // Replace with your AWS secret key
        region: process.env.AWS_REGION, // Replace with your DynamoDB region, e.g., 'us-east-1'
      });
      this.tableName = tableName;
      this.db = dynamoDb;
    }

  async createSession(session: WaWebSession): Promise<void> {
    const params: DynamoDB.DocumentClient.PutItemInput = {
      TableName: this.tableName,
      Item: session,
    };

    await this.db.put(params).promise();
  }

  async getSession(token: string): Promise<WaWebSession | null> {
    const params: DynamoDB.DocumentClient.GetItemInput = {
      TableName: this.tableName,
      Key: { token },
    };

    const result = await this.db.get(params).promise();
    return result.Item as WaWebSession | null;
  }

  async deleteSession(token: string): Promise<void> {
    const params: DynamoDB.DocumentClient.DeleteItemInput = {
      TableName: this.tableName,
      Key: { token },
    };

    await this.db.delete(params).promise();
  }
}
