import { Request, Response } from 'express';
import { CustomResponse } from '@interfaces/response.interface.js';

import WaWebSessionService from '@/services/waWebSession.service.js';

class waWebSessionController {
  public waWebSessionService = new WaWebSessionService();

  public createSession = async (req: Request, res: Response) => {
    try {
      const { cMobile, bMobile } = req.body;
      if (!cMobile || !bMobile) {
        res.status(400).json({ok: false, message: 'cMobile and bMobile are required' });
        return;
      }
      const response = await this.waWebSessionService.createSession(bMobile, cMobile);;
      if (response.ok) {
        return res.status(200).json(response);
      }

      return res.status(400).json({ok: false, err: response.err});
    } catch (error) {
        const err = error as Error;
        console.error(err.stack);
        return res.status(500).json({ok: false, err:"something went wrong"});
    }
  };

  public generateTotpToken = async (req: Request, res: Response) => {
    try {
      const { cMobile, bMobile } = req.body;
      if (!cMobile || !bMobile) {
        res.status(400).json({ok: false, message: 'cMobile and bMobile are required' });
        return;
      }
      const response = await this.waWebSessionService.generateTotpToken(bMobile, cMobile);
      if (response.ok) {
        return res.status(200).json(response);
      }
      return res.status(400).json({ok: false, err: response.err});  
    } catch (error) {
        const err = error as Error;
        console.error(err.stack);
        return res.status(500).json({ok: false, err:"something went wrong"});
    }
  };

  public verifyTotpToken = async (req: Request, res: Response) => {
    try {
      const { token } = req.body;
      if (!token) {
        res.status(400).json({ok: false, message: 'token is required' });
        return; 
      }
      const response = await this.waWebSessionService.verifyTotpToken(token);
      if (response.ok) {
        return res.status(200).json(response);
      }
      return res.status(400).json({ok: false, err: response.err});
    } catch (error) {
        const err = error as Error;
        console.error(err.stack);
        return res.status(500).json({ok: false, err:"something went wrong"});
    }
  };  

  public validateSession = async (req: Request, res: Response) => {
    try {
      const { token, newAccessToken = false } = req.body;
      if (!token) {
        res.status(200).json({ok: false, message: 'token is required' });
        return;
      }
      const response = await this.waWebSessionService.validateSession(token, newAccessToken);;
      if (response.ok) {
        return res.status(200).json(response);
      }

      return res.status(200).json({ok: false, err: response.err});
    } catch (error) {
        const err = error as Error;
        console.error(err.stack);
        return res.status(500).json({ok: false, err:"something went wrong"});
    }
  };

}

export default waWebSessionController;
