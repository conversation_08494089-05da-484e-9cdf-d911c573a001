import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle } from "./dialog";
import { Input } from "./input";
import { But<PERSON> } from "./button";
import { SearchItems } from "~/types/api/businessConsoleService/SalesAnalysis";
import { useFetcher } from "@remix-run/react";
import { ActionData } from "~/routes/home.salesAnalysis";




interface ModalProps {
      title: string;
      isOpen: boolean;
      onClose: () => void;
      onSelect: (sales: SearchItems) => void;
      itemList: SearchItems[];
      type: string;
}

export default function SalesDynamicSearchFilters({
      title,
      isOpen,
      onClose,
      onSelect,
      itemList,
      type
}: ModalProps) {
      const [search, setSearch] = useState("");
      const [selectedSeller, setSelectedSeller] = useState<SearchItems | null>(null);
      const [items, setItems] = useState<SearchItems[]>(itemList);
      const [page, setPage] = useState(0);
      const [totalPages, setTotalPages] = useState(100);
      const itemFetcher = useFetcher<ActionData>();

      const handleGetItems = () => {
            if (search.length < 3 && page === 0) return;

            const formData = new FormData();
            formData.append("type", type);
            formData.append("intent", type);
            formData.append("pageNo", page.toString());
            formData.append("size", "10");
            formData.append("matchBy", search);

            itemFetcher.submit(formData, {
                  method: "POST",
                  encType: "application/x-www-form-urlencoded"
            });
      };

      useEffect(() => {
            if (search.length >= 3 || page > 0) {
                  handleGetItems();
            }
      }, [search, page]);

      useEffect(() => {
            if (!search) {
                  setItems(itemList);
            } else if (itemFetcher?.data?.selectedData) {
                  setItems(itemFetcher.data.selectedData);
            }
      }, [search, itemFetcher.data, itemList]);

      const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            setSearch(e.target.value);
            setPage(0);
            if (e.target.value.length >= 3) {
                  handleGetItems();
            }
      };

      console.log(items, "45454544455")
      const nextPage = () => {
            setPage((prev) => (prev < totalPages ? prev + 1 : prev));
            handleGetItems();
      };

      const prevPage = () => {
            setPage((prev) => (prev > 1 ? prev - 1 : prev));
            handleGetItems();
      };

      const handleAddFilter = () => {
            if (selectedSeller) {
                  onSelect(selectedSeller);
            }
            onClose();
      };

      // useEffect(() => {
      //       if (itemFetcher.data?.selectedData) {
      //             console.log("Updating Items:", itemFetcher.data.selectedData);
      //             setItems(itemFetcher.data.selectedData); // Ensure fetched data updates items list
      //       }
      // }, [itemFetcher.data?.selectedData]);
      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="max-w-md p-6 bg-white rounded-lg shadow-lg">
                        <DialogHeader>
                              <DialogTitle className="text-lg font-semibold">{title}</DialogTitle>
                        </DialogHeader>

                        <Input
                              placeholder="Search..."
                              value={search}
                              onChange={handleSearchChange}
                              className="w-full mt-2 border rounded-md p-2"
                        />

                        {itemFetcher.state !== "idle" ? (
                              <p className="text-center text-gray-500 mt-4">Loading...</p>
                        ) : (
                              <div className="max-h-48 overflow-y-auto mt-4 space-y-2">
                                    {items.length > 0 ? (
                                          items.map((item) => (
                                                <label key={item.id} className="flex items-center space-x-2 cursor-pointer p-2 bg-gray-100 rounded-md hover:bg-gray-200">
                                                      <input
                                                            type="radio"
                                                            name="singleSelect"
                                                            checked={selectedSeller?.id === item.id}
                                                            onChange={() => setSelectedSeller(item)}
                                                            className="form-radio h-5 w-5 text-blue-500"
                                                      />
                                                      <span>{item.name}</span>
                                                </label>
                                          ))
                                    ) : (
                                          <p className="text-gray-500 text-center">No results found.</p>
                                    )}
                              </div>
                        )}

                        <div className="flex justify-between items-center mt-4">
                              <Button variant="outline" onClick={prevPage} disabled={page === 1}>
                                    Prev
                              </Button>
                              <span className="text-gray-600">Page {page} of {totalPages}</span>
                              <Button variant="outline" onClick={nextPage} disabled={page === totalPages}>
                                    Next
                              </Button>
                        </div>

                        <DialogFooter className="flex justify-end mt-4">
                              <Button variant="outline" onClick={onClose}>
                                    Close
                              </Button>
                              <Button className="ml-2" onClick={handleAddFilter} disabled={!selectedSeller}>
                                    + Add Filter
                              </Button>
                        </DialogFooter>
                  </DialogContent>
            </Dialog>
      );
}
