import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "../ui/dialog";
import { MyAddonGroupData } from "~/types/api/businessConsoleService/SellerManagement";
import { Form, useActionData, useSubmit } from "@remix-run/react";
import { Search, SquareX } from "lucide-react";
import { AddOnGroup } from "~/types/api/businessConsoleService/MyItemList";

interface SelectedItemAddonsProps {
      isOpen: boolean;
      items: MyAddonGroupData[] | [];
      onClose: () => void;
      header: string;
      groupData?: AddOnGroup;
      sellerId?: number;
      groupId?: number;
      isEdit?: boolean;
}

const SelectedItemAddons: React.FC<SelectedItemAddonsProps> = ({
      isOpen,
      items,
      onClose,
      header,
      groupData,
      sellerId,
      groupId,
      isEdit
}) => {
      // State for search and selection
      const [selectedId, setSelectedId] = useState<string | number | null>(null);
      const [searchTerm, setSearchTerm] = useState("");
      const [filteredAddon, setFilteredAddon] = useState<MyAddonGroupData[]>(items);
      const [choosenAddon, setChoosenAddon] = useState<boolean>(false);
      const [choosenAddonName, setChoosenAddonName] = useState<string>("");
      const submit = useSubmit();
      const actionData = useActionData<{ success?: boolean }>();

      // Form data state with proper initialization
      const [formData, setFormData] = useState<AddOnGroup>({
            sId: "",
            name: "",
            minSelect: 0,
            maxSelect: 0,
            description: "",
            seq: 0,
            varient: false,
      });

      // Initialize form data for edit mode
      useEffect(() => {
            if (groupData) {
                  setFormData({
                        id: groupData.id,
                        sId: groupData.sId,
                        name: groupData.name || "",
                        minSelect: typeof groupData.minSelect === "number" ? groupData.minSelect : 0,
                        maxSelect: typeof groupData.maxSelect === "number" ? groupData.maxSelect : 0,
                        description: groupData.description || "",
                        seq: typeof groupData.seq === "number" ? groupData.seq : 0,
                        varient: !!groupData.varient,
                  });

                  // Set edit mode states
                  if (groupData.sId) {
                        setSelectedId(groupData.sId);
                        setChoosenAddon(true);
                        const selectedAddon = items.find((item) => item.id === Number(groupData.sId));
                        if (selectedAddon) {
                              setChoosenAddonName(selectedAddon.displayName);
                        }
                  }
            } else {
                  // Reset for non-edit mode
                  setSelectedId(null);
                  setChoosenAddon(false);
                  setChoosenAddonName("");
                  setFormData({
                        sId: "",
                        name: "",
                        minSelect: 0,
                        maxSelect: 0,
                        description: "",
                        seq: 0,
                        varient: false,
                  });
            }
      }, [groupData, items, isOpen]);

      // Filter addons based on search term
      useEffect(() => {
            if (searchTerm.length >= 3 && searchTerm !== "") {
                  setFilteredAddon(
                        items?.filter((addon) =>
                              addon?.displayName.toLowerCase().includes(searchTerm.toLowerCase())
                        )
                  );
            } else {
                  setFilteredAddon(items);
            }
      }, [searchTerm, items]);

      // Close modal on successful submission
      useEffect(() => {
            if (actionData?.success) {
                  onClose();
            }
      }, [actionData, onClose]);

      // Handle addon selection
      const handleSelect = (addon: MyAddonGroupData) => {
            setSelectedId(addon.id);
            setChoosenAddon(true);
            setChoosenAddonName(addon.displayName);
      };

      // Handle deselecting addon
      const deselectAddonGroupMap = () => {
            setSelectedId(null);
            setChoosenAddon(false);
            setChoosenAddonName("");
      };

      // Handle cancel button
      const handleCancel = () => {
            if (choosenAddon || selectedId) {
                  // Show addon list again
                  setSelectedId(null);
                  setChoosenAddon(false);
                  setChoosenAddonName("");
                  setSearchTerm("");
                  setFilteredAddon(items);
            } else {
                  // Close modal if no addon is selected
                  onClose();
            }
      };

      if (!isOpen) return null;

      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl">
                        <DialogTitle className="text-2xl font-bold text-gray-900 mb-4">
                              {header}
                        </DialogTitle>

                        <div className="space-y-6">
                              {/* Search and Selection Section */}
                              {!choosenAddon && selectedId === null && (
                                    <>
                                          <div className="relative">
                                                <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                                                      <Search className="h-5 w-5 text-gray-400" />
                                                </div>
                                                <input
                                                      placeholder="Search by Addon Name"
                                                      type="search"
                                                      className="w-full pl-10 p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors"
                                                      autoFocus
                                                      value={searchTerm}
                                                      onChange={(e) => setSearchTerm(e.target.value)}
                                                />
                                          </div>
                                          <div className="mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                                <ul className="space-y-2">
                                                      {filteredAddon.length === 0 ? (
                                                            <p className="p-4 text-gray-500 text-center">
                                                                  No add-ons-group found
                                                            </p>
                                                      ) : (
                                                            filteredAddon?.map((item) => (
                                                                  <li key={item.id} className="flex items-center gap-3">
                                                                        <input
                                                                              type="checkbox"
                                                                              id={`item-${item.id}`}
                                                                              name="selectedItem"
                                                                              value={item.id}
                                                                              checked={selectedId === item.id}
                                                                              onChange={() => handleSelect(item)}
                                                                              className="h-5 w-5 text-blue-600 focus:ring-blue-500 rounded"
                                                                        />
                                                                        <label
                                                                              htmlFor={`item-${item.id}`}
                                                                              className={`cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id
                                                                                    ? "bg-blue-50 border-blue-200"
                                                                                    : "border-gray-200"
                                                                                    } text-gray-800 hover:bg-gray-50 transition-colors`}
                                                                        >
                                                                              {item?.displayName}{" "}
                                                                              <span className="text-gray-500">
                                                                                    ({item?.internalName})
                                                                              </span>
                                                                        </label>
                                                                  </li>
                                                            ))
                                                      )}
                                                </ul>
                                          </div>
                                    </>
                              )}

                              {/* Form Section - Shown when an addon is selected */}
                              {(choosenAddon || selectedId) && (
                                    <div className="space-y-4">
                                          {/* Selected Addon Display */}
                                          <div className="flex items-center justify-between bg-blue-50 p-3 rounded-lg">
                                                <p className="font-medium text-gray-800 truncate max-w-[80%]">
                                                      {choosenAddonName}
                                                </p>
                                                <SquareX
                                                      color="red"
                                                      className="cursor-pointer hover:scale-110 transition-transform"
                                                      onClick={deselectAddonGroupMap}
                                                />
                                          </div>

                                          {/* Form Fields */}
                                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                                <div>
                                                      <label
                                                            htmlFor="name"
                                                            className="block text-sm font-medium text-gray-700"
                                                      >
                                                            Name <span className="text-red-500">*</span>
                                                      </label>
                                                      <input
                                                            type="text"
                                                            name="name"
                                                            id="name"
                                                            value={formData.name}
                                                            onChange={(e) =>
                                                                  setFormData({ ...formData, name: e.target.value })
                                                            }
                                                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                                                            placeholder="Enter name"
                                                            required
                                                      />
                                                </div>
                                                <div>
                                                      <label
                                                            htmlFor="seq"
                                                            className="block text-sm font-medium text-gray-700 mb-1"
                                                      >
                                                            Sequence <span className="text-red-500">*</span>
                                                      </label>
                                                      <input
                                                            type="number"
                                                            id="seq"
                                                            name="seq"
                                                            value={formData.seq}
                                                            onChange={(e) =>
                                                                  setFormData({
                                                                        ...formData,
                                                                        seq: e.target.value === "" ? 0 : Number(e.target.value),
                                                                  })
                                                            }
                                                            min="0"
                                                            required
                                                            className="w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
                                                      />
                                                </div>
                                                <div>
                                                      <label
                                                            htmlFor="minSelect"
                                                            className="block text-sm font-medium text-gray-700"
                                                      >
                                                            Minimum Select
                                                      </label>
                                                      <input
                                                            type="number"
                                                            name="minSelect"
                                                            id="minSelect"
                                                            value={formData.minSelect}
                                                            onChange={(e) =>
                                                                  setFormData({
                                                                        ...formData,
                                                                        minSelect: e.target.value === "" ? 0 : Number(e.target.value),
                                                                  })
                                                            }
                                                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                                                            min="0"
                                                      />
                                                </div>
                                                <div>
                                                      <label
                                                            htmlFor="maxSelect"
                                                            className="block text-sm font-medium text-gray-700"
                                                      >
                                                            Maximum Select
                                                      </label>
                                                      <input
                                                            type="number"
                                                            name="maxSelect"
                                                            id="maxSelect"
                                                            value={formData.maxSelect}
                                                            onChange={(e) =>
                                                                  setFormData({
                                                                        ...formData,
                                                                        maxSelect: e.target.value === "" ? 0 : Number(e.target.value),
                                                                  })
                                                            }
                                                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                                                            min="0"
                                                      />
                                                </div>
                                                <div className="sm:col-span-2">
                                                      <label
                                                            htmlFor="description"
                                                            className="block text-sm font-medium text-gray-700"
                                                      >
                                                            Description
                                                      </label>
                                                      <textarea
                                                            name="description"
                                                            id="description"
                                                            value={formData.description}
                                                            onChange={(e) =>
                                                                  setFormData({ ...formData, description: e.target.value })
                                                            }
                                                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                                                            placeholder="Enter description"
                                                            rows={3}
                                                      />
                                                </div>
                                                {/* <div className="flex items-center gap-2">
                                                      <input
                                                            type="checkbox"
                                                            id="varient"
                                                            name="varient"
                                                            checked={formData.varient}
                                                            onChange={(e) =>
                                                                  setFormData({ ...formData, varient: e.target.checked ? true : false })
                                                            }
                                                            className="h-5 w-5 text-blue-600 rounded focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                                      />
                                                      <label
                                                            htmlFor="varient"
                                                            className="text-sm font-medium text-gray-700"
                                                      >
                                                            Variant
                                                      </label>
                                                </div> */}
                                          </div>
                                    </div>
                              )}
                        </div>

                        {/* Form Submission */}
                        <Form method="POST" className="mt-6 flex flex-col sm:flex-row gap-3 justify-end">
                              {/* Hidden fields to pass data */}
                              <input type="hidden" name="sId" value={selectedId?.toString()} />
                              <input type="hidden" name="itemId" value={groupId?.toString()} />
                              <input type="hidden" name="minSelect" value={formData.minSelect?.toString()} />
                              <input type="hidden" name="name" value={formData.name?.toString()} />
                              <input type="hidden" name="maxSelect" value={formData.maxSelect?.toString()} />
                              <input type="hidden" name="varient" value={formData.varient?.toString()} />
                              <input type="hidden" name="description" value={formData.description?.toString()} />
                              <input type="hidden" name="seq" value={formData.seq?.toString()} />
                              <input type="hidden" name="sellerId" value={sellerId?.toString()} />
                              <input type="hidden" name="addonName" value={choosenAddonName?.toString()} />
                              <input type="hidden" name="actionType" value={"actionItemAddonGroup"} />
                              <input type="hidden" name="mode" value={isEdit ? "EditMode" : ""} />
                              {isEdit && <input type="hidden" name="selectedId" value={groupData?.id?.toString()} />
                              }

                              {/* Action buttons */}
                              <button
                                    type="button"
                                    onClick={handleCancel}
                                    className="w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium transition-colors"
                              >
                                    Cancel
                              </button>
                              <button
                                    type="submit"
                                    disabled={!selectedId || !formData.name}
                                    className={`w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${!selectedId || !formData.name
                                          ? "bg-gray-400 cursor-not-allowed"
                                          : "bg-blue-600 hover:bg-blue-700"
                                          }`}
                              >
                                    Confirm
                              </button>
                        </Form>
                  </DialogContent>
            </Dialog>
      );
};

export default SelectedItemAddons;