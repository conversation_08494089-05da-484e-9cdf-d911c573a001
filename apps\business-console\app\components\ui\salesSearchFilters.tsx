import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "./dialog";
import { Input } from "./input";
import { Button } from "./button";
import { SearchItems } from "~/types/api/businessConsoleService/SalesAnalysis";


interface ModalProps {
      title: string;
      isOpen: boolean;
      onClose: () => void;
      items: any;
      onSelect: (seller: SearchItems) => void;
}
export default function SalesSearchFilters({ title, isOpen, onClose, items, onSelect }: ModalProps) {

      const [search, setSearch] = useState("");

      const filteredItems = items?.filter((item: any) =>
            item?.name?.toLowerCase().includes(search.toLowerCase())

      );
      const [selectedSeller, setSelectedSeller] = useState<{ id: number; name: string } | null>(null);



      const handleAddFilter = () => {
            if (selectedSeller) {
                  onSelect(selectedSeller);
            }
            onClose();
      };

      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="max-w-md p-6 bg-white rounded-lg shadow-lg">
                        <DialogHeader>
                              <DialogTitle className="text-lg font-semibold">{title}</DialogTitle>
                        </DialogHeader>

                        <Input
                              placeholder="Search..."
                              value={search}
                              onChange={(e) => setSearch(e.target.value)}
                              className="w-full mt-2 border rounded-md p-2"
                        />

                        <div className="max-h-48 overflow-y-auto mt-4 space-y-2">
                              {filteredItems.length > 0 ? (
                                    filteredItems?.map((item: any) => (
                                          <label
                                                key={item.id}
                                                className="flex items-center space-x-2 cursor-pointer p-2 bg-gray-100 rounded-md hover:bg-gray-200"
                                          >
                                                <input
                                                      type="radio"
                                                      name="singleSelect" // Ensures only one can be selected
                                                      checked={selectedSeller?.id === item.id}
                                                      onChange={() => setSelectedSeller({ id: item.id, name: item.name })}
                                                      className="form-radio h-5 w-5 text-blue-500"
                                                />
                                                <span>{item.name}</span>
                                          </label>
                                    ))
                              ) : (
                                    <p className="text-gray-500 text-center">No results found.</p>
                              )}
                        </div>


                        <DialogFooter className="flex justify-end mt-4">
                              <Button variant="outline" onClick={onClose}>Close</Button>
                              <Button className="ml-2" onClick={handleAddFilter}>+ Add Filter</Button>
                        </DialogFooter>

                  </DialogContent>
            </Dialog>
      );
}
