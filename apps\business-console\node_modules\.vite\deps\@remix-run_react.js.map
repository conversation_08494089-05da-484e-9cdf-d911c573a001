{"version": 3, "sources": ["../../react-router/lib/context.ts", "../../react-router/lib/hooks.tsx", "../../react-router/lib/deprecations.ts", "../../react-router/lib/components.tsx", "../../react-router/index.ts", "../../react-router-dom/dom.ts", "../../react-router-dom/index.tsx", "../../@remix-run/react/dist/esm/index.js", "../../@remix-run/react/dist/esm/browser.js", "../../@remix-run/react/dist/esm/_virtual/_rollupPluginBabelHelpers.js", "../../@remix-run/react/dist/esm/components.js", "../../@remix-run/react/dist/esm/invariant.js", "../../@remix-run/react/dist/esm/routeModules.js", "../../@remix-run/react/dist/esm/links.js", "../../@remix-run/react/dist/esm/markup.js", "../../@remix-run/react/dist/esm/single-fetch.js", "../../@remix-run/react/dist/esm/data.js", "../../@remix-run/react/dist/esm/fog-of-war.js", "../../@remix-run/react/dist/esm/routes.js", "../../@remix-run/react/dist/esm/errorBoundaries.js", "../../@remix-run/react/dist/esm/fallback.js", "../../@remix-run/react/dist/esm/errors.js", "../../@remix-run/react/dist/esm/scroll-restoration.js", "../../@remix-run/react/dist/esm/server.js", "../../react-router-dom/server.mjs"], "sourcesContent": ["import * as React from \"react\";\nimport type {\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  AgnosticRouteMatch,\n  History,\n  LazyRouteFunction,\n  Location,\n  Action as NavigationType,\n  RelativeRoutingType,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject\n  // Omit `future` since those can be pulled from the `router`\n  // `NavigationContext` needs future since it doesn't have a `router` in all cases\n  extends Omit<NavigationContextObject, \"future\"> {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  flushSync?: boolean;\n  viewTransition?: boolean;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n  future: {\n    v7_relativeSplatPath: boolean;\n  };\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n  isDataRoute: boolean;\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n  isDataRoute: false,\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  AgnosticRouteMatch,\n  Blocker,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RevalidationState,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  IDLE_BLOCKER,\n  Action as NavigationType,\n  UNSAFE_convertRouteMatchToUiMatch as convertRouteMatchToUiMatch,\n  UNSAFE_decodePath as decodePath,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  DataRouteMatch,\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/v6/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/v6/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, decodePath(pathname)),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nconst navigateEffectWarning =\n  `You should call navigate() in a React.useEffect(), not when ` +\n  `your component is first rendered.`;\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(\n  cb: Parameters<typeof React.useLayoutEffect>[0]\n) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  let { isDataRoute } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\n\nfunction useNavigateUnstable(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let { basename, future, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our history listener yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history (but only if we're not in a data router,\n      // otherwise it'll prepend the basename inside of the router).\n      // If this is a root navigation, then we navigate to the raw basename\n      // which allows the basename to have full control over the presence of a\n      // trailing slash on root links\n      if (dataRouterContext == null && basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [\n      basename,\n      navigator,\n      routePathnamesJson,\n      locationPathname,\n      dataRouterContext,\n    ]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/v6/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/v6/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/v6/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/v6/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { future } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/v6/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nexport function useRoutesImpl(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string,\n  dataRouterState?: RemixRouter[\"state\"],\n  future?: RemixRouter[\"future\"]\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator, static: isStatic } = React.useContext(NavigationContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n\n  let matches =\n    !isStatic &&\n    dataRouterState &&\n    dataRouterState.matches &&\n    dataRouterState.matches.length > 0\n      ? (dataRouterState.matches as AgnosticRouteMatch<string, RouteObject>[])\n      : matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined ||\n        matches[matches.length - 1].route.Component !== undefined ||\n        matches[matches.length - 1].route.lazy !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" ` +\n        `does not have an element or Component. This means it will render an <Outlet /> with a ` +\n        `null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterState,\n    future\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    console.error(\n      \"Error handled by React Router default ErrorBoundary:\",\n      error\n    );\n\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own <code style={codeStyles}>ErrorBoundary</code> or{\" \"}\n          <code style={codeStyles}>errorElement</code> prop on your route.\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\nconst defaultErrorElement = <DefaultErrorComponent />;\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (\n      state.location !== props.location ||\n      (state.revalidation !== \"idle\" && props.revalidation === \"idle\")\n    ) {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error !== undefined ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    (match.route.errorElement || match.route.ErrorBoundary)\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState: RemixRouter[\"state\"] | null = null,\n  future: RemixRouter[\"future\"] | null = null\n): React.ReactElement | null {\n  if (matches == null) {\n    if (!dataRouterState) {\n      return null;\n    }\n\n    if (dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else if (\n      future?.v7_partialHydration &&\n      parentMatches.length === 0 &&\n      !dataRouterState.initialized &&\n      dataRouterState.matches.length > 0\n    ) {\n      // Don't bail if we're initializing with partial hydration and we have\n      // router matches.  That means we're actively running `patchRoutesOnNavigation`\n      // so we should render down the partial matches to the appropriate\n      // `HydrateFallback`.  We only do this if `parentMatches` is empty so it\n      // only impacts the root matches for `RouterProvider` and no descendant\n      // `<Routes>`\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id] !== undefined\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for errors on route IDs: ${Object.keys(\n        errors\n      ).join(\",\")}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n\n      if (match.route.id) {\n        let { loaderData, errors } = dataRouterState;\n        let needsToRunLoader =\n          match.route.loader &&\n          loaderData[match.route.id] === undefined &&\n          (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error: any;\n    let shouldRenderHydrateFallback = false;\n    let errorElement: React.ReactNode | null = null;\n    let hydrateFallbackElement: React.ReactNode | null = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\n            \"route-fallback\",\n            false,\n            \"No `HydrateFallback` element provided to render during initial hydration\"\n          );\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children: React.ReactNode;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = <match.route.Component />;\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return (\n        <RenderedRoute\n          match={match}\n          routeContext={{\n            outlet,\n            matches,\n            isDataRoute: dataRouterState != null,\n          }}\n          children={children}\n        />\n      );\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState &&\n      (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        revalidation={dataRouterState.revalidation}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches, isDataRoute: true }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n}\n\nenum DataRouterStateHook {\n  UseBlocker = \"useBlocker\",\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n  UseRouteId = \"useRouteId\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nexport function useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(\n    () => ({\n      revalidate: dataRouterContext.router.revalidate,\n      state: state.revalidation,\n    }),\n    [dataRouterContext.router.revalidate, state.revalidation]\n  );\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches(): UIMatch[] {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () => matches.map((m) => convertRouteMatchToUiMatch(m, loaderData)),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router, basename } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (arg) => {\n      if (typeof shouldBlock !== \"function\") {\n        return !!shouldBlock;\n      }\n      if (basename === \"/\") {\n        return shouldBlock(arg);\n      }\n\n      // If they provided us a function and we've got an active basename, strip\n      // it from the locations we expose to the user to match the behavior of\n      // useLocation\n      let { currentLocation, nextLocation, historyAction } = arg;\n      return shouldBlock({\n        currentLocation: {\n          ...currentLocation,\n          pathname:\n            stripBasename(currentLocation.pathname, basename) ||\n            currentLocation.pathname,\n        },\n        nextLocation: {\n          ...nextLocation,\n          pathname:\n            stripBasename(nextLocation.pathname, basename) ||\n            nextLocation.pathname,\n        },\n        historyAction,\n      });\n    },\n    [basename, shouldBlock]\n  );\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey)\n    ? state.blockers.get(blockerKey)!\n    : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable(): NavigateFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our router subscriber yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        router.navigate(to);\n      } else {\n        router.navigate(to, { fromRouteId: id, ...options });\n      }\n    },\n    [router, id]\n  );\n\n  return navigate;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import type { FutureConfig as RouterFutureConfig } from \"@remix-run/router\";\nimport type { FutureConfig as RenderFutureConfig } from \"./components\";\n\nconst alreadyWarned: { [key: string]: boolean } = {};\n\nexport function warnOnce(key: string, message: string): void {\n  if (__DEV__ && !alreadyWarned[message]) {\n    alreadyWarned[message] = true;\n    console.warn(message);\n  }\n}\n\nconst logDeprecation = (flag: string, msg: string, link: string) =>\n  warnOnce(\n    flag,\n    `⚠️ React Router Future Flag Warning: ${msg}. ` +\n      `You can use the \\`${flag}\\` future flag to opt-in early. ` +\n      `For more information, see ${link}.`\n  );\n\nexport function logV6DeprecationWarnings(\n  renderFuture: Partial<RenderFutureConfig> | undefined,\n  routerFuture?: Omit<RouterFutureConfig, \"v7_prependBasename\">\n) {\n  if (renderFuture?.v7_startTransition === undefined) {\n    logDeprecation(\n      \"v7_startTransition\",\n      \"React Router will begin wrapping state updates in `React.startTransition` in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_starttransition\"\n    );\n  }\n\n  if (\n    renderFuture?.v7_relativeSplatPath === undefined &&\n    (!routerFuture || !routerFuture.v7_relativeSplatPath)\n  ) {\n    logDeprecation(\n      \"v7_relativeSplatPath\",\n      \"Relative route resolution within Splat routes is changing in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath\"\n    );\n  }\n\n  if (routerFuture) {\n    if (routerFuture.v7_fetcherPersist === undefined) {\n      logDeprecation(\n        \"v7_fetcherPersist\",\n        \"The persistence behavior of fetchers is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist\"\n      );\n    }\n\n    if (routerFuture.v7_normalizeFormMethod === undefined) {\n      logDeprecation(\n        \"v7_normalizeFormMethod\",\n        \"Casing of `formMethod` fields is being normalized to uppercase in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod\"\n      );\n    }\n\n    if (routerFuture.v7_partialHydration === undefined) {\n      logDeprecation(\n        \"v7_partialHydration\",\n        \"`RouterProvider` hydration behavior is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_partialhydration\"\n      );\n    }\n\n    if (routerFuture.v7_skipActionErrorRevalidation === undefined) {\n      logDeprecation(\n        \"v7_skipActionErrorRevalidation\",\n        \"The revalidation behavior after 4xx/5xx `action` responses is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation\"\n      );\n    }\n  }\n}\n", "import type {\n  InitialEntry,\n  LazyRouteFunction,\n  Location,\n  MemoryHistory,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RouterState,\n  RouterSubscriber,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\nimport * as React from \"react\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./context\";\nimport {\n  _renderMatches,\n  useAsyncValue,\n  useInRouterContext,\n  useLocation,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  useRoutesImpl,\n} from \"./hooks\";\nimport { logV6DeprecationWarnings } from \"./deprecations\";\n\nexport interface FutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_startTransition: boolean;\n}\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n  // Only accept future flags relevant to rendering behavior\n  // routing flags should be accessed via router.future\n  future?: Partial<Pick<FutureConfig, \"v7_startTransition\">>;\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let { v7_startTransition } = future || {};\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (newState: RouterState) => {\n      if (v7_startTransition && startTransitionImpl) {\n        startTransitionImpl(() => setStateImpl(newState));\n      } else {\n        setStateImpl(newState);\n      }\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [router, future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={basename}\n            location={state.location}\n            navigationType={state.historyAction}\n            navigator={navigator}\n            future={{\n              v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n            }}\n          >\n            {state.initialized || router.future.v7_partialHydration ? (\n              <DataRoutes\n                routes={router.routes}\n                future={router.future}\n                state={state}\n              />\n            ) : (\n              fallbackElement\n            )}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  future?: Partial<FutureConfig>;\n}\n\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/v6/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n  future,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/v6/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  let { future, static: isStatic } = React.useContext(NavigationContext);\n\n  warning(\n    !isStatic,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(\n    to,\n    getResolveToMatches(matches, future.v7_relativeSplatPath),\n    locationPathname,\n    relative === \"path\"\n  );\n  let jsonPath = JSON.stringify(path);\n\n  React.useEffect(\n    () => navigate(JSON.parse(jsonPath), { replace, state, relative }),\n    [navigate, jsonPath, relative, replace, state]\n  );\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/v6/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<NonIndexRouteObject>;\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<IndexRouteObject>;\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/v6/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n  future?: Partial<Pick<FutureConfig, \"v7_relativeSplatPath\">>;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/v6/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n  future,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({\n      basename,\n      navigator,\n      static: staticProp,\n      future: {\n        v7_relativeSplatPath: false,\n        ...future,\n      },\n    }),\n    [basename, future, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key,\n      },\n      navigationType,\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n\n  warning(\n    locationContext != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (locationContext == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider children={children} value={locationContext} />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/v6/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        \"_error\" in promise\n          ? AwaitRenderStatus.error\n          : \"_data\" in promise\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/v6/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    let treePath = [...parentPath, index];\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, treePath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary:\n        element.props.ErrorBoundary != null ||\n        element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n", "import * as React from \"react\";\nimport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AgnosticPatchRoutesOnNavigationFunction,\n  AgnosticPatchRoutesOnNavigationFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  HydrationState,\n  InitialEntry,\n  JsonFunction,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  RedirectFunction,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  FutureConfig as RouterFutureConfig,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  resolvePath,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  FutureConfig,\n  IndexRouteProps,\n  LayoutRouteProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  PathRouteProps,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n} from \"./lib/components\";\nimport {\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createRoutesFromChildren,\n  renderMatches,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  NavigateOptions,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteId,\n  useRouteLoaderData,\n  useRoutes,\n  useRoutesImpl,\n} from \"./lib/hooks\";\nimport { logV6DeprecationWarnings } from \"./lib/deprecations\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  Pathname,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  Blocker,\n  BlockerFunction,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useBlocker,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nexport type PatchRoutesOnNavigationFunctionArgs =\n  AgnosticPatchRoutesOnNavigationFunctionArgs<RouteObject, RouteMatch>;\n\nexport type PatchRoutesOnNavigationFunction =\n  AgnosticPatchRoutesOnNavigationFunction<RouteObject, RouteMatch>;\n\nfunction mapRouteProperties(route: RouteObject) {\n  let updates: Partial<RouteObject> & { hasErrorBoundary: boolean } = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null,\n  };\n\n  if (route.Component) {\n    if (__DEV__) {\n      if (route.element) {\n        warning(\n          false,\n          \"You should not include both `Component` and `element` on your route - \" +\n            \"`Component` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      element: React.createElement(route.Component),\n      Component: undefined,\n    });\n  }\n\n  if (route.HydrateFallback) {\n    if (__DEV__) {\n      if (route.hydrateFallbackElement) {\n        warning(\n          false,\n          \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" +\n            \"`HydrateFallback` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined,\n    });\n  }\n\n  if (route.ErrorBoundary) {\n    if (__DEV__) {\n      if (route.errorElement) {\n        warning(\n          false,\n          \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" +\n            \"`ErrorBoundary` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      errorElement: React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined,\n    });\n  }\n\n  return updates;\n}\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n    dataStrategy?: DataStrategyFunction;\n    patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  LocationContext as UNSAFE_LocationContext,\n  NavigationContext as UNSAFE_NavigationContext,\n  RouteContext as UNSAFE_RouteContext,\n  mapRouteProperties as UNSAFE_mapRouteProperties,\n  useRouteId as UNSAFE_useRouteId,\n  useRoutesImpl as UNSAFE_useRoutesImpl,\n  logV6DeprecationWarnings as UNSAFE_logV6DeprecationWarnings,\n};\n", "import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename, UNSAFE_warning as warning } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n\n  return searchParams;\n}\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\nexport type SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | JsonValue\n  | null;\n\n// One-time check for submitter support\nlet _formDataSupportsSubmitter: boolean | null = null;\n\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\n\n/**\n * Submit options shared by both navigations and fetchers\n */\ninterface SharedSubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The encoding used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * Enable flushSync for this submission's state updates\n   */\n  flushSync?: boolean;\n}\n\n/**\n * Submit options available to fetchers\n */\nexport interface FetcherSubmitOptions extends SharedSubmitOptions {}\n\n/**\n * Submit options available to navigations\n */\nexport interface SubmitOptions extends FetcherSubmitOptions {\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Enable view transitions on this submission navigation\n   */\n  viewTransition?: boolean;\n}\n\nconst supportedFormEncTypes: Set<FormEncType> = new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\",\n]);\n\nfunction getFormEncType(encType: string | null) {\n  if (encType != null && !supportedFormEncTypes.has(encType as FormEncType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` ` +\n        `and will default to \"${defaultEncType}\"`\n    );\n\n    return null;\n  }\n  return encType;\n}\n\nexport function getFormSubmissionInfo(\n  target: SubmitTarget,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData | undefined;\n  body: any;\n} {\n  let method: string;\n  let action: string | null;\n  let encType: string;\n  let formData: FormData | undefined;\n  let body: any;\n\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n\n    formData = new FormData(target);\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n\n    method =\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      getFormEncType(target.getAttribute(\"formenctype\")) ||\n      getFormEncType(form.getAttribute(\"enctype\")) ||\n      defaultEncType;\n\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport type {\n  DataRouteObject,\n  FutureConfig,\n  Location,\n  NavigateOptions,\n  NavigationType,\n  Navigator,\n  RelativeRoutingType,\n  RouteObject,\n  RouterProps,\n  RouterProviderProps,\n  To,\n  DataStrategyFunction,\n  PatchRoutesOnNavigationFunction,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_logV6DeprecationWarnings as logV6DeprecationWarnings,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n  UNSAFE_useRoutesImpl as useRoutesImpl,\n} from \"react-router\";\nimport type {\n  BrowserHistory,\n  Fetcher,\n  FormEncType,\n  FormMethod,\n  FutureConfig as RouterFutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n  RouterState,\n  RouterSubscriber,\n  BlockerFunction,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  UNSAFE_ErrorResponseImpl as ErrorResponseImpl,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n  matchPath,\n  IDLE_FETCHER,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n  SubmitTarget,\n  FetcherSubmitOptions,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams, ErrorResponseImpl as UNSAFE_ErrorResponseImpl };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker,\n  BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  PatchRoutesOnNavigationFunction,\n  PatchRoutesOnNavigationFunctionArgs,\n  Path,\n  PathMatch,\n  Pathname,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n  var __reactRouterVersion: string;\n  interface Document {\n    startViewTransition(cb: () => Promise<void> | void): ViewTransition;\n  }\n}\n\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"0\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Contexts\n////////////////////////////////////////////////////////////////////////////////\n\ntype ViewTransitionContextObject =\n  | {\n      isTransitioning: false;\n    }\n  | {\n      isTransitioning: true;\n      flushSync: boolean;\n      currentLocation: Location;\n      nextLocation: Location;\n    };\n\nconst ViewTransitionContext = React.createContext<ViewTransitionContextObject>({\n  isTransitioning: false,\n});\nif (__DEV__) {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\n\nexport { ViewTransitionContext as UNSAFE_ViewTransitionContext };\n\n// TODO: (v7) Change the useFetcher data from `any` to `unknown`\ntype FetchersContextObject = Map<string, any>;\n\nconst FetchersContext = React.createContext<FetchersContextObject>(new Map());\nif (__DEV__) {\n  FetchersContext.displayName = \"Fetchers\";\n}\n\nexport { FetchersContext as UNSAFE_FetchersContext };\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\n\nfunction startTransitionSafe(cb: () => void) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\n\nfunction flushSyncSafe(cb: () => void) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\n\ninterface ViewTransition {\n  finished: Promise<void>;\n  ready: Promise<void>;\n  updateCallbackDone: Promise<void>;\n  skipTransition(): void;\n}\n\nclass Deferred<T> {\n  status: \"pending\" | \"resolved\" | \"rejected\" = \"pending\";\n  promise: Promise<T>;\n  // @ts-expect-error - no initializer\n  resolve: (value: T) => void;\n  // @ts-expect-error - no initializer\n  reject: (reason?: unknown) => void;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = (value) => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = (reason) => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState<RouterState>();\n  let [vtContext, setVtContext] = React.useState<ViewTransitionContextObject>({\n    isTransitioning: false,\n  });\n  let [renderDfd, setRenderDfd] = React.useState<Deferred<void>>();\n  let [transition, setTransition] = React.useState<ViewTransition>();\n  let [interruption, setInterruption] = React.useState<{\n    state: RouterState;\n    currentLocation: Location;\n    nextLocation: Location;\n  }>();\n  let fetcherData = React.useRef<Map<string, any>>(new Map());\n  let { v7_startTransition } = future || {};\n\n  let optInStartTransition = React.useCallback(\n    (cb: () => void) => {\n      if (v7_startTransition) {\n        startTransitionSafe(cb);\n      } else {\n        cb();\n      }\n    },\n    [v7_startTransition]\n  );\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (\n      newState: RouterState,\n      {\n        deletedFetchers,\n        flushSync: flushSync,\n        viewTransitionOpts: viewTransitionOpts,\n      }\n    ) => {\n      newState.fetchers.forEach((fetcher, key) => {\n        if (fetcher.data !== undefined) {\n          fetcherData.current.set(key, fetcher.data);\n        }\n      });\n      deletedFetchers.forEach((key) => fetcherData.current.delete(key));\n\n      let isViewTransitionUnavailable =\n        router.window == null ||\n        router.window.document == null ||\n        typeof router.window.document.startViewTransition !== \"function\";\n\n      // If this isn't a view transition or it's not available in this browser,\n      // just update and be done with it\n      if (!viewTransitionOpts || isViewTransitionUnavailable) {\n        if (flushSync) {\n          flushSyncSafe(() => setStateImpl(newState));\n        } else {\n          optInStartTransition(() => setStateImpl(newState));\n        }\n        return;\n      }\n\n      // flushSync + startViewTransition\n      if (flushSync) {\n        // Flush through the context to mark DOM elements as transition=ing\n        flushSyncSafe(() => {\n          // Cancel any pending transitions\n          if (transition) {\n            renderDfd && renderDfd.resolve();\n            transition.skipTransition();\n          }\n          setVtContext({\n            isTransitioning: true,\n            flushSync: true,\n            currentLocation: viewTransitionOpts.currentLocation,\n            nextLocation: viewTransitionOpts.nextLocation,\n          });\n        });\n\n        // Update the DOM\n        let t = router.window!.document.startViewTransition(() => {\n          flushSyncSafe(() => setStateImpl(newState));\n        });\n\n        // Clean up after the animation completes\n        t.finished.finally(() => {\n          flushSyncSafe(() => {\n            setRenderDfd(undefined);\n            setTransition(undefined);\n            setPendingState(undefined);\n            setVtContext({ isTransitioning: false });\n          });\n        });\n\n        flushSyncSafe(() => setTransition(t));\n        return;\n      }\n\n      // startTransition + startViewTransition\n      if (transition) {\n        // Interrupting an in-progress transition, cancel and let everything flush\n        // out, and then kick off a new transition from the interruption state\n        renderDfd && renderDfd.resolve();\n        transition.skipTransition();\n        setInterruption({\n          state: newState,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      } else {\n        // Completed navigation update with opted-in view transitions, let 'er rip\n        setPendingState(newState);\n        setVtContext({\n          isTransitioning: true,\n          flushSync: false,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      }\n    },\n    [router.window, transition, renderDfd, fetcherData, optInStartTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred<void>());\n    }\n  }, [vtContext]);\n\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({ isTransitioning: false });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (\n      renderDfd &&\n      pendingState &&\n      state.location.key === pendingState.location.key\n    ) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation,\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  let routerFuture = React.useMemo<RouterProps[\"future\"]>(\n    () => ({\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n    }),\n    [router.future.v7_relativeSplatPath]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [future, router.future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <FetchersContext.Provider value={fetcherData.current}>\n            <ViewTransitionContext.Provider value={vtContext}>\n              <Router\n                basename={basename}\n                location={state.location}\n                navigationType={state.historyAction}\n                navigator={navigator}\n                future={routerFuture}\n              >\n                {state.initialized || router.future.v7_partialHydration ? (\n                  <MemoizedDataRoutes\n                    routes={router.routes}\n                    future={router.future}\n                    state={state}\n                  />\n                ) : (\n                  fallbackElement\n                )}\n              </Router>\n            </ViewTransitionContext.Provider>\n          </FetchersContext.Provider>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = React.memo(DataRoutes);\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  future,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({\n  basename,\n  children,\n  future,\n  window,\n}: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({\n  basename,\n  children,\n  future,\n  history,\n}: HistoryRouterProps) {\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n  viewTransition?: boolean;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      viewTransition,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport type NavLinkRenderProps = {\n  isActive: boolean;\n  isPending: boolean;\n  isTransitioning: boolean;\n};\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?: React.ReactNode | ((props: NavLinkRenderProps) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: NavLinkRenderProps) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: NavLinkRenderProps) => React.CSSProperties | undefined);\n}\n\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      viewTransition,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator, basename } = React.useContext(NavigationContext);\n    let isTransitioning =\n      routerState != null &&\n      // Conditional usage is OK here because the usage of a data router is static\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useViewTransitionState(path) &&\n      viewTransition === true;\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    if (nextLocationPathname && basename) {\n      nextLocationPathname =\n        stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n    }\n\n    // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n    // we're looking for a slash _after_ what's in `to`.  For example:\n    //\n    // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n    // both want to look for a / at index 6 to match URL `/users/matt`\n    const endSlashPosition =\n      toPathname !== \"/\" && toPathname.endsWith(\"/\")\n        ? toPathname.length - 1\n        : toPathname.length;\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(endSlashPosition) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let renderProps = {\n      isActive,\n      isPending,\n      isTransitioning,\n    };\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp(renderProps);\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n        isTransitioning ? \"transitioning\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n        viewTransition={viewTransition}\n      >\n        {typeof children === \"function\" ? children(renderProps) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n/**\n * Form props shared by navigations and fetchers\n */\ninterface SharedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * `<form encType>` - enhancing beyond the normal string type and limiting\n   * to the built-in browser supported values\n   */\n  encType?:\n    | \"application/x-www-form-urlencoded\"\n    | \"multipart/form-data\"\n    | \"text/plain\";\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * Form props available to fetchers\n */\nexport interface FetcherFormProps extends SharedFormProps {}\n\n/**\n * Form props available to navigations\n */\nexport interface FormProps extends SharedFormProps {\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Enable view transitions on this Form navigation\n   */\n  viewTransition?: boolean;\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (\n    {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      viewTransition,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmit();\n    let formAction = useFormAction(action, { relative });\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        fetcherKey,\n        method: submitMethod,\n        navigate,\n        replace,\n        state,\n        relative,\n        preventScrollReset,\n        viewTransition,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmit = \"useSubmit\",\n  UseSubmitFetcher = \"useSubmitFetcher\",\n  UseFetcher = \"useFetcher\",\n  useViewTransitionState = \"useViewTransitionState\",\n}\n\nenum DataRouterStateHook {\n  UseFetcher = \"useFetcher\",\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\n// Internal hooks\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n// External hooks\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    viewTransition,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n    viewTransition?: boolean;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, {\n          replace,\n          state,\n          preventScrollReset,\n          relative,\n          viewTransition,\n        });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Submits a fetcher `<form>` to the server without reloading the page.\n */\nexport interface FetcherSubmitFunction {\n  (\n    target: SubmitTarget,\n    // Fetchers cannot replace or set state because they are not navigation events\n    options?: FetcherSubmitOptions\n  ): void;\n}\n\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\n      \"You are calling submit during the server render. \" +\n        \"Try calling submit within a `useEffect` or callback instead.\"\n    );\n  }\n}\n\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => `__${String(++fetcherId)}__`;\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback<SubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      if (options.navigate === false) {\n        let key = options.fetcherKey || getUniqueFetcherId();\n        router.fetch(key, currentRouteId, options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          flushSync: options.flushSync,\n        });\n      } else {\n        router.navigate(options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          replace: options.replace,\n          state: options.state,\n          fromRouteId: currentRouteId,\n          flushSync: options.flushSync,\n          viewTransition: options.viewTransition,\n        });\n      }\n    },\n    [router, basename, currentRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some((v) => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: React.ForwardRefExoticComponent<\n    FetcherFormProps & React.RefAttributes<HTMLFormElement>\n  >;\n  submit: FetcherSubmitFunction;\n  load: (href: string, opts?: { flushSync?: boolean }) => void;\n};\n\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>({\n  key,\n}: { key?: string } = {}): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(RouteContext);\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n\n  invariant(fetcherData, `useFetcher must be used inside a FetchersContext`);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState<string>(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  // Fetcher additions\n  let load = React.useCallback(\n    (href: string, opts?: { flushSync?: boolean }) => {\n      invariant(routeId, \"No routeId available for fetcher.load()\");\n      router.fetch(fetcherKey, routeId, href, opts);\n    },\n    [fetcherKey, routeId, router]\n  );\n\n  let submitImpl = useSubmit();\n  let submit = React.useCallback<FetcherSubmitFunction>(\n    (target, opts) => {\n      submitImpl(target, {\n        ...opts,\n        navigate: false,\n        fetcherKey,\n      });\n    },\n    [fetcherKey, submitImpl]\n  );\n\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = React.forwardRef<HTMLFormElement, FetcherFormProps>(\n      (props, ref) => {\n        return (\n          <Form {...props} navigate={false} fetcherKey={fetcherKey} ref={ref} />\n        );\n      }\n    );\n    if (__DEV__) {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form: FetcherForm,\n      submit,\n      load,\n      ...fetcher,\n      data,\n    }),\n    [FetcherForm, submit, load, fetcher, data]\n  );\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): (Fetcher & { key: string })[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(([key, fetcher]) => ({\n    ...fetcher,\n    key,\n  }));\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let { basename } = React.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      try {\n        sessionStorage.setItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n          JSON.stringify(savedScrollPositions)\n        );\n      } catch (error) {\n        warning(\n          false,\n          `Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${error}).`\n        );\n      }\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename: GetScrollRestorationKeyFunction | undefined =\n        getKey && basename !== \"/\"\n          ? (location, matches) =>\n              getKey(\n                // Strip the basename to match useLocation()\n                {\n                  ...location,\n                  pathname:\n                    stripBasename(location.pathname, basename) ||\n                    location.pathname,\n                },\n                matches\n              )\n          : getKey;\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKeyWithoutBasename\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({\n  when,\n  message,\n}: {\n  when: boolean | BlockerFunction;\n  message: string;\n}) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(\n  to: To,\n  opts: { relative?: RelativeRoutingType } = {}\n) {\n  let vtContext = React.useContext(ViewTransitionContext);\n\n  invariant(\n    vtContext != null,\n    \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" +\n      \"Did you accidentally import `RouterProvider` from `react-router`?\"\n  );\n\n  let { basename } = useDataRouterContext(\n    DataRouterHook.useViewTransitionState\n  );\n  let path = useResolvedPath(to, { relative: opts.relative });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n\n  let currentPath =\n    stripBasename(vtContext.currentLocation.pathname, basename) ||\n    vtContext.currentLocation.pathname;\n  let nextPath =\n    stripBasename(vtContext.nextLocation.pathname, basename) ||\n    vtContext.nextLocation.pathname;\n\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return (\n    matchPath(path.pathname, nextPath) != null ||\n    matchPath(path.pathname, currentPath) != null\n  );\n}\n\nexport { useViewTransitionState as useViewTransitionState };\n\n//#endregion\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nexport { Navigate, NavigationType, Outlet, Route, Routes, createPath, createRoutesFromChildren, createRoutesFromElements, createSearchParams, generatePath, isRouteErrorResponse, matchPath, matchRoutes, parsePath, renderMatches, resolvePath, unstable_usePrompt, useAsyncError, useAsyncValue, useBeforeUnload, useBlocker, useFetchers, useFormAction, useHref, useInRouterContext, useLinkClickHandler, useLocation, useMatch, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRoutes, useSearchParams, useSubmit, useViewTransitionState } from 'react-router-dom';\nexport { data, defer, json, redirect, redirectDocument, replace } from '@remix-run/server-runtime';\nexport { RemixBrowser } from './browser.js';\nexport { Await, Form, Link, Links, LiveReload, Meta, NavLink, PrefetchPageLinks, Scripts, RemixContext as UNSAFE_RemixContext, useActionData, useFetcher, useLoaderData, useMatches, useRouteLoaderData } from './components.js';\nexport { ScrollRestoration } from './scroll-restoration.js';\nexport { RemixServer } from './server.js';\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { createRouter, createBrowserHistory } from '@remix-run/router';\nimport * as React from 'react';\nimport { UNSAFE_mapRouteProperties } from 'react-router';\nimport { matchRoutes, RouterProvider } from 'react-router-dom';\nimport { RemixContext } from './components.js';\nimport { RemixErrorBoundary } from './errorBoundaries.js';\nimport { deserializeErrors } from './errors.js';\nimport { createClientRoutesWithHMRRevalidationOptOut, createClientRoutes, shouldHydrateRouteLoader } from './routes.js';\nimport { decodeViaTurboStream, getSingleFetchDataStrategy } from './single-fetch.js';\nimport invariant from './invariant.js';\nimport { getPatchRoutesOnNavigationFunction, useFogOFWarDiscovery } from './fog-of-war.js';\n\n/* eslint-disable prefer-let/prefer-let */\n\n/* eslint-enable prefer-let/prefer-let */\n\nlet stateDecodingPromise;\nlet router;\nlet routerInitialized = false;\nlet hmrAbortController;\nlet hmrRouterReadyResolve;\n// There's a race condition with HMR where the remix:manifest is signaled before\n// the router is assigned in the RemixBrowser component. This promise gates the\n// HMR handler until the router is ready\nlet hmrRouterReadyPromise = new Promise(resolve => {\n  // body of a promise is executed immediately, so this can be resolved outside\n  // of the promise body\n  hmrRouterReadyResolve = resolve;\n}).catch(() => {\n  // This is a noop catch handler to avoid unhandled promise rejection warnings\n  // in the console. The promise is never rejected.\n  return undefined;\n});\n\n// @ts-expect-error\nif (import.meta && import.meta.hot) {\n  // @ts-expect-error\n  import.meta.hot.accept(\"remix:manifest\", async ({\n    assetsManifest,\n    needsRevalidation\n  }) => {\n    let router = await hmrRouterReadyPromise;\n    // This should never happen, but just in case...\n    if (!router) {\n      console.error(\"Failed to accept HMR update because the router was not ready.\");\n      return;\n    }\n    let routeIds = [...new Set(router.state.matches.map(m => m.route.id).concat(Object.keys(window.__remixRouteModules)))];\n    if (hmrAbortController) {\n      hmrAbortController.abort();\n    }\n    hmrAbortController = new AbortController();\n    let signal = hmrAbortController.signal;\n\n    // Load new route modules that we've seen.\n    let newRouteModules = Object.assign({}, window.__remixRouteModules, Object.fromEntries((await Promise.all(routeIds.map(async id => {\n      var _assetsManifest$hmr, _window$__remixRouteM, _window$__remixRouteM2, _window$__remixRouteM3;\n      if (!assetsManifest.routes[id]) {\n        return null;\n      }\n      let imported = await import(assetsManifest.routes[id].module + `?t=${(_assetsManifest$hmr = assetsManifest.hmr) === null || _assetsManifest$hmr === void 0 ? void 0 : _assetsManifest$hmr.timestamp}`);\n      return [id, {\n        ...imported,\n        // react-refresh takes care of updating these in-place,\n        // if we don't preserve existing values we'll loose state.\n        default: imported.default ? ((_window$__remixRouteM = window.__remixRouteModules[id]) === null || _window$__remixRouteM === void 0 ? void 0 : _window$__remixRouteM.default) ?? imported.default : imported.default,\n        ErrorBoundary: imported.ErrorBoundary ? ((_window$__remixRouteM2 = window.__remixRouteModules[id]) === null || _window$__remixRouteM2 === void 0 ? void 0 : _window$__remixRouteM2.ErrorBoundary) ?? imported.ErrorBoundary : imported.ErrorBoundary,\n        HydrateFallback: imported.HydrateFallback ? ((_window$__remixRouteM3 = window.__remixRouteModules[id]) === null || _window$__remixRouteM3 === void 0 ? void 0 : _window$__remixRouteM3.HydrateFallback) ?? imported.HydrateFallback : imported.HydrateFallback\n      }];\n    }))).filter(Boolean)));\n    Object.assign(window.__remixRouteModules, newRouteModules);\n    // Create new routes\n    let routes = createClientRoutesWithHMRRevalidationOptOut(needsRevalidation, assetsManifest.routes, window.__remixRouteModules, window.__remixContext.state, window.__remixContext.future, window.__remixContext.isSpaMode);\n\n    // This is temporary API and will be more granular before release\n    router._internalSetRoutes(routes);\n\n    // Wait for router to be idle before updating the manifest and route modules\n    // and triggering a react-refresh\n    let unsub = router.subscribe(state => {\n      if (state.revalidation === \"idle\") {\n        unsub();\n        // Abort if a new update comes in while we're waiting for the\n        // router to be idle.\n        if (signal.aborted) return;\n        // Ensure RouterProvider setState has flushed before re-rendering\n        setTimeout(() => {\n          Object.assign(window.__remixManifest, assetsManifest);\n          window.$RefreshRuntime$.performReactRefresh();\n        }, 1);\n      }\n    });\n    window.__remixRevalidation = (window.__remixRevalidation || 0) + 1;\n    router.revalidate();\n  });\n}\n\n/**\n * The entry point for a Remix app when it is rendered in the browser (in\n * `app/entry.client.js`). This component is used by React to hydrate the HTML\n * that was received from the server.\n */\nfunction RemixBrowser(_props) {\n  if (!router) {\n    // When single fetch is enabled, we need to suspend until the initial state\n    // snapshot is decoded into window.__remixContext.state\n    if (window.__remixContext.future.v3_singleFetch) {\n      // Note: `stateDecodingPromise` is not coupled to `router` - we'll reach this\n      // code potentially many times waiting for our state to arrive, but we'll\n      // then only get past here and create the `router` one time\n      if (!stateDecodingPromise) {\n        let stream = window.__remixContext.stream;\n        invariant(stream, \"No stream found for single fetch decoding\");\n        window.__remixContext.stream = undefined;\n        stateDecodingPromise = decodeViaTurboStream(stream, window).then(value => {\n          window.__remixContext.state = value.value;\n          stateDecodingPromise.value = true;\n        }).catch(e => {\n          stateDecodingPromise.error = e;\n        });\n      }\n      if (stateDecodingPromise.error) {\n        throw stateDecodingPromise.error;\n      }\n      if (!stateDecodingPromise.value) {\n        throw stateDecodingPromise;\n      }\n    }\n    let routes = createClientRoutes(window.__remixManifest.routes, window.__remixRouteModules, window.__remixContext.state, window.__remixContext.future, window.__remixContext.isSpaMode);\n    let hydrationData = undefined;\n    if (!window.__remixContext.isSpaMode) {\n      // Create a shallow clone of `loaderData` we can mutate for partial hydration.\n      // When a route exports a `clientLoader` and a `HydrateFallback`, the SSR will\n      // render the fallback so we need the client to do the same for hydration.\n      // The server loader data has already been exposed to these route `clientLoader`'s\n      // in `createClientRoutes` above, so we need to clear out the version we pass to\n      // `createBrowserRouter` so it initializes and runs the client loaders.\n      hydrationData = {\n        ...window.__remixContext.state,\n        loaderData: {\n          ...window.__remixContext.state.loaderData\n        }\n      };\n      let initialMatches = matchRoutes(routes, window.location, window.__remixContext.basename);\n      if (initialMatches) {\n        for (let match of initialMatches) {\n          let routeId = match.route.id;\n          let route = window.__remixRouteModules[routeId];\n          let manifestRoute = window.__remixManifest.routes[routeId];\n          // Clear out the loaderData to avoid rendering the route component when the\n          // route opted into clientLoader hydration and either:\n          // * gave us a HydrateFallback\n          // * or doesn't have a server loader and we have no data to render\n          if (route && shouldHydrateRouteLoader(manifestRoute, route, window.__remixContext.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n            hydrationData.loaderData[routeId] = undefined;\n          } else if (manifestRoute && !manifestRoute.hasLoader) {\n            // Since every Remix route gets a `loader` on the client side to load\n            // the route JS module, we need to add a `null` value to `loaderData`\n            // for any routes that don't have server loaders so our partial\n            // hydration logic doesn't kick off the route module loaders during\n            // hydration\n            hydrationData.loaderData[routeId] = null;\n          }\n        }\n      }\n      if (hydrationData && hydrationData.errors) {\n        hydrationData.errors = deserializeErrors(hydrationData.errors);\n      }\n    }\n\n    // We don't use createBrowserRouter here because we need fine-grained control\n    // over initialization to support synchronous `clientLoader` flows.\n    router = createRouter({\n      routes,\n      history: createBrowserHistory(),\n      basename: window.__remixContext.basename,\n      future: {\n        v7_normalizeFormMethod: true,\n        v7_fetcherPersist: window.__remixContext.future.v3_fetcherPersist,\n        v7_partialHydration: true,\n        v7_prependBasename: true,\n        v7_relativeSplatPath: window.__remixContext.future.v3_relativeSplatPath,\n        // Single fetch enables this underlying behavior\n        v7_skipActionErrorRevalidation: window.__remixContext.future.v3_singleFetch === true\n      },\n      hydrationData,\n      mapRouteProperties: UNSAFE_mapRouteProperties,\n      dataStrategy: window.__remixContext.future.v3_singleFetch ? getSingleFetchDataStrategy(window.__remixManifest, window.__remixRouteModules, () => router) : undefined,\n      patchRoutesOnNavigation: getPatchRoutesOnNavigationFunction(window.__remixManifest, window.__remixRouteModules, window.__remixContext.future, window.__remixContext.isSpaMode, window.__remixContext.basename)\n    });\n\n    // We can call initialize() immediately if the router doesn't have any\n    // loaders to run on hydration\n    if (router.state.initialized) {\n      routerInitialized = true;\n      router.initialize();\n    }\n\n    // @ts-ignore\n    router.createRoutesForHMR = createClientRoutesWithHMRRevalidationOptOut;\n    window.__remixRouter = router;\n\n    // Notify that the router is ready for HMR\n    if (hmrRouterReadyResolve) {\n      hmrRouterReadyResolve(router);\n    }\n  }\n\n  // Critical CSS can become stale after code changes, e.g. styles might be\n  // removed from a component, but the styles will still be present in the\n  // server HTML. This allows our HMR logic to clear the critical CSS state.\n\n  let [criticalCss, setCriticalCss] = React.useState(process.env.NODE_ENV === \"development\" ? window.__remixContext.criticalCss : undefined);\n  if (process.env.NODE_ENV === \"development\") {\n    window.__remixClearCriticalCss = () => setCriticalCss(undefined);\n  }\n\n  // This is due to the short circuit return above when the pathname doesn't\n  // match and we force a hard reload.  This is an exceptional scenario in which\n  // we can't hydrate anyway.\n\n  let [location, setLocation] = React.useState(router.state.location);\n  React.useLayoutEffect(() => {\n    // If we had to run clientLoaders on hydration, we delay initialization until\n    // after we've hydrated to avoid hydration issues from synchronous client loaders\n    if (!routerInitialized) {\n      routerInitialized = true;\n      router.initialize();\n    }\n  }, []);\n  React.useLayoutEffect(() => {\n    return router.subscribe(newState => {\n      if (newState.location !== location) {\n        setLocation(newState.location);\n      }\n    });\n  }, [location]);\n  useFogOFWarDiscovery(router, window.__remixManifest, window.__remixRouteModules, window.__remixContext.future, window.__remixContext.isSpaMode);\n\n  // We need to include a wrapper RemixErrorBoundary here in case the root error\n  // boundary also throws and we need to bubble up outside of the router entirely.\n  // Then we need a stateful location here so the user can back-button navigate\n  // out of there\n  return (\n    /*#__PURE__*/\n    // This fragment is important to ensure we match the <RemixServer> JSX\n    // structure so that useId values hydrate correctly\n    React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(RemixContext.Provider, {\n      value: {\n        manifest: window.__remixManifest,\n        routeModules: window.__remixRouteModules,\n        future: window.__remixContext.future,\n        criticalCss,\n        isSpaMode: window.__remixContext.isSpaMode\n      }\n    }, /*#__PURE__*/React.createElement(RemixErrorBoundary, {\n      location: location\n    }, /*#__PURE__*/React.createElement(RouterProvider, {\n      router: router,\n      fallbackElement: null,\n      future: {\n        v7_startTransition: true\n      }\n    }))), window.__remixContext.future.v3_singleFetch ? /*#__PURE__*/React.createElement(React.Fragment, null) : null)\n  );\n}\n\nexport { RemixBrowser };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nexport { _extends as extends };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { extends as _extends } from './_virtual/_rollupPluginBabelHelpers.js';\nimport * as React from 'react';\nimport { useHref, NavLink as NavLink$1, Link as Link$1, Form as Form$1, matchRoutes, useLocation, Await as Await$1, useAsyncError, useMatches as useMatches$1, useLoaderData as useLoaderData$1, useRouteLoaderData as useRouteLoaderData$1, useActionData as useActionData$1, use<PERSON><PERSON><PERSON> as useFetcher$1, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext } from 'react-router-dom';\nimport invariant from './invariant.js';\nimport { getKeyedLinksForMatches, isPageLinkDescriptor, getNewMatchesForLinks, getDataLinkHrefs, getModuleLinkHrefs, getKeyedPrefetchLinks } from './links.js';\nimport { escapeHtml, createHtml } from './markup.js';\nimport { singleFetchUrl } from './single-fetch.js';\nimport { isFogOfWarEnabled, getPartialManifest } from './fog-of-war.js';\n\nfunction useDataRouterContext() {\n  let context = React.useContext(UNSAFE_DataRouterContext);\n  invariant(context, \"You must render this element inside a <DataRouterContext.Provider> element\");\n  return context;\n}\nfunction useDataRouterStateContext() {\n  let context = React.useContext(UNSAFE_DataRouterStateContext);\n  invariant(context, \"You must render this element inside a <DataRouterStateContext.Provider> element\");\n  return context;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// RemixContext\n\nconst RemixContext = /*#__PURE__*/React.createContext(undefined);\nRemixContext.displayName = \"Remix\";\nfunction useRemixContext() {\n  let context = React.useContext(RemixContext);\n  invariant(context, \"You must render this element inside a <Remix> element\");\n  return context;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// Public API\n\n/**\n * Defines the discovery behavior of the link:\n *\n * - \"render\": Eagerly discover when the link is rendered (default)\n * - \"none\": No eager discovery - discover when the link is clicked\n */\n\n/**\n * Defines the prefetching behavior of the link:\n *\n * - \"none\": Never fetched\n * - \"intent\": Fetched when the user focuses or hovers the link\n * - \"render\": Fetched when the link is rendered\n * - \"viewport\": Fetched when the link is in the viewport\n */\n\nfunction usePrefetchBehavior(prefetch, theirElementProps) {\n  let [maybePrefetch, setMaybePrefetch] = React.useState(false);\n  let [shouldPrefetch, setShouldPrefetch] = React.useState(false);\n  let {\n    onFocus,\n    onBlur,\n    onMouseEnter,\n    onMouseLeave,\n    onTouchStart\n  } = theirElementProps;\n  let ref = React.useRef(null);\n  React.useEffect(() => {\n    if (prefetch === \"render\") {\n      setShouldPrefetch(true);\n    }\n    if (prefetch === \"viewport\") {\n      let callback = entries => {\n        entries.forEach(entry => {\n          setShouldPrefetch(entry.isIntersecting);\n        });\n      };\n      let observer = new IntersectionObserver(callback, {\n        threshold: 0.5\n      });\n      if (ref.current) observer.observe(ref.current);\n      return () => {\n        observer.disconnect();\n      };\n    }\n  }, [prefetch]);\n  let setIntent = () => {\n    if (prefetch === \"intent\") {\n      setMaybePrefetch(true);\n    }\n  };\n  let cancelIntent = () => {\n    if (prefetch === \"intent\") {\n      setMaybePrefetch(false);\n      setShouldPrefetch(false);\n    }\n  };\n  React.useEffect(() => {\n    if (maybePrefetch) {\n      let id = setTimeout(() => {\n        setShouldPrefetch(true);\n      }, 100);\n      return () => {\n        clearTimeout(id);\n      };\n    }\n  }, [maybePrefetch]);\n  return [shouldPrefetch, ref, {\n    onFocus: composeEventHandlers(onFocus, setIntent),\n    onBlur: composeEventHandlers(onBlur, cancelIntent),\n    onMouseEnter: composeEventHandlers(onMouseEnter, setIntent),\n    onMouseLeave: composeEventHandlers(onMouseLeave, cancelIntent),\n    onTouchStart: composeEventHandlers(onTouchStart, setIntent)\n  }];\n}\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\nfunction getDiscoverAttr(discover, isAbsolute, reloadDocument) {\n  return discover === \"render\" && !isAbsolute && !reloadDocument ? \"true\" : undefined;\n}\n\n/**\n * A special kind of `<Link>` that knows whether it is \"active\".\n *\n * @see https://remix.run/components/nav-link\n */\nlet NavLink = /*#__PURE__*/React.forwardRef(({\n  to,\n  prefetch = \"none\",\n  discover = \"render\",\n  ...props\n}, forwardedRef) => {\n  let isAbsolute = typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to);\n  let href = useHref(to);\n  let [shouldPrefetch, ref, prefetchHandlers] = usePrefetchBehavior(prefetch, props);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(NavLink$1, _extends({}, props, prefetchHandlers, {\n    ref: mergeRefs(forwardedRef, ref),\n    to: to,\n    \"data-discover\": getDiscoverAttr(discover, isAbsolute, props.reloadDocument)\n  })), shouldPrefetch && !isAbsolute ? /*#__PURE__*/React.createElement(PrefetchPageLinks, {\n    page: href\n  }) : null);\n});\nNavLink.displayName = \"NavLink\";\n\n/**\n * This component renders an anchor tag and is the primary way the user will\n * navigate around your website.\n *\n * @see https://remix.run/components/link\n */\nlet Link = /*#__PURE__*/React.forwardRef(({\n  to,\n  prefetch = \"none\",\n  discover = \"render\",\n  ...props\n}, forwardedRef) => {\n  let isAbsolute = typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to);\n  let href = useHref(to);\n  let [shouldPrefetch, ref, prefetchHandlers] = usePrefetchBehavior(prefetch, props);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Link$1, _extends({}, props, prefetchHandlers, {\n    ref: mergeRefs(forwardedRef, ref),\n    to: to,\n    \"data-discover\": getDiscoverAttr(discover, isAbsolute, props.reloadDocument)\n  })), shouldPrefetch && !isAbsolute ? /*#__PURE__*/React.createElement(PrefetchPageLinks, {\n    page: href\n  }) : null);\n});\nLink.displayName = \"Link\";\n/**\n * This component renders a form tag and is the primary way the user will\n * submit information via your website.\n *\n * @see https://remix.run/components/form\n */\nlet Form = /*#__PURE__*/React.forwardRef(({\n  discover = \"render\",\n  ...props\n}, forwardedRef) => {\n  let isAbsolute = typeof props.action === \"string\" && ABSOLUTE_URL_REGEX.test(props.action);\n  return /*#__PURE__*/React.createElement(Form$1, _extends({}, props, {\n    ref: forwardedRef,\n    \"data-discover\": getDiscoverAttr(discover, isAbsolute, props.reloadDocument)\n  }));\n});\nForm.displayName = \"Form\";\nfunction composeEventHandlers(theirHandler, ourHandler) {\n  return event => {\n    theirHandler && theirHandler(event);\n    if (!event.defaultPrevented) {\n      ourHandler(event);\n    }\n  };\n}\n\n// Return the matches actively being displayed:\n// - In SPA Mode we only SSR/hydrate the root match, and include all matches\n//   after hydration. This lets the router handle initial match loads via lazy().\n// - When an error boundary is rendered, we slice off matches up to the\n//   boundary for <Links>/<Meta>\nfunction getActiveMatches(matches, errors, isSpaMode) {\n  if (isSpaMode && !isHydrated) {\n    return [matches[0]];\n  }\n  if (errors) {\n    let errorIdx = matches.findIndex(m => errors[m.route.id] !== undefined);\n    return matches.slice(0, errorIdx + 1);\n  }\n  return matches;\n}\n\n/**\n * Renders the `<link>` tags for the current routes.\n *\n * @see https://remix.run/components/links\n */\nfunction Links() {\n  let {\n    isSpaMode,\n    manifest,\n    routeModules,\n    criticalCss\n  } = useRemixContext();\n  let {\n    errors,\n    matches: routerMatches\n  } = useDataRouterStateContext();\n  let matches = getActiveMatches(routerMatches, errors, isSpaMode);\n  let keyedLinks = React.useMemo(() => getKeyedLinksForMatches(matches, routeModules, manifest), [matches, routeModules, manifest]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, criticalCss ? /*#__PURE__*/React.createElement(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: criticalCss\n    }\n  }) : null, keyedLinks.map(({\n    key,\n    link\n  }) => isPageLinkDescriptor(link) ? /*#__PURE__*/React.createElement(PrefetchPageLinks, _extends({\n    key: key\n  }, link)) : /*#__PURE__*/React.createElement(\"link\", _extends({\n    key: key\n  }, link))));\n}\n\n/**\n * This component renders all the `<link rel=\"prefetch\">` and\n * `<link rel=\"modulepreload\"/>` tags for all the assets (data, modules, css) of\n * a given page.\n *\n * @param props\n * @param props.page\n * @see https://remix.run/components/prefetch-page-links\n */\nfunction PrefetchPageLinks({\n  page,\n  ...dataLinkProps\n}) {\n  let {\n    router\n  } = useDataRouterContext();\n  let matches = React.useMemo(() => matchRoutes(router.routes, page, router.basename), [router.routes, page, router.basename]);\n  if (!matches) {\n    console.warn(`Tried to prefetch ${page} but no routes matched.`);\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(PrefetchPageLinksImpl, _extends({\n    page: page,\n    matches: matches\n  }, dataLinkProps));\n}\nfunction useKeyedPrefetchLinks(matches) {\n  let {\n    manifest,\n    routeModules\n  } = useRemixContext();\n  let [keyedPrefetchLinks, setKeyedPrefetchLinks] = React.useState([]);\n  React.useEffect(() => {\n    let interrupted = false;\n    void getKeyedPrefetchLinks(matches, manifest, routeModules).then(links => {\n      if (!interrupted) {\n        setKeyedPrefetchLinks(links);\n      }\n    });\n    return () => {\n      interrupted = true;\n    };\n  }, [matches, manifest, routeModules]);\n  return keyedPrefetchLinks;\n}\nfunction PrefetchPageLinksImpl({\n  page,\n  matches: nextMatches,\n  ...linkProps\n}) {\n  let location = useLocation();\n  let {\n    future,\n    manifest,\n    routeModules\n  } = useRemixContext();\n  let {\n    loaderData,\n    matches\n  } = useDataRouterStateContext();\n  let newMatchesForData = React.useMemo(() => getNewMatchesForLinks(page, nextMatches, matches, manifest, location, future, \"data\"), [page, nextMatches, matches, manifest, location, future]);\n  let dataHrefs = React.useMemo(() => {\n    if (!future.v3_singleFetch) {\n      return getDataLinkHrefs(page, newMatchesForData, manifest);\n    }\n    if (page === location.pathname + location.search + location.hash) {\n      // Because we opt-into revalidation, don't compute this for the current page\n      // since it would always trigger a prefetch of the existing loaders\n      return [];\n    }\n\n    // Single-fetch is harder :)\n    // This parallels the logic in the single fetch data strategy\n    let routesParams = new Set();\n    let foundOptOutRoute = false;\n    nextMatches.forEach(m => {\n      var _routeModules$m$route;\n      if (!manifest.routes[m.route.id].hasLoader) {\n        return;\n      }\n      if (!newMatchesForData.some(m2 => m2.route.id === m.route.id) && m.route.id in loaderData && (_routeModules$m$route = routeModules[m.route.id]) !== null && _routeModules$m$route !== void 0 && _routeModules$m$route.shouldRevalidate) {\n        foundOptOutRoute = true;\n      } else if (manifest.routes[m.route.id].hasClientLoader) {\n        foundOptOutRoute = true;\n      } else {\n        routesParams.add(m.route.id);\n      }\n    });\n    if (routesParams.size === 0) {\n      return [];\n    }\n    let url = singleFetchUrl(page);\n    // When one or more routes have opted out, we add a _routes param to\n    // limit the loaders to those that have a server loader and did not\n    // opt out\n    if (foundOptOutRoute && routesParams.size > 0) {\n      url.searchParams.set(\"_routes\", nextMatches.filter(m => routesParams.has(m.route.id)).map(m => m.route.id).join(\",\"));\n    }\n    return [url.pathname + url.search];\n  }, [future.v3_singleFetch, loaderData, location, manifest, newMatchesForData, nextMatches, page, routeModules]);\n  let newMatchesForAssets = React.useMemo(() => getNewMatchesForLinks(page, nextMatches, matches, manifest, location, future, \"assets\"), [page, nextMatches, matches, manifest, location, future]);\n  let moduleHrefs = React.useMemo(() => getModuleLinkHrefs(newMatchesForAssets, manifest), [newMatchesForAssets, manifest]);\n\n  // needs to be a hook with async behavior because we need the modules, not\n  // just the manifest like the other links in here.\n  let keyedPrefetchLinks = useKeyedPrefetchLinks(newMatchesForAssets);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, dataHrefs.map(href => /*#__PURE__*/React.createElement(\"link\", _extends({\n    key: href,\n    rel: \"prefetch\",\n    as: \"fetch\",\n    href: href\n  }, linkProps))), moduleHrefs.map(href => /*#__PURE__*/React.createElement(\"link\", _extends({\n    key: href,\n    rel: \"modulepreload\",\n    href: href\n  }, linkProps))), keyedPrefetchLinks.map(({\n    key,\n    link\n  }) =>\n  /*#__PURE__*/\n  // these don't spread `linkProps` because they are full link descriptors\n  // already with their own props\n  React.createElement(\"link\", _extends({\n    key: key\n  }, link))));\n}\n\n/**\n * Renders HTML tags related to metadata for the current route.\n *\n * @see https://remix.run/components/meta\n */\nfunction Meta() {\n  let {\n    isSpaMode,\n    routeModules\n  } = useRemixContext();\n  let {\n    errors,\n    matches: routerMatches,\n    loaderData\n  } = useDataRouterStateContext();\n  let location = useLocation();\n  let _matches = getActiveMatches(routerMatches, errors, isSpaMode);\n  let error = null;\n  if (errors) {\n    error = errors[_matches[_matches.length - 1].route.id];\n  }\n  let meta = [];\n  let leafMeta = null;\n  let matches = [];\n  for (let i = 0; i < _matches.length; i++) {\n    let _match = _matches[i];\n    let routeId = _match.route.id;\n    let data = loaderData[routeId];\n    let params = _match.params;\n    let routeModule = routeModules[routeId];\n    let routeMeta = [];\n    let match = {\n      id: routeId,\n      data,\n      meta: [],\n      params: _match.params,\n      pathname: _match.pathname,\n      handle: _match.route.handle,\n      error\n    };\n    matches[i] = match;\n    if (routeModule !== null && routeModule !== void 0 && routeModule.meta) {\n      routeMeta = typeof routeModule.meta === \"function\" ? routeModule.meta({\n        data,\n        params,\n        location,\n        matches,\n        error\n      }) : Array.isArray(routeModule.meta) ? [...routeModule.meta] : routeModule.meta;\n    } else if (leafMeta) {\n      // We only assign the route's meta to the nearest leaf if there is no meta\n      // export in the route. The meta function may return a falsy value which\n      // is effectively the same as an empty array.\n      routeMeta = [...leafMeta];\n    }\n    routeMeta = routeMeta || [];\n    if (!Array.isArray(routeMeta)) {\n      throw new Error(\"The route at \" + _match.route.path + \" returns an invalid value. All route meta functions must \" + \"return an array of meta objects.\" + \"\\n\\nTo reference the meta function API, see https://remix.run/route/meta\");\n    }\n    match.meta = routeMeta;\n    matches[i] = match;\n    meta = [...routeMeta];\n    leafMeta = meta;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, meta.flat().map(metaProps => {\n    if (!metaProps) {\n      return null;\n    }\n    if (\"tagName\" in metaProps) {\n      let {\n        tagName,\n        ...rest\n      } = metaProps;\n      if (!isValidMetaTag(tagName)) {\n        console.warn(`A meta object uses an invalid tagName: ${tagName}. Expected either 'link' or 'meta'`);\n        return null;\n      }\n      let Comp = tagName;\n      return /*#__PURE__*/React.createElement(Comp, _extends({\n        key: JSON.stringify(rest)\n      }, rest));\n    }\n    if (\"title\" in metaProps) {\n      return /*#__PURE__*/React.createElement(\"title\", {\n        key: \"title\"\n      }, String(metaProps.title));\n    }\n    if (\"charset\" in metaProps) {\n      metaProps.charSet ??= metaProps.charset;\n      delete metaProps.charset;\n    }\n    if (\"charSet\" in metaProps && metaProps.charSet != null) {\n      return typeof metaProps.charSet === \"string\" ? /*#__PURE__*/React.createElement(\"meta\", {\n        key: \"charSet\",\n        charSet: metaProps.charSet\n      }) : null;\n    }\n    if (\"script:ld+json\" in metaProps) {\n      try {\n        let json = JSON.stringify(metaProps[\"script:ld+json\"]);\n        return /*#__PURE__*/React.createElement(\"script\", {\n          key: `script:ld+json:${json}`,\n          type: \"application/ld+json\",\n          dangerouslySetInnerHTML: {\n            __html: json\n          }\n        });\n      } catch (err) {\n        return null;\n      }\n    }\n    return /*#__PURE__*/React.createElement(\"meta\", _extends({\n      key: JSON.stringify(metaProps)\n    }, metaProps));\n  }));\n}\nfunction isValidMetaTag(tagName) {\n  return typeof tagName === \"string\" && /^(meta|link)$/.test(tagName);\n}\nfunction Await(props) {\n  return /*#__PURE__*/React.createElement(Await$1, props);\n}\n\n/**\n * Tracks whether Remix has finished hydrating or not, so scripts can be skipped\n * during client-side updates.\n */\nlet isHydrated = false;\n/**\n * Renders the `<script>` tags needed for the initial render. Bundles for\n * additional routes are loaded later as needed.\n *\n * @param props Additional properties to add to each script tag that is rendered.\n * In addition to scripts, \\<link rel=\"modulepreload\"> tags receive the crossOrigin\n * property if provided.\n *\n * @see https://remix.run/components/scripts\n */\nfunction Scripts(props) {\n  let {\n    manifest,\n    serverHandoffString,\n    abortDelay,\n    serializeError,\n    isSpaMode,\n    future,\n    renderMeta\n  } = useRemixContext();\n  let {\n    router,\n    static: isStatic,\n    staticContext\n  } = useDataRouterContext();\n  let {\n    matches: routerMatches\n  } = useDataRouterStateContext();\n  let enableFogOfWar = isFogOfWarEnabled(future, isSpaMode);\n\n  // Let <RemixServer> know that we hydrated and we should render the single\n  // fetch streaming scripts\n  if (renderMeta) {\n    renderMeta.didRenderScripts = true;\n  }\n  let matches = getActiveMatches(routerMatches, null, isSpaMode);\n  React.useEffect(() => {\n    isHydrated = true;\n  }, []);\n  let serializePreResolvedErrorImp = (key, error) => {\n    let toSerialize;\n    if (serializeError && error instanceof Error) {\n      toSerialize = serializeError(error);\n    } else {\n      toSerialize = error;\n    }\n    return `${JSON.stringify(key)}:__remixContext.p(!1, ${escapeHtml(JSON.stringify(toSerialize))})`;\n  };\n  let serializePreresolvedDataImp = (routeId, key, data) => {\n    let serializedData;\n    try {\n      serializedData = JSON.stringify(data);\n    } catch (error) {\n      return serializePreResolvedErrorImp(key, error);\n    }\n    return `${JSON.stringify(key)}:__remixContext.p(${escapeHtml(serializedData)})`;\n  };\n  let serializeErrorImp = (routeId, key, error) => {\n    let toSerialize;\n    if (serializeError && error instanceof Error) {\n      toSerialize = serializeError(error);\n    } else {\n      toSerialize = error;\n    }\n    return `__remixContext.r(${JSON.stringify(routeId)}, ${JSON.stringify(key)}, !1, ${escapeHtml(JSON.stringify(toSerialize))})`;\n  };\n  let serializeDataImp = (routeId, key, data) => {\n    let serializedData;\n    try {\n      serializedData = JSON.stringify(data);\n    } catch (error) {\n      return serializeErrorImp(routeId, key, error);\n    }\n    return `__remixContext.r(${JSON.stringify(routeId)}, ${JSON.stringify(key)}, ${escapeHtml(serializedData)})`;\n  };\n  let deferredScripts = [];\n  let initialScripts = React.useMemo(() => {\n    var _manifest$hmr;\n    let streamScript = future.v3_singleFetch ?\n    // prettier-ignore\n    \"window.__remixContext.stream = new ReadableStream({\" + \"start(controller){\" + \"window.__remixContext.streamController = controller;\" + \"}\" + \"}).pipeThrough(new TextEncoderStream());\" : \"\";\n    let contextScript = staticContext ? `window.__remixContext = ${serverHandoffString};${streamScript}` : \" \";\n\n    // When single fetch is enabled, deferred is handled by turbo-stream\n    let activeDeferreds = future.v3_singleFetch ? undefined : staticContext === null || staticContext === void 0 ? void 0 : staticContext.activeDeferreds;\n\n    // This sets up the __remixContext with utility functions used by the\n    // deferred scripts.\n    // - __remixContext.p is a function that takes a resolved value or error and returns a promise.\n    //   This is used for transmitting pre-resolved promises from the server to the client.\n    // - __remixContext.n is a function that takes a routeID and key to returns a promise for later\n    //   resolution by the subsequently streamed chunks.\n    // - __remixContext.r is a function that takes a routeID, key and value or error and resolves\n    //   the promise created by __remixContext.n.\n    // - __remixContext.t is a map or routeId to keys to an object containing `e` and `r` methods\n    //   to resolve or reject the promise created by __remixContext.n.\n    // - __remixContext.a is the active number of deferred scripts that should be rendered to match\n    //   the SSR tree for hydration on the client.\n    contextScript += !activeDeferreds ? \"\" : [\"__remixContext.p = function(v,e,p,x) {\", \"  if (typeof e !== 'undefined') {\", process.env.NODE_ENV === \"development\" ? \"    x=new Error(e.message);\\n    x.stack=e.stack;\" : '    x=new Error(\"Unexpected Server Error\");\\n    x.stack=undefined;', \"    p=Promise.reject(x);\", \"  } else {\", \"    p=Promise.resolve(v);\", \"  }\", \"  return p;\", \"};\", \"__remixContext.n = function(i,k) {\", \"  __remixContext.t = __remixContext.t || {};\", \"  __remixContext.t[i] = __remixContext.t[i] || {};\", \"  let p = new Promise((r, e) => {__remixContext.t[i][k] = {r:(v)=>{r(v);},e:(v)=>{e(v);}};});\", typeof abortDelay === \"number\" ? `setTimeout(() => {if(typeof p._error !== \"undefined\" || typeof p._data !== \"undefined\"){return;} __remixContext.t[i][k].e(new Error(\"Server timeout.\"))}, ${abortDelay});` : \"\", \"  return p;\", \"};\", \"__remixContext.r = function(i,k,v,e,p,x) {\", \"  p = __remixContext.t[i][k];\", \"  if (typeof e !== 'undefined') {\", process.env.NODE_ENV === \"development\" ? \"    x=new Error(e.message);\\n    x.stack=e.stack;\" : '    x=new Error(\"Unexpected Server Error\");\\n    x.stack=undefined;', \"    p.e(x);\", \"  } else {\", \"    p.r(v);\", \"  }\", \"};\"].join(\"\\n\") + Object.entries(activeDeferreds).map(([routeId, deferredData]) => {\n      let pendingKeys = new Set(deferredData.pendingKeys);\n      let promiseKeyValues = deferredData.deferredKeys.map(key => {\n        if (pendingKeys.has(key)) {\n          deferredScripts.push( /*#__PURE__*/React.createElement(DeferredHydrationScript, {\n            key: `${routeId} | ${key}`,\n            deferredData: deferredData,\n            routeId: routeId,\n            dataKey: key,\n            scriptProps: props,\n            serializeData: serializeDataImp,\n            serializeError: serializeErrorImp\n          }));\n          return `${JSON.stringify(key)}:__remixContext.n(${JSON.stringify(routeId)}, ${JSON.stringify(key)})`;\n        } else {\n          let trackedPromise = deferredData.data[key];\n          if (typeof trackedPromise._error !== \"undefined\") {\n            return serializePreResolvedErrorImp(key, trackedPromise._error);\n          } else {\n            return serializePreresolvedDataImp(routeId, key, trackedPromise._data);\n          }\n        }\n      }).join(\",\\n\");\n      return `Object.assign(__remixContext.state.loaderData[${JSON.stringify(routeId)}], {${promiseKeyValues}});`;\n    }).join(\"\\n\") + (deferredScripts.length > 0 ? `__remixContext.a=${deferredScripts.length};` : \"\");\n    let routeModulesScript = !isStatic ? \" \" : `${(_manifest$hmr = manifest.hmr) !== null && _manifest$hmr !== void 0 && _manifest$hmr.runtime ? `import ${JSON.stringify(manifest.hmr.runtime)};` : \"\"}${enableFogOfWar ? \"\" : `import ${JSON.stringify(manifest.url)}`};\n${matches.map((match, index) => `import * as route${index} from ${JSON.stringify(manifest.routes[match.route.id].module)};`).join(\"\\n\")}\n${enableFogOfWar ?\n    // Inline a minimal manifest with the SSR matches\n    `window.__remixManifest = ${JSON.stringify(getPartialManifest(manifest, router), null, 2)};` : \"\"}\nwindow.__remixRouteModules = {${matches.map((match, index) => `${JSON.stringify(match.route.id)}:route${index}`).join(\",\")}};\n\nimport(${JSON.stringify(manifest.entry.module)});`;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"script\", _extends({}, props, {\n      suppressHydrationWarning: true,\n      dangerouslySetInnerHTML: createHtml(contextScript),\n      type: undefined\n    })), /*#__PURE__*/React.createElement(\"script\", _extends({}, props, {\n      suppressHydrationWarning: true,\n      dangerouslySetInnerHTML: createHtml(routeModulesScript),\n      type: \"module\",\n      async: true\n    })));\n    // disabled deps array because we are purposefully only rendering this once\n    // for hydration, after that we want to just continue rendering the initial\n    // scripts as they were when the page first loaded\n    // eslint-disable-next-line\n  }, []);\n  if (!isStatic && typeof __remixContext === \"object\" && __remixContext.a) {\n    for (let i = 0; i < __remixContext.a; i++) {\n      deferredScripts.push( /*#__PURE__*/React.createElement(DeferredHydrationScript, {\n        key: i,\n        scriptProps: props,\n        serializeData: serializeDataImp,\n        serializeError: serializeErrorImp\n      }));\n    }\n  }\n  let routePreloads = matches.map(match => {\n    let route = manifest.routes[match.route.id];\n    return (route.imports || []).concat([route.module]);\n  }).flat(1);\n  let preloads = isHydrated ? [] : manifest.entry.imports.concat(routePreloads);\n  return isHydrated ? null : /*#__PURE__*/React.createElement(React.Fragment, null, !enableFogOfWar ? /*#__PURE__*/React.createElement(\"link\", {\n    rel: \"modulepreload\",\n    href: manifest.url,\n    crossOrigin: props.crossOrigin\n  }) : null, /*#__PURE__*/React.createElement(\"link\", {\n    rel: \"modulepreload\",\n    href: manifest.entry.module,\n    crossOrigin: props.crossOrigin\n  }), dedupe(preloads).map(path => /*#__PURE__*/React.createElement(\"link\", {\n    key: path,\n    rel: \"modulepreload\",\n    href: path,\n    crossOrigin: props.crossOrigin\n  })), initialScripts, deferredScripts);\n}\nfunction DeferredHydrationScript({\n  dataKey,\n  deferredData,\n  routeId,\n  scriptProps,\n  serializeData,\n  serializeError\n}) {\n  if (typeof document === \"undefined\" && deferredData && dataKey && routeId) {\n    invariant(deferredData.pendingKeys.includes(dataKey), `Deferred data for route ${routeId} with key ${dataKey} was not pending but tried to render a script for it.`);\n  }\n  return /*#__PURE__*/React.createElement(React.Suspense, {\n    fallback:\n    // This makes absolutely no sense. The server renders null as a fallback,\n    // but when hydrating, we need to render a script tag to avoid a hydration issue.\n    // To reproduce a hydration mismatch, just render null as a fallback.\n    typeof document === \"undefined\" && deferredData && dataKey && routeId ? null : /*#__PURE__*/React.createElement(\"script\", _extends({}, scriptProps, {\n      async: true,\n      suppressHydrationWarning: true,\n      dangerouslySetInnerHTML: {\n        __html: \" \"\n      }\n    }))\n  }, typeof document === \"undefined\" && deferredData && dataKey && routeId ? /*#__PURE__*/React.createElement(Await, {\n    resolve: deferredData.data[dataKey],\n    errorElement: /*#__PURE__*/React.createElement(ErrorDeferredHydrationScript, {\n      dataKey: dataKey,\n      routeId: routeId,\n      scriptProps: scriptProps,\n      serializeError: serializeError\n    }),\n    children: data => {\n      return /*#__PURE__*/React.createElement(\"script\", _extends({}, scriptProps, {\n        async: true,\n        suppressHydrationWarning: true,\n        dangerouslySetInnerHTML: {\n          __html: serializeData(routeId, dataKey, data)\n        }\n      }));\n    }\n  }) : /*#__PURE__*/React.createElement(\"script\", _extends({}, scriptProps, {\n    async: true,\n    suppressHydrationWarning: true,\n    dangerouslySetInnerHTML: {\n      __html: \" \"\n    }\n  })));\n}\nfunction ErrorDeferredHydrationScript({\n  dataKey,\n  routeId,\n  scriptProps,\n  serializeError\n}) {\n  let error = useAsyncError();\n  return /*#__PURE__*/React.createElement(\"script\", _extends({}, scriptProps, {\n    suppressHydrationWarning: true,\n    dangerouslySetInnerHTML: {\n      __html: serializeError(routeId, dataKey, error)\n    }\n  }));\n}\nfunction dedupe(array) {\n  return [...new Set(array)];\n}\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n *\n * @see https://remix.run/hooks/use-matches\n */\nfunction useMatches() {\n  return useMatches$1();\n}\n\n/**\n * Returns the JSON parsed data from the current route's `loader`.\n *\n * @see https://remix.run/hooks/use-loader-data\n */\nfunction useLoaderData() {\n  return useLoaderData$1();\n}\n\n/**\n * Returns the loaderData for the given routeId.\n *\n * @see https://remix.run/hooks/use-route-loader-data\n */\nfunction useRouteLoaderData(routeId) {\n  return useRouteLoaderData$1(routeId);\n}\n\n/**\n * Returns the JSON parsed data from the current route's `action`.\n *\n * @see https://remix.run/hooks/use-action-data\n */\nfunction useActionData() {\n  return useActionData$1();\n}\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n *\n * @see https://remix.run/hooks/use-fetcher\n */\nfunction useFetcher(opts = {}) {\n  return useFetcher$1(opts);\n}\n\n/**\n * This component connects your app to the Remix asset server and\n * automatically reloads the page when files change in development.\n * In production, it renders null, so you can safely render it always in your root route.\n *\n * @see https://remix.run/docs/components/live-reload\n */\nconst LiveReload =\n// Dead Code Elimination magic for production builds.\n// This way devs don't have to worry about doing the NODE_ENV check themselves.\nprocess.env.NODE_ENV !== \"development\" ? () => null : function LiveReload({\n  origin,\n  port,\n  timeoutMs = 1000,\n  nonce = undefined\n}) {\n  // @ts-expect-error\n  let isViteClient = import.meta && import.meta.env !== undefined;\n  if (isViteClient) {\n    console.warn([\"`<LiveReload />` is obsolete when using Vite and can conflict with Vite's built-in HMR runtime.\", \"\", \"Remove `<LiveReload />` from your code and instead only use `<Scripts />`.\", \"Then refresh the page to remove lingering scripts from `<LiveReload />`.\"].join(\"\\n\"));\n    return null;\n  }\n  origin ??= process.env.REMIX_DEV_ORIGIN;\n  let js = String.raw;\n  return /*#__PURE__*/React.createElement(\"script\", {\n    nonce: nonce,\n    suppressHydrationWarning: true,\n    dangerouslySetInnerHTML: {\n      __html: js`\n                function remixLiveReloadConnect(config) {\n                  let LIVE_RELOAD_ORIGIN = ${JSON.stringify(origin)};\n                  let protocol =\n                    LIVE_RELOAD_ORIGIN ? new URL(LIVE_RELOAD_ORIGIN).protocol.replace(/^http/, \"ws\") :\n                    location.protocol === \"https:\" ? \"wss:\" : \"ws:\"; // remove in v2?\n                  let hostname = LIVE_RELOAD_ORIGIN ? new URL(LIVE_RELOAD_ORIGIN).hostname : location.hostname;\n                  let url = new URL(protocol + \"//\" + hostname + \"/socket\");\n\n                  url.port =\n                    ${port} ||\n                    (LIVE_RELOAD_ORIGIN ? new URL(LIVE_RELOAD_ORIGIN).port : 8002);\n\n                  let ws = new WebSocket(url.href);\n                  ws.onmessage = async (message) => {\n                    let event = JSON.parse(message.data);\n                    if (event.type === \"LOG\") {\n                      console.log(event.message);\n                    }\n                    if (event.type === \"RELOAD\") {\n                      console.log(\"💿 Reloading window ...\");\n                      window.location.reload();\n                    }\n                    if (event.type === \"HMR\") {\n                      if (!window.__hmr__ || !window.__hmr__.contexts) {\n                        console.log(\"💿 [HMR] No HMR context, reloading window ...\");\n                        window.location.reload();\n                        return;\n                      }\n                      if (!event.updates || !event.updates.length) return;\n                      let updateAccepted = false;\n                      let needsRevalidation = new Set();\n                      for (let update of event.updates) {\n                        console.log(\"[HMR] \" + update.reason + \" [\" + update.id +\"]\")\n                        if (update.revalidate) {\n                          needsRevalidation.add(update.routeId);\n                          console.log(\"[HMR] Revalidating [\" + update.routeId + \"]\");\n                        }\n                        let imported = await import(update.url +  '?t=' + event.assetsManifest.hmr.timestamp);\n                        if (window.__hmr__.contexts[update.id]) {\n                          let accepted = window.__hmr__.contexts[update.id].emit(\n                            imported\n                          );\n                          if (accepted) {\n                            console.log(\"[HMR] Update accepted by\", update.id);\n                            updateAccepted = true;\n                          }\n                        }\n                      }\n                      if (event.assetsManifest && window.__hmr__.contexts[\"remix:manifest\"]) {\n                        let accepted = window.__hmr__.contexts[\"remix:manifest\"].emit(\n                          { needsRevalidation, assetsManifest: event.assetsManifest }\n                        );\n                        if (accepted) {\n                          console.log(\"[HMR] Update accepted by\", \"remix:manifest\");\n                          updateAccepted = true;\n                        }\n                      }\n                      if (!updateAccepted) {\n                        console.log(\"[HMR] Update rejected, reloading...\");\n                        window.location.reload();\n                      }\n                    }\n                  };\n                  ws.onopen = () => {\n                    if (config && typeof config.onOpen === \"function\") {\n                      config.onOpen();\n                    }\n                  };\n                  ws.onclose = (event) => {\n                    if (event.code === 1006) {\n                      console.log(\"Remix dev asset server web socket closed. Reconnecting...\");\n                      setTimeout(\n                        () =>\n                          remixLiveReloadConnect({\n                            onOpen: () => window.location.reload(),\n                          }),\n                      ${String(timeoutMs)}\n                      );\n                    }\n                  };\n                  ws.onerror = (error) => {\n                    console.log(\"Remix dev asset server web socket error:\");\n                    console.error(error);\n                  };\n                }\n                remixLiveReloadConnect();\n              `\n    }\n  });\n};\nfunction mergeRefs(...refs) {\n  return value => {\n    refs.forEach(ref => {\n      if (typeof ref === \"function\") {\n        ref(value);\n      } else if (ref != null) {\n        ref.current = value;\n      }\n    });\n  };\n}\n\nexport { Await, Form, Link, Links, LiveReload, Meta, NavLink, PrefetchPageLinks, RemixContext, Scripts, composeEventHandlers, useActionData, useFetcher, useLoaderData, useMatches, useRemixContext, useRouteLoaderData };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction invariant(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nexport { invariant as default };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n/**\n * A function that handles data mutations for a route on the client\n */\n\n/**\n * Arguments passed to a route `clientAction` function\n */\n\n/**\n * A function that loads data for a route on the client\n */\n\n/**\n * Arguments passed to a route `clientLoader` function\n */\n\n/**\n * ErrorBoundary to display for this route\n */\n\n/**\n * `<Route HydrateFallback>` component to render on initial loads\n * when client loaders are present\n */\n\n/**\n * Optional, root-only `<Route Layout>` component to wrap the root content in.\n * Useful for defining the <html>/<head>/<body> document shell shared by the\n * Component, HydrateFallback, and ErrorBoundary\n */\n\n/**\n * A function that defines `<link>` tags to be inserted into the `<head>` of\n * the document on route transitions.\n *\n * @see https://remix.run/route/meta\n */\n\n/**\n * A React component that is rendered for a route.\n */\n\n/**\n * An arbitrary object that is associated with a route.\n *\n * @see https://remix.run/route/handle\n */\n\nasync function loadRouteModule(route, routeModulesCache) {\n  if (route.id in routeModulesCache) {\n    return routeModulesCache[route.id];\n  }\n  try {\n    let routeModule = await import( /* webpackIgnore: true */route.module);\n    routeModulesCache[route.id] = routeModule;\n    return routeModule;\n  } catch (error) {\n    // If we can't load the route it's likely one of 2 things:\n    // - User got caught in the middle of a deploy and the CDN no longer has the\n    //   asset we're trying to import! Reload from the server and the user\n    //   (should) get the new manifest--unless the developer purged the static\n    //   assets, the manifest path, but not the documents 😬\n    // - Or, the asset trying to be imported has an error (usually in vite dev\n    //   mode), so the best we can do here is log the error for visibility\n    //   (via `Preserve log`) and reload\n\n    // Log the error so it can be accessed via the `Preserve Log` setting\n    console.error(`Error loading route module \\`${route.module}\\`, reloading page...`);\n    console.error(error);\n    if (window.__remixContext.isSpaMode &&\n    // @ts-expect-error\n    typeof import.meta.hot !== \"undefined\") {\n      // In SPA Mode (which implies vite) we don't want to perform a hard reload\n      // on dev-time errors since it's a vite compilation error and a reload is\n      // just going to fail with the same issue.  Let the UI bubble to the error\n      // boundary and let them see the error in the overlay or the dev server log\n      throw error;\n    }\n    window.location.reload();\n    return new Promise(() => {\n      // check out of this hook cause the DJs never gonna re[s]olve this\n    });\n  }\n}\n\nexport { loadRouteModule };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { parsePath } from 'react-router-dom';\nimport { loadRouteModule } from './routeModules.js';\n\n/**\n * Represents a `<link>` element.\n *\n * WHATWG Specification: https://html.spec.whatwg.org/multipage/semantics.html#the-link-element\n */\n\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Gets all the links for a set of matches. The modules are assumed to have been\n * loaded already.\n */\nfunction getKeyedLinksForMatches(matches, routeModules, manifest) {\n  let descriptors = matches.map(match => {\n    var _module$links;\n    let module = routeModules[match.route.id];\n    let route = manifest.routes[match.route.id];\n    return [route.css ? route.css.map(href => ({\n      rel: \"stylesheet\",\n      href\n    })) : [], (module === null || module === void 0 ? void 0 : (_module$links = module.links) === null || _module$links === void 0 ? void 0 : _module$links.call(module)) || []];\n  }).flat(2);\n  let preloads = getCurrentPageModulePreloadHrefs(matches, manifest);\n  return dedupeLinkDescriptors(descriptors, preloads);\n}\nasync function prefetchStyleLinks(route, routeModule) {\n  var _route$css, _routeModule$links;\n  if (!route.css && !routeModule.links || !isPreloadSupported()) return;\n  let descriptors = [((_route$css = route.css) === null || _route$css === void 0 ? void 0 : _route$css.map(href => ({\n    rel: \"stylesheet\",\n    href\n  }))) ?? [], ((_routeModule$links = routeModule.links) === null || _routeModule$links === void 0 ? void 0 : _routeModule$links.call(routeModule)) ?? []].flat(1);\n  if (descriptors.length === 0) return;\n  let styleLinks = [];\n  for (let descriptor of descriptors) {\n    if (!isPageLinkDescriptor(descriptor) && descriptor.rel === \"stylesheet\") {\n      styleLinks.push({\n        ...descriptor,\n        rel: \"preload\",\n        as: \"style\"\n      });\n    }\n  }\n\n  // don't block for non-matching media queries, or for stylesheets that are\n  // already in the DOM (active route revalidations)\n  let matchingLinks = styleLinks.filter(link => (!link.media || window.matchMedia(link.media).matches) && !document.querySelector(`link[rel=\"stylesheet\"][href=\"${link.href}\"]`));\n  await Promise.all(matchingLinks.map(prefetchStyleLink));\n}\nasync function prefetchStyleLink(descriptor) {\n  return new Promise(resolve => {\n    let link = document.createElement(\"link\");\n    Object.assign(link, descriptor);\n    function removeLink() {\n      // if a navigation interrupts this prefetch React will update the <head>\n      // and remove the link we put in there manually, so we check if it's still\n      // there before trying to remove it\n      if (document.head.contains(link)) {\n        document.head.removeChild(link);\n      }\n    }\n    link.onload = () => {\n      removeLink();\n      resolve();\n    };\n    link.onerror = () => {\n      removeLink();\n      resolve();\n    };\n    document.head.appendChild(link);\n  });\n}\n\n////////////////////////////////////////////////////////////////////////////////\nfunction isPageLinkDescriptor(object) {\n  return object != null && typeof object.page === \"string\";\n}\nfunction isHtmlLinkDescriptor(object) {\n  if (object == null) {\n    return false;\n  }\n\n  // <link> may not have an href if <link rel=\"preload\"> is used with imageSrcSet + imageSizes\n  // https://github.com/remix-run/remix/issues/184\n  // https://html.spec.whatwg.org/commit-snapshots/cb4f5ff75de5f4cbd7013c4abad02f21c77d4d1c/#attr-link-imagesrcset\n  if (object.href == null) {\n    return object.rel === \"preload\" && typeof object.imageSrcSet === \"string\" && typeof object.imageSizes === \"string\";\n  }\n  return typeof object.rel === \"string\" && typeof object.href === \"string\";\n}\nasync function getKeyedPrefetchLinks(matches, manifest, routeModules) {\n  let links = await Promise.all(matches.map(async match => {\n    let mod = await loadRouteModule(manifest.routes[match.route.id], routeModules);\n    return mod.links ? mod.links() : [];\n  }));\n  return dedupeLinkDescriptors(links.flat(1).filter(isHtmlLinkDescriptor).filter(link => link.rel === \"stylesheet\" || link.rel === \"preload\").map(link => link.rel === \"stylesheet\" ? {\n    ...link,\n    rel: \"prefetch\",\n    as: \"style\"\n  } : {\n    ...link,\n    rel: \"prefetch\"\n  }));\n}\n\n// This is ridiculously identical to transition.ts `filterMatchesToLoad`\nfunction getNewMatchesForLinks(page, nextMatches, currentMatches, manifest, location, future, mode) {\n  let path = parsePathPatch(page);\n  let isNew = (match, index) => {\n    if (!currentMatches[index]) return true;\n    return match.route.id !== currentMatches[index].route.id;\n  };\n  let matchPathChanged = (match, index) => {\n    var _currentMatches$index;\n    return (\n      // param change, /users/123 -> /users/456\n      currentMatches[index].pathname !== match.pathname ||\n      // splat param changed, which is not present in match.path\n      // e.g. /files/images/avatar.jpg -> files/finances.xls\n      ((_currentMatches$index = currentMatches[index].route.path) === null || _currentMatches$index === void 0 ? void 0 : _currentMatches$index.endsWith(\"*\")) && currentMatches[index].params[\"*\"] !== match.params[\"*\"]\n    );\n  };\n\n  // NOTE: keep this mostly up-to-date w/ the transition data diff, but this\n  // version doesn't care about submissions\n  let newMatches = mode === \"data\" && (future.v3_singleFetch || location.search !== path.search) ?\n  // this is really similar to stuff in transition.ts, maybe somebody smarter\n  // than me (or in less of a hurry) can share some of it. You're the best.\n  nextMatches.filter((match, index) => {\n    let manifestRoute = manifest.routes[match.route.id];\n    if (!manifestRoute.hasLoader) {\n      return false;\n    }\n    if (isNew(match, index) || matchPathChanged(match, index)) {\n      return true;\n    }\n\n    // For reused routes on GET navigations, by default:\n    // - Single fetch always revalidates\n    // - Multi fetch revalidates if search params changed\n    let defaultShouldRevalidate = future.v3_singleFetch || location.search !== path.search;\n    if (match.route.shouldRevalidate) {\n      var _currentMatches$;\n      let routeChoice = match.route.shouldRevalidate({\n        currentUrl: new URL(location.pathname + location.search + location.hash, window.origin),\n        currentParams: ((_currentMatches$ = currentMatches[0]) === null || _currentMatches$ === void 0 ? void 0 : _currentMatches$.params) || {},\n        nextUrl: new URL(page, window.origin),\n        nextParams: match.params,\n        defaultShouldRevalidate\n      });\n      if (typeof routeChoice === \"boolean\") {\n        return routeChoice;\n      }\n    }\n    return defaultShouldRevalidate;\n  }) : nextMatches.filter((match, index) => {\n    let manifestRoute = manifest.routes[match.route.id];\n    return (mode === \"assets\" || manifestRoute.hasLoader) && (isNew(match, index) || matchPathChanged(match, index));\n  });\n  return newMatches;\n}\nfunction getDataLinkHrefs(page, matches, manifest) {\n  let path = parsePathPatch(page);\n  return dedupeHrefs(matches.filter(match => manifest.routes[match.route.id].hasLoader && !manifest.routes[match.route.id].hasClientLoader).map(match => {\n    let {\n      pathname,\n      search\n    } = path;\n    let searchParams = new URLSearchParams(search);\n    searchParams.set(\"_data\", match.route.id);\n    return `${pathname}?${searchParams}`;\n  }));\n}\nfunction getModuleLinkHrefs(matches, manifestPatch) {\n  return dedupeHrefs(matches.map(match => {\n    let route = manifestPatch.routes[match.route.id];\n    let hrefs = [route.module];\n    if (route.imports) {\n      hrefs = hrefs.concat(route.imports);\n    }\n    return hrefs;\n  }).flat(1));\n}\n\n// The `<Script>` will render rel=modulepreload for the current page, we don't\n// need to include them in a page prefetch, this gives us the list to remove\n// while deduping.\nfunction getCurrentPageModulePreloadHrefs(matches, manifest) {\n  return dedupeHrefs(matches.map(match => {\n    let route = manifest.routes[match.route.id];\n    let hrefs = [route.module];\n    if (route.imports) {\n      hrefs = hrefs.concat(route.imports);\n    }\n    return hrefs;\n  }).flat(1));\n}\nfunction dedupeHrefs(hrefs) {\n  return [...new Set(hrefs)];\n}\nfunction sortKeys(obj) {\n  let sorted = {};\n  let keys = Object.keys(obj).sort();\n  for (let key of keys) {\n    sorted[key] = obj[key];\n  }\n  return sorted;\n}\nfunction dedupeLinkDescriptors(descriptors, preloads) {\n  let set = new Set();\n  let preloadsSet = new Set(preloads);\n  return descriptors.reduce((deduped, descriptor) => {\n    let alreadyModulePreload = preloads && !isPageLinkDescriptor(descriptor) && descriptor.as === \"script\" && descriptor.href && preloadsSet.has(descriptor.href);\n    if (alreadyModulePreload) {\n      return deduped;\n    }\n    let key = JSON.stringify(sortKeys(descriptor));\n    if (!set.has(key)) {\n      set.add(key);\n      deduped.push({\n        key,\n        link: descriptor\n      });\n    }\n    return deduped;\n  }, []);\n}\n\n// https://github.com/remix-run/history/issues/897\nfunction parsePathPatch(href) {\n  let path = parsePath(href);\n  if (path.search === undefined) path.search = \"\";\n  return path;\n}\n\n// Detect if this browser supports <link rel=\"preload\"> (or has it enabled).\n// Originally added to handle the firefox `network.preload` config:\n//   https://bugzilla.mozilla.org/show_bug.cgi?id=1847811\nlet _isPreloadSupported;\nfunction isPreloadSupported() {\n  if (_isPreloadSupported !== undefined) {\n    return _isPreloadSupported;\n  }\n  let el = document.createElement(\"link\");\n  _isPreloadSupported = el.relList.supports(\"preload\");\n  el = null;\n  return _isPreloadSupported;\n}\n\nexport { getDataLinkHrefs, getKeyedLinksForMatches, getKeyedPrefetchLinks, getModuleLinkHrefs, getNewMatchesForLinks, isPageLinkDescriptor, prefetchStyleLinks };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n// This escapeHtml utility is based on https://github.com/zertosh/htmlescape\n// License: https://github.com/zertosh/htmlescape/blob/0527ca7156a524d256101bb310a9f970f63078ad/LICENSE\n\n// We've chosen to inline the utility here to reduce the number of npm dependencies we have,\n// slightly decrease the code size compared the original package and make it esm compatible.\n\nconst ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nconst ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction escapeHtml(html) {\n  return html.replace(ESCAPE_REGEX, match => ESCAPE_LOOKUP[match]);\n}\nfunction createHtml(html) {\n  return {\n    __html: html\n  };\n}\n\nexport { createHtml, escapeHtml };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { isRouteErrorResponse, data, UNSAFE_ErrorResponseImpl, redirect } from '@remix-run/router';\nimport { UNSAFE_SingleFetchRedirectSymbol } from '@remix-run/server-runtime';\nimport { decode } from 'turbo-stream';\nimport { createRequestInit, isResponse } from './data.js';\nimport { escapeHtml } from './markup.js';\nimport invariant from './invariant.js';\n\n// StreamTransfer recursively renders down chunks of the `serverHandoffStream`\n// into the client-side `streamController`\nfunction StreamTransfer({\n  context,\n  identifier,\n  reader,\n  textDecoder,\n  nonce\n}) {\n  // If the user didn't render the <Scripts> component then we don't have to\n  // bother streaming anything in\n  if (!context.renderMeta || !context.renderMeta.didRenderScripts) {\n    return null;\n  }\n  if (!context.renderMeta.streamCache) {\n    context.renderMeta.streamCache = {};\n  }\n  let {\n    streamCache\n  } = context.renderMeta;\n  let promise = streamCache[identifier];\n  if (!promise) {\n    promise = streamCache[identifier] = reader.read().then(result => {\n      streamCache[identifier].result = {\n        done: result.done,\n        value: textDecoder.decode(result.value, {\n          stream: true\n        })\n      };\n    }).catch(e => {\n      streamCache[identifier].error = e;\n    });\n  }\n  if (promise.error) {\n    throw promise.error;\n  }\n  if (promise.result === undefined) {\n    throw promise;\n  }\n  let {\n    done,\n    value\n  } = promise.result;\n  let scriptTag = value ? /*#__PURE__*/React.createElement(\"script\", {\n    nonce: nonce,\n    dangerouslySetInnerHTML: {\n      __html: `window.__remixContext.streamController.enqueue(${escapeHtml(JSON.stringify(value))});`\n    }\n  }) : null;\n  if (done) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, scriptTag, /*#__PURE__*/React.createElement(\"script\", {\n      nonce: nonce,\n      dangerouslySetInnerHTML: {\n        __html: `window.__remixContext.streamController.close();`\n      }\n    }));\n  } else {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, scriptTag, /*#__PURE__*/React.createElement(React.Suspense, null, /*#__PURE__*/React.createElement(StreamTransfer, {\n      context: context,\n      identifier: identifier + 1,\n      reader: reader,\n      textDecoder: textDecoder,\n      nonce: nonce\n    })));\n  }\n}\nfunction getSingleFetchDataStrategy(manifest, routeModules, getRouter) {\n  return async ({\n    request,\n    matches,\n    fetcherKey\n  }) => {\n    // Actions are simple and behave the same for navigations and fetchers\n    if (request.method !== \"GET\") {\n      return singleFetchActionStrategy(request, matches);\n    }\n\n    // Fetcher loads are singular calls to one loader\n    if (fetcherKey) {\n      return singleFetchLoaderFetcherStrategy(request, matches);\n    }\n\n    // Navigational loads are more complex...\n    return singleFetchLoaderNavigationStrategy(manifest, routeModules, getRouter(), request, matches);\n  };\n}\n\n// Actions are simple since they're singular calls to the server for both\n// navigations and fetchers)\nasync function singleFetchActionStrategy(request, matches) {\n  let actionMatch = matches.find(m => m.shouldLoad);\n  invariant(actionMatch, \"No action match found\");\n  let actionStatus = undefined;\n  let result = await actionMatch.resolve(async handler => {\n    let result = await handler(async () => {\n      let url = singleFetchUrl(request.url);\n      let init = await createRequestInit(request);\n      let {\n        data,\n        status\n      } = await fetchAndDecode(url, init);\n      actionStatus = status;\n      return unwrapSingleFetchResult(data, actionMatch.route.id);\n    });\n    return result;\n  });\n  if (isResponse(result.result) || isRouteErrorResponse(result.result)) {\n    return {\n      [actionMatch.route.id]: result\n    };\n  }\n\n  // For non-responses, proxy along the statusCode via data()\n  // (most notably for skipping action error revalidation)\n  return {\n    [actionMatch.route.id]: {\n      type: result.type,\n      result: data(result.result, actionStatus)\n    }\n  };\n}\n\n// Loaders are trickier since we only want to hit the server once, so we\n// create a singular promise for all server-loader routes to latch onto.\nasync function singleFetchLoaderNavigationStrategy(manifest, routeModules, router, request, matches) {\n  // Track which routes need a server load - in case we need to tack on a\n  // `_routes` param\n  let routesParams = new Set();\n\n  // We only add `_routes` when one or more routes opts out of a load via\n  // `shouldRevalidate` or `clientLoader`\n  let foundOptOutRoute = false;\n\n  // Deferreds for each route so we can be sure they've all loaded via\n  // `match.resolve()`, and a singular promise that can tell us all routes\n  // have been resolved\n  let routeDfds = matches.map(() => createDeferred());\n  let routesLoadedPromise = Promise.all(routeDfds.map(d => d.promise));\n\n  // Deferred that we'll use for the call to the server that each match can\n  // await and parse out it's specific result\n  let singleFetchDfd = createDeferred();\n\n  // Base URL and RequestInit for calls to the server\n  let url = stripIndexParam(singleFetchUrl(request.url));\n  let init = await createRequestInit(request);\n\n  // We'll build up this results object as we loop through matches\n  let results = {};\n  let resolvePromise = Promise.all(matches.map(async (m, i) => m.resolve(async handler => {\n    routeDfds[i].resolve();\n    if (!m.shouldLoad) {\n      var _routeModules$m$route;\n      // If we're not yet initialized and this is the initial load, respect\n      // `shouldLoad` because we're only dealing with `clientLoader.hydrate`\n      // routes which will fall into the `clientLoader` section below.\n      if (!router.state.initialized) {\n        return;\n      }\n\n      // Otherwise, we opt out if we currently have data, a `loader`, and a\n      // `shouldRevalidate` function.  This implies that the user opted out\n      // via `shouldRevalidate`\n      if (m.route.id in router.state.loaderData && manifest.routes[m.route.id].hasLoader && (_routeModules$m$route = routeModules[m.route.id]) !== null && _routeModules$m$route !== void 0 && _routeModules$m$route.shouldRevalidate) {\n        foundOptOutRoute = true;\n        return;\n      }\n    }\n\n    // When a route has a client loader, it opts out of the singular call and\n    // calls it's server loader via `serverLoader()` using a `?_routes` param\n    if (manifest.routes[m.route.id].hasClientLoader) {\n      if (manifest.routes[m.route.id].hasLoader) {\n        foundOptOutRoute = true;\n      }\n      try {\n        let result = await fetchSingleLoader(handler, url, init, m.route.id);\n        results[m.route.id] = {\n          type: \"data\",\n          result\n        };\n      } catch (e) {\n        results[m.route.id] = {\n          type: \"error\",\n          result: e\n        };\n      }\n      return;\n    }\n\n    // Load this route on the server if it has a loader\n    if (manifest.routes[m.route.id].hasLoader) {\n      routesParams.add(m.route.id);\n    }\n\n    // Lump this match in with the others on a singular promise\n    try {\n      let result = await handler(async () => {\n        let data = await singleFetchDfd.promise;\n        return unwrapSingleFetchResults(data, m.route.id);\n      });\n      results[m.route.id] = {\n        type: \"data\",\n        result\n      };\n    } catch (e) {\n      results[m.route.id] = {\n        type: \"error\",\n        result: e\n      };\n    }\n  })));\n\n  // Wait for all routes to resolve above before we make the HTTP call\n  await routesLoadedPromise;\n\n  // We can skip the server call:\n  // - On initial hydration - only clientLoaders can pass through via `clientLoader.hydrate`\n  // - If there are no routes to fetch from the server\n  //\n  // One exception - if we are performing an HDR revalidation we have to call\n  // the server in case a new loader has shown up that the manifest doesn't yet\n  // know about\n  if ((!router.state.initialized || routesParams.size === 0) && !window.__remixHdrActive) {\n    singleFetchDfd.resolve({});\n  } else {\n    try {\n      // When one or more routes have opted out, we add a _routes param to\n      // limit the loaders to those that have a server loader and did not\n      // opt out\n      if (foundOptOutRoute && routesParams.size > 0) {\n        url.searchParams.set(\"_routes\", matches.filter(m => routesParams.has(m.route.id)).map(m => m.route.id).join(\",\"));\n      }\n      let data = await fetchAndDecode(url, init);\n      singleFetchDfd.resolve(data.data);\n    } catch (e) {\n      singleFetchDfd.reject(e);\n    }\n  }\n  await resolvePromise;\n  return results;\n}\n\n// Fetcher loader calls are much simpler than navigational loader calls\nasync function singleFetchLoaderFetcherStrategy(request, matches) {\n  let fetcherMatch = matches.find(m => m.shouldLoad);\n  invariant(fetcherMatch, \"No fetcher match found\");\n  let result = await fetcherMatch.resolve(async handler => {\n    let url = stripIndexParam(singleFetchUrl(request.url));\n    let init = await createRequestInit(request);\n    return fetchSingleLoader(handler, url, init, fetcherMatch.route.id);\n  });\n  return {\n    [fetcherMatch.route.id]: result\n  };\n}\nfunction fetchSingleLoader(handler, url, init, routeId) {\n  return handler(async () => {\n    let singleLoaderUrl = new URL(url);\n    singleLoaderUrl.searchParams.set(\"_routes\", routeId);\n    let {\n      data\n    } = await fetchAndDecode(singleLoaderUrl, init);\n    return unwrapSingleFetchResults(data, routeId);\n  });\n}\nfunction stripIndexParam(url) {\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  return url;\n}\nfunction singleFetchUrl(reqUrl) {\n  let url = typeof reqUrl === \"string\" ? new URL(reqUrl, window.location.origin) : reqUrl;\n  if (url.pathname === \"/\") {\n    url.pathname = \"_root.data\";\n  } else {\n    url.pathname = `${url.pathname.replace(/\\/$/, \"\")}.data`;\n  }\n  return url;\n}\nasync function fetchAndDecode(url, init) {\n  let res = await fetch(url, init);\n  // Don't do a hard check against the header here.  We'll get `text/x-script`\n  // when we have a running server, but if folks want to prerender `.data` files\n  // and serve them from a CDN we should let them come back with whatever\n  // Content-Type their CDN provides and not force them to make sure `.data`\n  // files are served as `text/x-script`.  We'll throw if we can't decode anyway.\n\n  // some status codes are not permitted to have bodies, so we want to just\n  // treat those as \"no data\" instead of throwing an exception.\n  // 304 is not included here because the browser should fill those responses\n  // with the cached body content.\n  let NO_BODY_STATUS_CODES = new Set([100, 101, 204, 205]);\n  if (NO_BODY_STATUS_CODES.has(res.status)) {\n    if (!init.method || init.method === \"GET\") {\n      // SingleFetchResults can just have no routeId keys which will result\n      // in no data for all routes\n      return {\n        status: res.status,\n        data: {}\n      };\n    } else {\n      // SingleFetchResult is for a singular route and can specify no data\n      return {\n        status: res.status,\n        data: {\n          data: null\n        }\n      };\n    }\n  }\n  invariant(res.body, \"No response body to decode\");\n  try {\n    let decoded = await decodeViaTurboStream(res.body, window);\n    return {\n      status: res.status,\n      data: decoded.value\n    };\n  } catch (e) {\n    console.error(e);\n    throw new Error(`Unable to decode turbo-stream response from URL: ${url.toString()}`);\n  }\n}\n\n// Note: If you change this function please change the corresponding\n// encodeViaTurboStream function in server-runtime\nfunction decodeViaTurboStream(body, global) {\n  return decode(body, {\n    plugins: [(type, ...rest) => {\n      // Decode Errors back into Error instances using the right type and with\n      // the right (potentially undefined) stacktrace\n      if (type === \"SanitizedError\") {\n        let [name, message, stack] = rest;\n        let Constructor = Error;\n        // @ts-expect-error\n        if (name && name in global && typeof global[name] === \"function\") {\n          // @ts-expect-error\n          Constructor = global[name];\n        }\n        let error = new Constructor(message);\n        error.stack = stack;\n        return {\n          value: error\n        };\n      }\n      if (type === \"ErrorResponse\") {\n        let [data, status, statusText] = rest;\n        return {\n          value: new UNSAFE_ErrorResponseImpl(status, statusText, data)\n        };\n      }\n      if (type === \"SingleFetchRedirect\") {\n        return {\n          value: {\n            [UNSAFE_SingleFetchRedirectSymbol]: rest[0]\n          }\n        };\n      }\n    }, (type, value) => {\n      if (type === \"SingleFetchFallback\") {\n        return {\n          value: undefined\n        };\n      }\n      if (type === \"SingleFetchClassInstance\") {\n        return {\n          value\n        };\n      }\n    }]\n  });\n}\nfunction unwrapSingleFetchResults(results, routeId) {\n  let redirect = results[UNSAFE_SingleFetchRedirectSymbol];\n  if (redirect) {\n    return unwrapSingleFetchResult(redirect, routeId);\n  }\n  return results[routeId] !== undefined ? unwrapSingleFetchResult(results[routeId], routeId) : null;\n}\nfunction unwrapSingleFetchResult(result, routeId) {\n  if (\"error\" in result) {\n    throw result.error;\n  } else if (\"redirect\" in result) {\n    let headers = {};\n    if (result.revalidate) {\n      headers[\"X-Remix-Revalidate\"] = \"yes\";\n    }\n    if (result.reload) {\n      headers[\"X-Remix-Reload-Document\"] = \"yes\";\n    }\n    if (result.replace) {\n      headers[\"X-Remix-Replace\"] = \"yes\";\n    }\n    throw redirect(result.redirect, {\n      status: result.status,\n      headers\n    });\n  } else if (\"data\" in result) {\n    return result.data;\n  } else {\n    throw new Error(`No response found for routeId \"${routeId}\"`);\n  }\n}\nfunction createDeferred() {\n  let resolve;\n  let reject;\n  let promise = new Promise((res, rej) => {\n    resolve = async val => {\n      res(val);\n      try {\n        await promise;\n      } catch (e) {}\n    };\n    reject = async error => {\n      rej(error);\n      try {\n        await promise;\n      } catch (e) {}\n    };\n  });\n  return {\n    promise,\n    //@ts-ignore\n    resolve,\n    //@ts-ignore\n    reject\n  };\n}\n\nexport { StreamTransfer, decodeViaTurboStream, getSingleFetchDataStrategy, singleFetchUrl };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { AbortedDeferredError, UNSAFE_DeferredData } from '@remix-run/router';\n\n/**\n * Data for a route that was returned from a `loader()`.\n */\n\nfunction isCatchResponse(response) {\n  return response.headers.get(\"X-Remix-Catch\") != null;\n}\nfunction isErrorResponse(response) {\n  return response.headers.get(\"X-Remix-Error\") != null;\n}\nfunction isNetworkErrorResponse(response) {\n  // If we reach the Remix server, we can safely identify response types via the\n  // X-Remix-Error/X-Remix-Catch headers.  However, if we never reach the Remix\n  // server, and instead receive a 4xx/5xx from somewhere in between (like\n  // Cloudflare), then we get a false negative in the isErrorResponse check and\n  // we incorrectly assume that the user returns the 4xx/5xx response and\n  // consider it successful.  To alleviate this, we add X-Remix-Response to any\n  // non-Error/non-Catch responses coming back from the server.  If we don't\n  // see this, we can conclude that a 4xx/5xx response never actually reached\n  // the Remix server and we can bubble it up as an error.\n  return isResponse(response) && response.status >= 400 && response.headers.get(\"X-Remix-Error\") == null && response.headers.get(\"X-Remix-Catch\") == null && response.headers.get(\"X-Remix-Response\") == null;\n}\nfunction isRedirectResponse(response) {\n  return response.headers.get(\"X-Remix-Redirect\") != null;\n}\nfunction isDeferredResponse(response) {\n  var _response$headers$get;\n  return !!((_response$headers$get = response.headers.get(\"Content-Type\")) !== null && _response$headers$get !== void 0 && _response$headers$get.match(/text\\/remix-deferred/));\n}\nfunction isResponse(value) {\n  return value != null && typeof value.status === \"number\" && typeof value.statusText === \"string\" && typeof value.headers === \"object\" && typeof value.body !== \"undefined\";\n}\nfunction isDeferredData(value) {\n  let deferred = value;\n  return deferred && typeof deferred === \"object\" && typeof deferred.data === \"object\" && typeof deferred.subscribe === \"function\" && typeof deferred.cancel === \"function\" && typeof deferred.resolveData === \"function\";\n}\nasync function fetchData(request, routeId, retry = 0) {\n  let url = new URL(request.url);\n  url.searchParams.set(\"_data\", routeId);\n  if (retry > 0) {\n    // Retry up to 3 times waiting 50, 250, 1250 ms\n    // between retries for a total of 1550 ms before giving up.\n    await new Promise(resolve => setTimeout(resolve, 5 ** retry * 10));\n  }\n  let init = await createRequestInit(request);\n  let revalidation = window.__remixRevalidation;\n  let response = await fetch(url.href, init).catch(error => {\n    if (typeof revalidation === \"number\" && revalidation === window.__remixRevalidation && (error === null || error === void 0 ? void 0 : error.name) === \"TypeError\" && retry < 3) {\n      return fetchData(request, routeId, retry + 1);\n    }\n    throw error;\n  });\n  if (isErrorResponse(response)) {\n    let data = await response.json();\n    let error = new Error(data.message);\n    error.stack = data.stack;\n    return error;\n  }\n  if (isNetworkErrorResponse(response)) {\n    let text = await response.text();\n    let error = new Error(text);\n    error.stack = undefined;\n    return error;\n  }\n  return response;\n}\nasync function createRequestInit(request) {\n  let init = {\n    signal: request.signal\n  };\n  if (request.method !== \"GET\") {\n    init.method = request.method;\n    let contentType = request.headers.get(\"Content-Type\");\n\n    // Check between word boundaries instead of startsWith() due to the last\n    // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n    if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n      init.headers = {\n        \"Content-Type\": contentType\n      };\n      init.body = JSON.stringify(await request.json());\n    } else if (contentType && /\\btext\\/plain\\b/.test(contentType)) {\n      init.headers = {\n        \"Content-Type\": contentType\n      };\n      init.body = await request.text();\n    } else if (contentType && /\\bapplication\\/x-www-form-urlencoded\\b/.test(contentType)) {\n      init.body = new URLSearchParams(await request.text());\n    } else {\n      init.body = await request.formData();\n    }\n  }\n  return init;\n}\nconst DEFERRED_VALUE_PLACEHOLDER_PREFIX = \"__deferred_promise:\";\nasync function parseDeferredReadableStream(stream) {\n  if (!stream) {\n    throw new Error(\"parseDeferredReadableStream requires stream argument\");\n  }\n  let deferredData;\n  let deferredResolvers = {};\n  try {\n    let sectionReader = readStreamSections(stream);\n\n    // Read the first section to get the critical data\n    let initialSectionResult = await sectionReader.next();\n    let initialSection = initialSectionResult.value;\n    if (!initialSection) throw new Error(\"no critical data\");\n    let criticalData = JSON.parse(initialSection);\n\n    // Setup deferred data and resolvers for later based on the critical data\n    if (typeof criticalData === \"object\" && criticalData !== null) {\n      for (let [eventKey, value] of Object.entries(criticalData)) {\n        if (typeof value !== \"string\" || !value.startsWith(DEFERRED_VALUE_PLACEHOLDER_PREFIX)) {\n          continue;\n        }\n        deferredData = deferredData || {};\n        deferredData[eventKey] = new Promise((resolve, reject) => {\n          deferredResolvers[eventKey] = {\n            resolve: value => {\n              resolve(value);\n              delete deferredResolvers[eventKey];\n            },\n            reject: error => {\n              reject(error);\n              delete deferredResolvers[eventKey];\n            }\n          };\n        });\n      }\n    }\n\n    // Read the rest of the stream and resolve deferred promises\n    void (async () => {\n      try {\n        for await (let section of sectionReader) {\n          // Determine event type and data\n          let [event, ...sectionDataStrings] = section.split(\":\");\n          let sectionDataString = sectionDataStrings.join(\":\");\n          let data = JSON.parse(sectionDataString);\n          if (event === \"data\") {\n            for (let [key, value] of Object.entries(data)) {\n              if (deferredResolvers[key]) {\n                deferredResolvers[key].resolve(value);\n              }\n            }\n          } else if (event === \"error\") {\n            for (let [key, value] of Object.entries(data)) {\n              let err = new Error(value.message);\n              err.stack = value.stack;\n              if (deferredResolvers[key]) {\n                deferredResolvers[key].reject(err);\n              }\n            }\n          }\n        }\n        for (let [key, resolver] of Object.entries(deferredResolvers)) {\n          resolver.reject(new AbortedDeferredError(`Deferred ${key} will never be resolved`));\n        }\n      } catch (error) {\n        // Reject any existing deferred promises if something blows up\n        for (let resolver of Object.values(deferredResolvers)) {\n          resolver.reject(error);\n        }\n      }\n    })();\n    return new UNSAFE_DeferredData({\n      ...criticalData,\n      ...deferredData\n    });\n  } catch (error) {\n    for (let resolver of Object.values(deferredResolvers)) {\n      resolver.reject(error);\n    }\n    throw error;\n  }\n}\nasync function* readStreamSections(stream) {\n  let reader = stream.getReader();\n  let buffer = [];\n  let sections = [];\n  let closed = false;\n  let encoder = new TextEncoder();\n  let decoder = new TextDecoder();\n  let readStreamSection = async () => {\n    if (sections.length > 0) return sections.shift();\n\n    // Read from the stream until we have at least one complete section to process\n    while (!closed && sections.length === 0) {\n      let chunk = await reader.read();\n      if (chunk.done) {\n        closed = true;\n        break;\n      }\n      // Buffer the raw chunks\n      buffer.push(chunk.value);\n      try {\n        // Attempt to split off a section from the buffer\n        let bufferedString = decoder.decode(mergeArrays(...buffer));\n        let splitSections = bufferedString.split(\"\\n\\n\");\n        if (splitSections.length >= 2) {\n          // We have a complete section, so add it to the sections array\n          sections.push(...splitSections.slice(0, -1));\n          // Remove the section from the buffer and store the rest for future processing\n          buffer = [encoder.encode(splitSections.slice(-1).join(\"\\n\\n\"))];\n        }\n\n        // If we successfully parsed at least one section, break out of reading the stream\n        // to allow upstream processing of the processable sections\n        if (sections.length > 0) {\n          break;\n        }\n      } catch {\n        // If we failed to parse the buffer it was because we failed to decode the stream\n        // because we are missing bytes that we haven't yet received, so continue reading\n        // from the stream until we have a complete section\n        continue;\n      }\n    }\n\n    // If we have a complete section, return it\n    if (sections.length > 0) {\n      return sections.shift();\n    }\n\n    // If we have no complete section, but we have no more chunks to process,\n    // split those sections and clear out the buffer as there is no more data\n    // to process. If this errors, let it bubble up as the stream ended\n    // without valid data\n    if (buffer.length > 0) {\n      let bufferedString = decoder.decode(mergeArrays(...buffer));\n      sections = bufferedString.split(\"\\n\\n\").filter(s => s);\n      buffer = [];\n    }\n\n    // Return any remaining sections that have been processed\n    return sections.shift();\n  };\n  let section = await readStreamSection();\n  while (section) {\n    yield section;\n    section = await readStreamSection();\n  }\n}\nfunction mergeArrays(...arrays) {\n  let out = new Uint8Array(arrays.reduce((total, arr) => total + arr.length, 0));\n  let offset = 0;\n  for (let arr of arrays) {\n    out.set(arr, offset);\n    offset += arr.length;\n  }\n  return out;\n}\n\nexport { createRequestInit, fetchData, isCatchResponse, isDeferredData, isDeferredResponse, isErrorResponse, isNetworkErrorResponse, isRedirectResponse, isResponse, parseDeferredReadableStream };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { matchRoutes } from '@remix-run/router';\nimport * as React from 'react';\nimport { createClientRoutes } from './routes.js';\n\n// Currently rendered links that may need prefetching\nconst nextPaths = new Set();\n\n// FIFO queue of previously discovered routes to prevent re-calling on\n// subsequent navigations to the same path\nconst discoveredPathsMaxSize = 1000;\nconst discoveredPaths = new Set();\n\n// 7.5k to come in under the ~8k limit for most browsers\n// https://stackoverflow.com/a/417184\nconst URL_LIMIT = 7680;\nfunction isFogOfWarEnabled(future, isSpaMode) {\n  return future.v3_lazyRouteDiscovery === true && !isSpaMode;\n}\nfunction getPartialManifest(manifest, router) {\n  // Start with our matches for this pathname\n  let routeIds = new Set(router.state.matches.map(m => m.route.id));\n  let segments = router.state.location.pathname.split(\"/\").filter(Boolean);\n  let paths = [\"/\"];\n\n  // We've already matched to the last segment\n  segments.pop();\n\n  // Traverse each path for our parents and match in case they have pathless/index\n  // children we need to include in the initial manifest\n  while (segments.length > 0) {\n    paths.push(`/${segments.join(\"/\")}`);\n    segments.pop();\n  }\n  paths.forEach(path => {\n    let matches = matchRoutes(router.routes, path, router.basename);\n    if (matches) {\n      matches.forEach(m => routeIds.add(m.route.id));\n    }\n  });\n  let initialRoutes = [...routeIds].reduce((acc, id) => Object.assign(acc, {\n    [id]: manifest.routes[id]\n  }), {});\n  return {\n    ...manifest,\n    routes: initialRoutes\n  };\n}\nfunction getPatchRoutesOnNavigationFunction(manifest, routeModules, future, isSpaMode, basename) {\n  if (!isFogOfWarEnabled(future, isSpaMode)) {\n    return undefined;\n  }\n  return async ({\n    path,\n    patch,\n    signal\n  }) => {\n    if (discoveredPaths.has(path)) {\n      return;\n    }\n    await fetchAndApplyManifestPatches([path], manifest, routeModules, future, isSpaMode, basename, patch, signal);\n  };\n}\nfunction useFogOFWarDiscovery(router, manifest, routeModules, future, isSpaMode) {\n  React.useEffect(() => {\n    var _navigator$connection;\n    // Don't prefetch if not enabled or if the user has `saveData` enabled\n    if (!isFogOfWarEnabled(future, isSpaMode) || ((_navigator$connection = navigator.connection) === null || _navigator$connection === void 0 ? void 0 : _navigator$connection.saveData) === true) {\n      return;\n    }\n\n    // Register a link href for patching\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let url = new URL(path, window.location.origin);\n      if (!discoveredPaths.has(url.pathname)) {\n        nextPaths.add(url.pathname);\n      }\n    }\n\n    // Fetch patches for all currently rendered links\n    async function fetchPatches() {\n      let lazyPaths = Array.from(nextPaths.keys()).filter(path => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (lazyPaths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(lazyPaths, manifest, routeModules, future, isSpaMode, router.basename, router.patchRoutes);\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n\n    // Register and fetch patches for all initially-rendered links/forms\n    document.body.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(el => registerElement(el));\n    fetchPatches();\n\n    // Setup a MutationObserver to fetch all subsequently rendered links/forms\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    function isElement(node) {\n      return node.nodeType === Node.ELEMENT_NODE;\n    }\n    let observer = new MutationObserver(records => {\n      let elements = new Set();\n      records.forEach(r => {\n        [r.target, ...r.addedNodes].forEach(node => {\n          if (!isElement(node)) return;\n          if (node.tagName === \"A\" && node.getAttribute(\"data-discover\")) {\n            elements.add(node);\n          } else if (node.tagName === \"FORM\" && node.getAttribute(\"data-discover\")) {\n            elements.add(node);\n          }\n          if (node.tagName !== \"A\") {\n            node.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(el => elements.add(el));\n          }\n        });\n      });\n      elements.forEach(el => registerElement(el));\n      debouncedFetchPatches();\n    });\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n    return () => observer.disconnect();\n  }, [future, isSpaMode, manifest, routeModules, router]);\n}\nasync function fetchAndApplyManifestPatches(paths, manifest, routeModules, future, isSpaMode, basename, patchRoutes, signal) {\n  let manifestPath = `${basename ?? \"/\"}/__manifest`.replace(/\\/+/g, \"/\");\n  let url = new URL(manifestPath, window.location.origin);\n  paths.sort().forEach(path => url.searchParams.append(\"p\", path));\n  url.searchParams.set(\"version\", manifest.version);\n\n  // If the URL is nearing the ~8k limit on GET requests, skip this optimization\n  // step and just let discovery happen on link click.  We also wipe out the\n  // nextPaths Set here so we can start filling it with fresh links\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let serverPatches;\n  try {\n    let res = await fetch(url, {\n      signal\n    });\n    if (!res.ok) {\n      throw new Error(`${res.status} ${res.statusText}`);\n    } else if (res.status >= 400) {\n      throw new Error(await res.text());\n    }\n    serverPatches = await res.json();\n  } catch (e) {\n    if (signal !== null && signal !== void 0 && signal.aborted) return;\n    throw e;\n  }\n\n  // Patch routes we don't know about yet into the manifest\n  let knownRoutes = new Set(Object.keys(manifest.routes));\n  let patches = Object.values(serverPatches).reduce((acc, route) => !knownRoutes.has(route.id) ? Object.assign(acc, {\n    [route.id]: route\n  }) : acc, {});\n  Object.assign(manifest.routes, patches);\n\n  // Track discovered paths so we don't have to fetch them again\n  paths.forEach(p => addToFifoQueue(p, discoveredPaths));\n\n  // Identify all parentIds for which we have new children to add and patch\n  // in their new children\n  let parentIds = new Set();\n  Object.values(patches).forEach(patch => {\n    if (!patch.parentId || !patches[patch.parentId]) {\n      parentIds.add(patch.parentId);\n    }\n  });\n  parentIds.forEach(parentId => patchRoutes(parentId || null, createClientRoutes(patches, routeModules, null, future, isSpaMode, parentId)));\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    if (typeof first === \"string\") queue.delete(first);\n  }\n  queue.add(path);\n}\n\n// Thanks Josh!\n// https://www.joshwcomeau.com/snippets/javascript/debounce/\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\nexport { fetchAndApplyManifestPatches, getPartialManifest, getPatchRoutesOnNavigationFunction, isFogOfWarEnabled, useFogOFWarDiscovery };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { UNSAFE_ErrorResponseImpl } from '@remix-run/router';\nimport { useRouteError, redirect } from 'react-router-dom';\nimport { loadRouteModule } from './routeModules.js';\nimport { fetchData, isRedirectResponse, isCatchResponse, isDeferredResponse, parseDeferredReadableStream, isDeferredData, isResponse } from './data.js';\nimport { prefetchStyleLinks } from './links.js';\nimport { RemixRootDefaultErrorBoundary } from './errorBoundaries.js';\nimport { RemixRootDefaultHydrateFallback } from './fallback.js';\nimport invariant from './invariant.js';\n\n// NOTE: make sure to change the Route in server-runtime if you change this\n\n// NOTE: make sure to change the EntryRoute in server-runtime if you change this\n\n// Create a map of routes by parentId to use recursively instead of\n// repeatedly filtering the manifest.\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach(route => {\n    let parentId = route.parentId || \"\";\n    if (!routes[parentId]) {\n      routes[parentId] = [];\n    }\n    routes[parentId].push(route);\n  });\n  return routes;\n}\nfunction getRouteComponents(route, routeModule, isSpaMode) {\n  let Component = getRouteModuleComponent(routeModule);\n  // HydrateFallback can only exist on the root route in SPA Mode\n  let HydrateFallback = routeModule.HydrateFallback && (!isSpaMode || route.id === \"root\") ? routeModule.HydrateFallback : route.id === \"root\" ? RemixRootDefaultHydrateFallback : undefined;\n  let ErrorBoundary = routeModule.ErrorBoundary ? routeModule.ErrorBoundary : route.id === \"root\" ? () => /*#__PURE__*/React.createElement(RemixRootDefaultErrorBoundary, {\n    error: useRouteError()\n  }) : undefined;\n  if (route.id === \"root\" && routeModule.Layout) {\n    return {\n      ...(Component ? {\n        element: /*#__PURE__*/React.createElement(routeModule.Layout, null, /*#__PURE__*/React.createElement(Component, null))\n      } : {\n        Component\n      }),\n      ...(ErrorBoundary ? {\n        errorElement: /*#__PURE__*/React.createElement(routeModule.Layout, null, /*#__PURE__*/React.createElement(ErrorBoundary, null))\n      } : {\n        ErrorBoundary\n      }),\n      ...(HydrateFallback ? {\n        hydrateFallbackElement: /*#__PURE__*/React.createElement(routeModule.Layout, null, /*#__PURE__*/React.createElement(HydrateFallback, null))\n      } : {\n        HydrateFallback\n      })\n    };\n  }\n  return {\n    Component,\n    ErrorBoundary,\n    HydrateFallback\n  };\n}\nfunction createServerRoutes(manifest, routeModules, future, isSpaMode, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest), spaModeLazyPromise = Promise.resolve({\n  Component: () => null\n})) {\n  return (routesByParentId[parentId] || []).map(route => {\n    let routeModule = routeModules[route.id];\n    invariant(routeModule, \"No `routeModule` available to create server routes\");\n    let dataRoute = {\n      ...getRouteComponents(route, routeModule, isSpaMode),\n      caseSensitive: route.caseSensitive,\n      id: route.id,\n      index: route.index,\n      path: route.path,\n      handle: routeModule.handle,\n      // For SPA Mode, all routes are lazy except root.  However we tell the\n      // router root is also lazy here too since we don't need a full\n      // implementation - we just need a `lazy` prop to tell the RR rendering\n      // where to stop which is always at the root route in SPA mode\n      lazy: isSpaMode ? () => spaModeLazyPromise : undefined,\n      // For partial hydration rendering, we need to indicate when the route\n      // has a loader/clientLoader, but it won't ever be called during the static\n      // render, so just give it a no-op function so we can render down to the\n      // proper fallback\n      loader: route.hasLoader || route.hasClientLoader ? () => null : undefined\n      // We don't need action/shouldRevalidate on these routes since they're\n      // for a static render\n    };\n    let children = createServerRoutes(manifest, routeModules, future, isSpaMode, route.id, routesByParentId, spaModeLazyPromise);\n    if (children.length > 0) dataRoute.children = children;\n    return dataRoute;\n  });\n}\nfunction createClientRoutesWithHMRRevalidationOptOut(needsRevalidation, manifest, routeModulesCache, initialState, future, isSpaMode) {\n  return createClientRoutes(manifest, routeModulesCache, initialState, future, isSpaMode, \"\", groupRoutesByParentId(manifest), needsRevalidation);\n}\nfunction preventInvalidServerHandlerCall(type, route, isSpaMode) {\n  if (isSpaMode) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You cannot call ${fn} in SPA Mode (routeId: \"${route.id}\")`;\n    console.error(msg);\n    throw new UNSAFE_ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n  let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n  let msg = `You are trying to call ${fn} on a route that does not have a server ` + `${type} (routeId: \"${route.id}\")`;\n  if (type === \"loader\" && !route.hasLoader || type === \"action\" && !route.hasAction) {\n    console.error(msg);\n    throw new UNSAFE_ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nfunction noActionDefinedError(type, routeId) {\n  let article = type === \"clientAction\" ? \"a\" : \"an\";\n  let msg = `Route \"${routeId}\" does not have ${article} ${type}, but you are trying to ` + `submit to it. To fix this, please add ${article} \\`${type}\\` function to the route`;\n  console.error(msg);\n  throw new UNSAFE_ErrorResponseImpl(405, \"Method Not Allowed\", new Error(msg), true);\n}\nfunction createClientRoutes(manifest, routeModulesCache, initialState, future, isSpaMode, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest), needsRevalidation) {\n  return (routesByParentId[parentId] || []).map(route => {\n    let routeModule = routeModulesCache[route.id];\n\n    // Fetch data from the server either via single fetch or the standard `?_data`\n    // request.  Unwrap it when called via `serverLoader`/`serverAction` in a\n    // client handler, otherwise return the raw response for the router to unwrap\n    async function fetchServerHandlerAndMaybeUnwrap(request, unwrap, singleFetch) {\n      if (typeof singleFetch === \"function\") {\n        let result = await singleFetch();\n        return result;\n      }\n      let result = await fetchServerHandler(request, route);\n      return unwrap ? unwrapServerResponse(result) : result;\n    }\n    function fetchServerLoader(request, unwrap, singleFetch) {\n      if (!route.hasLoader) return Promise.resolve(null);\n      return fetchServerHandlerAndMaybeUnwrap(request, unwrap, singleFetch);\n    }\n    function fetchServerAction(request, unwrap, singleFetch) {\n      if (!route.hasAction) {\n        throw noActionDefinedError(\"action\", route.id);\n      }\n      return fetchServerHandlerAndMaybeUnwrap(request, unwrap, singleFetch);\n    }\n    async function prefetchStylesAndCallHandler(handler) {\n      // Only prefetch links if we exist in the routeModulesCache (critical modules\n      // and navigating back to pages previously loaded via route.lazy).  Initial\n      // execution of route.lazy (when the module is not in the cache) will handle\n      // prefetching style links via loadRouteModuleWithBlockingLinks.\n      let cachedModule = routeModulesCache[route.id];\n      let linkPrefetchPromise = cachedModule ? prefetchStyleLinks(route, cachedModule) : Promise.resolve();\n      try {\n        return handler();\n      } finally {\n        await linkPrefetchPromise;\n      }\n    }\n    let dataRoute = {\n      id: route.id,\n      index: route.index,\n      path: route.path\n    };\n    if (routeModule) {\n      var _initialState$loaderD, _initialState$errors, _routeModule$clientLo;\n      // Use critical path modules directly\n      Object.assign(dataRoute, {\n        ...dataRoute,\n        ...getRouteComponents(route, routeModule, isSpaMode),\n        handle: routeModule.handle,\n        shouldRevalidate: getShouldRevalidateFunction(future, routeModule, route.id, needsRevalidation)\n      });\n      let initialData = initialState === null || initialState === void 0 ? void 0 : (_initialState$loaderD = initialState.loaderData) === null || _initialState$loaderD === void 0 ? void 0 : _initialState$loaderD[route.id];\n      let initialError = initialState === null || initialState === void 0 ? void 0 : (_initialState$errors = initialState.errors) === null || _initialState$errors === void 0 ? void 0 : _initialState$errors[route.id];\n      let isHydrationRequest = needsRevalidation == null && (((_routeModule$clientLo = routeModule.clientLoader) === null || _routeModule$clientLo === void 0 ? void 0 : _routeModule$clientLo.hydrate) === true || !route.hasLoader);\n      dataRoute.loader = async ({\n        request,\n        params\n      }, singleFetch) => {\n        try {\n          let result = await prefetchStylesAndCallHandler(async () => {\n            invariant(routeModule, \"No `routeModule` available for critical-route loader\");\n            if (!routeModule.clientLoader) {\n              if (isSpaMode) return null;\n              // Call the server when no client loader exists\n              return fetchServerLoader(request, false, singleFetch);\n            }\n            return routeModule.clientLoader({\n              request,\n              params,\n              async serverLoader() {\n                preventInvalidServerHandlerCall(\"loader\", route, isSpaMode);\n\n                // On the first call, resolve with the server result\n                if (isHydrationRequest) {\n                  if (initialData !== undefined) {\n                    return initialData;\n                  }\n                  if (initialError !== undefined) {\n                    throw initialError;\n                  }\n                  return null;\n                }\n\n                // Call the server loader for client-side navigations\n                return fetchServerLoader(request, true, singleFetch);\n              }\n            });\n          });\n          return result;\n        } finally {\n          // Whether or not the user calls `serverLoader`, we only let this\n          // stick around as true for one loader call\n          isHydrationRequest = false;\n        }\n      };\n\n      // Let React Router know whether to run this on hydration\n      dataRoute.loader.hydrate = shouldHydrateRouteLoader(route, routeModule, isSpaMode);\n      dataRoute.action = ({\n        request,\n        params\n      }, singleFetch) => {\n        return prefetchStylesAndCallHandler(async () => {\n          invariant(routeModule, \"No `routeModule` available for critical-route action\");\n          if (!routeModule.clientAction) {\n            if (isSpaMode) {\n              throw noActionDefinedError(\"clientAction\", route.id);\n            }\n            return fetchServerAction(request, false, singleFetch);\n          }\n          return routeModule.clientAction({\n            request,\n            params,\n            async serverAction() {\n              preventInvalidServerHandlerCall(\"action\", route, isSpaMode);\n              return fetchServerAction(request, true, singleFetch);\n            }\n          });\n        });\n      };\n    } else {\n      // If the lazy route does not have a client loader/action we want to call\n      // the server loader/action in parallel with the module load so we add\n      // loader/action as static props on the route\n      if (!route.hasClientLoader) {\n        dataRoute.loader = ({\n          request\n        }, singleFetch) => prefetchStylesAndCallHandler(() => {\n          if (isSpaMode) return Promise.resolve(null);\n          return fetchServerLoader(request, false, singleFetch);\n        });\n      }\n      if (!route.hasClientAction) {\n        dataRoute.action = ({\n          request\n        }, singleFetch) => prefetchStylesAndCallHandler(() => {\n          if (isSpaMode) {\n            throw noActionDefinedError(\"clientAction\", route.id);\n          }\n          return fetchServerAction(request, false, singleFetch);\n        });\n      }\n\n      // Load all other modules via route.lazy()\n      dataRoute.lazy = async () => {\n        let mod = await loadRouteModuleWithBlockingLinks(route, routeModulesCache);\n        let lazyRoute = {\n          ...mod\n        };\n        if (mod.clientLoader) {\n          let clientLoader = mod.clientLoader;\n          lazyRoute.loader = (args, singleFetch) => clientLoader({\n            ...args,\n            async serverLoader() {\n              preventInvalidServerHandlerCall(\"loader\", route, isSpaMode);\n              return fetchServerLoader(args.request, true, singleFetch);\n            }\n          });\n        }\n        if (mod.clientAction) {\n          let clientAction = mod.clientAction;\n          lazyRoute.action = (args, singleFetch) => clientAction({\n            ...args,\n            async serverAction() {\n              preventInvalidServerHandlerCall(\"action\", route, isSpaMode);\n              return fetchServerAction(args.request, true, singleFetch);\n            }\n          });\n        }\n        return {\n          ...(lazyRoute.loader ? {\n            loader: lazyRoute.loader\n          } : {}),\n          ...(lazyRoute.action ? {\n            action: lazyRoute.action\n          } : {}),\n          hasErrorBoundary: lazyRoute.hasErrorBoundary,\n          shouldRevalidate: getShouldRevalidateFunction(future, lazyRoute, route.id, needsRevalidation),\n          handle: lazyRoute.handle,\n          // No need to wrap these in layout since the root route is never\n          // loaded via route.lazy()\n          Component: lazyRoute.Component,\n          ErrorBoundary: lazyRoute.ErrorBoundary\n        };\n      };\n    }\n    let children = createClientRoutes(manifest, routeModulesCache, initialState, future, isSpaMode, route.id, routesByParentId, needsRevalidation);\n    if (children.length > 0) dataRoute.children = children;\n    return dataRoute;\n  });\n}\nfunction getShouldRevalidateFunction(future, route, routeId, needsRevalidation) {\n  // During HDR we force revalidation for updated routes\n  if (needsRevalidation) {\n    return wrapShouldRevalidateForHdr(routeId, route.shouldRevalidate, needsRevalidation);\n  }\n\n  // Single fetch revalidates by default, so override the RR default value which\n  // matches the multi-fetch behavior with `true`\n  if (future.v3_singleFetch && route.shouldRevalidate) {\n    let fn = route.shouldRevalidate;\n    return opts => fn({\n      ...opts,\n      defaultShouldRevalidate: true\n    });\n  }\n  return route.shouldRevalidate;\n}\n\n// When an HMR / HDR update happens we opt out of all user-defined\n// revalidation logic and force a revalidation on the first call\nfunction wrapShouldRevalidateForHdr(routeId, routeShouldRevalidate, needsRevalidation) {\n  let handledRevalidation = false;\n  return arg => {\n    if (!handledRevalidation) {\n      handledRevalidation = true;\n      return needsRevalidation.has(routeId);\n    }\n    return routeShouldRevalidate ? routeShouldRevalidate(arg) : arg.defaultShouldRevalidate;\n  };\n}\nasync function loadRouteModuleWithBlockingLinks(route, routeModules) {\n  let routeModule = await loadRouteModule(route, routeModules);\n  await prefetchStyleLinks(route, routeModule);\n\n  // Include all `browserSafeRouteExports` fields, except `HydrateFallback`\n  // since those aren't used on lazily loaded routes\n  return {\n    Component: getRouteModuleComponent(routeModule),\n    ErrorBoundary: routeModule.ErrorBoundary,\n    clientAction: routeModule.clientAction,\n    clientLoader: routeModule.clientLoader,\n    handle: routeModule.handle,\n    links: routeModule.links,\n    meta: routeModule.meta,\n    shouldRevalidate: routeModule.shouldRevalidate\n  };\n}\nasync function fetchServerHandler(request, route) {\n  let result = await fetchData(request, route.id);\n  if (result instanceof Error) {\n    throw result;\n  }\n  if (isRedirectResponse(result)) {\n    throw getRedirect(result);\n  }\n  if (isCatchResponse(result)) {\n    throw result;\n  }\n  if (isDeferredResponse(result) && result.body) {\n    return await parseDeferredReadableStream(result.body);\n  }\n  return result;\n}\nfunction unwrapServerResponse(result) {\n  if (isDeferredData(result)) {\n    return result.data;\n  }\n  if (isResponse(result)) {\n    let contentType = result.headers.get(\"Content-Type\");\n    // Check between word boundaries instead of startsWith() due to the last\n    // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n    if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n      return result.json();\n    } else {\n      return result.text();\n    }\n  }\n  return result;\n}\nfunction getRedirect(response) {\n  let status = parseInt(response.headers.get(\"X-Remix-Status\"), 10) || 302;\n  let url = response.headers.get(\"X-Remix-Redirect\");\n  let headers = {};\n  let revalidate = response.headers.get(\"X-Remix-Revalidate\");\n  if (revalidate) {\n    headers[\"X-Remix-Revalidate\"] = revalidate;\n  }\n  let reloadDocument = response.headers.get(\"X-Remix-Reload-Document\");\n  if (reloadDocument) {\n    headers[\"X-Remix-Reload-Document\"] = reloadDocument;\n  }\n  let replace = response.headers.get(\"X-Remix-Replace\");\n  if (replace) {\n    headers[\"X-Remix-Replace\"] = replace;\n  }\n  return redirect(url, {\n    status,\n    headers\n  });\n}\n\n// Our compiler generates the default export as `{}` when no default is provided,\n// which can lead us to trying to use that as a Component in RR and calling\n// createElement on it.  Patching here as a quick fix and hoping it's no longer\n// an issue in Vite.\nfunction getRouteModuleComponent(routeModule) {\n  if (routeModule.default == null) return undefined;\n  let isEmptyObject = typeof routeModule.default === \"object\" && Object.keys(routeModule.default).length === 0;\n  if (!isEmptyObject) {\n    return routeModule.default;\n  }\n}\nfunction shouldHydrateRouteLoader(route, routeModule, isSpaMode) {\n  return isSpaMode && route.id !== \"root\" || routeModule.clientLoader != null && (routeModule.clientLoader.hydrate === true || route.hasLoader !== true);\n}\n\nexport { createClientRoutes, createClientRoutesWithHMRRevalidationOptOut, createServerRoutes, shouldHydrateRouteLoader };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { isRouteErrorResponse } from 'react-router-dom';\nimport { useRemixContext, Scripts } from './components.js';\n\nclass RemixErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: props.error || null,\n      location: props.location\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application (even the HTML!) that will have no effect--the error page\n    // continues to display. This gives us a mechanism to recover from the error\n    // when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location) {\n      return {\n        error: props.error || null,\n        location: props.location\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error || state.error,\n      location: state.location\n    };\n  }\n  render() {\n    if (this.state.error) {\n      return /*#__PURE__*/React.createElement(RemixRootDefaultErrorBoundary, {\n        error: this.state.error,\n        isOutsideRemixApp: true\n      });\n    } else {\n      return this.props.children;\n    }\n  }\n}\n\n/**\n * When app's don't provide a root level ErrorBoundary, we default to this.\n */\nfunction RemixRootDefaultErrorBoundary({\n  error,\n  isOutsideRemixApp\n}) {\n  console.error(error);\n  let heyDeveloper = /*#__PURE__*/React.createElement(\"script\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        console.log(\n          \"💿 Hey developer 👋. You can provide a way better UX than this when your app throws errors. Check out https://remix.run/guides/errors for more information.\"\n        );\n      `\n    }\n  });\n  if (isRouteErrorResponse(error)) {\n    return /*#__PURE__*/React.createElement(BoundaryShell, {\n      title: \"Unhandled Thrown Response!\"\n    }, /*#__PURE__*/React.createElement(\"h1\", {\n      style: {\n        fontSize: \"24px\"\n      }\n    }, error.status, \" \", error.statusText), heyDeveloper);\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /*#__PURE__*/React.createElement(BoundaryShell, {\n    title: \"Application Error!\",\n    isOutsideRemixApp: isOutsideRemixApp\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    style: {\n      fontSize: \"24px\"\n    }\n  }, \"Application Error\"), /*#__PURE__*/React.createElement(\"pre\", {\n    style: {\n      padding: \"2rem\",\n      background: \"hsla(10, 50%, 50%, 0.1)\",\n      color: \"red\",\n      overflow: \"auto\"\n    }\n  }, errorInstance.stack), heyDeveloper);\n}\nfunction BoundaryShell({\n  title,\n  renderScripts,\n  isOutsideRemixApp,\n  children\n}) {\n  var _routeModules$root;\n  let {\n    routeModules\n  } = useRemixContext();\n\n  // Generally speaking, when the root route has a Layout we want to use that\n  // as the app shell instead of the default `BoundaryShell` wrapper markup below.\n  // This is true for `loader`/`action` errors, most render errors, and\n  // `HydrateFallback` scenarios.\n\n  // However, render errors thrown from the `Layout` present a bit of an issue\n  // because if the `Layout` itself throws during the `ErrorBoundary` pass and\n  // we bubble outside the `RouterProvider` to the wrapping `RemixErrorBoundary`,\n  // by returning only `children` here we'll be trying to append a `<div>` to\n  // the `document` and the DOM will throw, putting React into an error/hydration\n  // loop.\n\n  // Instead, if we're ever rendering from the outermost `RemixErrorBoundary`\n  // during hydration that wraps `RouterProvider`, then we can't trust the\n  // `Layout` and should fallback to the default app shell so we're always\n  // returning an `<html>` document.\n  if ((_routeModules$root = routeModules.root) !== null && _routeModules$root !== void 0 && _routeModules$root.Layout && !isOutsideRemixApp) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(\"html\", {\n    lang: \"en\"\n  }, /*#__PURE__*/React.createElement(\"head\", null, /*#__PURE__*/React.createElement(\"meta\", {\n    charSet: \"utf-8\"\n  }), /*#__PURE__*/React.createElement(\"meta\", {\n    name: \"viewport\",\n    content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n  }), /*#__PURE__*/React.createElement(\"title\", null, title)), /*#__PURE__*/React.createElement(\"body\", null, /*#__PURE__*/React.createElement(\"main\", {\n    style: {\n      fontFamily: \"system-ui, sans-serif\",\n      padding: \"2rem\"\n    }\n  }, children, renderScripts ? /*#__PURE__*/React.createElement(Scripts, null) : null)));\n}\n\nexport { BoundaryShell, RemixErrorBoundary, RemixRootDefaultErrorBoundary };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { BoundaryShell } from './errorBoundaries.js';\n\n// If the user sets `clientLoader.hydrate=true` somewhere but does not\n// provide a `HydrateFallback` at any level of the tree, then we need to at\n// least include `<Scripts>` in the SSR so we can hydrate the app and call the\n// `clientLoader` functions\nfunction RemixRootDefaultHydrateFallback() {\n  return /*#__PURE__*/React.createElement(BoundaryShell, {\n    title: \"Loading...\",\n    renderScripts: true\n  }, /*#__PURE__*/React.createElement(\"script\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n              console.log(\n                \"💿 Hey developer 👋. You can provide a way better UX than this \" +\n                \"when your app is loading JS modules and/or running \\`clientLoader\\` \" +\n                \"functions. Check out https://remix.run/route/hydrate-fallback \" +\n                \"for more information.\"\n              );\n            `\n    }\n  }));\n}\n\nexport { RemixRootDefaultHydrateFallback };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { UNSAFE_ErrorResponseImpl } from '@remix-run/router';\n\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in remix-server-runtime/errors.ts :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new UNSAFE_ErrorResponseImpl(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\nexport { deserializeErrors };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { extends as _extends } from './_virtual/_rollupPluginBabelHelpers.js';\nimport * as React from 'react';\nimport { useLocation, useMatches, UNSAFE_useScrollRestoration } from 'react-router-dom';\nimport { useRemixContext } from './components.js';\n\nlet STORAGE_KEY = \"positions\";\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n *\n * @see https://remix.run/components/scroll-restoration\n */\nfunction ScrollRestoration({\n  getKey,\n  ...props\n}) {\n  let {\n    isSpaMode\n  } = useRemixContext();\n  let location = useLocation();\n  let matches = useMatches();\n  UNSAFE_useScrollRestoration({\n    getKey,\n    storageKey: STORAGE_KEY\n  });\n\n  // In order to support `get<PERSON><PERSON>`, we need to compute a \"key\" here so we can\n  // hydrate that up so that SSR scroll restoration isn't waiting on <PERSON>act to\n  // hydrate. *However*, our key on the server is not the same as our key on\n  // the client!  So if the user's getKey implementation returns the SSR\n  // location key, then let's ignore it and let our inline <script> below pick\n  // up the client side history state key\n  let key = React.useMemo(() => {\n    if (!getKey) return null;\n    let userKey = getKey(location, matches);\n    return userKey !== location.key ? userKey : null;\n  },\n  // Nah, we only need this the first time for the SSR render\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n\n  // In SPA Mode, there's nothing to restore on initial render since we didn't\n  // render anything on the server\n  if (isSpaMode) {\n    return null;\n  }\n  let restoreScroll = ((STORAGE_KEY, restoreKey) => {\n    if (!window.history.state || !window.history.state.key) {\n      let key = Math.random().toString(32).slice(2);\n      window.history.replaceState({\n        key\n      }, \"\");\n    }\n    try {\n      let positions = JSON.parse(sessionStorage.getItem(STORAGE_KEY) || \"{}\");\n      let storedY = positions[restoreKey || window.history.state.key];\n      if (typeof storedY === \"number\") {\n        window.scrollTo(0, storedY);\n      }\n    } catch (error) {\n      console.error(error);\n      sessionStorage.removeItem(STORAGE_KEY);\n    }\n  }).toString();\n  return /*#__PURE__*/React.createElement(\"script\", _extends({}, props, {\n    suppressHydrationWarning: true,\n    dangerouslySetInnerHTML: {\n      __html: `(${restoreScroll})(${JSON.stringify(STORAGE_KEY)}, ${JSON.stringify(key)})`\n    }\n  }));\n}\n\nexport { ScrollRestoration };\n", "/**\n * @remix-run/react v2.15.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { createStaticRouter, StaticRouterProvider } from 'react-router-dom/server';\nimport { RemixContext } from './components.js';\nimport { RemixErrorBoundary } from './errorBoundaries.js';\nimport { createServerRoutes, shouldHydrateRouteLoader } from './routes.js';\nimport { StreamTransfer } from './single-fetch.js';\n\n/**\n * The entry point for a Remix app when it is rendered on the server (in\n * `app/entry.server.js`). This component is used to generate the HTML in the\n * response from the server.\n */\nfunction RemixServer({\n  context,\n  url,\n  abortDelay,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let {\n    manifest,\n    routeModules,\n    criticalCss,\n    serverHandoffString\n  } = context;\n  let routes = createServerRoutes(manifest.routes, routeModules, context.future, context.isSpaMode);\n\n  // Create a shallow clone of `loaderData` we can mutate for partial hydration.\n  // When a route exports a `clientLoader` and a `HydrateFallback`, we want to\n  // render the fallback on the server so we clear our the `loaderData` during SSR.\n  // Is it important not to change the `context` reference here since we use it\n  // for context._deepestRenderedBoundaryId tracking\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    // Clear out the loaderData to avoid rendering the route component when the\n    // route opted into clientLoader hydration and either:\n    // * gave us a HydrateFallback\n    // * or doesn't have a server loader and we have no data to render\n    if (route && shouldHydrateRouteLoader(manifestRoute, route, context.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      context.staticHandlerContext.loaderData[routeId] = undefined;\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext, {\n    future: {\n      v7_partialHydration: true,\n      v7_relativeSplatPath: context.future.v3_relativeSplatPath\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(RemixContext.Provider, {\n    value: {\n      manifest,\n      routeModules,\n      criticalCss,\n      serverHandoffString,\n      future: context.future,\n      isSpaMode: context.isSpaMode,\n      serializeError: context.serializeError,\n      abortDelay,\n      renderMeta: context.renderMeta\n    }\n  }, /*#__PURE__*/React.createElement(RemixErrorBoundary, {\n    location: router.state.location\n  }, /*#__PURE__*/React.createElement(StaticRouterProvider, {\n    router: router,\n    context: context.staticHandlerContext,\n    hydrate: false\n  }))), context.future.v3_singleFetch && context.serverHandoffStream ? /*#__PURE__*/React.createElement(React.Suspense, null, /*#__PURE__*/React.createElement(StreamTransfer, {\n    context: context,\n    identifier: 0,\n    reader: context.serverHandoffStream.getReader(),\n    textDecoder: new TextDecoder(),\n    nonce: nonce\n  })) : null);\n}\n\nexport { RemixServer };\n", "import * as React from 'react';\nimport { Action, UNSAFE_invariant, isRouteErrorResponse, createStaticHandler as createStaticHandler$1, UNSAFE_convertRoutesToDataRoutes, IDLE_NAVIGATION, IDLE_FETCHER, IDLE_BLOCKER } from '@remix-run/router';\nimport { UNSAFE_useRoutesImpl, UNSAFE_mapRouteProperties } from 'react-router';\nimport { parsePath, Router, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext, UNSAFE_FetchersContext, UNSAFE_ViewTransitionContext, createPath } from 'react-router-dom';\n\n/**\n * A `<Router>` that may not navigate to any other location. This is useful\n * on the server where there is no stateful UI.\n */\nfunction StaticRouter({\n  basename,\n  children,\n  location: locationProp = \"/\",\n  future\n}) {\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n  let action = Action.Pop;\n  let location = {\n    pathname: locationProp.pathname || \"/\",\n    search: locationProp.search || \"\",\n    hash: locationProp.hash || \"\",\n    state: locationProp.state != null ? locationProp.state : null,\n    key: locationProp.key || \"default\"\n  };\n  let staticNavigator = getStatelessNavigator();\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: location,\n    navigationType: action,\n    navigator: staticNavigator,\n    future: future,\n    static: true\n  });\n}\n/**\n * A Data Router that may not navigate to any other location. This is useful\n * on the server where there is no stateful UI.\n */\nfunction StaticRouterProvider({\n  context,\n  router,\n  hydrate = true,\n  nonce\n}) {\n  !(router && context) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"You must provide `router` and `context` to <StaticRouterProvider>\") : UNSAFE_invariant(false) : void 0;\n  let dataRouterContext = {\n    router,\n    navigator: getStatelessNavigator(),\n    static: true,\n    staticContext: context,\n    basename: context.basename || \"/\"\n  };\n  let fetchersContext = new Map();\n  let hydrateScript = \"\";\n  if (hydrate !== false) {\n    let data = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors)\n    };\n    // Use JSON.parse here instead of embedding a raw JS object here to speed\n    // up parsing on the client.  Dual-stringify is needed to ensure all quotes\n    // are properly escaped in the resulting string.  See:\n    //   https://v8.dev/blog/cost-of-javascript-2019#json\n    let json = htmlEscape(JSON.stringify(JSON.stringify(data)));\n    hydrateScript = `window.__staticRouterHydrationData = JSON.parse(${json});`;\n  }\n  let {\n    state\n  } = dataRouterContext.router;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(UNSAFE_DataRouterContext.Provider, {\n    value: dataRouterContext\n  }, /*#__PURE__*/React.createElement(UNSAFE_DataRouterStateContext.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(UNSAFE_FetchersContext.Provider, {\n    value: fetchersContext\n  }, /*#__PURE__*/React.createElement(UNSAFE_ViewTransitionContext.Provider, {\n    value: {\n      isTransitioning: false\n    }\n  }, /*#__PURE__*/React.createElement(Router, {\n    basename: dataRouterContext.basename,\n    location: state.location,\n    navigationType: state.historyAction,\n    navigator: dataRouterContext.navigator,\n    static: dataRouterContext.static,\n    future: {\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath\n    }\n  }, /*#__PURE__*/React.createElement(DataRoutes, {\n    routes: router.routes,\n    future: router.future,\n    state: state\n  })))))), hydrateScript ? /*#__PURE__*/React.createElement(\"script\", {\n    suppressHydrationWarning: true,\n    nonce: nonce,\n    dangerouslySetInnerHTML: {\n      __html: hydrateScript\n    }\n  }) : null);\n}\nfunction DataRoutes({\n  routes,\n  future,\n  state\n}) {\n  return UNSAFE_useRoutesImpl(routes, undefined, state, future);\n}\nfunction serializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // deserializeErrors in react-router-dom/index.tsx :)\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = {\n        ...val,\n        __type: \"RouteErrorResponse\"\n      };\n    } else if (val instanceof Error) {\n      // Do not serialize stack traces from SSR for security reasons\n      serialized[key] = {\n        message: val.message,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.\n        ...(val.name !== \"Error\" ? {\n          __subType: val.name\n        } : {})\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nfunction getStatelessNavigator() {\n  return {\n    createHref,\n    encodeLocation,\n    push(to) {\n      throw new Error(`You cannot use navigator.push() on the server because it is a stateless ` + `environment. This error was probably triggered when you did a ` + `\\`navigate(${JSON.stringify(to)})\\` somewhere in your app.`);\n    },\n    replace(to) {\n      throw new Error(`You cannot use navigator.replace() on the server because it is a stateless ` + `environment. This error was probably triggered when you did a ` + `\\`navigate(${JSON.stringify(to)}, { replace: true })\\` somewhere ` + `in your app.`);\n    },\n    go(delta) {\n      throw new Error(`You cannot use navigator.go() on the server because it is a stateless ` + `environment. This error was probably triggered when you did a ` + `\\`navigate(${delta})\\` somewhere in your app.`);\n    },\n    back() {\n      throw new Error(`You cannot use navigator.back() on the server because it is a stateless ` + `environment.`);\n    },\n    forward() {\n      throw new Error(`You cannot use navigator.forward() on the server because it is a stateless ` + `environment.`);\n    }\n  };\n}\nfunction createStaticHandler(routes, opts) {\n  return createStaticHandler$1(routes, {\n    ...opts,\n    mapRouteProperties: UNSAFE_mapRouteProperties\n  });\n}\nfunction createStaticRouter(routes, context, opts = {}) {\n  let manifest = {};\n  let dataRoutes = UNSAFE_convertRoutesToDataRoutes(routes, UNSAFE_mapRouteProperties, undefined, manifest);\n\n  // Because our context matches may be from a framework-agnostic set of\n  // routes passed to createStaticHandler(), we update them here with our\n  // newly created/enhanced data routes\n  let matches = context.matches.map(match => {\n    let route = manifest[match.route.id] || match.route;\n    return {\n      ...match,\n      route\n    };\n  });\n  let msg = method => `You cannot use router.${method}() on the server because it is a stateless environment`;\n  return {\n    get basename() {\n      return context.basename;\n    },\n    get future() {\n      return {\n        v7_fetcherPersist: false,\n        v7_normalizeFormMethod: false,\n        v7_partialHydration: opts.future?.v7_partialHydration === true,\n        v7_prependBasename: false,\n        v7_relativeSplatPath: opts.future?.v7_relativeSplatPath === true,\n        v7_skipActionErrorRevalidation: false\n      };\n    },\n    get state() {\n      return {\n        historyAction: Action.Pop,\n        location: context.location,\n        matches,\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: context.errors,\n        initialized: true,\n        navigation: IDLE_NAVIGATION,\n        restoreScrollPosition: null,\n        preventScrollReset: false,\n        revalidation: \"idle\",\n        fetchers: new Map(),\n        blockers: new Map()\n      };\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return undefined;\n    },\n    initialize() {\n      throw msg(\"initialize\");\n    },\n    subscribe() {\n      throw msg(\"subscribe\");\n    },\n    enableScrollRestoration() {\n      throw msg(\"enableScrollRestoration\");\n    },\n    navigate() {\n      throw msg(\"navigate\");\n    },\n    fetch() {\n      throw msg(\"fetch\");\n    },\n    revalidate() {\n      throw msg(\"revalidate\");\n    },\n    createHref,\n    encodeLocation,\n    getFetcher() {\n      return IDLE_FETCHER;\n    },\n    deleteFetcher() {\n      throw msg(\"deleteFetcher\");\n    },\n    dispose() {\n      throw msg(\"dispose\");\n    },\n    getBlocker() {\n      return IDLE_BLOCKER;\n    },\n    deleteBlocker() {\n      throw msg(\"deleteBlocker\");\n    },\n    patchRoutes() {\n      throw msg(\"patchRoutes\");\n    },\n    _internalFetchControllers: new Map(),\n    _internalActiveDeferreds: new Map(),\n    _internalSetRoutes() {\n      throw msg(\"_internalSetRoutes\");\n    }\n  };\n}\nfunction createHref(to) {\n  return typeof to === \"string\" ? to : createPath(to);\n}\nfunction encodeLocation(to) {\n  let href = typeof to === \"string\" ? to : createPath(to);\n  // Treating this as a full URL will strip any trailing spaces so we need to\n  // pre-encode them since they might be part of a matching splat param from\n  // an ancestor route\n  href = href.replace(/ $/, \"%20\");\n  let encoded = ABSOLUTE_URL_REGEX.test(href) ? new URL(href) : new URL(href, \"http://localhost\");\n  return {\n    pathname: encoded.pathname,\n    search: encoded.search,\n    hash: encoded.hash\n  };\n}\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n// This utility is based on https://github.com/zertosh/htmlescape\n// License: https://github.com/zertosh/htmlescape/blob/0527ca7156a524d256101bb310a9f970f63078ad/LICENSE\nconst ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nconst ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction htmlEscape(str) {\n  return str.replace(ESCAPE_REGEX, match => ESCAPE_LOOKUP[match]);\n}\n\nexport { StaticRouter, StaticRouterProvider, createStaticHandler, createStaticRouter };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFO,IAAMA,oBACLC,oBAA8C,IAAI;AAC1D,IAAAC,MAAa;AACXF,oBAAkBG,cAAc;AAClC;AAEO,IAAMC,yBAA+BH,oBAE1C,IAAI;AACN,IAAAC,MAAa;AACXE,yBAAuBD,cAAc;AACvC;AAEO,IAAME,eAAqBJ,oBAAqC,IAAI;AAC3E,IAAAC,MAAa;AACXG,eAAaF,cAAc;AAC7B;AAsCO,IAAMG,oBAA0BL,oBACrC,IACF;AAEA,IAAAC,MAAa;AACXI,oBAAkBH,cAAc;AAClC;AAOO,IAAMI,kBAAwBN,oBACnC,IACF;AAEA,IAAAC,MAAa;AACXK,kBAAgBJ,cAAc;AAChC;IAQaK,eAAqBP,oBAAkC;EAClEQ,QAAQ;EACRC,SAAS,CAAA;EACTC,aAAa;AACf,CAAC;AAED,IAAAT,MAAa;AACXM,eAAaL,cAAc;AAC7B;AAEO,IAAMS,oBAA0BX,oBAAmB,IAAI;AAE9D,IAAAC,MAAa;AACXU,oBAAkBT,cAAc;AAClC;ACtHO,SAASU,QACdC,IAAMC,OAEE;AAAA,MADR;IAAEC;EAA6C,IAACD,UAAA,SAAG,CAAA,IAAEA;AAErD,GACEE,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAEC;IAAUC,WAAAA;EAAU,IAAUC,iBAAWf,iBAAiB;AAChE,MAAI;IAAEgB;IAAMC;IAAUC;EAAO,IAAIC,gBAAgBX,IAAI;IAAEE;EAAS,CAAC;AAEjE,MAAIU,iBAAiBH;AAMrB,MAAIJ,aAAa,KAAK;AACpBO,qBACEH,aAAa,MAAMJ,WAAWQ,UAAU,CAACR,UAAUI,QAAQ,CAAC;EAChE;AAEA,SAAOH,WAAUQ,WAAW;IAAEL,UAAUG;IAAgBF;IAAQF;EAAK,CAAC;AACxE;AAOO,SAASL,qBAA8B;AAC5C,SAAaI,iBAAWd,eAAe,KAAK;AAC9C;AAYO,SAASsB,cAAwB;AACtC,GACEZ,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,SAAaG,iBAAWd,eAAe,EAAEuB;AAC3C;AAQO,SAASC,oBAAoC;AAClD,SAAaV,iBAAWd,eAAe,EAAEyB;AAC3C;AASO,SAASC,SAGdC,SAA+D;AAC/D,GACEjB,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAEK;MAAaM,YAAW;AAC9B,SAAaM,cACX,MAAMC,UAA0BF,SAASG,WAAWd,QAAQ,CAAC,GAC7D,CAACA,UAAUW,OAAO,CACpB;AACF;AAUA,IAAMI,wBACJ;AAIF,SAASC,0BACPC,IACA;AACA,MAAIC,WAAiBpB,iBAAWf,iBAAiB,EAAEoC;AACnD,MAAI,CAACD,UAAU;AAIbE,IAAMC,sBAAgBJ,EAAE;EAC1B;AACF;AAQO,SAASK,cAAgC;AAC9C,MAAI;IAAElC;EAAY,IAAUU,iBAAWb,YAAY;AAGnD,SAAOG,cAAcmC,kBAAiB,IAAKC,oBAAmB;AAChE;AAEA,SAASA,sBAAwC;AAC/C,GACE9B,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI8B,oBAA0B3B,iBAAWrB,iBAAiB;AAC1D,MAAI;IAAEmB;IAAU8B;IAAQ7B,WAAAA;EAAU,IAAUC,iBAAWf,iBAAiB;AACxE,MAAI;IAAEI;EAAQ,IAAUW,iBAAWb,YAAY;AAC/C,MAAI;IAAEe,UAAU2B;MAAqBrB,YAAW;AAEhD,MAAIsB,qBAAqBC,KAAKC,UAC5BC,oBAAoB5C,SAASuC,OAAOM,oBAAoB,CAC1D;AAEA,MAAIC,YAAkBC,aAAO,KAAK;AAClClB,4BAA0B,MAAM;AAC9BiB,cAAUE,UAAU;EACtB,CAAC;AAED,MAAIC,WAAmCC,kBACrC,SAAC9C,IAAiB+C,SAAkC;AAAA,QAAlCA,YAAwB,QAAA;AAAxBA,gBAA2B,CAAA;IAAE;AAC7C3D,WAAA4D,QAAQN,UAAUE,SAASpB,qBAAqB,IAAC;AAIjD,QAAI,CAACkB,UAAUE,QAAS;AAExB,QAAI,OAAO5C,OAAO,UAAU;AAC1BM,MAAAA,WAAU2C,GAAGjD,EAAE;AACf;IACF;AAEA,QAAIkD,OAAOC,UACTnD,IACAsC,KAAKc,MAAMf,kBAAkB,GAC7BD,kBACAW,QAAQ7C,aAAa,MACvB;AAQA,QAAIgC,qBAAqB,QAAQ7B,aAAa,KAAK;AACjD6C,WAAKzC,WACHyC,KAAKzC,aAAa,MACdJ,WACAQ,UAAU,CAACR,UAAU6C,KAAKzC,QAAQ,CAAC;IAC3C;AAEA,KAAC,CAAC,CAACsC,QAAQM,UAAU/C,WAAU+C,UAAU/C,WAAUgD,MACjDJ,MACAH,QAAQQ,OACRR,OACF;EACF,GACA,CACE1C,UACAC,YACA+B,oBACAD,kBACAF,iBAAiB,CAErB;AAEA,SAAOW;AACT;AAEA,IAAMW,gBAAsBrE,oBAAuB,IAAI;AAOhD,SAASsE,mBAA+C;AAC7D,SAAalD,iBAAWiD,aAAa;AACvC;AAQO,SAASE,UAAUC,SAA8C;AACtE,MAAIhE,SAAeY,iBAAWb,YAAY,EAAEC;AAC5C,MAAIA,QAAQ;AACV,WACEiE,oBAACJ,cAAcK,UAAQ;MAACC,OAAOH;IAAQ,GAAEhE,MAA+B;EAE5E;AACA,SAAOA;AACT;AAQO,SAASoE,YAId;AACA,MAAI;IAAEnE;EAAQ,IAAUW,iBAAWb,YAAY;AAC/C,MAAIsE,aAAapE,QAAQA,QAAQqE,SAAS,CAAC;AAC3C,SAAOD,aAAcA,WAAWE,SAAiB,CAAA;AACnD;AAOO,SAASvD,gBACdX,IAAMmE,QAEA;AAAA,MADN;IAAEjE;EAA6C,IAACiE,WAAA,SAAG,CAAA,IAAEA;AAErD,MAAI;IAAEhC;EAAO,IAAU5B,iBAAWf,iBAAiB;AACnD,MAAI;IAAEI;EAAQ,IAAUW,iBAAWb,YAAY;AAC/C,MAAI;IAAEe,UAAU2B;MAAqBrB,YAAW;AAChD,MAAIsB,qBAAqBC,KAAKC,UAC5BC,oBAAoB5C,SAASuC,OAAOM,oBAAoB,CAC1D;AAEA,SAAapB,cACX,MACE8B,UACEnD,IACAsC,KAAKc,MAAMf,kBAAkB,GAC7BD,kBACAlC,aAAa,MACf,GACF,CAACF,IAAIqC,oBAAoBD,kBAAkBlC,QAAQ,CACrD;AACF;AAUO,SAASkE,UACdC,QACAC,aAC2B;AAC3B,SAAOC,cAAcF,QAAQC,WAAW;AAC1C;AAGO,SAASC,cACdF,QACAC,aACAE,iBACArC,QAC2B;AAC3B,GACEhC,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAEE,WAAAA;IAAWsB,QAAQD;EAAS,IAAUpB,iBAAWf,iBAAiB;AACxE,MAAI;IAAEI,SAAS6E;EAAc,IAAUlE,iBAAWb,YAAY;AAC9D,MAAIsE,aAAaS,cAAcA,cAAcR,SAAS,CAAC;AACvD,MAAIS,eAAeV,aAAaA,WAAWE,SAAS,CAAA;AACpD,MAAIS,iBAAiBX,aAAaA,WAAWvD,WAAW;AACxD,MAAImE,qBAAqBZ,aAAaA,WAAWa,eAAe;AAChE,MAAIC,cAAcd,cAAcA,WAAWe;AAE3C,MAAA3F,MAAa;AAqBX,QAAI4F,aAAcF,eAAeA,YAAY5B,QAAS;AACtD+B,gBACEN,gBACA,CAACG,eAAeE,WAAWE,SAAS,GAAG,GACvC,oEAAA,MACMP,iBAAuCK,2BAAAA,aAAwB,kBAAA;;KAI1BA,2CAAAA,aAAU,oBAC1CA,YAAAA,eAAe,MAAM,MAASA,aAAU,QAAI,MACzD;EACF;AAEA,MAAIG,sBAAsBpE,YAAW;AAErC,MAAIC;AACJ,MAAIsD,aAAa;AAAA,QAAAc;AACf,QAAIC,oBACF,OAAOf,gBAAgB,WAAWgB,UAAUhB,WAAW,IAAIA;AAE7D,MACEM,uBAAuB,SAAGQ,wBACxBC,kBAAkB5E,aAAQ,OAAA,SAA1B2E,sBAA4BG,WAAWX,kBAAkB,MAACxF,OAF9DgB,UAAS,OAGP,8KACmF,iEAClBwE,qBAAkB,SAAI,mBACpES,kBAAkB5E,WAAQ,sCAAuC,IANtFL,UAAS,KAAA,IAAA;AASTY,eAAWqE;EACb,OAAO;AACLrE,eAAWmE;EACb;AAEA,MAAI1E,WAAWO,SAASP,YAAY;AAEpC,MAAI+E,oBAAoB/E;AACxB,MAAImE,uBAAuB,KAAK;AAe9B,QAAIa,iBAAiBb,mBAAmBvB,QAAQ,OAAO,EAAE,EAAEqC,MAAM,GAAG;AACpE,QAAIC,WAAWlF,SAAS4C,QAAQ,OAAO,EAAE,EAAEqC,MAAM,GAAG;AACpDF,wBAAoB,MAAMG,SAASC,MAAMH,eAAexB,MAAM,EAAE4B,KAAK,GAAG;EAC1E;AAEA,MAAIjG,UACF,CAAC+B,YACD6C,mBACAA,gBAAgB5E,WAChB4E,gBAAgB5E,QAAQqE,SAAS,IAC5BO,gBAAgB5E,UACjBkG,YAAYzB,QAAQ;IAAE5D,UAAU+E;EAAkB,CAAC;AAEzD,MAAApG,MAAa;AACXA,WAAA4D,QACE8B,eAAelF,WAAW,MAAI,iCACCoB,SAASP,WAAWO,SAASN,SAASM,SAASR,OAAI,IACpF,IAAC;AAEDpB,WAAA4D,QACEpD,WAAW,QACTA,QAAQA,QAAQqE,SAAS,CAAC,EAAEc,MAAMgB,YAAYC,UAC9CpG,QAAQA,QAAQqE,SAAS,CAAC,EAAEc,MAAMkB,cAAcD,UAChDpG,QAAQA,QAAQqE,SAAS,CAAC,EAAEc,MAAMmB,SAASF,QAC7C,qCAAmChF,SAASP,WAAWO,SAASN,SAASM,SAASR,OAAI,6IAGxF,IAAC;EACH;AAEA,MAAI2F,kBAAkBC,eACpBxG,WACEA,QAAQyG,IAAKC,WACXC,OAAOC,OAAO,CAAA,GAAIF,OAAO;IACvBpC,QAAQqC,OAAOC,OAAO,CAAA,GAAI9B,cAAc4B,MAAMpC,MAAM;IACpDzD,UAAUI,UAAU;MAClB+D;;MAEAtE,WAAUmG,iBACNnG,WAAUmG,eAAeH,MAAM7F,QAAQ,EAAEA,WACzC6F,MAAM7F;IAAQ,CACnB;IACDoE,cACEyB,MAAMzB,iBAAiB,MACnBD,qBACA/D,UAAU;MACR+D;;MAEAtE,WAAUmG,iBACNnG,WAAUmG,eAAeH,MAAMzB,YAAY,EAAEpE,WAC7C6F,MAAMzB;IAAY,CACvB;GACR,CACH,GACFJ,eACAD,iBACArC,MACF;AAKA,MAAImC,eAAe6B,iBAAiB;AAClC,WACEvC,oBAACnE,gBAAgBoE,UAAQ;MACvBC,OAAO;QACL9C,UAAQ0F,SAAA;UACNjG,UAAU;UACVC,QAAQ;UACRF,MAAM;UACN+C,OAAO;UACPoD,KAAK;QAAS,GACX3F,QAAQ;QAEbE,gBAAgB0F,OAAeC;MACjC;IAAE,GAEDV,eACuB;EAE9B;AAEA,SAAOA;AACT;AAEA,SAASW,wBAAwB;AAC/B,MAAIC,QAAQC,cAAa;AACzB,MAAIC,UAAUC,qBAAqBH,KAAK,IACjCA,MAAMI,SAAUJ,MAAAA,MAAMK,aACzBL,iBAAiBM,QACjBN,MAAME,UACN3E,KAAKC,UAAUwE,KAAK;AACxB,MAAIO,QAAQP,iBAAiBM,QAAQN,MAAMO,QAAQ;AACnD,MAAIC,YAAY;AAChB,MAAIC,YAAY;IAAEC,SAAS;IAAUC,iBAAiBH;;AACtD,MAAII,aAAa;IAAEF,SAAS;IAAWC,iBAAiBH;;AAExD,MAAIK,UAAU;AACd,MAAAxI,MAAa;AACXyI,YAAQd,MACN,wDACAA,KACF;AAEAa,cACEhE,oBAAAkE,gBACEjG,MAAA+B,oBAAA,KAAA,MAAG,qBAAsB,GACzBA,oBAAA,KAAA,MAAG,gGAEqBA,oBAAA,QAAA;MAAMmE,OAAOJ;OAAY,eAAmB,GAAI,OAAC,KACvE/D,oBAAA,QAAA;MAAMmE,OAAOJ;IAAW,GAAC,cAAkB,GAC1C,sBAAA,CACH;EAEN;AAEA,SACE/D,oBAAAkE,gBAAA,MACElE,oBAAI,MAAA,MAAA,+BAAiC,GACrCA,oBAAA,MAAA;IAAImE,OAAO;MAAEC,WAAW;IAAS;EAAE,GAAEf,OAAY,GAChDK,QAAQ1D,oBAAA,OAAA;IAAKmE,OAAOP;EAAU,GAAEF,KAAW,IAAI,MAC/CM,OACD;AAEN;AAEA,IAAMK,sBAAsBrE,oBAACkD,uBAAqB,IAAE;AAgB7C,IAAMoB,sBAAN,cAAwCjC,gBAG7C;EACAkC,YAAYC,OAAiC;AAC3C,UAAMA,KAAK;AACX,SAAK7E,QAAQ;MACXvC,UAAUoH,MAAMpH;MAChBqH,cAAcD,MAAMC;MACpBtB,OAAOqB,MAAMrB;;EAEjB;EAEA,OAAOuB,yBAAyBvB,OAAY;AAC1C,WAAO;MAAEA;;EACX;EAEA,OAAOwB,yBACLH,OACA7E,OACA;AASA,QACEA,MAAMvC,aAAaoH,MAAMpH,YACxBuC,MAAM8E,iBAAiB,UAAUD,MAAMC,iBAAiB,QACzD;AACA,aAAO;QACLtB,OAAOqB,MAAMrB;QACb/F,UAAUoH,MAAMpH;QAChBqH,cAAcD,MAAMC;;IAExB;AAMA,WAAO;MACLtB,OAAOqB,MAAMrB,UAAUf,SAAYoC,MAAMrB,QAAQxD,MAAMwD;MACvD/F,UAAUuC,MAAMvC;MAChBqH,cAAcD,MAAMC,gBAAgB9E,MAAM8E;;EAE9C;EAEAG,kBAAkBzB,OAAY0B,WAAgB;AAC5CZ,YAAQd,MACN,yDACAA,OACA0B,SACF;EACF;EAEAC,SAAS;AACP,WAAO,KAAKnF,MAAMwD,UAAUf,SAC1BpC,oBAAClE,aAAamE,UAAQ;MAACC,OAAO,KAAKsE,MAAMO;IAAa,GACpD/E,oBAAC9D,kBAAkB+D,UAAQ;MACzBC,OAAO,KAAKP,MAAMwD;MAClB6B,UAAU,KAAKR,MAAMS;IAAU,CAChC,CACoB,IAEvB,KAAKT,MAAMQ;EAEf;AACF;AAQA,SAASE,cAAaC,MAAwD;AAAA,MAAvD;IAAEJ;IAAcrC;IAAOsC;EAA6B,IAACG;AAC1E,MAAI7G,oBAA0B3B,iBAAWrB,iBAAiB;AAI1D,MACEgD,qBACAA,kBAAkBN,UAClBM,kBAAkB8G,kBACjB1C,MAAMvB,MAAMkE,gBAAgB3C,MAAMvB,MAAMmE,gBACzC;AACAhH,sBAAkB8G,cAAcG,6BAA6B7C,MAAMvB,MAAMqE;EAC3E;AAEA,SACExF,oBAAClE,aAAamE,UAAQ;IAACC,OAAO6E;EAAa,GACxCC,QACoB;AAE3B;AAEO,SAASxC,eACdxG,SACA6E,eACAD,iBACArC,QAC2B;AAAA,MAAAkH;AAAA,MAH3B5E,kBAA2B,QAAA;AAA3BA,oBAA8B,CAAA;EAAE;AAAA,MAChCD,oBAA4C,QAAA;AAA5CA,sBAA+C;EAAI;AAAA,MACnDrC,WAAoC,QAAA;AAApCA,aAAuC;EAAI;AAE3C,MAAIvC,WAAW,MAAM;AAAA,QAAA0J;AACnB,QAAI,CAAC9E,iBAAiB;AACpB,aAAO;IACT;AAEA,QAAIA,gBAAgB+E,QAAQ;AAG1B3J,gBAAU4E,gBAAgB5E;IAC5B,YACE0J,UAAAnH,WAAAmH,QAAAA,QAAQE,uBACR/E,cAAcR,WAAW,KACzB,CAACO,gBAAgBiF,eACjBjF,gBAAgB5E,QAAQqE,SAAS,GACjC;AAOArE,gBAAU4E,gBAAgB5E;IAC5B,OAAO;AACL,aAAO;IACT;EACF;AAEA,MAAIuG,kBAAkBvG;AAGtB,MAAI2J,UAAMF,mBAAG7E,oBAAA6E,OAAAA,SAAAA,iBAAiBE;AAC9B,MAAIA,UAAU,MAAM;AAClB,QAAIG,aAAavD,gBAAgBwD,UAC9BC,OAAMA,EAAE7E,MAAMqE,OAAMG,UAAM,OAAA,SAANA,OAASK,EAAE7E,MAAMqE,EAAE,OAAMpD,MAChD;AACA,MACE0D,cAAc,KAACtK,OADjBgB,UAAS,OAAA,8DAEqDmG,OAAOsD,KACjEN,MACF,EAAE1D,KAAK,GAAG,CAAC,IAJbzF,UAAS,KAAA,IAAA;AAMT+F,sBAAkBA,gBAAgBP,MAChC,GACAkE,KAAKC,IAAI5D,gBAAgBlC,QAAQyF,aAAa,CAAC,CACjD;EACF;AAIA,MAAIM,iBAAiB;AACrB,MAAIC,gBAAgB;AACpB,MAAIzF,mBAAmBrC,UAAUA,OAAOqH,qBAAqB;AAC3D,aAASU,IAAI,GAAGA,IAAI/D,gBAAgBlC,QAAQiG,KAAK;AAC/C,UAAI5D,QAAQH,gBAAgB+D,CAAC;AAE7B,UAAI5D,MAAMvB,MAAMoF,mBAAmB7D,MAAMvB,MAAMqF,wBAAwB;AACrEH,wBAAgBC;MAClB;AAEA,UAAI5D,MAAMvB,MAAMqE,IAAI;AAClB,YAAI;UAAEiB;UAAYd,QAAAA;QAAO,IAAI/E;AAC7B,YAAI8F,mBACFhE,MAAMvB,MAAMwF,UACZF,WAAW/D,MAAMvB,MAAMqE,EAAE,MAAMpD,WAC9B,CAACuD,WAAUA,QAAOjD,MAAMvB,MAAMqE,EAAE,MAAMpD;AACzC,YAAIM,MAAMvB,MAAMmB,QAAQoE,kBAAkB;AAIxCN,2BAAiB;AACjB,cAAIC,iBAAiB,GAAG;AACtB9D,8BAAkBA,gBAAgBP,MAAM,GAAGqE,gBAAgB,CAAC;UAC9D,OAAO;AACL9D,8BAAkB,CAACA,gBAAgB,CAAC,CAAC;UACvC;AACA;QACF;MACF;IACF;EACF;AAEA,SAAOA,gBAAgBqE,YAAY,CAAC7K,QAAQ2G,OAAOmE,UAAU;AAE3D,QAAI1D;AACJ,QAAI2D,8BAA8B;AAClC,QAAIzB,eAAuC;AAC3C,QAAImB,yBAAiD;AACrD,QAAI5F,iBAAiB;AACnBuC,cAAQwC,UAAUjD,MAAMvB,MAAMqE,KAAKG,OAAOjD,MAAMvB,MAAMqE,EAAE,IAAIpD;AAC5DiD,qBAAe3C,MAAMvB,MAAMkE,gBAAgBhB;AAE3C,UAAI+B,gBAAgB;AAClB,YAAIC,gBAAgB,KAAKQ,UAAU,GAAG;AACpCxF,sBACE,kBACA,OACA,0EACF;AACAyF,wCAA8B;AAC9BN,mCAAyB;QAC3B,WAAWH,kBAAkBQ,OAAO;AAClCC,wCAA8B;AAC9BN,mCAAyB9D,MAAMvB,MAAMqF,0BAA0B;QACjE;MACF;IACF;AAEA,QAAIxK,WAAU6E,cAAckG,OAAOxE,gBAAgBP,MAAM,GAAG6E,QAAQ,CAAC,CAAC;AACtE,QAAIG,cAAcA,MAAM;AACtB,UAAIhC;AACJ,UAAI7B,OAAO;AACT6B,mBAAWK;iBACFyB,6BAA6B;AACtC9B,mBAAWwB;MACb,WAAW9D,MAAMvB,MAAMkB,WAAW;AAOhC2C,mBAAWhF,oBAAC0C,MAAMvB,MAAMkB,WAAS,IAAE;MACrC,WAAWK,MAAMvB,MAAMgB,SAAS;AAC9B6C,mBAAWtC,MAAMvB,MAAMgB;MACzB,OAAO;AACL6C,mBAAWjJ;MACb;AACA,aACEiE,oBAACkF,eAAa;QACZxC;QACAqC,cAAc;UACZhJ;UACAC,SAAAA;UACAC,aAAa2E,mBAAmB;;QAElCoE;MAAmB,CACpB;;AAML,WAAOpE,oBACJ8B,MAAMvB,MAAMmE,iBAAiB5C,MAAMvB,MAAMkE,gBAAgBwB,UAAU,KACpE7G,oBAACsE,qBAAmB;MAClBlH,UAAUwD,gBAAgBxD;MAC1BqH,cAAc7D,gBAAgB6D;MAC9BQ,WAAWI;MACXlC;MACA6B,UAAUgC,YAAW;MACrBjC,cAAc;QAAEhJ,QAAQ;QAAMC,SAAAA;QAASC,aAAa;MAAK;IAAE,CAC5D,IAED+K,YAAW;KAEZ,IAAiC;AACtC;AAAC,IAEIC,iBAAc,SAAdA,iBAAc;AAAdA,EAAAA,gBAAc,YAAA,IAAA;AAAdA,EAAAA,gBAAc,gBAAA,IAAA;AAAdA,EAAAA,gBAAc,mBAAA,IAAA;AAAA,SAAdA;AAAc,EAAdA,kBAAc,CAAA,CAAA;AAAA,IAMdC,sBAAmB,SAAnBA,sBAAmB;AAAnBA,EAAAA,qBAAmB,YAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,oBAAA,IAAA;AAAnBA,EAAAA,qBAAmB,YAAA,IAAA;AAAnBA,EAAAA,qBAAmB,gBAAA,IAAA;AAAnBA,EAAAA,qBAAmB,mBAAA,IAAA;AAAnBA,EAAAA,qBAAmB,YAAA,IAAA;AAAA,SAAnBA;AAAmB,EAAnBA,uBAAmB,CAAA,CAAA;AAaxB,SAASC,0BACPC,UACA;AACA,SAAUA,WAAQ;AACpB;AAEA,SAASC,qBAAqBD,UAA0B;AACtD,MAAIE,MAAY3K,iBAAWrB,iBAAiB;AAC5C,GAAUgM,MAAG9L,OAAbgB,UAAS,OAAM2K,0BAA0BC,QAAQ,CAAC,IAAlD5K,UAAS,KAAA,IAAA;AACT,SAAO8K;AACT;AAEA,SAASC,mBAAmBH,UAA+B;AACzD,MAAIzH,QAAchD,iBAAWjB,sBAAsB;AACnD,GAAUiE,QAAKnE,OAAfgB,UAAS,OAAQ2K,0BAA0BC,QAAQ,CAAC,IAApD5K,UAAS,KAAA,IAAA;AACT,SAAOmD;AACT;AAEA,SAAS6H,gBAAgBJ,UAA+B;AACtD,MAAIjG,QAAcxE,iBAAWb,YAAY;AACzC,GAAUqF,QAAK3F,OAAfgB,UAAS,OAAQ2K,0BAA0BC,QAAQ,CAAC,IAApD5K,UAAS,KAAA,IAAA;AACT,SAAO2E;AACT;AAGA,SAASsG,kBAAkBL,UAA+B;AACxD,MAAIjG,QAAQqG,gBAAgBJ,QAAQ;AACpC,MAAIM,YAAYvG,MAAMnF,QAAQmF,MAAMnF,QAAQqE,SAAS,CAAC;AACtD,GACEqH,UAAUvG,MAAMqE,KAAEhK,OADpBgB,UAEK4K,OAAAA,WAAQ,wDAAA,IAFb5K,UAAS,KAAA,IAAA;AAIT,SAAOkL,UAAUvG,MAAMqE;AACzB;AAKO,SAASmC,aAAa;AAC3B,SAAOF,kBAAkBP,oBAAoBU,UAAU;AACzD;AAMO,SAASC,gBAAgB;AAC9B,MAAIlI,QAAQ4H,mBAAmBL,oBAAoBY,aAAa;AAChE,SAAOnI,MAAMoI;AACf;AAMO,SAASC,iBAAiB;AAC/B,MAAI1J,oBAAoB+I,qBAAqBJ,eAAegB,cAAc;AAC1E,MAAItI,QAAQ4H,mBAAmBL,oBAAoBe,cAAc;AACjE,SAAaxK,cACX,OAAO;IACLyK,YAAY5J,kBAAkB6J,OAAOD;IACrCvI,OAAOA,MAAM8E;EACf,IACA,CAACnG,kBAAkB6J,OAAOD,YAAYvI,MAAM8E,YAAY,CAC1D;AACF;AAMO,SAAS2D,aAAwB;AACtC,MAAI;IAAEpM;IAASyK;EAAW,IAAIc,mBAC5BL,oBAAoBmB,UACtB;AACA,SAAa5K,cACX,MAAMzB,QAAQyG,IAAKuD,OAAMsC,2BAA2BtC,GAAGS,UAAU,CAAC,GAClE,CAACzK,SAASyK,UAAU,CACtB;AACF;AAKO,SAAS8B,gBAAyB;AACvC,MAAI5I,QAAQ4H,mBAAmBL,oBAAoBsB,aAAa;AAChE,MAAIC,UAAUhB,kBAAkBP,oBAAoBsB,aAAa;AAEjE,MAAI7I,MAAMgG,UAAUhG,MAAMgG,OAAO8C,OAAO,KAAK,MAAM;AACjDxE,YAAQd,MACuDsF,6DAAAA,UAAO,GACtE;AACA,WAAOrG;EACT;AACA,SAAOzC,MAAM8G,WAAWgC,OAAO;AACjC;AAKO,SAASC,mBAAmBD,SAA0B;AAC3D,MAAI9I,QAAQ4H,mBAAmBL,oBAAoByB,kBAAkB;AACrE,SAAOhJ,MAAM8G,WAAWgC,OAAO;AACjC;AAKO,SAASG,gBAAyB;AACvC,MAAIjJ,QAAQ4H,mBAAmBL,oBAAoB2B,aAAa;AAChE,MAAIJ,UAAUhB,kBAAkBP,oBAAoBsB,aAAa;AACjE,SAAO7I,MAAMmJ,aAAanJ,MAAMmJ,WAAWL,OAAO,IAAIrG;AACxD;AAOO,SAASgB,gBAAyB;AAAA,MAAA2F;AACvC,MAAI5F,QAAcxG,iBAAWT,iBAAiB;AAC9C,MAAIyD,QAAQ4H,mBAAmBL,oBAAoB8B,aAAa;AAChE,MAAIP,UAAUhB,kBAAkBP,oBAAoB8B,aAAa;AAIjE,MAAI7F,UAAUf,QAAW;AACvB,WAAOe;EACT;AAGA,UAAA4F,gBAAOpJ,MAAMgG,WAANoD,OAAAA,SAAAA,cAAeN,OAAO;AAC/B;AAKO,SAASQ,gBAAyB;AACvC,MAAI/I,QAAcvD,iBAAWhB,YAAY;AACzC,SAAOuE,SAAK,OAAA,SAALA,MAAOgJ;AAChB;AAKO,SAASC,gBAAyB;AACvC,MAAIjJ,QAAcvD,iBAAWhB,YAAY;AACzC,SAAOuE,SAAK,OAAA,SAALA,MAAOkJ;AAChB;AAEA,IAAIC,YAAY;AAQT,SAASC,WAAWC,aAAiD;AAC1E,MAAI;IAAEpB,QAAAA;IAAQ1L;EAAS,IAAI4K,qBAAqBJ,eAAeuC,UAAU;AACzE,MAAI7J,QAAQ4H,mBAAmBL,oBAAoBsC,UAAU;AAE7D,MAAI,CAACC,YAAYC,aAAa,IAAUC,eAAS,EAAE;AACnD,MAAIC,kBAAwB1K,kBACzB2K,SAAQ;AACP,QAAI,OAAON,gBAAgB,YAAY;AACrC,aAAO,CAAC,CAACA;IACX;AACA,QAAI9M,aAAa,KAAK;AACpB,aAAO8M,YAAYM,GAAG;IACxB;AAKA,QAAI;MAAEC;MAAiBC;MAAcC;IAAc,IAAIH;AACvD,WAAON,YAAY;MACjBO,iBAAehH,SAAA,CAAA,GACVgH,iBAAe;QAClBjN,UACEoN,cAAcH,gBAAgBjN,UAAUJ,QAAQ,KAChDqN,gBAAgBjN;OACnB;MACDkN,cAAYjH,SAAA,CAAA,GACPiH,cAAY;QACflN,UACEoN,cAAcF,aAAalN,UAAUJ,QAAQ,KAC7CsN,aAAalN;OAChB;MACDmN;IACF,CAAC;EACH,GACA,CAACvN,UAAU8M,WAAW,CACxB;AAIAtL,EAAMiM,gBAAU,MAAM;AACpB,QAAInH,MAAMoH,OAAO,EAAEd,SAAS;AAC5BK,kBAAc3G,GAAG;AACjB,WAAO,MAAMoF,QAAOiC,cAAcrH,GAAG;EACvC,GAAG,CAACoF,OAAM,CAAC;AAMXlK,EAAMiM,gBAAU,MAAM;AACpB,QAAIT,eAAe,IAAI;AACrBtB,MAAAA,QAAOkC,WAAWZ,YAAYG,eAAe;IAC/C;KACC,CAACzB,SAAQsB,YAAYG,eAAe,CAAC;AAIxC,SAAOH,cAAc9J,MAAM2K,SAASC,IAAId,UAAU,IAC9C9J,MAAM2K,SAASE,IAAIf,UAAU,IAC7BgB;AACN;AAMA,SAASrM,oBAAsC;AAC7C,MAAI;IAAE+J,QAAAA;EAAO,IAAId,qBAAqBJ,eAAeyD,iBAAiB;AACtE,MAAIlF,KAAKiC,kBAAkBP,oBAAoBwD,iBAAiB;AAEhE,MAAI5L,YAAkBC,aAAO,KAAK;AAClClB,4BAA0B,MAAM;AAC9BiB,cAAUE,UAAU;EACtB,CAAC;AAED,MAAIC,WAAmCC,kBACrC,SAAC9C,IAAiB+C,SAAkC;AAAA,QAAlCA,YAAwB,QAAA;AAAxBA,gBAA2B,CAAA;IAAE;AAC7C3D,WAAA4D,QAAQN,UAAUE,SAASpB,qBAAqB,IAAC;AAIjD,QAAI,CAACkB,UAAUE,QAAS;AAExB,QAAI,OAAO5C,OAAO,UAAU;AAC1B+L,MAAAA,QAAOlJ,SAAS7C,EAAE;IACpB,OAAO;AACL+L,MAAAA,QAAOlJ,SAAS7C,IAAE0G,SAAA;QAAI6H,aAAanF;SAAOrG,OAAO,CAAE;IACrD;EACF,GACA,CAACgJ,SAAQ3C,EAAE,CACb;AAEA,SAAOvG;AACT;AAEA,IAAM2L,kBAAyC,CAAA;AAE/C,SAASvJ,YAAY0B,KAAa8H,MAAexH,SAAiB;AAChE,MAAI,CAACwH,QAAQ,CAACD,gBAAc7H,GAAG,GAAG;AAChC6H,oBAAc7H,GAAG,IAAI;AACrBvH,WAAA4D,QAAQ,OAAOiE,OAAO,IAAC;EACzB;AACF;ACrmCA,IAAMuH,gBAA4C,CAAA;AAE3C,SAASE,SAAS/H,KAAaM,SAAuB;AAC3D,MAAe,CAACuH,cAAcvH,OAAO,GAAG;AACtCuH,kBAAcvH,OAAO,IAAI;AACzBY,YAAQ8G,KAAK1H,OAAO;EACtB;AACF;AAEA,IAAM2H,iBAAiBA,CAACC,MAAcC,KAAaC,SACjDL,SACEG,MACA,0CAAwCC,MAAG,QAAA,sBACpBD,OAAsC,sCAAA,+BAC9BE,OAAI,IACrC;AAEK,SAASC,yBACdC,cACAC,cACA;AACA,OAAID,gBAAAA,OAAAA,SAAAA,aAAcE,wBAAuBnJ,QAAW;AAClD4I,mBACE,sBACA,mFACA,gEACF;EACF;AAEA,OACEK,gBAAY,OAAA,SAAZA,aAAcxM,0BAAyBuD,WACtC,CAACkJ,gBAAgB,CAACA,aAAazM,uBAChC;AACAmM,mBACE,wBACA,mEACA,kEACF;EACF;AAEA,MAAIM,cAAc;AAChB,QAAIA,aAAaE,sBAAsBpJ,QAAW;AAChD4I,qBACE,qBACA,0DACA,+DACF;IACF;AAEA,QAAIM,aAAaG,2BAA2BrJ,QAAW;AACrD4I,qBACE,0BACA,wEACA,oEACF;IACF;AAEA,QAAIM,aAAa1F,wBAAwBxD,QAAW;AAClD4I,qBACE,uBACA,yDACA,iEACF;IACF;AAEA,QAAIM,aAAaI,mCAAmCtJ,QAAW;AAC7D4I,qBACE,kCACA,gFACA,4EACF;IACF;EACF;AACF;ACWA,IAAMW,mBAAmB;AACzB,IAAMC,sBAAsB3N,MAAM0N,gBAAgB;AAsM3C,SAASE,SAAQC,OAKA;AAAA,MALC;IACvBC;IACAC,SAAAA;IACAC;IACAC;EACa,IAACJ;AACd,GACEK,mBAAkB,IAAEC,OADtBC;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAEC;IAAQC,QAAQC;EAAS,IAAUC,iBAAWC,iBAAiB;AAErEN,SAAAO,QACE,CAACH,UACD,uNAGF,IAAC;AAED,MAAI;IAAEI;EAAQ,IAAUH,iBAAWI,YAAY;AAC/C,MAAI;IAAEC,UAAUC;MAAqBC,YAAW;AAChD,MAAIC,WAAWC,YAAW;AAI1B,MAAIC,OAAOC,UACTrB,IACAsB,oBAAoBT,SAASN,OAAOgB,oBAAoB,GACxDP,kBACAb,aAAa,MACf;AACA,MAAIqB,WAAWC,KAAKC,UAAUN,IAAI;AAElCO,EAAMC,gBACJ,MAAMV,SAASO,KAAKI,MAAML,QAAQ,GAAG;IAAEvB,SAAAA;IAASC;IAAOC;EAAS,CAAC,GACjE,CAACe,UAAUM,UAAUrB,UAAUF,UAASC,KAAK,CAC/C;AAEA,SAAO;AACT;AAWO,SAAS4B,OAAOC,OAA+C;AACpE,SAAOC,UAAUD,MAAME,OAAO;AAChC;AAmDO,SAASC,MAAMC,QAA+C;AAE5D9B,SADPC,UAAS,OAEP,sIACoE,IAHtEA,UAAS,KAAA;AAKX;AAqBO,SAAS8B,OAAMC,OAQqB;AAAA,MARpB;IACrBC,UAAUC,eAAe;IACzBC,WAAW;IACXC,UAAUC;IACVC,iBAAiBC,OAAeC;IAChCC,WAAAA;IACAtC,QAAQuC,aAAa;IACrBxC;EACW,IAAC8B;AACZ,GACE,CAACjC,mBAAkB,IAAEC,OADvBC,UAEE,OAAA,wGACqD,IAHvDA,UAAS,KAAA,IAAA;AAQT,MAAIgC,WAAWC,aAAatC,QAAQ,QAAQ,GAAG;AAC/C,MAAI+C,oBAA0BC,cAC5B,OAAO;IACLX;IACAQ,WAAAA;IACAtC,QAAQuC;IACRxC,QAAM2C,SAAA;MACJ3B,sBAAsB;IAAK,GACxBhB,MAAM;MAGb,CAAC+B,UAAU/B,QAAQuC,YAAWC,UAAU,CAC1C;AAEA,MAAI,OAAOL,iBAAiB,UAAU;AACpCA,mBAAeS,UAAUT,YAAY;EACvC;AAEA,MAAI;IACF3B,WAAW;IACXqC,SAAS;IACTC,OAAO;IACPnD,QAAQ;IACRoD,MAAM;EACR,IAAIZ;AAEJ,MAAIa,kBAAwBN,cAAQ,MAAM;AACxC,QAAIO,mBAAmBC,cAAc1C,UAAUuB,QAAQ;AAEvD,QAAIkB,oBAAoB,MAAM;AAC5B,aAAO;IACT;AAEA,WAAO;MACLf,UAAU;QACR1B,UAAUyC;QACVJ;QACAC;QACAnD;QACAoD;;MAEFX;;EAEJ,GAAG,CAACL,UAAUvB,UAAUqC,QAAQC,MAAMnD,OAAOoD,KAAKX,cAAc,CAAC;AAEjEtC,SAAAO,QACE2C,mBAAmB,MACnB,uBAAqBjB,WAAQ,sCAAA,MACvBvB,WAAWqC,SAASC,OAA2C,2CAAA,kDAEvE,IAAC;AAED,MAAIE,mBAAmB,MAAM;AAC3B,WAAO;EACT;AAEA,SACEG,oBAAC/C,kBAAkBgD,UAAQ;IAACC,OAAOZ;EAAkB,GACnDU,oBAACG,gBAAgBF,UAAQ;IAACnB;IAAoBoB,OAAOL;EAAgB,CAAE,CAC7C;AAEhC;AAaO,SAASO,OAAMC,OAGqB;AAAA,MAHpB;IACrBvB;IACAC;EACW,IAACsB;AACZ,SAAOC,UAAUC,yBAAyBzB,QAAQ,GAAGC,QAAQ;AAC/D;AAgBO,SAASyB,MAAKC,OAAkD;AAAA,MAAjD;IAAE3B;IAAU4B;IAAcC;EAAoB,IAACF;AACnE,SACET,oBAACY,oBAAkB;IAACD;IAAkBD;KACpCV,oBAACa,cAAc/B,MAAAA,QAAuB,CACpB;AAExB;AAAC,IAWIgC,oBAAiB,SAAjBA,oBAAiB;AAAjBA,EAAAA,mBAAAA,mBAAiB,SAAA,IAAA,CAAA,IAAA;AAAjBA,EAAAA,mBAAAA,mBAAiB,SAAA,IAAA,CAAA,IAAA;AAAjBA,EAAAA,mBAAAA,mBAAiB,OAAA,IAAA,CAAA,IAAA;AAAA,SAAjBA;AAAiB,EAAjBA,qBAAiB,CAAA,CAAA;AAMtB,IAAMC,sBAAsB,IAAIC,QAAQ,MAAM;AAAA,CAAE;AAEhD,IAAMJ,qBAAN,cAAuCK,gBAGrC;EACAC,YAAY7C,OAAgC;AAC1C,UAAMA,KAAK;AACX,SAAK7B,QAAQ;MAAE2E,OAAO;;EACxB;EAEA,OAAOC,yBAAyBD,OAAY;AAC1C,WAAO;MAAEA;;EACX;EAEAE,kBAAkBF,OAAYG,WAAgB;AAC5CC,YAAQJ,MACN,oDACAA,OACAG,SACF;EACF;EAEAE,SAAS;AACP,QAAI;MAAE1C;MAAU4B;MAAcC;QAAY,KAAKtC;AAE/C,QAAIoD,UAAiC;AACrC,QAAIC,SAA4BZ,kBAAkBa;AAElD,QAAI,EAAEhB,mBAAmBK,UAAU;AAEjCU,eAASZ,kBAAkBc;AAC3BH,gBAAUT,QAAQL,QAAO;AACzBkB,aAAOC,eAAeL,SAAS,YAAY;QAAEM,KAAKA,MAAM;MAAK,CAAC;AAC9DF,aAAOC,eAAeL,SAAS,SAAS;QAAEM,KAAKA,MAAMpB;MAAQ,CAAC;IAChE,WAAW,KAAKnE,MAAM2E,OAAO;AAE3BO,eAASZ,kBAAkBK;AAC3B,UAAIa,cAAc,KAAKxF,MAAM2E;AAC7BM,gBAAUT,QAAQiB,OAAM,EAAGC,MAAM,MAAM;MAAA,CAAE;AACzCL,aAAOC,eAAeL,SAAS,YAAY;QAAEM,KAAKA,MAAM;MAAK,CAAC;AAC9DF,aAAOC,eAAeL,SAAS,UAAU;QAAEM,KAAKA,MAAMC;MAAY,CAAC;IACrE,WAAYrB,QAA2BwB,UAAU;AAE/CV,gBAAUd;AACVe,eACE,YAAYD,UACRX,kBAAkBK,QAClB,WAAWM,UACXX,kBAAkBc,UAClBd,kBAAkBa;IAC1B,OAAO;AAELD,eAASZ,kBAAkBa;AAC3BE,aAAOC,eAAenB,SAAS,YAAY;QAAEoB,KAAKA,MAAM;MAAK,CAAC;AAC9DN,gBAAUd,QAAQyB,KACfC,CAAAA,UACCR,OAAOC,eAAenB,SAAS,SAAS;QAAEoB,KAAKA,MAAMM;OAAM,GAC5DlB,WACCU,OAAOC,eAAenB,SAAS,UAAU;QAAEoB,KAAKA,MAAMZ;MAAM,CAAC,CACjE;IACF;AAEA,QACEO,WAAWZ,kBAAkBK,SAC7BM,QAAQa,kBAAkBC,sBAC1B;AAEA,YAAMxB;IACR;AAEA,QAAIW,WAAWZ,kBAAkBK,SAAS,CAACT,cAAc;AAEvD,YAAMe,QAAQa;IAChB;AAEA,QAAIZ,WAAWZ,kBAAkBK,OAAO;AAEtC,aAAOnB,oBAACwC,aAAavC,UAAQ;QAACC,OAAOuB;QAAS3C,UAAU4B;MAAa,CAAE;IACzE;AAEA,QAAIgB,WAAWZ,kBAAkBc,SAAS;AAExC,aAAO5B,oBAACwC,aAAavC,UAAQ;QAACC,OAAOuB;QAAS3C;MAAmB,CAAE;IACrE;AAGA,UAAM2C;EACR;AACF;AAMA,SAASZ,aAAY4B,OAIlB;AAAA,MAJmB;IACpB3D;EAGF,IAAC2D;AACC,MAAIJ,QAAOK,cAAa;AACxB,MAAIC,WAAW,OAAO7D,aAAa,aAAaA,SAASuD,KAAI,IAAIvD;AACjE,SAAOkB,oBAAA4C,gBAAGD,MAAAA,QAAW;AACvB;AAaO,SAASpC,yBACdzB,UACA+D,YACe;AAAA,MADfA,eAAoB,QAAA;AAApBA,iBAAuB,CAAA;EAAE;AAEzB,MAAIC,SAAwB,CAAA;AAE5B7E,EAAM8E,eAASC,QAAQlE,UAAU,CAACmE,SAASC,UAAU;AACnD,QAAI,CAAOC,qBAAeF,OAAO,GAAG;AAGlC;IACF;AAEA,QAAIG,WAAW,CAAC,GAAGP,YAAYK,KAAK;AAEpC,QAAID,QAAQI,SAAeT,gBAAU;AAEnCE,aAAOQ,KAAKC,MACVT,QACAvC,yBAAyB0C,QAAQ5E,MAAMS,UAAUsE,QAAQ,CAC3D;AACA;IACF;AAEA,MACEH,QAAQI,SAAS7E,SAAK7B,OADxBC,UAGI,OAAA,OAAA,OAAOqG,QAAQI,SAAS,WAAWJ,QAAQI,OAAOJ,QAAQI,KAAKG,QAAI,wGAAA,IAHvE5G,UAAS,KAAA,IAAA;AAOT,MACE,CAACqG,QAAQ5E,MAAM6E,SAAS,CAACD,QAAQ5E,MAAMS,YAAQnC,OADjDC,UAAS,OAEP,0CAA0C,IAF5CA,UAAS,KAAA,IAAA;AAKT,QAAI6G,QAAqB;MACvBC,IAAIT,QAAQ5E,MAAMqF,MAAMN,SAASO,KAAK,GAAG;MACzCC,eAAeX,QAAQ5E,MAAMuF;MAC7BX,SAASA,QAAQ5E,MAAM4E;MACvBhC,WAAWgC,QAAQ5E,MAAM4C;MACzBiC,OAAOD,QAAQ5E,MAAM6E;MACrBxF,MAAMuF,QAAQ5E,MAAMX;MACpBmG,QAAQZ,QAAQ5E,MAAMwF;MACtBC,QAAQb,QAAQ5E,MAAMyF;MACtBpD,cAAcuC,QAAQ5E,MAAMqC;MAC5BqD,eAAed,QAAQ5E,MAAM0F;MAC7BC,kBACEf,QAAQ5E,MAAM0F,iBAAiB,QAC/Bd,QAAQ5E,MAAMqC,gBAAgB;MAChCuD,kBAAkBhB,QAAQ5E,MAAM4F;MAChCC,QAAQjB,QAAQ5E,MAAM6F;MACtBC,MAAMlB,QAAQ5E,MAAM8F;;AAGtB,QAAIlB,QAAQ5E,MAAMS,UAAU;AAC1B2E,YAAM3E,WAAWyB,yBACf0C,QAAQ5E,MAAMS,UACdsE,QACF;IACF;AAEAN,WAAOQ,KAAKG,KAAK;EACnB,CAAC;AAED,SAAOX;AACT;AAKO,SAASsB,cACdjH,SAC2B;AAC3B,SAAOkH,eAAelH,OAAO;AAC/B;ACtfA,SAASmH,mBAAmBb,OAAoB;AAC9C,MAAIc,UAAgE;;;IAGlEP,kBAAkBP,MAAMM,iBAAiB,QAAQN,MAAM/C,gBAAgB;;AAGzE,MAAI+C,MAAMxC,WAAW;AACnB,QAAAtE,MAAa;AACX,UAAI8G,MAAMR,SAAS;AACjBtG,eAAAO,QACE,OACA,iGAEF,IAAC;MACH;IACF;AACA2E,WAAO2C,OAAOD,SAAS;MACrBtB,SAAejD,oBAAcyD,MAAMxC,SAAS;MAC5CA,WAAWwD;IACb,CAAC;EACH;AAEA,MAAIhB,MAAMiB,iBAAiB;AACzB,QAAA/H,MAAa;AACX,UAAI8G,MAAMkB,wBAAwB;AAChChI,eAAAO,QACE,OACA,4HAEF,IAAC;MACH;IACF;AACA2E,WAAO2C,OAAOD,SAAS;MACrBI,wBAA8B3E,oBAAcyD,MAAMiB,eAAe;MACjEA,iBAAiBD;IACnB,CAAC;EACH;AAEA,MAAIhB,MAAMM,eAAe;AACvB,QAAApH,MAAa;AACX,UAAI8G,MAAM/C,cAAc;AACtB/D,eAAAO,QACE,OACA,8GAEF,IAAC;MACH;IACF;AACA2E,WAAO2C,OAAOD,SAAS;MACrB7D,cAAoBV,oBAAcyD,MAAMM,aAAa;MACrDA,eAAeU;IACjB,CAAC;EACH;AAEA,SAAOF;AACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpSO,IAAMK,gBAAgC;AAC7C,IAAMC,iBAA8B;AAE9B,SAAUC,cAAcC,QAAW;AACvC,SAAOA,UAAU,QAAQ,OAAOA,OAAOC,YAAY;AACrD;AAEM,SAAUC,gBAAgBF,QAAW;AACzC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAEM,SAAUC,cAAcJ,QAAW;AACvC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAEM,SAAUE,eAAeL,QAAW;AACxC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAOA,SAASG,gBAAgBC,OAAwB;AAC/C,SAAO,CAAC,EAAEA,MAAMC,WAAWD,MAAME,UAAUF,MAAMG,WAAWH,MAAMI;AACpE;AAEgB,SAAAC,uBACdL,OACAM,QAAe;AAEf,SACEN,MAAMO,WAAW;GAChB,CAACD,UAAUA,WAAW;EACvB,CAACP,gBAAgBC,KAAK;AAE1B;AA+BgB,SAAAQ,mBACdC,MAA8B;AAAA,MAA9BA,SAAA,QAAA;AAAAA,WAA4B;EAAE;AAE9B,SAAO,IAAIC,gBACT,OAAOD,SAAS,YAChBE,MAAMC,QAAQH,IAAI,KAClBA,gBAAgBC,kBACZD,OACAI,OAAOC,KAAKL,IAAI,EAAEM,OAAO,CAACC,OAAMC,QAAO;AACrC,QAAIC,QAAQT,KAAKQ,GAAG;AACpB,WAAOD,MAAKG,OACVR,MAAMC,QAAQM,KAAK,IAAIA,MAAME,IAAKC,OAAM,CAACJ,KAAKI,CAAC,CAAC,IAAI,CAAC,CAACJ,KAAKC,KAAK,CAAC,CAAC;KAEnE,CAAA,CAAyB,CAAC;AAErC;AAEgB,SAAAI,2BACdC,gBACAC,qBAA2C;AAE3C,MAAIC,eAAejB,mBAAmBe,cAAc;AAEpD,MAAIC,qBAAqB;AAMvBA,wBAAoBE,QAAQ,CAACC,GAAGV,QAAO;AACrC,UAAI,CAACQ,aAAaG,IAAIX,GAAG,GAAG;AAC1BO,4BAAoBK,OAAOZ,GAAG,EAAES,QAASR,WAAS;AAChDO,uBAAaK,OAAOb,KAAKC,KAAK;QAChC,CAAC;MACF;IACH,CAAC;EACF;AAED,SAAOO;AACT;AAoBA,IAAIM,6BAA6C;AAEjD,SAASC,+BAA4B;AACnC,MAAID,+BAA+B,MAAM;AACvC,QAAI;AACF,UAAIE;QACFC,SAASC,cAAc,MAAM;;QAE7B;MAAC;AAEHJ,mCAA6B;aACtBK,GAAG;AACVL,mCAA6B;IAC9B;EACF;AACD,SAAOA;AACT;AAgFA,IAAMM,wBAA0C,oBAAIC,IAAI,CACtD,qCACA,uBACA,YAAY,CACb;AAED,SAASC,eAAeC,SAAsB;AAC5C,MAAIA,WAAW,QAAQ,CAACH,sBAAsBT,IAAIY,OAAsB,GAAG;AACzEC,WAAAC,QACE,OACA,MAAIF,UACsBjD,+DAAAA,0BAAAA,iBAAc,IAAG,IAC5C;AAED,WAAO;EACR;AACD,SAAOiD;AACT;AAEgB,SAAAG,sBACdrC,QACAsC,UAAgB;AAQhB,MAAIC;AACJ,MAAIC;AACJ,MAAIN;AACJ,MAAIO;AACJ,MAAIC;AAEJ,MAAInD,cAAcS,MAAM,GAAG;AAIzB,QAAI2C,OAAO3C,OAAO4C,aAAa,QAAQ;AACvCJ,aAASG,OAAOE,cAAcF,MAAML,QAAQ,IAAI;AAChDC,aAASvC,OAAO4C,aAAa,QAAQ,KAAK5D;AAC1CkD,cAAUD,eAAejC,OAAO4C,aAAa,SAAS,CAAC,KAAK3D;AAE5DwD,eAAW,IAAId,SAAS3B,MAAM;aAE9BX,gBAAgBW,MAAM,KACrBR,eAAeQ,MAAM,MACnBA,OAAO8C,SAAS,YAAY9C,OAAO8C,SAAS,UAC/C;AACA,QAAIC,OAAO/C,OAAO+C;AAElB,QAAIA,QAAQ,MAAM;AAChB,YAAM,IAAIC,MAAK,oEACuD;IAEvE;AAOD,QAAIL,OAAO3C,OAAO4C,aAAa,YAAY,KAAKG,KAAKH,aAAa,QAAQ;AAC1EJ,aAASG,OAAOE,cAAcF,MAAML,QAAQ,IAAI;AAEhDC,aACEvC,OAAO4C,aAAa,YAAY,KAChCG,KAAKH,aAAa,QAAQ,KAC1B5D;AACFkD,cACED,eAAejC,OAAO4C,aAAa,aAAa,CAAC,KACjDX,eAAec,KAAKH,aAAa,SAAS,CAAC,KAC3C3D;AAGFwD,eAAW,IAAId,SAASoB,MAAM/C,MAAM;AAMpC,QAAI,CAAC0B,6BAA4B,GAAI;AACnC,UAAI;QAAEuB;QAAMH;QAAMlC;MAAK,IAAKZ;AAC5B,UAAI8C,SAAS,SAAS;AACpB,YAAII,SAASD,OAAUA,OAAI,MAAM;AACjCR,iBAASjB,OAAU0B,SAAM,KAAK,GAAG;AACjCT,iBAASjB,OAAU0B,SAAM,KAAK,GAAG;iBACxBD,MAAM;AACfR,iBAASjB,OAAOyB,MAAMrC,KAAK;MAC5B;IACF;EACF,WAAU1B,cAAcc,MAAM,GAAG;AAChC,UAAM,IAAIgD,MACR,oFAC+B;EAElC,OAAM;AACLT,aAASvD;AACTwD,aAAS;AACTN,cAAUjD;AACVyD,WAAO1C;EACR;AAGD,MAAIyC,YAAYP,YAAY,cAAc;AACxCQ,WAAOD;AACPA,eAAWU;EACZ;AAED,SAAO;IAAEX;IAAQD,QAAQA,OAAOjD,YAAW;IAAI4C;IAASO;IAAUC;;AACpE;;;;AC/FA,IAAAU,uBAAA;AAEA,IAAI;AACFC,SAAOC,uBAAuBF;AAC/B,SAAQtB,GAAG;AACV;AAoIF,IAAMyB,wBAA8BC,qBAA2C;EAC7EC,iBAAiB;AAClB,CAAA;AACD,IAAAC,MAAa;AACXH,wBAAsBI,cAAc;AACrC;AAOKC,IAAAA,kBAAwBJ,qBAAqC,oBAAIK,IAAG,CAAE;AAC5E,IAAAH,MAAa;AACXE,kBAAgBD,cAAc;AAC/B;AA+BD,IAAMG,oBAAmB;AACzB,IAAMC,uBAAsBC,OAAMF,iBAAgB;AAClD,IAAMG,aAAa;AACnB,IAAMC,gBAAgBC,SAASF,UAAU;AACzC,IAAMG,SAAS;AACf,IAAMC,YAAYL,OAAMI,MAAM;AAE9B,SAASE,oBAAoBC,IAAc;AACzC,MAAIR,sBAAqB;AACvBA,IAAAA,qBAAoBQ,EAAE;EACvB,OAAM;AACLA,OAAE;EACH;AACH;AAEA,SAASC,cAAcD,IAAc;AACnC,MAAIL,eAAe;AACjBA,kBAAcK,EAAE;EACjB,OAAM;AACLA,OAAE;EACH;AACH;AASA,IAAME,WAAN,MAAc;EAOZC,cAAA;AANA,SAAMC,SAAwC;AAO5C,SAAKC,UAAU,IAAIC,QAAQ,CAACC,SAASC,WAAU;AAC7C,WAAKD,UAAWE,WAAS;AACvB,YAAI,KAAKL,WAAW,WAAW;AAC7B,eAAKA,SAAS;AACdG,kBAAQE,KAAK;QACd;;AAEH,WAAKD,SAAUE,YAAU;AACvB,YAAI,KAAKN,WAAW,WAAW;AAC7B,eAAKA,SAAS;AACdI,iBAAOE,MAAM;QACd;;IAEL,CAAC;EACH;AACD;AAKK,SAAUC,eAAcC,MAIR;AAAA,MAJS;IAC7BC;IACAC,QAAAA;IACAC;EACoB,IAAAH;AACpB,MAAI,CAACI,OAAOC,YAAY,IAAUC,gBAASJ,QAAOE,KAAK;AACvD,MAAI,CAACG,cAAcC,eAAe,IAAUF,gBAAQ;AACpD,MAAI,CAACG,WAAWC,YAAY,IAAUJ,gBAAsC;IAC1EhC,iBAAiB;EAClB,CAAA;AACD,MAAI,CAACqC,WAAWC,YAAY,IAAUN,gBAAQ;AAC9C,MAAI,CAACO,YAAYC,aAAa,IAAUR,gBAAQ;AAChD,MAAI,CAACS,cAAcC,eAAe,IAAUV,gBAAQ;AAKpD,MAAIW,cAAoBC,cAAyB,oBAAIxC,IAAG,CAAE;AAC1D,MAAI;IAAEyC;EAAkB,IAAKhB,UAAU,CAAA;AAEvC,MAAIiB,uBAA6BC,mBAC9BjC,QAAkB;AACjB,QAAI+B,oBAAoB;AACtBhC,0BAAoBC,EAAE;IACvB,OAAM;AACLA,SAAE;IACH;EACH,GACA,CAAC+B,kBAAkB,CAAC;AAGtB,MAAIG,WAAiBD,mBACnB,CACEE,UAAqBC,UAMnB;AAAA,QALF;MACEC;MACAC;MACAC;IACD,IAAAH;AAEDD,aAASK,SAASC,QAAQ,CAACC,SAASC,QAAO;AACzC,UAAID,QAAQE,SAASC,QAAW;AAC9BhB,oBAAYiB,QAAQC,IAAIJ,KAAKD,QAAQE,IAAI;MAC1C;IACH,CAAC;AACDP,oBAAgBI,QAASE,SAAQd,YAAYiB,QAAQE,OAAOL,GAAG,CAAC;AAEhE,QAAIM,8BACFnC,QAAOoC,UAAU,QACjBpC,QAAOoC,OAAOC,YAAY,QAC1B,OAAOrC,QAAOoC,OAAOC,SAASC,wBAAwB;AAIxD,QAAI,CAACb,sBAAsBU,6BAA6B;AACtD,UAAIX,WAAW;AACbrC,sBAAc,MAAMgB,aAAakB,QAAQ,CAAC;MAC3C,OAAM;AACLH,6BAAqB,MAAMf,aAAakB,QAAQ,CAAC;MAClD;AACD;IACD;AAGD,QAAIG,WAAW;AAEbrC,oBAAc,MAAK;AAEjB,YAAIwB,YAAY;AACdF,uBAAaA,UAAUhB,QAAO;AAC9BkB,qBAAW4B,eAAc;QAC1B;AACD/B,qBAAa;UACXpC,iBAAiB;UACjBoD,WAAW;UACXgB,iBAAiBf,mBAAmBe;UACpCC,cAAchB,mBAAmBgB;QAClC,CAAA;MACH,CAAC;AAGD,UAAIC,IAAI1C,QAAOoC,OAAQC,SAASC,oBAAoB,MAAK;AACvDnD,sBAAc,MAAMgB,aAAakB,QAAQ,CAAC;MAC5C,CAAC;AAGDqB,QAAEC,SAASC,QAAQ,MAAK;AACtBzD,sBAAc,MAAK;AACjBuB,uBAAaqB,MAAS;AACtBnB,wBAAcmB,MAAS;AACvBzB,0BAAgByB,MAAS;AACzBvB,uBAAa;YAAEpC,iBAAiB;UAAK,CAAE;QACzC,CAAC;MACH,CAAC;AAEDe,oBAAc,MAAMyB,cAAc8B,CAAC,CAAC;AACpC;IACD;AAGD,QAAI/B,YAAY;AAGdF,mBAAaA,UAAUhB,QAAO;AAC9BkB,iBAAW4B,eAAc;AACzBzB,sBAAgB;QACdZ,OAAOmB;QACPmB,iBAAiBf,mBAAmBe;QACpCC,cAAchB,mBAAmBgB;MAClC,CAAA;IACF,OAAM;AAELnC,sBAAgBe,QAAQ;AACxBb,mBAAa;QACXpC,iBAAiB;QACjBoD,WAAW;QACXgB,iBAAiBf,mBAAmBe;QACpCC,cAAchB,mBAAmBgB;MAClC,CAAA;IACF;EACH,GACA,CAACzC,QAAOoC,QAAQzB,YAAYF,WAAWM,aAAaG,oBAAoB,CAAC;AAK3EvC,EAAMkE,uBAAgB,MAAM7C,QAAO8C,UAAU1B,QAAQ,GAAG,CAACpB,SAAQoB,QAAQ,CAAC;AAI1EzC,EAAMoE,iBAAU,MAAK;AACnB,QAAIxC,UAAUnC,mBAAmB,CAACmC,UAAUiB,WAAW;AACrDd,mBAAa,IAAItB,SAAQ,CAAQ;IAClC;EACH,GAAG,CAACmB,SAAS,CAAC;AAKd5B,EAAMoE,iBAAU,MAAK;AACnB,QAAItC,aAAaJ,gBAAgBL,QAAOoC,QAAQ;AAC9C,UAAIf,WAAWhB;AACf,UAAI2C,gBAAgBvC,UAAUlB;AAC9B,UAAIoB,cAAaX,QAAOoC,OAAOC,SAASC,oBAAoB,YAAW;AACrEpB,6BAAqB,MAAMf,aAAakB,QAAQ,CAAC;AACjD,cAAM2B;MACR,CAAC;AACDrC,MAAAA,YAAWgC,SAASC,QAAQ,MAAK;AAC/BlC,qBAAaqB,MAAS;AACtBnB,sBAAcmB,MAAS;AACvBzB,wBAAgByB,MAAS;AACzBvB,qBAAa;UAAEpC,iBAAiB;QAAK,CAAE;MACzC,CAAC;AACDwC,oBAAcD,WAAU;IACzB;EACH,GAAG,CAACO,sBAAsBb,cAAcI,WAAWT,QAAOoC,MAAM,CAAC;AAIjEzD,EAAMoE,iBAAU,MAAK;AACnB,QACEtC,aACAJ,gBACAH,MAAM+C,SAASpB,QAAQxB,aAAa4C,SAASpB,KAC7C;AACApB,gBAAUhB,QAAO;IAClB;EACH,GAAG,CAACgB,WAAWE,YAAYT,MAAM+C,UAAU5C,YAAY,CAAC;AAIxD1B,EAAMoE,iBAAU,MAAK;AACnB,QAAI,CAACxC,UAAUnC,mBAAmByC,cAAc;AAC9CP,sBAAgBO,aAAaX,KAAK;AAClCM,mBAAa;QACXpC,iBAAiB;QACjBoD,WAAW;QACXgB,iBAAiB3B,aAAa2B;QAC9BC,cAAc5B,aAAa4B;MAC5B,CAAA;AACD3B,sBAAgBiB,MAAS;IAC1B;KACA,CAACxB,UAAUnC,iBAAiByC,YAAY,CAAC;AAE5ClC,EAAMoE,iBAAU,MAAK;AACnB1E,WAAA6E,QACEnD,mBAAmB,QAAQ,CAACC,QAAOC,OAAOkD,qBAC1C,8HACoE,IACrE;KAGA,CAAA,CAAE;AAEL,MAAIC,aAAkBC,eAAQ,MAAgB;AAC5C,WAAO;MACLC,YAAYtD,QAAOsD;MACnBC,gBAAgBvD,QAAOuD;MACvBC,IAAKC,OAAMzD,QAAO0D,SAASD,CAAC;MAC5BE,MAAMA,CAACC,IAAI1D,QAAO2D,SAChB7D,QAAO0D,SAASE,IAAI;QAClB1D,OAAAA;QACA4D,oBAAoBD,QAAAA,OAAAA,SAAAA,KAAMC;OAC3B;MACHC,SAASA,CAACH,IAAI1D,QAAO2D,SACnB7D,QAAO0D,SAASE,IAAI;QAClBG,SAAS;QACT7D,OAAAA;QACA4D,oBAAoBD,QAAAA,OAAAA,SAAAA,KAAMC;OAC3B;;EAEP,GAAG,CAAC9D,OAAM,CAAC;AAEX,MAAIgE,WAAWhE,QAAOgE,YAAY;AAElC,MAAIC,oBAA0BZ,eAC5B,OAAO;IACLrD,QAAAA;IACAoD,WAAAA;IACAc,QAAQ;IACRF;MAEF,CAAChE,SAAQoD,YAAWY,QAAQ,CAAC;AAG/B,MAAIG,eAAqBd,eACvB,OAAO;IACLe,sBAAsBpE,QAAOC,OAAOmE;MAEtC,CAACpE,QAAOC,OAAOmE,oBAAoB,CAAC;AAGtCzF,EAAMoE,iBACJ,MAAMsB,yBAAyBpE,QAAQD,QAAOC,MAAM,GACpD,CAACA,QAAQD,QAAOC,MAAM,CAAC;AASzB,SACEqE,qBAAAC,iBAAA,MACED,qBAACE,kBAAkBC,UAAS;IAAA9E,OAAOsE;KACjCK,qBAACI,uBAAuBD,UAAS;IAAA9E,OAAOO;KACrCoE,qBAAA/F,gBAAgBkG,UAAQ;IAAC9E,OAAOoB,YAAYiB;KAC3CsC,qBAACpG,sBAAsBuG,UAAS;IAAA9E,OAAOY;EAAS,GAC9C+D,qBAACK,QAAM;IACLX;IACAf,UAAU/C,MAAM+C;IAChB2B,gBAAgB1E,MAAM2E;IACtBzB,WAAWA;IACXnD,QAAQkE;EAEP,GAAAjE,MAAM4E,eAAe9E,QAAOC,OAAOkD,sBAClCmB,qBAACS,oBACC;IAAAC,QAAQhF,QAAOgF;IACf/E,QAAQD,QAAOC;IACfC;GAAY,IAGdH,eACD,CACM,CACsB,CACR,CACK,GAEnC,IAAI;AAGX;AAGA,IAAMgF,qBAA2BE,YAAKC,UAAU;AAEhD,SAASA,WAAUC,OAQlB;AAAA,MARmB;IAClBH;IACA/E;IACAC;EAKD,IAAAiF;AACC,SAAOC,cAAcJ,QAAQjD,QAAW7B,OAAOD,MAAM;AACvD;AAwHA,SAASoF,cAAaC,OAKD;AAAA,MALE;IACrBC;IACAC;IACAC;IACAC;EACmB,IAAAJ;AACnB,MAAI,CAACK,OAAOC,YAAY,IAAUC,gBAAS;IACzCC,QAAQJ,QAAQI;IAChBC,UAAUL,QAAQK;EACnB,CAAA;AACD,MAAI;IAAEC;EAAkB,IAAKP,UAAU,CAAA;AACvC,MAAIQ,WAAiBC,mBAClBC,cAA4D;AAC3DH,0BAAsBI,uBAClBA,qBAAoB,MAAMR,aAAaO,QAAQ,CAAC,IAChDP,aAAaO,QAAQ;EAC3B,GACA,CAACP,cAAcI,kBAAkB,CAAC;AAGpCK,EAAMC,uBAAgB,MAAMZ,QAAQa,OAAON,QAAQ,GAAG,CAACP,SAASO,QAAQ,CAAC;AAEzEI,EAAMG,iBAAU,MAAMC,yBAAyBhB,MAAM,GAAG,CAACA,MAAM,CAAC;AAEhE,SACEiB,qBAACC,QAAM;IACLpB;IACAC;IACAO,UAAUJ,MAAMI;IAChBa,gBAAgBjB,MAAMG;IACtBe,WAAWnB;IACXD;EAAc,CAAA;AAGpB;AAEA,IAAAqB,MAAa;AACXzB,gBAAc0B,cAAc;AAC7B;AAeD,IAAMC,YACJ,OAAOC,WAAW,eAClB,OAAOA,OAAOC,aAAa,eAC3B,OAAOD,OAAOC,SAASR,kBAAkB;AAE3C,IAAMS,qBAAqB;AAKdC,IAAAA,OAAaC,kBACxB,SAASC,YAAWC,OAalBC,KAAG;AAAA,MAZH;IACEC;IACAC;IACAC;IACAC,SAAAA;IACAjC;IACAkC;IACAC;IACAC;IACAC;EACO,IACRT,OADIU,OAAIC,8BAAAX,OAAAY,SAAA;AAIT,MAAI;IAAE5C;EAAQ,IAAW6C,kBAAWC,iBAAiB;AAGrD,MAAIC;AACJ,MAAIC,aAAa;AAEjB,MAAI,OAAOT,OAAO,YAAYX,mBAAmBqB,KAAKV,EAAE,GAAG;AAEzDQ,mBAAeR;AAGf,QAAId,WAAW;AACb,UAAI;AACF,YAAIyB,aAAa,IAAIC,IAAIzB,OAAOlB,SAAS4C,IAAI;AAC7C,YAAIC,YAAYd,GAAGe,WAAW,IAAI,IAC9B,IAAIH,IAAID,WAAWK,WAAWhB,EAAE,IAChC,IAAIY,IAAIZ,EAAE;AACd,YAAIiB,OAAOC,cAAcJ,UAAUK,UAAU1D,QAAQ;AAErD,YAAIqD,UAAUM,WAAWT,WAAWS,UAAUH,QAAQ,MAAM;AAE1DjB,eAAKiB,OAAOH,UAAUO,SAASP,UAAUQ;QAC1C,OAAM;AACLb,uBAAa;QACd;eACMc,GAAG;AAEVvC,eAAAwC,QACE,OACA,eAAaxB,KAAE,wGACsC,IACtD;MACF;IACF;EACF;AAGD,MAAIa,OAAOY,QAAQzB,IAAI;IAAEJ;EAAU,CAAA;AAEnC,MAAI8B,kBAAkBC,oBAAoB3B,IAAI;IAC5CF,SAAAA;IACAjC;IACAkC;IACAE;IACAL;IACAM;EACD,CAAA;AACD,WAAS0B,YACPC,OAAsD;AAEtD,QAAIlC,QAASA,SAAQkC,KAAK;AAC1B,QAAI,CAACA,MAAMC,kBAAkB;AAC3BJ,sBAAgBG,KAAK;IACtB;EACH;AAEA;;IAEEjD,qBAAA,KAAAmD,UAAA,CAAA,GACM5B,MAAI;MACRU,MAAML,gBAAgBK;MACtBlB,SAASc,cAAcZ,iBAAiBF,UAAUiC;MAClDlC;MACAK;KAAc,CAAA;;AAGpB,CAAC;AAGH,IAAAf,MAAa;AACXM,OAAKL,cAAc;AACpB;AAsBY+C,IAAAA,UAAgBzC,kBAC3B,SAAS0C,eAAcC,OAYrBxC,KAAG;AAAA,MAXH;IACE,gBAAgByC,kBAAkB;IAClCC,gBAAgB;IAChBC,WAAWC,gBAAgB;IAC3BC,MAAM;IACNC,OAAOC;IACPzC;IACAE;IACAxC;EAED,IAAAwE,OADI/B,OAAIC,8BAAA8B,OAAAQ,UAAA;AAIT,MAAIzB,OAAO0B,gBAAgB3C,IAAI;IAAEJ,UAAUO,KAAKP;EAAQ,CAAE;AAC1D,MAAI3B,WAAW2E,YAAW;AAC1B,MAAIC,cAAoBvC,kBAAWwC,sBAAsB;AACzD,MAAI;IAAE/D,WAAAA;IAAWtB;EAAU,IAAS6C,kBAAWC,iBAAiB;AAChE,MAAIwC,kBACFF,eAAe;;EAGfG,uBAAuB/B,IAAI,KAC3Bf,mBAAmB;AAErB,MAAI+C,aAAalE,WAAUmE,iBACvBnE,WAAUmE,eAAejC,IAAI,EAAEE,WAC/BF,KAAKE;AACT,MAAIgC,mBAAmBlF,SAASkD;AAChC,MAAIiC,uBACFP,eAAeA,YAAYQ,cAAcR,YAAYQ,WAAWpF,WAC5D4E,YAAYQ,WAAWpF,SAASkD,WAChC;AAEN,MAAI,CAACiB,eAAe;AAClBe,uBAAmBA,iBAAiBG,YAAW;AAC/CF,2BAAuBA,uBACnBA,qBAAqBE,YAAW,IAChC;AACJL,iBAAaA,WAAWK,YAAW;EACpC;AAED,MAAIF,wBAAwB3F,UAAU;AACpC2F,2BACElC,cAAckC,sBAAsB3F,QAAQ,KAAK2F;EACpD;AAOD,QAAMG,mBACJN,eAAe,OAAOA,WAAWO,SAAS,GAAG,IACzCP,WAAWQ,SAAS,IACpBR,WAAWQ;AACjB,MAAIC,WACFP,qBAAqBF,cACpB,CAACV,OACAY,iBAAiBpC,WAAWkC,UAAU,KACtCE,iBAAiBQ,OAAOJ,gBAAgB,MAAM;AAElD,MAAIK,YACFR,wBAAwB,SACvBA,yBAAyBH,cACvB,CAACV,OACAa,qBAAqBrC,WAAWkC,UAAU,KAC1CG,qBAAqBO,OAAOV,WAAWQ,MAAM,MAAM;AAEzD,MAAII,cAAc;IAChBH;IACAE;IACAb;;AAGF,MAAIe,cAAcJ,WAAWvB,kBAAkB4B;AAE/C,MAAI1B;AACJ,MAAI,OAAOC,kBAAkB,YAAY;AACvCD,gBAAYC,cAAcuB,WAAW;EACtC,OAAM;AAMLxB,gBAAY,CACVC,eACAoB,WAAW,WAAW,MACtBE,YAAY,YAAY,MACxBb,kBAAkB,kBAAkB,IAAI,EAEvCiB,OAAOC,OAAO,EACdC,KAAK,GAAG;EACZ;AAED,MAAI1B,QACF,OAAOC,cAAc,aAAaA,UAAUoB,WAAW,IAAIpB;AAE7D,SACElE,qBAACe,MAAIyC,UAAA,CAAA,GACC5B,MAAI;IACM,gBAAA2D;IACdzB;IACA3C;IACA8C;IACAxC;IACAE;GAEC,GAAA,OAAOxC,aAAa,aAAaA,SAASmG,WAAW,IAAInG,QAAQ;AAGxE,CAAC;AAGH,IAAAsB,MAAa;AACXgD,UAAQ/C,cAAc;AACvB;AAsGM,IAAMkF,OAAa5E,kBACxB,CAAA6E,OAeEC,iBACE;AAAA,MAfF;IACEC;IACAC;IACA1E;IACAC,SAAAA;IACAjC;IACA2G,SAASC;IACTzG;IACA0G;IACA9E;IACAK;IACAC;MAEDkE,OADIO,QAAKvE,8BAAAgE,OAAAQ,UAAA;AAIV,MAAIC,SAASC,UAAS;AACtB,MAAIC,aAAaC,cAAchH,QAAQ;IAAE4B;EAAU,CAAA;AACnD,MAAIqF,aACFT,OAAOlB,YAAW,MAAO,QAAQ,QAAQ;AAE3C,MAAI4B,gBAA0DrD,WAAS;AACrE6C,gBAAYA,SAAS7C,KAAK;AAC1B,QAAIA,MAAMC,iBAAkB;AAC5BD,UAAMsD,eAAc;AAEpB,QAAIC,YAAavD,MAAqCwD,YACnDD;AAEH,QAAIE,gBACDF,aAAAA,OAAAA,SAAAA,UAAWG,aAAa,YAAY,MACrCf;AAEFK,WAAOO,aAAavD,MAAM2D,eAAe;MACvClB;MACAE,QAAQc;MACRf;MACAzE,SAAAA;MACAjC;MACA+B;MACAK;MACAC;IACD,CAAA;;AAGH,SACEtB,qBAAA,QAAAmD,UAAA;IACErC,KAAK2E;IACLG,QAAQS;IACRjH,QAAQ+G;IACRL,UAAU7E,iBAAiB6E,WAAWQ;KAClCP,KAAK,CAAA;AAGf,CAAC;AAGH,IAAA3F,MAAa;AACXmF,OAAKlF,cAAc;AACpB;SAWewG,kBAAiBC,QAGR;AAAA,MAHS;IAChCC;IACAC;EACuB,IAAAF;AACvBG,uBAAqB;IAAEF;IAAQC;EAAU,CAAE;AAC3C,SAAO;AACT;AAEA,IAAA5G,MAAa;AACXyG,oBAAkBxG,cAAc;AACjC;AAOD,IAAK6G;CAAL,SAAKA,iBAAc;AACjBA,EAAAA,gBAAA,sBAAA,IAAA;AACAA,EAAAA,gBAAA,WAAA,IAAA;AACAA,EAAAA,gBAAA,kBAAA,IAAA;AACAA,EAAAA,gBAAA,YAAA,IAAA;AACAA,EAAAA,gBAAA,wBAAA,IAAA;AACF,GANKA,oBAAAA,kBAMJ,CAAA,EAAA;AAED,IAAKC;CAAL,SAAKA,sBAAmB;AACtBA,EAAAA,qBAAA,YAAA,IAAA;AACAA,EAAAA,qBAAA,aAAA,IAAA;AACAA,EAAAA,qBAAA,sBAAA,IAAA;AACF,GAJKA,yBAAAA,uBAIJ,CAAA,EAAA;AAID,SAASC,2BACPC,UAA8C;AAE9C,SAAUA,WAAQ;AACpB;AAEA,SAASC,sBAAqBD,UAAwB;AACpD,MAAIE,MAAY7F,kBAAW8F,iBAAiB;AAC5C,GAAUD,MAAGnH,OAAbqH,UAAS,OAAML,2BAA0BC,QAAQ,CAAC,IAAlDI,UAAS,KAAA,IAAA;AACT,SAAOF;AACT;AAEA,SAASG,oBAAmBL,UAA6B;AACvD,MAAIpI,QAAcyC,kBAAWwC,sBAAsB;AACnD,GAAUjF,QAAKmB,OAAfqH,UAAS,OAAQL,2BAA0BC,QAAQ,CAAC,IAApDI,UAAS,KAAA,IAAA;AACT,SAAOxI;AACT;AASM,SAAU8D,oBACd3B,IAAMuG,OAeA;AAAA,MAdN;IACExG;IACAD,SAAS0G;IACT3I;IACAoC;IACAL;IACAM;yBAQE,CAAA,IAAEqG;AAEN,MAAIhC,WAAWkC,YAAW;AAC1B,MAAIxI,WAAW2E,YAAW;AAC1B,MAAI3B,OAAO0B,gBAAgB3C,IAAI;IAAEJ;EAAU,CAAA;AAE3C,SAAaxB,mBACVyD,WAA0C;AACzC,QAAI6E,uBAAuB7E,OAAO9B,MAAM,GAAG;AACzC8B,YAAMsD,eAAc;AAIpB,UAAIrF,WACF0G,gBAAgBzC,SACZyC,cACAG,WAAW1I,QAAQ,MAAM0I,WAAW1F,IAAI;AAE9CsD,eAASvE,IAAI;QACXF,SAAAA;QACAjC;QACAoC;QACAL;QACAM;MACD,CAAA;IACF;KAEH,CACEjC,UACAsG,UACAtD,MACAuF,aACA3I,OACAkC,QACAC,IACAC,oBACAL,UACAM,cAAc,CACf;AAEL;AAMM,SAAU0G,gBACdC,aAAiC;AAEjC7H,SAAAwC,QACE,OAAOsF,oBAAoB,aAC3B,yOAG+C,IAChD;AAED,MAAIC,yBAA+BC,cAAOC,mBAAmBJ,WAAW,CAAC;AACzE,MAAIK,wBAA8BF,cAAO,KAAK;AAE9C,MAAI/I,WAAW2E,YAAW;AAC1B,MAAIuE,eAAqBC,eACvB;;;;IAIEC,2BACEpJ,SAASoD,QACT6F,sBAAsBI,UAAU,OAAOP,uBAAuBO,OAAO;KAEzE,CAACrJ,SAASoD,MAAM,CAAC;AAGnB,MAAIkD,WAAWkC,YAAW;AAC1B,MAAIc,kBAAwBnJ,mBAC1B,CAACoJ,UAAUC,oBAAmB;AAC5B,UAAMC,kBAAkBT,mBACtB,OAAOO,aAAa,aAAaA,SAASL,YAAY,IAAIK,QAAQ;AAEpEN,0BAAsBI,UAAU;AAChC/C,aAAS,MAAMmD,iBAAiBD,eAAe;EACjD,GACA,CAAClD,UAAU4C,YAAY,CAAC;AAG1B,SAAO,CAACA,cAAcI,eAAe;AACvC;AA2CA,SAASI,+BAA4B;AACnC,MAAI,OAAOvI,aAAa,aAAa;AACnC,UAAM,IAAIwI,MACR,+GACgE;EAEnE;AACH;AAEA,IAAIC,YAAY;AAChB,IAAIC,qBAAqBA,MAAA,OAAWC,OAAO,EAAEF,SAAS,IAAK;SAM3C/C,YAAS;AACvB,MAAI;IAAEkD,QAAAA;EAAM,IAAK9B,sBAAqBJ,gBAAemC,SAAS;AAC9D,MAAI;IAAExK;EAAQ,IAAW6C,kBAAWC,iBAAiB;AACrD,MAAI2H,iBAAiBC,WAAU;AAE/B,SAAa/J,mBACX,SAAC2B,QAAQqI,SAAgB;AAAA,QAAhBA,YAAO,QAAA;AAAPA,gBAAU,CAAA;IAAE;AACnBT,iCAA4B;AAE5B,QAAI;MAAE3J;MAAQwG;MAAQ6D;MAASC;MAAUC;IAAI,IAAKC,sBAChDzI,QACAtC,QAAQ;AAGV,QAAI2K,QAAQ7D,aAAa,OAAO;AAC9B,UAAIkE,MAAML,QAAQ9D,cAAcwD,mBAAkB;AAClDE,MAAAA,QAAOU,MAAMD,KAAKP,gBAAgBE,QAAQpK,UAAUA,QAAQ;QAC1DiC,oBAAoBmI,QAAQnI;QAC5BqI;QACAC;QACAtD,YAAYmD,QAAQ5D,UAAWA;QAC/BmE,aAAaP,QAAQC,WAAYA;QACjCO,WAAWR,QAAQQ;MACpB,CAAA;IACF,OAAM;AACLZ,MAAAA,QAAOzD,SAAS6D,QAAQpK,UAAUA,QAAQ;QACxCiC,oBAAoBmI,QAAQnI;QAC5BqI;QACAC;QACAtD,YAAYmD,QAAQ5D,UAAWA;QAC/BmE,aAAaP,QAAQC,WAAYA;QACjCvI,SAASsI,QAAQtI;QACjBjC,OAAOuK,QAAQvK;QACfgL,aAAaX;QACbU,WAAWR,QAAQQ;QACnB1I,gBAAgBkI,QAAQlI;MACzB,CAAA;IACF;KAEH,CAAC8H,SAAQvK,UAAUyK,cAAc,CAAC;AAEtC;AAIM,SAAUlD,cACdhH,QAAe8K,QACsC;AAAA,MAArD;IAAElJ;0BAAiD,CAAA,IAAEkJ;AAErD,MAAI;IAAErL;EAAQ,IAAW6C,kBAAWC,iBAAiB;AACrD,MAAIwI,eAAqBzI,kBAAW0I,YAAY;AAChD,GAAUD,eAAY/J,OAAtBqH,UAAS,OAAe,kDAAkD,IAA1EA,UAAS,KAAA,IAAA;AAET,MAAI,CAAC4C,KAAK,IAAIF,aAAaG,QAAQC,MAAM,EAAE;AAG3C,MAAIlI,OAAIc,UAAQY,CAAAA,GAAAA,gBAAgB3E,SAASA,SAAS,KAAK;IAAE4B;EAAQ,CAAE,CAAC;AAKpE,MAAI3B,WAAW2E,YAAW;AAC1B,MAAI5E,UAAU,MAAM;AAGlBiD,SAAKI,SAASpD,SAASoD;AAKvB,QAAI+H,SAAS,IAAItC,gBAAgB7F,KAAKI,MAAM;AAC5C,QAAIgI,cAAcD,OAAOE,OAAO,OAAO;AACvC,QAAIC,qBAAqBF,YAAYG,KAAMC,OAAMA,MAAM,EAAE;AACzD,QAAIF,oBAAoB;AACtBH,aAAOM,OAAO,OAAO;AACrBL,kBAAYrF,OAAQyF,OAAMA,CAAC,EAAEE,QAASF,OAAML,OAAOQ,OAAO,SAASH,CAAC,CAAC;AACrE,UAAII,KAAKT,OAAOU,SAAQ;AACxB7I,WAAKI,SAASwI,KAASA,MAAAA,KAAO;IAC/B;EACF;AAED,OAAK,CAAC7L,UAAUA,WAAW,QAAQiL,MAAMc,MAAMC,OAAO;AACpD/I,SAAKI,SAASJ,KAAKI,SACfJ,KAAKI,OAAOvB,QAAQ,OAAO,SAAS,IACpC;EACL;AAMD,MAAIrC,aAAa,KAAK;AACpBwD,SAAKE,WACHF,KAAKE,aAAa,MAAM1D,WAAWwM,UAAU,CAACxM,UAAUwD,KAAKE,QAAQ,CAAC;EACzE;AAED,SAAOwF,WAAW1F,IAAI;AACxB;SAgBgBiJ,WAAUC,QAEF;AAAA,MAAAC;AAAA,MAFgB;IACtC3B;0BACoB,CAAA,IAAE0B;AACtB,MAAI;IAAEnC,QAAAA;EAAM,IAAK9B,sBAAqBJ,gBAAeuE,UAAU;AAC/D,MAAIxM,QAAQyI,oBAAmBP,qBAAoBsE,UAAU;AAC7D,MAAIC,cAAoBhK,kBAAWiK,eAAe;AAClD,MAAIR,QAAczJ,kBAAW0I,YAAY;AACzC,MAAIwB,WAAOJ,iBAAGL,MAAMb,QAAQa,MAAMb,QAAQzF,SAAS,CAAC,MAAC,OAAA,SAAvC2G,eAAyCL,MAAMU;AAE7D,GAAUH,cAAWtL,OAArBqH,UAAS,OAAA,kDAAA,IAATA,UAAS,KAAA,IAAA;AACT,GAAU0D,QAAK/K,OAAfqH,UAAS,OAAA,+CAAA,IAATA,UAAS,KAAA,IAAA;AACT,IACEmE,WAAW,QAAIxL,OADjBqH,UAAS,OAAA,kEAAA,IAATA,UAAS,KAAA,IAAA;AAQT,MAAIqE,aAAaC,YAAYA,UAAS,IAAK;AAC3C,MAAI,CAACrG,YAAYsG,aAAa,IAAU7M,gBAAiB0K,OAAOiC,UAAU;AAC1E,MAAIjC,OAAOA,QAAQnE,YAAY;AAC7BsG,kBAAcnC,GAAG;EAClB,WAAU,CAACnE,YAAY;AAEtBsG,kBAAc9C,mBAAkB,CAAE;EACnC;AAGDvJ,EAAMG,iBAAU,MAAK;AACnBsJ,IAAAA,QAAO6C,WAAWvG,UAAU;AAC5B,WAAO,MAAK;AAIV0D,MAAAA,QAAO8C,cAAcxG,UAAU;;EAEnC,GAAG,CAAC0D,SAAQ1D,UAAU,CAAC;AAGvB,MAAIyG,OAAa3M,mBACf,CAACyC,MAAcmK,SAAkC;AAC/C,KAAUR,UAAOxL,OAAjBqH,UAAS,OAAU,yCAAyC,IAA5DA,UAAS,KAAA,IAAA;AACT2B,IAAAA,QAAOU,MAAMpE,YAAYkG,SAAS3J,MAAMmK,IAAI;KAE9C,CAAC1G,YAAYkG,SAASxC,OAAM,CAAC;AAG/B,MAAIiD,aAAanG,UAAS;AAC1B,MAAID,SAAezG,mBACjB,CAAC2B,QAAQiL,SAAQ;AACfC,eAAWlL,QAAMgC,UAAA,CAAA,GACZiJ,MAAI;MACPzG,UAAU;MACVD;IAAU,CAAA,CACX;EACH,GACA,CAACA,YAAY2G,UAAU,CAAC;AAG1B,MAAIC,cAAoB9D,eAAQ,MAAK;AACnC,QAAI8D,eAAoB3L,kBACtB,CAACoF,OAAOjF,QAAO;AACb,aACGd,qBAAAuF,MAAIpC,UAAA,CAAA,GAAK4C,OAAK;QAAEJ,UAAU;QAAOD;QAAwB5E;MAAQ,CAAA,CAAA;IAEtE,CAAC;AAEH,QAAAV,MAAa;AACXkM,MAAAA,aAAYjM,cAAc;IAC3B;AACD,WAAOiM;EACT,GAAG,CAAC5G,UAAU,CAAC;AAGf,MAAI6G,UAAUtN,MAAMuN,SAASC,IAAI/G,UAAU,KAAKgH;AAChD,MAAIC,QAAOjB,YAAYe,IAAI/G,UAAU;AACrC,MAAIkH,wBAA8BpE,eAChC,MAAArF,UAAA;IACEoC,MAAM+G;IACNrG;IACAkG;EAAI,GACDI,SAAO;IACVI,MAAAA;EAAI,CAAA,GAEN,CAACL,aAAarG,QAAQkG,MAAMI,SAASI,KAAI,CAAC;AAG5C,SAAOC;AACT;SAMgBC,cAAW;AACzB,MAAI5N,QAAQyI,oBAAmBP,qBAAoB2F,WAAW;AAC9D,SAAOC,MAAMC,KAAK/N,MAAMuN,SAASS,QAAO,CAAE,EAAEC,IAAIC,YAAA;AAAA,QAAC,CAACtD,KAAK0C,OAAO,IAACY;AAAA,WAAAhK,UAAA,CAAA,GAC1DoJ,SAAO;MACV1C;IAAG,CAAA;EAAA,CACH;AACJ;AAEA,IAAMuD,iCAAiC;AACvC,IAAIC,uBAA+C,CAAA;AAKnD,SAASpG,qBAAoBqG,QAMvB;AAAA,MANwB;IAC5BvG;IACAC;0BAIE,CAAA,IAAEsG;AACJ,MAAI;IAAElE,QAAAA;EAAM,IAAK9B,sBAAqBJ,gBAAeqG,oBAAoB;AACzE,MAAI;IAAEC;IAAuBnM;EAAoB,IAAGqG,oBAClDP,qBAAoBoG,oBAAoB;AAE1C,MAAI;IAAE1O;EAAQ,IAAW6C,kBAAWC,iBAAiB;AACrD,MAAItC,WAAW2E,YAAW;AAC1B,MAAIsG,UAAUmD,WAAU;AACxB,MAAIhJ,aAAaiJ,cAAa;AAG9B/N,EAAMG,iBAAU,MAAK;AACnBS,WAAOvB,QAAQ2O,oBAAoB;AACnC,WAAO,MAAK;AACVpN,aAAOvB,QAAQ2O,oBAAoB;;KAEpC,CAAA,CAAE;AAGLC,cACQpO,mBAAY,MAAK;AACrB,QAAIiF,WAAWxF,UAAU,QAAQ;AAC/B,UAAI4K,OAAO9C,SAASA,OAAO1H,UAAUiL,OAAO,IAAI,SAASjL,SAASwK;AAClEwD,2BAAqBxD,GAAG,IAAItJ,OAAOsN;IACpC;AACD,QAAI;AACFC,qBAAeC,QACb/G,cAAcoG,gCACdY,KAAKC,UAAUZ,oBAAoB,CAAC;aAE/Ba,OAAO;AACd9N,aAAAwC,QACE,OAAK,sGAC+FsL,QAAK,IAAI,IAC9G;IACF;AACD3N,WAAOvB,QAAQ2O,oBAAoB;EACrC,GAAG,CAAC3G,YAAYD,QAAQtC,WAAWxF,OAAOI,UAAUiL,OAAO,CAAC,CAAC;AAI/D,MAAI,OAAO9J,aAAa,aAAa;AAEnCb,IAAMC,uBAAgB,MAAK;AACzB,UAAI;AACF,YAAIuO,mBAAmBL,eAAeM,QACpCpH,cAAcoG,8BAA8B;AAE9C,YAAIe,kBAAkB;AACpBd,iCAAuBW,KAAKK,MAAMF,gBAAgB;QACnD;eACMxL,GAAG;MACV;IAEJ,GAAG,CAACqE,UAAU,CAAC;AAIfrH,IAAMC,uBAAgB,MAAK;AACzB,UAAI0O,wBACFvH,UAAUlI,aAAa,MACnB,CAACQ,WAAUiL,aACTvD;;QACE5D,UAAA,CAAA,GAEK9D,WAAQ;UACXkD,UACED,cAAcjD,UAASkD,UAAU1D,QAAQ,KACzCQ,UAASkD;SAEb+H;QAAAA;MAAO,IAEXvD;AACN,UAAIwH,2BAA2BnF,WAAAA,OAAAA,SAAAA,QAAQoF,wBACrCnB,sBACA,MAAM9M,OAAOsN,SACbS,qBAAqB;AAEvB,aAAO,MAAMC,4BAA4BA,yBAAwB;OAChE,CAACnF,SAAQvK,UAAUkI,MAAM,CAAC;AAI7BpH,IAAMC,uBAAgB,MAAK;AAEzB,UAAI4N,0BAA0B,OAAO;AACnC;MACD;AAGD,UAAI,OAAOA,0BAA0B,UAAU;AAC7CjN,eAAOkO,SAAS,GAAGjB,qBAAqB;AACxC;MACD;AAGD,UAAInO,SAASqD,MAAM;AACjB,YAAIgM,KAAKlO,SAASmO,eAChBC,mBAAmBvP,SAASqD,KAAK6H,MAAM,CAAC,CAAC,CAAC;AAE5C,YAAImE,IAAI;AACNA,aAAGG,eAAc;AACjB;QACD;MACF;AAGD,UAAIxN,uBAAuB,MAAM;AAC/B;MACD;AAGDd,aAAOkO,SAAS,GAAG,CAAC;OACnB,CAACpP,UAAUmO,uBAAuBnM,kBAAkB,CAAC;EACzD;AACH;AAYgB,SAAAyN,gBACdC,UACAvF,SAA+B;AAE/B,MAAI;IAAEwF;EAAO,IAAKxF,WAAW,CAAA;AAC7B7J,EAAMG,iBAAU,MAAK;AACnB,QAAIsM,OAAO4C,WAAW,OAAO;MAAEA;IAAS,IAAG7J;AAC3C5E,WAAO0O,iBAAiB,gBAAgBF,UAAU3C,IAAI;AACtD,WAAO,MAAK;AACV7L,aAAO2O,oBAAoB,gBAAgBH,UAAU3C,IAAI;;EAE7D,GAAG,CAAC2C,UAAUC,OAAO,CAAC;AACxB;AAUA,SAASpB,YACPmB,UACAvF,SAA+B;AAE/B,MAAI;IAAEwF;EAAO,IAAKxF,WAAW,CAAA;AAC7B7J,EAAMG,iBAAU,MAAK;AACnB,QAAIsM,OAAO4C,WAAW,OAAO;MAAEA;IAAS,IAAG7J;AAC3C5E,WAAO0O,iBAAiB,YAAYF,UAAU3C,IAAI;AAClD,WAAO,MAAK;AACV7L,aAAO2O,oBAAoB,YAAYH,UAAU3C,IAAI;;EAEzD,GAAG,CAAC2C,UAAUC,OAAO,CAAC;AACxB;AAUA,SAASG,UAASC,QAMjB;AAAA,MANkB;IACjBC;IACAC;EAID,IAAAF;AACC,MAAIG,UAAUC,WAAWH,IAAI;AAE7B1P,EAAMG,iBAAU,MAAK;AACnB,QAAIyP,QAAQtQ,UAAU,WAAW;AAC/B,UAAIwQ,UAAUlP,OAAOmP,QAAQJ,OAAO;AACpC,UAAIG,SAAS;AAIXE,mBAAWJ,QAAQE,SAAS,CAAC;MAC9B,OAAM;AACLF,gBAAQK,MAAK;MACd;IACF;EACH,GAAG,CAACL,SAASD,OAAO,CAAC;AAErB3P,EAAMG,iBAAU,MAAK;AACnB,QAAIyP,QAAQtQ,UAAU,aAAa,CAACoQ,MAAM;AACxCE,cAAQK,MAAK;IACd;EACH,GAAG,CAACL,SAASF,IAAI,CAAC;AACpB;AAYA,SAASjL,uBACPhD,IACAgL,MAA6C;AAAA,MAA7CA,SAAAA,QAAAA;AAAAA,WAA2C,CAAA;EAAE;AAE7C,MAAIyD,YAAkBnO,kBAAWoO,qBAAqB;AAEtD,IACED,aAAa,QAAIzP,OADnBqH,UAEE,OAAA,wJACqE,IAHvEA,UAAS,KAAA,IAAA;AAMT,MAAI;IAAE5I;EAAQ,IAAKyI,sBACjBJ,gBAAe9C,sBAAsB;AAEvC,MAAI/B,OAAO0B,gBAAgB3C,IAAI;IAAEJ,UAAUoL,KAAKpL;EAAQ,CAAE;AAC1D,MAAI,CAAC6O,UAAU1L,iBAAiB;AAC9B,WAAO;EACR;AAED,MAAI4L,cACFzN,cAAcuN,UAAUG,gBAAgBzN,UAAU1D,QAAQ,KAC1DgR,UAAUG,gBAAgBzN;AAC5B,MAAI0N,WACF3N,cAAcuN,UAAUK,aAAa3N,UAAU1D,QAAQ,KACvDgR,UAAUK,aAAa3N;AAezB,SACE4N,UAAU9N,KAAKE,UAAU0N,QAAQ,KAAK,QACtCE,UAAU9N,KAAKE,UAAUwN,WAAW,KAAK;AAE7C;;;AClgEA;;;ACDA;AACA,IAAAK,SAAuB;;;ACDvB,SAASC,YAAW;AAClB,EAAAA,YAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACvC;;;ACZA,IAAAC,SAAuB;;;ACDvB,SAASC,WAAU,OAAO,SAAS;AACjC,MAAI,UAAU,SAAS,UAAU,QAAQ,OAAO,UAAU,aAAa;AACrE,UAAM,IAAI,MAAM,OAAO;AAAA,EACzB;AACF;;;AC4CA,eAAe,gBAAgB,OAAO,mBAAmB;AACvD,MAAI,MAAM,MAAM,mBAAmB;AACjC,WAAO,kBAAkB,MAAM,EAAE;AAAA,EACnC;AACA,MAAI;AACF,QAAI,cAAc,MAAM;AAAA;AAAA,MAAiC,MAAM;AAAA;AAC/D,sBAAkB,MAAM,EAAE,IAAI;AAC9B,WAAO;AAAA,EACT,SAAS,OAAO;AAWd,YAAQ,MAAM,gCAAgC,MAAM,MAAM,uBAAuB;AACjF,YAAQ,MAAM,KAAK;AACnB,QAAI,OAAO,eAAe;AAAA,IAE1B,OAAO,YAAY,QAAQ,aAAa;AAKtC,YAAM;AAAA,IACR;AACA,WAAO,SAAS,OAAO;AACvB,WAAO,IAAI,QAAQ,MAAM;AAAA,IAEzB,CAAC;AAAA,EACH;AACF;;;ACpEA,SAAS,wBAAwB,SAAS,cAAc,UAAU;AAChE,MAAI,cAAc,QAAQ,IAAI,WAAS;AACrC,QAAI;AACJ,QAAI,SAAS,aAAa,MAAM,MAAM,EAAE;AACxC,QAAI,QAAQ,SAAS,OAAO,MAAM,MAAM,EAAE;AAC1C,WAAO,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI,WAAS;AAAA,MACzC,KAAK;AAAA,MACL;AAAA,IACF,EAAE,IAAI,CAAC,IAAI,WAAW,QAAQ,WAAW,SAAS,UAAU,gBAAgB,OAAO,WAAW,QAAQ,kBAAkB,SAAS,SAAS,cAAc,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,EAC7K,CAAC,EAAE,KAAK,CAAC;AACT,MAAI,WAAW,iCAAiC,SAAS,QAAQ;AACjE,SAAO,sBAAsB,aAAa,QAAQ;AACpD;AACA,eAAe,mBAAmB,OAAO,aAAa;AACpD,MAAI,YAAY;AAChB,MAAI,CAAC,MAAM,OAAO,CAAC,YAAY,SAAS,CAAC,mBAAmB,EAAG;AAC/D,MAAI,cAAc,GAAG,aAAa,MAAM,SAAS,QAAQ,eAAe,SAAS,SAAS,WAAW,IAAI,WAAS;AAAA,IAChH,KAAK;AAAA,IACL;AAAA,EACF,EAAE,MAAM,CAAC,KAAK,qBAAqB,YAAY,WAAW,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,KAAK,WAAW,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC;AAC9J,MAAI,YAAY,WAAW,EAAG;AAC9B,MAAI,aAAa,CAAC;AAClB,WAAS,cAAc,aAAa;AAClC,QAAI,CAAC,qBAAqB,UAAU,KAAK,WAAW,QAAQ,cAAc;AACxE,iBAAW,KAAK;AAAA,QACd,GAAG;AAAA,QACH,KAAK;AAAA,QACL,IAAI;AAAA,MACN,CAAC;AAAA,IACH;AAAA,EACF;AAIA,MAAI,gBAAgB,WAAW,OAAO,WAAS,CAAC,KAAK,SAAS,OAAO,WAAW,KAAK,KAAK,EAAE,YAAY,CAAC,SAAS,cAAc,gCAAgC,KAAK,IAAI,IAAI,CAAC;AAC9K,QAAM,QAAQ,IAAI,cAAc,IAAI,iBAAiB,CAAC;AACxD;AACA,eAAe,kBAAkB,YAAY;AAC3C,SAAO,IAAI,QAAQ,aAAW;AAC5B,QAAI,OAAO,SAAS,cAAc,MAAM;AACxC,WAAO,OAAO,MAAM,UAAU;AAC9B,aAAS,aAAa;AAIpB,UAAI,SAAS,KAAK,SAAS,IAAI,GAAG;AAChC,iBAAS,KAAK,YAAY,IAAI;AAAA,MAChC;AAAA,IACF;AACA,SAAK,SAAS,MAAM;AAClB,iBAAW;AACX,cAAQ;AAAA,IACV;AACA,SAAK,UAAU,MAAM;AACnB,iBAAW;AACX,cAAQ;AAAA,IACV;AACA,aAAS,KAAK,YAAY,IAAI;AAAA,EAChC,CAAC;AACH;AAGA,SAAS,qBAAqB,QAAQ;AACpC,SAAO,UAAU,QAAQ,OAAO,OAAO,SAAS;AAClD;AACA,SAAS,qBAAqB,QAAQ;AACpC,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AAKA,MAAI,OAAO,QAAQ,MAAM;AACvB,WAAO,OAAO,QAAQ,aAAa,OAAO,OAAO,gBAAgB,YAAY,OAAO,OAAO,eAAe;AAAA,EAC5G;AACA,SAAO,OAAO,OAAO,QAAQ,YAAY,OAAO,OAAO,SAAS;AAClE;AACA,eAAe,sBAAsB,SAAS,UAAU,cAAc;AACpE,MAAI,QAAQ,MAAM,QAAQ,IAAI,QAAQ,IAAI,OAAM,UAAS;AACvD,QAAI,MAAM,MAAM,gBAAgB,SAAS,OAAO,MAAM,MAAM,EAAE,GAAG,YAAY;AAC7E,WAAO,IAAI,QAAQ,IAAI,MAAM,IAAI,CAAC;AAAA,EACpC,CAAC,CAAC;AACF,SAAO,sBAAsB,MAAM,KAAK,CAAC,EAAE,OAAO,oBAAoB,EAAE,OAAO,UAAQ,KAAK,QAAQ,gBAAgB,KAAK,QAAQ,SAAS,EAAE,IAAI,UAAQ,KAAK,QAAQ,eAAe;AAAA,IAClL,GAAG;AAAA,IACH,KAAK;AAAA,IACL,IAAI;AAAA,EACN,IAAI;AAAA,IACF,GAAG;AAAA,IACH,KAAK;AAAA,EACP,CAAC,CAAC;AACJ;AAGA,SAAS,sBAAsB,MAAM,aAAa,gBAAgB,UAAU,UAAU,QAAQ,MAAM;AAClG,MAAI,OAAO,eAAe,IAAI;AAC9B,MAAI,QAAQ,CAAC,OAAO,UAAU;AAC5B,QAAI,CAAC,eAAe,KAAK,EAAG,QAAO;AACnC,WAAO,MAAM,MAAM,OAAO,eAAe,KAAK,EAAE,MAAM;AAAA,EACxD;AACA,MAAI,mBAAmB,CAAC,OAAO,UAAU;AACvC,QAAI;AACJ;AAAA;AAAA,MAEE,eAAe,KAAK,EAAE,aAAa,MAAM;AAAA;AAAA,QAGvC,wBAAwB,eAAe,KAAK,EAAE,MAAM,UAAU,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,SAAS,GAAG,MAAM,eAAe,KAAK,EAAE,OAAO,GAAG,MAAM,MAAM,OAAO,GAAG;AAAA;AAAA,EAEtN;AAIA,MAAI,aAAa,SAAS,WAAW,OAAO,kBAAkB,SAAS,WAAW,KAAK;AAAA;AAAA;AAAA,IAGvF,YAAY,OAAO,CAAC,OAAO,UAAU;AACnC,UAAI,gBAAgB,SAAS,OAAO,MAAM,MAAM,EAAE;AAClD,UAAI,CAAC,cAAc,WAAW;AAC5B,eAAO;AAAA,MACT;AACA,UAAI,MAAM,OAAO,KAAK,KAAK,iBAAiB,OAAO,KAAK,GAAG;AACzD,eAAO;AAAA,MACT;AAKA,UAAI,0BAA0B,OAAO,kBAAkB,SAAS,WAAW,KAAK;AAChF,UAAI,MAAM,MAAM,kBAAkB;AAChC,YAAI;AACJ,YAAI,cAAc,MAAM,MAAM,iBAAiB;AAAA,UAC7C,YAAY,IAAI,IAAI,SAAS,WAAW,SAAS,SAAS,SAAS,MAAM,OAAO,MAAM;AAAA,UACtF,iBAAiB,mBAAmB,eAAe,CAAC,OAAO,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,WAAW,CAAC;AAAA,UACvI,SAAS,IAAI,IAAI,MAAM,OAAO,MAAM;AAAA,UACpC,YAAY,MAAM;AAAA,UAClB;AAAA,QACF,CAAC;AACD,YAAI,OAAO,gBAAgB,WAAW;AACpC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,MAAI,YAAY,OAAO,CAAC,OAAO,UAAU;AACxC,QAAI,gBAAgB,SAAS,OAAO,MAAM,MAAM,EAAE;AAClD,YAAQ,SAAS,YAAY,cAAc,eAAe,MAAM,OAAO,KAAK,KAAK,iBAAiB,OAAO,KAAK;AAAA,EAChH,CAAC;AACD,SAAO;AACT;AACA,SAAS,iBAAiB,MAAM,SAAS,UAAU;AACjD,MAAI,OAAO,eAAe,IAAI;AAC9B,SAAO,YAAY,QAAQ,OAAO,WAAS,SAAS,OAAO,MAAM,MAAM,EAAE,EAAE,aAAa,CAAC,SAAS,OAAO,MAAM,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,WAAS;AACrJ,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,eAAe,IAAI,gBAAgB,MAAM;AAC7C,iBAAa,IAAI,SAAS,MAAM,MAAM,EAAE;AACxC,WAAO,GAAG,QAAQ,IAAI,YAAY;AAAA,EACpC,CAAC,CAAC;AACJ;AACA,SAAS,mBAAmB,SAAS,eAAe;AAClD,SAAO,YAAY,QAAQ,IAAI,WAAS;AACtC,QAAI,QAAQ,cAAc,OAAO,MAAM,MAAM,EAAE;AAC/C,QAAI,QAAQ,CAAC,MAAM,MAAM;AACzB,QAAI,MAAM,SAAS;AACjB,cAAQ,MAAM,OAAO,MAAM,OAAO;AAAA,IACpC;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,CAAC,CAAC;AACZ;AAKA,SAAS,iCAAiC,SAAS,UAAU;AAC3D,SAAO,YAAY,QAAQ,IAAI,WAAS;AACtC,QAAI,QAAQ,SAAS,OAAO,MAAM,MAAM,EAAE;AAC1C,QAAI,QAAQ,CAAC,MAAM,MAAM;AACzB,QAAI,MAAM,SAAS;AACjB,cAAQ,MAAM,OAAO,MAAM,OAAO;AAAA,IACpC;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,CAAC,CAAC;AACZ;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAC3B;AACA,SAAS,SAAS,KAAK;AACrB,MAAI,SAAS,CAAC;AACd,MAAI,OAAO,OAAO,KAAK,GAAG,EAAE,KAAK;AACjC,WAAS,OAAO,MAAM;AACpB,WAAO,GAAG,IAAI,IAAI,GAAG;AAAA,EACvB;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,aAAa,UAAU;AACpD,MAAI,MAAM,oBAAI,IAAI;AAClB,MAAI,cAAc,IAAI,IAAI,QAAQ;AAClC,SAAO,YAAY,OAAO,CAAC,SAAS,eAAe;AACjD,QAAI,uBAAuB,YAAY,CAAC,qBAAqB,UAAU,KAAK,WAAW,OAAO,YAAY,WAAW,QAAQ,YAAY,IAAI,WAAW,IAAI;AAC5J,QAAI,sBAAsB;AACxB,aAAO;AAAA,IACT;AACA,QAAI,MAAM,KAAK,UAAU,SAAS,UAAU,CAAC;AAC7C,QAAI,CAAC,IAAI,IAAI,GAAG,GAAG;AACjB,UAAI,IAAI,GAAG;AACX,cAAQ,KAAK;AAAA,QACX;AAAA,QACA,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAGA,SAAS,eAAe,MAAM;AAC5B,MAAI,OAAO,UAAU,IAAI;AACzB,MAAI,KAAK,WAAW,OAAW,MAAK,SAAS;AAC7C,SAAO;AACT;AAKA,IAAI;AACJ,SAAS,qBAAqB;AAC5B,MAAI,wBAAwB,QAAW;AACrC,WAAO;AAAA,EACT;AACA,MAAI,KAAK,SAAS,cAAc,MAAM;AACtC,wBAAsB,GAAG,QAAQ,SAAS,SAAS;AACnD,OAAK;AACL,SAAO;AACT;;;ACpPA,IAAM,gBAAgB;AAAA,EACpB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,UAAU;AAAA,EACV,UAAU;AACZ;AACA,IAAM,eAAe;AACrB,SAAS,WAAW,MAAM;AACxB,SAAO,KAAK,QAAQ,cAAc,WAAS,cAAc,KAAK,CAAC;AACjE;AACA,SAAS,WAAW,MAAM;AACxB,SAAO;AAAA,IACL,QAAQ;AAAA,EACV;AACF;;;ACrBA,IAAAC,SAAuB;AACvB;AACA;AACA;;;ACHA;AAMA,SAAS,gBAAgB,UAAU;AACjC,SAAO,SAAS,QAAQ,IAAI,eAAe,KAAK;AAClD;AACA,SAAS,gBAAgB,UAAU;AACjC,SAAO,SAAS,QAAQ,IAAI,eAAe,KAAK;AAClD;AACA,SAAS,uBAAuB,UAAU;AAUxC,SAAO,WAAW,QAAQ,KAAK,SAAS,UAAU,OAAO,SAAS,QAAQ,IAAI,eAAe,KAAK,QAAQ,SAAS,QAAQ,IAAI,eAAe,KAAK,QAAQ,SAAS,QAAQ,IAAI,kBAAkB,KAAK;AACzM;AACA,SAAS,mBAAmB,UAAU;AACpC,SAAO,SAAS,QAAQ,IAAI,kBAAkB,KAAK;AACrD;AACA,SAAS,mBAAmB,UAAU;AACpC,MAAI;AACJ,SAAO,CAAC,GAAG,wBAAwB,SAAS,QAAQ,IAAI,cAAc,OAAO,QAAQ,0BAA0B,UAAU,sBAAsB,MAAM,sBAAsB;AAC7K;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,SAAS,QAAQ,OAAO,MAAM,WAAW,YAAY,OAAO,MAAM,eAAe,YAAY,OAAO,MAAM,YAAY,YAAY,OAAO,MAAM,SAAS;AACjK;AACA,SAAS,eAAe,OAAO;AAC7B,MAAI,WAAW;AACf,SAAO,YAAY,OAAO,aAAa,YAAY,OAAO,SAAS,SAAS,YAAY,OAAO,SAAS,cAAc,cAAc,OAAO,SAAS,WAAW,cAAc,OAAO,SAAS,gBAAgB;AAC/M;AACA,eAAe,UAAU,SAAS,SAAS,QAAQ,GAAG;AACpD,MAAI,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC7B,MAAI,aAAa,IAAI,SAAS,OAAO;AACrC,MAAI,QAAQ,GAAG;AAGb,UAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,KAAK,QAAQ,EAAE,CAAC;AAAA,EACnE;AACA,MAAI,OAAO,MAAM,kBAAkB,OAAO;AAC1C,MAAI,eAAe,OAAO;AAC1B,MAAI,WAAW,MAAM,MAAM,IAAI,MAAM,IAAI,EAAE,MAAM,WAAS;AACxD,QAAI,OAAO,iBAAiB,YAAY,iBAAiB,OAAO,wBAAwB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,UAAU,eAAe,QAAQ,GAAG;AAC9K,aAAO,UAAU,SAAS,SAAS,QAAQ,CAAC;AAAA,IAC9C;AACA,UAAM;AAAA,EACR,CAAC;AACD,MAAI,gBAAgB,QAAQ,GAAG;AAC7B,QAAIC,QAAO,MAAM,SAAS,KAAK;AAC/B,QAAI,QAAQ,IAAI,MAAMA,MAAK,OAAO;AAClC,UAAM,QAAQA,MAAK;AACnB,WAAO;AAAA,EACT;AACA,MAAI,uBAAuB,QAAQ,GAAG;AACpC,QAAI,OAAO,MAAM,SAAS,KAAK;AAC/B,QAAI,QAAQ,IAAI,MAAM,IAAI;AAC1B,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,eAAe,kBAAkB,SAAS;AACxC,MAAI,OAAO;AAAA,IACT,QAAQ,QAAQ;AAAA,EAClB;AACA,MAAI,QAAQ,WAAW,OAAO;AAC5B,SAAK,SAAS,QAAQ;AACtB,QAAI,cAAc,QAAQ,QAAQ,IAAI,cAAc;AAIpD,QAAI,eAAe,wBAAwB,KAAK,WAAW,GAAG;AAC5D,WAAK,UAAU;AAAA,QACb,gBAAgB;AAAA,MAClB;AACA,WAAK,OAAO,KAAK,UAAU,MAAM,QAAQ,KAAK,CAAC;AAAA,IACjD,WAAW,eAAe,kBAAkB,KAAK,WAAW,GAAG;AAC7D,WAAK,UAAU;AAAA,QACb,gBAAgB;AAAA,MAClB;AACA,WAAK,OAAO,MAAM,QAAQ,KAAK;AAAA,IACjC,WAAW,eAAe,yCAAyC,KAAK,WAAW,GAAG;AACpF,WAAK,OAAO,IAAI,gBAAgB,MAAM,QAAQ,KAAK,CAAC;AAAA,IACtD,OAAO;AACL,WAAK,OAAO,MAAM,QAAQ,SAAS;AAAA,IACrC;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,oCAAoC;AAC1C,eAAe,4BAA4B,QAAQ;AACjD,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,sDAAsD;AAAA,EACxE;AACA,MAAI;AACJ,MAAI,oBAAoB,CAAC;AACzB,MAAI;AACF,QAAI,gBAAgB,mBAAmB,MAAM;AAG7C,QAAI,uBAAuB,MAAM,cAAc,KAAK;AACpD,QAAI,iBAAiB,qBAAqB;AAC1C,QAAI,CAAC,eAAgB,OAAM,IAAI,MAAM,kBAAkB;AACvD,QAAI,eAAe,KAAK,MAAM,cAAc;AAG5C,QAAI,OAAO,iBAAiB,YAAY,iBAAiB,MAAM;AAC7D,eAAS,CAAC,UAAU,KAAK,KAAK,OAAO,QAAQ,YAAY,GAAG;AAC1D,YAAI,OAAO,UAAU,YAAY,CAAC,MAAM,WAAW,iCAAiC,GAAG;AACrF;AAAA,QACF;AACA,uBAAe,gBAAgB,CAAC;AAChC,qBAAa,QAAQ,IAAI,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxD,4BAAkB,QAAQ,IAAI;AAAA,YAC5B,SAAS,CAAAC,WAAS;AAChB,sBAAQA,MAAK;AACb,qBAAO,kBAAkB,QAAQ;AAAA,YACnC;AAAA,YACA,QAAQ,WAAS;AACf,qBAAO,KAAK;AACZ,qBAAO,kBAAkB,QAAQ;AAAA,YACnC;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAGA,UAAM,YAAY;AAChB,UAAI;AACF,uBAAe,WAAW,eAAe;AAEvC,cAAI,CAAC,OAAO,GAAG,kBAAkB,IAAI,QAAQ,MAAM,GAAG;AACtD,cAAI,oBAAoB,mBAAmB,KAAK,GAAG;AACnD,cAAID,QAAO,KAAK,MAAM,iBAAiB;AACvC,cAAI,UAAU,QAAQ;AACpB,qBAAS,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQA,KAAI,GAAG;AAC7C,kBAAI,kBAAkB,GAAG,GAAG;AAC1B,kCAAkB,GAAG,EAAE,QAAQ,KAAK;AAAA,cACtC;AAAA,YACF;AAAA,UACF,WAAW,UAAU,SAAS;AAC5B,qBAAS,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQA,KAAI,GAAG;AAC7C,kBAAI,MAAM,IAAI,MAAM,MAAM,OAAO;AACjC,kBAAI,QAAQ,MAAM;AAClB,kBAAI,kBAAkB,GAAG,GAAG;AAC1B,kCAAkB,GAAG,EAAE,OAAO,GAAG;AAAA,cACnC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,iBAAS,CAAC,KAAK,QAAQ,KAAK,OAAO,QAAQ,iBAAiB,GAAG;AAC7D,mBAAS,OAAO,IAAI,qBAAqB,YAAY,GAAG,yBAAyB,CAAC;AAAA,QACpF;AAAA,MACF,SAAS,OAAO;AAEd,iBAAS,YAAY,OAAO,OAAO,iBAAiB,GAAG;AACrD,mBAAS,OAAO,KAAK;AAAA,QACvB;AAAA,MACF;AAAA,IACF,GAAG;AACH,WAAO,IAAI,aAAoB;AAAA,MAC7B,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AAAA,EACH,SAAS,OAAO;AACd,aAAS,YAAY,OAAO,OAAO,iBAAiB,GAAG;AACrD,eAAS,OAAO,KAAK;AAAA,IACvB;AACA,UAAM;AAAA,EACR;AACF;AACA,gBAAgB,mBAAmB,QAAQ;AACzC,MAAI,SAAS,OAAO,UAAU;AAC9B,MAAI,SAAS,CAAC;AACd,MAAI,WAAW,CAAC;AAChB,MAAI,SAAS;AACb,MAAI,UAAU,IAAI,YAAY;AAC9B,MAAI,UAAU,IAAI,YAAY;AAC9B,MAAI,oBAAoB,YAAY;AAClC,QAAI,SAAS,SAAS,EAAG,QAAO,SAAS,MAAM;AAG/C,WAAO,CAAC,UAAU,SAAS,WAAW,GAAG;AACvC,UAAI,QAAQ,MAAM,OAAO,KAAK;AAC9B,UAAI,MAAM,MAAM;AACd,iBAAS;AACT;AAAA,MACF;AAEA,aAAO,KAAK,MAAM,KAAK;AACvB,UAAI;AAEF,YAAI,iBAAiB,QAAQ,OAAO,YAAY,GAAG,MAAM,CAAC;AAC1D,YAAI,gBAAgB,eAAe,MAAM,MAAM;AAC/C,YAAI,cAAc,UAAU,GAAG;AAE7B,mBAAS,KAAK,GAAG,cAAc,MAAM,GAAG,EAAE,CAAC;AAE3C,mBAAS,CAAC,QAAQ,OAAO,cAAc,MAAM,EAAE,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,QAChE;AAIA,YAAI,SAAS,SAAS,GAAG;AACvB;AAAA,QACF;AAAA,MACF,QAAQ;AAIN;AAAA,MACF;AAAA,IACF;AAGA,QAAI,SAAS,SAAS,GAAG;AACvB,aAAO,SAAS,MAAM;AAAA,IACxB;AAMA,QAAI,OAAO,SAAS,GAAG;AACrB,UAAI,iBAAiB,QAAQ,OAAO,YAAY,GAAG,MAAM,CAAC;AAC1D,iBAAW,eAAe,MAAM,MAAM,EAAE,OAAO,OAAK,CAAC;AACrD,eAAS,CAAC;AAAA,IACZ;AAGA,WAAO,SAAS,MAAM;AAAA,EACxB;AACA,MAAI,UAAU,MAAM,kBAAkB;AACtC,SAAO,SAAS;AACd,UAAM;AACN,cAAU,MAAM,kBAAkB;AAAA,EACpC;AACF;AACA,SAAS,eAAe,QAAQ;AAC9B,MAAI,MAAM,IAAI,WAAW,OAAO,OAAO,CAAC,OAAO,QAAQ,QAAQ,IAAI,QAAQ,CAAC,CAAC;AAC7E,MAAI,SAAS;AACb,WAAS,OAAO,QAAQ;AACtB,QAAI,IAAI,KAAK,MAAM;AACnB,cAAU,IAAI;AAAA,EAChB;AACA,SAAO;AACT;;;ADpPA,SAAS,eAAe;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AAGD,MAAI,CAAC,QAAQ,cAAc,CAAC,QAAQ,WAAW,kBAAkB;AAC/D,WAAO;AAAA,EACT;AACA,MAAI,CAAC,QAAQ,WAAW,aAAa;AACnC,YAAQ,WAAW,cAAc,CAAC;AAAA,EACpC;AACA,MAAI;AAAA,IACF;AAAA,EACF,IAAI,QAAQ;AACZ,MAAI,UAAU,YAAY,UAAU;AACpC,MAAI,CAAC,SAAS;AACZ,cAAU,YAAY,UAAU,IAAI,OAAO,KAAK,EAAE,KAAK,YAAU;AAC/D,kBAAY,UAAU,EAAE,SAAS;AAAA,QAC/B,MAAM,OAAO;AAAA,QACb,OAAO,YAAY,OAAO,OAAO,OAAO;AAAA,UACtC,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF,CAAC,EAAE,MAAM,OAAK;AACZ,kBAAY,UAAU,EAAE,QAAQ;AAAA,IAClC,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,OAAO;AACjB,UAAM,QAAQ;AAAA,EAChB;AACA,MAAI,QAAQ,WAAW,QAAW;AAChC,UAAM;AAAA,EACR;AACA,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,QAAQ;AACZ,MAAI,YAAY,QAA2B,qBAAc,UAAU;AAAA,IACjE;AAAA,IACA,yBAAyB;AAAA,MACvB,QAAQ,kDAAkD,WAAW,KAAK,UAAU,KAAK,CAAC,CAAC;AAAA,IAC7F;AAAA,EACF,CAAC,IAAI;AACL,MAAI,MAAM;AACR,WAA0B,qBAAoB,iBAAU,MAAM,WAA8B,qBAAc,UAAU;AAAA,MAClH;AAAA,MACA,yBAAyB;AAAA,QACvB,QAAQ;AAAA,MACV;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,OAAO;AACL,WAA0B,qBAAoB,iBAAU,MAAM,WAA8B,qBAAoB,iBAAU,MAAyB,qBAAc,gBAAgB;AAAA,MAC/K;AAAA,MACA,YAAY,aAAa;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AACF;AACA,SAAS,2BAA2B,UAAU,cAAc,WAAW;AACrE,SAAO,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AAEJ,QAAI,QAAQ,WAAW,OAAO;AAC5B,aAAO,0BAA0B,SAAS,OAAO;AAAA,IACnD;AAGA,QAAI,YAAY;AACd,aAAO,iCAAiC,SAAS,OAAO;AAAA,IAC1D;AAGA,WAAO,oCAAoC,UAAU,cAAc,UAAU,GAAG,SAAS,OAAO;AAAA,EAClG;AACF;AAIA,eAAe,0BAA0B,SAAS,SAAS;AACzD,MAAI,cAAc,QAAQ,KAAK,OAAK,EAAE,UAAU;AAChD,EAAAE,WAAU,aAAa,uBAAuB;AAC9C,MAAI,eAAe;AACnB,MAAI,SAAS,MAAM,YAAY,QAAQ,OAAM,YAAW;AACtD,QAAIC,UAAS,MAAM,QAAQ,YAAY;AACrC,UAAI,MAAM,eAAe,QAAQ,GAAG;AACpC,UAAI,OAAO,MAAM,kBAAkB,OAAO;AAC1C,UAAI;AAAA,QACF,MAAAC;AAAA,QACA;AAAA,MACF,IAAI,MAAM,eAAe,KAAK,IAAI;AAClC,qBAAe;AACf,aAAO,wBAAwBA,OAAM,YAAY,MAAM,EAAE;AAAA,IAC3D,CAAC;AACD,WAAOD;AAAA,EACT,CAAC;AACD,MAAI,WAAW,OAAO,MAAM,KAAK,qBAAqB,OAAO,MAAM,GAAG;AACpE,WAAO;AAAA,MACL,CAAC,YAAY,MAAM,EAAE,GAAG;AAAA,IAC1B;AAAA,EACF;AAIA,SAAO;AAAA,IACL,CAAC,YAAY,MAAM,EAAE,GAAG;AAAA,MACtB,MAAM,OAAO;AAAA,MACb,QAAQ,KAAK,OAAO,QAAQ,YAAY;AAAA,IAC1C;AAAA,EACF;AACF;AAIA,eAAe,oCAAoC,UAAU,cAAcE,SAAQ,SAAS,SAAS;AAGnG,MAAI,eAAe,oBAAI,IAAI;AAI3B,MAAI,mBAAmB;AAKvB,MAAI,YAAY,QAAQ,IAAI,MAAM,eAAe,CAAC;AAClD,MAAI,sBAAsB,QAAQ,IAAI,UAAU,IAAI,OAAK,EAAE,OAAO,CAAC;AAInE,MAAI,iBAAiB,eAAe;AAGpC,MAAI,MAAM,gBAAgB,eAAe,QAAQ,GAAG,CAAC;AACrD,MAAI,OAAO,MAAM,kBAAkB,OAAO;AAG1C,MAAI,UAAU,CAAC;AACf,MAAI,iBAAiB,QAAQ,IAAI,QAAQ,IAAI,OAAO,GAAG,MAAM,EAAE,QAAQ,OAAM,YAAW;AACtF,cAAU,CAAC,EAAE,QAAQ;AACrB,QAAI,CAAC,EAAE,YAAY;AACjB,UAAI;AAIJ,UAAI,CAACA,QAAO,MAAM,aAAa;AAC7B;AAAA,MACF;AAKA,UAAI,EAAE,MAAM,MAAMA,QAAO,MAAM,cAAc,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,cAAc,wBAAwB,aAAa,EAAE,MAAM,EAAE,OAAO,QAAQ,0BAA0B,UAAU,sBAAsB,kBAAkB;AAC/N,2BAAmB;AACnB;AAAA,MACF;AAAA,IACF;AAIA,QAAI,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,iBAAiB;AAC/C,UAAI,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,WAAW;AACzC,2BAAmB;AAAA,MACrB;AACA,UAAI;AACF,YAAI,SAAS,MAAM,kBAAkB,SAAS,KAAK,MAAM,EAAE,MAAM,EAAE;AACnE,gBAAQ,EAAE,MAAM,EAAE,IAAI;AAAA,UACpB,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF,SAAS,GAAG;AACV,gBAAQ,EAAE,MAAM,EAAE,IAAI;AAAA,UACpB,MAAM;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF;AACA;AAAA,IACF;AAGA,QAAI,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,WAAW;AACzC,mBAAa,IAAI,EAAE,MAAM,EAAE;AAAA,IAC7B;AAGA,QAAI;AACF,UAAI,SAAS,MAAM,QAAQ,YAAY;AACrC,YAAID,QAAO,MAAM,eAAe;AAChC,eAAO,yBAAyBA,OAAM,EAAE,MAAM,EAAE;AAAA,MAClD,CAAC;AACD,cAAQ,EAAE,MAAM,EAAE,IAAI;AAAA,QACpB,MAAM;AAAA,QACN;AAAA,MACF;AAAA,IACF,SAAS,GAAG;AACV,cAAQ,EAAE,MAAM,EAAE,IAAI;AAAA,QACpB,MAAM;AAAA,QACN,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,CAAC,CAAC,CAAC;AAGH,QAAM;AASN,OAAK,CAACC,QAAO,MAAM,eAAe,aAAa,SAAS,MAAM,CAAC,OAAO,kBAAkB;AACtF,mBAAe,QAAQ,CAAC,CAAC;AAAA,EAC3B,OAAO;AACL,QAAI;AAIF,UAAI,oBAAoB,aAAa,OAAO,GAAG;AAC7C,YAAI,aAAa,IAAI,WAAW,QAAQ,OAAO,OAAK,aAAa,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,OAAK,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG,CAAC;AAAA,MAClH;AACA,UAAID,QAAO,MAAM,eAAe,KAAK,IAAI;AACzC,qBAAe,QAAQA,MAAK,IAAI;AAAA,IAClC,SAAS,GAAG;AACV,qBAAe,OAAO,CAAC;AAAA,IACzB;AAAA,EACF;AACA,QAAM;AACN,SAAO;AACT;AAGA,eAAe,iCAAiC,SAAS,SAAS;AAChE,MAAI,eAAe,QAAQ,KAAK,OAAK,EAAE,UAAU;AACjD,EAAAF,WAAU,cAAc,wBAAwB;AAChD,MAAI,SAAS,MAAM,aAAa,QAAQ,OAAM,YAAW;AACvD,QAAI,MAAM,gBAAgB,eAAe,QAAQ,GAAG,CAAC;AACrD,QAAI,OAAO,MAAM,kBAAkB,OAAO;AAC1C,WAAO,kBAAkB,SAAS,KAAK,MAAM,aAAa,MAAM,EAAE;AAAA,EACpE,CAAC;AACD,SAAO;AAAA,IACL,CAAC,aAAa,MAAM,EAAE,GAAG;AAAA,EAC3B;AACF;AACA,SAAS,kBAAkB,SAAS,KAAK,MAAM,SAAS;AACtD,SAAO,QAAQ,YAAY;AACzB,QAAI,kBAAkB,IAAI,IAAI,GAAG;AACjC,oBAAgB,aAAa,IAAI,WAAW,OAAO;AACnD,QAAI;AAAA,MACF,MAAAE;AAAA,IACF,IAAI,MAAM,eAAe,iBAAiB,IAAI;AAC9C,WAAO,yBAAyBA,OAAM,OAAO;AAAA,EAC/C,CAAC;AACH;AACA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,cAAc,IAAI,aAAa,OAAO,OAAO;AACjD,MAAI,aAAa,OAAO,OAAO;AAC/B,MAAI,oBAAoB,CAAC;AACzB,WAAS,cAAc,aAAa;AAClC,QAAI,YAAY;AACd,wBAAkB,KAAK,UAAU;AAAA,IACnC;AAAA,EACF;AACA,WAAS,UAAU,mBAAmB;AACpC,QAAI,aAAa,OAAO,SAAS,MAAM;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,MAAI,MAAM,OAAO,WAAW,WAAW,IAAI,IAAI,QAAQ,OAAO,SAAS,MAAM,IAAI;AACjF,MAAI,IAAI,aAAa,KAAK;AACxB,QAAI,WAAW;AAAA,EACjB,OAAO;AACL,QAAI,WAAW,GAAG,IAAI,SAAS,QAAQ,OAAO,EAAE,CAAC;AAAA,EACnD;AACA,SAAO;AACT;AACA,eAAe,eAAe,KAAK,MAAM;AACvC,MAAI,MAAM,MAAM,MAAM,KAAK,IAAI;AAW/B,MAAI,uBAAuB,oBAAI,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC;AACvD,MAAI,qBAAqB,IAAI,IAAI,MAAM,GAAG;AACxC,QAAI,CAAC,KAAK,UAAU,KAAK,WAAW,OAAO;AAGzC,aAAO;AAAA,QACL,QAAQ,IAAI;AAAA,QACZ,MAAM,CAAC;AAAA,MACT;AAAA,IACF,OAAO;AAEL,aAAO;AAAA,QACL,QAAQ,IAAI;AAAA,QACZ,MAAM;AAAA,UACJ,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,EAAAF,WAAU,IAAI,MAAM,4BAA4B;AAChD,MAAI;AACF,QAAI,UAAU,MAAM,qBAAqB,IAAI,MAAM,MAAM;AACzD,WAAO;AAAA,MACL,QAAQ,IAAI;AAAA,MACZ,MAAM,QAAQ;AAAA,IAChB;AAAA,EACF,SAAS,GAAG;AACV,YAAQ,MAAM,CAAC;AACf,UAAM,IAAI,MAAM,oDAAoD,IAAI,SAAS,CAAC,EAAE;AAAA,EACtF;AACF;AAIA,SAAS,qBAAqB,MAAM,QAAQ;AAC1C,SAAO,OAAO,MAAM;AAAA,IAClB,SAAS,CAAC,CAAC,SAAS,SAAS;AAG3B,UAAI,SAAS,kBAAkB;AAC7B,YAAI,CAAC,MAAM,SAAS,KAAK,IAAI;AAC7B,YAAI,cAAc;AAElB,YAAI,QAAQ,QAAQ,UAAU,OAAO,OAAO,IAAI,MAAM,YAAY;AAEhE,wBAAc,OAAO,IAAI;AAAA,QAC3B;AACA,YAAI,QAAQ,IAAI,YAAY,OAAO;AACnC,cAAM,QAAQ;AACd,eAAO;AAAA,UACL,OAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,SAAS,iBAAiB;AAC5B,YAAI,CAACE,OAAM,QAAQ,UAAU,IAAI;AACjC,eAAO;AAAA,UACL,OAAO,IAAI,kBAAyB,QAAQ,YAAYA,KAAI;AAAA,QAC9D;AAAA,MACF;AACA,UAAI,SAAS,uBAAuB;AAClC,eAAO;AAAA,UACL,OAAO;AAAA,YACL,CAAC,yBAAgC,GAAG,KAAK,CAAC;AAAA,UAC5C;AAAA,QACF;AAAA,MACF;AAAA,IACF,GAAG,CAAC,MAAM,UAAU;AAClB,UAAI,SAAS,uBAAuB;AAClC,eAAO;AAAA,UACL,OAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,SAAS,4BAA4B;AACvC,eAAO;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AACA,SAAS,yBAAyB,SAAS,SAAS;AAClD,MAAIE,YAAW,QAAQ,yBAAgC;AACvD,MAAIA,WAAU;AACZ,WAAO,wBAAwBA,WAAU,OAAO;AAAA,EAClD;AACA,SAAO,QAAQ,OAAO,MAAM,SAAY,wBAAwB,QAAQ,OAAO,GAAG,OAAO,IAAI;AAC/F;AACA,SAAS,wBAAwB,QAAQ,SAAS;AAChD,MAAI,WAAW,QAAQ;AACrB,UAAM,OAAO;AAAA,EACf,WAAW,cAAc,QAAQ;AAC/B,QAAI,UAAU,CAAC;AACf,QAAI,OAAO,YAAY;AACrB,cAAQ,oBAAoB,IAAI;AAAA,IAClC;AACA,QAAI,OAAO,QAAQ;AACjB,cAAQ,yBAAyB,IAAI;AAAA,IACvC;AACA,QAAI,OAAO,SAAS;AAClB,cAAQ,iBAAiB,IAAI;AAAA,IAC/B;AACA,UAAM,SAAS,OAAO,UAAU;AAAA,MAC9B,QAAQ,OAAO;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH,WAAW,UAAU,QAAQ;AAC3B,WAAO,OAAO;AAAA,EAChB,OAAO;AACL,UAAM,IAAI,MAAM,kCAAkC,OAAO,GAAG;AAAA,EAC9D;AACF;AACA,SAAS,iBAAiB;AACxB,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AACtC,cAAU,OAAM,QAAO;AACrB,UAAI,GAAG;AACP,UAAI;AACF,cAAM;AAAA,MACR,SAAS,GAAG;AAAA,MAAC;AAAA,IACf;AACA,aAAS,OAAM,UAAS;AACtB,UAAI,KAAK;AACT,UAAI;AACF,cAAM;AAAA,MACR,SAAS,GAAG;AAAA,MAAC;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,EACF;AACF;;;AE5bA;AACA,IAAAC,SAAuB;;;ACDvB,IAAAC,SAAuB;AACvB;;;ACDA,IAAAC,SAAuB;AAIvB,IAAM,qBAAN,cAAuC,iBAAU;AAAA,EAC/C,YAAY,OAAO;AACjB,UAAM,KAAK;AACX,SAAK,QAAQ;AAAA,MACX,OAAO,MAAM,SAAS;AAAA,MACtB,UAAU,MAAM;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO,yBAAyB,OAAO;AACrC,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,yBAAyB,OAAO,OAAO;AAU5C,QAAI,MAAM,aAAa,MAAM,UAAU;AACrC,aAAO;AAAA,QACL,OAAO,MAAM,SAAS;AAAA,QACtB,UAAU,MAAM;AAAA,MAClB;AAAA,IACF;AAMA,WAAO;AAAA,MACL,OAAO,MAAM,SAAS,MAAM;AAAA,MAC5B,UAAU,MAAM;AAAA,IAClB;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI,KAAK,MAAM,OAAO;AACpB,aAA0B,qBAAc,+BAA+B;AAAA,QACrE,OAAO,KAAK,MAAM;AAAA,QAClB,mBAAmB;AAAA,MACrB,CAAC;AAAA,IACH,OAAO;AACL,aAAO,KAAK,MAAM;AAAA,IACpB;AAAA,EACF;AACF;AAKA,SAAS,8BAA8B;AAAA,EACrC;AAAA,EACA;AACF,GAAG;AACD,UAAQ,MAAM,KAAK;AACnB,MAAI,eAAkC,qBAAc,UAAU;AAAA,IAC5D,yBAAyB;AAAA,MACvB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,IAKV;AAAA,EACF,CAAC;AACD,MAAI,qBAAqB,KAAK,GAAG;AAC/B,WAA0B,qBAAc,eAAe;AAAA,MACrD,OAAO;AAAA,IACT,GAAsB,qBAAc,MAAM;AAAA,MACxC,OAAO;AAAA,QACL,UAAU;AAAA,MACZ;AAAA,IACF,GAAG,MAAM,QAAQ,KAAK,MAAM,UAAU,GAAG,YAAY;AAAA,EACvD;AACA,MAAI;AACJ,MAAI,iBAAiB,OAAO;AAC1B,oBAAgB;AAAA,EAClB,OAAO;AACL,QAAI,cAAc,SAAS,OAAO,kBAAkB,OAAO,UAAU,YAAY,cAAc,QAAQ,MAAM,SAAS,IAAI,KAAK,UAAU,KAAK;AAC9I,oBAAgB,IAAI,MAAM,WAAW;AAAA,EACvC;AACA,SAA0B,qBAAc,eAAe;AAAA,IACrD,OAAO;AAAA,IACP;AAAA,EACF,GAAsB,qBAAc,MAAM;AAAA,IACxC,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF,GAAG,mBAAmB,GAAsB,qBAAc,OAAO;AAAA,IAC/D,OAAO;AAAA,MACL,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,EACF,GAAG,cAAc,KAAK,GAAG,YAAY;AACvC;AACA,SAAS,cAAc;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI;AACJ,MAAI;AAAA,IACF;AAAA,EACF,IAAI,gBAAgB;AAkBpB,OAAK,qBAAqB,aAAa,UAAU,QAAQ,uBAAuB,UAAU,mBAAmB,UAAU,CAAC,mBAAmB;AACzI,WAAO;AAAA,EACT;AACA,SAA0B,qBAAc,QAAQ;AAAA,IAC9C,MAAM;AAAA,EACR,GAAsB,qBAAc,QAAQ,MAAyB,qBAAc,QAAQ;AAAA,IACzF,SAAS;AAAA,EACX,CAAC,GAAsB,qBAAc,QAAQ;AAAA,IAC3C,MAAM;AAAA,IACN,SAAS;AAAA,EACX,CAAC,GAAsB,qBAAc,SAAS,MAAM,KAAK,CAAC,GAAsB,qBAAc,QAAQ,MAAyB,qBAAc,QAAQ;AAAA,IACnJ,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,EACF,GAAG,UAAU,gBAAmC,qBAAc,SAAS,IAAI,IAAI,IAAI,CAAC,CAAC;AACvF;;;ACnJA,IAAAC,SAAuB;AAOvB,SAAS,kCAAkC;AACzC,SAA0B,qBAAc,eAAe;AAAA,IACrD,OAAO;AAAA,IACP,eAAe;AAAA,EACjB,GAAsB,qBAAc,UAAU;AAAA,IAC5C,yBAAyB;AAAA,MACvB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQV;AAAA,EACF,CAAC,CAAC;AACJ;;;AFPA,SAAS,sBAAsB,UAAU;AACvC,MAAI,SAAS,CAAC;AACd,SAAO,OAAO,QAAQ,EAAE,QAAQ,WAAS;AACvC,QAAI,WAAW,MAAM,YAAY;AACjC,QAAI,CAAC,OAAO,QAAQ,GAAG;AACrB,aAAO,QAAQ,IAAI,CAAC;AAAA,IACtB;AACA,WAAO,QAAQ,EAAE,KAAK,KAAK;AAAA,EAC7B,CAAC;AACD,SAAO;AACT;AACA,SAAS,mBAAmB,OAAO,aAAa,WAAW;AACzD,MAAIC,aAAY,wBAAwB,WAAW;AAEnD,MAAI,kBAAkB,YAAY,oBAAoB,CAAC,aAAa,MAAM,OAAO,UAAU,YAAY,kBAAkB,MAAM,OAAO,SAAS,kCAAkC;AACjL,MAAI,gBAAgB,YAAY,gBAAgB,YAAY,gBAAgB,MAAM,OAAO,SAAS,MAAyB,qBAAc,+BAA+B;AAAA,IACtK,OAAO,cAAc;AAAA,EACvB,CAAC,IAAI;AACL,MAAI,MAAM,OAAO,UAAU,YAAY,QAAQ;AAC7C,WAAO;AAAA,MACL,GAAIA,aAAY;AAAA,QACd,SAA4B,qBAAc,YAAY,QAAQ,MAAyB,qBAAcA,YAAW,IAAI,CAAC;AAAA,MACvH,IAAI;AAAA,QACF,WAAAA;AAAA,MACF;AAAA,MACA,GAAI,gBAAgB;AAAA,QAClB,cAAiC,qBAAc,YAAY,QAAQ,MAAyB,qBAAc,eAAe,IAAI,CAAC;AAAA,MAChI,IAAI;AAAA,QACF;AAAA,MACF;AAAA,MACA,GAAI,kBAAkB;AAAA,QACpB,wBAA2C,qBAAc,YAAY,QAAQ,MAAyB,qBAAc,iBAAiB,IAAI,CAAC;AAAA,MAC5I,IAAI;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,WAAAA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,UAAU,cAAc,QAAQ,WAAW,WAAW,IAAI,mBAAmB,sBAAsB,QAAQ,GAAG,qBAAqB,QAAQ,QAAQ;AAAA,EAC7K,WAAW,MAAM;AACnB,CAAC,GAAG;AACF,UAAQ,iBAAiB,QAAQ,KAAK,CAAC,GAAG,IAAI,WAAS;AACrD,QAAI,cAAc,aAAa,MAAM,EAAE;AACvC,IAAAC,WAAU,aAAa,oDAAoD;AAC3E,QAAI,YAAY;AAAA,MACd,GAAG,mBAAmB,OAAO,aAAa,SAAS;AAAA,MACnD,eAAe,MAAM;AAAA,MACrB,IAAI,MAAM;AAAA,MACV,OAAO,MAAM;AAAA,MACb,MAAM,MAAM;AAAA,MACZ,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,MAKpB,MAAM,YAAY,MAAM,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA,MAK7C,QAAQ,MAAM,aAAa,MAAM,kBAAkB,MAAM,OAAO;AAAA;AAAA;AAAA,IAGlE;AACA,QAAI,WAAW,mBAAmB,UAAU,cAAc,QAAQ,WAAW,MAAM,IAAI,kBAAkB,kBAAkB;AAC3H,QAAI,SAAS,SAAS,EAAG,WAAU,WAAW;AAC9C,WAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,4CAA4C,mBAAmB,UAAU,mBAAmB,cAAc,QAAQ,WAAW;AACpI,SAAO,mBAAmB,UAAU,mBAAmB,cAAc,QAAQ,WAAW,IAAI,sBAAsB,QAAQ,GAAG,iBAAiB;AAChJ;AACA,SAAS,gCAAgC,MAAM,OAAO,WAAW;AAC/D,MAAI,WAAW;AACb,QAAIC,MAAK,SAAS,WAAW,mBAAmB;AAChD,QAAIC,OAAM,mBAAmBD,GAAE,2BAA2B,MAAM,EAAE;AAClE,YAAQ,MAAMC,IAAG;AACjB,UAAM,IAAI,kBAAyB,KAAK,eAAe,IAAI,MAAMA,IAAG,GAAG,IAAI;AAAA,EAC7E;AACA,MAAI,KAAK,SAAS,WAAW,mBAAmB;AAChD,MAAI,MAAM,0BAA0B,EAAE,2CAAgD,IAAI,eAAe,MAAM,EAAE;AACjH,MAAI,SAAS,YAAY,CAAC,MAAM,aAAa,SAAS,YAAY,CAAC,MAAM,WAAW;AAClF,YAAQ,MAAM,GAAG;AACjB,UAAM,IAAI,kBAAyB,KAAK,eAAe,IAAI,MAAM,GAAG,GAAG,IAAI;AAAA,EAC7E;AACF;AACA,SAAS,qBAAqB,MAAM,SAAS;AAC3C,MAAI,UAAU,SAAS,iBAAiB,MAAM;AAC9C,MAAI,MAAM,UAAU,OAAO,mBAAmB,OAAO,IAAI,IAAI,iEAAsE,OAAO,MAAM,IAAI;AACpJ,UAAQ,MAAM,GAAG;AACjB,QAAM,IAAI,kBAAyB,KAAK,sBAAsB,IAAI,MAAM,GAAG,GAAG,IAAI;AACpF;AACA,SAAS,mBAAmB,UAAU,mBAAmB,cAAc,QAAQ,WAAW,WAAW,IAAI,mBAAmB,sBAAsB,QAAQ,GAAG,mBAAmB;AAC9K,UAAQ,iBAAiB,QAAQ,KAAK,CAAC,GAAG,IAAI,WAAS;AACrD,QAAI,cAAc,kBAAkB,MAAM,EAAE;AAK5C,mBAAe,iCAAiC,SAAS,QAAQ,aAAa;AAC5E,UAAI,OAAO,gBAAgB,YAAY;AACrC,YAAIC,UAAS,MAAM,YAAY;AAC/B,eAAOA;AAAA,MACT;AACA,UAAI,SAAS,MAAM,mBAAmB,SAAS,KAAK;AACpD,aAAO,SAAS,qBAAqB,MAAM,IAAI;AAAA,IACjD;AACA,aAAS,kBAAkB,SAAS,QAAQ,aAAa;AACvD,UAAI,CAAC,MAAM,UAAW,QAAO,QAAQ,QAAQ,IAAI;AACjD,aAAO,iCAAiC,SAAS,QAAQ,WAAW;AAAA,IACtE;AACA,aAAS,kBAAkB,SAAS,QAAQ,aAAa;AACvD,UAAI,CAAC,MAAM,WAAW;AACpB,cAAM,qBAAqB,UAAU,MAAM,EAAE;AAAA,MAC/C;AACA,aAAO,iCAAiC,SAAS,QAAQ,WAAW;AAAA,IACtE;AACA,mBAAe,6BAA6B,SAAS;AAKnD,UAAI,eAAe,kBAAkB,MAAM,EAAE;AAC7C,UAAI,sBAAsB,eAAe,mBAAmB,OAAO,YAAY,IAAI,QAAQ,QAAQ;AACnG,UAAI;AACF,eAAO,QAAQ;AAAA,MACjB,UAAE;AACA,cAAM;AAAA,MACR;AAAA,IACF;AACA,QAAI,YAAY;AAAA,MACd,IAAI,MAAM;AAAA,MACV,OAAO,MAAM;AAAA,MACb,MAAM,MAAM;AAAA,IACd;AACA,QAAI,aAAa;AACf,UAAI,uBAAuB,sBAAsB;AAEjD,aAAO,OAAO,WAAW;AAAA,QACvB,GAAG;AAAA,QACH,GAAG,mBAAmB,OAAO,aAAa,SAAS;AAAA,QACnD,QAAQ,YAAY;AAAA,QACpB,kBAAkB,4BAA4B,QAAQ,aAAa,MAAM,IAAI,iBAAiB;AAAA,MAChG,CAAC;AACD,UAAI,cAAc,iBAAiB,QAAQ,iBAAiB,SAAS,UAAU,wBAAwB,aAAa,gBAAgB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,MAAM,EAAE;AACtN,UAAI,eAAe,iBAAiB,QAAQ,iBAAiB,SAAS,UAAU,uBAAuB,aAAa,YAAY,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,MAAM,EAAE;AAChN,UAAI,qBAAqB,qBAAqB,WAAW,wBAAwB,YAAY,kBAAkB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,aAAa,QAAQ,CAAC,MAAM;AACrN,gBAAU,SAAS,OAAO;AAAA,QACxB;AAAA,QACA;AAAA,MACF,GAAG,gBAAgB;AACjB,YAAI;AACF,cAAI,SAAS,MAAM,6BAA6B,YAAY;AAC1D,YAAAH,WAAU,aAAa,sDAAsD;AAC7E,gBAAI,CAAC,YAAY,cAAc;AAC7B,kBAAI,UAAW,QAAO;AAEtB,qBAAO,kBAAkB,SAAS,OAAO,WAAW;AAAA,YACtD;AACA,mBAAO,YAAY,aAAa;AAAA,cAC9B;AAAA,cACA;AAAA,cACA,MAAM,eAAe;AACnB,gDAAgC,UAAU,OAAO,SAAS;AAG1D,oBAAI,oBAAoB;AACtB,sBAAI,gBAAgB,QAAW;AAC7B,2BAAO;AAAA,kBACT;AACA,sBAAI,iBAAiB,QAAW;AAC9B,0BAAM;AAAA,kBACR;AACA,yBAAO;AAAA,gBACT;AAGA,uBAAO,kBAAkB,SAAS,MAAM,WAAW;AAAA,cACrD;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AACD,iBAAO;AAAA,QACT,UAAE;AAGA,+BAAqB;AAAA,QACvB;AAAA,MACF;AAGA,gBAAU,OAAO,UAAU,yBAAyB,OAAO,aAAa,SAAS;AACjF,gBAAU,SAAS,CAAC;AAAA,QAClB;AAAA,QACA;AAAA,MACF,GAAG,gBAAgB;AACjB,eAAO,6BAA6B,YAAY;AAC9C,UAAAA,WAAU,aAAa,sDAAsD;AAC7E,cAAI,CAAC,YAAY,cAAc;AAC7B,gBAAI,WAAW;AACb,oBAAM,qBAAqB,gBAAgB,MAAM,EAAE;AAAA,YACrD;AACA,mBAAO,kBAAkB,SAAS,OAAO,WAAW;AAAA,UACtD;AACA,iBAAO,YAAY,aAAa;AAAA,YAC9B;AAAA,YACA;AAAA,YACA,MAAM,eAAe;AACnB,8CAAgC,UAAU,OAAO,SAAS;AAC1D,qBAAO,kBAAkB,SAAS,MAAM,WAAW;AAAA,YACrD;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AAIL,UAAI,CAAC,MAAM,iBAAiB;AAC1B,kBAAU,SAAS,CAAC;AAAA,UAClB;AAAA,QACF,GAAG,gBAAgB,6BAA6B,MAAM;AACpD,cAAI,UAAW,QAAO,QAAQ,QAAQ,IAAI;AAC1C,iBAAO,kBAAkB,SAAS,OAAO,WAAW;AAAA,QACtD,CAAC;AAAA,MACH;AACA,UAAI,CAAC,MAAM,iBAAiB;AAC1B,kBAAU,SAAS,CAAC;AAAA,UAClB;AAAA,QACF,GAAG,gBAAgB,6BAA6B,MAAM;AACpD,cAAI,WAAW;AACb,kBAAM,qBAAqB,gBAAgB,MAAM,EAAE;AAAA,UACrD;AACA,iBAAO,kBAAkB,SAAS,OAAO,WAAW;AAAA,QACtD,CAAC;AAAA,MACH;AAGA,gBAAU,OAAO,YAAY;AAC3B,YAAI,MAAM,MAAM,iCAAiC,OAAO,iBAAiB;AACzE,YAAI,YAAY;AAAA,UACd,GAAG;AAAA,QACL;AACA,YAAI,IAAI,cAAc;AACpB,cAAI,eAAe,IAAI;AACvB,oBAAU,SAAS,CAAC,MAAM,gBAAgB,aAAa;AAAA,YACrD,GAAG;AAAA,YACH,MAAM,eAAe;AACnB,8CAAgC,UAAU,OAAO,SAAS;AAC1D,qBAAO,kBAAkB,KAAK,SAAS,MAAM,WAAW;AAAA,YAC1D;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,IAAI,cAAc;AACpB,cAAI,eAAe,IAAI;AACvB,oBAAU,SAAS,CAAC,MAAM,gBAAgB,aAAa;AAAA,YACrD,GAAG;AAAA,YACH,MAAM,eAAe;AACnB,8CAAgC,UAAU,OAAO,SAAS;AAC1D,qBAAO,kBAAkB,KAAK,SAAS,MAAM,WAAW;AAAA,YAC1D;AAAA,UACF,CAAC;AAAA,QACH;AACA,eAAO;AAAA,UACL,GAAI,UAAU,SAAS;AAAA,YACrB,QAAQ,UAAU;AAAA,UACpB,IAAI,CAAC;AAAA,UACL,GAAI,UAAU,SAAS;AAAA,YACrB,QAAQ,UAAU;AAAA,UACpB,IAAI,CAAC;AAAA,UACL,kBAAkB,UAAU;AAAA,UAC5B,kBAAkB,4BAA4B,QAAQ,WAAW,MAAM,IAAI,iBAAiB;AAAA,UAC5F,QAAQ,UAAU;AAAA;AAAA;AAAA,UAGlB,WAAW,UAAU;AAAA,UACrB,eAAe,UAAU;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW,mBAAmB,UAAU,mBAAmB,cAAc,QAAQ,WAAW,MAAM,IAAI,kBAAkB,iBAAiB;AAC7I,QAAI,SAAS,SAAS,EAAG,WAAU,WAAW;AAC9C,WAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,4BAA4B,QAAQ,OAAO,SAAS,mBAAmB;AAE9E,MAAI,mBAAmB;AACrB,WAAO,2BAA2B,SAAS,MAAM,kBAAkB,iBAAiB;AAAA,EACtF;AAIA,MAAI,OAAO,kBAAkB,MAAM,kBAAkB;AACnD,QAAI,KAAK,MAAM;AACf,WAAO,UAAQ,GAAG;AAAA,MAChB,GAAG;AAAA,MACH,yBAAyB;AAAA,IAC3B,CAAC;AAAA,EACH;AACA,SAAO,MAAM;AACf;AAIA,SAAS,2BAA2B,SAAS,uBAAuB,mBAAmB;AACrF,MAAI,sBAAsB;AAC1B,SAAO,SAAO;AACZ,QAAI,CAAC,qBAAqB;AACxB,4BAAsB;AACtB,aAAO,kBAAkB,IAAI,OAAO;AAAA,IACtC;AACA,WAAO,wBAAwB,sBAAsB,GAAG,IAAI,IAAI;AAAA,EAClE;AACF;AACA,eAAe,iCAAiC,OAAO,cAAc;AACnE,MAAI,cAAc,MAAM,gBAAgB,OAAO,YAAY;AAC3D,QAAM,mBAAmB,OAAO,WAAW;AAI3C,SAAO;AAAA,IACL,WAAW,wBAAwB,WAAW;AAAA,IAC9C,eAAe,YAAY;AAAA,IAC3B,cAAc,YAAY;AAAA,IAC1B,cAAc,YAAY;AAAA,IAC1B,QAAQ,YAAY;AAAA,IACpB,OAAO,YAAY;AAAA,IACnB,MAAM,YAAY;AAAA,IAClB,kBAAkB,YAAY;AAAA,EAChC;AACF;AACA,eAAe,mBAAmB,SAAS,OAAO;AAChD,MAAI,SAAS,MAAM,UAAU,SAAS,MAAM,EAAE;AAC9C,MAAI,kBAAkB,OAAO;AAC3B,UAAM;AAAA,EACR;AACA,MAAI,mBAAmB,MAAM,GAAG;AAC9B,UAAM,YAAY,MAAM;AAAA,EAC1B;AACA,MAAI,gBAAgB,MAAM,GAAG;AAC3B,UAAM;AAAA,EACR;AACA,MAAI,mBAAmB,MAAM,KAAK,OAAO,MAAM;AAC7C,WAAO,MAAM,4BAA4B,OAAO,IAAI;AAAA,EACtD;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,QAAQ;AACpC,MAAI,eAAe,MAAM,GAAG;AAC1B,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,WAAW,MAAM,GAAG;AACtB,QAAI,cAAc,OAAO,QAAQ,IAAI,cAAc;AAGnD,QAAI,eAAe,wBAAwB,KAAK,WAAW,GAAG;AAC5D,aAAO,OAAO,KAAK;AAAA,IACrB,OAAO;AACL,aAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,YAAY,UAAU;AAC7B,MAAI,SAAS,SAAS,SAAS,QAAQ,IAAI,gBAAgB,GAAG,EAAE,KAAK;AACrE,MAAI,MAAM,SAAS,QAAQ,IAAI,kBAAkB;AACjD,MAAI,UAAU,CAAC;AACf,MAAI,aAAa,SAAS,QAAQ,IAAI,oBAAoB;AAC1D,MAAI,YAAY;AACd,YAAQ,oBAAoB,IAAI;AAAA,EAClC;AACA,MAAI,iBAAiB,SAAS,QAAQ,IAAI,yBAAyB;AACnE,MAAI,gBAAgB;AAClB,YAAQ,yBAAyB,IAAI;AAAA,EACvC;AACA,MAAII,WAAU,SAAS,QAAQ,IAAI,iBAAiB;AACpD,MAAIA,UAAS;AACX,YAAQ,iBAAiB,IAAIA;AAAA,EAC/B;AACA,SAAO,SAAS,KAAK;AAAA,IACnB;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAMA,SAAS,wBAAwB,aAAa;AAC5C,MAAI,YAAY,WAAW,KAAM,QAAO;AACxC,MAAI,gBAAgB,OAAO,YAAY,YAAY,YAAY,OAAO,KAAK,YAAY,OAAO,EAAE,WAAW;AAC3G,MAAI,CAAC,eAAe;AAClB,WAAO,YAAY;AAAA,EACrB;AACF;AACA,SAAS,yBAAyB,OAAO,aAAa,WAAW;AAC/D,SAAO,aAAa,MAAM,OAAO,UAAU,YAAY,gBAAgB,SAAS,YAAY,aAAa,YAAY,QAAQ,MAAM,cAAc;AACnJ;;;AD9ZA,IAAM,YAAY,oBAAI,IAAI;AAI1B,IAAM,yBAAyB;AAC/B,IAAM,kBAAkB,oBAAI,IAAI;AAIhC,IAAM,YAAY;AAClB,SAAS,kBAAkB,QAAQ,WAAW;AAC5C,SAAO,OAAO,0BAA0B,QAAQ,CAAC;AACnD;AACA,SAAS,mBAAmB,UAAUC,SAAQ;AAE5C,MAAI,WAAW,IAAI,IAAIA,QAAO,MAAM,QAAQ,IAAI,OAAK,EAAE,MAAM,EAAE,CAAC;AAChE,MAAI,WAAWA,QAAO,MAAM,SAAS,SAAS,MAAM,GAAG,EAAE,OAAO,OAAO;AACvE,MAAI,QAAQ,CAAC,GAAG;AAGhB,WAAS,IAAI;AAIb,SAAO,SAAS,SAAS,GAAG;AAC1B,UAAM,KAAK,IAAI,SAAS,KAAK,GAAG,CAAC,EAAE;AACnC,aAAS,IAAI;AAAA,EACf;AACA,QAAM,QAAQ,UAAQ;AACpB,QAAI,UAAU,YAAYA,QAAO,QAAQ,MAAMA,QAAO,QAAQ;AAC9D,QAAI,SAAS;AACX,cAAQ,QAAQ,OAAK,SAAS,IAAI,EAAE,MAAM,EAAE,CAAC;AAAA,IAC/C;AAAA,EACF,CAAC;AACD,MAAI,gBAAgB,CAAC,GAAG,QAAQ,EAAE,OAAO,CAAC,KAAK,OAAO,OAAO,OAAO,KAAK;AAAA,IACvE,CAAC,EAAE,GAAG,SAAS,OAAO,EAAE;AAAA,EAC1B,CAAC,GAAG,CAAC,CAAC;AACN,SAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQ;AAAA,EACV;AACF;AACA,SAAS,mCAAmC,UAAU,cAAc,QAAQ,WAAW,UAAU;AAC/F,MAAI,CAAC,kBAAkB,QAAQ,SAAS,GAAG;AACzC,WAAO;AAAA,EACT;AACA,SAAO,OAAO;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,gBAAgB,IAAI,IAAI,GAAG;AAC7B;AAAA,IACF;AACA,UAAM,6BAA6B,CAAC,IAAI,GAAG,UAAU,cAAc,QAAQ,WAAW,UAAU,OAAO,MAAM;AAAA,EAC/G;AACF;AACA,SAAS,qBAAqBA,SAAQ,UAAU,cAAc,QAAQ,WAAW;AAC/E,EAAM,iBAAU,MAAM;AACpB,QAAI;AAEJ,QAAI,CAAC,kBAAkB,QAAQ,SAAS,OAAO,wBAAwB,UAAU,gBAAgB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,cAAc,MAAM;AAC7L;AAAA,IACF;AAGA,aAAS,gBAAgB,IAAI;AAC3B,UAAI,OAAO,GAAG,YAAY,SAAS,GAAG,aAAa,QAAQ,IAAI,GAAG,aAAa,MAAM;AACrF,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AACA,UAAI,MAAM,IAAI,IAAI,MAAM,OAAO,SAAS,MAAM;AAC9C,UAAI,CAAC,gBAAgB,IAAI,IAAI,QAAQ,GAAG;AACtC,kBAAU,IAAI,IAAI,QAAQ;AAAA,MAC5B;AAAA,IACF;AAGA,mBAAe,eAAe;AAC5B,UAAI,YAAY,MAAM,KAAK,UAAU,KAAK,CAAC,EAAE,OAAO,UAAQ;AAC1D,YAAI,gBAAgB,IAAI,IAAI,GAAG;AAC7B,oBAAU,OAAO,IAAI;AACrB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AACD,UAAI,UAAU,WAAW,GAAG;AAC1B;AAAA,MACF;AACA,UAAI;AACF,cAAM,6BAA6B,WAAW,UAAU,cAAc,QAAQ,WAAWA,QAAO,UAAUA,QAAO,WAAW;AAAA,MAC9H,SAAS,GAAG;AACV,gBAAQ,MAAM,oCAAoC,CAAC;AAAA,MACrD;AAAA,IACF;AAGA,aAAS,KAAK,iBAAiB,uCAAuC,EAAE,QAAQ,QAAM,gBAAgB,EAAE,CAAC;AACzG,iBAAa;AAGb,QAAI,wBAAwB,SAAS,cAAc,GAAG;AACtD,aAAS,UAAU,MAAM;AACvB,aAAO,KAAK,aAAa,KAAK;AAAA,IAChC;AACA,QAAI,WAAW,IAAI,iBAAiB,aAAW;AAC7C,UAAI,WAAW,oBAAI,IAAI;AACvB,cAAQ,QAAQ,OAAK;AACnB,SAAC,EAAE,QAAQ,GAAG,EAAE,UAAU,EAAE,QAAQ,UAAQ;AAC1C,cAAI,CAAC,UAAU,IAAI,EAAG;AACtB,cAAI,KAAK,YAAY,OAAO,KAAK,aAAa,eAAe,GAAG;AAC9D,qBAAS,IAAI,IAAI;AAAA,UACnB,WAAW,KAAK,YAAY,UAAU,KAAK,aAAa,eAAe,GAAG;AACxE,qBAAS,IAAI,IAAI;AAAA,UACnB;AACA,cAAI,KAAK,YAAY,KAAK;AACxB,iBAAK,iBAAiB,uCAAuC,EAAE,QAAQ,QAAM,SAAS,IAAI,EAAE,CAAC;AAAA,UAC/F;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,eAAS,QAAQ,QAAM,gBAAgB,EAAE,CAAC;AAC1C,4BAAsB;AAAA,IACxB,CAAC;AACD,aAAS,QAAQ,SAAS,iBAAiB;AAAA,MACzC,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,iBAAiB,CAAC,iBAAiB,QAAQ,QAAQ;AAAA,IACrD,CAAC;AACD,WAAO,MAAM,SAAS,WAAW;AAAA,EACnC,GAAG,CAAC,QAAQ,WAAW,UAAU,cAAcA,OAAM,CAAC;AACxD;AACA,eAAe,6BAA6B,OAAO,UAAU,cAAc,QAAQ,WAAW,UAAU,aAAa,QAAQ;AAC3H,MAAI,eAAe,GAAG,YAAY,GAAG,cAAc,QAAQ,QAAQ,GAAG;AACtE,MAAI,MAAM,IAAI,IAAI,cAAc,OAAO,SAAS,MAAM;AACtD,QAAM,KAAK,EAAE,QAAQ,UAAQ,IAAI,aAAa,OAAO,KAAK,IAAI,CAAC;AAC/D,MAAI,aAAa,IAAI,WAAW,SAAS,OAAO;AAKhD,MAAI,IAAI,SAAS,EAAE,SAAS,WAAW;AACrC,cAAU,MAAM;AAChB;AAAA,EACF;AACA,MAAI;AACJ,MAAI;AACF,QAAI,MAAM,MAAM,MAAM,KAAK;AAAA,MACzB;AAAA,IACF,CAAC;AACD,QAAI,CAAC,IAAI,IAAI;AACX,YAAM,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,UAAU,EAAE;AAAA,IACnD,WAAW,IAAI,UAAU,KAAK;AAC5B,YAAM,IAAI,MAAM,MAAM,IAAI,KAAK,CAAC;AAAA,IAClC;AACA,oBAAgB,MAAM,IAAI,KAAK;AAAA,EACjC,SAAS,GAAG;AACV,QAAI,WAAW,QAAQ,WAAW,UAAU,OAAO,QAAS;AAC5D,UAAM;AAAA,EACR;AAGA,MAAI,cAAc,IAAI,IAAI,OAAO,KAAK,SAAS,MAAM,CAAC;AACtD,MAAI,UAAU,OAAO,OAAO,aAAa,EAAE,OAAO,CAAC,KAAK,UAAU,CAAC,YAAY,IAAI,MAAM,EAAE,IAAI,OAAO,OAAO,KAAK;AAAA,IAChH,CAAC,MAAM,EAAE,GAAG;AAAA,EACd,CAAC,IAAI,KAAK,CAAC,CAAC;AACZ,SAAO,OAAO,SAAS,QAAQ,OAAO;AAGtC,QAAM,QAAQ,OAAK,eAAe,GAAG,eAAe,CAAC;AAIrD,MAAI,YAAY,oBAAI,IAAI;AACxB,SAAO,OAAO,OAAO,EAAE,QAAQ,WAAS;AACtC,QAAI,CAAC,MAAM,YAAY,CAAC,QAAQ,MAAM,QAAQ,GAAG;AAC/C,gBAAU,IAAI,MAAM,QAAQ;AAAA,IAC9B;AAAA,EACF,CAAC;AACD,YAAU,QAAQ,cAAY,YAAY,YAAY,MAAM,mBAAmB,SAAS,cAAc,MAAM,QAAQ,WAAW,QAAQ,CAAC,CAAC;AAC3I;AACA,SAAS,eAAe,MAAM,OAAO;AACnC,MAAI,MAAM,QAAQ,wBAAwB;AACxC,QAAI,QAAQ,MAAM,OAAO,EAAE,KAAK,EAAE;AAClC,QAAI,OAAO,UAAU,SAAU,OAAM,OAAO,KAAK;AAAA,EACnD;AACA,QAAM,IAAI,IAAI;AAChB;AAIA,SAAS,SAAS,UAAU,MAAM;AAChC,MAAI;AACJ,SAAO,IAAI,SAAS;AAClB,WAAO,aAAa,SAAS;AAC7B,gBAAY,OAAO,WAAW,MAAM,SAAS,GAAG,IAAI,GAAG,IAAI;AAAA,EAC7D;AACF;;;APjMA,SAASC,wBAAuB;AAC9B,MAAI,UAAgB,kBAAW,iBAAwB;AACvD,EAAAC,WAAU,SAAS,4EAA4E;AAC/F,SAAO;AACT;AACA,SAAS,4BAA4B;AACnC,MAAI,UAAgB,kBAAW,sBAA6B;AAC5D,EAAAA,WAAU,SAAS,iFAAiF;AACpG,SAAO;AACT;AAKA,IAAM,eAAkC,qBAAc,MAAS;AAC/D,aAAa,cAAc;AAC3B,SAAS,kBAAkB;AACzB,MAAI,UAAgB,kBAAW,YAAY;AAC3C,EAAAA,WAAU,SAAS,uDAAuD;AAC1E,SAAO;AACT;AAqBA,SAAS,oBAAoB,UAAU,mBAAmB;AACxD,MAAI,CAAC,eAAe,gBAAgB,IAAU,gBAAS,KAAK;AAC5D,MAAI,CAAC,gBAAgB,iBAAiB,IAAU,gBAAS,KAAK;AAC9D,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,MAAY,cAAO,IAAI;AAC3B,EAAM,iBAAU,MAAM;AACpB,QAAI,aAAa,UAAU;AACzB,wBAAkB,IAAI;AAAA,IACxB;AACA,QAAI,aAAa,YAAY;AAC3B,UAAI,WAAW,aAAW;AACxB,gBAAQ,QAAQ,WAAS;AACvB,4BAAkB,MAAM,cAAc;AAAA,QACxC,CAAC;AAAA,MACH;AACA,UAAI,WAAW,IAAI,qBAAqB,UAAU;AAAA,QAChD,WAAW;AAAA,MACb,CAAC;AACD,UAAI,IAAI,QAAS,UAAS,QAAQ,IAAI,OAAO;AAC7C,aAAO,MAAM;AACX,iBAAS,WAAW;AAAA,MACtB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,YAAY,MAAM;AACpB,QAAI,aAAa,UAAU;AACzB,uBAAiB,IAAI;AAAA,IACvB;AAAA,EACF;AACA,MAAI,eAAe,MAAM;AACvB,QAAI,aAAa,UAAU;AACzB,uBAAiB,KAAK;AACtB,wBAAkB,KAAK;AAAA,IACzB;AAAA,EACF;AACA,EAAM,iBAAU,MAAM;AACpB,QAAI,eAAe;AACjB,UAAI,KAAK,WAAW,MAAM;AACxB,0BAAkB,IAAI;AAAA,MACxB,GAAG,GAAG;AACN,aAAO,MAAM;AACX,qBAAa,EAAE;AAAA,MACjB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,aAAa,CAAC;AAClB,SAAO,CAAC,gBAAgB,KAAK;AAAA,IAC3B,SAAS,qBAAqB,SAAS,SAAS;AAAA,IAChD,QAAQ,qBAAqB,QAAQ,YAAY;AAAA,IACjD,cAAc,qBAAqB,cAAc,SAAS;AAAA,IAC1D,cAAc,qBAAqB,cAAc,YAAY;AAAA,IAC7D,cAAc,qBAAqB,cAAc,SAAS;AAAA,EAC5D,CAAC;AACH;AACA,IAAMC,sBAAqB;AAC3B,SAAS,gBAAgB,UAAU,YAAY,gBAAgB;AAC7D,SAAO,aAAa,YAAY,CAAC,cAAc,CAAC,iBAAiB,SAAS;AAC5E;AAOA,IAAIC,WAA6B,kBAAW,CAAC;AAAA,EAC3C;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,GAAG;AACL,GAAG,iBAAiB;AAClB,MAAI,aAAa,OAAO,OAAO,YAAYD,oBAAmB,KAAK,EAAE;AACrE,MAAI,OAAO,QAAQ,EAAE;AACrB,MAAI,CAAC,gBAAgB,KAAK,gBAAgB,IAAI,oBAAoB,UAAU,KAAK;AACjF,SAA0B,qBAAoB,iBAAU,MAAyB,qBAAc,SAAWE,UAAS,CAAC,GAAG,OAAO,kBAAkB;AAAA,IAC9I,KAAK,UAAU,cAAc,GAAG;AAAA,IAChC;AAAA,IACA,iBAAiB,gBAAgB,UAAU,YAAY,MAAM,cAAc;AAAA,EAC7E,CAAC,CAAC,GAAG,kBAAkB,CAAC,aAAgC,qBAAc,mBAAmB;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,IAAI,IAAI;AACX,CAAC;AACDD,SAAQ,cAAc;AAQtB,IAAIE,QAA0B,kBAAW,CAAC;AAAA,EACxC;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,GAAG;AACL,GAAG,iBAAiB;AAClB,MAAI,aAAa,OAAO,OAAO,YAAYH,oBAAmB,KAAK,EAAE;AACrE,MAAI,OAAO,QAAQ,EAAE;AACrB,MAAI,CAAC,gBAAgB,KAAK,gBAAgB,IAAI,oBAAoB,UAAU,KAAK;AACjF,SAA0B,qBAAoB,iBAAU,MAAyB,qBAAc,MAAQE,UAAS,CAAC,GAAG,OAAO,kBAAkB;AAAA,IAC3I,KAAK,UAAU,cAAc,GAAG;AAAA,IAChC;AAAA,IACA,iBAAiB,gBAAgB,UAAU,YAAY,MAAM,cAAc;AAAA,EAC7E,CAAC,CAAC,GAAG,kBAAkB,CAAC,aAAgC,qBAAc,mBAAmB;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,IAAI,IAAI;AACX,CAAC;AACDC,MAAK,cAAc;AAOnB,IAAIC,QAA0B,kBAAW,CAAC;AAAA,EACxC,WAAW;AAAA,EACX,GAAG;AACL,GAAG,iBAAiB;AAClB,MAAI,aAAa,OAAO,MAAM,WAAW,YAAYJ,oBAAmB,KAAK,MAAM,MAAM;AACzF,SAA0B,qBAAc,MAAQE,UAAS,CAAC,GAAG,OAAO;AAAA,IAClE,KAAK;AAAA,IACL,iBAAiB,gBAAgB,UAAU,YAAY,MAAM,cAAc;AAAA,EAC7E,CAAC,CAAC;AACJ,CAAC;AACDE,MAAK,cAAc;AACnB,SAAS,qBAAqB,cAAc,YAAY;AACtD,SAAO,WAAS;AACd,oBAAgB,aAAa,KAAK;AAClC,QAAI,CAAC,MAAM,kBAAkB;AAC3B,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACF;AAOA,SAAS,iBAAiB,SAAS,QAAQ,WAAW;AACpD,MAAI,aAAa,CAAC,YAAY;AAC5B,WAAO,CAAC,QAAQ,CAAC,CAAC;AAAA,EACpB;AACA,MAAI,QAAQ;AACV,QAAI,WAAW,QAAQ,UAAU,OAAK,OAAO,EAAE,MAAM,EAAE,MAAM,MAAS;AACtE,WAAO,QAAQ,MAAM,GAAG,WAAW,CAAC;AAAA,EACtC;AACA,SAAO;AACT;AAOA,SAAS,QAAQ;AACf,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB;AACpB,MAAI;AAAA,IACF;AAAA,IACA,SAAS;AAAA,EACX,IAAI,0BAA0B;AAC9B,MAAI,UAAU,iBAAiB,eAAe,QAAQ,SAAS;AAC/D,MAAI,aAAmB,eAAQ,MAAM,wBAAwB,SAAS,cAAc,QAAQ,GAAG,CAAC,SAAS,cAAc,QAAQ,CAAC;AAChI,SAA0B,qBAAoB,iBAAU,MAAM,cAAiC,qBAAc,SAAS;AAAA,IACpH,yBAAyB;AAAA,MACvB,QAAQ;AAAA,IACV;AAAA,EACF,CAAC,IAAI,MAAM,WAAW,IAAI,CAAC;AAAA,IACzB;AAAA,IACA;AAAA,EACF,MAAM,qBAAqB,IAAI,IAAuB,qBAAc,mBAAmBF,UAAS;AAAA,IAC9F;AAAA,EACF,GAAG,IAAI,CAAC,IAAuB,qBAAc,QAAQA,UAAS;AAAA,IAC5D;AAAA,EACF,GAAG,IAAI,CAAC,CAAC,CAAC;AACZ;AAWA,SAAS,kBAAkB;AAAA,EACzB;AAAA,EACA,GAAG;AACL,GAAG;AACD,MAAI;AAAA,IACF,QAAAG;AAAA,EACF,IAAIP,sBAAqB;AACzB,MAAI,UAAgB,eAAQ,MAAM,YAAYO,QAAO,QAAQ,MAAMA,QAAO,QAAQ,GAAG,CAACA,QAAO,QAAQ,MAAMA,QAAO,QAAQ,CAAC;AAC3H,MAAI,CAAC,SAAS;AACZ,YAAQ,KAAK,qBAAqB,IAAI,yBAAyB;AAC/D,WAAO;AAAA,EACT;AACA,SAA0B,qBAAc,uBAAuBH,UAAS;AAAA,IACtE;AAAA,IACA;AAAA,EACF,GAAG,aAAa,CAAC;AACnB;AACA,SAAS,sBAAsB,SAAS;AACtC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB;AACpB,MAAI,CAAC,oBAAoB,qBAAqB,IAAU,gBAAS,CAAC,CAAC;AACnE,EAAM,iBAAU,MAAM;AACpB,QAAI,cAAc;AAClB,SAAK,sBAAsB,SAAS,UAAU,YAAY,EAAE,KAAK,WAAS;AACxE,UAAI,CAAC,aAAa;AAChB,8BAAsB,KAAK;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AACX,oBAAc;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,SAAS,UAAU,YAAY,CAAC;AACpC,SAAO;AACT;AACA,SAAS,sBAAsB;AAAA,EAC7B;AAAA,EACA,SAAS;AAAA,EACT,GAAG;AACL,GAAG;AACD,MAAI,WAAW,YAAY;AAC3B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB;AACpB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,0BAA0B;AAC9B,MAAI,oBAA0B,eAAQ,MAAM,sBAAsB,MAAM,aAAa,SAAS,UAAU,UAAU,QAAQ,MAAM,GAAG,CAAC,MAAM,aAAa,SAAS,UAAU,UAAU,MAAM,CAAC;AAC3L,MAAI,YAAkB,eAAQ,MAAM;AAClC,QAAI,CAAC,OAAO,gBAAgB;AAC1B,aAAO,iBAAiB,MAAM,mBAAmB,QAAQ;AAAA,IAC3D;AACA,QAAI,SAAS,SAAS,WAAW,SAAS,SAAS,SAAS,MAAM;AAGhE,aAAO,CAAC;AAAA,IACV;AAIA,QAAI,eAAe,oBAAI,IAAI;AAC3B,QAAI,mBAAmB;AACvB,gBAAY,QAAQ,OAAK;AACvB,UAAI;AACJ,UAAI,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,WAAW;AAC1C;AAAA,MACF;AACA,UAAI,CAAC,kBAAkB,KAAK,QAAM,GAAG,MAAM,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,MAAM,eAAe,wBAAwB,aAAa,EAAE,MAAM,EAAE,OAAO,QAAQ,0BAA0B,UAAU,sBAAsB,kBAAkB;AACtO,2BAAmB;AAAA,MACrB,WAAW,SAAS,OAAO,EAAE,MAAM,EAAE,EAAE,iBAAiB;AACtD,2BAAmB;AAAA,MACrB,OAAO;AACL,qBAAa,IAAI,EAAE,MAAM,EAAE;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,QAAI,aAAa,SAAS,GAAG;AAC3B,aAAO,CAAC;AAAA,IACV;AACA,QAAI,MAAM,eAAe,IAAI;AAI7B,QAAI,oBAAoB,aAAa,OAAO,GAAG;AAC7C,UAAI,aAAa,IAAI,WAAW,YAAY,OAAO,OAAK,aAAa,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,OAAK,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG,CAAC;AAAA,IACtH;AACA,WAAO,CAAC,IAAI,WAAW,IAAI,MAAM;AAAA,EACnC,GAAG,CAAC,OAAO,gBAAgB,YAAY,UAAU,UAAU,mBAAmB,aAAa,MAAM,YAAY,CAAC;AAC9G,MAAI,sBAA4B,eAAQ,MAAM,sBAAsB,MAAM,aAAa,SAAS,UAAU,UAAU,QAAQ,QAAQ,GAAG,CAAC,MAAM,aAAa,SAAS,UAAU,UAAU,MAAM,CAAC;AAC/L,MAAI,cAAoB,eAAQ,MAAM,mBAAmB,qBAAqB,QAAQ,GAAG,CAAC,qBAAqB,QAAQ,CAAC;AAIxH,MAAI,qBAAqB,sBAAsB,mBAAmB;AAClE,SAA0B,qBAAoB,iBAAU,MAAM,UAAU,IAAI,UAA2B,qBAAc,QAAQA,UAAS;AAAA,IACpI,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ;AAAA,EACF,GAAG,SAAS,CAAC,CAAC,GAAG,YAAY,IAAI,UAA2B,qBAAc,QAAQA,UAAS;AAAA,IACzF,KAAK;AAAA,IACL,KAAK;AAAA,IACL;AAAA,EACF,GAAG,SAAS,CAAC,CAAC,GAAG,mBAAmB,IAAI,CAAC;AAAA,IACvC;AAAA,IACA;AAAA,EACF;AAAA;AAAA;AAAA,IAIM,qBAAc,QAAQA,UAAS;AAAA,MACnC;AAAA,IACF,GAAG,IAAI,CAAC;AAAA,GAAC,CAAC;AACZ;AAOA,SAAS,OAAO;AACd,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB;AACpB,MAAI;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT;AAAA,EACF,IAAI,0BAA0B;AAC9B,MAAI,WAAW,YAAY;AAC3B,MAAI,WAAW,iBAAiB,eAAe,QAAQ,SAAS;AAChE,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACV,YAAQ,OAAO,SAAS,SAAS,SAAS,CAAC,EAAE,MAAM,EAAE;AAAA,EACvD;AACA,MAAI,OAAO,CAAC;AACZ,MAAI,WAAW;AACf,MAAI,UAAU,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,QAAI,SAAS,SAAS,CAAC;AACvB,QAAI,UAAU,OAAO,MAAM;AAC3B,QAAII,QAAO,WAAW,OAAO;AAC7B,QAAI,SAAS,OAAO;AACpB,QAAI,cAAc,aAAa,OAAO;AACtC,QAAI,YAAY,CAAC;AACjB,QAAI,QAAQ;AAAA,MACV,IAAI;AAAA,MACJ,MAAAA;AAAA,MACA,MAAM,CAAC;AAAA,MACP,QAAQ,OAAO;AAAA,MACf,UAAU,OAAO;AAAA,MACjB,QAAQ,OAAO,MAAM;AAAA,MACrB;AAAA,IACF;AACA,YAAQ,CAAC,IAAI;AACb,QAAI,gBAAgB,QAAQ,gBAAgB,UAAU,YAAY,MAAM;AACtE,kBAAY,OAAO,YAAY,SAAS,aAAa,YAAY,KAAK;AAAA,QACpE,MAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC,IAAI,MAAM,QAAQ,YAAY,IAAI,IAAI,CAAC,GAAG,YAAY,IAAI,IAAI,YAAY;AAAA,IAC7E,WAAW,UAAU;AAInB,kBAAY,CAAC,GAAG,QAAQ;AAAA,IAC1B;AACA,gBAAY,aAAa,CAAC;AAC1B,QAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,YAAM,IAAI,MAAM,kBAAkB,OAAO,MAAM,OAAO,mKAA6K;AAAA,IACrO;AACA,UAAM,OAAO;AACb,YAAQ,CAAC,IAAI;AACb,WAAO,CAAC,GAAG,SAAS;AACpB,eAAW;AAAA,EACb;AACA,SAA0B,qBAAoB,iBAAU,MAAM,KAAK,KAAK,EAAE,IAAI,eAAa;AACzF,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AACA,QAAI,aAAa,WAAW;AAC1B,UAAI;AAAA,QACF;AAAA,QACA,GAAG;AAAA,MACL,IAAI;AACJ,UAAI,CAAC,eAAe,OAAO,GAAG;AAC5B,gBAAQ,KAAK,0CAA0C,OAAO,oCAAoC;AAClG,eAAO;AAAA,MACT;AACA,UAAI,OAAO;AACX,aAA0B,qBAAc,MAAMJ,UAAS;AAAA,QACrD,KAAK,KAAK,UAAU,IAAI;AAAA,MAC1B,GAAG,IAAI,CAAC;AAAA,IACV;AACA,QAAI,WAAW,WAAW;AACxB,aAA0B,qBAAc,SAAS;AAAA,QAC/C,KAAK;AAAA,MACP,GAAG,OAAO,UAAU,KAAK,CAAC;AAAA,IAC5B;AACA,QAAI,aAAa,WAAW;AAC1B,gBAAU,YAAV,UAAU,UAAY,UAAU;AAChC,aAAO,UAAU;AAAA,IACnB;AACA,QAAI,aAAa,aAAa,UAAU,WAAW,MAAM;AACvD,aAAO,OAAO,UAAU,YAAY,WAA8B,qBAAc,QAAQ;AAAA,QACtF,KAAK;AAAA,QACL,SAAS,UAAU;AAAA,MACrB,CAAC,IAAI;AAAA,IACP;AACA,QAAI,oBAAoB,WAAW;AACjC,UAAI;AACF,YAAIK,QAAO,KAAK,UAAU,UAAU,gBAAgB,CAAC;AACrD,eAA0B,qBAAc,UAAU;AAAA,UAChD,KAAK,kBAAkBA,KAAI;AAAA,UAC3B,MAAM;AAAA,UACN,yBAAyB;AAAA,YACvB,QAAQA;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAA0B,qBAAc,QAAQL,UAAS;AAAA,MACvD,KAAK,KAAK,UAAU,SAAS;AAAA,IAC/B,GAAG,SAAS,CAAC;AAAA,EACf,CAAC,CAAC;AACJ;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,OAAO,YAAY,YAAY,gBAAgB,KAAK,OAAO;AACpE;AACA,SAASM,OAAM,OAAO;AACpB,SAA0B,qBAAc,OAAS,KAAK;AACxD;AAMA,IAAI,aAAa;AAWjB,SAAS,QAAQ,OAAO;AACtB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB;AACpB,MAAI;AAAA,IACF,QAAAH;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACF,IAAIP,sBAAqB;AACzB,MAAI;AAAA,IACF,SAAS;AAAA,EACX,IAAI,0BAA0B;AAC9B,MAAI,iBAAiB,kBAAkB,QAAQ,SAAS;AAIxD,MAAI,YAAY;AACd,eAAW,mBAAmB;AAAA,EAChC;AACA,MAAI,UAAU,iBAAiB,eAAe,MAAM,SAAS;AAC7D,EAAM,iBAAU,MAAM;AACpB,iBAAa;AAAA,EACf,GAAG,CAAC,CAAC;AACL,MAAI,+BAA+B,CAAC,KAAK,UAAU;AACjD,QAAI;AACJ,QAAI,kBAAkB,iBAAiB,OAAO;AAC5C,oBAAc,eAAe,KAAK;AAAA,IACpC,OAAO;AACL,oBAAc;AAAA,IAChB;AACA,WAAO,GAAG,KAAK,UAAU,GAAG,CAAC,yBAAyB,WAAW,KAAK,UAAU,WAAW,CAAC,CAAC;AAAA,EAC/F;AACA,MAAI,8BAA8B,CAAC,SAAS,KAAKQ,UAAS;AACxD,QAAI;AACJ,QAAI;AACF,uBAAiB,KAAK,UAAUA,KAAI;AAAA,IACtC,SAAS,OAAO;AACd,aAAO,6BAA6B,KAAK,KAAK;AAAA,IAChD;AACA,WAAO,GAAG,KAAK,UAAU,GAAG,CAAC,qBAAqB,WAAW,cAAc,CAAC;AAAA,EAC9E;AACA,MAAI,oBAAoB,CAAC,SAAS,KAAK,UAAU;AAC/C,QAAI;AACJ,QAAI,kBAAkB,iBAAiB,OAAO;AAC5C,oBAAc,eAAe,KAAK;AAAA,IACpC,OAAO;AACL,oBAAc;AAAA,IAChB;AACA,WAAO,oBAAoB,KAAK,UAAU,OAAO,CAAC,KAAK,KAAK,UAAU,GAAG,CAAC,SAAS,WAAW,KAAK,UAAU,WAAW,CAAC,CAAC;AAAA,EAC5H;AACA,MAAI,mBAAmB,CAAC,SAAS,KAAKA,UAAS;AAC7C,QAAI;AACJ,QAAI;AACF,uBAAiB,KAAK,UAAUA,KAAI;AAAA,IACtC,SAAS,OAAO;AACd,aAAO,kBAAkB,SAAS,KAAK,KAAK;AAAA,IAC9C;AACA,WAAO,oBAAoB,KAAK,UAAU,OAAO,CAAC,KAAK,KAAK,UAAU,GAAG,CAAC,KAAK,WAAW,cAAc,CAAC;AAAA,EAC3G;AACA,MAAI,kBAAkB,CAAC;AACvB,MAAI,iBAAuB,eAAQ,MAAM;AACvC,QAAI;AACJ,QAAI,eAAe,OAAO;AAAA;AAAA,MAE1B;AAAA,QAA2L;AAC3L,QAAI,gBAAgB,gBAAgB,2BAA2B,mBAAmB,IAAI,YAAY,KAAK;AAGvG,QAAI,kBAAkB,OAAO,iBAAiB,SAAY,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc;AActI,qBAAiB,CAAC,kBAAkB,KAAK,CAAC,0CAA0C,qCAAqC,OAAyC,sDAAsD,uEAAuE,4BAA4B,cAAc,6BAA6B,OAAO,eAAe,MAAM,sCAAsC,gDAAgD,sDAAsD,iGAAiG,OAAO,eAAe,WAAW,6JAA6J,UAAU,OAAO,IAAI,eAAe,MAAM,8CAA8C,iCAAiC,qCAAqC,OAAyC,sDAAsD,uEAAuE,eAAe,cAAc,eAAe,OAAO,IAAI,EAAE,KAAK,IAAI,IAAI,OAAO,QAAQ,eAAe,EAAE,IAAI,CAAC,CAAC,SAAS,YAAY,MAAM;AACtvC,UAAI,cAAc,IAAI,IAAI,aAAa,WAAW;AAClD,UAAI,mBAAmB,aAAa,aAAa,IAAI,SAAO;AAC1D,YAAI,YAAY,IAAI,GAAG,GAAG;AACxB,0BAAgB,KAAyB,qBAAc,yBAAyB;AAAA,YAC9E,KAAK,GAAG,OAAO,MAAM,GAAG;AAAA,YACxB;AAAA,YACA;AAAA,YACA,SAAS;AAAA,YACT,aAAa;AAAA,YACb,eAAe;AAAA,YACf,gBAAgB;AAAA,UAClB,CAAC,CAAC;AACF,iBAAO,GAAG,KAAK,UAAU,GAAG,CAAC,qBAAqB,KAAK,UAAU,OAAO,CAAC,KAAK,KAAK,UAAU,GAAG,CAAC;AAAA,QACnG,OAAO;AACL,cAAI,iBAAiB,aAAa,KAAK,GAAG;AAC1C,cAAI,OAAO,eAAe,WAAW,aAAa;AAChD,mBAAO,6BAA6B,KAAK,eAAe,MAAM;AAAA,UAChE,OAAO;AACL,mBAAO,4BAA4B,SAAS,KAAK,eAAe,KAAK;AAAA,UACvE;AAAA,QACF;AAAA,MACF,CAAC,EAAE,KAAK,KAAK;AACb,aAAO,iDAAiD,KAAK,UAAU,OAAO,CAAC,OAAO,gBAAgB;AAAA,IACxG,CAAC,EAAE,KAAK,IAAI,KAAK,gBAAgB,SAAS,IAAI,oBAAoB,gBAAgB,MAAM,MAAM;AAC9F,QAAI,qBAAqB,CAAC,WAAW,MAAM,IAAI,gBAAgB,SAAS,SAAS,QAAQ,kBAAkB,UAAU,cAAc,UAAU,UAAU,KAAK,UAAU,SAAS,IAAI,OAAO,CAAC,MAAM,EAAE,GAAG,iBAAiB,KAAK,UAAU,KAAK,UAAU,SAAS,GAAG,CAAC,EAAE;AAAA,EACtQ,QAAQ,IAAI,CAAC,OAAO,UAAU,oBAAoB,KAAK,SAAS,KAAK,UAAU,SAAS,OAAO,MAAM,MAAM,EAAE,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAAA,EACrI;AAAA;AAAA,MAEE,4BAA4B,KAAK,UAAU,mBAAmB,UAAUD,OAAM,GAAG,MAAM,CAAC,CAAC;AAAA,QAAM,EAAE;AAAA,gCACrE,QAAQ,IAAI,CAAC,OAAO,UAAU,GAAG,KAAK,UAAU,MAAM,MAAM,EAAE,CAAC,SAAS,KAAK,EAAE,EAAE,KAAK,GAAG,CAAC;AAAA;AAAA,SAEjH,KAAK,UAAU,SAAS,MAAM,MAAM,CAAC;AAC1C,WAA0B,qBAAoB,iBAAU,MAAyB,qBAAc,UAAUH,UAAS,CAAC,GAAG,OAAO;AAAA,MAC3H,0BAA0B;AAAA,MAC1B,yBAAyB,WAAW,aAAa;AAAA,MACjD,MAAM;AAAA,IACR,CAAC,CAAC,GAAsB,qBAAc,UAAUA,UAAS,CAAC,GAAG,OAAO;AAAA,MAClE,0BAA0B;AAAA,MAC1B,yBAAyB,WAAW,kBAAkB;AAAA,MACtD,MAAM;AAAA,MACN,OAAO;AAAA,IACT,CAAC,CAAC,CAAC;AAAA,EAKL,GAAG,CAAC,CAAC;AACL,MAAI,CAAC,YAAY,OAAO,mBAAmB,YAAY,eAAe,GAAG;AACvE,aAAS,IAAI,GAAG,IAAI,eAAe,GAAG,KAAK;AACzC,sBAAgB,KAAyB,qBAAc,yBAAyB;AAAA,QAC9E,KAAK;AAAA,QACL,aAAa;AAAA,QACb,eAAe;AAAA,QACf,gBAAgB;AAAA,MAClB,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACA,MAAI,gBAAgB,QAAQ,IAAI,WAAS;AACvC,QAAI,QAAQ,SAAS,OAAO,MAAM,MAAM,EAAE;AAC1C,YAAQ,MAAM,WAAW,CAAC,GAAG,OAAO,CAAC,MAAM,MAAM,CAAC;AAAA,EACpD,CAAC,EAAE,KAAK,CAAC;AACT,MAAI,WAAW,aAAa,CAAC,IAAI,SAAS,MAAM,QAAQ,OAAO,aAAa;AAC5E,SAAO,aAAa,OAA0B,qBAAoB,iBAAU,MAAM,CAAC,iBAAoC,qBAAc,QAAQ;AAAA,IAC3I,KAAK;AAAA,IACL,MAAM,SAAS;AAAA,IACf,aAAa,MAAM;AAAA,EACrB,CAAC,IAAI,MAAyB,qBAAc,QAAQ;AAAA,IAClD,KAAK;AAAA,IACL,MAAM,SAAS,MAAM;AAAA,IACrB,aAAa,MAAM;AAAA,EACrB,CAAC,GAAG,OAAO,QAAQ,EAAE,IAAI,UAA2B,qBAAc,QAAQ;AAAA,IACxE,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,IACN,aAAa,MAAM;AAAA,EACrB,CAAC,CAAC,GAAG,gBAAgB,eAAe;AACtC;AACA,SAAS,wBAAwB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,OAAO,aAAa,eAAe,gBAAgB,WAAW,SAAS;AACzE,IAAAH,WAAU,aAAa,YAAY,SAAS,OAAO,GAAG,2BAA2B,OAAO,aAAa,OAAO,uDAAuD;AAAA,EACrK;AACA,SAA0B,qBAAoB,iBAAU;AAAA,IACtD;AAAA;AAAA;AAAA;AAAA,MAIA,OAAO,aAAa,eAAe,gBAAgB,WAAW,UAAU,OAA0B,qBAAc,UAAUG,UAAS,CAAC,GAAG,aAAa;AAAA,QAClJ,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,UACvB,QAAQ;AAAA,QACV;AAAA,MACF,CAAC,CAAC;AAAA;AAAA,EACJ,GAAG,OAAO,aAAa,eAAe,gBAAgB,WAAW,UAA6B,qBAAcM,QAAO;AAAA,IACjH,SAAS,aAAa,KAAK,OAAO;AAAA,IAClC,cAAiC,qBAAc,8BAA8B;AAAA,MAC3E;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD,UAAU,CAAAF,UAAQ;AAChB,aAA0B,qBAAc,UAAUJ,UAAS,CAAC,GAAG,aAAa;AAAA,QAC1E,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,UACvB,QAAQ,cAAc,SAAS,SAASI,KAAI;AAAA,QAC9C;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AAAA,EACF,CAAC,IAAuB,qBAAc,UAAUJ,UAAS,CAAC,GAAG,aAAa;AAAA,IACxE,OAAO;AAAA,IACP,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,MACvB,QAAQ;AAAA,IACV;AAAA,EACF,CAAC,CAAC,CAAC;AACL;AACA,SAAS,6BAA6B;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,QAAQ,cAAc;AAC1B,SAA0B,qBAAc,UAAUA,UAAS,CAAC,GAAG,aAAa;AAAA,IAC1E,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,MACvB,QAAQ,eAAe,SAAS,SAAS,KAAK;AAAA,IAChD;AAAA,EACF,CAAC,CAAC;AACJ;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAC3B;AAOA,SAASO,cAAa;AACpB,SAAO,WAAa;AACtB;AAOA,SAASC,iBAAgB;AACvB,SAAO,cAAgB;AACzB;AAOA,SAASC,oBAAmB,SAAS;AACnC,SAAO,mBAAqB,OAAO;AACrC;AAOA,SAASC,iBAAgB;AACvB,SAAO,cAAgB;AACzB;AAQA,SAASC,YAAW,OAAO,CAAC,GAAG;AAC7B,SAAO,WAAa,IAAI;AAC1B;AASA,IAAM;AAAA;AAAA;AAAA,EAGN,QAAyC,MAAM,OAAO,SAASC,YAAW;AAAA,IACxE;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV,GAAG;AAED,QAAI,eAAe,eAAe,YAAY,QAAQ;AACtD,QAAI,cAAc;AAChB,cAAQ,KAAK,CAAC,mGAAmG,IAAI,8EAA8E,0EAA0E,EAAE,KAAK,IAAI,CAAC;AACzR,aAAO;AAAA,IACT;AACA,wBAAW,QAAQ,IAAI;AACvB,QAAI,KAAK,OAAO;AAChB,WAA0B,qBAAc,UAAU;AAAA,MAChD;AAAA,MACA,0BAA0B;AAAA,MAC1B,yBAAyB;AAAA,QACvB,QAAQ;AAAA;AAAA,6CAE+B,KAAK,UAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAQ7C,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAmEF,OAAO,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWrC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,WAAS;AACd,SAAK,QAAQ,SAAO;AAClB,UAAI,OAAO,QAAQ,YAAY;AAC7B,YAAI,KAAK;AAAA,MACX,WAAW,OAAO,MAAM;AACtB,YAAI,UAAU;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;AW54BA;AAEA,SAAS,kBAAkB,QAAQ;AACjC,MAAI,CAAC,OAAQ,QAAO;AACpB,MAAI,UAAU,OAAO,QAAQ,MAAM;AACnC,MAAI,aAAa,CAAC;AAClB,WAAS,CAAC,KAAK,GAAG,KAAK,SAAS;AAG9B,QAAI,OAAO,IAAI,WAAW,sBAAsB;AAC9C,iBAAW,GAAG,IAAI,IAAI,kBAAyB,IAAI,QAAQ,IAAI,YAAY,IAAI,MAAM,IAAI,aAAa,IAAI;AAAA,IAC5G,WAAW,OAAO,IAAI,WAAW,SAAS;AAExC,UAAI,IAAI,WAAW;AACjB,YAAI,mBAAmB,OAAO,IAAI,SAAS;AAC3C,YAAI,OAAO,qBAAqB,YAAY;AAC1C,cAAI;AAEF,gBAAI,QAAQ,IAAI,iBAAiB,IAAI,OAAO;AAC5C,kBAAM,QAAQ,IAAI;AAClB,uBAAW,GAAG,IAAI;AAAA,UACpB,SAAS,GAAG;AAAA,UAEZ;AAAA,QACF;AAAA,MACF;AACA,UAAI,WAAW,GAAG,KAAK,MAAM;AAC3B,YAAI,QAAQ,IAAI,MAAM,IAAI,OAAO;AACjC,cAAM,QAAQ,IAAI;AAClB,mBAAW,GAAG,IAAI;AAAA,MACpB;AAAA,IACF,OAAO;AACL,iBAAW,GAAG,IAAI;AAAA,IACpB;AAAA,EACF;AACA,SAAO;AACT;;;AbpBA,IAAI;AACJ,IAAI;AACJ,IAAI,oBAAoB;AACxB,IAAI;AACJ,IAAI;AAIJ,IAAI,wBAAwB,IAAI,QAAQ,aAAW;AAGjD,0BAAwB;AAC1B,CAAC,EAAE,MAAM,MAAM;AAGb,SAAO;AACT,CAAC;AAGD,IAAI,eAAe,YAAY,KAAK;AAElC,cAAY,IAAI,OAAO,kBAAkB,OAAO;AAAA,IAC9C;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAIC,UAAS,MAAM;AAEnB,QAAI,CAACA,SAAQ;AACX,cAAQ,MAAM,+DAA+D;AAC7E;AAAA,IACF;AACA,QAAI,WAAW,CAAC,GAAG,IAAI,IAAIA,QAAO,MAAM,QAAQ,IAAI,OAAK,EAAE,MAAM,EAAE,EAAE,OAAO,OAAO,KAAK,OAAO,mBAAmB,CAAC,CAAC,CAAC;AACrH,QAAI,oBAAoB;AACtB,yBAAmB,MAAM;AAAA,IAC3B;AACA,yBAAqB,IAAI,gBAAgB;AACzC,QAAI,SAAS,mBAAmB;AAGhC,QAAI,kBAAkB,OAAO,OAAO,CAAC,GAAG,OAAO,qBAAqB,OAAO,aAAa,MAAM,QAAQ,IAAI,SAAS,IAAI,OAAM,OAAM;AACjI,UAAI,qBAAqB,uBAAuB,wBAAwB;AACxE,UAAI,CAAC,eAAe,OAAO,EAAE,GAAG;AAC9B,eAAO;AAAA,MACT;AACA,UAAI,WAAW,MAAM,OAAO,eAAe,OAAO,EAAE,EAAE,SAAS,OAAO,sBAAsB,eAAe,SAAS,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB,SAAS;AACnM,aAAO,CAAC,IAAI;AAAA,QACV,GAAG;AAAA;AAAA;AAAA,QAGH,SAAS,SAAS,YAAY,wBAAwB,OAAO,oBAAoB,EAAE,OAAO,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,YAAY,SAAS,UAAU,SAAS;AAAA,QAC5M,eAAe,SAAS,kBAAkB,yBAAyB,OAAO,oBAAoB,EAAE,OAAO,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,kBAAkB,SAAS,gBAAgB,SAAS;AAAA,QACvO,iBAAiB,SAAS,oBAAoB,yBAAyB,OAAO,oBAAoB,EAAE,OAAO,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,oBAAoB,SAAS,kBAAkB,SAAS;AAAA,MACjP,CAAC;AAAA,IACH,CAAC,CAAC,GAAG,OAAO,OAAO,CAAC,CAAC;AACrB,WAAO,OAAO,OAAO,qBAAqB,eAAe;AAEzD,QAAI,SAAS,4CAA4C,mBAAmB,eAAe,QAAQ,OAAO,qBAAqB,OAAO,eAAe,OAAO,OAAO,eAAe,QAAQ,OAAO,eAAe,SAAS;AAGzN,IAAAA,QAAO,mBAAmB,MAAM;AAIhC,QAAI,QAAQA,QAAO,UAAU,WAAS;AACpC,UAAI,MAAM,iBAAiB,QAAQ;AACjC,cAAM;AAGN,YAAI,OAAO,QAAS;AAEpB,mBAAW,MAAM;AACf,iBAAO,OAAO,OAAO,iBAAiB,cAAc;AACpD,iBAAO,iBAAiB,oBAAoB;AAAA,QAC9C,GAAG,CAAC;AAAA,MACN;AAAA,IACF,CAAC;AACD,WAAO,uBAAuB,OAAO,uBAAuB,KAAK;AACjE,IAAAA,QAAO,WAAW;AAAA,EACpB,CAAC;AACH;AAOA,SAAS,aAAa,QAAQ;AAC5B,MAAI,CAAC,QAAQ;AAGX,QAAI,OAAO,eAAe,OAAO,gBAAgB;AAI/C,UAAI,CAAC,sBAAsB;AACzB,YAAI,SAAS,OAAO,eAAe;AACnC,QAAAC,WAAU,QAAQ,2CAA2C;AAC7D,eAAO,eAAe,SAAS;AAC/B,+BAAuB,qBAAqB,QAAQ,MAAM,EAAE,KAAK,WAAS;AACxE,iBAAO,eAAe,QAAQ,MAAM;AACpC,+BAAqB,QAAQ;AAAA,QAC/B,CAAC,EAAE,MAAM,OAAK;AACZ,+BAAqB,QAAQ;AAAA,QAC/B,CAAC;AAAA,MACH;AACA,UAAI,qBAAqB,OAAO;AAC9B,cAAM,qBAAqB;AAAA,MAC7B;AACA,UAAI,CAAC,qBAAqB,OAAO;AAC/B,cAAM;AAAA,MACR;AAAA,IACF;AACA,QAAI,SAAS,mBAAmB,OAAO,gBAAgB,QAAQ,OAAO,qBAAqB,OAAO,eAAe,OAAO,OAAO,eAAe,QAAQ,OAAO,eAAe,SAAS;AACrL,QAAI,gBAAgB;AACpB,QAAI,CAAC,OAAO,eAAe,WAAW;AAOpC,sBAAgB;AAAA,QACd,GAAG,OAAO,eAAe;AAAA,QACzB,YAAY;AAAA,UACV,GAAG,OAAO,eAAe,MAAM;AAAA,QACjC;AAAA,MACF;AACA,UAAI,iBAAiB,YAAY,QAAQ,OAAO,UAAU,OAAO,eAAe,QAAQ;AACxF,UAAI,gBAAgB;AAClB,iBAAS,SAAS,gBAAgB;AAChC,cAAI,UAAU,MAAM,MAAM;AAC1B,cAAI,QAAQ,OAAO,oBAAoB,OAAO;AAC9C,cAAI,gBAAgB,OAAO,gBAAgB,OAAO,OAAO;AAKzD,cAAI,SAAS,yBAAyB,eAAe,OAAO,OAAO,eAAe,SAAS,MAAM,MAAM,mBAAmB,CAAC,cAAc,YAAY;AACnJ,0BAAc,WAAW,OAAO,IAAI;AAAA,UACtC,WAAW,iBAAiB,CAAC,cAAc,WAAW;AAMpD,0BAAc,WAAW,OAAO,IAAI;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AACA,UAAI,iBAAiB,cAAc,QAAQ;AACzC,sBAAc,SAAS,kBAAkB,cAAc,MAAM;AAAA,MAC/D;AAAA,IACF;AAIA,aAAS,aAAa;AAAA,MACpB;AAAA,MACA,SAAS,qBAAqB;AAAA,MAC9B,UAAU,OAAO,eAAe;AAAA,MAChC,QAAQ;AAAA,QACN,wBAAwB;AAAA,QACxB,mBAAmB,OAAO,eAAe,OAAO;AAAA,QAChD,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,sBAAsB,OAAO,eAAe,OAAO;AAAA;AAAA,QAEnD,gCAAgC,OAAO,eAAe,OAAO,mBAAmB;AAAA,MAClF;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,OAAO,eAAe,OAAO,iBAAiB,2BAA2B,OAAO,iBAAiB,OAAO,qBAAqB,MAAM,MAAM,IAAI;AAAA,MAC3J,yBAAyB,mCAAmC,OAAO,iBAAiB,OAAO,qBAAqB,OAAO,eAAe,QAAQ,OAAO,eAAe,WAAW,OAAO,eAAe,QAAQ;AAAA,IAC/M,CAAC;AAID,QAAI,OAAO,MAAM,aAAa;AAC5B,0BAAoB;AACpB,aAAO,WAAW;AAAA,IACpB;AAGA,WAAO,qBAAqB;AAC5B,WAAO,gBAAgB;AAGvB,QAAI,uBAAuB;AACzB,4BAAsB,MAAM;AAAA,IAC9B;AAAA,EACF;AAMA,MAAI,CAAC,aAAa,cAAc,IAAU,gBAAS,OAAyC,OAAO,eAAe,cAAc,MAAS;AACzI,MAAI,MAAwC;AAC1C,WAAO,0BAA0B,MAAM,eAAe,MAAS;AAAA,EACjE;AAMA,MAAI,CAAC,UAAU,WAAW,IAAU,gBAAS,OAAO,MAAM,QAAQ;AAClE,EAAM,uBAAgB,MAAM;AAG1B,QAAI,CAAC,mBAAmB;AACtB,0BAAoB;AACpB,aAAO,WAAW;AAAA,IACpB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,EAAM,uBAAgB,MAAM;AAC1B,WAAO,OAAO,UAAU,cAAY;AAClC,UAAI,SAAS,aAAa,UAAU;AAClC,oBAAY,SAAS,QAAQ;AAAA,MAC/B;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,QAAQ,CAAC;AACb,uBAAqB,QAAQ,OAAO,iBAAiB,OAAO,qBAAqB,OAAO,eAAe,QAAQ,OAAO,eAAe,SAAS;AAM9I;AAAA;AAAA;AAAA,IAIQ,qBAAoB,iBAAU,MAAyB,qBAAc,aAAa,UAAU;AAAA,MAChG,OAAO;AAAA,QACL,UAAU,OAAO;AAAA,QACjB,cAAc,OAAO;AAAA,QACrB,QAAQ,OAAO,eAAe;AAAA,QAC9B;AAAA,QACA,WAAW,OAAO,eAAe;AAAA,MACnC;AAAA,IACF,GAAsB,qBAAc,oBAAoB;AAAA,MACtD;AAAA,IACF,GAAsB,qBAAc,gBAAgB;AAAA,MAClD;AAAA,MACA,iBAAiB;AAAA,MACjB,QAAQ;AAAA,QACN,oBAAoB;AAAA,MACtB;AAAA,IACF,CAAC,CAAC,CAAC,GAAG,OAAO,eAAe,OAAO,iBAAoC,qBAAoB,iBAAU,IAAI,IAAI,IAAI;AAAA;AAErH;;;AcxQA,IAAAC,UAAuB;AAIvB,IAAI,cAAc;AAQlB,SAASC,mBAAkB;AAAA,EACzB;AAAA,EACA,GAAG;AACL,GAAG;AACD,MAAI;AAAA,IACF;AAAA,EACF,IAAI,gBAAgB;AACpB,MAAI,WAAW,YAAY;AAC3B,MAAI,UAAU,WAAW;AACzB,uBAA4B;AAAA,IAC1B;AAAA,IACA,YAAY;AAAA,EACd,CAAC;AAQD,MAAI,MAAY;AAAA,IAAQ,MAAM;AAC5B,UAAI,CAAC,OAAQ,QAAO;AACpB,UAAI,UAAU,OAAO,UAAU,OAAO;AACtC,aAAO,YAAY,SAAS,MAAM,UAAU;AAAA,IAC9C;AAAA;AAAA;AAAA,IAGA,CAAC;AAAA,EAAC;AAIF,MAAI,WAAW;AACb,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,CAACC,cAAa,eAAe;AAChD,QAAI,CAAC,OAAO,QAAQ,SAAS,CAAC,OAAO,QAAQ,MAAM,KAAK;AACtD,UAAIC,OAAM,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AAC5C,aAAO,QAAQ,aAAa;AAAA,QAC1B,KAAAA;AAAA,MACF,GAAG,EAAE;AAAA,IACP;AACA,QAAI;AACF,UAAI,YAAY,KAAK,MAAM,eAAe,QAAQD,YAAW,KAAK,IAAI;AACtE,UAAI,UAAU,UAAU,cAAc,OAAO,QAAQ,MAAM,GAAG;AAC9D,UAAI,OAAO,YAAY,UAAU;AAC/B,eAAO,SAAS,GAAG,OAAO;AAAA,MAC5B;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,KAAK;AACnB,qBAAe,WAAWA,YAAW;AAAA,IACvC;AAAA,EACF,GAAG,SAAS;AACZ,SAA0B,sBAAc,UAAUE,UAAS,CAAC,GAAG,OAAO;AAAA,IACpE,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,MACvB,QAAQ,IAAI,aAAa,KAAK,KAAK,UAAU,WAAW,CAAC,KAAK,KAAK,UAAU,GAAG,CAAC;AAAA,IACnF;AAAA,EACF,CAAC,CAAC;AACJ;;;ACvEA,IAAAC,UAAuB;;;ACVvB,IAAAC,UAAuB;AACvB;AAwCA,SAAS,qBAAqB;AAAA,EAC5B;AAAA,EACA,QAAAC;AAAA,EACA,UAAU;AAAA,EACV;AACF,GAAG;AACD,IAAEA,WAAU,WAAW,OAAwC,UAAiB,OAAO,mEAAmE,IAAI,UAAiB,KAAK,IAAI;AACxL,MAAI,oBAAoB;AAAA,IACtB,QAAAA;AAAA,IACA,WAAW,sBAAsB;AAAA,IACjC,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,UAAU,QAAQ,YAAY;AAAA,EAChC;AACA,MAAI,kBAAkB,oBAAI,IAAI;AAC9B,MAAI,gBAAgB;AACpB,MAAI,YAAY,OAAO;AACrB,QAAIC,QAAO;AAAA,MACT,YAAY,QAAQ;AAAA,MACpB,YAAY,QAAQ;AAAA,MACpB,QAAQ,gBAAgB,QAAQ,MAAM;AAAA,IACxC;AAKA,QAAIC,QAAO,WAAW,KAAK,UAAU,KAAK,UAAUD,KAAI,CAAC,CAAC;AAC1D,oBAAgB,mDAAmDC,KAAI;AAAA,EACzE;AACA,MAAI;AAAA,IACF;AAAA,EACF,IAAI,kBAAkB;AACtB,SAA0B,sBAAoB,kBAAU,MAAyB,sBAAc,kBAAyB,UAAU;AAAA,IAChI,OAAO;AAAA,EACT,GAAsB,sBAAc,uBAA8B,UAAU;AAAA,IAC1E,OAAO;AAAA,EACT,GAAsB,sBAAc,gBAAuB,UAAU;AAAA,IACnE,OAAO;AAAA,EACT,GAAsB,sBAAc,sBAA6B,UAAU;AAAA,IACzE,OAAO;AAAA,MACL,iBAAiB;AAAA,IACnB;AAAA,EACF,GAAsB,sBAAc,QAAQ;AAAA,IAC1C,UAAU,kBAAkB;AAAA,IAC5B,UAAU,MAAM;AAAA,IAChB,gBAAgB,MAAM;AAAA,IACtB,WAAW,kBAAkB;AAAA,IAC7B,QAAQ,kBAAkB;AAAA,IAC1B,QAAQ;AAAA,MACN,sBAAsBF,QAAO,OAAO;AAAA,IACtC;AAAA,EACF,GAAsB,sBAAcG,aAAY;AAAA,IAC9C,QAAQH,QAAO;AAAA,IACf,QAAQA,QAAO;AAAA,IACf;AAAA,EACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAmC,sBAAc,UAAU;AAAA,IAClE,0BAA0B;AAAA,IAC1B;AAAA,IACA,yBAAyB;AAAA,MACvB,QAAQ;AAAA,IACV;AAAA,EACF,CAAC,IAAI,IAAI;AACX;AACA,SAASG,YAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,cAAqB,QAAQ,QAAW,OAAO,MAAM;AAC9D;AACA,SAAS,gBAAgB,QAAQ;AAC/B,MAAI,CAAC,OAAQ,QAAO;AACpB,MAAI,UAAU,OAAO,QAAQ,MAAM;AACnC,MAAI,aAAa,CAAC;AAClB,WAAS,CAAC,KAAK,GAAG,KAAK,SAAS;AAG9B,QAAI,qBAAqB,GAAG,GAAG;AAC7B,iBAAW,GAAG,IAAI;AAAA,QAChB,GAAG;AAAA,QACH,QAAQ;AAAA,MACV;AAAA,IACF,WAAW,eAAe,OAAO;AAE/B,iBAAW,GAAG,IAAI;AAAA,QAChB,SAAS,IAAI;AAAA,QACb,QAAQ;AAAA;AAAA;AAAA,QAGR,GAAI,IAAI,SAAS,UAAU;AAAA,UACzB,WAAW,IAAI;AAAA,QACjB,IAAI,CAAC;AAAA,MACP;AAAA,IACF,OAAO;AACL,iBAAW,GAAG,IAAI;AAAA,IACpB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,wBAAwB;AAC/B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK,IAAI;AACP,YAAM,IAAI,MAAM,oJAA8J,KAAK,UAAU,EAAE,CAAC,4BAA4B;AAAA,IAC9N;AAAA,IACA,QAAQ,IAAI;AACV,YAAM,IAAI,MAAM,uJAAiK,KAAK,UAAU,EAAE,CAAC,+CAAoD;AAAA,IACzP;AAAA,IACA,GAAG,OAAO;AACR,YAAM,IAAI,MAAM,kJAA4J,KAAK,4BAA4B;AAAA,IAC/M;AAAA,IACA,OAAO;AACL,YAAM,IAAI,MAAM,sFAA2F;AAAA,IAC7G;AAAA,IACA,UAAU;AACR,YAAM,IAAI,MAAM,yFAA8F;AAAA,IAChH;AAAA,EACF;AACF;AAOA,SAAS,mBAAmB,QAAQ,SAAS,OAAO,CAAC,GAAG;AACtD,MAAI,WAAW,CAAC;AAChB,MAAI,aAAa,0BAAiC,QAAQ,oBAA2B,QAAW,QAAQ;AAKxG,MAAI,UAAU,QAAQ,QAAQ,IAAI,WAAS;AACzC,QAAI,QAAQ,SAAS,MAAM,MAAM,EAAE,KAAK,MAAM;AAC9C,WAAO;AAAA,MACL,GAAG;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,MAAM,YAAU,yBAAyB,MAAM;AACnD,SAAO;AAAA,IACL,IAAI,WAAW;AACb,aAAO,QAAQ;AAAA,IACjB;AAAA,IACA,IAAI,SAAS;AA1LjB;AA2LM,aAAO;AAAA,QACL,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,uBAAqB,UAAK,WAAL,mBAAa,yBAAwB;AAAA,QAC1D,oBAAoB;AAAA,QACpB,wBAAsB,UAAK,WAAL,mBAAa,0BAAyB;AAAA,QAC5D,gCAAgC;AAAA,MAClC;AAAA,IACF;AAAA,IACA,IAAI,QAAQ;AACV,aAAO;AAAA,QACL,eAAe,OAAO;AAAA,QACtB,UAAU,QAAQ;AAAA,QAClB;AAAA,QACA,YAAY,QAAQ;AAAA,QACpB,YAAY,QAAQ;AAAA,QACpB,QAAQ,QAAQ;AAAA,QAChB,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,cAAc;AAAA,QACd,UAAU,oBAAI,IAAI;AAAA,QAClB,UAAU,oBAAI,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,IACA,IAAI,SAAS;AACX,aAAO;AAAA,IACT;AAAA,IACA,IAAI,SAAS;AACX,aAAO;AAAA,IACT;AAAA,IACA,aAAa;AACX,YAAM,IAAI,YAAY;AAAA,IACxB;AAAA,IACA,YAAY;AACV,YAAM,IAAI,WAAW;AAAA,IACvB;AAAA,IACA,0BAA0B;AACxB,YAAM,IAAI,yBAAyB;AAAA,IACrC;AAAA,IACA,WAAW;AACT,YAAM,IAAI,UAAU;AAAA,IACtB;AAAA,IACA,QAAQ;AACN,YAAM,IAAI,OAAO;AAAA,IACnB;AAAA,IACA,aAAa;AACX,YAAM,IAAI,YAAY;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AACX,aAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AACd,YAAM,IAAI,eAAe;AAAA,IAC3B;AAAA,IACA,UAAU;AACR,YAAM,IAAI,SAAS;AAAA,IACrB;AAAA,IACA,aAAa;AACX,aAAO;AAAA,IACT;AAAA,IACA,gBAAgB;AACd,YAAM,IAAI,eAAe;AAAA,IAC3B;AAAA,IACA,cAAc;AACZ,YAAM,IAAI,aAAa;AAAA,IACzB;AAAA,IACA,2BAA2B,oBAAI,IAAI;AAAA,IACnC,0BAA0B,oBAAI,IAAI;AAAA,IAClC,qBAAqB;AACnB,YAAM,IAAI,oBAAoB;AAAA,IAChC;AAAA,EACF;AACF;AACA,SAAS,WAAW,IAAI;AACtB,SAAO,OAAO,OAAO,WAAW,KAAK,WAAW,EAAE;AACpD;AACA,SAAS,eAAe,IAAI;AAC1B,MAAI,OAAO,OAAO,OAAO,WAAW,KAAK,WAAW,EAAE;AAItD,SAAO,KAAK,QAAQ,MAAM,KAAK;AAC/B,MAAI,UAAUC,oBAAmB,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,kBAAkB;AAC9F,SAAO;AAAA,IACL,UAAU,QAAQ;AAAA,IAClB,QAAQ,QAAQ;AAAA,IAChB,MAAM,QAAQ;AAAA,EAChB;AACF;AACA,IAAMA,sBAAqB;AAI3B,IAAMC,iBAAgB;AAAA,EACpB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,UAAU;AAAA,EACV,UAAU;AACZ;AACA,IAAMC,gBAAe;AACrB,SAAS,WAAW,KAAK;AACvB,SAAO,IAAI,QAAQA,eAAc,WAASD,eAAc,KAAK,CAAC;AAChE;;;ADhRA,SAAS,YAAY;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,IAAI,GAAG;AAAA,EACnB;AACA,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,SAAS,mBAAmB,SAAS,QAAQ,cAAc,QAAQ,QAAQ,QAAQ,SAAS;AAOhG,UAAQ,qBAAqB,aAAa;AAAA,IACxC,GAAG,QAAQ,qBAAqB;AAAA,EAClC;AACA,WAAS,SAAS,QAAQ,qBAAqB,SAAS;AACtD,QAAI,UAAU,MAAM,MAAM;AAC1B,QAAI,QAAQ,aAAa,OAAO;AAChC,QAAI,gBAAgB,QAAQ,SAAS,OAAO,OAAO;AAKnD,QAAI,SAAS,yBAAyB,eAAe,OAAO,QAAQ,SAAS,MAAM,MAAM,mBAAmB,CAAC,cAAc,YAAY;AACrI,cAAQ,qBAAqB,WAAW,OAAO,IAAI;AAAA,IACrD;AAAA,EACF;AACA,MAAIE,UAAS,mBAAmB,QAAQ,QAAQ,sBAAsB;AAAA,IACpE,QAAQ;AAAA,MACN,qBAAqB;AAAA,MACrB,sBAAsB,QAAQ,OAAO;AAAA,IACvC;AAAA,EACF,CAAC;AACD,SAA0B,sBAAoB,kBAAU,MAAyB,sBAAc,aAAa,UAAU;AAAA,IACpH,OAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,QAAQ;AAAA,MAChB,WAAW,QAAQ;AAAA,MACnB,gBAAgB,QAAQ;AAAA,MACxB;AAAA,MACA,YAAY,QAAQ;AAAA,IACtB;AAAA,EACF,GAAsB,sBAAc,oBAAoB;AAAA,IACtD,UAAUA,QAAO,MAAM;AAAA,EACzB,GAAsB,sBAAc,sBAAsB;AAAA,IACxD,QAAQA;AAAA,IACR,SAAS,QAAQ;AAAA,IACjB,SAAS;AAAA,EACX,CAAC,CAAC,CAAC,GAAG,QAAQ,OAAO,kBAAkB,QAAQ,sBAAyC,sBAAoB,kBAAU,MAAyB,sBAAc,gBAAgB;AAAA,IAC3K;AAAA,IACA,YAAY;AAAA,IACZ,QAAQ,QAAQ,oBAAoB,UAAU;AAAA,IAC9C,aAAa,IAAI,YAAY;AAAA,IAC7B;AAAA,EACF,CAAC,CAAC,IAAI,IAAI;AACZ;", "names": ["DataRouterContext", "createContext", "process", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "isDataRoute", "RouteErrorContext", "useHref", "to", "_temp", "relative", "useInRouterContext", "invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "decodePath", "navigateEffectWarning", "useIsomorphicLayoutEffect", "cb", "isStatic", "static", "React", "useLayoutEffect", "useNavigate", "useNavigateStable", "useNavigateUnstable", "dataRouterContext", "future", "locationPathname", "routePathnamesJson", "JSON", "stringify", "getResolveToMatches", "v7_relativeSplatPath", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "createElement", "Provider", "value", "useParams", "routeMatch", "length", "params", "_temp2", "useRoutes", "routes", "locationArg", "useRoutesImpl", "dataRouterState", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "pathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "parentSegments", "split", "segments", "slice", "join", "matchRoutes", "element", "undefined", "Component", "lazy", "renderedMatches", "_renderMatches", "map", "match", "Object", "assign", "encodeLocation", "_extends", "key", "NavigationType", "Pop", "DefaultErrorComponent", "error", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "console", "Fragment", "style", "fontStyle", "defaultErrorElement", "RenderErrorBoundary", "constructor", "props", "revalidation", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "children", "component", "RenderedRoute", "_ref", "staticContext", "errorElement", "Error<PERSON>ou<PERSON><PERSON>", "_deepestRenderedBoundaryId", "id", "_dataRouterState", "_future", "errors", "v7_partialHydration", "initialized", "errorIndex", "findIndex", "m", "keys", "Math", "min", "renderFallback", "fallbackIndex", "i", "HydrateFallback", "hydrateFallbackElement", "loaderData", "needsToRunLoader", "loader", "reduceRight", "index", "shouldRenderHydrateFallback", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useRouteId", "UseRouteId", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "useMatches", "UseMatches", "convertRouteMatchToUiMatch", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "actionData", "_state$errors", "UseRouteError", "useAsyncValue", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "set<PERSON><PERSON>er<PERSON>ey", "useState", "blockerFunction", "arg", "currentLocation", "nextLocation", "historyAction", "stripBasename", "useEffect", "String", "deleteBlocker", "get<PERSON><PERSON>er", "blockers", "has", "get", "IDLE_BLOCKER", "UseNavigateStable", "fromRouteId", "alreadyWarned", "cond", "warnOnce", "warn", "logDeprecation", "flag", "msg", "link", "logV6DeprecationWarnings", "renderFuture", "routerFuture", "v7_startTransition", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_skipActionErrorRevalidation", "START_TRANSITION", "startTransitionImpl", "Navigate", "_ref4", "to", "replace", "state", "relative", "useInRouterContext", "process", "invariant", "future", "static", "isStatic", "useContext", "NavigationContext", "warning", "matches", "RouteContext", "pathname", "locationPathname", "useLocation", "navigate", "useNavigate", "path", "resolveTo", "getResolveToMatches", "v7_relativeSplatPath", "jsonPath", "JSON", "stringify", "React", "useEffect", "parse", "Outlet", "props", "useOutlet", "context", "Route", "_props", "Router", "_ref5", "basename", "basenameProp", "children", "location", "locationProp", "navigationType", "NavigationType", "Pop", "navigator", "staticProp", "navigationContext", "useMemo", "_extends", "parsePath", "search", "hash", "key", "locationContext", "trailingPathname", "stripBasename", "createElement", "Provider", "value", "LocationContext", "Routes", "_ref6", "useRoutes", "createRoutesFromChildren", "Await", "_ref7", "errorElement", "resolve", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "ResolveAwait", "AwaitRenderStatus", "neverSettledPromise", "Promise", "Component", "constructor", "error", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "console", "render", "promise", "status", "pending", "success", "Object", "defineProperty", "get", "renderError", "reject", "catch", "_tracked", "then", "data", "_error", "Aborted<PERSON>eferredError", "AwaitContext", "_ref8", "useAsyncValue", "to<PERSON><PERSON>", "Fragment", "parentPath", "routes", "Children", "for<PERSON>ach", "element", "index", "isValidElement", "treePath", "type", "push", "apply", "name", "route", "id", "join", "caseSensitive", "loader", "action", "Error<PERSON>ou<PERSON><PERSON>", "hasErrorBou<PERSON>ry", "shouldRevalidate", "handle", "lazy", "renderMatches", "_renderMatches", "mapRouteProperties", "updates", "assign", "undefined", "HydrateFallback", "hydrateFallbackElement", "defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "shouldProcessLinkClick", "target", "button", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "getSearchParamsForLocation", "locationSearch", "defaultSearchParams", "searchParams", "for<PERSON>ach", "_", "has", "getAll", "append", "_formDataSupportsSubmitter", "isFormDataSubmitterSupported", "FormData", "document", "createElement", "e", "supportedFormEncTypes", "Set", "getFormEncType", "encType", "process", "warning", "getFormSubmissionInfo", "basename", "method", "action", "formData", "body", "attr", "getAttribute", "stripBasename", "type", "form", "Error", "name", "prefix", "undefined", "REACT_ROUTER_VERSION", "window", "__reactRouterVersion", "ViewTransitionContext", "createContext", "isTransitioning", "process", "displayName", "FetchersContext", "Map", "START_TRANSITION", "startTransitionImpl", "React", "FLUSH_SYNC", "flushSyncImpl", "ReactDOM", "USE_ID", "useIdImpl", "startTransitionSafe", "cb", "flushSyncSafe", "Deferred", "constructor", "status", "promise", "Promise", "resolve", "reject", "value", "reason", "RouterProvider", "_ref", "fallbackElement", "router", "future", "state", "setStateImpl", "useState", "pendingState", "setPendingState", "vtContext", "setVtContext", "renderDfd", "setRenderDfd", "transition", "setTransition", "interruption", "setInterruption", "fetcherData", "useRef", "v7_startTransition", "optInStartTransition", "useCallback", "setState", "newState", "_ref2", "deletedFetchers", "flushSync", "viewTransitionOpts", "fetchers", "for<PERSON>ach", "fetcher", "key", "data", "undefined", "current", "set", "delete", "isViewTransitionUnavailable", "window", "document", "startViewTransition", "skipTransition", "currentLocation", "nextLocation", "t", "finished", "finally", "useLayoutEffect", "subscribe", "useEffect", "renderPromise", "location", "warning", "v7_partialHydration", "navigator", "useMemo", "createHref", "encodeLocation", "go", "n", "navigate", "push", "to", "opts", "preventScrollReset", "replace", "basename", "dataRouterContext", "static", "routerFuture", "v7_relativeSplatPath", "logV6DeprecationWarnings", "createElement", "Fragment", "DataRouterContext", "Provider", "DataRouterStateContext", "Router", "navigationType", "historyAction", "initialized", "MemoizedDataRoutes", "routes", "memo", "DataRoutes", "_ref3", "useRoutesImpl", "HistoryRouter", "_ref6", "basename", "children", "future", "history", "state", "setStateImpl", "useState", "action", "location", "v7_startTransition", "setState", "useCallback", "newState", "startTransitionImpl", "React", "useLayoutEffect", "listen", "useEffect", "logV6DeprecationWarnings", "createElement", "Router", "navigationType", "navigator", "process", "displayName", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "LinkWithRef", "_ref7", "ref", "onClick", "relative", "reloadDocument", "replace", "target", "to", "preventScrollReset", "viewTransition", "rest", "_objectWithoutPropertiesLoose", "_excluded", "useContext", "NavigationContext", "absoluteHref", "isExternal", "test", "currentUrl", "URL", "href", "targetUrl", "startsWith", "protocol", "path", "stripBasename", "pathname", "origin", "search", "hash", "e", "warning", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "event", "defaultPrevented", "_extends", "NavLink", "NavLinkWithRef", "_ref8", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "_excluded2", "useResolvedPath", "useLocation", "routerState", "DataRouterStateContext", "isTransitioning", "useViewTransitionState", "toPathname", "encodeLocation", "locationPathname", "nextLocationPathname", "navigation", "toLowerCase", "endSlashPosition", "endsWith", "length", "isActive", "char<PERSON>t", "isPending", "renderProps", "aria<PERSON>urrent", "undefined", "filter", "Boolean", "join", "Form", "_ref9", "forwardedRef", "fetcher<PERSON>ey", "navigate", "method", "defaultMethod", "onSubmit", "props", "_excluded3", "submit", "useSubmit", "formAction", "useFormAction", "formMethod", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "submitMethod", "getAttribute", "currentTarget", "ScrollRestoration", "_ref10", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "DataRouterContext", "invariant", "useDataRouterState", "_temp", "replaceProp", "useNavigate", "shouldProcessLinkClick", "createPath", "useSearchParams", "defaultInit", "URLSearchParams", "defaultSearchParamsRef", "useRef", "createSearchParams", "hasSetSearchParamsRef", "searchParams", "useMemo", "getSearchParamsForLocation", "current", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "validateClientSideSubmission", "Error", "fetcherId", "getUniqueFetcherId", "String", "router", "UseSubmit", "currentRouteId", "useRouteId", "options", "encType", "formData", "body", "getFormSubmissionInfo", "key", "fetch", "formEncType", "flushSync", "fromRouteId", "_temp2", "routeContext", "RouteContext", "match", "matches", "slice", "params", "indexValues", "getAll", "hasNakedIndexParam", "some", "v", "delete", "for<PERSON>ach", "append", "qs", "toString", "route", "index", "joinPaths", "useFetcher", "_temp3", "_route$matches", "UseFetcher", "fetcherData", "FetchersContext", "routeId", "id", "defaultKey", "useIdImpl", "setFetcher<PERSON>ey", "getFetcher", "deleteFetcher", "load", "opts", "submitImpl", "FetcherForm", "fetcher", "fetchers", "get", "IDLE_FETCHER", "data", "fetcherWithComponents", "useFetchers", "UseFetchers", "Array", "from", "entries", "map", "_ref11", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "_temp4", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "error", "sessionPositions", "getItem", "parse", "getKeyWithoutBasename", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "decodeURIComponent", "scrollIntoView", "useBeforeUnload", "callback", "capture", "addEventListener", "removeEventListener", "usePrompt", "_ref12", "when", "message", "blocker", "useBlocker", "proceed", "confirm", "setTimeout", "reset", "vtContext", "ViewTransitionContext", "currentPath", "currentLocation", "nextPath", "nextLocation", "matchPath", "React", "_extends", "React", "invariant", "React", "data", "value", "invariant", "result", "data", "router", "redirect", "React", "React", "React", "React", "Component", "invariant", "fn", "msg", "result", "replace", "router", "useDataRouterContext", "invariant", "ABSOLUTE_URL_REGEX", "NavLink", "_extends", "Link", "Form", "router", "data", "json", "Await", "useMatches", "useLoaderData", "useRouteLoaderData", "useActionData", "useFetcher", "LiveReload", "router", "invariant", "React", "ScrollRestoration", "STORAGE_KEY", "key", "_extends", "React", "React", "router", "data", "json", "DataRoutes", "ABSOLUTE_URL_REGEX", "ESCAPE_LOOKUP", "ESCAPE_REGEX", "router"]}