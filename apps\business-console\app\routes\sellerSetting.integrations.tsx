import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Switch } from "~/components/ui/switch";
import {
  Plug,
  Settings as SettingsIcon,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Eye,
  EyeOff,
  Copy,
  Plus,
  Edit,
  Trash2,
  Activity,
  Zap,
  Smartphone,
  CreditCard,
  FileText,
  Webhook,
  <PERSON>,
  <PERSON>fresh<PERSON><PERSON>
} from "lucide-react";
import { ComingSoonOverlay } from "~/components/modals/ComingSoonOverlay";

const mockIntegrations = {
  pixels: [
    {
      id: "PIX-001",
      platform: "Meta (Facebook)",
      pixelId: "**********123456",
      measurementId: "G-XXXXXXXXXX",
      mode: "JavaScript + CAPI",
      lastEvent: "2025-01-14T10:30:00Z",
      status: "Active",
      linkedOutlets: ["Downtown", "Mall Road"],
      eventsTracked: 1247,
      conversionValue: 45680
    },
    {
      id: "PIX-002",
      platform: "Google Analytics",
      pixelId: null,
      measurementId: "G-YYYYYYYYYY",
      mode: "GA4 + Enhanced Ecommerce",
      lastEvent: "2025-01-14T10:25:00Z",
      status: "Active",
      linkedOutlets: ["Downtown", "Mall Road"],
      eventsTracked: 892,
      conversionValue: 38920
    },
    {
      id: "PIX-003",
      platform: "Google Ads",
      pixelId: "AW-**********",
      measurementId: null,
      mode: "Conversion Tracking",
      lastEvent: "2025-01-14T09:45:00Z",
      status: "Warning",
      linkedOutlets: ["Downtown"],
      eventsTracked: 234,
      conversionValue: 12450
    }
  ],
  posConnectors: [
    {
      id: "POS-001",
      provider: "Petpooja",
      storeMapping: { "Downtown": "STORE_001", "Mall Road": "STORE_002" },
      syncFrequency: "Real-time",
      lastSync: "2025-01-14T10:30:00Z",
      status: "Connected",
      errorLog: [],
      menuItemsSync: 156,
      ordersSync: 1247
    },
    {
      id: "POS-002",
      provider: "Posist",
      storeMapping: { "Mall Road": "LOC_MR_001" },
      syncFrequency: "Every 5 minutes",
      lastSync: "2025-01-14T10:25:00Z",
      status: "Connected",
      errorLog: [],
      menuItemsSync: 89,
      ordersSync: 567
    },
    {
      id: "POS-003",
      provider: "Revel Systems",
      storeMapping: { "Downtown": "REV_DT_001" },
      syncFrequency: "Every 10 minutes",
      lastSync: "2025-01-14T09:45:00Z",
      status: "Error",
      errorLog: ["Connection timeout", "Invalid API key"],
      menuItemsSync: 0,
      ordersSync: 0
    }
  ],
  deliveryPartners: [
    {
      id: "DEL-001",
      partner: "Swiggy",
      apiKey: "swg_live_********************************",
      serviceableOutlets: ["Downtown", "Mall Road"],
      lastRateCardSync: "2025-01-13T18:30:00Z",
      webhookUrl: "https://api.mnet.restaurant/webhooks/swiggy",
      status: "Active",
      ordersToday: 45,
      commissionRate: 23.5
    },
    {
      id: "DEL-002",
      partner: "Zomato",
      apiKey: "zmt_live_********************************",
      serviceableOutlets: ["Downtown", "Mall Road"],
      lastRateCardSync: "2025-01-14T08:15:00Z",
      webhookUrl: "https://api.mnet.restaurant/webhooks/zomato",
      status: "Active",
      ordersToday: 38,
      commissionRate: 25.0
    },
    {
      id: "DEL-003",
      partner: "Dunzo",
      apiKey: "dnz_test_********************************",
      serviceableOutlets: ["Downtown"],
      lastRateCardSync: "2025-01-12T15:20:00Z",
      webhookUrl: "https://api.mnet.restaurant/webhooks/dunzo",
      status: "Testing",
      ordersToday: 3,
      commissionRate: 15.0
    }
  ],
  accounting: [
    {
      id: "ACC-001",
      integrationName: "Tally ERP 9",
      authType: "API Key",
      webhookSecret: "whsec_********************************",
      lastPush: "2025-01-14T10:00:00Z",
      status: "Active",
      recordsPushed: 1247,
      lastSyncAmount: 156780
    },
    {
      id: "ACC-002",
      integrationName: "QuickBooks Online",
      authType: "OAuth 2.0",
      webhookSecret: "whsec_********************************",
      lastPush: "2025-01-14T09:30:00Z",
      status: "Active",
      recordsPushed: 892,
      lastSyncAmount: 98450
    },
    {
      id: "ACC-003",
      integrationName: "Zoho Books",
      authType: "OAuth 2.0",
      webhookSecret: null,
      lastPush: null,
      status: "Setup Required",
      recordsPushed: 0,
      lastSyncAmount: 0
    }
  ]
};

export default function Integrations() {
  const [selectedIntegration, setSelectedIntegration] = useState(null);
  const [selectedTab, setSelectedTab] = useState("pixels");
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({});

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Active":
      case "Connected": return "default";
      case "Warning":
      case "Testing": return "secondary";
      case "Error":
      case "Setup Required": return "destructive";
      case "Inactive": return "outline";
      default: return "secondary";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Active":
      case "Connected": return <CheckCircle className="h-3 w-3" />;
      case "Warning": return <AlertCircle className="h-3 w-3" />;
      case "Error":
      case "Setup Required": return <XCircle className="h-3 w-3" />;
      case "Testing": return <Clock className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  const toggleApiKeyVisibility = (id: string) => {
    setShowApiKey(prev => ({ ...prev, [id]: !prev[id] }));
  };

  const maskApiKey = (apiKey: string, show: boolean) => {
    if (!apiKey) return "Not configured";
    if (show) return apiKey;
    return apiKey.substring(0, 8) + "********************************";
  };

  const renderPixels = () => (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Active Pixels</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockIntegrations.pixels.filter(p => p.status === "Active").length}
            </div>
            <p className="text-xs text-muted-foreground">
              out of {mockIntegrations.pixels.length} configured
            </p>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Events Tracked</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockIntegrations.pixels.reduce((acc, p) => acc + p.eventsTracked, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Today</p>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Conversion Value</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ₹{mockIntegrations.pixels.reduce((acc, p) => acc + p.conversionValue, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Tracked today</p>
          </CardContent>
        </Card>
      </div>

      {/* Pixels Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-normal">Pixel Tracking</CardTitle>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Pixel
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-black font-semibold">Platform</TableHead>
                <TableHead className="text-black font-semibold">ID / Measurement ID</TableHead>
                <TableHead className="text-black font-semibold">Mode</TableHead>
                <TableHead className="text-black font-semibold">Events Today</TableHead>
                <TableHead className="text-black font-semibold">Status</TableHead>
                <TableHead className="text-black font-semibold">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockIntegrations.pixels.map((pixel) => (
                <TableRow key={pixel.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center">
                        <Smartphone className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{pixel.platform}</p>
                        <p className="text-sm text-muted-foreground">
                          {pixel.linkedOutlets.join(", ")}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      {pixel.pixelId && (
                        <p className="text-sm font-mono">{pixel.pixelId}</p>
                      )}
                      {pixel.measurementId && (
                        <p className="text-sm font-mono text-muted-foreground">{pixel.measurementId}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{pixel.mode}</Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{pixel.eventsTracked}</p>
                      <p className="text-sm text-green-600">₹{pixel.conversionValue.toLocaleString()}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(pixel.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(pixel.status)}
                        {pixel.status}
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">
                        <SettingsIcon className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <RefreshCw className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );

  const renderPOSConnectors = () => (
    <div className="space-y-6">
      {/* POS Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Connected POS</CardTitle>
            <Plug className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockIntegrations.posConnectors.filter(p => p.status === "Connected").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Active connections
            </p>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Orders Synced</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockIntegrations.posConnectors.reduce((acc, p) => acc + p.ordersSync, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Today</p>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Menu Items</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockIntegrations.posConnectors.reduce((acc, p) => acc + p.menuItemsSync, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Synced items</p>
          </CardContent>
        </Card>
      </div>

      {/* POS Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-normal">POS Integrations</CardTitle>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add POS
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-black font-semibold">Provider</TableHead>
                <TableHead className="text-black font-semibold">Store Mapping</TableHead>
                <TableHead className="text-black font-semibold">Sync Frequency</TableHead>
                <TableHead className="text-black font-semibold">Last Sync</TableHead>
                <TableHead className="text-black font-semibold">Status</TableHead>
                <TableHead className="text-black font-semibold">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockIntegrations.posConnectors.map((pos) => (
                <TableRow key={pos.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center">
                        <Plug className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{pos.provider}</p>
                        <p className="text-sm text-muted-foreground">
                          {pos.ordersSync} orders synced
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {Object.entries(pos.storeMapping).map(([outlet, storeId]) => (
                        <div key={outlet} className="text-sm">
                          <span className="font-medium">{outlet}</span>
                          <span className="text-muted-foreground"> → {storeId}</span>
                        </div>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{pos.syncFrequency}</Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="text-sm">{new Date(pos.lastSync).toLocaleString()}</p>
                      {pos.errorLog.length > 0 && (
                        <p className="text-xs text-red-600">{pos.errorLog[0]}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(pos.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(pos.status)}
                        {pos.status}
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">
                        <SettingsIcon className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <RefreshCw className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );

  const renderDeliveryPartners = () => (
    <div className="space-y-6">
      {/* Delivery Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Active Partners</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockIntegrations.deliveryPartners.filter(p => p.status === "Active").length}
            </div>
            <p className="text-xs text-muted-foreground">Connected platforms</p>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Orders Today</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockIntegrations.deliveryPartners.reduce((acc, p) => acc + p.ordersToday, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Delivery orders</p>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Avg Commission</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(mockIntegrations.deliveryPartners.reduce((acc, p) => acc + p.commissionRate, 0) / mockIntegrations.deliveryPartners.length).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">Commission rate</p>
          </CardContent>
        </Card>
      </div>

      {/* Delivery Partners Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-normal">Delivery Partners</CardTitle>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Partner
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-black font-semibold">Partner</TableHead>
                <TableHead className="text-black font-semibold">API Key</TableHead>
                <TableHead className="text-black font-semibold">Serviceable Outlets</TableHead>
                <TableHead className="text-black font-semibold">Commission</TableHead>
                <TableHead className="text-black font-semibold">Status</TableHead>
                <TableHead className="text-black font-semibold">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockIntegrations.deliveryPartners.map((partner) => (
                <TableRow key={partner.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center">
                        <CreditCard className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{partner.partner}</p>
                        <p className="text-sm text-muted-foreground">
                          {partner.ordersToday} orders today
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-sm">
                        {maskApiKey(partner.apiKey, showApiKey[partner.id])}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleApiKeyVisibility(partner.id)}
                      >
                        {showApiKey[partner.id] ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {partner.serviceableOutlets.map((outlet) => (
                        <Badge key={outlet} variant="outline" className="text-xs mr-1">
                          {outlet}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <p className="font-medium">{partner.commissionRate}%</p>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(partner.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(partner.status)}
                        {partner.status}
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">
                        <SettingsIcon className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <RefreshCw className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );

  const renderAccounting = () => (
    <div className="space-y-6">
      {/* Accounting Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Connected Systems</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockIntegrations.accounting.filter(a => a.status === "Active").length}
            </div>
            <p className="text-xs text-muted-foreground">
              out of {mockIntegrations.accounting.length} configured
            </p>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Records Synced</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockIntegrations.accounting.reduce((acc, a) => acc + a.recordsPushed, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Today</p>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Sync Value</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ₹{mockIntegrations.accounting.reduce((acc, a) => acc + a.lastSyncAmount, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Last sync total</p>
          </CardContent>
        </Card>
      </div>

      {/* Accounting Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-normal">Accounting & ERP Systems</CardTitle>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Integration
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-black font-semibold">System</TableHead>
                <TableHead className="text-black font-semibold">Auth Type</TableHead>
                <TableHead className="text-black font-semibold">Webhook Secret</TableHead>
                <TableHead className="text-black font-semibold">Last Sync</TableHead>
                <TableHead className="text-black font-semibold">Status</TableHead>
                <TableHead className="text-black font-semibold">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockIntegrations.accounting.map((acc) => (
                <TableRow key={acc.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center">
                        <FileText className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{acc.integrationName}</p>
                        <p className="text-sm text-muted-foreground">
                          {acc.recordsPushed} records synced
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{acc.authType}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-sm">
                        {acc.webhookSecret ?
                          (showApiKey[acc.id] ? acc.webhookSecret : "whsec_********************************") :
                          "Not configured"
                        }
                      </span>
                      {acc.webhookSecret && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleApiKeyVisibility(acc.id)}
                          >
                            {showApiKey[acc.id] ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Copy className="h-3 w-3" />
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      {acc.lastPush ? (
                        <>
                          <p className="text-sm">{new Date(acc.lastPush).toLocaleString()}</p>
                          <p className="text-xs text-green-600">₹{acc.lastSyncAmount.toLocaleString()}</p>
                        </>
                      ) : (
                        <p className="text-sm text-muted-foreground">Never synced</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(acc.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(acc.status)}
                        {acc.status}
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">
                        <SettingsIcon className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <RefreshCw className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="p-6">
      <div className="mb-4">
        <h1 className="text-3xl font-bold text-gray-900">Integrations</h1>
        <p className="text-gray-600 mt-2">Connect with third-party services</p>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid grid-cols-2 lg:grid-cols-4 w-full h-20 lg:h-10">
          <TabsTrigger value="pixels" className="font-semibold">Tracking Pixels</TabsTrigger>
          <TabsTrigger value="pos" className="font-semibold">POS Systems</TabsTrigger>
          <TabsTrigger value="delivery" className="font-semibold">Delivery Partners</TabsTrigger>
          <TabsTrigger value="accounting" className="font-semibold">Accounting/ERP</TabsTrigger>
        </TabsList>

        <TabsContent value="pixels">
          {renderPixels()}
        </TabsContent>

        <TabsContent value="pos">
          {renderPOSConnectors()}
        </TabsContent>

        <TabsContent value="delivery">
          {renderDeliveryPartners()}
        </TabsContent>

        <TabsContent value="accounting">
          {renderAccounting()}
        </TabsContent>
      </Tabs>
      {(selectedTab === "pixels" || selectedTab === "pos" || selectedTab === "delivery" || selectedTab === "accounting") && <ComingSoonOverlay />}
    </div>
  );
} 