import { ActionFunction, json, LoaderFunction, redirect } from "@remix-run/node";
import { Await, Form, useFetcher, useLoaderData } from "@remix-run/react";
import * as React from "react";
import { CalendarIcon } from "lucide-react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";

import {
      Table,
      TableBody,
      TableCell,
      TableHead,
      TableHeader,
      TableRow,
} from "~/components/ui/table";
import {
      Popover,
      PopoverContent,
      PopoverTrigger,
} from "~/components/ui/popover";
import { Calendar } from "~/components/ui/calendar";
import { format } from "date-fns";
import { Transaction } from "~/types/api/businessConsoleService/payments";
import { getTransactionDetails, updateMarkAsPaid, updateTransaction } from "~/services/payments";
import { Checkbox } from "~/components/ui/checkbox";
import {
      Select,
      SelectContent,
      SelectItem,
      SelectTrigger,
      SelectValue,
} from "~/components/ui/select";
import SucessPopUp from "~/components/ui/sucessPopUp";
import { withAuth, withResponse } from "@utils/auth-utils";
import MarkAsPaid from "~/components/ui/markAsPaidPopup";
import { useState } from "react";
import SpinnerLoader from "~/components/loader/SpinnerLoader";

interface LoaderData {
      bankData: Transaction[],
      adminPermission: boolean
}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
      try {
            const permission = user?.userDetails.roles?.includes("AC_Basic")
            // console.log(permission, "444444444444444444")
            const url = new URL(request.url);
            const date = url.searchParams.get("date") ? url.searchParams.get("date") : new Date().toISOString().split("T")[0];

            if (!date) {
                  return json({ error: "Date is required" }, { status: 400 });
            }
            const response = await getTransactionDetails(date, request);
            return withResponse({
                  bankData: response.data,
                  adminPermission: permission

            }, response.headers);
      } catch (error) {
            console.error(error);
            throw new Response("Failed to fetch transactions", { status: 500 });
      }
});
export const action: ActionFunction = withAuth(async ({ request, user }) => {
      const formData = await request.formData();
      const rowId = Number(formData.get("rowId"));
      const intent = formData.get("intent")
      const date = formData.get("date") as string
      const depositId = formData.get("depositId") as unknown as number;
      const amount = formData.get("amount") as unknown as number;
      const note = formData.get("note") as string
      const userId = user?.userId;
      const permission = user?.userPermissions?.includes("seller_app.mnetAdminBasic")
      console.log(user, "444444444444444444")
      if (intent === "markAsPaid") {
            try {
                  const response = await updateMarkAsPaid(userId, depositId, request);
                  return withResponse({
                        bankData: response.data,
                        adminPermission: permission
                  }, response.headers)
            }

            catch (error) {
                  console.error(error);
                  throw new Response("Failed to update transaction", { status: 500 });
            }
      }


      if (intent == "updateTransaction") {
            try {
                  const response = await updateTransaction(rowId, request);
                  return withResponse({
                        bankData: response.data,
                        adminPermission: permission
                  }, response.headers)
            } catch (error) {
                  console.error(error);
                  throw new Response("Failed to update transaction", { status: 500 });
            }
      }




});

export default function BankTransactions() {
      const { bankData, adminPermission } = useLoaderData<LoaderData>();
      const fetcher = useFetcher<{ bankData: Transaction[] }>();
      const [data, setData] = React.useState<Transaction[]>(bankData);
      const [date, setDate] = React.useState<Date | undefined>(new Date());
      const [filters, setFilters] = React.useState<string[]>(["ALL"]);
      const [searchTerm, setSearchTerm] = React.useState("");
      const [loading, setLoading] = React.useState(false);
      const [pageSize, setPageSize] = React.useState("100");
      const [currentPage, setCurrentPage] = React.useState(1);

      React.useEffect(() => {
            if (fetcher.data?.bankData) {
                  applyFilters(fetcher.data.bankData); // Always filter from fresh fetched data
            }
      }, [fetcher.data, filters, searchTerm]);

      const applyFilters = (newData: Transaction[]) => {
            if (!Array.isArray(newData)) {
                  return []; // Return an empty array as a fallback
            }
            else {
                  const filteredData = newData?.filter(
                        (trans) =>
                              (filters.includes("ALL") || filters.includes(trans.status)) &&
                              (
                                    trans.businessName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                    trans.orderGroupId?.toString().includes(searchTerm) ||
                                    trans.amount?.toString().includes(searchTerm) ||
                                    trans.id?.toString().includes(searchTerm)
                              )
                  );
                  setData(filteredData);
                  setCurrentPage(1);
            }


      };

      React.useEffect(() => {
            if (fetcher.data?.bankData) {
                  applyFilters(fetcher.data.bankData);
            }
      }, [filters, searchTerm]);



      const handleCheckboxChange = (status: string) => {
            setFilters((prev) => {
                  let newFilters;

                  if (status === "ALL") {
                        newFilters = ["ALL"];
                  } else {
                        if (prev.includes(status)) {
                              newFilters = prev.filter((x) => x !== status);
                        } else {
                              newFilters = prev.filter((x) => x !== "ALL").concat(status);
                        }
                  }

                  return newFilters;
            });
      };


      ;

      const formatDate = (dateString: string) => {
            const date = new Date(dateString);
            const options: Intl.DateTimeFormatOptions = {
                  day: "2-digit",
                  month: "short",
                  hour: "2-digit",
                  minute: "2-digit",
                  hour12: true,
            };
            return date.toLocaleString("en-US", options);
      };


      const handleSubmit = (date: Date) => {
            if (!date) return;
            const formattedDate = new Date(date);


            fetcher.load(`/home/<USER>"yyyy-MM-dd")}`);
      };

      const paginatedData = React.useMemo(() => {
            const start = (currentPage - 1) * Number(pageSize);
            const end = start + Number(pageSize);
            return [...data]
                  .sort((a, b) => (a.id > b.id ? -1 : 1))
                  .slice(start, end);
      }, [data, currentPage, pageSize]);

      const totalPages = Math.ceil(data.length / Number(pageSize));

      return (
            <React.Suspense fallback={<div>Loading...</div>}>
                  <div>
                        <div className="flex flex-col sm:flex-row sm:space-x-3 my-3">
                              <Form className="flex sm:space-x-2 mb-3 sm:mb-0">
                                    <Popover>
                                          <PopoverTrigger asChild>
                                                <Button variant="outline" className="w-[280px]">
                                                      <CalendarIcon className="mr-2 h-4 w-4" />
                                                      {date ? format(date, "PPP") : "Pick a date"}
                                                </Button>
                                          </PopoverTrigger>
                                          <PopoverContent className="w-auto p-0" >
                                                <Calendar
                                                      mode="single"
                                                      selected={date}
                                                      onSelect={(newDate) => {
                                                            if (newDate) {
                                                                  setDate(newDate);
                                                            }
                                                      }}
                                                      initialFocus
                                                />
                                          </PopoverContent>
                                    </Popover>

                                    <Button onClick={() => date && handleSubmit(date)} >
                                          {loading ? "Submitting" : "Get Transactions"}
                                    </Button>
                              </Form>
                              <Input
                                    placeholder="Search by name, id, or order id..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="max-w-sm"
                              />
                              <Select value={pageSize} onValueChange={setPageSize}>
                                    <SelectTrigger className="w-[180px]">
                                          <SelectValue placeholder="Rows per page" />
                                    </SelectTrigger>
                                    <SelectContent>
                                          <SelectItem value="5">5 per page</SelectItem>
                                          <SelectItem value="10">10 per page</SelectItem>
                                          <SelectItem value="20">20 per page</SelectItem>
                                          <SelectItem value="50">50 per page</SelectItem>
                                          <SelectItem value="100">100 per page</SelectItem>
                                    </SelectContent>
                              </Select>
                        </div>

                        <div className="flex flex-wrap sm:flex-nowrap sm:space-x-8">
                              {["ALL", "INITIATED", "PENDING", "PAID", "FAILED"].map((status) => (
                                    <div key={status} className="flex items-center space-x-2 my-2">
                                          <Checkbox
                                                id={status}
                                                checked={filters.includes(status)}
                                                onCheckedChange={() => handleCheckboxChange(status)}
                                          />
                                          <label
                                                htmlFor={status}
                                                className="text-sm font-medium leading-none"
                                          >
                                                {status}
                                          </label>
                                    </div>
                              ))}
                        </div>

                        <div className="overflow-x-auto rounded-md border">
                              <Table>
                                    <TableHeader>
                                          <TableRow>
                                                <TableHead>Id</TableHead>
                                                <TableHead>Name</TableHead>
                                                <TableHead>Amount</TableHead>
                                                <TableHead>Int Time</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Last Updated</TableHead>
                                                <TableHead>Paid Time</TableHead>
                                                <TableHead>Order Details</TableHead>
                                                <TableHead>Bank Details</TableHead>
                                          </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                          {paginatedData.length > 0 ? (
                                                paginatedData.map((row) => (
                                                      <TransactionRow
                                                            key={row.id}
                                                            row={row}
                                                            formatDate={formatDate}
                                                            adminPermission={adminPermission}
                                                      />))
                                          ) : (
                                                <TableRow>
                                                      <TableCell
                                                            colSpan={9}
                                                            className="h-24 text-center"
                                                      >
                                                            No results.
                                                      </TableCell>
                                                </TableRow>
                                          )}
                                    </TableBody>
                              </Table>
                        </div>

                        <div className="flex items-center justify-between px-2 py-4">
                              <div className="text-sm text-gray-500">
                                    Showing {(currentPage - 1) * Number(pageSize) + 1} to{" "}
                                    {Math.min(currentPage * Number(pageSize), data.length)} of{" "}
                                    {data.length} results
                              </div>
                              <div className="flex items-center space-x-2">
                                    <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                                          disabled={currentPage === 1}
                                    >
                                          Previous
                                    </Button>
                                    <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() =>
                                                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                                          }
                                          disabled={currentPage === totalPages}
                                    >
                                          Next
                                    </Button>
                              </div>
                        </div>
                  </div>
            </React.Suspense>
      );
}




function TransactionRow({
      row,
      formatDate,
      adminPermission

}: {
      row: Transaction;
      formatDate: (dateString: string) => string;
      adminPermission: boolean
}) {
      const [showModal, setShowModal] = useState(false);
      const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
      const fetcher = useFetcher()



      const [loadingRowId, setLoadingRowId] = React.useState<string | null>(null);

      const handleUpdateTransaction = async (rowId: string) => {
            setLoadingRowId(rowId); // Start loading for this row
            const formData = new FormData();
            formData.append("intent", "updateTransaction");
            formData.append("rowId", rowId);

            await fetcher.submit(formData, { method: "PUT" });

            setLoadingRowId(null); // Stop loading
      };
      const handleShowModal = () => {
            setSelectedTransaction(row);
            setShowModal(true);
      }

      const isLoading = fetcher.state !== "idle";
      return (
            <>
                  <TableRow key={row.id}>

                        <TableCell>{row.id}</TableCell>
                        <TableCell>{row.businessName}</TableCell>
                        <TableCell>
                              {new Intl.NumberFormat("en-US", {
                                    style: "currency",
                                    currency: "INR",
                              }).format(parseFloat(row.amount.toString()))}
                        </TableCell>
                        <TableCell>{formatDate(row.initiatedTime)}</TableCell>

                        <TableCell className="flex flex-col gap-2 mt-2">
                              <button
                                    className={`${row.status === "PENDING"
                                          ? "bg-orange-400 hover:bg-orange-200"
                                          : row.status === "INITIATED"
                                                ? "bg-blue-600 hover:bg-blue-400"
                                                : ""
                                          } flex-1 cursor-pointer px-2 py-1 rounded flex items-center justify-center`}
                                    onClick={() => handleUpdateTransaction(row.id.toString())}
                                    disabled={row.status === "PAID" || row.status === "FAILED" || loadingRowId === row.id.toString()}
                              >
                                    {loadingRowId === row.id.toString() ? (
                                          <span className="animate-spin border-2 border-red-200 border-t-transparent rounded-full w-5 h-5"></span>
                                    ) : (
                                          <p
                                                className={
                                                      row.status === "PAID"
                                                            ? "font-bold text-green-600"
                                                            : row.status === "FAILED"
                                                                  ? "font-bold text-red-600"
                                                                  : ""
                                                }
                                          >
                                                {isLoading ? "updating..." : row.status}
                                          </p>
                                    )}
                              </button>

                              {(row.status === "FAILED" || row.status === "PENDING") && adminPermission === true && <Button className="bg-sky-400 text-white font-bold" onClick={handleShowModal}>
                                    Mark As Paid
                              </Button>}
                        </TableCell>
                        <TableCell>{formatDate(row.lastUpdatedTime)}</TableCell>
                        <TableCell>{formatDate(row.completedTime)}</TableCell>
                        <TableCell>
                              <div className="space-y-1">
                                    <p>Id: {row.orderGroupId}</p>
                                    <p>{row.orderStatus}</p>
                                    <p>{row.sellerName}</p>
                                    <p>{row.deliveryDate}</p>
                              </div>
                        </TableCell>
                        <TableCell>
                              <div className="space-y-1">
                                    <p>Id: {row.bankRRN}</p>
                                    <p>Ch: {row.channel}</p>
                                    <p>Note: {row.note}</p>
                              </div>
                        </TableCell>
                  </TableRow>

                  {/* Show modal when button is clicked */}
                  <MarkAsPaid
                        isClosed={() => setShowModal(false)}
                        heading={`Transaction from ${selectedTransaction?.businessName ?? ""}`}
                        isOpen={showModal}
                        content="You are about to mark the payment status as paid. This is an irreversible action." row={selectedTransaction} />


            </>
      );
}




