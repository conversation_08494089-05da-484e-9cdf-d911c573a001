import { CreateStockTransactionInput, StockTransactionType } from "~/types/api/businessConsoleService/ItemStock";
import { Button } from "../ui/button";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "../ui/dialog";
import { useState } from "react";
import { useFetcher, useNavigation } from "@remix-run/react";

interface AddEntryModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemId: number;
  onAddEntry: (data: CreateStockTransactionInput) => Promise<void>;
}
function AddEntryModal({ isOpen, onClose, itemId, onAddEntry }: AddEntryModalProps) {
  const [formData, setFormData] = useState<CreateStockTransactionInput>({
    stockTransactionType: StockTransactionType.CORRECTION, // or another valid StockTransactionType value
    narration: "",
    quantity: 0,
    deliveryDate: new Date().toISOString().split("T")[0],
  });
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);   

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.stockTransactionType || !formData.narration || formData.quantity <= 0) {
      setError("All fields are required and must be valid");
      return;
    }

    setLoading(true);
    try {
      await onAddEntry(formData);
      setFormData({
        stockTransactionType: StockTransactionType.CORRECTION,
        narration: "",
        quantity: 0,
        deliveryDate: new Date().toISOString().split("T")[0],
      });
      setError(null);
      onClose(); // Close modal on success
    } catch (err: any) {
      setError(err.message || "Failed to create transaction");
    } finally {
      setLoading(false);
    }
  };



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Transaction</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          {error && <div className="mb-4 text-red-600">{error}</div>}
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="transactionType" className="text-right text-sm font-medium">
                Type:
              </label>
              <select
                id="transactionType"
                value={formData.stockTransactionType}
                onChange={(e) => setFormData({ ...formData, stockTransactionType: e.target.value as StockTransactionType })}
                className="col-span-3 border rounded-md p-2"
                required
              >
                <option value="">Select Type</option>
                <option value={StockTransactionType.RECEIVED}>Received</option>
                <option value={StockTransactionType.DELIVERED}>Delivered</option>
                <option value={StockTransactionType.SPOILED}>Spoiled</option>
                <option value={StockTransactionType.RETURNED}>Returned</option>
                <option value={StockTransactionType.CORRECTION}>Correction</option>
                <option value={StockTransactionType.CONVERTED}>Converted</option>
              </select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="narration" className="text-right text-sm font-medium">
                Narration:
              </label>
              <input
                id="narration"
                value={formData.narration}
                onChange={(e) => setFormData({ ...formData, narration: e.target.value })}
                className="col-span-3 border rounded-md p-2"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="quantity" className="text-right text-sm font-medium">
                Quantity:
              </label>
              <input
                id="quantity"
                type="number"
                value={formData.quantity}
                onChange={(e) => setFormData({ ...formData, quantity: Number(e.target.value) })}
                min="1"
                className="col-span-3 border rounded-md p-2"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="deliveryDate" className="text-right text-sm font-medium">
                Date:
              </label>
              <input
                id="deliveryDate"
                type="date"
                value={formData.deliveryDate}
                onChange={(e) => setFormData({ ...formData, deliveryDate: e.target.value })}
                className="col-span-3 border rounded-md p-2"
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              type="submit" 
              loading={loading}
              className="ml-2"
              disabled={loading}
            >
              {loading ? "Creating..." : "Add Transaction"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default AddEntryModal;
