import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@components/ui/card";
import { Button } from "@components/ui/button";
import { Badge } from "@components/ui/badge";
import { MessageCircle, Share2, Users } from "lucide-react";

export interface Campaign {
  id: number;
  title: string;
  type: "SMS" | "WhatsApp";
  status: "Active" | "Scheduled" | "Completed";
  reach: string;
  date: string;
}

interface CampaignCardProps {
  campaign: Campaign;
  onViewDetails: (id: number) => void;
}

export function CampaignCard({ campaign, onViewDetails }: CampaignCardProps) {
  const getStatusColor = (status: Campaign["status"]) => {
    switch (status) {
      case "Active":
        return "bg-green-500";
      case "Scheduled":
        return "bg-blue-500";
      case "Completed":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  const getTypeIcon = (type: Campaign["type"]) => {
    switch (type) {
      case "SMS":
        return <MessageCircle className="h-4 w-4" />;
      case "WhatsApp":
        return <Share2 className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{campaign.title}</CardTitle>
          <Badge className={getStatusColor(campaign.status)}>
            {campaign.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center text-sm text-muted-foreground">
            {getTypeIcon(campaign.type)}
            <span className="ml-2">{campaign.type}</span>
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <Users className="h-4 w-4" />
            <span className="ml-2">{campaign.reach}</span>
          </div>
          <div className="text-sm text-muted-foreground">
            {new Date(campaign.date).toLocaleDateString()}
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => onViewDetails(campaign.id)}
        >
          View Details
        </Button>
      </CardFooter>
    </Card>
  );
} 