import * as React from "react";
import type { MasterItemDto } from "~/types/home/<USER>";
import { Button } from "@components/ui/button";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "~/components/ui/pagination";

interface ItemTableProps {
  items: MasterItemDto[];
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onEditItem: (item: MasterItemDto) => void;
  onDuplicateItem: (item: MasterItemDto) => void;
}

function ItemTable({
  items,
  currentPage,
  totalPages,
  itemsPerPage,
  onPageChange,
  onEditItem,
  onDuplicateItem,
}: ItemTableProps) {
  // Ensure items is an array
  const safeItems = Array.isArray(items) ? items : [];
  const totalItems = safeItems.length;

  // Calculate start and end indices for current page (zero-based)
  const startIndex = currentPage * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);

  // Function to generate page numbers with ellipsis
  const getPageNumbers = () => {
    const pages: Array<number | 'ellipsis'> = [];
    const maxVisiblePages = 5;
    const halfVisible = Math.floor(maxVisiblePages / 2);

    let startPage = Math.max(0, currentPage - halfVisible);
    const endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(0, endPage - maxVisiblePages + 1);
    }

    // Add first page
    if (startPage > 0) {
      pages.push(0);
      if (startPage > 1) pages.push('ellipsis');
    }

    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    // Add last page
    if (endPage < totalPages - 1) {
      if (endPage < totalPages - 2) pages.push('ellipsis');
      pages.push(totalPages - 1);
    }

    return pages;
  };

  return (
    <div className="rounded-md border">
      <div className="relative w-full overflow-auto">
        <table className="w-full caption-bottom text-sm">
          <thead>
            <tr className="border-b bg-gray-100">
              <th className="px-4 py-2 text-left">ID</th>
              <th className="px-4 py-2 text-left">Item Name</th>
              <th className="px-4 py-2 text-left">Brand</th>
              <th className="px-4 py-2 text-left">Unit</th>
              <th className="px-4 py-2 text-left">Categories</th>
              <th className="px-4 py-2 text-left">Item Details</th>
              <th className="px-4 py-2 text-left">Status</th>
              <th className="px-4 py-2 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {safeItems.length === 0 ? (
              <tr>
                <td colSpan={8} className="px-4 py-8 text-center text-gray-500">
                  No items found
                </td>
              </tr>
            ) : (
              safeItems.map((item) => (
                <tr key={item.id} className="border-b">
                  <td className="px-4 py-2">{item.id}</td>
                  <td className="px-4 py-2">{item.name}</td>
                  <td className="px-4 py-2">{item.brandName}</td>
                  <td className="px-4 py-2">{item.defaultUnit}</td>
                  <td className="px-4 py-2">
                    {item.categories?.map((cat) => (
                      <div key={cat.id} className="flex gap-2 items-center last:mb-0">
                        <div className="text-xs text-gray-500">ID: {cat.id}</div>
                        <div className="text-sm">{cat.name}</div>
                      </div>
                    )) || '-'}
                  </td>
                  <td className="px-4 py-2">
                    <div className="space-y-1">
                      <div className="text-xs">
                        <span className="font-medium">Min Qty:</span> {item.minimumOrderQty || '-'}
                      </div>
                      <div className="text-sm">
                        <span className="font-medium">Increment Qty:</span> {item.incrementOrderQty || '-'}
                      </div>
                      <div className="text-sm">
                        <span className="font-medium">MRP:</span> {item.mrp || '-'}
                      </div>
                      <div className="text-sm">
                        <span className="font-medium">WtFactor:</span> {'-'}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-2">
                    <span className={`px-2 py-1 rounded-full text-xs ${item.disabled ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}`}>
                      {item.disabled ? 'Disabled' : 'Active'}
                    </span>
                  </td>
                  <td className="px-4 py-2">
                    <div className="flex flex-col gap-2">
                      {/* Edit Button */}
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => onEditItem(item)}
                      >
                        Edit
                      </Button>

                      {/* Duplicate Button */}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onDuplicateItem(item)}
                      >
                        Duplicate
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
        <div className="flex items-center justify-end space-x-2 py-4">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e: React.MouseEvent) => {
                    e.preventDefault();
                    if (currentPage > 0) {
                      onPageChange(currentPage - 1);
                    }
                  }}
                  aria-disabled={currentPage <= 0}
                />
              </PaginationItem>

              {getPageNumbers().map((page, index) => (
                page === 'ellipsis' ? (
                  <PaginationItem key={`ellipsis-${index}`}>
                    <PaginationEllipsis />
                  </PaginationItem>
                ) : (
                  <PaginationItem key={page}>
                    <PaginationLink
                      href="#"
                      onClick={(e: React.MouseEvent) => {
                        e.preventDefault();
                        onPageChange(Number(page));
                      }}
                      isActive={currentPage === page}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                )
              ))}

              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e: React.MouseEvent) => {
                    e.preventDefault();
                    if (currentPage < totalPages - 1) {
                      onPageChange(currentPage + 1);
                    }
                  }}
                  aria-disabled={currentPage >= totalPages - 1}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </div>
    </div>
  );
}

export default ItemTable;