import { GoogleMap, InfoWindow, Load<PERSON>, <PERSON>er, Polygon } from "@react-google-maps/api";
import { useCallback, useState } from "react";
import { Circle, Edit, Trash2, X } from "lucide-react";
import { MasterLocalities, smSellerArea, StateAndDistricts } from "~/types/api/businessConsoleService/SellerManagement";
import { Switch } from "./switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select";
import { Checkbox } from "./checkbox";
import { newLocal } from "~/routes/home.networkDetails";
import { number, string } from "zod";
import { useFetcher } from "@remix-run/react";
import SpinnerLoader from "../loader/SpinnerLoader";
import { Button } from "./button";
interface PointerLocation {
      latitude: number | null;
      longitude: number | null;
}

interface InfoWindowState {
      isShown: boolean;
      areaId: number | null;
      selectedAreaDetails: smSellerArea | null;
}

interface MapComponentProps {
      googleMapsApiKey: string;
      sellerAreas: smSellerArea[];
      visibleAreas: Set<number>;
      getPolygonColor: (index: number) => string;
      decodePolygon: (polygon: string) => google.maps.LatLngLiteral[];
      getPolygonCenter: (polygon: google.maps.LatLngLiteral[]) => google.maps.LatLngLiteral;
      handleLocateShopClicked: (state: boolean) => void;
      onLoad: (mapInstance: google.maps.Map) => void;
      onUnmount: () => void;
      isLocateShopClicked: boolean;
      updateToggle: (areaId: number) => void,
      statesAndDistricts: StateAndDistricts[],
      userId: number,
      sellerId: number,
      handleSubmit: (selectedLocalites: MasterLocalities[]) => void

}
const MapComponent: React.FC<MapComponentProps> = ({
      googleMapsApiKey,
      sellerAreas,
      visibleAreas,
      getPolygonColor,
      decodePolygon,
      getPolygonCenter,
      handleLocateShopClicked,
      onLoad,
      onUnmount,
      isLocateShopClicked,
      updateToggle,
      statesAndDistricts,
      userId,
      sellerId,
      handleSubmit
}) => {
      const [mapLoaded, setMapLoaded] = useState(false);
      const [latitude, setLatitude] = useState<number | null>(null);
      const [longitude, setLongitude] = useState<number | null>(null);
      const [infoWindowShown, setInfoWindowShown] = useState<InfoWindowState>({
            isShown: false,
            areaId: null,
            selectedAreaDetails: null,
      });
      const [pointerLocation, setPointerLocation] = useState<PointerLocation>({ latitude: null, longitude: null });
      const [showMarker, setShowMarker] = useState(false);
      const [isAddLocalityClicked, setisAddLocalityClicked] = useState(false);
      const [selectedState, setSelectedState] = useState('');
      const [masterLocalities, setMasterLocalities] = useState<MasterLocalities[]>([]);
      const [newLocalities, setNewLocalities] = useState<MasterLocalities[]>([])
      let stateList: string[] = [];
      statesAndDistricts?.forEach((area) => stateList.push(area.state));
      const [selectedDistrict, setSelectedDistrict] = useState('');
      const handleFindLocations = useCallback((
            lat: number | null,
            long: number | null,
            showMarker: boolean
      ) => {
            if (lat && long) {
                  setPointerLocation({ latitude: lat, longitude: long });
                  setShowMarker(showMarker);
            }
            else {
                  alert("Please enter valid numeric values for latitude and longitude.");
            }
      }, []);


      const handleSwitch = (areaId: number) => {

            updateToggle(areaId);
      };
      const handleAddLocalityClicked = useCallback((state: boolean) => {
            setisAddLocalityClicked(state);
      }, []
      );

      const handleMasterLocalitiesClicked = useCallback(async (userId: number, state: string, district: string) => {
            if (!state || !district) {
                  alert("Please select a state and district to proceed...");
                  return;
            }

            try {
                  const response = await fetch(`./api/masterLocalities?userId=${userId}&state=${state}&district=${district}`);
                  //apps/business-console/app/routes/api/masterLocalities.ts
                  const data = await response.json();
                  if (!response.ok) {
                        throw new Error(data.error || "Unknown error");
                  }
                  setMasterLocalities(data.masterLocalities.data);
            } catch (error) {
                  console.error("Error fetching Master Localities:", error);
                  alert("Fetching Master Localities failed. Please try again.");
            }
      }, []);

      const [selectedLocalities, setSelectedLocalities] = useState<MasterLocalities[]>([]);

      const [selectedLocalityId, setSelectedLocalityId] = useState<number | null>(null);
      const handleNewLocalities = useCallback((id: number, localityDetails: MasterLocalities, agentUserId?: number) => {
            setNewLocalities((prevState) => {
                  let updatedLocalities;
                  if (prevState.some((abc) => abc.id === id)) {
                        updatedLocalities = prevState.filter((abc) => abc.id !== id);
                  } else {
                        updatedLocalities = [...prevState, localityDetails];
                  }

                  setSelectedLocalities(updatedLocalities);
                  return updatedLocalities;
            });
      }, []);

      // const handleNewLocalities = useCallback(
      //       (id: number, localityDetails: MasterLocalities) => {
      //             setNewLocalities((prevState) => {
      //                   if (prevState.some((abc) => abc.id === id)) {
      //                         // Remove the locality if it exists
      //                         return prevState.filter((abc) => abc.id !== id);
      //                   } else {
      //                         // Add the new locality
      //                         return [...prevState, localityDetails];
      //                   }
      //             });

      //             setSelectedLocalityId((prev) => (prev === id ? null : id)); // Toggle selection
      //       },
      //       []
      // );
      const fetcher = useFetcher()
      const isLoading = fetcher.state !== "idle";
      const handleSubmitLocalities = () => {
            handleSubmit(selectedLocalities)
            setisAddLocalityClicked(false)
            setSelectedDistrict('');
            setSelectedState('');
            stateList = [];
            setMasterLocalities([]);
            setSelectedLocalityId(null);
            setSelectedLocalities([]);

      }


      const handleMarkerClick = useCallback(
            (id: number,
                  areaDetails: smSellerArea
            ) => {
                  setInfoWindowShown((prevState) => {
                        if (prevState.isShown) {
                              if (prevState.areaId === id) {

                                    return { areaId: null, isShown: false, selectedAreaDetails: null };
                              } else {

                                    return { areaId: id, isShown: true, selectedAreaDetails: areaDetails };
                              }
                        } else {

                              return { areaId: id, isShown: true, selectedAreaDetails: areaDetails };
                        }
                  });
            },
            []
      );



      return (
            <LoadScript googleMapsApiKey={googleMapsApiKey}>

                  <div className="flex w-full h-[87vh] border rounded-xl border-neutral-200 my-4">
                        <div className="flex flex-col gap-4 w-[20vw] overflow-auto bg-white shadow rounded-xl p-3">
                              {sellerAreas.length ? (

                                    sellerAreas.map((seller, index) => {

                                          return (
                                                <div key={seller.sellerAreaId} className="flex flex-col w-full gap-3 bg-white p-2 rounded-lg shadow">
                                                      <div className="flex gap-2 justify-between items-center">
                                                            <div>
                                                                  <div className="text-sm text-typography-400">id: {seller.sellerAreaId}</div>
                                                                  <div className="text-lg text-typography-800">{seller.area.name}</div>
                                                            </div>
                                                            <Circle size={"1rem"} fill={getPolygonColor(index)} color={getPolygonColor(index)} />
                                                      </div>


                                                      <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="text-red-500 hover:text-red-900"
                                                            onClick={() => {
                                                                  if (confirm("Are you sure you want to delete this area?")) {
                                                                        handleSwitch(seller?.sellerAreaId);
                                                                  }
                                                            }}
                                                            style={{ alignSelf: "flex-end" }}
                                                      >
                                                            <Trash2 size={16} />
                                                      </Button>


                                                </div>
                                          )
                                    })
                              ) : (
                                    <div className="h-24 text-center">No Network Areas found.</div>
                              )}
                        </div>
                        <div className="relative flex-1 h-full bg-white">
                              <div className='absolute flex z-10 top-0 left-0 w-full justify-between p-3 max-h-full'>

                                    {!isAddLocalityClicked ? (<div className='p-3 h-fit bg-white rounded-xl flex gap-2 text-blue-600 items-center shadow '>
                                          <button
                                                onClick={() => { handleAddLocalityClicked(true); }}
                                          >
                                                + &nbsp; Add New Locality
                                          </button>
                                    </div>) : (<div className='p-3 bg-white rounded-xl flex flex-col gap-2  items-center shadow '>

                                          <div className='flex w-full justify-between'>
                                                Add Localities
                                                <button onClick={() => {

                                                      handleAddLocalityClicked(false)
                                                }}><X size={16} /></button>
                                          </div>
                                          <div className='flex w-full p-1 items-center gap-3'>
                                                <Select value={selectedState} onValueChange={setSelectedState}>
                                                      <SelectTrigger className="w-[180px]">
                                                            <SelectValue placeholder="Select state" />
                                                      </SelectTrigger>
                                                      <SelectContent>
                                                            {stateList.map((state) => (
                                                                  <SelectItem key={state} value={state}>{state}</SelectItem>
                                                            ))}
                                                      </SelectContent>
                                                </Select>
                                                <Select value={selectedDistrict} onValueChange={(newDistrict) => {
                                                      setSelectedDistrict(newDistrict); // Update selected district
                                                      handleMasterLocalitiesClicked(userId, selectedState, newDistrict); // Fetch data
                                                }} disabled={!selectedState}>
                                                      <SelectTrigger className="w-[180px]">
                                                            <SelectValue placeholder="Select District" />
                                                      </SelectTrigger>
                                                      <SelectContent>
                                                            {statesAndDistricts
                                                                  ?.filter((abc) => abc.state === selectedState)
                                                                  .map((district) => (
                                                                        <SelectItem key={district.district} value={district.district}>
                                                                              {district.district}
                                                                        </SelectItem>
                                                                  ))}
                                                      </SelectContent>
                                                </Select>
                                          </div>
                                          <div className='flex flex-col gap-2 self-start max-h-full w-full overflow-auto p-2 '>
                                                {masterLocalities && masterLocalities.length > 0 ? (
                                                      (masterLocalities.filter(a => !sellerAreas?.some(na => na.area.id === a.id)).length > 0 ? masterLocalities.filter(a => !sellerAreas?.some(na => na.area.id === a.id)) : masterLocalities).map((locality, index) => (
                                                            <>
                                                                  <div key={locality.id}>
                                                                        <label className="cursor-pointer flex items-center gap-2 p-1" htmlFor={`locality-${locality.id}`}>
                                                                              <Checkbox
                                                                                    id={`locality-${locality.id}`}
                                                                                    checked={newLocalities.some((abc) => abc.id === locality.id)}
                                                                                    onCheckedChange={() => handleNewLocalities(locality.id, locality)}
                                                                              />
                                                                              <div className='flex flex-col gap-2'>
                                                                                    <span>{locality.id} - {locality.name}</span>

                                                                              </div>
                                                                        </label>
                                                                  </div>
                                                                  {index < masterLocalities.length - 1 && (<div className='border-b border-neutral-200'></div>)} </>
                                                      ))
                                                ) : (
                                                      <div>No Localities Fetched yet</div>
                                                )} </div>


                                          <button
                                                type="submit"
                                                className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-400"
                                                onClick={() => handleSubmitLocalities()}
                                                disabled={selectedLocalities.length === 0}
                                          >

                                                {isLoading ? "Submitting..." : "Submit Localities"
                                                }

                                          </button>
                                    </div>)
                                    }
                                    {!isLocateShopClicked ? (
                                          <div className="p-3 absolute z-20 top-2 right-2 bg-white rounded-xl flex gap-2 text-blue-600 items-center shadow">
                                                <button onClick={() => handleLocateShopClicked(true)}>
                                                      📍 &nbsp; Locate a shop
                                                </button>
                                          </div>
                                    ) : (
                                          <div className="p-3 absolute z-20 top-2 right-2 bg-white rounded-xl flex flex-col gap-2 items-center shadow">
                                                <div className="flex w-full justify-between">
                                                      Locate a shop
                                                      <button onClick={() => handleLocateShopClicked(false)}>
                                                            <X size={16} />
                                                      </button>
                                                </div>
                                                <div className="flex w-full p-1 items-center justify-between">
                                                      <p>Latitude : </p>
                                                      <input
                                                            type="text"
                                                            className="border border-neutral-400 rounded-md p-1"
                                                            onChange={(e) => setLatitude(parseFloat(e.target.value))}
                                                      />
                                                </div>
                                                <div className="flex gap-1 p-1 items-center">
                                                      <p>Longitude : </p>
                                                      <input
                                                            type="text"
                                                            className="border border-neutral-400 rounded-md p-1"
                                                            onChange={(e) => setLongitude(parseFloat(e.target.value))}
                                                      />
                                                </div>
                                                <button
                                                      className="text-primary border border-primary p-2 rounded-md"
                                                      onClick={() => handleFindLocations(latitude ?? 0, longitude ?? 0, true)}
                                                >
                                                      📍 &nbsp; Find Location
                                                </button>
                                          </div>
                                    )}
                              </div>
                              {!mapLoaded && (
                                    <div className="flex items-center justify-center h-full">
                                          <div className="loader">Loading Map...</div>
                                    </div>
                              )}


                              {googleMapsApiKey ? (
                                    <GoogleMap
                                          mapContainerStyle={{ width: "100%", height: "100%" }}
                                          center={{ lat: 12.9716, lng: 77.5946 }}
                                          zoom={11}
                                          onLoad={(mapInstance) => {
                                                onLoad(mapInstance); // Ensure this is always a valid function
                                                setMapLoaded(true);
                                          }}
                                          onUnmount={() => {
                                                onUnmount();
                                                setMapLoaded(false);
                                          }}
                                          options={{
                                                mapTypeControl: false,
                                                streetViewControl: false,
                                                fullscreenControl: false,
                                                clickableIcons: false,
                                                gestureHandling: "auto",
                                          }}
                                    >
                                          {mapLoaded ? (sellerAreas?.filter((abc) => visibleAreas.has(abc.sellerAreaId))?.map((area, index) => (
                                                area.area.polygon
                                                && (

                                                      <Polygon
                                                            key={area.sellerAreaId}
                                                            paths={decodePolygon(area.area.polygon)}
                                                            options={{
                                                                  fillColor: isAddLocalityClicked ? "#3b82f6" : getPolygonColor(index),
                                                                  fillOpacity: 0.2,
                                                                  strokeColor: isAddLocalityClicked ? "#3b82f6" : getPolygonColor(index),
                                                                  strokeOpacity: 1,
                                                                  strokeWeight: 2,
                                                                  draggable: false,
                                                                  editable: false,
                                                                  geodesic: false,
                                                                  zIndex: 1,
                                                                  clickable: true, // Ensure polygons remain clickable
                                                            }}
                                                            onClick={() => handleMarkerClick(area.sellerAreaId, area)}

                                                      />
                                                )
                                          ))) : null}

                                          {mapLoaded ? (newLocalities?.map((area, index) => (
                                                area.polygon
                                                && (

                                                      <Polygon
                                                            key={area.id}
                                                            paths={decodePolygon(area.polygon)}
                                                            options={{
                                                                  fillColor: "#10b981",
                                                                  fillOpacity: 0.4,
                                                                  strokeColor: "#10b981",
                                                                  strokeOpacity: 1,
                                                                  strokeWeight: 2,
                                                                  draggable: false,
                                                                  editable: false,
                                                                  geodesic: false,
                                                                  zIndex: 10,
                                                                  clickable: false, // Ensure polygons remain clickable
                                                            }}
                                                      />
                                                )
                                          ))) : null}



                                          {infoWindowShown && infoWindowShown.areaId && infoWindowShown.isShown && infoWindowShown.selectedAreaDetails && (

                                                <>
                                                      {console.log("entered infowindow params with ##########", infoWindowShown)}
                                                      <InfoWindow
                                                            position={getPolygonCenter(decodePolygon(infoWindowShown.selectedAreaDetails?.area?.polygon || ""))}
                                                            onCloseClick={() => setInfoWindowShown({ isShown: false, areaId: null, selectedAreaDetails: null })}
                                                            options={{
                                                                  headerDisabled: true,
                                                                  minWidth: 200,
                                                                  disableAutoPan: true,
                                                            }}
                                                      >
                                                            <div className="flex flex-col gap-2 overflow-hidden ">
                                                                  <div className='flex justify-between w-full align-middle items-center'>
                                                                        <h2 className='text-md font-semibold text-typography-300'>Locality Info</h2>
                                                                        <button className="inline-flex items-center gap-1 hover:text-blue-800"
                                                                              onClick={() => setInfoWindowShown({ isShown: false, areaId: null, selectedAreaDetails: null })}>
                                                                              <X className="h-5 w-5" />
                                                                        </button>
                                                                  </div>
                                                                  <div className="flex flex-col gap-1 text-sm font-medium text-typography-700 w-[calc(100%-1rem)]">
                                                                        <p><span className='text-sm text-typography-300 font-thin'>Area id:</span> {infoWindowShown.selectedAreaDetails?.sellerAreaId}</p>
                                                                        <p><span className='text-sm text-typography-300 font-thin'>Area Name: </span> {infoWindowShown.selectedAreaDetails?.area?.name}</p>
                                                                  </div>
                                                            </div>
                                                      </InfoWindow>
                                                      <style>
                                                            {`.info-window-wrapper {
                                  padding: 10px; 
                                }
                              
                                .gm-style-iw { 
                                  padding: 12px !important; 
                                }
                                .gm-style-iw-d { 
                                  padding: 0px !important; 
                                  overflow:hidden !important;
                                }`}
                                                      </style> </>
                                          )}
                                          {showMarker && pointerLocation.latitude !== null && pointerLocation.longitude !== null && (
                                                <Marker position={{ lat: pointerLocation.latitude, lng: pointerLocation.longitude }} />
                                          )}
                                    </GoogleMap>
                              ) : (
                                    <div className="flex items-center justify-center h-full">
                                          <p className="text-red-500">Google Maps API key is missing.</p>
                                    </div>
                              )}
                        </div>
                  </div>
            </LoadScript>
      );
};

export default MapComponent;
