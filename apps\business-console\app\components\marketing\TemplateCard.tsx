import { <PERSON><PERSON> } from "@components/ui/button";
import {
  Card,
  CardContent,
} from "@components/ui/card";
import { Badge } from "@components/ui/badge";
import { MessageCircle, Share2, Variable } from "lucide-react";
import type { Template } from "../../schemas/marketing";
import { BsFillReplyFill } from "react-icons/bs";
import { FaWhatsapp } from "react-icons/fa";


interface TemplateCardProps {
  template: Template;
  onSendMessage: (template: Template) => void;
}

export function TemplateCard({ template, onSendMessage }: TemplateCardProps) {
  // Get icon based on template type
  const getTypeIcon = (type: Template["type"]) => {
    switch (type) {
      case "SMS":
        return <MessageCircle className="h-4 w-4" />;
      case "WhatsApp":
      case "hybrid":
        return <Share2 className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-4 flex flex-col">
        {/* Template Info Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            {getTypeIcon(template.type)}
            <h3 className="font-medium">{template.name}</h3>
          </div>
          <Badge variant="outline" className="font-normal">
            {template.type}
          </Badge>
        </div>

        {/* WhatsApp Preview */}
        <div className="border rounded-lg p-4 mb-4 space-y-4 bg-gray-50">
          <div className="text-sm text-muted-foreground mb-2">WhatsApp Preview:</div>
          
          {/* Message Bubble */}
          <div className="bg-[#e7ffdb] rounded-lg p-3 max-w-[85%] ml-auto relative shadow-sm">
            {/* Message Header */}
            <div className="text-sm text-[#111b21] font-bold mb-2">
              {template.header}
            </div>
            {/* Message Content */}
            <div className="whitespace-pre-wrap text-[#111b21] text-sm">
              {template.preview || template.content}
            </div>
            
            {/* Time */}
            <div className="text-right text-xs text-[#667781] mt-1">
              09:30 AM
            </div>

            {/* Tail */}
            <div className="absolute right-0 top-0 w-2 h-4 bg-[#e7ffdb] transform translate-x-[7px]" style={{
              clipPath: 'polygon(0 0, 100% 0, 0 100%)'
            }} />
            <div className="flex flex-col justify-center gap-1">
              {template.ctas?.map((cta) => (
                <>
                  <div className="border-t mt-1 -mx-3"></div>
                  <div key={cta.id} className="text-blue-500 px-2 py-1 text-sm flex gap-2 items-center justify-center">
                    {cta.type === "quick_reply" ? <BsFillReplyFill />: null} <span>{cta.label}</span>
                  </div>
                </>
              ))}
            </div>
          </div>
        </div>

        {/* Variables Section */}
        {template.variables.length > 0 && (
          <div className="space-y-2 mb-4">
            <div className="flex items-center text-sm text-muted-foreground">
              <Variable className="h-4 w-4 mr-2" />
              <span>Variables</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {template.variables.map((variable) => (
                <Badge key={variable} variant="secondary">
                  {variable}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Last Used & Action */}
        <div className="flex items-center justify-between mt-4 self-end">
          {template.lastUsed && (
            <span className="text-sm text-muted-foreground">
              Last used: {template.lastUsed}
            </span>
          )}
          <Button
            className="rounded-md border border-primary-200 text-primary-600 shadow-none flex items-center hover:bg-primary-600 hover:text-white" 
            variant="outline" 
            size="sm"
            onClick={() => onSendMessage(template)}
          >
            <FaWhatsapp className="mr-2" />
            <span>Send Message</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 