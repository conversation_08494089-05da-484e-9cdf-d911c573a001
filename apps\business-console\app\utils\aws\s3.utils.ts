// src/utils/s3Utils.ts
import { Upload } from "@aws-sdk/lib-storage";
import { s3Client } from "@utils/aws/aws.utils.js";
// import logger from "@utils/express-logger.js";

interface S3UploadConfig {
  bucketName: string;
  key: string;
  body: Buffer;
  contentType: string;
}

class S3Utils {
  /**
   * Uploads a file to S3 using the Upload class for better handling
   * @param config S3 upload configuration
   */
  public async uploadFile(config: S3UploadConfig): Promise<string> {
    const { bucketName, key, body, contentType } = config;
    try {
      const upload = new Upload({
        client: s3Client as any,
        params: {
          Bucket: bucketName,
          Key: key,
          Body: body,
          ContentType: contentType,
        },
        queueSize: 4, // concurrency
        partSize: 5 * 1024 * 1024, // 5MB
        leavePartsOnError: false,
      });

      upload.on("httpUploadProgress", (progress) => {
        console.log(
          `Uploading ${key}: ${progress.loaded}/${progress.total} bytes`
        );
      });

      const result = await upload.done();
      console.log(`Successfully uploaded ${key} to S3.`);
      return result.Location || `https://${bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
    } catch (error: any) {
      console.log(`Failed to upload ${key} to S3: ${error.message}`);
      throw error;
    }
  }
}

export default new S3Utils();
