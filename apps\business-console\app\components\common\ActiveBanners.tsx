import { <PERSON><PERSON><PERSON>, <PERSON>ci<PERSON> } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { useState, useEffect } from "react";
import { Form, useFetcher } from "@remix-run/react";
import { BuyerNetworkBanners } from "~/types/api/businessConsoleService/BuyerAccountingResponse";

interface InActiveBannersProps {
      bannerDetails: BuyerNetworkBanners;
}

export default function ActiveBanners({ bannerDetails }: InActiveBannersProps) {
      const [sequence, setSequence] = useState(bannerDetails.sequenceId);
      const [isEditing, setIsEditing] = useState(false);
      const [error, setError] = useState<string | null>(null);
      const fetcher = useFetcher();
      const loading = fetcher.state === "submitting";

      // Reset sequence to original value when bannerDetails changes
      useEffect(() => {
            setSequence(bannerDetails.sequenceId);
      }, [bannerDetails.sequenceId]);

      // Handle input change and reset logic
      const handleChange = (value: string) => {
            const numValue = Number(value);
            setSequence(numValue);

            if (isNaN(numValue) || numValue < 1) {
                  setError("Sequence must be a number greater than 0");
            } else {
                  setError(null);
            }
      };

      // Save the sequence
      const handleSave = () => {
            if (error || sequence === bannerDetails.sequenceId) {
                  setIsEditing(false);
                  return;
            }

            const formData = new FormData();
            formData.append("netWorkId", bannerDetails.networkId.toString());
            formData.append("sequenceId", sequence.toString());
            formData.append("actionType", "updateSequence");
            formData.append("bannerId", bannerDetails.id.toString());

            fetcher.submit(formData, { method: "put" });
            setIsEditing(false); // Exit edit mode after submission
      };


      return (
            <div className="flex flex-col shadow-lg bg-white px-6 py-6 w-full rounded-xl border border-gray-200 mb-2 transition-all hover:shadow-xl">
                  {/* Banner Image Section */}
                  <div className="flex gap-8 border-b border-dashed border-gray-300 pb-4 w-full">
                        {/* <p className="text-2xl font-semibold text-gray-800">{bannerDetails?.id}.</p> */}
                        <img
                              src={bannerDetails.bannerUrl}
                              alt="Banner"
                              className="rounded-lg sm: w-full md:max-w-md h-[160px] object-fill shadow-sm"
                        />
                  </div>

                  {/* Sequence & Remove Button Section */}
                  <div className="mt-4 w-full">
                        <p className="text-sm text-gray-500 font-medium mb-1">Sequence</p>
                        <div className="flex items-center justify-between w-full gap-2">
                              {/* Sequence Input/Edit */}
                              <div className="relative flex items-center">
                                    {isEditing ? (
                                          <input
                                                type="number"
                                                value={sequence}
                                                onChange={(e) => handleChange(e.target.value)}
                                                onBlur={() => handleSave()} // Call handleSave directly when input loses focus
                                                onKeyPress={(e) => e.key === "Enter" && handleSave()}
                                                className={`w-20 px-3 py-2 text-sm text-gray-800 bg-white border ${error ? "border-red-500" : "border-gray-300 focus:ring-2 focus:ring-blue-500"
                                                      } rounded-md shadow-sm focus:outline-none transition-all`}
                                                min="1"
                                                autoFocus
                                          />
                                    ) : (
                                          <div className="flex items-center gap-2 px-3 py-2 bg-gray-100 border border-gray-300 rounded-md">
                                                <p className="text-sm text-gray-800 font-medium">{sequence}</p>
                                                <button
                                                      onClick={() => setIsEditing(true)}
                                                      className="text-gray-500 hover:text-blue-600 transition-colors"
                                                >
                                                      <Pencil size={16} />
                                                </button>
                                          </div>
                                    )}
                              </div>
                              {/* Remove Button */}
                              <Form method="post" className="flex-shrink-0">
                                    <input type="hidden" name="actionType" value="updateBannerStatus" />
                                    <input type="hidden" name="bannerId" value={bannerDetails?.id} />
                                    <input
                                          type="hidden"
                                          name="status"
                                          value={bannerDetails?.active != null ? String(bannerDetails.active) : "false"}
                                    />
                                    <Button
                                          className="flex items-center gap-2 py-2 px-4 text-white bg-secondary-600 hover:bg-secondary-700 rounded-md shadow-md transition-all disabled:opacity-50"
                                          type="submit"
                                          disabled={loading}
                                    >
                                          {loading ? (
                                                "Updating..."
                                          ) : (
                                                <>
                                                      <CircleMinus size={20} />
                                                      <span>Remove Banner</span>
                                                </>
                                          )}
                                    </Button>
                              </Form>
                        </div>
                  </div>
            </div>
      );
}