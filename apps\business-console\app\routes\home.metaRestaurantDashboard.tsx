import { useLoaderData, useSubmit, useSearchParams } from "@remix-run/react";
import { LoaderFunction, LoaderFunctionArgs, json } from "@remix-run/node";
import jwt from "jsonwebtoken";
import { useEffect, useState } from "react";
import { withAuth } from "~/utils/auth-utils";

const METABASE_SECRET_KEY = process.env.METABASE_SECRET_KEY || "";
const METABASE_SITE_URL = process.env.METABASE_SITE_URL || "http://43.205.118.52:4001";

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {


      if (!METABASE_SECRET_KEY) {
            throw new Error("Metabase secret key is not configured.");
      }

      const payload = {
            resource: { dashboard: 8 },
            params: {
                 
            },
            exp: Math.round(Date.now() / 1000) + (10 * 60) // expires in 10 minutes
      };

      const token = jwt.sign(payload, METABASE_SECRET_KEY);
      try{
      const embedUrl = `${METABASE_SITE_URL}/embed/dashboard/${token}#bordered=false&titled=false`;

      return json({ embedUrl });
      }
      catch(err){
        console.log(err,"error while showing embedUrl")
      }
});

export default function MetaRestaurantDashBoard() {
      const { embedUrl,  } = useLoaderData<typeof loader>();
      const [isLoading, setIsLoading] = useState(true);
     


      // Handle iframe loading state
      useEffect(() => {
            if (embedUrl) {
                  setIsLoading(false);
            }
      }, [embedUrl]);

      // Scroll synchronization
      useEffect(() => {
            const handleScroll = () => {
                  const iframe = document.getElementById("metabase-iframe") as HTMLIFrameElement;
                  if (iframe) {
                        iframe.contentWindow?.scrollTo(0, window.scrollY);
                  }
            };

            window.addEventListener("scroll", handleScroll);
            return () => window.removeEventListener("scroll", handleScroll);
      }, []);

    

      return (
            <div className="flex min-h-screen">
                  <main className="flex-1 overflow-y-auto">
                        <div className="p-4 sm:p-6">
                             

                              <div className="bg-white shadow-md rounded-md overflow-hidden">
                                    {isLoading ? (
                                          <div className="flex justify-center items-center h-96">
                                                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                                          </div>
                                    ) : embedUrl ? (
                                          <iframe
                                                id="metabase-iframe"
                                                src={embedUrl}
                                                title="Metabase Dashboard"
                                              
                                                className="w-full h-[2000px] border-0"
                                                allowTransparency
                                          />
                                    ) : (
                                          <div className="p-6 text-center text-red-500">
                                                Failed to load the dashboard.
                                          </div>
                                    )}
                              </div>
                        </div>
                  </main>
            </div>
      );
}