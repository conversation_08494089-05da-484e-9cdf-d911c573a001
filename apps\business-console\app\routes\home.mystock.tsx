import { useEffect, useState } from "react";
import { useToast } from "~/hooks/use-toast";
import { ItemStock } from "~/types/api/businessConsoleService/ItemStock";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Input } from "~/components/ui/input";
import { Layout } from "~/root";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { json, Link, useFetcher, useLoaderData } from "@remix-run/react";
import { Button } from "~/components/ui/button";
import { Switch } from "~/components/ui/switch";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { getMyStocks, updateMyStock } from "~/services/stockservices/mystockservice";
import { User } from "~/types";

 interface LoaderData{
  stockItems: ItemStock[];
  pageNo:number,
  pageSize:number,
  sellerId:number,
  
 }
interface ActionData{
  error: string | null;
  success: boolean,
  stockData: ItemStock
 }
export const loader = withAuth(async ({ request,user }) => {
  const url = new URL(request.url);
  const SellerId= parseInt(url.searchParams.get("sellerId") as string||"0")
   const isSeller = SellerId==null || isNaN(SellerId) || SellerId === 0;
  const pageNo =parseInt(url.searchParams.get("page")||"0");
  const pageSize = parseInt(url.searchParams.get("pageSize")||"100")
  const matchBy = url.searchParams.get("matchBy") || "";
  try {
    const response = await getMyStocks(isSeller,SellerId,pageNo,pageSize,matchBy,request);
    return withResponse({
      stockItems: response.data,
      pageNo,
      pageSize,
      sellerId: SellerId,
    },response?.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    // Return a JSON-based error shape
    throw new Response("failed to get sellers", { status: 500 })
  }
});
export const action = withAuth(async ({ request }: { request: Request,  }) => {
  const formData = await request.formData();
  const stockId = parseInt(formData.get('stockId') as string);
  const active = formData.has('active') ? formData.get('active') === 'true' : undefined;
  const pricePerUnit = formData.has('pricePerUnit') ? parseFloat(formData.get('pricePerUnit') as string) : undefined;
  const maxAvailableQty = formData.has('maxAvailableQty') ? parseFloat(formData.get('maxAvailableQty') as string) : undefined;
        const sellerId = parseInt(formData.get('sellerId') as string);
        const isSeller = sellerId==null || isNaN(sellerId) || sellerId === 0;
 
  // Only include fields that are present
  const requestBody: any = { stockId };
  if (typeof active === 'boolean') requestBody.active = active;
  if (typeof pricePerUnit === 'number' && !isNaN(pricePerUnit)) requestBody.pricePerUnit = pricePerUnit;
  if (typeof maxAvailableQty === 'number' && !isNaN(maxAvailableQty)) requestBody.maxAvailableQty = maxAvailableQty;

  try {
    const response = await updateMyStock( isSeller, stockId, sellerId, requestBody, request);
    return withResponse({
      stockData: response.data,
      success: true,
      error: null,
      
    }, response?.headers);
  } catch (error) {
    withResponse({
      stockData: null,
      success: false,
      error: "failed to update stock",
    }, undefined)
  
       
    console.error("Error in action:", error);
    throw new Response("failed to update stock", { status: 500 });
  }
});

export default function MyStock() {
  const { stockItems, pageNo, pageSize,sellerId } = useLoaderData<LoaderData>();
  const [items, setItems] = useState<ItemStock[]>(stockItems)
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("itemName");
  const { toast } = useToast();
//   const actionFetcher= useFetcher<ActionData>()s
 useEffect(()=>{
   if (stockItems ){
   setItems(stockItems)
 }
 },[stockItems])

  const fetcher = useFetcher<ActionData>();

  // Re-run the loader after a successful action
  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data?.success && fetcher.data.stockData) {
      setItems((prevItems) =>
        prevItems.map((item) =>
          item.stockId === fetcher.data?.stockData?.stockId ? fetcher.data?.stockData : item
        )
      );
      toast({
        title: "Stock updated successfully",
        variant: "default",
        description: "Stock updated successfully",
      });
      // Use pageNo and pageSize from loader data
    } else if (fetcher.state === "idle" && fetcher.data?.error) {
      toast({
        title: "Something went wrong",
        variant: "destructive",
        description: fetcher.data?.error,
      });
    }
  }, [fetcher.state, fetcher.data,  ]);
 

  const filteredItems = items.filter(item => 
    item.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.distributor.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const sortedItems = [...filteredItems].sort((a, b) => {
    switch (sortBy) {
      case "itemName":
        return a.itemName.localeCompare(b.itemName);
      case "distributerName":
        return a.distributor.localeCompare(b.distributor);
      case "myPrice":
        return b.pricePerUnit - a.pricePerUnit;
      case "newOrders":
        return b.maxAvailableQty - a.maxAvailableQty;
      case "active":
        return a.active === b.active ? 0 : a.active ? -1 : 1;
      default:
        return 0;
    }
  });
  
  // const handlePriceEdit = (id: string, newPrice: number) => {
  //   if (isNaN(newPrice) || newPrice <= 0) {
  //     toast({
  //       title: "Invalid Price",
  //       description: "Please enter a valid price amount",
  //       variant: "destructive"
  //     });
  //     return;
  //   }
    
  //   updateItem(id, { pricePerUnit: newPrice });
  // };
  return (
    <Layout>
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">My Stock</h1>
        <p className="text-muted-foreground mt-2">
          Manage your inventory items and track stock levels
        </p>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search by item name or distributer..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </div>
        <div className="w-full md:w-48">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger>
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="itemName">Item Name</SelectItem>
              <SelectItem value="distributerName">Distributer</SelectItem>
              <SelectItem value="inStock">Stock Level</SelectItem>
              <SelectItem value="myPrice">Price</SelectItem>
              <SelectItem value="newOrders">New Orders</SelectItem>
              <SelectItem value="active">Active Status</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item Name</TableHead>
              <TableHead>Distributer</TableHead>
              <TableHead>Avail QTY</TableHead>
              <TableHead>My Price</TableHead>
              <TableHead>Active</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedItems.length > 0 ? (
              sortedItems.map((item) => (
                <TableRow key={item.stockId}>
                  <TableCell>
                    <Link 
                     to={`/home/<USER>/${item.stockId}/transactions?sellerId=${sellerId}`} 
                      className="font-medium text-primary hover:underline"
                    >
                      {item.itemName}
                    </Link>
                  </TableCell>
                  <TableCell>{item.distributor}</TableCell>
                  <TableCell>
                    {item.maxAvailableQty > 0 ? (
                     <div className="flex items-center gap-2">
                     <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                       {item.maxAvailableQty}
                     </span>
                     <Button 
                       variant="ghost" 
                       size="sm" 
                       onClick={() => {
                         const input = prompt("Enter new Qty:", item.maxAvailableQty.toString());
                   
                         // ✅ Don't continue if user pressed Cancel
                         if (input === null) return;
                   
                         const newQty = parseFloat(input);
                   
                         // ✅ Don't submit if input is not a valid number
                         if (isNaN(newQty)) return;
                   
                         const formData = new FormData();
                         formData.append("stockId", item.stockId.toString());
                         formData.append("maxAvailableQty", newQty.toString());
                         if (sellerId !== undefined) {
                           formData.append("sellerId", sellerId.toString());
                         }
                         fetcher.submit(
                           formData,
                           { method: 'POST' }
                         );
                       }}
                       className="h-7 px-2 text-xs"
                     >
                       Edit
                     </Button>
                   </div>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span>₹ {item.pricePerUnit.toFixed(2)}</span>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => {
                          const input = prompt("Enter new price:", item.pricePerUnit.toString());
                          const formData = new FormData();

                         

                          // Cancelled prompt
                          if (input === null) return;
                        
                          const newPrice = parseFloat(input);
                          if (sellerId !== undefined) {
                            formData.append("sellerId", sellerId.toString());
                          }
                          formData.append("stockId", item.stockId.toString());
                          formData.append("pricePerUnit", newPrice.toString());

                          // Invalid input (e.g. empty or not a number)
                          if (isNaN(newPrice)) return;   

                          fetcher.submit(
                            formData,
                            { method: 'POST' }
                          );
                    
                        }}
                        className="h-7 px-2 text-xs"
                      >
                        Edit
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={item.active}
                      onCheckedChange={(checked) => {
                        const formData = new FormData();
                         if (sellerId !== undefined) {
                            formData.append("sellerId", sellerId.toString());
                          }
                          formData.append("stockId", item.stockId.toString());
                          formData.append("active", checked.toString());
                        
                          fetcher.submit(
                            formData,
                            { method: 'POST' }
                          );                      
                      
                      }
                      }
                    />
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                  No items found. Try adjusting your search criteria.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </Layout>
  );
}