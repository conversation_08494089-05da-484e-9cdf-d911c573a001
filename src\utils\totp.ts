import speakeasy from 'speakeasy';

/**
 * Generates a TOTP token using a secret.
 * If a secret is provided via the `secret` parameter or via the TOTP_SECRET env variable, that value is used.
 * Otherwise, a new secret is generated and logged.
 *
 * @param customTime Optional Unix timestamp in milliseconds to generate the token for.
 * @param customSecret Optional secret to override the environment secret.
 * @param step Optional time step in seconds (default is 30 seconds)
 * @returns An object containing the secret used (base32 encoded) and the generated TOTP token.
 */
export function generateTotpToken({ 
  customSecret,
  step = 900  // 15 minutes in seconds
}: { 
  customSecret?: string,
  step?: number 
}): { secret: string; token: string } {
  // Use provided secret, or the environment variable, or generate a new one.
  const secret = customSecret || process.env.TOTP_SECRET || (() => {
    const generated = speakeasy.generateSecret({ length: 20 });
    console.log('Generated new TOTP secret:', generated.base32);
    return generated.base32;
  })();

  // Generate the TOTP token. If customTime is provided, convert from ms to seconds.
  const token = speakeasy.totp({
    secret,
    encoding: 'base32',
    algorithm: 'sha1',
    step, 
  });

  return { secret, token };
}

export function verifyTotpToken(
  token: string, 
  secret: string, 
  step: number = 900  // 15 minutes in seconds
): boolean {
  return speakeasy.totp.verify({
    token,
    secret,
    encoding: 'base32',
    algorithm: 'sha1',
    step, 
  });
}
