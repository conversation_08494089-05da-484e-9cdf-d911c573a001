// services/tripService.ts
import { API_BASE_URL } from "@utils/api";
import { ApiResponse } from "~/types/api/Api";
import {
  BcSalesDashboardDto,
  SearchItems,
} from "~/types/api/businessConsoleService/SalesAnalysis";
import { TripSummary } from "~/types/api/businessConsoleService/tripSummaryDetails";
import { apiRequest } from "~/utils/api";

export async function getTripSummary(
  fromDate: string,
  toDate:string,
  request?: Request
): Promise<ApiResponse<TripSummary[]>> {
  const response = await apiRequest<TripSummary[]>(
    `${API_BASE_URL}/bc/mnetmanager/trips/date/${fromDate}/${toDate}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch trip summary");
  }
}
export async function getSalesAnalysis(
  date: string,
  dataType: string,
  noOfDays: number,
  selectedSellerId?: number | null,
  selectedAgentId?: number | null,
  selectedBuyerId?: number | null,
  selectedLocalityId?: number | null,
  matchBy?: string,
  pageNo?: number | null,
  pageSize?: number | null,
  dDate?: string | null,
  request?: Request
): Promise<ApiResponse<BcSalesDashboardDto[]>> {
  // Construct base URL
  let url = `${API_BASE_URL}/sales/dashboard?date=${date}&days=${noOfDays}`;

  if (dataType !== "all") {
    url += `&dataType=${dataType}`;
  }

  // Append optional parameters only if they are not null or undefined
  if (selectedSellerId != null) url += `&sellerId=${selectedSellerId}`;
  if (selectedAgentId != null) url += `&agentId=${selectedAgentId}`;
  if (selectedBuyerId != null) url += `&buyerId=${selectedBuyerId}`;
  if (selectedLocalityId != null) url += `&areaId=${selectedLocalityId}`;
  if (matchBy) url += `&matchBy=${matchBy}`;
  if (pageNo != null) url += `&pageNo=${pageNo}`;
  if (pageSize != null) url += `&pageSize=${pageSize}`;
  if (dDate !== "") url += `&deliveryDate=${dDate}`;

  try {
    const response = await apiRequest<BcSalesDashboardDto[]>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );
    return response;
  } catch (error) {
    throw new Error("Failed to fetch getSalesAnalysis: " + error);
  }
}

export async function getSearchFilters(
  type: string,
  matchBy?: string,
  pageNo?: number,
  pageSize?: number,
  request?: Request
): Promise<ApiResponse<SearchItems[]>> {
  // Debug API_BASE_URL
  console.log("API_BASE_URL:", API_BASE_URL);

  if (!API_BASE_URL) {
    throw new Error("API_BASE_URL is not defined.");
  }

  // Build query parameters dynamically

  console.log("Received Params:");
  console.log("type:", type);
  console.log("matchBy:", matchBy);
  console.log("pageNo:", pageNo);
  console.log("pageSize:", pageSize);

  let url = `${API_BASE_URL}/sales/search?type=${type.toString()}`;

  if (matchBy) url += `&matchBy=${matchBy}`;
  if (pageNo !== undefined) url += `&pageNo=${pageNo}`;
  if (pageSize) url += `&size=${pageSize}`;

  // Debug queryParams

  console.log("Constructed URL:", url);

  const response = await apiRequest<SearchItems[]>(
    url,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    console.log("API Response:", response);
    return response;
  } else {
    throw new Error("Failed to fetch getSearchFilters");
  }
}
