import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogHeader } from "./alert-dialog";
import { Button } from "./button";





export default function SucessPopUp({ isOpen, message, onOpen, onClose, title }: { isOpen: boolean, message: string; onOpen: () => void; onClose: () => void, title: string }) {


      return (
            <AlertDialog open={isOpen} onOpenChange={onOpen}>
                  <AlertDialogContent>
                        <AlertDialogHeader>
                              <h3 className="text-lg font-semibold">{title}</h3>
                        </AlertDialogHeader>
                        <AlertDialogDescription>
                              {message}
                        </AlertDialogDescription>
                        <div className="flex justify-end mt-4">
                              <Button onClick={onClose}>Close</Button>
                        </div>
                  </AlertDialogContent>
            </AlertDialog>
      )





}


