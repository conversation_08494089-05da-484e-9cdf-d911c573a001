export function parseJWT(token: string) {
    const payload = token.split('.')[1];
    const decodedPayload = base64UrlDecode(payload);
    return JSON.parse(decodedPayload);
}

function base64UrlDecode(input:string) {
    input = input
        .replace(/-/g, '+')
        .replace(/_/g, '/');

    const pad = input.length % 4;
    if (pad) {
        input += new Array(5 - pad).join('=');
    }

    return atob(input);
}
