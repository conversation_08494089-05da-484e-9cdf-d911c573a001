import { getFirebaseAdmin } from '@/utils/firebase.utils.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function simpleFirebaseBackup() {
    try {
        console.log('🚀 Starting Firebase collection backup...');
        
        const db = getFirebaseAdmin();
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupCollectionName = `facebook-connects-backup-${timestamp}`;
        
        console.log(`📋 Copying facebook-connects collection to ${backupCollectionName}...`);
        
        // Get all documents from facebook-connects collection
        const querySnapshot = await db.collection('facebook-connects').get();
        console.log(`✅ Found ${querySnapshot.size} documents to copy`);

        if (querySnapshot.size === 0) {
            console.log('⚠️ No documents found to backup');
            return;
        }

        // Copy each document to the backup collection
        let copiedCount = 0;
        for (const doc of querySnapshot.docs) {
            try {
                const data = doc.data();
                await db.collection(backupCollectionName).doc(doc.id).set(data);
                copiedCount++;
                
                if (copiedCount % 10 === 0) {
                    console.log(`📊 Copied ${copiedCount}/${querySnapshot.size} documents...`);
                }
            } catch (error) {
                console.error(`❌ Error copying document ${doc.id}:`, error);
            }
        }

        // Save metadata to JSON file
        const outputDir = path.join(__dirname, 'output');
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const metadata = {
            timestamp: new Date().toISOString(),
            sourceCollection: 'facebook-connects',
            backupCollection: backupCollectionName,
            totalDocuments: querySnapshot.size,
            copiedDocuments: copiedCount,
            backupDate: new Date().toISOString()
        };

        const jsonFileName = `backup_metadata_${timestamp}.json`;
        const jsonFilePath = path.join(outputDir, jsonFileName);
        fs.writeFileSync(jsonFilePath, JSON.stringify(metadata, null, 2), 'utf8');

        console.log('🎉 Backup completed successfully!');
        console.log(`📊 Summary: ${copiedCount}/${querySnapshot.size} documents copied`);
        console.log(`🔥 Firebase backup collection: ${backupCollectionName}`);
        console.log(`📁 Metadata file: ${jsonFilePath}`);

    } catch (error) {
        console.error('❌ Backup failed:', error);
        process.exit(1);
    }
}

// Run the backup
simpleFirebaseBackup();
