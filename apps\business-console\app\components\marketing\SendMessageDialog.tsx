import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@components/ui/dialog";
import { Button } from "@components/ui/button";
import { Input } from "@components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";
import { Label } from "@components/ui/label";
import { SearchableSelect } from "~/components/common/SearchableSelect";
import type { Template, CustomerGroup, SearchParams } from "~/schemas/marketing";
import type { SellerItem } from "~/types/api/businessConsoleService/MyItemList";
import type { BuyerSummaryDetailsResponseItem } from "~/types/api/businessConsoleService/BuyerSummaryDetailsResponseItem";
import { useFetcher } from "@remix-run/react";
import { LoaderData } from "~/routes/seller.marketing.templates";

interface SendMessageDialogProps {
  template: Template;
  customerGroups: CustomerGroup[];
  showItems: boolean;
  showCustomers: boolean;
  showVariables: boolean;
  onClose: () => void;
  onSend: (data: {
    id:number;
    templateId: string;
    variables: Record<string, string>;
    selectedGroup: string;
    selectedItems?: number[];
    selectedCustomers?: number[];
  }) => void;
}

interface PaginationParams {
  search: string;
  page: number;
  pageSize: number;
}

export function SendMessageDialog({
  template,
  customerGroups,
  showItems = false,
  showCustomers = true,
  showVariables = false,
  onClose,
  onSend,
}: SendMessageDialogProps) {
  const itemsFetcher = useFetcher<LoaderData>();
  const customersFetcher = useFetcher<LoaderData>();

  const [step, setStep] = useState<"variables" | "targeting" | "confirm">("targeting");
  const [variables, setVariables] = useState<Record<string, string>>(() =>
    Object.fromEntries(template.variables.map(v => [v, ""]))
  );
  const [selectedGroup, setSelectedGroup] = useState<string>(customerGroups[0]?.type || "All");
  const [selectedItems, setSelectedItems] = useState<SellerItem[]>([]);
  const [selectedCustomers, setSelectedCustomers] = useState<BuyerSummaryDetailsResponseItem[]>([]);
  const [items, setItems] = useState<SellerItem[]>([]);
  const [customers, setCustomers] = useState<BuyerSummaryDetailsResponseItem[]>([]);
  
  const [hasMoreItems, setHasMoreItems] = useState(true);
  const [hasMoreCustomers, setHasMoreCustomers] = useState(true);

  // Pagination State
  const [itemPagination, setItemPagination] = useState<PaginationParams>({
    search: "",
    page: 1,
    pageSize: 10,
  });
  const [customerPagination, setCustomerPagination] = useState<PaginationParams>({
    search: "",
    page: 1,
    pageSize: 10,
  });

  // Handle items data updates
  useEffect(() => {
    if (itemsFetcher.data?.data) {
      const newItems = itemsFetcher.data.data as SellerItem[];
      if (itemPagination.page > 1) {
        // Load more case
        setItems(prev => {
          const existingIds = new Set(prev.map(item => item.Id));
          const filteredNewItems = newItems.filter(item => !existingIds.has(item.Id));
          return [...prev, ...filteredNewItems];
        });
      } else {
        // Initial load or search case
        setItems(newItems);
      }
      setHasMoreItems(newItems.length === itemPagination.pageSize);
    }
  }, [itemsFetcher.data, itemPagination.page]);

  // Handle customers data updates
  useEffect(() => {
    if (customersFetcher.data?.data) {
      const newCustomers = customersFetcher.data.data as BuyerSummaryDetailsResponseItem[];
      if (customerPagination.page > 1) {
        // Load more case
        setCustomers(prev => {
          const existingIds = new Set(prev.map(customer => customer.buyerId));
          const filteredNewCustomers = newCustomers.filter(customer => !existingIds.has(customer.buyerId));
          return [...prev, ...filteredNewCustomers];
        });
      } else {
        // Initial load or search case
        setCustomers(newCustomers);
      }
      setHasMoreCustomers(newCustomers.length === customerPagination.pageSize);
    }
  }, [customersFetcher.data, customerPagination.page]);

  const onSearchItems = useCallback((params: SearchParams) => {
    const searchParams = new URLSearchParams();
    searchParams.set("searchType", "items");
    if (params.search) searchParams.set("search", params.search);
    if (params.page) searchParams.set("page", String(params.page));
    if (params.pageSize) searchParams.set("pageSize", String(params.pageSize));
    
    itemsFetcher.load(`?${searchParams.toString()}`);
  }, [itemsFetcher]);

  const onSearchCustomers = useCallback((params: SearchParams) => {
    const searchParams = new URLSearchParams();
    searchParams.set("searchType", "customers");
    if (params.search) searchParams.set("search", params.search);
    if (params.page) searchParams.set("page", String(params.page));
    if (params.pageSize) searchParams.set("pageSize", String(params.pageSize));
    
    customersFetcher.load(`?${searchParams.toString()}`);
  }, [customersFetcher]);

  // Handle load more for items
  const handleLoadMoreItems = useCallback(() => {
    const nextPage = itemPagination.page + 1;
    setItemPagination(prev => ({ ...prev, page: nextPage }));
    onSearchItems({
      ...itemPagination,
      page: nextPage,
    });
  }, [itemPagination, onSearchItems]);

  // Handle load more for customers
  const handleLoadMoreCustomers = useCallback(() => {
    const nextPage = customerPagination.page + 1;
    setCustomerPagination(prev => ({ ...prev, page: nextPage }));
    onSearchCustomers({
      ...customerPagination,
      page: nextPage,
    });
  }, [customerPagination, onSearchCustomers]);

  // Handle item search
  const handleItemSearch = useCallback(async (search: string) => {
    const newPagination = {
      search,
      page: 1,
      pageSize: 10,
    };
    setItemPagination(newPagination);
    onSearchItems(newPagination);
  }, [onSearchItems]);

  // Handle customer search
  const handleCustomerSearch = useCallback(async (search: string) => {
    const newPagination = {
      search,
      page: 1,
      pageSize: 10,
    };
    setCustomerPagination(newPagination);
    onSearchCustomers(newPagination);
  }, [onSearchCustomers]);

  const handleVariableChange = (name: string, value: string) => {
    setVariables(prev => ({ ...prev, [name]: value }));
  };

  const handleNext = () => {
    if (step === "variables") {
      setStep("targeting");
    } else if (step === "targeting") {
      setStep("confirm");
    }
  };

  const handleBack = () => {
    if (step === "targeting") {
      setStep("variables");
    } else if (step === "confirm") {
      setStep("targeting");
    }
  };

  const handleSend = () => {
    onSend({
      id: template.id,
      templateId: template.templateId,
      variables,
      selectedGroup,
      selectedItems: selectedItems.map(item => item.Id),
      selectedCustomers: selectedCustomers.map(customer => customer.buyerId),
    });
    onClose();
  };

  const toggleItem = useCallback((item: SellerItem) => {
    setSelectedItems(prev => {
      const exists = prev.some(i => i.Id === item.Id);
      return exists
        ? prev.filter(i => i.Id !== item.Id)
        : [...prev, item];
    });
  }, []);

  const toggleCustomer = useCallback((customer: BuyerSummaryDetailsResponseItem) => {
    setSelectedCustomers(prev => {
      const exists = prev.some(c => c.buyerId === customer.buyerId);
      return exists
        ? prev.filter(c => c.buyerId !== customer.buyerId)
        : [...prev, customer];
    });
  }, []);

  const isNextDisabled = () => {
    if (step === "variables") {
      return template.variables.some((v) => !variables[v]);
    }
    if (step === "targeting") {
      if (selectedGroup === "Custom") {
        return selectedCustomers.length === 0;
      }
      return !selectedGroup;
    }
    return false;
  };

  const renderTargetingStep = () => (
    <div className="space-y-6">
      {showItems && (
        <div className="space-y-2">
          <Label>Select Items</Label>
          <SearchableSelect<SellerItem>
            items={items}
            selectedItems={selectedItems}
            onSearch={handleItemSearch}
            onToggleItem={toggleItem}
            onLoadMore={handleLoadMoreItems}
            hasMore={hasMoreItems}
            isLoading={itemsFetcher.state === "loading"}
            getItemId={(item) => item.Id}
            getItemLabel={(item) => item.name}
            placeholder="Select items..."
            searchPlaceholder="Search items..."
            emptyMessage="No items found."
            loadMoreText="Load More Items"
          />
        </div>
      )}

      {showCustomers && (
        <div className="space-y-2">
          <Label>Select Customer Group</Label>
          <Select value={selectedGroup} onValueChange={setSelectedGroup}>
            <SelectTrigger>
              <SelectValue placeholder="Select a group" />
            </SelectTrigger>
            <SelectContent>
              {customerGroups.map((group) => (
                <SelectItem key={group.type} value={group.type}>
                  {group.name} ({group.customerCount} customers)
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {selectedGroup === "Custom" && (
        <div className="space-y-2">
          <Label>Select Customers</Label>
          <SearchableSelect<BuyerSummaryDetailsResponseItem>
            items={customers}
            selectedItems={selectedCustomers}
            onSearch={handleCustomerSearch}
            onToggleItem={toggleCustomer}
            onLoadMore={handleLoadMoreCustomers}
            hasMore={hasMoreCustomers}
            isLoading={customersFetcher.state === "loading"}
            getItemId={(customer) => customer.buyerId}
            getItemLabel={(customer) => customer.buyerName}
            placeholder="Search customers..."
            searchPlaceholder="Search customers..."
            emptyMessage="No customers found."
            loadMoreText="Load More Customers"
          />
        </div>
      )}
    </div>
  );

  const renderStep = () => {
    switch (step) {
      case "variables":
        return (
          <div className="space-y-4">
            {template.variables.map((variable) => (
              <div key={variable} className="space-y-2">
                <Label>{variable}</Label>
                <Input
                  value={variables[variable] || ""}
                  onChange={(e) => handleVariableChange(variable, e.target.value)}
                  placeholder={`Enter ${variable}`}
                />
              </div>
            ))}
          </div>
        );
      case "targeting":
        return renderTargetingStep();
      case "confirm":
      default:
        return (
          <div className="space-y-4">
            {showVariables && Object.keys(variables).length > 0 && (
              <div className="space-y-2">
                <h3 className="font-medium">Variables</h3>
                {Object.entries(variables).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-muted-foreground">{key}:</span>
                    <span>{value}</span>
                  </div>
                ))}
              </div>
            )}
            <div className="space-y-2">
              <h3 className="font-medium">Target Audience</h3>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Group:</span>
                <span>
                  {selectedGroup === "Custom"
                    ? `Custom (${selectedCustomers.length} customers)`
                    : customerGroups.find((g) => g.type === selectedGroup)?.name || ""}
                </span>
              </div>
              {selectedItems.length > 0 && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Items:</span>
                  <span>{selectedItems.length} selected</span>
                </div>
              )}
            </div>
          </div>
        );
    }
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-2xl rounded-md">
        <DialogHeader>
          <DialogTitle>
            {step === "variables"
              ? "Fill in Variables"
              : step === "targeting"
              ? "Select Target Audience"
              : "Confirm Message"}
          </DialogTitle>
        </DialogHeader>

        {renderStep()}

        <DialogFooter>
          {step !== "targeting"  && (
            <Button variant="outline" onClick={handleBack}>
              Back
            </Button>
          )}
          {step === "confirm" ? (
            <Button onClick={handleSend}>Send Message</Button>
          ) : (
            <Button onClick={handleNext} disabled={isNextDisabled()}>
              Next
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
