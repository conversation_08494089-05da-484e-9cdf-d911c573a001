import { useEffect, useState } from "react";
import { Form, useActionData, useFetcher } from "@remix-run/react";
import { Input } from "~/components/ui/input";
import { Button } from "./button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "./dialog";
import { Label } from "./label";
import { useToast } from "./ToastProvider";
import SpinnerLoader from "../loader/SpinnerLoader";
import { RadioGroup, RadioGroupItem } from "./radio-group";
interface SellerData {
      id?: number;
      name: string;
      address: string;
      email: string;
      customerSupportNumber: string;
      owner: {
            firstName: string;
            lastName: string;
            email: string;
            mobileNumber: string;
            address: string;
      };
      latitude: string;
      longitude: string;
      ondcDomain: "RET10" | "RET11";
      pincode: string;
}
const initialFormState: SellerData = {
      name: "",
      address: "",
      email: "",
      customerSupportNumber: "",
      owner: {
            firstName: "",
            lastName: "",
            email: "",
            mobileNumber: "",
            address: "",
      },
      latitude: "",
      longitude: "",
      ondcDomain: "RET10",
      pincode: "",
};
interface createSellerProps {
      isOpen: boolean;
      onClose: () => void;
      seller?: SellerData

}
export default function CreateSeller({ isOpen, onClose, seller }: createSellerProps) {
      const [formData, setFormData] = useState<SellerData>({
            id: seller?.id || undefined,
            name: seller?.name || "",
            address: seller?.address || "",
            email: seller?.email || "",
            customerSupportNumber: seller?.customerSupportNumber || "",
            owner: {
                  firstName: seller?.owner.firstName || "",
                  lastName: seller?.owner.lastName || "",
                  email: seller?.owner.email || "",
                  mobileNumber: seller?.owner.mobileNumber || "",
                  address: seller?.owner.address || "",
            },
            latitude: seller?.latitude || "",
            longitude: seller?.longitude || "",
            ondcDomain: seller?.ondcDomain || "RET10",
            pincode: seller?.pincode || "",
      });

      const fetcher = useFetcher()
      const [errors, setErrors] = useState<Record<string, string>>({});
      const isEditMode = !!seller?.id;
      const isLoading = fetcher.state !== "idle";
      const { showToast } = useToast()
      useEffect(() => {
            if (fetcher.state === "idle" && fetcher.data) {
                  if (fetcher?.data?.success === true) {
                        showToast("Seller created successfully", "success");
                        setFormData(initialFormState);
                        setErrors({});
                        onClose();
                  } else if (fetcher?.data?.success === false && fetcher?.data?.error) {
                        showToast(fetcher.data?.error, "error");
                  } else {
                        showToast("Unexpected response from server", "error");
                  }
            }
      }, [fetcher.state, fetcher.data, onClose]);
      useEffect(() => {
            if (!isOpen) {
                  setFormData(initialFormState);
            }
      }, [isOpen])

      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            const { name, value } = e.target;
            if (name.startsWith("owner.")) {
                  const field = name.split(".")[1];
                  setFormData((prev) => ({
                        ...prev,
                        owner: { ...prev.owner, [field]: value },
                  }));
            }
            else {
                  setFormData((prev) => ({ ...prev, [name]: value }));
            }
      };
      const validateForm = () => {
            let newErrors: Record<string, string> = {};

            if (!formData.name) newErrors.name = "Name is required";
            if (!formData.address) {
                  newErrors.address = "Address is required";
            } else if (!(/^[A-Za-z0-9\s]{25,}$/).test(formData.address)) {
                  newErrors.address = "Address should be atleast 25 characters and contains only 0-9 and A-Z";
            }
            if (!formData.email) newErrors.email = "Email is required";
            if (!formData.customerSupportNumber) newErrors.customerSupportNumber = "Customer Support Number is required";
            if (!formData.owner.firstName) newErrors["owner.firstName"] = "First Name is required";
            if (!formData.owner.lastName) newErrors["owner.lastName"] = "Last Name is required";
            if (!formData.owner.email) newErrors["owner.email"] = "Owner Email is required";
            if (!formData.owner.mobileNumber) {
                  newErrors["owner.mobileNumber"] = "Mobile Number is required";
            } else if (!/^\d{10}$/.test(formData.owner.mobileNumber)) {
                  newErrors["owner.mobileNumber"] = "Mobile Number must be 10 digits";
            }
            if (formData.ondcDomain === "RET11") {
                  if (!formData.pincode) {
                        newErrors.pincode = "Pincode is required"
                  } else if (formData.pincode.length !== 6) {
                        newErrors.pincode = "Pincode must be 6 digits";
                  }
            }
            if (!formData.latitude) newErrors.latitude = "Latitude is required";
            if (!formData.longitude) newErrors.longitude = "Longitude is required";

            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
      };
      const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();
            if (!validateForm()) return;

            const data = new FormData();
            Object.entries(formData).forEach(([key, value]) => {
                  if (typeof value === "object") {
                        Object.entries(value).forEach(([subKey, subValue]) => {
                              data.append(`owner.${subKey}`, String(subValue));
                        });
                  } else {
                        data.append(key, String(value));
                  }
            });
            data.append("roles", "SellerOwner");
            data.append("actionType", "createNewSeller");
            fetcher.submit(data, { method: isEditMode ? "put" : "post" });
      };
      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="max-h-[90vh] max-w-screen-md flex flex-col p-0">
                        {isLoading && (
                              <div className="absolute inset-0 flex items-center justify-center bg-white/10 backdrop-blur-sm z-50">
                                    <SpinnerLoader loading={isLoading} />
                              </div>
                        )}
                        <DialogHeader className="sticky top-0 bg-white z-10 px-6 py-4 border-b">
                              <DialogTitle className="font-bold text-lg">Create Seller</DialogTitle>
                        </DialogHeader>
                        <Form onSubmit={handleSubmit} className="flex-1 overflow-y-auto space-y-4 p-6">

                              <h3 className="font-semibold">Business Type</h3>
                              <RadioGroup
                                    value={formData.ondcDomain}
                                    onValueChange={(val: "RET10" | "RET11") => setFormData((prev) => ({ ...prev, ondcDomain: val }))}
                                    className="flex flex-row gap-5"
                              >
                                    <div className="flex items-center space-x-2">
                                          <RadioGroupItem id="type-RET10" value="RET10" />
                                          <Label htmlFor="type-RET10">Non-Restaurant</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                          <RadioGroupItem id="type-RET11" value="RET11" />
                                          <Label htmlFor="type-RET11">Restaurant</Label>
                                    </div>
                              </RadioGroup>

                              <h3 className="font-semibold">Business Details</h3>
                              <div className="space-y-3 ">
                                    <div className="flex  flex-col md:flex-row  gap-3">
                                          <Label className=" flex-1 flex flex-col gap-y-2">
                                                Name
                                                <Input type="text" name="name" placeholder="Name" value={formData.name} onChange={handleChange} className={errors.name ? "border-red-500" : ""} />
                                                {errors.name && <span className="text-red-500 text-sm">{errors.name}</span>}
                                          </Label>
                                          <Label className=" flex-1 flex flex-col gap-y-2">
                                                Address
                                                <Input type="text" name="address" placeholder="Address" value={formData.address} onChange={handleChange} required className={errors.address ? "border-red-500" : ""} />
                                                {errors.address && <span className="text-red-500 text-sm">{errors.address}</span>}

                                          </Label>
                                    </div>

                                    <div className="flex flex-col md:flex-row gap-3">
                                          <Label className="flex-1 flex flex-col gap-y-2">
                                                Email
                                                <Input type="email" name="email" placeholder="Email" onChange={handleChange} required className={errors.email ? "border-red-500" : ""} />
                                                {errors.email && <span className="text-red-500 text-sm">{errors.email}</span>}
                                          </Label>
                                          <Label className=" flex-1  flex flex-col gap-y-2">
                                                Customer Support Number
                                                <Input
                                                      type="number"
                                                      name="customerSupportNumber"
                                                      value={formData.customerSupportNumber}
                                                      onChange={(e) => {
                                                            const value = e.target.value.replace(/\D/g, ""); // Only allow numbers
                                                            if (value.length <= 10) {
                                                                  setFormData((prev) => ({ ...prev, customerSupportNumber: value }));
                                                            }
                                                      }}
                                                      className={errors.customerSupportNumber ? "border-red-500" : ""}
                                                      placeholder="Enter 10-digit number"
                                                />
                                                {errors.customerSupportNumber && (
                                                      <span className="text-red-500 text-sm">{errors.customerSupportNumber}</span>
                                                )}
                                          </Label>
                                    </div>
                              </div>
                              <h3 className="font-semibold">Owner Details</h3>
                              <div className="space-y-3">
                                    <div className="flex flex-col md:flex-row gap-3">
                                          <Label className=" flex-1 flex flex-col gap-y-2">
                                                First Name
                                                <Input type="text" name="owner.firstName" placeholder="First Name" onChange={handleChange} required />
                                          </Label>
                                          <Label className="flex-1  flex flex-col gap-y-2">
                                                Last Name
                                                <Input type="text" name="owner.lastName" placeholder="Last Name" onChange={handleChange} required />
                                          </Label>
                                    </div>

                                    <div className=" flex flex-col md:flex-row gap-3">
                                          <Label className=" flex-1 flex flex-col gap-y-2">
                                                Owner Email
                                                <Input type="email" name="owner.email" placeholder="Owner Email" onChange={handleChange} required />
                                          </Label>
                                          <Label className=" flex-1  flex flex-col gap-y-2">
                                                Owner Mobile Number
                                                <Input type="number" name="owner.mobileNumber" value={formData.owner.mobileNumber}

                                                      onChange={(e) => {
                                                            const value = e.target.value.replace(/\D/g, ""); // Only allow numbers
                                                            if (value.length <= 10) {
                                                                  setFormData((prev) => ({
                                                                        ...prev,
                                                                        owner: {
                                                                              ...prev.owner,
                                                                              mobileNumber: value
                                                                        },
                                                                  }));
                                                            }
                                                      }}

                                                      placeholder="Owner MobileNumber"
                                                      className={errors["owner.mobileNumber"] ? "border-red-500" : ""} />
                                                {errors["owner.mobileNumber"] && <span className="text-red-500 text-sm">{errors["owner.mobileNumber"]}</span>}
                                          </Label>
                                    </div>
                                    <Label>
                                          Owner Address
                                          <Input type="text" name="owner.address" placeholder="Owner Address" onChange={handleChange} required />
                                    </Label>
                              </div>

                              <h3 className="font-semibold">Location</h3>
                              {formData.ondcDomain === "RET11" && <Label className="flex-1 flex flex-col  gap-y-3 w-fit">
                                    Pincode
                                    <Input type="text" name="pincode" placeholder="Pincode" onChange={handleChange} required />
                                    {errors.pincode && <span className="text-red-500 text-sm">{errors.pincode}</span>}
                              </Label>}
                              <div className=" flex flex-col md:flex-row gap-3">

                                    <Label className="flex-1 flex flex-col  gap-y-3 tex-">
                                          Latitude
                                          <Input type="text" name="latitude" placeholder="Latitude" onChange={handleChange} required />
                                    </Label>
                                    <Label className="flex-1 flex  flex-col gap-y-3">
                                          Longitude
                                          <Input type="text" name="longitude" placeholder="Longitude" onChange={handleChange} required />
                                    </Label>
                              </div>
                              <div className="flex justify-end">
                                    <Button type="submit" className="rounded-full hover:bg-blue-400  bg-blue-300" loading={isLoading} disabled={isLoading}>{isEditMode ? "Update Seller" : "Create Seller"}</Button>
                              </div>
                        </Form>
                  </DialogContent>
            </Dialog>
      );
}
