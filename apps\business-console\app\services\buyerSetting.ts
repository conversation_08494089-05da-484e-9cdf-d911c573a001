import { ApiResponse } from '~/types/api/Api';
import { BuyerNetworkBanners, CustomerAcquisition, CustomerConversionRate, SellerSales } from './../types/api/businessConsoleService/BuyerAccountingResponse';
import { API_BASE_URL, apiRequest } from '~/utils/api';

export  interface bannerSeqPayload{
  id:number,
  sequenceId:number,
  networkId:number
}
export async function getBuyerNBanners(
  request?: Request
): Promise<ApiResponse<BuyerNetworkBanners[]>> {
  const response = await apiRequest<BuyerNetworkBanners[]>(

    `${API_BASE_URL}/bc/seller/banners`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to fetch Buyer Banners");
  }
}
export async function updateBuyerNBanners(
  buyerId:number,
  request?: Request
): Promise<ApiResponse<BuyerNetworkBanners>> {
  try{
  const response = await apiRequest<BuyerNetworkBanners>(
    `${API_BASE_URL}/bc/seller/banner/${buyerId}`,
    "PUT",
    undefined,
    {},
    true,
   request
  );
  if (response.statusCode !== 200) {
    throw new Error("No response received from API");
  }

  return response;
  }
  catch (error) {
    console.error("Error updating banner status", error);
    throw error;
  }
}
export async function updateBuyerNSequence(
  payload:bannerSeqPayload,
  request?: Request
): Promise<ApiResponse<BuyerNetworkBanners>> {
  try{
    console.log(payload,"oooooooooooooooo")
  const response = await apiRequest<BuyerNetworkBanners>(
    `${API_BASE_URL}/bc/seller/banner`,
    "PUT",
    [{id:payload.id,
      networkId:payload.networkId,
      sequenceId:payload.sequenceId,
    }],
    {},
    true,
   request
  );
  if (response.statusCode !== 200) {
    throw new Error("No response received from API");
  }
  return response;
  }
  catch (error) {
    console.error("Error updating banner status", error);
    throw error;
  }
}

//dash board for customerAnalysis api func
export async function getSellerSales(
  totalWeeks:number,
  request?: Request
): Promise<ApiResponse<SellerSales[]>> {
  const response = await apiRequest<SellerSales[]>(

    `${API_BASE_URL}/inventory/dashboard/sales?totalWeeks=${totalWeeks}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to fetch SellerSales ");
  }
}
//get the conversion Rates for CustomerAnalysis
export async function getConversionRate(
  totalWeeks:number,
  request?: Request
): Promise<ApiResponse<CustomerConversionRate[]>> {
  const response = await apiRequest<CustomerConversionRate[]>(
    `${API_BASE_URL}/inventory/dashboard/conversion?totalWeeks=${totalWeeks}`,
    "GET",
    undefined,
    {},
    true,
    request
  );
  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to fetch conversionRates ");
  }
}
export async function getAcquiSitionRates(
  totalWeeks:number,
  request?: Request
): Promise<ApiResponse<CustomerAcquisition[]>> {
  const response = await apiRequest<CustomerAcquisition[]>(
    `${API_BASE_URL}/inventory/dashboard/cacquisition?totalWeeks=${totalWeeks}`,
    "GET",
    undefined,
    {},
    true,
    request
  );
  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to fetch conversionRates ");
  }
}