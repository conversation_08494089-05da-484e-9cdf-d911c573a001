import { NetworkConfig } from "./Network";
export interface Networks {
  id: number;
  name: string;
  description: string;
  isPrivate: boolean;
  managerBusinessId: number;
  managerName: string;
  managerId: number;
  networkType: string;
}

export interface NetworkAreas {
  areaId?: number;
  agentUserId?: number;
  networkAreaId: number;
  networkAreaName: string;
  encodedPolygon: string;
  agentName: string | null;
  networkName: string;
  disabled: boolean;
}

export interface NetworkConfig {
  id: number;
  domain: string;
  businessLogo: string;
  homePageBanner: string;
  pwaAppIcon: string;
  footerAppIcon: string;
  networkId: number;
  multiSeller: Boolean;
  defaultSellerId: number;
  wabEnabled: Boolean;
  wabMobileNumber: string;
  defaultStartPage: string;
  imageBaseUrl: string;
  networkType?: string;
  wabDatasetId: string;

}

export interface NetworkBanner {
  id: number;
  networkId: number;
  bannerUrl: string;
  sequenceId: number;
  target: string;
  active?: boolean
}

export interface NetworkBuyer {
  networkBuyerId: number;
  agentName: string;
  buyerName: string;
  overrideAgentName: string;
  mobileNumber: string;
}