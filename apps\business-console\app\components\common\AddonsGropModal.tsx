import { MyAddonGroupData } from "~/types/api/businessConsoleService/SellerManagement";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import React, { useState, useEffect } from "react";
import { useFetcher } from "@remix-run/react";
import { useToast } from "../ui/ToastProvider";

interface AddonsGroupProps {
      isOpen: boolean;
      data?: MyAddonGroupData | undefined;
      onClose: () => void;
      header: string;
      sellerId: number
}

const AddonsGroupModal: React.FC<AddonsGroupProps> = ({ isOpen, data, onClose, header, sellerId }) => {
      const [formData, setFormData] = useState<MyAddonGroupData>(data ||
      {

            internalName: "",
            displayName: "",
            description: "",
            active: false,

      }
      );
      const addonsGroupFetcher = useFetcher()

      const loading = addonsGroupFetcher.state != "idle";

      const { showToast } = useToast();

      useEffect(() => {
            if (addonsGroupFetcher.data) {

                  if (addonsGroupFetcher.data?.sucess) {

                        showToast("sucessFully added", "success")
                        onClose();
                        setFormData({
                              internalName: "",
                              displayName: "",
                              description: "",
                              active: false,
                        })
                  }

                  else if (!addonsGroupFetcher.data?.sucess) {
                        showToast("Failed to add. Please try again.", "error");
                  }
            }

      }, [addonsGroupFetcher.data])

      useEffect(() => {
            if (data && isOpen) {
                  setFormData(data)
            }
            else if (!isOpen) {
                  setFormData({
                        internalName: "",
                        displayName: "",
                        description: "",
                        active: false,
                  })
            }

      }, [data, isOpen])




      const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
            const { name, value } = e.target;
            setFormData((prevData) => ({
                  ...prevData,
                  [name]: name === "active" ? value === "true" : value // Convert active to boolean
            }));
      };

      const handleSave = () => {
            const fetcherData = new FormData();
            fetcherData.append("actionType", "addonGroupAdd");
            fetcherData.append("mode", "editMode");
            fetcherData.append("AddonGId", data?.id?.toString())
            fetcherData.append("sellerId", sellerId as unknown as string);
            fetcherData.append("addonGroupData", JSON.stringify(formData))
            addonsGroupFetcher.submit(fetcherData, { method: "POST" })
            onClose()
      };
      if (!isOpen) return null;




      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="max-w-md p-6 bg-white rounded-xl shadow-2xl sm:max-w-lg">
                        <DialogTitle className="text-xl font-bold text-gray-900 sm:text-2xl">{header}</DialogTitle>

                        <div className="mt-4 space-y-5 max-h-[60vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                              {/* Editable Fields */}
                              <div className="space-y-4">
                                    {[
                                          { id: 1, name: "internalName" as const, type: "text", label: "InternalName" },
                                          { id: 2, name: "displayName" as const, type: "text", label: "DisplayName" },
                                          { id: 3, name: "description" as const, type: "text", label: "Description" },

                                    ].map((field) => (
                                          <div key={field.name} className="flex flex-col gap-2 sm:flex-row sm:items-center">
                                                <label className="w-full text-sm font-medium text-gray-700 sm:w-1/3">{field.label}</label>


                                                <input
                                                      type={field.type}
                                                      name={field.name}
                                                      value={formData?.[field.name as keyof MyAddonGroupData] ?? ""} onChange={handleChange}
                                                      className="w-full rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 sm:w-2/3"
                                                      placeholder={`Enter ${field.label}`}
                                                />

                                          </div>
                                    ))}
                              </div>

                              {/* Radio Button Group */}
                              <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                                    <label className="w-full text-sm font-medium text-gray-700 sm:w-1/3">Active Status</label>
                                    <div className="flex gap-6 w-full sm:w-2/3">
                                          <label className="flex items-center gap-2 text-sm text-gray-600 cursor-pointer">
                                                <input
                                                      type="radio"
                                                      name="active"
                                                      value="true"
                                                      checked={formData?.active === true}
                                                      onChange={handleChange}
                                                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                                />
                                                Yes
                                          </label>
                                          <label className="flex items-center gap-2 text-sm text-gray-600 cursor-pointer">
                                                <input
                                                      type="radio"
                                                      name="active"
                                                      value="false"
                                                      checked={formData?.active === false}
                                                      onChange={handleChange}
                                                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                                                />
                                                No
                                          </label>
                                    </div>
                              </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="mt-6 flex flex-col gap-3 sm:flex-row sm:justify-end">
                              <button
                                    onClick={onClose}
                                    className="w-full rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 sm:w-auto"
                              >
                                    Cancel
                              </button>
                              <button
                                    onClick={handleSave}
                                    disabled={loading} // Disable button while loading
                                    className={`w-full rounded-lg ${loading ? 'bg-gray-400' : 'bg-blue-600'} px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:w-auto`}
                              >
                                    {loading ? 'Saving...' : 'Save'} {/* Show loader text */}
                              </button>
                        </div>
                  </DialogContent>
            </Dialog>
      );
};

export default AddonsGroupModal;