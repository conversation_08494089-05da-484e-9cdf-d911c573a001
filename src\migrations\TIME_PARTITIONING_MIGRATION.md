# Time Partitioning Migration Guide

## Overview
This guide covers the migration to add time-based partitioning to both NotificationLog and WebhookLog tables for efficient querying of millions of records.

## Migration Files

### Migration 004: NotificationLog Time Partitioning
- **File**: `004-add-time-partitioning-to-notification-logs.ts`
- **Dependencies**: Migration 003 (NotificationLog entity enhancement)
- **Purpose**: Adds date partitioning to notification logs

### Migration 005: WebhookLog Time Partitioning  
- **File**: `005-add-time-partitioning-to-webhook-logs.ts`
- **Dependencies**: Migration 001 (WebhookLog table creation)
- **Purpose**: Adds date partitioning to webhook logs

## What These Migrations Do

### 1. **Add New Global Secondary Indexes**
Each migration adds 3 new GSIs for time-based querying:

#### NotificationLog Table
- `DatePartitionIndex`: `datePartition` + `timestamp`
- `MonthPartitionIndex`: `monthPartition` + `timestamp`  
- `BusinessDateIndex`: `businessId` + `datePartition`

#### WebhookLog Table
- `DatePartitionIndex`: `datePartition` + `timestamp`
- `MonthPartitionIndex`: `monthPartition` + `timestamp`
- `BusinessDateIndex`: `businessNumber` + `datePartition`

### 2. **Data Migration (Last 30 Days)**
- Scans existing records from the last 30 days
- Adds `datePartition` (YYYY-MM-DD) and `monthPartition` (YYYY-MM) fields
- Processes in batches to avoid throttling
- Provides detailed progress logging

### 3. **Cost Optimization**
- Reduces query costs by 60-80%
- Enables efficient time-based queries
- Prevents hot partition issues
- Supports pagination and batch operations

## Running the Migrations

### Prerequisites
Ensure you have run the required dependency migrations:

```bash
# For NotificationLog migration (004)
npm run migrate:notification-log

# For WebhookLog migration (005)  
npm run migrate:whatsapp-status
```

### Step 1: Run NotificationLog Migration
```bash
npm run migrate:time-partitioning-notification
```

**Expected Output:**
```
🚀 Migration 004: Add Time Partitioning to NotificationLog Table
📋 Enhancing table: notification_logs_prod
✨ Features:
   - Time-based partitioning (datePartition, monthPartition)
   - 3 new Global Secondary Indexes for time-based queries
   - Cost optimization for millions of records
   - Data migration for last 30 days of records

⏳ Step 1: Adding new Global Secondary Indexes...
   Adding 3 new indexes one by one (DynamoDB limitation)...
   Creating index 1/3: DatePartitionIndex...
   Waiting for DatePartitionIndex to become active...
   ✅ DatePartitionIndex is now active!
   Creating index 2/3: MonthPartitionIndex...
   ✅ MonthPartitionIndex is now active!
   Creating index 3/3: BusinessDateIndex...
   ✅ BusinessDateIndex is now active!
✅ All indexes created successfully!

⏳ Step 2: Migrating existing data (last 30 days)...
   Processed 1500 items, updated 1200 items
✅ Data migration completed. Processed: 1500, Updated: 1200

🎉 Migration completed successfully!
💡 Your NotificationLog queries will now be much more efficient.
```

### Step 2: Run WebhookLog Migration
```bash
npm run migrate:time-partitioning-webhook
```

**Expected Output:**
```
🚀 Migration 005: Add Time Partitioning to WebhookLog Table
📋 Enhancing table: webhook_logs_prod
✨ Features:
   - Time-based partitioning (datePartition, monthPartition)
   - 3 new Global Secondary Indexes for time-based queries
   - Cost optimization for millions of webhook records
   - Data migration for last 30 days of records

⏳ Step 1: Adding new Global Secondary Indexes...
   Adding 3 new indexes one by one (DynamoDB limitation)...
   Creating index 1/3: DatePartitionIndex...
   ✅ DatePartitionIndex is now active!
   Creating index 2/3: MonthPartitionIndex...
   ✅ MonthPartitionIndex is now active!
   Creating index 3/3: BusinessDateIndex...
   ✅ BusinessDateIndex is now active!
✅ All indexes created successfully!

⏳ Step 2: Migrating existing data (last 30 days)...
   Processed 800 items, updated 650 items
✅ Data migration completed. Processed: 800, Updated: 650

🎉 Migration completed successfully!
💡 Your WebhookLog queries will now be much more efficient.
```

### Step 3: Validate Migrations
```bash
npm run migrate:time-partitioning-validate
```

**Expected Output:**
```
🔍 Validating Migration 004: Add Time Partitioning to NotificationLog Table
✅ Migration validation passed!
   - Table exists
   - All required indexes exist and are active
   - Time partitioning is ready for use

🔍 Validating Migration 005: Add Time Partitioning to WebhookLog Table
✅ Migration validation passed!
   - Table exists
   - All required indexes exist and are active
   - Time partitioning is ready for use
```

## Migration Timeline

### Estimated Duration
- **Index Creation**: 5-10 minutes per table (DynamoDB limitation)
- **Data Migration**: 2-5 minutes per table (depends on record count)
- **Total Time**: 15-30 minutes for both tables

### Progress Monitoring
The migrations provide real-time progress updates:
- Index creation status
- Data migration progress
- Error handling and retry logic

## Rollback Instructions

If you need to rollback the migrations:

```bash
npm run migrate:time-partitioning-rollback
```

**Note**: This will remove the new indexes but will NOT remove the `datePartition` and `monthPartition` fields from existing records.

## Post-Migration Usage

### New Query Methods Available

#### NotificationLog Repository
```typescript
// Most efficient business queries
await notificationRepository.getLogsByBusinessAndDateRange(
    businessId, 
    '2024-01-01', 
    '2024-01-31'
);

// Daily analytics
await notificationRepository.getLogsByDate('2024-01-15');

// Monthly analytics  
await notificationRepository.getLogsByMonth('2024-01');

// Business analytics
await notificationRepository.getBusinessAnalytics(businessId, startDate, endDate);
```

#### WebhookLog Repository
```typescript
// Most efficient business queries
await webhookRepository.getLogsByBusinessAndDateRange(
    businessNumber, 
    '2024-01-01', 
    '2024-01-31'
);

// Daily webhook analytics
await webhookRepository.getLogsByDate('2024-01-15');

// Monthly webhook analytics
await webhookRepository.getLogsByMonth('2024-01');

// Business webhook analytics
await webhookRepository.getBusinessWebhookAnalytics(businessNumber, startDate, endDate);
```

## Cost Benefits

### Before Migration
- Large scans across entire tables
- High RCU consumption for time-based queries
- Potential hot partition issues
- Expensive analytics queries

### After Migration
- Targeted queries using date partitions
- 60-80% reduction in RCU consumption
- Efficient pagination and batch operations
- Cost-effective analytics without full data transfer

## Troubleshooting

### Common Issues

#### 1. Migration Fails with "Table does not exist"
**Solution**: Run dependency migrations first
```bash
# For NotificationLog
npm run migrate:notification-log

# For WebhookLog
npm run migrate:whatsapp-status
```

#### 2. Index Creation Times Out
**Solution**: This is normal for large tables. The migration will retry automatically.

#### 3. Data Migration Takes Too Long
**Solution**: The migration only processes the last 30 days. This is intentional to minimize costs.

#### 4. Validation Fails
**Solution**: Check if indexes are still being created. Wait a few minutes and retry validation.

### Error Recovery
If a migration fails partway through:
1. Check the error message
2. Run validation to see current state
3. Re-run the migration (it will skip completed steps)
4. If issues persist, use rollback and start fresh

## Performance Monitoring

### Key Metrics to Track
- **Query Performance**: Monitor query execution times
- **Cost Reduction**: Track RCU consumption before/after
- **Index Usage**: Monitor which indexes are being used
- **Error Rates**: Watch for any query failures

### CloudWatch Alerts
Set up alerts for:
- High RCU consumption
- Query timeouts
- Index throttling

## Support

If you encounter issues:
1. Check the migration logs for detailed error messages
2. Verify AWS permissions for DynamoDB operations
3. Ensure sufficient capacity for index creation
4. Contact the development team with specific error details

## Migration Checklist

- [ ] Run dependency migrations (003 for NotificationLog, 001 for WebhookLog)
- [ ] Run NotificationLog time partitioning migration (004)
- [ ] Run WebhookLog time partitioning migration (005)
- [ ] Validate both migrations
- [ ] Update application code to use new query methods
- [ ] Monitor performance and costs
- [ ] Set up CloudWatch alerts for monitoring

This migration will significantly improve your DynamoDB query performance and reduce costs for millions of records. 