import { isTokenExpired } from "./jwt";
import { getSession, commitSession } from "./session.server";
import { ApiResponse } from "~/types/api/Api";
import dayjs from 'dayjs';

export const API_BASE_URL = process.env.API_BASE_URL;

type HttpMethod = "GET" | "POST" | "PUT" | "DELETE" | "PATCH";

interface TokenResponse {
  statusCode: number;
  access_token: string;
  refresh_token: string;
}

// Logger utility
const logRequest = (method: string, url: string, headers: any, body?: any) => {
  console.log(`\n[${dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')}] MNETAPI: Request [${method}] \x1b[38;2;30;135;200m${url}\x1b[0m`);
  console.log(`[${dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')}] MNETAPI: Headers:`, JSON.stringify(headers));
  if (body) {
    console.log(`[${dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')}] MNETAPI: Request Body:`, JSON.stringify(body));
  }
};

const logResponse = (url: string, status: number, body: string) => {
  console.log(`\n[${dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')}] MNETAPI: Response [${status}] \x1b[38;2;30;135;200m${url}\x1b[0m`);
  try {
    const parsedBody = JSON.parse(body);
    console.log(`[${dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')}] MNETAPI: Response Body:`, JSON.stringify(parsedBody));
  } catch {
    console.log(`[${dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')}] MNETAPI: Response Body:`, body);
  }
};

// Singleton for token refresh state
class TokenRefreshManager {
  private static instance: TokenRefreshManager;
  private isRefreshing = false;
  private refreshSubscribers: ((token: string) => void)[] = [];
  private latestAccessToken: string | null = null;

  private constructor() { }

  static getInstance(): TokenRefreshManager {
    if (!TokenRefreshManager.instance) {
      TokenRefreshManager.instance = new TokenRefreshManager();
    }
    return TokenRefreshManager.instance;
  }

  setLatestToken(token: string | null) {
    this.latestAccessToken = token;
    console.log("MNETAPI: Updated access token");
  }

  getLatestToken(): string | null {
    return this.latestAccessToken;
  }

  async waitForRefresh(): Promise<string> {
    console.log("MNETAPI: Waiting for token refresh to complete");
    return new Promise((resolve) => {
      this.refreshSubscribers.push(resolve);
    });
  }

  notifySubscribers(token: string) {
    console.log(
      `MNETAPI: Notifying ${this.refreshSubscribers.length} subscribers of new token`
    );
    this.refreshSubscribers.forEach((cb) => cb(token));
    this.refreshSubscribers = [];
  }

  async refreshToken(currentRefreshToken: string): Promise<TokenResponse> {
    if (this.isRefreshing) {
      console.log("MNETAPI: Token refresh already in progress, waiting...");
      const token = await this.waitForRefresh();
      return {
        access_token: token,
        refresh_token: currentRefreshToken,
        statusCode: 200,
      };
    }

    console.log("MNETAPI: Starting token refresh");
    this.isRefreshing = true;

    try {
      const refreshUrl = `${API_BASE_URL}/oauth/access_token`;
      const refreshBody = {
        grant_type: "refresh_token",
        refresh_token: currentRefreshToken,
      };

      logRequest(
        "POST",
        refreshUrl,
        { "Content-Type": "application/json" },
        refreshBody
      );

      const response = await fetch(refreshUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(refreshBody),
      });

      const responseText = await response.text();
      logResponse(refreshUrl, response.status, responseText);

      if (!response.ok) {
        // throw new Error(RefreshTokenCodes.expired);
        this.isRefreshing = false;
        return {
          statusCode: 401,
          access_token: "",
          refresh_token: "",
        };
      }

      const tokens = JSON.parse(responseText);
      tokens.statusCode = 200;
      console.log("MNETAPI: Token refresh successful");
      this.isRefreshing = false;
      this.notifySubscribers(tokens.access_token);
      return tokens;
    } catch (error) {
      console.error("MNETAPI: Token refresh failed:", error);
      this.isRefreshing = false;
      // throw error;
      return {
        statusCode: 401,
        access_token: "",
        refresh_token: "",
      };
    }
  }
}

export async function apiRequest<T>(
  url: string,
  method: HttpMethod = "GET",
  body?: unknown,
  headers: HeadersInit = {},
  expectJson = true,
  request?: Request
): Promise<ApiResponse<T>> {
  const tokenManager = TokenRefreshManager.getInstance();
  const session = request
    ? await getSession(request.headers.get("Cookie"))
    : null;
  const responseHeaders = new Headers();
  // let statusCode = 401;

  const getRequestHeaders = (accessToken?: string | null) => ({
    Accept: "application/json",
    "Content-Type": "application/json",
    ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    ...headers,
  });

  const executeRequest = async (accessToken?: string | null) => {
    const requestHeaders = getRequestHeaders(accessToken);
    logRequest(method, url, requestHeaders, body);

    const response = await fetch(url, {
      method,
      headers: requestHeaders,
      body: body ? JSON.stringify(body) : undefined,
    });

    const responseText = await response.text();
    logResponse(url, response.status, responseText);

    console.log("responseStatus: " + response.status);

    return { response, responseText };
  };

  try {
    let accessToken =
      session?.get("access_token") || tokenManager.getLatestToken();
    console.log(
      "MNETAPI: Using access token:",
      accessToken ? "Present" : "Not present"
    );

    const refreshToken = session?.get("refresh_token");

    console.log("isAccessTokenExpired: " + isTokenExpired(accessToken));

    if (accessToken && refreshToken && isTokenExpired(accessToken) && session) {
      console.log("MNETAPI: Token is expired, attempting token refresh");
      const refreshToken = session.get("refresh_token");
      if (!refreshToken) {
        console.error("MNETAPI: No refresh token available");
        throw new Error("No refresh token available");
      }

      const tokens = await tokenManager.refreshToken(refreshToken);

      if (tokens.statusCode === 200) {
        // Update session
        session.set("access_token", tokens.access_token);
        session.set("refresh_token", tokens.refresh_token);
        tokenManager.setLatestToken(tokens.access_token);
        console.log("MNETAPI: Updated session with new tokens");

        // Set new session cookie
        responseHeaders.set("Set-Cookie", await commitSession(session));

        accessToken = tokens.access_token;

        // // Retry request with new token
        // console.log("MNETAPI: Retrying original request with new token");
        // ({ response, responseText } = await executeRequest(
        //   tokens.access_token
        // ));
      } else {
        return {
          statusCode: 401,
          headers: undefined,
          data: {} as T,
        };
      }
    }

    const { response, responseText } = await executeRequest(accessToken);

    // Handle authentication errors
    if (response.status === 401 && session) {
      // console.log("MNETAPI: Received 401, attempting token refresh");
      // const refreshToken = session.get("refresh_token");
      // if (!refreshToken) {
      //   console.error("MNETAPI: No refresh token available");
      //   throw new Error("No refresh token available");
      // }

      // const tokens = await tokenManager.refreshToken(refreshToken);

      // if (tokens.statusCode === 200) {
      //   // Update session
      //   session.set("access_token", tokens.access_token);
      //   session.set("refresh_token", tokens.refresh_token);
      //   tokenManager.setLatestToken(tokens.access_token);
      //   console.log("MNETAPI: Updated session with new tokens");

      //   // Set new session cookie
      //   responseHeaders.set("Set-Cookie", await commitSession(session));

      //   // Retry request with new token
      //   console.log("MNETAPI: Retrying original request with new token");
      //   ({ response, responseText } = await executeRequest(
      //     tokens.access_token
      //   ));
      // } else {
      return {
        statusCode: 401,
        headers: undefined,
        data: {} as T,
      };
      // }
    }

    if (!response.ok) {
      const errorData = responseText
        ? JSON.parse(responseText)
        : { message: "API request failed" };
      console.error(`[${dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')}] MNETAPI: Request failed:`, errorData.message);
      throw new Error(errorData.message || "API request failed");
    }

    // if (!responseText && expectJson) {
    //   console.error("MNETAPI: Empty response received");
    //   throw new Error("Empty response received");
    // }

    return {
      statusCode: 200,
      data:
        expectJson && responseText && responseText.length
          ? (JSON.parse(responseText) as T)
          : (responseText as T),
      headers: responseHeaders,
    };
  } catch (error) {
    console.error(`[${dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')}] MNETAPI: Error in apiRequest:`, error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Unknown error occurred");
  }
}
