import { useState, useRef, useEffect } from "react";

import { Save, X } from "lucide-react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import { useFetcher, useSearchParams } from "@remix-run/react";
import { useToast } from "../ui/ToastProvider";

interface CreateBannerModalProps {
      isOpen: boolean;
      onClose: () => void;
      networkId: number

}

export default function CreateNetworkBannerModal({ isOpen, onClose, networkId }: CreateBannerModalProps) {
      const [bannerUrl, setBannerUrl] = useState<string>("");
      const [previewUrl, setPreviewUrl] = useState<string>("");
      const [sequenceId, setSequenceId] = useState<number | "">("");
      const fileInputRef = useRef<HTMLInputElement>(null);
      const [uploadError, setUploadError] = useState<string | null>(null);

      const uploadFetcher = useFetcher<{ success: boolean; fileUrl: string; error?: string }>();

      const bannerFetcher = useFetcher();

      const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
            const files = Array.from(event.target.files || []);
            if (files.length === 0) return;

            setUploadError(null);

            const MAX_FILE_SIZE = 500 * 1024
            const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];

            const validFile = files.find((file) => {
                  if (file.size > MAX_FILE_SIZE) {
                        setUploadError("File size exceeds 500Kb limit.");
                        return false;
                  }
                  if (!allowedTypes.includes(file.type)) {
                        setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed.");
                        return false;
                  }
                  return true;
            });

            if (!validFile) return;

            const uploadFormData = new FormData();
            uploadFormData.append("_action", "uploadImage");
            uploadFormData.append("file", validFile, validFile.name);

            uploadFetcher.submit(uploadFormData, {
                  method: "post",
                  action: "/home/<USER>",
                  encType: "multipart/form-data",
            });
      };
      const handleSave = () => {
            if (bannerUrl && sequenceId !== "") {
                  const formData = new FormData();
                  formData.append("bannerUrl", bannerUrl);
                  formData.append("sequenceId", sequenceId.toString());
                  formData.append("networkId", networkId.toString());

                  formData.append("actionType", "createNetworkBanner");
                  bannerFetcher.submit(formData, { method: "POST" })
            }
      };
      const handleClose = () => {
            onClose()

            setBannerUrl("");
            setPreviewUrl("");
            setSequenceId("")
      }

      const { showToast } = useToast()

      useEffect(() => {
            if (bannerFetcher?.data) {
                  if (bannerFetcher.data?.sucess) {
                        showToast("Network banner Created Successfully", "success");
                        onClose(); // Close only after success
                  } else {
                        if (bannerFetcher.data?.sucess == false) {
                              showToast("Network banner Failed", "error");

                        }
                  }
            }
      }, [bannerFetcher.data]);

      useEffect(() => {
            if (uploadFetcher.data) { // Ensure currentRowId is not null
                  if (uploadFetcher.data.error) {
                        setUploadError(uploadFetcher.data.error);
                  } else if (uploadFetcher.data.fileUrl) {
                        const uploadedUrl = uploadFetcher.data.fileUrl;

                        setBannerUrl(
                              uploadedUrl, // `!` asserts it's not null
                        );

                        setPreviewUrl(
                              uploadedUrl,
                        );

                        setUploadError(null);
                        if (fileInputRef.current) fileInputRef.current.value = "";
                  }
            }
      }, [uploadFetcher.data]); // Ensure currentRowId is included in dependencies


      return (
            <Dialog open={isOpen} onOpenChange={handleClose}>
                  <DialogContent>
                        <DialogTitle>Create Network Banner</DialogTitle>
                        <div className="flex flex-col gap-4">
                              {/* File Upload */}
                              <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    onChange={handleFileSelect}
                                    className="file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200 transition"
                              />
                              {/* Image Preview */}
                              {previewUrl && (
                                    <div className="flex flex-col items-center p-2 border border-gray-300 rounded-lg bg-white shadow-sm">
                                          <span className="text-md font-bold text-red-600">Upload Image 1000*400 Size</span>
                                          <span className="text-sm text-gray-600">Image Preview</span>
                                          <img src={previewUrl} alt="Preview" className="mt-2 rounded-md w-[250px] h-[100px] object-cover" />
                                    </div>
                              )}


                              {uploadError !== "" &&
                                    <span className="text-md font-bold text-red-600">{uploadError}</span>
                              }

                              {/* Sequence Input */}
                              <Input
                                    type="number"
                                    placeholder="Enter Sequence"
                                    value={sequenceId}
                                    onChange={(e) => setSequenceId(e.target.value ? Number(e.target.value) : "")}
                              />
                        </div>
                        <div className=" flex flex-row justify-center gap-5">
                              <Button onClick={handleClose} variant="outline"><X size={16} /> Cancel</Button>
                              <Button onClick={handleSave} disabled={!bannerUrl || sequenceId === ""}>
                                    <Save size={16} /> Save
                              </Button>
                        </div>
                  </DialogContent>
            </Dialog>
      );
}
