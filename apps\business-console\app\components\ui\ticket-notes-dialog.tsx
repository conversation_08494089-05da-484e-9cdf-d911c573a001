import { useState, useEffect } from "react";
import { useFetcher } from "@remix-run/react";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from "./dialog";
import { But<PERSON> } from "./button";
import { Input } from "./input";
import { TicketNote } from "~/types/api/businessConsoleService/Tickets";

interface TicketNotesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ticketId: number;
  ticketNotes: TicketNote[];
}

// Response type for note submission
interface NoteSubmitResponse {
  success: boolean;
  message?: string;
  note?: TicketNote;
  error?: string;
}

export function TicketNotesDialog({ 
  isOpen, 
  onClose, 
  ticketId, 
  ticketNotes
}: TicketNotesDialogProps) {
  const [note, setNote] = useState("");
  const addNoteFetcher = useFetcher<NoteSubmitResponse>();
  const [hasSubmitted, setHasSubmitted] = useState(false);
  
  const isSubmitting = addNoteFetcher.state === "submitting";
  
  // Reset note input when submission is successful
  useEffect(() => {
    if (
      hasSubmitted && 
      addNoteFetcher.state === "idle" && 
      addNoteFetcher.data?.success === true
    ) {
      setNote("");
      setHasSubmitted(false);
      
      // Simply close the dialog - Remix will automatically revalidate the data
      onClose();
    }
  }, [addNoteFetcher.state, addNoteFetcher.data, hasSubmitted, onClose]);
  
  const handleSubmit = () => {
    if (!note.trim()) return;
    
    setHasSubmitted(true);
    addNoteFetcher.submit(
      { 
        note,
        ticketId: ticketId.toString(),
        _action: "addNote"
      },
      { 
        method: "post", 
        action: `/home/<USER>/notes` 
      }
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Ticket Notes</DialogTitle>
        </DialogHeader>
        
        <div className="max-h-[400px] overflow-y-auto py-4">
          {ticketNotes && ticketNotes.length > 0 ? (
            <div className="space-y-4">
              {ticketNotes.map((noteItem) => (
                <div key={noteItem.ticketNoteId} className="border rounded-md p-3 bg-gray-50">
                  <p className="text-sm">{noteItem.note}</p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-center text-gray-500 py-4">No notes available</p>
          )}
        </div>
        
        <div className="pt-4 border-t">
          <div className="flex flex-col space-y-4">
            <Input
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder="Add a note..."
              className="w-full"
            />
            <Button 
              onClick={handleSubmit} 
              disabled={!note.trim() || isSubmitting}
              loading={isSubmitting}
              className="self-end"
            >
              Add Note
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 