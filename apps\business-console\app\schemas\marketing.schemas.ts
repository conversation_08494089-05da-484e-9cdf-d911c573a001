import { z } from "zod";

export const templateSchema = z.object({
  id: z.number(),
  name: z.string(),
  type: z.enum(["hybrid", "system"]),
  phone: z.string(),
  preview: z.string(),
  lastUsed: z.string(),
  variables: z.array(z.string()),
});

export const customerGroupSchema = z.object({
  id: z.string(),
  name: z.string(),
  count: z.number(),
});

export const customerSchema = z.object({
  id: z.number(),
  name: z.string(),
  phone: z.string(),
  email: z.string().optional(),
  address: z.string().optional(),
  lastOrder: z.string().optional(),
});

export const sendMessageSchema = z.object({
  templateId: z.number(),
  variables: z.record(z.string()),
  selectedGroup: z.string(),
  selectedItem: z.number().optional(),
  selectedCustomers: z.array(z.number()).optional(),
});

export const searchParamsSchema = z.object({
  page: z.coerce.number().default(1),
  pageSize: z.coerce.number().default(10),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

export type Template = z.infer<typeof templateSchema>;
export type CustomerGroup = z.infer<typeof customerGroupSchema>;
export type Customer = z.infer<typeof customerSchema>;
export type SendMessagePayload = z.infer<typeof sendMessageSchema>;
export type SearchParams = z.infer<typeof searchParamsSchema>; 