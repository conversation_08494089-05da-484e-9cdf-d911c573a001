// app/services/auth.server.ts
import { API_BASE_URL, apiRequest } from '~/utils/api';
import { DeviceInfo } from '~/types/deviceInfo';
import { ApiResponse } from '~/types/api/Api';
import { User } from '~/types/user';

interface RequestOtpPayload {
    app: string;
    mobileNumber: string;
    password: string;
    admin: boolean;
    deviceInfo: DeviceInfo;
}

interface VerifyOtpPayload {
    username: string;
    password: string;
}

interface VerifyOtpResponse {
    userId: number;
    userName: string;
    access_token: string;
    refresh_token: string;
    roles: string[];
    source?: string;
}

export const NETWORK_ID = 3;

export async function requestOtp(payload: RequestOtpPayload): Promise<ApiResponse<void>> {
    const url = `${API_BASE_URL}/login/otp/generate`;
    return apiRequest<void>(url, 'POST', payload, {}, false);
}

export async function verifyOtp(mobileNumber: string, otp: string, source?: string): Promise<ApiResponse<VerifyOtpResponse>> {
    let url = `${API_BASE_URL}/login`;
    if (source === "whatsAppLogin") {
      url = `${API_BASE_URL}/login?source=whatsAppLogin`;
    }

    const payload: VerifyOtpPayload = {
        username: JSON.stringify({
            mobileNumber: mobileNumber,
            appInfo: {
                app: "seller_app",
                networkId: 0,
                version: "200"
            }
        }),
        password: otp
    };

    return apiRequest<VerifyOtpResponse>(
        url,
        'POST',
        payload,
        { Authorization: 'Bearer null' }
    );
}

export function getDeviceInfo(): DeviceInfo {
    return {
        brand: 'google',
        buildNumber: '155',
        bundleId: 'com.mandi_app',
        deviceId: 'sunfish',
        model: 'Pixel 4a',
    };
}
