import React, { useCallback, useEffect, useState } from 'react';
import { Pencil, Trash } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { ResponsiveTable } from '../ui/responsiveTable';
import AddVariation from './AddVariation';
import { MyVariationData } from '~/types/api/businessConsoleService/SellerManagement';
import { useFetcher, useNavigate } from '@remix-run/react';
import GlobalSpinnerLoader from '../loader/GlobalSpinnerLoader';
import SpinnerLoader from '../loader/SpinnerLoader';
import ResponsiveSpinnnerLoader from '../loader/ResponsiveSpinnerLoader';
import { Input } from '../ui/input';
import { useDebounce } from '~/hooks/useDebounce';

interface MyVariationTabProps {
      variationData: MyVariationData[] | [];
      sellerId: number
}

const myVariationHeader = [
      "Id",
      "InternalName",
      "DisplayName",
      "Group",
      "Active",
      "",
      ""
]
const VariationTab: React.FC<MyVariationTabProps> = ({
      variationData,
      sellerId
}) => {
      const variationFetcher = useFetcher()
      const [isItemModalOpen, setIsItemModalOpen] = useState(false)
      const [selectedVarData, setSelectedVarData] = useState<MyVariationData>();
      const [isVarEdit, setIsVarEdit] = useState(false);
      const [searchTerm, setSearchTerm] = useState('');
      const [filteredVariations, setFilteredVariations] = useState<MyVariationData[]>([])
      const handleDelete = (variation: MyVariationData) => {
            const formData = new FormData();
            formData.append("actionType", "vardelete");
            formData.append("varId", variation?.id?.toString() ?? ""); variationFetcher.submit(formData, { method: 'post' })
      }
      const handleEditModal = (row: MyVariationData) => {
            setSelectedVarData(row);
            setIsVarEdit(true)
            setIsItemModalOpen(true)

      }
      const handleAddModal = () => {
            setSelectedVarData(undefined);
            setIsVarEdit(false)
            setIsItemModalOpen(true)

      }
      const loading = variationFetcher.state !== "idle"
      const debouncedSearchTerm = useDebounce(searchTerm, 500);





      useEffect(() => {
            if (debouncedSearchTerm.length >= 2 && debouncedSearchTerm !== "") {
                  const filtered = variationData.filter((item) =>
                        [item.displayName, item.internalName].some(
                              (field) => field?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
                        )
                  )
                  setFilteredVariations(filtered)
            }
            else {
                  setFilteredVariations(variationData)
            }

      }, [debouncedSearchTerm, variationData]);

      const navigate = useNavigate()

      return (
            <>
                  <div className="flex justify-between mb-4">
                        {loading && <SpinnerLoader loading={loading} size={20} />}
                        <Input
                              placeholder="Search by Internal Name or display Name"
                              value={searchTerm}

                              type='search'
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm mt-2 rounded-full "
                        />

                        {/* <Select value={pageSize} onValueChange={handlePageSizeChange}>
                              <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="Items per page" />
                              </SelectTrigger>
                              <SelectContent>
                                    <SelectItem value="5">5 per page</SelectItem>
                                    <SelectItem value="10">10 per page</SelectItem>
                                    <SelectItem value="20">20 per page</SelectItem>
                                    <SelectItem value="50">50 per page</SelectItem>
                              </SelectContent>
                        </Select> */}
                  </div>
                  <ResponsiveTable
                        headers={myVariationHeader}
                        data={filteredVariations}
                        renderRow={(row) => (
                              <tr key={row.id} className="border-b">
                                    <td className="py-2 px-3 text-center whitespace-normal break-words ">{row.id}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words text-blue-300 cursor-pointer" >{row?.internalName || "-"}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.displayName || "-"}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.groupName}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words">
                                          {row?.active ? (
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                      <svg
                                                            className="w-4 h-4 mr-1 text-green-500"
                                                            fill="currentColor"
                                                            viewBox="0 0 20 20"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                      >
                                                            <path
                                                                  fillRule="evenodd"
                                                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                                  clipRule="evenodd"
                                                            />
                                                      </svg>
                                                      Active
                                                </span>
                                          ) : (
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                      <svg
                                                            className="w-4 h-4 mr-1 text-red-500"
                                                            fill="currentColor"
                                                            viewBox="0 0 20 20"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                      >
                                                            <path
                                                                  fillRule="evenodd"
                                                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                                  clipRule="evenodd"
                                                            />
                                                      </svg>
                                                      Inactive
                                                </span>
                                          )}
                                    </td>
                                    <td className="py-2 px-3 text-center cursor-pointer">
                                          <Button
                                                variant="ghost"
                                                size="sm"
                                                className="text-red-500 hover:text-red-900"
                                                onClick={() => {
                                                      if (confirm(`Are you sure you want to delete this Variation? ${row.displayName}`)) {
                                                            handleDelete(row)
                                                      }
                                                }}
                                                style={{ alignSelf: "flex-end" }}
                                          >
                                                <Trash size={20} />
                                          </Button>
                                    </td>
                                    <td className="py-2 px-3 text-center cursor-pointer">
                                          <Pencil color='blue' size={20} onClick={() => handleEditModal(row)} />
                                    </td>
                              </tr>
                        )}
                  />
                  {/* <div className="flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap">
                        <h2 className="shrink-0">Current Page: {pageNum + 1}</h2>
                        <div className="overflow-x-auto">
                              <ResponsivePagination
                                    totalPages={Number(pageSize)}
                                    currentPage={pageNum}
                                    onPageChange={handlePageChange}
                              />
                        </div>
                  </div> */}
                  <AddVariation
                        isOpen={isItemModalOpen}
                        isEdit={isVarEdit}
                        data={isVarEdit ? selectedVarData : undefined}
                        onClose={() => setIsItemModalOpen(false)}
                        header={isVarEdit ? 'Edit Variation' : 'Add Variation'} sellerId={sellerId} />
                  <Button className="fixed bottom-5 right-5 rounded-full cursor-pointer" onClick={() => handleAddModal()}>+ Add Variation</Button>
            </>
      );
};
export default VariationTab;
