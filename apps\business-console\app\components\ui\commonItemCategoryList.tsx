import { j<PERSON>, <PERSON>aderFunction } from "@remix-run/node";
import { Form, useLoaderData } from "@remix-run/react";
import { error } from "console";
import { useState } from "react";
import { Input } from "~/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { getMasterItemCategory, getNetWorkItemCategories } from "~/services/masterItemCategories";
import { MasterItemCategories } from "~/types/api/businessConsoleService/MasterItemCategory";
import { Button } from "./button";
import { Card } from "./card";
import React from "react";

interface CommonItemCategoryProps {
      data: MasterItemCategories[] | undefined,
      mItemCategoryList?: MasterItemCategories[] | undefined,
      ncId?: number,
      addItem?: (categoryId: number) => void



}
export default function CommonItemCategoryList({ data, mItemCategoryList, addItem }: CommonItemCategoryProps) {
      const [searchTerm, setSearchTerm] = useState('');
      const [openAddbtn, setOpenAddbtn] = useState(false);
      const [selectedCategory, setSelectedCategory] = useState("");
      return (
            <div className="container mx-auto p-6">
                  <div className="flex justify-between mb-4">
                        <Input
                              placeholder="Search by Item Name"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"
                        />
                        <Button onClick={() => setOpenAddbtn(true)} size="sm">
                              Add
                        </Button>
                  </div>
                  {openAddbtn && <Card className="w-full md:w-1/2 max-w-xl p-6 border rounded-lg shadow-md space-y-6 bg-white ">
                        <div className="space-y-6">
                              <div className="my-4">
                                    <div className="flex items-center space-x-4">
                                          <p className="text-lg font-semibold text-gray-700">Adding Network Category</p>
                                    </div>
                                    <div className="flex items-center space-x-4 mt-6">
                                          <p className="text-lg font-semibold text-gray-700">Select Category :</p>
                                          <div className="w-full sm:w-auto">
                                                <select
                                                      className="border border-gray-300 p-2 rounded-md w-full sm:w-56 text-sm"
                                                      value={selectedCategory}
                                                      onChange={(e) => setSelectedCategory(e.target.value)}
                                                >
                                                      <option value="">All Categories</option>
                                                      {mItemCategoryList?.map((category) => (
                                                            <option key={category.id} value={category.id}>
                                                                  {category.name}
                                                            </option>
                                                      ))}
                                                </select>
                                          </div>
                                    </div>
                                    <div className="  justify-center items-center my-4">
                                          <Button className="align-center" onClick={() => addItem?.(Number(selectedCategory))}>ADD</Button>
                                    </div>
                              </div>
                        </div>
                  </Card>}
                  {openAddbtn == false && <Table>
                        <TableHeader>
                              <TableRow>
                                    <TableHead className="cursor-pointer" >Item Image</TableHead>
                                    <TableHead className="cursor-pointer" >Item Name </TableHead>
                                    <TableHead className="cursor-pointer" >picturex </TableHead>
                                    <TableHead className="cursor-pointer" >picturexx </TableHead>
                                    <TableHead className="cursor-pointer" >level </TableHead>

                              </TableRow>
                        </TableHeader>
                        <TableBody>
                              {data?.filter((x) => x.name.toLowerCase().includes(searchTerm.toLowerCase())).sort((a, b) => a.name.localeCompare(b.name)).map((item) => {
                                    return (
                                          <TableRow key={item.level}>
                                                <TableCell> <img src={item?.picture}
                                                      alt="ItemImage"
                                                      className="h-10 w-10" /></TableCell>
                                                <TableCell>{item.name}</TableCell>
                                                <TableCell> <img src={item?.picturex}
                                                      alt="ItemImage"
                                                      className="h-10 w-10" /></TableCell>
                                                <TableCell> <img src={item?.picturexx}
                                                      alt="ItemImage"
                                                      className="h-10 w-10" /></TableCell>

                                                <TableCell>{item.level}</TableCell>

                                          </TableRow>
                                    )
                              })}
                        </TableBody>
                  </Table>}


            </div>
      )



}