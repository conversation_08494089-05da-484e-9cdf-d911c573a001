import { use<PERSON><PERSON><PERSON>, useLoaderD<PERSON>, useSearchParams } from '@remix-run/react';
import { BadgeCheck, BadgeX, Filter, PercentCircle, Edit2 } from 'lucide-react';
import { useState } from 'react';
import { URL } from 'url'
import CreateDeliveryConfig from '~/components/common/CreateDeliveryConfig';
import SpinnerLoader from '~/components/loader/SpinnerLoader';
import { Button } from '~/components/ui/button';
import { createDcConfig, getDeliveryConfigs } from '~/services/deliveryConfigService'
import { DcBody, DcCong, DcCreateRes, dclistingResponse } from '~/types/api/businessConsoleService/DeliveryConfig';
import { withAuth, withResponse } from '~/utils/auth-utils'
interface loaderData{
  dcConfig:DcCong,
  sellerId:number
}
export const loader= withAuth(
  async({request,user})=>{
    const url= new URL(request.url);
    const sellerId= Number(url.searchParams.get("sellerId"));    
    try{
const response = await getDeliveryConfigs(sellerId,request);
 return withResponse({
  dcConfig:response.data,
  sellerId:sellerId
 })
    }
    catch(err){
      throw new Response("failed to get Configs", { status: 500 })
    }
  }
)

export const action = withAuth(async ({ request }) => {

  const formData = await request.formData();
  const actionType = formData.get("actionType");
  const sellerId= Number(formData.get("sellerId"));
  const dcBody=formData.get("dcreqBody");
  const configId= Number(formData.get('configId'))


    
  if (actionType === "createDcConfig") {

    if(!dcBody|| typeof dcBody!=="string" )
    { throw new Response("invalid request body")

    }
    let dcReqBody ;
    dcReqBody= JSON.parse(dcBody);
    dcReqBody.sellerId = sellerId;
    if (dcReqBody.configType === "PERCENTAGE_BASED") {
      delete dcReqBody.minOrderValue;
      delete dcReqBody.maxOrderValue;
      if(dcReqBody.maxBuyerDeliveryCharge>0){
       delete dcReqBody.maxSellerDeliveryCharge
      }
      else if(dcReqBody.maxSellerDeliveryCharge>0){
        delete dcReqBody.maxBuyerDeliveryCharge
      }
      else if(dcReqBody.maxSellerDeliveryCharge===0||dcReqBody.maxBuyerDeliveryCharge===0){
        delete dcReqBody.maxSellerDeliveryCharge
        delete dcReqBody.maxBuyerDeliveryCharge
      }
    }
    if(dcReqBody.configType=== "ORDER_VALUE_BASED"){
      delete dcReqBody.maxBuyerDeliveryCharge,
      delete dcReqBody.maxSellerDeliveryCharge
    }

    try {
      const response= await createDcConfig( dcReqBody,request,configId)
      
       return withResponse({ sucess: response.status === 200 }, response.headers);
     
    } catch (error) {
      throw new Response("failed to create", { status: 500 })

    }
    
  }
}
)



const CONFIG_TYPE_LABELS: Record<string, string> = {
  PERCENTAGE_BASED: "Percentage",
  ORDER_VALUE_BASED:"OrderValue"
};

function DeliveryConfig() {
  const { dcConfig, sellerId } = useLoaderData<loaderData>();
  const [showError, setShowError] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [configTypeFilter, setConfigTypeFilter] = useState<string>("");
  const [editConfig, setEditConfig] = useState<dclistingResponse | null>(null); // Track config being edited
  const fetcher = useFetcher<DcCreateRes>();

  // Filtered data
  const filteredData = configTypeFilter
    ? dcConfig?.data?.filter((item) => item.configType === configTypeFilter)
    : dcConfig?.data;



    const handleSave = (formData: DcBody) => {
      const fetcherData = new FormData();
      fetcherData.append("actionType", "createDcConfig");
      fetcherData.append("sellerId", sellerId.toString());
      fetcherData.append("dcreqBody", JSON.stringify(formData));
      // If editing, send config id
      if (editConfig?.id) {
        fetcherData.append("configId", editConfig.id.toString());
      }
      fetcher.submit(fetcherData, { method: "POST" });
      setEditConfig(null); // Reset after save
      setIsModalOpen(false);
    };

    const handleEdit = (config: dclistingResponse) => {
      setEditConfig(config);
      setIsModalOpen(true);
    };

    const handleModalClose = () => {
      setIsModalOpen(false);
      setEditConfig(null);
    };

  const loading = fetcher.state !== "idle";
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white flex flex-col items-center py-8 px-2">

      {loading && <SpinnerLoader loading={loading} size={20}/>}
      {dcConfig?.error?.message && showError && (
        <div className="fixed top-6 left-1/2 -translate-x-1/2 z-50 bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded shadow-lg flex items-center gap-4">
          <span className="font-semibold">Error:</span>
          <span>{dcConfig.error.message}</span>
          <button
            className="ml-4 px-2 py-1 bg-red-400 text-white rounded hover:bg-red-500"
            onClick={() => setShowError(false)}
          >
            Close
          </button>
        </div>
      )}

      <div className="w-full max-w-4xl">
        {/* Filter Bar */}
        <div className="flex flex-col sm:flex-row items-center justify-between mb-6 gap-4">
          <h1 className="text-2xl font-bold text-blue-900 flex items-center gap-2">
            <PercentCircle className="h-7 w-7 text-blue-500" />
            Delivery Configurations
          </h1>
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-blue-400" />
            <select
              value={configTypeFilter}
              onChange={e => setConfigTypeFilter(e.target.value)}
              className="rounded border border-gray-300 p-2 text-sm focus:border-blue-400 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            >
              <option value="">All Config Types</option>
              {Array.from(new Set(dcConfig?.data?.map(item => item.configType))).map(type => (
                <option key={type} value={type}>
                  {CONFIG_TYPE_LABELS[type] || type}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* List of Cards */}
        {filteredData?.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {filteredData.map((item: dclistingResponse) => (
              <div
                key={item.id}
                className="relative bg-gradient-to-br from-blue-50 via-white to-blue-100 rounded-2xl shadow-xl border border-blue-200 p-6 flex flex-col gap-4 hover:shadow-2xl hover:scale-[1.01] transition-all duration-200"
              >
                {/* Config Type Label - top left */}
                <span
                  className="absolute top-3 left-3 px-3 py-1 rounded-full bg-blue-100 text-blue-700 text-xs font-semibold border border-blue-200 shadow z-10"
                  style={{ pointerEvents: 'none' }}
                >
                  {CONFIG_TYPE_LABELS[item.configType] || item.configType}
                </span>
                {/* Edit Button - top right */}
                <button
                  className="absolute top-3 right-3 bg-white border border-blue-200 hover:bg-blue-100 text-blue-700 rounded-full p-2 shadow transition group z-20"
                  title="Edit"
                  onClick={() => handleEdit(item)}
                >
                  <Edit2 className="h-5 w-5 group-hover:scale-110 transition-transform" />
                  <span className="sr-only">Edit</span>
                </button>
                {/* Card Header */}
                <div className="flex items-center justify-between mt-6 mb-2">
                  <div className="flex items-center gap-3">
                    <span className="text-xl font-extrabold text-blue-800 tracking-wide">#{item.id}</span>
                    <span className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold shadow-sm
                      ${item.active ? "bg-green-100 text-green-700" : "bg-gray-200 text-gray-500"}`}>
                      {item.active ? (
                        <>
                          <BadgeCheck className="h-4 w-4" /> Active
                        </>
                      ) : (
                        <>
                          <BadgeX className="h-4 w-4" /> Inactive
                        </>
                      )}
                    </span>
                  </div>
                  <span className="text-xs text-blue-500 font-semibold">v{item.version}</span>
                </div>
                {/* Divider */}
                <div className="border-t border-blue-100 my-2"></div>
                {/* Details */}
                <div className="grid grid-cols-2 gap-x-6 gap-y-2 text-sm text-gray-700">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-900">Buyer %:</span>
                    <span>{item.buyerPercentage || 0}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-900">Seller %:</span>
                    <span>{item.sellerPercentage || 0}</span>
                  </div>
                  {item.configType === "ORDER_VALUE_BASED" && (
                    <>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">Min Order Value:</span>
                        <span>{item.minOrderValue || 0}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">Max Order Value:</span>
                        <span>{item.maxOrderValue || 0}</span>
                      </div>
                    </>
                  )}
                  {item.configType === "PERCENTAGE_BASED" && (
                    <>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">Max Buyer D.C:</span>
                        <span>{item.maxBuyerDeliveryCharge || 0}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">Max Seller D.C:</span>
                        <span>{item.maxSellerDeliveryCharge || 0}</span>
                      </div>
                    </>
                  )}
                </div>
                {/* Dates */}
                <div className="flex flex-row items-center justify-between mt-4 text-xs text-gray-500 gap-2">
                  <div className="flex items-center gap-2">
                    <span className="inline-block bg-blue-100 text-blue-700 rounded px-2 py-0.5 font-semibold">Created</span>
                    <span className="font-medium text-gray-700">{new Date(item.createdAt).toLocaleString()}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="inline-block bg-blue-100 text-blue-700 rounded px-2 py-0.5 font-semibold">Updated</span>
                    <span className="font-medium text-gray-700">{new Date(item.updatedAt).toLocaleString()}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-12">No delivery configs found.</div>
        )}

        {/* Create Button */}
        <Button
          className="fixed bottom-5 right-5 rounded-full shadow-lg bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 text-lg"
          onClick={() => {
            setEditConfig(null);
            setIsModalOpen(true);
          }}
        >
          + Create Delivery Config
        </Button>
      </div>
      <CreateDeliveryConfig
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onSave={handleSave}
        initialConfig={
          editConfig
            ? {
                ...editConfig,
                // Remove fields not in DcBody if needed
                // id, createdAt, updatedAt, version, etc. are not part of DcBody
              }
            : undefined
        }
      />
    </div>
  );
}

export default DeliveryConfig;