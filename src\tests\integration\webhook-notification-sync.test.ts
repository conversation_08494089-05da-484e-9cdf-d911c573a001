/**
 * Webhook Notification Sync Integration Tests
 * 
 * End-to-end tests for webhook processing and notification synchronization,
 * testing the complete flow from webhook receipt to notification updates.
 */

import { WebhookNotificationSyncService } from '../../services/webhookNotificationSync.service.js';
import { NotificationLogService } from '../../services/notificationLogService.js';
import { 
    NotificationStatus, 
    CampaignType, 
    MessageCategory, 
    CustomerSegment 
} from '../../database/entities/NotificationLog.js';
import { WhatsAppStatus } from '../../types/whatsapp-webhook.types.js';

// Mock testing functions since no test framework is configured
const describe = (name: string, fn: () => void) => {
    console.log(`\n🧪 ${name}`);
    fn();
};

const test = (name: string, fn: () => Promise<void>) => {
    console.log(`  ⚙️ ${name}`);
    fn().then(() => console.log(`  ✅ ${name} - PASSED`))
         .catch(err => console.error(`  ❌ ${name} - FAILED:`, err));
};

const expect = (actual: any) => ({
    toBe: (expected: any) => {
        if (actual !== expected) {
            throw new Error(`Expected ${expected}, got ${actual}`);
        }
    },
    toBeDefined: () => {
        if (actual === undefined || actual === null) {
            throw new Error(`Expected value to be defined, got ${actual}`);
        }
    },
    toHaveLength: (length: number) => {
        if (!actual || actual.length !== length) {
            throw new Error(`Expected length ${length}, got ${actual?.length}`);
        }
    },
    toHaveProperty: (prop: string) => {
        if (!actual || !(prop in actual)) {
            throw new Error(`Expected object to have property ${prop}`);
        }
    },
    toContain: (item: any) => {
        if (!actual || !actual.includes(item)) {
            throw new Error(`Expected array to contain ${item}`);
        }
    },
    toMatch: (pattern: RegExp) => {
        if (!pattern.test(actual)) {
            throw new Error(`Expected ${actual} to match ${pattern}`);
        }
    },
    toEqual: (matcher: any) => {
        if (typeof matcher === 'function' && matcher.name === 'Number') {
            if (typeof actual !== 'number') {
                throw new Error(`Expected number, got ${typeof actual}`);
            }
        } else if (typeof matcher === 'object' && matcher?.constructor?.name === 'Number') {
            // Handle expect.any(Number) pattern
            if (typeof actual !== 'number') {
                throw new Error(`Expected number, got ${typeof actual}`);
            }
        } else {
            // Regular equality check
            if (actual !== matcher) {
                throw new Error(`Expected ${matcher}, got ${actual}`);
            }
        }
    },
    toBeGreaterThan: (value: number) => {
        if (actual <= value) {
            throw new Error(`Expected ${actual} to be greater than ${value}`);
        }
    },
    toBeLessThan: (value: number) => {
        if (actual >= value) {
            throw new Error(`Expected ${actual} to be less than ${value}`);
        }
    }
});

describe('Webhook Notification Sync Integration', () => {
    let syncService: WebhookNotificationSyncService;
    let notificationService: NotificationLogService;

    // Initialize services
    syncService = new WebhookNotificationSyncService();
    notificationService = new NotificationLogService();

    describe('WhatsApp Message Flow Simulation', () => {
        test('should handle complete message lifecycle: sent → delivered → read', async () => {
            // Step 1: Create initial campaign notification
            const campaignNotification = await notificationService.logCampaignNotification({
                businessId: 'business-test',
                campaignId: 'integration_test_campaign',
                campaignName: 'Integration Test Campaign',
                campaignType: CampaignType.PROMOTIONAL,
                messageCategory: MessageCategory.PROMOTIONAL_OFFER,
                customerSegment: CustomerSegment.HIGH_VALUE,
                tags: ['integration', 'test'],
                recipients: ['+919876543210'],
                templateName: 'test_template',
                recipient: '+919876543210',
                providerRequest: { messaging_product: 'whatsapp' },
                status: NotificationStatus.PENDING,
                whatsappMessageId: 'wamid.integration_test_123'
            });

            expect(campaignNotification.status).toBe(NotificationStatus.PENDING);
            expect(campaignNotification.whatsappMessageId).toBe('wamid.integration_test_123');

            // Step 2: Simulate SENT webhook
            const sentStatus: WhatsAppStatus = {
                id: 'wamid.integration_test_123',
                status: 'sent',
                timestamp: Date.now().toString(),
                recipient_id: '+919876543210'
            };

            const sentResult = await syncService.processStatusUpdates('webhook-sent-123', [sentStatus]);
            expect(sentResult.successfulUpdates).toBe(1);

            // Step 3: Simulate DELIVERED webhook
            const deliveredStatus: WhatsAppStatus = {
                id: 'wamid.integration_test_123',
                status: 'delivered',
                timestamp: (Date.now() + 5000).toString(),
                recipient_id: '+919876543210'
            };

            const deliveredResult = await syncService.processStatusUpdates('webhook-delivered-123', [deliveredStatus]);
            expect(deliveredResult.successfulUpdates).toBe(1);

            // Step 4: Simulate READ webhook
            const readStatus: WhatsAppStatus = {
                id: 'wamid.integration_test_123',
                status: 'read',
                timestamp: (Date.now() + 10000).toString(),
                recipient_id: '+919876543210'
            };

            const readResult = await syncService.processStatusUpdates('webhook-read-123', [readStatus]);
            expect(readResult.successfulUpdates).toBe(1);

            // Step 5: Verify final notification state
            const finalNotification = await notificationService.getNotificationLog(
                campaignNotification.notificationId,
                campaignNotification.timestamp
            );

            expect(finalNotification?.status).toBe(NotificationStatus.READ);
            expect(finalNotification?.whatsappStatus).toBe(NotificationStatus.READ);
            expect(finalNotification?.sentAt).toBeDefined();
            expect(finalNotification?.deliveredAt).toBeDefined();
            expect(finalNotification?.readAt).toBeDefined();
            expect(finalNotification?.sentAtISO).toBeDefined();
            expect(finalNotification?.deliveredAtISO).toBeDefined();
            expect(finalNotification?.readAtISO).toBeDefined();
        });

        test('should handle failed message workflow', async () => {
            // Step 1: Create initial notification
            const notification = await notificationService.logWhatsAppNotification(
                'business-test',
                '+919876543210',
                '+919876543210',
                { templateName: 'test_template' },
                { messaging_product: 'whatsapp' },
                { messages: [{ id: 'wamid.failed_test_456' }] },
                NotificationStatus.SENT
            );

            // Update with WhatsApp message ID
            await notificationService.updateNotificationStatus(
                notification.notificationId,
                notification.timestamp,
                NotificationStatus.SENT,
                undefined,
                undefined,
                'wamid.failed_test_456'
            );

            // Step 2: Simulate FAILED webhook
            const failedStatus: WhatsAppStatus = {
                id: 'wamid.failed_test_456',
                status: 'failed',
                timestamp: Date.now().toString(),
                recipient_id: '+919876543210',
                errors: [{
                    code: 131000,
                    title: 'MESSAGE_UNDELIVERABLE',
                    message: 'Phone number not reachable'
                }]
            };

            const failedResult = await syncService.processStatusUpdates('webhook-failed-123', [failedStatus]);
            expect(failedResult.successfulUpdates).toBe(1);

            // Step 3: Verify failed notification state
            const finalNotification = await notificationService.getNotificationLog(
                notification.notificationId,
                notification.timestamp
            );

            expect(finalNotification?.status).toBe(NotificationStatus.FAILED);
            expect(finalNotification?.whatsappStatus).toBe(NotificationStatus.FAILED);
            expect(finalNotification?.failedAt).toBeDefined();
            expect(finalNotification?.failedAtISO).toBeDefined();
            expect(finalNotification?.errorMessage).toContain('Phone number not reachable');
        });
    });

    describe('Batch Webhook Processing', () => {
        test('should handle multiple status updates in batch', async () => {
            // Create multiple notifications
            const notifications = await Promise.all([
                notificationService.logCampaignNotification({
                    businessId: 'business-test',
                    campaignId: 'batch_test_campaign',
                    campaignName: 'Batch Test Campaign',
                    campaignType: CampaignType.MARKETING,
                    messageCategory: MessageCategory.GENERAL,
                    recipients: ['+919876543210'],
                    recipient: '+919876543210',
                    providerRequest: { messaging_product: 'whatsapp' },
                    status: NotificationStatus.SENT,
                    whatsappMessageId: 'wamid.batch_1'
                }),
                notificationService.logCampaignNotification({
                    businessId: 'business-test',
                    campaignId: 'batch_test_campaign',
                    campaignName: 'Batch Test Campaign',
                    campaignType: CampaignType.MARKETING,
                    messageCategory: MessageCategory.GENERAL,
                    recipients: ['+919876543211'],
                    recipient: '+919876543211',
                    providerRequest: { messaging_product: 'whatsapp' },
                    status: NotificationStatus.SENT,
                    whatsappMessageId: 'wamid.batch_2'
                }),
                notificationService.logCampaignNotification({
                    businessId: 'business-test',
                    campaignId: 'batch_test_campaign',
                    campaignName: 'Batch Test Campaign',
                    campaignType: CampaignType.MARKETING,
                    messageCategory: MessageCategory.GENERAL,
                    recipients: ['+919876543212'],
                    recipient: '+919876543212',
                    providerRequest: { messaging_product: 'whatsapp' },
                    status: NotificationStatus.SENT,
                    whatsappMessageId: 'wamid.batch_3'
                })
            ]);

            // Create batch status updates
            const statusUpdates: WhatsAppStatus[] = [
                {
                    id: 'wamid.batch_1',
                    status: 'delivered',
                    timestamp: Date.now().toString(),
                    recipient_id: '+919876543210'
                },
                {
                    id: 'wamid.batch_2',
                    status: 'delivered',
                    timestamp: Date.now().toString(),
                    recipient_id: '+919876543211'
                },
                {
                    id: 'wamid.batch_3',
                    status: 'failed',
                    timestamp: Date.now().toString(),
                    recipient_id: '+919876543212',
                    errors: [{
                        code: 131026,
                        title: 'RATE_LIMIT_EXCEEDED',
                        message: 'Rate limit exceeded'
                    }]
                }
            ];

            // Process batch updates
            const batchResult = await syncService.processStatusUpdates('webhook-batch-123', statusUpdates);

            expect(batchResult.totalUpdatesProcessed).toBe(3);
            expect(batchResult.successfulUpdates).toBe(3);
            expect(batchResult.failedUpdates).toBe(0);
            expect(batchResult.processedNotifications).toHaveLength(3);

            // Verify individual notification states
            const updatedNotifications = await Promise.all(
                notifications.map(n => 
                    notificationService.getNotificationLog(n.notificationId, n.timestamp)
                )
            );

            expect(updatedNotifications[0]?.status).toBe(NotificationStatus.DELIVERED);
            expect(updatedNotifications[1]?.status).toBe(NotificationStatus.DELIVERED);
            expect(updatedNotifications[2]?.status).toBe(NotificationStatus.FAILED);
            expect(updatedNotifications[2]?.errorMessage).toContain('Rate limit exceeded');
        });

        test('should handle partial batch failures gracefully', async () => {
            // Create valid notification
            const validNotification = await notificationService.logCampaignNotification({
                businessId: 'business-test',
                campaignId: 'partial_batch_test',
                campaignName: 'Partial Batch Test',
                campaignType: CampaignType.MARKETING,
                messageCategory: MessageCategory.GENERAL,
                recipients: ['+919876543210'],
                recipient: '+919876543210',
                providerRequest: { messaging_product: 'whatsapp' },
                status: NotificationStatus.SENT,
                whatsappMessageId: 'wamid.valid_batch'
            });

            // Create batch with valid and invalid updates
            const mixedUpdates: WhatsAppStatus[] = [
                {
                    id: 'wamid.valid_batch',
                    status: 'delivered',
                    timestamp: Date.now().toString(),
                    recipient_id: '+919876543210'
                },
                {
                    id: 'wamid.nonexistent',
                    status: 'delivered',
                    timestamp: Date.now().toString(),
                    recipient_id: '+919876543999'
                }
            ];

            const batchResult = await syncService.processStatusUpdates('webhook-mixed-123', mixedUpdates);

            expect(batchResult.totalUpdatesProcessed).toBe(2);
            expect(batchResult.successfulUpdates).toBe(1);
            expect(batchResult.failedUpdates).toBe(0);
            expect(batchResult.skippedUpdates).toBe(1);
            expect(batchResult.processedNotifications).toHaveLength(1);
            expect(batchResult.failedNotifications).toHaveLength(1);
            expect(batchResult.failedNotifications[0].whatsappMessageId).toBe('wamid.nonexistent');
        });
    });

    describe('Campaign Analytics Integration', () => {
        test('should update campaign analytics through webhook sync', async () => {
            const campaignId = 'analytics_integration_test';
            
            // Create multiple campaign notifications
            const notifications = await Promise.all([
                notificationService.logCampaignNotification({
                    businessId: 'business-test',
                    campaignId,
                    campaignName: 'Analytics Integration Test',
                    campaignType: CampaignType.MARKETING,
                    messageCategory: MessageCategory.GENERAL,
                    customerSegment: CustomerSegment.HIGH_VALUE,
                    recipients: ['+919876543210'],
                    recipient: '+919876543210',
                    providerRequest: { messaging_product: 'whatsapp' },
                    status: NotificationStatus.SENT,
                    whatsappMessageId: 'wamid.analytics_1'
                }),
                notificationService.logCampaignNotification({
                    businessId: 'business-test',
                    campaignId,
                    campaignName: 'Analytics Integration Test',
                    campaignType: CampaignType.MARKETING,
                    messageCategory: MessageCategory.GENERAL,
                    customerSegment: CustomerSegment.GENERAL,
                    recipients: ['+919876543211'],
                    recipient: '+919876543211',
                    providerRequest: { messaging_product: 'whatsapp' },
                    status: NotificationStatus.SENT,
                    whatsappMessageId: 'wamid.analytics_2'
                })
            ]);

            // Simulate different webhook outcomes
            await syncService.processStatusUpdates('webhook-analytics-1', [{
                id: 'wamid.analytics_1',
                status: 'read',
                timestamp: Date.now().toString(),
                recipient_id: '+919876543210'
            }]);

            await syncService.processStatusUpdates('webhook-analytics-2', [{
                id: 'wamid.analytics_2',
                status: 'delivered',
                timestamp: Date.now().toString(),
                recipient_id: '+919876543211'
            }]);

            // Get campaign analytics
            const analytics = await notificationService.getCampaignAnalytics(campaignId);

            expect(analytics.campaignId).toBe(campaignId);
            expect(analytics.totalMessages).toBe(2);
            expect(analytics.readCount).toBe(1);
            expect(analytics.deliveredCount).toBe(1);
            expect(analytics.readRate).toBe(100); // 1 read out of 1 delivered (readRate = read/delivered * 100)

            // Verify segment breakdown
            expect(analytics.segmentBreakdown).toHaveProperty(CustomerSegment.HIGH_VALUE);
            expect(analytics.segmentBreakdown).toHaveProperty(CustomerSegment.GENERAL);

            // Verify time series data includes ISO timestamps
            expect(analytics.timeSeriesData.length).toBeGreaterThan(0);
            expect(analytics.timeSeriesData[0]).toHaveProperty('timestampISO');
            expect(analytics.startDateISO).toBeDefined();
            expect(analytics.endDateISO).toBeDefined();
        });

        test('should track customer engagement across multiple campaigns', async () => {
            const customerNumber = '+919999888777';
            
            // Create notifications for same customer across different campaigns
            await Promise.all([
                notificationService.logCampaignNotification({
                    businessId: 'business-test',
                    campaignId: 'engagement_campaign_1',
                    campaignName: 'Engagement Test 1',
                    campaignType: CampaignType.PROMOTIONAL,
                    messageCategory: MessageCategory.PROMOTIONAL_OFFER,
                    customerSegment: CustomerSegment.HIGH_VALUE,
                    recipients: [customerNumber],
                    recipient: customerNumber,
                    providerRequest: { messaging_product: 'whatsapp' },
                    status: NotificationStatus.SENT,
                    whatsappMessageId: 'wamid.engagement_1'
                }),
                notificationService.logCampaignNotification({
                    businessId: 'business-test',
                    campaignId: 'engagement_campaign_2',
                    campaignName: 'Engagement Test 2',
                    campaignType: CampaignType.MARKETING,
                    messageCategory: MessageCategory.GENERAL,
                    customerSegment: CustomerSegment.HIGH_VALUE,
                    recipients: [customerNumber],
                    recipient: customerNumber,
                    providerRequest: { messaging_product: 'whatsapp' },
                    status: NotificationStatus.SENT,
                    whatsappMessageId: 'wamid.engagement_2'
                })
            ]);

            // Simulate webhook responses
            await syncService.processStatusUpdates('webhook-engagement-1', [{
                id: 'wamid.engagement_1',
                status: 'read',
                timestamp: Date.now().toString(),
                recipient_id: customerNumber
            }]);

            await syncService.processStatusUpdates('webhook-engagement-2', [{
                id: 'wamid.engagement_2',
                status: 'delivered',
                timestamp: Date.now().toString(),
                recipient_id: customerNumber
            }]);

            // Get customer engagement analytics
            const engagement = await notificationService.getCustomerEngagement(customerNumber);

            expect(engagement.customerId).toBe(customerNumber);
            expect(engagement.totalMessagesReceived).toBe(2);
            expect(engagement.totalMessagesDelivered).toBe(2);
            expect(engagement.totalMessagesRead).toBe(1);
            expect(engagement.deliveryRate).toBe(100);
            expect(engagement.readRate).toBe(50); // 1 read out of 2 delivered

            expect(engagement.campaignParticipation).toHaveLength(2);
            expect(engagement.campaignParticipation.some(c => c.campaignId === 'engagement_campaign_1')).toBe(true);
            expect(engagement.campaignParticipation.some(c => c.campaignId === 'engagement_campaign_2')).toBe(true);

            // Verify preferred categories
            expect(engagement.preferredMessageCategories).toContain(MessageCategory.PROMOTIONAL_OFFER);

            // Verify ISO timestamps
            expect(engagement.lastMessageTimestampISO).toBeDefined();
            expect(engagement.lastDeliveryTimestampISO).toBeDefined();
            expect(engagement.lastReadTimestampISO).toBeDefined();
        });
    });

    describe('Performance and Error Handling', () => {
        test('should handle webhook sync performance monitoring', async () => {
            const startTime = Date.now();
            
            // Create test notification
            const notification = await notificationService.logCampaignNotification({
                businessId: 'business-test',
                campaignId: 'performance_test',
                campaignName: 'Performance Test',
                campaignType: CampaignType.MARKETING,
                messageCategory: MessageCategory.GENERAL,
                recipients: ['+919876543210'],
                recipient: '+919876543210',
                providerRequest: { messaging_product: 'whatsapp' },
                status: NotificationStatus.SENT,
                whatsappMessageId: 'wamid.performance_test'
            });

            // Process webhook update
            const statusUpdate: WhatsAppStatus = {
                id: 'wamid.performance_test',
                status: 'delivered',
                timestamp: Date.now().toString(),
                recipient_id: '+919876543210'
            };

            const result = await syncService.processStatusUpdates('webhook-performance', [statusUpdate]);

            expect(result.successfulUpdates).toBe(1);
            expect(result.processingDuration).toBeGreaterThan(0);
            expect(typeof result.processingDuration).toBe('number');
            
            const endTime = Date.now();
            expect(result.processingDuration).toBeLessThan(endTime - startTime + 100); // Allow small buffer
        });

        test('should handle invalid webhook data gracefully', async () => {
            const invalidUpdate: WhatsAppStatus = {
                id: '', // Invalid empty message ID
                status: 'delivered',
                timestamp: Date.now().toString(),
                recipient_id: '+919876543210'
            };

            const result = await syncService.processStatusUpdates('webhook-invalid', [invalidUpdate]);

            expect(result.failedUpdates).toBe(1);
            expect(result.successfulUpdates).toBe(0);
        });

        test('should track sync metrics for batch operations', async () => {
            // Create notifications for batch test
            await Promise.all([
                notificationService.logCampaignNotification({
                    businessId: 'business-test',
                    campaignId: 'metrics_test',
                    campaignName: 'Metrics Test',
                    campaignType: CampaignType.MARKETING,
                    messageCategory: MessageCategory.GENERAL,
                    recipients: ['+919876543210'],
                    recipient: '+919876543210',
                    providerRequest: { messaging_product: 'whatsapp' },
                    status: NotificationStatus.SENT,
                    whatsappMessageId: 'wamid.metrics_1'
                }),
                notificationService.logCampaignNotification({
                    businessId: 'business-test',
                    campaignId: 'metrics_test',
                    campaignName: 'Metrics Test',
                    campaignType: CampaignType.MARKETING,
                    messageCategory: MessageCategory.GENERAL,
                    recipients: ['+919876543211'],
                    recipient: '+919876543211',
                    providerRequest: { messaging_product: 'whatsapp' },
                    status: NotificationStatus.SENT,
                    whatsappMessageId: 'wamid.metrics_2'
                })
            ]);

            const batchUpdates: WhatsAppStatus[] = [
                {
                    id: 'wamid.metrics_1',
                    status: 'delivered',
                    timestamp: Date.now().toString(),
                    recipient_id: '+919876543210'
                },
                {
                    id: 'wamid.metrics_2',
                    status: 'read',
                    timestamp: Date.now().toString(),
                    recipient_id: '+919876543211'
                }
            ];

            const batchResult = await syncService.processStatusUpdates('webhook-metrics', batchUpdates);

            expect(batchResult.syncId).toMatch(/^sync_\d+_[a-z0-9]+$/);
            expect(batchResult.totalUpdatesProcessed).toBe(2);
            expect(batchResult.successfulUpdates).toBe(2);
            expect(typeof batchResult.processingDuration).toBe('number');
            expect(typeof batchResult.averageUpdateTime).toBe('number');
            expect(batchResult.averageUpdateTime).toBe(batchResult.processingDuration / 2);
        });
    });

    describe('ISO Timestamp Verification', () => {
        test('should maintain consistent ISO timestamp format throughout sync process', async () => {
            // Create notification
            const notification = await notificationService.logCampaignNotification({
                businessId: 'business-test',
                campaignId: 'iso_test',
                campaignName: 'ISO Test',
                campaignType: CampaignType.MARKETING,
                messageCategory: MessageCategory.GENERAL,
                recipients: ['+919876543210'],
                recipient: '+919876543210',
                providerRequest: { messaging_product: 'whatsapp' },
                status: NotificationStatus.SENT,
                whatsappMessageId: 'wamid.iso_test'
            });

            // Verify initial ISO timestamps
            expect(notification.timestampISO).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
            expect(notification.lastUpdatedISO).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);

            // Process webhook update
            await syncService.processStatusUpdates('webhook-iso-test', [{
                id: 'wamid.iso_test',
                status: 'delivered',
                timestamp: Date.now().toString(),
                recipient_id: '+919876543210'
            }]);

            // Verify updated notification has proper ISO timestamps
            const updatedNotification = await notificationService.getNotificationLog(
                notification.notificationId,
                notification.timestamp
            );

            expect(updatedNotification?.deliveredAtISO).toBeDefined();
            expect(updatedNotification?.deliveredAtISO).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
            expect(updatedNotification?.lastUpdatedISO).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}\+05:30$/);
        });
    });
}); 