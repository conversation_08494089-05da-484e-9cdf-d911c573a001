import type { ApiResponse } from "../types/api/Api";
import type { Template, CustomerGroup, SearchParams, WaTemplateCtas } from "../schemas/marketing";
import type { SellerItem } from "../types/api/businessConsoleService/MyItemList";
import type { BuyerSummaryDetailsResponseItem } from "../types/api/businessConsoleService/BuyerSummaryDetailsResponseItem";
import { getSellerItems } from "./myItems";
import { getBuyerSummary } from "./businessConsoleService";
import { API_BASE_URL, apiRequest } from "../utils/api";

const waTemplateCtas: { [key: string]: WaTemplateCtas } = {
  "pay_dues": {
    id: 1,
    label: "Pay Now",
    value: "pay_dues",
    type: "quick_reply",
  },
  "order_history": {
    id: 2,
    label: "Request Details",
    value: "order_history",
    type: "quick_reply",
  },
  "help": {
    id: 3,
    label: "Contact Us",
    value: "help",
    type: "quick_reply",
  },
  "place_update_order": {
    id: 4,
    label: "Start Shopping",
    value: "place_update_order",
    type: "quick_reply",
  },
};

// Mock data for templates
const mockTemplates: Template[] = [
  {
    id: 1,
    templateId: "item_discount_trial_offer",
    name: "Discount Offer",
    header: "",
    content: `*🎉 Exclusive Offer Just for You!* Grab a limited-time deal on *{{Item_Name}}* now! 🎁 Save more while enjoying the best quality products. 🛒 Don't miss out—this offer ends soon!`,
    preview: `*🎉 Exclusive Offer Just for You!* \n\n Grab a limited-time deal on *{{Item_Name}}* now! \n🎁 Save more while enjoying the best quality products. \n🛒 Don't miss out—this offer ends soon!`,
    type: "WhatsApp",
    ctas: [waTemplateCtas.place_update_order],
    phone: "",
    variables: ["Item_Name"],
    lastUsed: "",
  },
  {
    id: 2,
    templateId: "early_pending_payment_special_offer",
    name: "Pending payment offer",
    header: "Settle balance and save!",
    content:`*🎉 Hi {{customer_name}},* \n\nYour account shows an outstanding balance of *₹{{pending_amount}}*. \nWe're offering an *exclusive discount* or priority services for settling this balance by *{{due_date}}* ! \n\n This balance reflects all partial payments made and outstanding amounts. Act now to unlock the benefits!`,
    preview:`*🎉 Hi {{customer_name}},* \n\nYour account shows an outstanding balance of *₹{{pending_amount}}*. \nWe're offering an *exclusive discount* or priority services for settling this balance by *{{due_date}}* ! \n\n This balance reflects all partial payments made and outstanding amounts. Act now to unlock the benefits!`,
    type: "WhatsApp",
    ctas: [waTemplateCtas.pay_dues, waTemplateCtas.order_history],
    phone: "",
    variables: ["customer_name", "pending_amount", "due_date"],
    lastUsed: "",
  },
  {
    id: 3,
    templateId: "pending_payment_reminder",
    name: "Pending payment reminder",
    header: "Outstanding balance: ₹{{pending_amount}}",
    content: `👋 Dear {{customer_name}}, \n\nWe'd like to remind you that your current outstanding balance with us is ₹{{pending_amount}} as of {{today_date}}.\n\nThis may include multiple orders, and we appreciate your partial payments made. To ensure your account remains in good standing, we kindly request you to settle the remaining amount.\n\nTap below to clear your balance or reach out for a detailed breakdown.`,
    preview: `👋 Dear {{customer_name}}, \n\nWe'd like to remind you that your current outstanding balance with us is ₹{{pending_amount}} as of {{today_date}}.\n\nThis may include multiple orders, and we appreciate your partial payments made. To ensure your account remains in good standing, we kindly request you to settle the remaining amount.\n\nTap below to clear your balance or reach out for a detailed breakdown.`,
    type: "WhatsApp",
    ctas: [waTemplateCtas.pay_dues, waTemplateCtas.order_history],
    phone: "",
    variables: ["customer_name", "pending_amount", "today_date"],
    lastUsed: "",
  },
  {
    id: 4,
    templateId: "open_for_ordering",
    name: "Open for ordering",
    header: "Fresh, Fast, and Affordable",
    content: `*🍎 Hello,*\n\nWe're open for orders! Explore our wide range of products: \n\n🍅 Fresh Fruits & Vegetables\n🥖 Daily Groceries & Staples\n🧴 Personal Care Products\n🧃 Beverages and more!\n\nShop today for premium quality and hassle-free delivery.`,
    type: "WhatsApp",
    ctas: [ waTemplateCtas.place_update_order, waTemplateCtas.help],
    phone: "",
    variables: [],
    lastUsed: "",
  },
];

export async function getTemplates(params: SearchParams): Promise<ApiResponse<Template[]>> {
  // Mock API call with search and pagination
  const filteredTemplates = params.search
    ? mockTemplates.filter(template => 
        (template.name?.toLowerCase().includes(params.search!.toLowerCase()) || false) 
      )
    : mockTemplates;

  return {
    data: filteredTemplates,
    statusCode: 200,
  };
}

export async function getCustomerGroups(params: SearchParams, request: Request): Promise<ApiResponse<CustomerGroup[]>> {
  const response = await apiRequest<CustomerGroup[]>(
    `${API_BASE_URL}/bc/seller/customer/groups`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch customer groups");
  }
}

export async function searchItems(params: SearchParams): Promise<ApiResponse<SellerItem[]>> {
  return getSellerItems(params.page - 1, params.pageSize);
}

export async function searchCustomers(params: SearchParams): Promise<ApiResponse<BuyerSummaryDetailsResponseItem[]>> {
  return getBuyerSummary(
    1, // userId - should come from auth context
    params.page,
    params.pageSize,
    "all", // tabValue
    "name", // sortBy
    params.search || "",
    "asc" // sortByOrder
  );
}

export async function sendMessage(data: {
  id:number;
  templateId: string;
  variables: Record<string, string>;
  selectedGroup: string;
  selectedItems?: number[];
  selectedCustomers?: number[];
}, request: Request): Promise<ApiResponse<void>> {
  const response = await apiRequest<void>(
    `${API_BASE_URL}/bc/seller/whatsapp/message-template`,
    'POST',
    data,
    {},
    true,
    request
  );

  if (!response) {
    throw new Error('Failed to send message');
  }
  
  return {
    statusCode: 200,
    data: undefined,
  };
} 