import { Seller<PERSON>reas } from "~/types/api/businessConsoleService/MasterItemCategory";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./table";
import { Switch } from "./switch";
import { useState } from "react";

interface sellerAreasProps {
      sellerAreaData: SellerAreas[],
      updateToggle: (status: boolean, areaId: number) => void
}

export default function SellerAreaList({ sellerAreaData, updateToggle }: sellerAreasProps) {
      const [toggledAreas, setToggledAreas] = useState<Set<number>>(new Set());

      const handleSwitch = (status: boolean, areaId: number) => {
            const updatedToggles = new Set(toggledAreas);
            if (status) {
                  updatedToggles.add(areaId);
            } else {
                  updatedToggles.delete(areaId);
            }
            setToggledAreas(updatedToggles);
            updateToggle(status, areaId);
      }

      return (
            <Table className="w-full min-w-[600px] border-collapse">
                  <TableHeader >
                        <TableRow className="bg-gray-100">
                              <TableHead className="font-bold text-gray-800">Area Name</TableHead>
                              <TableHead className="font-bold  text-gray-800">District</TableHead>
                              <TableHead className="font-bold  text-gray-800">State</TableHead>
                              <TableHead className="font-bold  text-gray-800">Status</TableHead>
                        </TableRow>
                  </TableHeader>
                  <TableBody>
                        {Array.isArray(sellerAreaData) && sellerAreaData.map((x) => {
                              const isToggled = toggledAreas.has(x?.sellerAreaId);
                              return (
                                    <TableRow key={x?.area?.id} className="bg-gray-50 font-medium">
                                          <TableCell >{x?.area?.name}</TableCell>
                                          <TableCell>{x?.area?.district}</TableCell>
                                          <TableCell>{x?.area?.state}</TableCell>
                                          <TableCell>
                                                <Switch
                                                      checked={isToggled || x?.disabled}
                                                      onClick={() => handleSwitch(!isToggled, x?.sellerAreaId)}
                                                />
                                          </TableCell>
                                    </TableRow>
                              )
                        })}
                  </TableBody>
            </Table>
      )
}
