import { WhatsAppMessageType, WhatsAppStatus, WhatsAppError } from './whatsapp-webhook.types.js';

export enum WebhookStatus {
    RECEIVED = 'RECEIVED',
    PROCESSING = 'PROCESSING',
    PROCESSED = 'PROCESSED',
    FAILED = 'FAILED',
    IGNORED = 'IGNORED'
}

export enum MessageType {
    TEXT = 'text',
    INTERACTIVE = 'interactive', 
    BUTTON = 'button',
    LOCATION = 'location',
    IMAGE = 'image',
    DOCUMENT = 'document',
    AUDIO = 'audio',
    VIDEO = 'video',
    STICKER = 'sticker',
    CONTACTS = 'contacts',
    ORDER = 'order',
    SYSTEM = 'system',
    STATUS_UPDATE = 'status_update',    // 🆕 WhatsApp message status updates
    ERROR_NOTIFICATION = 'error_notification', // 🆕 WhatsApp error notifications
    UNKNOWN = 'unknown'
}

export enum MessageDirection {
    INBOUND = 'INBOUND',
    OUTBOUND = 'OUTBOUND'
}

/**
 * WhatsApp message delivery status
 */
export enum WhatsAppMessageStatus {
    SENT = 'sent',
    DELIVERED = 'delivered',
    READ = 'read',
    FAILED = 'failed'
}

/**
 * Message codes for categorizing specific webhook types for easy searching
 */
export enum MessageCode {
    GREETING = 'GREETING',              // Greeting messages and say_hello interactions
    ORDER_STATUS = 'ORDER_STATUS',      // Order status inquiries
    FREE_GIFT_CLAIM = 'FREE_GIFT_CLAIM' // Free gift claim requests
}

export interface WebhookLogFilter {
    businessNumber?: string;
    customerNumber?: string;
    messageType?: MessageType;
    messageCode?: MessageCode;          // 🆕 Filter by message code
    whatsappStatus?: WhatsAppMessageStatus; // 🆕 Filter by WhatsApp message status
    status?: WebhookStatus;
    startTimestamp?: number;
    endTimestamp?: number;
    limit?: number;
    lastEvaluatedKey?: Record<string, any>;
}

export interface WebhookAnalytics {
    totalWebhooks: number;
    messageTypeBreakdown: Record<MessageType, number>;
    messageCodeBreakdown: Record<MessageCode, number>;  // 🆕 Analytics by message code
    whatsappStatusBreakdown: Record<WhatsAppMessageStatus, number>; // 🆕 WhatsApp status analytics
    statusBreakdown: Record<WebhookStatus, number>;
    averageProcessingTime: number;
    errorRate: number;
    peakHours: { hour: number; count: number }[];
    errorBreakdown: { code: number; count: number; title: string }[]; // 🆕 Error analytics
}

export interface WebhookLogResponse {
    webhookId: string;
    timestamp: number;
    timestampISO: string;                 // ISO timestamp in IST timezone
    businessNumber: string;
    customerNumber?: string;
    messageType: MessageType;
    messageCode?: MessageCode;            // 🆕 Message code for easy searching
    status: WebhookStatus;
    
    // 🆕 WhatsApp specific status and error tracking
    messageId?: string;                   // WhatsApp message ID (for messages and status updates)
    whatsappStatus?: WhatsAppMessageStatus; // Message delivery status
    whatsappErrors?: WhatsAppError[];    // WhatsApp error details
    
    receivedAt: number;
    receivedAtISO: string;                // ISO timestamp in IST timezone
    processedAt?: number;
    processedAtISO?: string;              // ISO timestamp in IST timezone
    errorMessage?: string;
    processingDuration?: number;
}

export interface BatchWebhookQuery {
    filters: WebhookLogFilter;
    sortBy?: 'timestamp' | 'processingDuration';
    sortOrder?: 'asc' | 'desc';
}

/**
 * Maps WhatsApp Cloud API message types to our internal MessageType enum
 */
export function mapWhatsAppMessageType(whatsappType: WhatsAppMessageType): MessageType {
    switch (whatsappType) {
        case 'text':
            return MessageType.TEXT;
        case 'interactive':
            return MessageType.INTERACTIVE;
        case 'button':
            return MessageType.BUTTON;
        case 'location':
            return MessageType.LOCATION;
        case 'image':
            return MessageType.IMAGE;
        case 'document':
            return MessageType.DOCUMENT;
        case 'audio':
            return MessageType.AUDIO;
        case 'video':
            return MessageType.VIDEO;
        case 'sticker':
            return MessageType.STICKER;
        case 'contacts':
            return MessageType.CONTACTS;
        case 'order':
            return MessageType.ORDER;
        case 'system':
            return MessageType.SYSTEM;
        default:
            return MessageType.UNKNOWN;
    }
}

/**
 * Maps WhatsApp status to our enum
 */
export function mapWhatsAppStatus(status: string): WhatsAppMessageStatus {
    switch (status) {
        case 'sent':
            return WhatsAppMessageStatus.SENT;
        case 'delivered':
            return WhatsAppMessageStatus.DELIVERED;
        case 'read':
            return WhatsAppMessageStatus.READ;
        case 'failed':
            return WhatsAppMessageStatus.FAILED;
        default:
            return WhatsAppMessageStatus.FAILED;
    }
}

/**
 * Type guard to check if a webhook contains valid message data
 */
export function hasValidMessage(payload: any): payload is { entry: Array<{ changes: Array<{ value: { messages: any[] } }> }> } {
    return payload?.entry?.[0]?.changes?.[0]?.value?.messages?.length > 0;
}

/**
 * Type guard to check if a webhook contains status updates
 */
export function hasStatusUpdates(payload: any): payload is { entry: Array<{ changes: Array<{ value: { statuses: any[] } }> }> } {
    return payload?.entry?.[0]?.changes?.[0]?.value?.statuses?.length > 0;
}

/**
 * Type guard to check if a webhook contains errors
 */
export function hasErrors(payload: any): payload is { entry: Array<{ changes: Array<{ value: { errors: any[] } }> }> } {
    return payload?.entry?.[0]?.changes?.[0]?.value?.errors?.length > 0;
}

/**
 * Extract WhatsApp status updates from webhook payload
 */
export function extractStatusUpdates(payload: any): WhatsAppStatus[] {
    if (!hasStatusUpdates(payload)) return [];
    return payload.entry[0].changes[0].value.statuses;
}

/**
 * Extract WhatsApp errors from webhook payload
 */
export function extractErrors(payload: any): WhatsAppError[] {
    if (!hasErrors(payload)) return [];
    return payload.entry[0].changes[0].value.errors;
} 