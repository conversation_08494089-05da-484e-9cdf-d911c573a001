import { LoaderFunction } from "@remix-run/node";
import { generateSessionToken } from "~/utils/jwt";
import { getSession } from "~/utils/session.server";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { useEffect, useRef, useState } from "react";
import { IframeData, ServerEnv } from "~/types";
import ErrorBoundaryComponent from "~/components/error/ErrorBoundary";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { Button } from "~/components/ui/button";
import ErrorModel from "~/components/error/Error";

type EventName = "PLACE_ORDER" | "IFRAME_LOADED" | "IFRAME_ERROR";

interface IframeMessage {
  type: EventName;
  payload: {
    success: boolean;
    message?: string;
  };
}

interface LoaderData {
  buyerId: number;
  token: string;
  iframeData: IframeData;
  serverEnv: ServerEnv;
}

export const loader: LoaderFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token");
  const refresh_token = session.get("refresh_token");
  const url = new URL(request.url);
  const mobileNumber = url.searchParams.get("mobileNumber");
  const buyerIdStr = url.searchParams.get("buyerId");

  if (!access_token || !refresh_token || !mobileNumber || !buyerIdStr) {
    throw Response.json(
      {},
      { status: 404, statusText: "Invalid Mobile or Buyer ID" }
    );
  }

  const buyerId = parseInt(buyerIdStr, 10);

  const data: IframeData = {
    buyerData: {
      buyerId,
      mobileNumber,
    },
    sellerData: {
      sellerId: 1,
      mobileNumber: "",
    },
  };

  const tokenForIframe = generateSessionToken({
    access_token,
    refresh_token,
    data,
  });

  return {
    buyerId,
    token: tokenForIframe,
    iframeData: data,
    serverEnv: process.env.SERVER_ENV as ServerEnv,
  };
};

export default function PlaceOrder() {
  const navigate = useNavigate();
  const [iframeLoading, setIframeLoading] = useState(true);
  const [error, setError] = useState("");
  const loader = useLoaderData<LoaderData>();
  const token = loader.token;
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      // Add listener for messages from the child
      const handleMessage = (event: MessageEvent) => {
        console.log("Message received from Child:", event.data);
        const message = event.data as IframeMessage;
        if (message.type === "PLACE_ORDER") {
          if (message.payload.success) {
            navigate(`/home/<USER>/${loader.buyerId}`);
          } else {
            setError("Unable to place order");
          }
        }
        if (message.type === "IFRAME_LOADED") {
          if (message.payload.success) {
            setIframeLoading(false);
          } else {
            setIframeLoading(false);
            setError("Something went wrong");
          }
        }

        if (message.type === "IFRAME_ERROR") {
          console.error("IFRAME Error: ", message.payload.message);
          setIframeLoading(false);
          setError(message.payload.message || "Something went wrong");
        }
      };

      window.addEventListener("message", handleMessage);

      // Cleanup
      return () => {
        window.removeEventListener("message", handleMessage);
      };
    }
  }, []);

  // const sendMessageToChild = () => {
  //   if (iframeRef.current?.contentWindow) {
  //     iframeRef.current.contentWindow.postMessage({ type: "GREETING", data: "Hello from Parent!" }, "*");
  //   }
  // };

  const handleCloseError = () => {
    setError("");
    navigate(-1);
  };

  if (!iframeLoading && error.length) {
    setIframeLoading(false);
    return (
      <ErrorModel
        title={"Oops"}
        message={error || "Something went wrong!"}
        onClose={handleCloseError}
        buttonType="primary"
        buttonText="Retry"
      />
    );
  }
  console.log(loader.serverEnv);
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 p-4 overflow-y-scroll z-50 h-full w-full">
      {iframeLoading && <SpinnerLoader loading={iframeLoading} />}
      <div className="flex flex-col min-h-[80%] min-w-[412px]">
        <div className="flex flex-row justify-between items-center bg-white w-full p-4 h-14 mb-2  rounded-md">
          <div className="text-lg font-semibold">
            {"Place order for"}
            <span className="text-teal-500 ml-4">
              {loader.iframeData.buyerData.mobileNumber}
            </span>
          </div>
          <Button
            onClick={() => navigate(-1)}
            className="bg-red-500 px-4 py-3 rounded-md text-lg hover:bg-red-600"
          >
            Cancel
          </Button>
        </div>
        <iframe
          ref={iframeRef}
          className="flex-grow overflow-y-scroll rounded-md w-full"
          title="chooseitems"
          src={`https://po${
            loader.serverEnv === "development" ? "-uat" : ""
          }.mnetlive.com/seller/chooseitems?token=${token}`}
          // style={{ width: '480px', height: '700px', border: 'none' }}
        />
      </div>
    </div>
  );
}

export function ErrorBoundary() {
  const navigate = useNavigate();
  return <ErrorBoundaryComponent onRetry={() => navigate(-1)} />;
}
