import {Request, Response, NextFunction } from "express";
import logger from '@utils/express-logger.js'

// Middleware for logging requests and responses
export function logRequestResponse(req: Request, res: Response, next: NextFunction): void {
    const startTime = process.hrtime(); // Capture start time for performance metrics
  
    // Attach the request ID to the request object for downstream access
  
    // Log the request details
    logger.info({
      timestamp: new Date().toISOString(),
      method: req.method,
      url: req.url,
      headers: req.headers,
      queryParams: req.query,
      body: req.body,
      message: 'Request Payload:',
    });
  
    // Capture response details
    const originalSend = res.send;
    const chunks: Buffer[] = [];
    res.send = function (...args: any[]) {
      const [chunk] = args; 
      if (chunk) {
        chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
      }
      // TODO: // debug originalSend.call(this, ...args)
      return originalSend.call(this, ...args);
    };
  
    // Log response details after it's sent
    res.on('finish', () => {
      const responseBody = Buffer.concat(chunks).toString('utf8');
      const diff = process.hrtime(startTime);
      const responseTime = (diff[0] * 1e9 + diff[1]) / 1e6; // in ms
  
      logger.info({
        timestamp: new Date().toISOString(),
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        headers: res.getHeaders(),
        responseBody,
        responseTime: `${responseTime.toFixed(2)}ms`,
        message: 'Response Payload: ',
      });
    });
  
    next();
  }