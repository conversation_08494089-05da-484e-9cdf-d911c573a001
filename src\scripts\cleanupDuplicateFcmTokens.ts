import { UserRepository } from '@repository/dynamoDB/user.repository.js';
import { User, FcmTokenDetails } from '@entity/dynamoDB/users.entity.js';
import logger from '@utils/express-logger.js';
import {config} from 'dotenv'

config();

class FcmTokenCleanupService {
    private readonly userRepository: UserRepository;
    private readonly BATCH_SIZE = 50; // Number of users to process in parallel
    private readonly TOKEN_BATCH_SIZE = 20; // Number of token operations to run in parallel

    constructor() {
        this.userRepository = new UserRepository('users');
    }

    private async processUserTokens(user: User): Promise<void> {
        if (!user.fcmTokens || user.fcmTokens.length === 0) {
            return;
        }

        try {
            // Group tokens by deviceId._j
            const tokenGroups = new Map<string, FcmTokenDetails[]>();
            user.fcmTokens.forEach(token => {
                if (token.deviceId?._j) {
                    const key = token.deviceId._j;
                    if (!tokenGroups.has(key)) {
                        tokenGroups.set(key, []);
                    }
                    tokenGroups.get(key)?.push(token);
                }
            });

            // For each group, keep only the most recent token
            const tokensToRemove: FcmTokenDetails[] = [];
            tokenGroups.forEach(tokens => {
                if (tokens.length > 1) {
                    // Sort by lastUpdated in descending order
                    tokens.sort((a, b) => b.lastUpdated - a.lastUpdated);
                    // Keep the first one (most recent), remove the rest
                    tokensToRemove.push(...tokens.slice(1));
                }
            });

            if (tokensToRemove.length > 0) {
                logger.info(`Found ${tokensToRemove.length} duplicate tokens for user ${user.mobileNumber}`);

                // Remove all tokens in one operation
                await this.userRepository.removeFcmTokens(user.mobileNumber, tokensToRemove);
                logger.info(`Successfully removed ${tokensToRemove.length} duplicate tokens for user ${user.mobileNumber}`);
            }
        } catch (error) {
            logger.error(`Error processing tokens for user ${user.mobileNumber}: ${error}`);
        }
    }

    public async cleanupDuplicateTokens(): Promise<void> {
        try {
            let lastEvaluatedKey: any = undefined;
            let totalUsersProcessed = 0;
            let totalTokensRemoved = 0;

            do {
                // Fetch users in batches
                const result = await this.userRepository.scanUsers(this.BATCH_SIZE, lastEvaluatedKey);
                const users = result.items;
                lastEvaluatedKey = result.lastEvaluatedKey;

                if (users.length > 0) {
                    logger.info(`Processing batch of ${users.length} users`);

                    // Process users in parallel
                    await Promise.all(
                        users.map((user: User) => this.processUserTokens(user))
                    );

                    totalUsersProcessed += users.length;
                    logger.info(`Processed ${totalUsersProcessed} users so far`);
                }

            } while (lastEvaluatedKey);

            logger.info(`Cleanup completed. Processed ${totalUsersProcessed} users in total.`);

        } catch (error) {
            logger.error(`Error during cleanup: ${error}`);
            throw error;
        }
    }
}

// Script execution
async function main() {
    const cleanupService = new FcmTokenCleanupService();
    logger.info('Starting FCM token cleanup process...');
    
    try {
        await cleanupService.cleanupDuplicateTokens();
        logger.info('Cleanup process completed successfully');
    } catch (error) {
        logger.error('Cleanup process failed:', error);
        process.exit(1);
    }
}

// Run the script if this is the main module
import { fileURLToPath } from 'url';
if (process.argv[1] === fileURLToPath(import.meta.url)) {
    main();
}

export default FcmTokenCleanupService; 