import { ItemStock, Transaction } from "../businessConsoleService/ItemStock";

export const myStockItems: ItemStock[] = [
  {
    id: "1",
    itemName: "Rice Basmati",
    distributerName: "Global Foods Inc.",
    inStock: 250,
    myPrice: 85.50,
    active: true,
    newOrders: 50,
    unit: "kg"
  },
  {
    id: "2",
    itemName: "Wheat Flour",
    distributerName: "Nature Mills Co.",
    inStock: 180,
    myPrice: 42.75,
    active: true,
    newOrders: 30,
    unit: "kg"
  },
  {
    id: "3",
    itemName: "Sugar",
    distributerName: "Sweet Supplies Ltd.",
    inStock: 120,
    myPrice: 32.25,
    active: false,
    newOrders: 0,
    unit: "kg"
  },
  {
    id: "4",
    itemName: "Cooking Oil",
    distributerName: "Golden Oils Corp.",
    inStock: 75,
    myPrice: 110.00,
    active: true,
    newOrders: 25,
    unit: "L"
  },
  {
    id: "5",
    itemName: "Salt",
    distributerName: "Mineral Essentials",
    inStock: 90,
    myPrice: 18.50,
    active: true,
    newOrders: 15,
    unit: "kg"
  },
  {
    id: "6",
    itemName: "Lentils",
    distributerName: "Pulse Traders",
    inStock: 200,
    myPrice: 65.00,
    active: true,
    newOrders: 40,
    unit: "kg"
  }
];

export const stockWithMeItems: ItemStock[] = [
  {
    id: "7",
    itemName: "Black Pepper",
    distributerName: "Spice World",
    inStock: 45,
    myPrice: 120.00,
    active: true,
    newOrders: 10,
    unit: "kg"
  },
  {
    id: "8",
    itemName: "Cumin Seeds",
    distributerName: "Spice World",
    inStock: 30,
    myPrice: 85.00,
    active: true,
    newOrders: 5,
    unit: "kg"
  },
  {
    id: "9",
    itemName: "Coriander Powder",
    distributerName: "Flavor Essentials",
    inStock: 40,
    myPrice: 75.00,
    active: false,
    newOrders: 0,
    unit: "kg"
  }
];

export const transactions: Transaction[] = [
  {
    id: "t1",
    itemId: "1",
    date: "2025-05-10",
    narration: "Initial stock purchase",
    receivedQty: 250,
    deliveredQty: 0,
    otherCredits: 0,
    otherDebits: 0,
    type: "instock"
  },
  {
    id: "t2",
    itemId: "1",
    date: "2025-05-15",
    narration: "Order #12345 - Customer: ABC Restaurant",
    receivedQty: 0,
    deliveredQty: 50,
    otherCredits: 0,
    otherDebits: 0,
    type: "outstock"
  },
  {
    id: "t3",
    itemId: "1",
    date: "2025-05-18",
    narration: "Restock from supplier",
    receivedQty: 100,
    deliveredQty: 0,
    otherCredits: 0,
    otherDebits: 0,
    type: "instock"
  },
  {
    id: "t4",
    itemId: "1",
    date: "2025-05-20",
    narration: "Damaged inventory adjustment",
    receivedQty: 0,
    deliveredQty: 5,
    otherCredits: 0,
    otherDebits: 5,
    type: "spoil"
  },
  {
    id: "t5",
    itemId: "1",
    date: "2025-05-22",
    narration: "Order #12389 - Customer: XYZ Catering",
    receivedQty: 0,
    deliveredQty: 45,
    otherCredits: 0,
    otherDebits: 0,
    type: "outstock"
  }
];

// Add transactions for other items
for (const item of [...myStockItems, ...stockWithMeItems]) {
  if (item.id !== "1") { // Skip item 1 as we already have transactions for it
    transactions.push(
      {
        id: `t${transactions.length + 1}`,
        itemId: item.id,
        date: "2025-05-01",
        narration: "Initial stock purchase",
        receivedQty: item.inStock + 20, // Initial amount was more
        deliveredQty: 0,
        otherCredits: 0,
        otherDebits: 0,
        type: "instock"
      },
      {
        id: `t${transactions.length + 2}`,
        itemId: item.id,
        date: "2025-05-12",
        narration: `Order #${12300 + parseInt(item.id)} - Customer: Sample Customer`,
        receivedQty: 0,
        deliveredQty: 20,
        otherCredits: 0,
        otherDebits: 0,
        type: "outstock"
      }
    );
  }
}
