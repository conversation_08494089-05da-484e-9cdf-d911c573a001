import { NotificationLog, NotificationStatus, CampaignType, MessageCategory, CustomerSegment } from '../database/entities/NotificationLog.js';

// 🆕 Campaign notification interfaces
export interface CampaignNotificationRequest {
    businessId: string;
    campaignId: string;
    campaignName: string;
    campaignType: CampaignType;
    messageCategory: MessageCategory;
    customerSegment?: CustomerSegment;
    tags?: string[];
    
    // Message details
    recipients: string[];              // List of phone numbers or device tokens
    templateName?: string;            // WhatsApp template name
    templateValues?: string[];        // Template parameter values
    messageContent?: string;          // Direct message content
    
    // Scheduling
    scheduleAt?: number;              // Unix timestamp for scheduled sending
    timezone?: string;                // Timezone for scheduling
    
    // Analytics tracking
    trackingEnabled?: boolean;        // Enable detailed analytics
    customMetadata?: Record<string, any>; // Custom data for analytics
}

export interface CampaignNotificationBatch {
    campaignId: string;
    campaignName: string;
    campaignType: CampaignType;
    messageCategory: MessageCategory;
    customerSegment: CustomerSegment;
    
    // Batch details
    batchId: string;
    totalRecipients: number;
    processedCount: number;
    successCount: number;
    failureCount: number;
    
    // Timing
    startedAt: number;
    completedAt?: number;
    estimatedCompletion?: number;
    
    // Status
    status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
    errors?: string[];
}

// 🆕 Analytics response interfaces
export interface CampaignAnalytics {
    campaignId: string;
    campaignName: string;
    campaignType: CampaignType;
    messageCategory: MessageCategory;
    
    // Overall metrics
    totalMessages: number;
    sentCount: number;
    deliveredCount: number;
    readCount: number;
    failedCount: number;
    
    // Rates (percentages)
    deliveryRate: number;
    readRate: number;
    failureRate: number;
    
    // Timing analytics
    averageDeliveryTime?: number;     // Average time to delivery in seconds
    averageReadTime?: number;         // Average time from delivery to read
    
    // Segment breakdown
    segmentBreakdown: {
        [segment in CustomerSegment]?: {
            count: number;
            deliveryRate: number;
            readRate: number;
        }
    };
    
    // Time series data (hourly breakdown)
    timeSeriesData: {
        timestamp: number;
        timestampISO: string;         // 🆕 IST ISO timestamp for readability
        sent: number;
        delivered: number;
        read: number;
        failed: number;
    }[];
    
    // Campaign period
    startDate: number;
    startDateISO: string;             // 🆕 IST ISO timestamp for readability
    endDate: number;
    endDateISO: string;               // 🆕 IST ISO timestamp for readability
    duration: number;                 // Campaign duration in seconds
}

export interface CustomerEngagementAnalytics {
    customerId: string;
    mobileNumber: string;
    customerSegment: CustomerSegment;
    
    // Overall engagement
    totalMessagesReceived: number;
    totalMessagesDelivered: number;
    totalMessagesRead: number;
    
    // Engagement rates
    deliveryRate: number;
    readRate: number;
    responseRate: number;             // If tracking responses
    
    // Campaign participation
    campaignParticipation: {
        campaignId: string;
        campaignName: string;
        campaignType: CampaignType;
        messageCategory: MessageCategory;
        participated: boolean;
        delivered: boolean;
        read: boolean;
        timestamp: number;
        timestampISO: string;         // 🆕 IST ISO timestamp for readability
    }[];
    
    // Preferences (inferred from behavior)
    preferredMessageCategories: MessageCategory[];
    bestEngagementTime?: {
        hour: number;                 // 0-23
        dayOfWeek: number;           // 0-6 (Sunday-Saturday)
    };
    
    // Last activity
    lastMessageTimestamp: number;
    lastMessageTimestampISO: string;  // 🆕 IST ISO timestamp for readability
    lastDeliveryTimestamp?: number;
    lastDeliveryTimestampISO?: string; // 🆕 IST ISO timestamp for readability
    lastReadTimestamp?: number;
    lastReadTimestampISO?: string;    // 🆕 IST ISO timestamp for readability
}

export interface NotificationAnalyticsSummary {
    businessId: string;
    period: {
        startDate: number;
        startDateISO: string;         // 🆕 IST ISO timestamp for readability
        endDate: number;
        endDateISO: string;           // 🆕 IST ISO timestamp for readability
        duration: number;
    };
    
    // Overall metrics
    totalNotifications: number;
    totalUniqueRecipients: number;
    
    // Status breakdown
    statusBreakdown: {
        [status in NotificationStatus]: number;
    };
    
    // Channel breakdown
    channelBreakdown: {
        whatsapp: number;
        firebase: number;
    };
    
    // Campaign breakdown
    campaignBreakdown: {
        [campaignType in CampaignType]: {
            count: number;
            deliveryRate: number;
            readRate: number;
        }
    };
    
    // Message category breakdown
    categoryBreakdown: {
        [category in MessageCategory]: {
            count: number;
            deliveryRate: number;
            readRate: number;
        }
    };
    
    // Customer segment breakdown
    segmentBreakdown: {
        [segment in CustomerSegment]: {
            count: number;
            deliveryRate: number;
            readRate: number;
        }
    };
    
    // Performance trends
    dailyTrends: {
        date: string;                 // YYYY-MM-DD format
        dateISO: string;              // 🆕 Full IST ISO timestamp for reference
        sent: number;
        delivered: number;
        read: number;
        failed: number;
    }[];
    
    // Top performing campaigns
    topCampaigns: {
        campaignId: string;
        campaignName: string;
        campaignType: CampaignType;
        totalMessages: number;
        deliveryRate: number;
        readRate: number;
    }[];
}

// 🆕 Webhook integration interfaces
export interface WebhookNotificationUpdate {
    whatsappMessageId: string;
    notificationId?: string;          // If already linked
    status: NotificationStatus;
    timestamp: number;
    
    // WhatsApp specific data
    recipientId: string;
    conversationId?: string;
    
    // Error information (if failed)
    error?: {
        code: number;
        title: string;
        message: string;
        errorData?: Record<string, any>;
    };
    
    // Pricing information (if available)
    pricing?: {
        billable: boolean;
        pricingModel: string;
        category: string;
    };
}

export interface NotificationSyncResult {
    syncId: string;
    timestamp: number;
    
    // Sync statistics
    totalUpdatesProcessed: number;
    successfulUpdates: number;
    failedUpdates: number;
    skippedUpdates: number;
    
    // Details
    processedNotifications: string[]; // Notification IDs that were updated
    failedNotifications: {
        notificationId?: string;
        whatsappMessageId: string;
        error: string;
    }[];
    
    // Performance
    processingDuration: number;       // In milliseconds
    averageUpdateTime: number;        // Average time per update
}

// 🆕 Query and filter interfaces
export interface NotificationQueryFilters {
    businessId?: string;
    campaignId?: string;
    campaignType?: CampaignType;
    messageCategory?: MessageCategory;
    customerSegment?: CustomerSegment;
    status?: NotificationStatus;
    whatsappStatus?: NotificationStatus;
    
    // Time range
    startTimestamp?: number;
    endTimestamp?: number;
    
    // Recipient filters
    mobileNumber?: string;
    recipientPattern?: string;        // Regex pattern for recipient matching
    
    // Metadata filters
    tags?: string[];                  // Messages must have all specified tags
    hasWebhookLog?: boolean;          // Filter by webhook log presence
    
    // Pagination
    limit?: number;
    lastEvaluatedKey?: Record<string, any>;
}

export interface BulkNotificationOperation {
    operationId: string;
    operationType: 'UPDATE_STATUS' | 'ADD_TAGS' | 'UPDATE_SEGMENT' | 'DELETE';
    
    // Filters for selecting notifications
    filters: NotificationQueryFilters;
    
    // Operation data
    updateData?: Partial<NotificationLog>;
    
    // Progress tracking
    status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
    totalItems?: number;
    processedItems?: number;
    failedItems?: number;
    
    // Timing
    startedAt: number;
    completedAt?: number;
    estimatedCompletion?: number;
    
    // Results
    errors?: string[];
    results?: {
        updated: string[];            // Notification IDs that were updated
        failed: string[];             // Notification IDs that failed to update
    };
}

// 🆕 Export interfaces for external systems
export interface NotificationExportRequest {
    businessId: string;
    exportFormat: 'CSV' | 'JSON' | 'EXCEL';
    filters: NotificationQueryFilters;
    
    // Export options
    includePayloads?: boolean;
    includeAnalytics?: boolean;
    groupByCampaign?: boolean;
    
    // Fields to include
    fields?: string[];
    
    // Delivery options
    emailTo?: string[];
    downloadUrl?: boolean;
}

export interface NotificationExportResult {
    exportId: string;
    requestId: string;
    status: 'PROCESSING' | 'COMPLETED' | 'FAILED';
    
    // File details
    fileName?: string;
    fileSize?: number;
    downloadUrl?: string;
    
    // Export statistics
    totalRecords: number;
    exportedRecords: number;
    
    // Timing
    requestedAt: number;
    completedAt?: number;
    expiresAt?: number;               // Download URL expiry
    
    // Error details
    error?: string;
} 