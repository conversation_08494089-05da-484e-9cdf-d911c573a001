import { Check, CirclePlus, Pencil } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, DialogTrigger } from "./dialog";
import { But<PERSON> } from "./button";
import { useState } from "react";
import { ItemsList, MasterItemCategories } from "~/types/api/businessConsoleService/MasterItemCategory";
import { CategoryItem, SearchableCategories } from "../masterItems/searchableCategories";




interface AddCategoryItemProps {
      categoryDetails?: MasterItemCategories[];
      buttonName: string;
      handleSubmit: (itemId: number, ParentCategoryIds: CategoryItem[], itemDetails: ItemsList, closeDialog: () => void) => void;
      mode?: string
      level?: number,
      itemDetails?: ItemsList

}
export default function AddCategoryItem({
      categoryDetails,
      buttonName,
      handleSubmit,
      mode,
      level,
      itemDetails

}: AddCategoryItemProps) {

      const [dialogOpen, setDialogOpen] = useState(false);

      const transformToCategoryItem = (parentCategories: MasterItemCategories[]): CategoryItem[] => {
            return parentCategories.map((category) => ({
                  value: category.id.toString(),
                  label: category.name,
                  numericId: category.id,
            }));
      };

      const [updatedCategory, setUpdatedCategory] = useState<CategoryItem[]>(() =>
            mode === "Edit" && categoryDetails ? transformToCategoryItem(categoryDetails) : []
      );

      function handleClose() {
            setDialogOpen(false);
      }

      const handleSubmitBtn = () => {

            const categoriesWithDetails = updatedCategory.map((category) => ({
                  id: category.numericId,
                  disabled: false,
                  name: category.label,
                  picture: itemDetails?.picture || "",
                  picturex: "",
                  picturexx: "",
                  level: level || 0,
                  totalItems: 1,
                  parentCategories: [],
            }));
            const requestBody = {
                  id: itemDetails?.id || 0,
                  disabled: false,
                  defaultUnit: itemDetails?.defaultUnit || "",
                  name: itemDetails?.name || "Default Item Name",
                  picture: itemDetails?.picture || "",
                  nameInKannada: itemDetails?.nameInKannada || "",
                  nameInTelugu: itemDetails?.nameInTelugu || "",
                  nameInTamil: itemDetails?.nameInTamil || "",
                  nameInMalayalam: itemDetails?.nameInMalayalam || "",
                  nameInHindi: itemDetails?.nameInHindi || "",
                  nameInAssame: itemDetails?.nameInAssame || "",
                  nameInGujarati: itemDetails?.nameInGujarati || "",
                  nameInMarathi: itemDetails?.nameInMarathi || "",
                  nameInBangla: itemDetails?.nameInBangla || "",
                  defaultWeightFactor: itemDetails?.defaultWeightFactor || 0,
                  gstHsnCode: itemDetails?.gstHsnCode || "",
                  gstRate: itemDetails?.gstRate || 0,
                  source: itemDetails?.source || "",
                  sourceKey: itemDetails?.sourceKey || "",
                  productId: itemDetails?.productId || "",
                  brandName: itemDetails?.brandName || "",
                  packaging: itemDetails?.packaging || "",
                  mrp: itemDetails?.mrp || 0,
                  ownerBId: itemDetails?.ownerBId || 0,
                  b2b: itemDetails?.b2b || false,
                  b2c: itemDetails?.b2c || false,
                  groupId: itemDetails?.groupId || "",
                  groupSeq: itemDetails?.groupSeq || 0,
                  searchTag: itemDetails?.searchTag || "",

                  categories: categoriesWithDetails,
            };




            handleSubmit(itemDetails?.id ?? 0, updatedCategory, requestBody, handleClose);

      }



      return (
            <div className="container mx-auto   ">
                  <Dialog open={dialogOpen} onOpenChange={(isOpen) => {
                        setDialogOpen(isOpen);
                        if (!isOpen) {
                              setDialogOpen(false);
                        }
                  }}>
                        <DialogTrigger asChild className="flex ">
                              <Pencil
                                    size={16}
                                    className="cursor-pointer"  >
                              </Pencil>
                        </DialogTrigger>
                        <DialogContent
                              className="w-full sm:w-[400px] md:w-[600px] max-h-[90vh] overflow-y-auto absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                        >
                              <div className="grid gap-4">
                                    <div className="space-y-2">
                                          <h4 className="font-medium leading-none text-center md:text-left">
                                                Edit Categories For Item
                                          </h4>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                          <div className="space-y-2">
                                                <img
                                                      src={
                                                            itemDetails?.picture && itemDetails.picture.includes(",")
                                                                  ? itemDetails.picture.split(",")[0] // Use the first image in the split array
                                                                  : itemDetails?.picture
                                                      }
                                                      alt=""
                                                      className="w-12 h-12 object-cover"
                                                />
                                                <p className="text-md font-bold">Item Name : {itemDetails?.name}  </p>
                                          </div>
                                    </div>
                              </div>
                              <div className="col-span-full">
                                    <SearchableCategories
                                          label="Assigned Categories"
                                          apiUrl="/home/<USER>"

                                          selectedCategories={updatedCategory || []}
                                          onCategoryAdd={(categoryId, categoryName) => {

                                                setUpdatedCategory((prevUpdatedCategory) => [
                                                      ...(prevUpdatedCategory || []),
                                                      { numericId: categoryId, value: categoryName, label: "" }
                                                ]);
                                          }}
                                          onCategoryRemove={(categoryId) => {

                                                setUpdatedCategory((prevUpdatedCategory) =>
                                                      (prevUpdatedCategory || []).filter(
                                                            (cat) => cat.numericId !== categoryId
                                                      )
                                                );
                                          }}
                                          required={true}
                                          level={1}

                                    />
                              </div>
                              <div className="flex flex-col md:flex-row justify-end gap-2 mt-5">
                                    <Button
                                          size="sm"
                                          className="w-full md:w-auto"

                                          onClick={() => handleSubmitBtn()}  >
                                          Submit
                                    </Button>

                              </div>
                        </DialogContent>
                  </Dialog>

            </div>
      );
}
