
import { IServiceResponse } from '@/interfaces/service.interface.js';
import logger from '@utils/express-logger.js'

import MnetApiGatewayService from './mnetApiGateway.service.js';


class WaReplyButtonHandlerService {
    private mnetApiGatewayService: MnetApiGatewayService;


    constructor() {
        this.mnetApiGatewayService = new MnetApiGatewayService();
    }

    async createSession(bMobile: string, cMobile: string): Promise<IServiceResponse<{ token: string }>> {
        try {
            // const token = uuidv4();
            // const expiryTime = Date.now() + 15 * 60 * 1000; // Expires in 15 minutes
            // const session: WaReplyButtonHandler = {
            //     bMobile,
            //     cMobile,
            //     token,
            //     expiryTime,
            //     expiryTimeISO: new Date(expiryTime).toISOString(),
            //     createdAt: Date.now(),
            //     createdAtISO: new Date().toISOString(),
            // };

            // await this.sessionRepository.createSession(session);
            // return {
            //     ok: true,
            //     data: { token },
            // };
            return {ok: true, data: { token: '1234' }};
        } catch (err) {
            const error = err as Error;
            logger.error(error);
            logger.error(`Error Creating Session:' ${JSON.stringify(error.message)}`);
            return { ok: false, err: `Error Creating Session:' ${JSON.stringify(error.message)}` };
        }
    }

  
}

export default WaReplyButtonHandlerService;
