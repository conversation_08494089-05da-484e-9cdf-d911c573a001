# Testing Documentation

## Overview

This project uses Jest as the primary testing framework with TypeScript support. The testing setup includes comprehensive mocks for browser APIs and Node.js globals to ensure tests run consistently across different environments.

## Test Configuration

### Jest Configuration (`jest.config.mjs`)

```javascript
{
  preset: 'ts-jest/presets/default-esm',
  testEnvironment: 'node',
  roots: ['<rootDir>/app'],
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/__tests__/**/*.tsx',
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx'
  ],
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      useESM: true,
      tsconfig: 'tsconfig.json'
    }]
  },
  moduleNameMapper: {
    '^~/(.*)$': '<rootDir>/app/$1',
    '^@components/(.*)$': '<rootDir>/app/components/$1',
    '^@utils/(.*)$': '<rootDir>/app/utils/$1',
    // ... other path mappings
  },
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  collectCoverageFrom: [
    'app/**/*.{ts,tsx}',
    '!app/**/*.d.ts',
    '!app/**/*.test.{ts,tsx}',
    '!app/**/*.spec.{ts,tsx}',
    '!app/**/__tests__/**',
    '!app/entry.client.tsx',
    '!app/entry.server.tsx',
    '!app/root.tsx'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  testTimeout: 10000,
  verbose: true,
  clearMocks: true,
  restoreMocks: true,
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
}
```

### Jest Setup (`jest.setup.js`)

The setup file provides comprehensive mocks for:
- Browser APIs (fetch, localStorage, sessionStorage, window, document)
- Node.js globals (Date.now, Math.random, performance)
- Web APIs (Headers, Request, Response, URLSearchParams)

## Available Test Scripts

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run only changed files
npm run test:cache

# Run specific test file
npm test -- path/to/test.ts

# Run tests with open handles detection
npm test -- --detectOpenHandles
```

## Cache Service Tests

### Test Structure

The cache service tests are organized into several categories:

1. **Basic Operations**
   - Set and get values
   - Check for non-existent keys
   - Verify key existence
   - Delete operations
   - Clear all entries

2. **Memory Management**
   - Max size enforcement
   - LRU eviction strategy
   - Statistics accuracy

3. **TTL and Expiry**
   - Time-based expiration
   - Custom TTL per item
   - Automatic cleanup

4. **Token Utilities**
   - JWT token parsing
   - User ID extraction
   - Cache key generation

5. **Advanced Features**
   - getOrSet functionality
   - Error handling
   - Type safety

### Example Test

```typescript
describe('CacheService', () => {
  let cache: CacheService<string>;
  
  beforeEach(() => {
    cache = new CacheService<string>({
      ttl: 1000,
      maxSize: 5,
      cleanupInterval: 500,
    });
  });

  afterEach(() => {
    cache.destroy();
  });

  test('should set and get value correctly', () => {
    cache.set('test-key', 'test-value');
    const result = cache.get('test-key');
    expect(result).toBe('test-value');
  });
});
```

## Test Best Practices

### 1. Proper Cleanup

Always clean up resources in `afterEach` or `afterAll`:

```typescript
afterEach(() => {
  cache.destroy();
});
```

### 2. Mock Management

Use Jest's built-in mock management:

```typescript
beforeEach(() => {
  jest.clearAllMocks();
});

afterEach(() => {
  jest.restoreAllMocks();
});
```

### 3. Async Testing

Handle async operations properly:

```typescript
test('should handle async operations', async () => {
  const result = await cache.getOrSet('key', async () => 'value');
  expect(result).toBe('value');
});
```

### 4. Error Testing

Test error conditions:

```typescript
test('should handle fetch errors', async () => {
  await expect(
    cache.getOrSet('error-key', async () => {
      throw new Error('API Error');
    })
  ).rejects.toThrow('API Error');
});
```

## Coverage Requirements

- **Statements**: 90%
- **Branches**: 85%
- **Functions**: 90%
- **Lines**: 90%

## Debugging Tests

### Common Issues

1. **Tests hanging**: Usually caused by unclosed intervals or timers
   ```bash
   npm test -- --detectOpenHandles
   ```

2. **Memory leaks**: Check for proper cleanup in `afterEach`
   ```bash
   npm test -- --detectLeaks
   ```

3. **Type errors**: Ensure TypeScript configuration is correct
   ```bash
   npm run typecheck
   ```

### Debug Mode

Run tests with verbose output:

```bash
npm test -- --verbose
```

## Integration Testing

For integration tests that require external services:

```typescript
describe('Integration Tests', () => {
  test('should work with real API', async () => {
    const cache = new CacheService<ApiResponse>();
    
    const result = await cache.getOrSet('api-data', async () => {
      const response = await fetch('https://api.example.com/data');
      return response.json();
    });
    
    expect(result).toBeDefined();
    cache.destroy();
  });
});
```

## Performance Testing

Test cache performance under load:

```typescript
test('should handle concurrent access', async () => {
  const promises = [];
  
  for (let i = 0; i < 100; i++) {
    promises.push(
      cache.getOrSet(`key-${i}`, async () => `value-${i}`)
    );
  }
  
  const results = await Promise.all(promises);
  expect(results).toHaveLength(100);
});
```

## Continuous Integration

### GitHub Actions Example

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - run: npm ci
      - run: npm run typecheck
      - run: npm test
      - run: npm run test:coverage
```

## Troubleshooting

### Jest Configuration Issues

1. **Module resolution errors**: Check `moduleNameMapper` configuration
2. **TypeScript errors**: Verify `tsconfig.json` includes test files
3. **ESM issues**: Ensure `useESM: true` in ts-jest config

### Test Environment Issues

1. **Browser API errors**: Check `jest.setup.js` mocks
2. **Node.js globals**: Verify global mocks are working
3. **Async operations**: Ensure proper cleanup in tests

### Performance Issues

1. **Slow tests**: Use `--maxWorkers=1` for debugging
2. **Memory issues**: Check for memory leaks with `--detectLeaks`
3. **Timeout issues**: Increase `testTimeout` in config

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [ts-jest Documentation](https://kulshekhar.github.io/ts-jest/)
- [Testing Best Practices](https://jestjs.io/docs/best-practices)
- [Mock Functions](https://jestjs.io/docs/mock-functions) 