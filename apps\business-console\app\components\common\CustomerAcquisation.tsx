import {
      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON>,
      <PERSON><PERSON>ianG<PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON>,
      <PERSON>
} from "recharts";
import { CustomerAcquisition } from "~/types/api/businessConsoleService/BuyerAccountingResponse";

interface CustomerAcquisitionProps {
      customerAcquisitionRate: CustomerAcquisition;
}

interface CustomTickProps {
      x?: number;
      y?: number;
      payload?: { value: string };
      index?: number;
      chartData?: { week: string; range: string; ordered: number; new: number; repeat: number }[];
}
const legendData = [
      { label: "Ordered", color: "#7987FF" },
      { label: "New", color: "#E697FF" },
      { label: "Repeat", color: "#FFA5CB" },
];


const CustomTick = ({
      x = 0,
      y = 0,
      payload,
      index = 0,
      chartData = []
}: CustomTickProps) => {
      const week = payload?.value || "";
      const range = chartData[index]?.range || "";
      return (
            <g transform={`translate(${x},${y})`}>
                  <text
                        x={0}
                        y={0}
                        dy={8}
                        textAnchor="middle"
                        fill="#6b7280"
                        fontSize={12}
                        className="font-medium"
                  >
                        {week}
                  </text>
                  <text
                        x={0}
                        y={20}
                        dy={8}
                        textAnchor="middle"
                        fill="#9ca3af"
                        fontSize={8}
                        className="font-light"
                  >
                        {range}
                  </text>
            </g>
      );
};
export default function CustomersAcquisition({ customerAcquisitionRate }: CustomerAcquisitionProps) {
      const chartData = (customerAcquisitionRate?.weeklyAcquisition || []).map((acquisition) => ({
            week: acquisition?.week || "N/A",
            range: acquisition?.dateRange || "",
            ordered: acquisition?.ordered || 0,
            new: acquisition?.newCustomers || 0,
            repeat: acquisition?.repeat || 0,
      }));

      const totalCustomers = customerAcquisitionRate?.totalCustomers;
      // const avgNewCustomers = customerAcquisitionRate?.weeklyAcquisition
      //       ? Math.round(
      //             customerAcquisitionRate.weeklyAcquisition.reduce((sum, week) => sum + (week.new || 0), 0) /
      //             customerAcquisitionRate.weeklyAcquisition.length
      //       )
      //       : 80;

      const avgNewCustomers = customerAcquisitionRate.newCustomers



      return (
            <div className="w-full my-3 bg-white rounded-xl shadow-lg p-6">
                  <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-gray-800">
                              Customer Acquisition
                        </h3>

                        <div className="flex items-baseline gap-4">
                              <p className="text-2xl font-bold text-purple-600">
                                    {totalCustomers || ""}
                              </p>
                              <span className="text-sm text-gray-500">
                                    {avgNewCustomers} new customers per week
                              </span>
                        </div>

                        <div className="flex flex-col sm:flex-row gap-6 items-start">
                              <div className="w-full sm:w-2/3">
                                    <ResponsiveContainer width="100%" height={250}>
                                          <LineChart
                                                data={chartData}
                                                margin={{ top: 20, right: 20, bottom: 20, left: 0 }}
                                          >
                                                <CartesianGrid
                                                      vertical={false}
                                                      stroke="#e5e7eb"
                                                      strokeDasharray="3 3"
                                                />
                                                <XAxis
                                                      dataKey="week"
                                                      tickLine={false}
                                                      tickMargin={15}
                                                      axisLine={{ stroke: "#e5e7eb" }}
                                                      height={60}
                                                      tick={<CustomTick chartData={chartData} />}
                                                />
                                                <YAxis
                                                      dataKey="new"

                                                      allowDecimals={false}
                                                      tickLine={false}
                                                      axisLine={{ stroke: "#e5e7eb" }}
                                                      tickFormatter={(value) => `${value}`}
                                                      className="text-gray-600"
                                                />
                                                <Tooltip
                                                      formatter={(value: number, name: string) => [
                                                            value,
                                                            name.charAt(0).toUpperCase() + name.slice(1)
                                                      ]}
                                                      contentStyle={{
                                                            backgroundColor: "#fff",
                                                            borderRadius: "8px",
                                                            border: "1px solid #e5e7eb",
                                                            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                                                      }}
                                                />
                                                {legendData.map((item) => (
                                                      <Line
                                                            key={item.label}
                                                            dataKey={item.label.toLowerCase()}
                                                            type="monotone"
                                                            stroke={item.color}
                                                            strokeWidth={2}
                                                            dot={{ r: 4 }}
                                                            activeDot={{ r: 6 }}
                                                      />
                                                ))}
                                          </LineChart>
                                    </ResponsiveContainer>
                              </div>
                              <div className="flex  sm:flex-row md:flex-col gap-3 self-center">
                                    {legendData.map((item) => (
                                          <div key={item.label} className="flex items-center gap-2">
                                                <span
                                                      className="w-3 h-3 rounded-full"
                                                      style={{ backgroundColor: item.color }}
                                                />
                                                <span className="text-sm font-medium text-gray-700">
                                                      {item.label}
                                                </span>
                                          </div>
                                    ))}
                              </div>
                        </div>
                  </div>
            </div>
      );
}