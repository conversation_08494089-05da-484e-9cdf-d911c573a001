// Jest setup file
// This file runs before each test file

// Mock fetch globally
global.fetch = () => Promise.resolve({});

// Mock Date.now for consistent timestamps in tests
const mockDate = new Date('2024-01-01T00:00:00.000Z');
global.Date.now = () => mockDate.getTime();

// Mock localStorage and sessionStorage
const localStorageMock = {
  getItem: () => null,
  setItem: () => {},
  removeItem: () => {},
  clear: () => {},
};
global.localStorage = localStorageMock;
global.sessionStorage = localStorageMock;

// Mock window object for browser APIs
global.window = {
  location: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    pathname: '/',
    search: '',
  },
  history: {
    pushState: () => {},
    replaceState: () => {},
  },
  addEventListener: () => {},
  removeEventListener: () => {},
};

// Mock document object
global.document = {
  createElement: () => ({}),
  getElementById: () => null,
  querySelector: () => null,
  addEventListener: () => {},
  removeEventListener: () => {},
};

// Mock navigator
global.navigator = {
  userAgent: 'jest-test-agent',
  onLine: true,
};

// Mock URL constructor
global.URL = class URL {
  constructor(url) {
    this.href = url;
    this.origin = 'http://localhost:3000';
    this.pathname = '/';
    this.search = '';
    this.searchParams = {
      get: () => null,
      set: () => {},
      delete: () => {},
    };
  }
};

// Mock Headers
global.Headers = class Headers {
  constructor(init = {}) {
    this.headers = new Map(Object.entries(init));
  }
  
  get(name) {
    return this.headers.get(name) || null;
  }
  
  set(name, value) {
    this.headers.set(name, value);
  }
  
  has(name) {
    return this.headers.has(name);
  }
  
  delete(name) {
    this.headers.delete(name);
  }
  
  forEach(callback) {
    this.headers.forEach(callback);
  }
};

// Mock Request
global.Request = class Request {
  constructor(url, init = {}) {
    this.url = url;
    this.method = init.method || 'GET';
    this.headers = new Headers(init.headers);
    this.body = init.body;
  }
};

// Mock Response
global.Response = class Response {
  constructor(body, init = {}) {
    this.body = body;
    this.status = init.status || 200;
    this.statusText = init.statusText || 'OK';
    this.headers = new Headers(init.headers);
    this.ok = this.status >= 200 && this.status < 300;
  }
  
  async json() {
    return typeof this.body === 'string' ? JSON.parse(this.body) : this.body;
  }
  
  async text() {
    return typeof this.body === 'string' ? this.body : JSON.stringify(this.body);
  }
};

// Mock Math.random for deterministic tests
global.Math.random = () => 0.5;

// Mock performance.now
global.performance = {
  now: () => Date.now(),
};

// Mock URLSearchParams
global.URLSearchParams = class URLSearchParams {
  constructor(init = '') {
    this.params = new Map();
    if (typeof init === 'string') {
      init.split('&').forEach(pair => {
        const [key, value] = pair.split('=');
        if (key) this.params.set(key, value || '');
      });
    }
  }
  
  get(name) {
    return this.params.get(name) || null;
  }
  
  set(name, value) {
    this.params.set(name, value);
  }
  
  delete(name) {
    this.params.delete(name);
  }
  
  has(name) {
    return this.params.has(name);
  }
  
  forEach(callback) {
    this.params.forEach(callback);
  }
  
  toString() {
    return Array.from(this.params.entries())
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
  }
}; 