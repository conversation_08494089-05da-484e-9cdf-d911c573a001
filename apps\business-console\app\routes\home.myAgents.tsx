import { RefreshCcw } from "lucide-react"
import { useState } from "react"
import { Button } from "~/components/ui/button"
import { Input } from "~/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@components/ui/table"




export default function MyAgents() {
      const [searchTerm, setSearchTerm] = useState('')
      return (
            <div className="container mx-auto p-6 ">
                  <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold">My Agents</h1>
                        <div className="flex gap-2">


                              <Button variant="outline" size="icon">
                                    <RefreshCcw className="h-4 w-4" />
                              </Button>
                        </div>
                  </div>
                  <div className="flex justify-between mb-4">
                        <Input
                              placeholder="Search by name"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"
                        />
                  </div>
                  <Table >
                        <TableHeader>
                              <TableRow>
                                    <TableHead className="cursor-pointer">Id</TableHead>
                                    <TableHead className="cursor-pointer">Name</TableHead>
                                    <TableHead className="cursor-pointer">Mobile Number</TableHead>
                                    <TableHead className="cursor-pointer"> Manager</TableHead>
                                    <TableHead className="cursor-pointer"> Is Active</TableHead>
                              </TableRow>
                        </TableHeader>
                        <TableBody>
                              <TableRow>
                                    <TableCell>12312</TableCell>
                              </TableRow>
                        </TableBody>
                  </Table>


            </div>
      )
}