export interface MasterItemCategories {
  id: number;
  disabled: boolean;
  sequence: number;
  name: string;
  picture: string;
  picturex: string;
  picturexx: string;
  level: number;
  totalItems: number;
  ondcDomain: "RET10" | "RET11";
  parentCategories: MasterItemCategories[];
}
interface Owner {
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
  address: string;
  password: string;
  businessId: number;
  roles: string[];
}

export interface CreateSellerRequest {
  name: string;
  address: string;
  email: string;
  customerSupportNumber: string;
  owner: Owner;
  areaId: number;
  latitude: string;
  longitude: string;
  ondcDomain: "RET10" | "RET11";
  pincode: string;
  miSource: "mnet" | "rnet";
}

export interface CreateUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
  address: string;
  password: string;
  businessId: number;
  roles: string[];
}
export interface SellerItemCategories {
  sICId: number;
  itemCategoryId: number;
  name: string;
  picture: string;
  picturex: string;
  picturexx: string;
  level: number;
}
export interface Seller {
  id: number;
  name: string;
  enabled: boolean;
  businessId: number;
}
export interface SelectedSeller {
  id: number;
  name: string;
  enabled: boolean;
  auto_accept: boolean;
  auto_pack: boolean;
  auto_pickup: boolean;
  auto_dispatch: boolean;
  listing_seq: number;
  mininum_required_balance: number;
  allow_cod: boolean;
  minimum_order_qty: number;
  minimum_order_value: number;
  wa_enable: boolean;
  approx_pricing: boolean;
  categories_enabled: boolean;
  strikeoff_enabled: boolean;
  item_pick_enabled: boolean;
  contract_price_enabled: boolean;
  delivery_type: string;
  fav_items_enabled: boolean;
  category_level: number;
  business_id: number;
  dispatch_time: string;
  delivery_time: string;
  booking_close_time: number;
  booking_open_time: number;
  is_pay_later_enabled: boolean;
  auto_activate: boolean;
  advance_booking_days: number;
  miSources: string;
  t1Open: number;
  t1Close: number;
  t2Open: number;
  t2Close: number;
  t3Open: number;
  t3Close: number;
  menuId: string;
  pos: string;
  posCustId: string;
  outletId: number;
  ondcDomain: string;
  spCustId: string;
  spId: number;
  sourceSystem: string;


}
export interface Areas {
  id: number;
  name: string;
  district: string;
  state: string;
  polygon: string;
}

export interface SellerAreas {
  sellerAreaId: number;
  disabled: boolean;
  area: Areas;
}

export interface ItemsList {
  id: number;
  disabled: boolean;
  defaultUnit: string;
  name: string;
  picture: string;
  nameInKannada: string;
  nameInTelugu: string;
  nameInTamil: string;
  nameInMalayalam: string;
  nameInHindi: string;
  nameInAssame: string;
  nameInGujarati: string;
  nameInMarathi: string;
  nameInBangla: string;
  defaultWeightFactor: number;
  gstHsnCode: string;
  gstRate: number;
  source: string;
  sourceKey: string;
  productId: string;
  brandName: string;
  packaging: string;
  mrp: number;
  ownerBId: number;
  b2b: boolean;
  b2c: boolean;
  groupId: string;
  groupSeq: number;
  searchTag: string;
  minimumOrderQty: number;
  incrementOrderQty: number;
  maximumOrderQty: number;
  maxAvailableQty: number;
  categories: MasterItemCategories[];
}
