import { json, LoaderFunctionArgs, ActionFunctionArgs, redirect } from "@remix-run/node";
  import { useLoaderData, Form, useNavigation, useActionData, useFetcher } from "@remix-run/react";
  import { useEffect, useRef, useState } from "react";
  import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
  } from "~/components/ui/table";
  import { Badge } from "~/components/ui/badge";
  import { Button } from "~/components/ui/button";
  import { Card, CardHeader, CardContent, CardTitle } from "~/components/ui/card";
  import { Plus, Printer, User } from "lucide-react";
  import { createStockTransactions, getStockTransactions } from "~/services/stockservices/stockTransactions";
  import { CreateStockTransactionInput, InvStockTransaction, StockTransactionType } from "~/types/api/businessConsoleService/ItemStock";
  import { withResponse } from "~/utils/auth-utils";
  import AddEntryModal from "~/components/StockComponent/AddEntryModal";
import { toast } from "~/hooks/use-toast";
import SpinnerLoader from "~/components/loader/SpinnerLoader";





  interface ApiResponse<T> {
    data: T;
    status: number;
    message?: string;
  }

  interface LoaderData {
    transactions: InvStockTransaction[];
    unit: string;
    selectedDate: string;
    stockId: number;
    sellerId?: number;
  }

  interface ActionData {
    error?: string;
    success?: boolean;
    message?: string;
  }

  // Sample AddEntryModal component

  // Loader function to fetch transaction data
  export async function loader({ request}: LoaderFunctionArgs) {
    const url = new URL(request.url);
    const deliveryDate = url.searchParams.get("deliveryDate") || new Date().toISOString().split("T")[0];
    const unit = url.searchParams.get("unit") || "units";
    const SellerId= parseInt(url.searchParams.get("sellerId") as string||"0")
    const isSeller = SellerId==null || isNaN(SellerId) || SellerId === 0;
    const stockId = (typeof (arguments[0]?.params?.stockId) !== "undefined") ? parseInt(arguments[0].params.stockId) : 0; // Get stockId from params


    try {
      const response = await getStockTransactions(isSeller, stockId,SellerId, deliveryDate, request);
      return withResponse<LoaderData>({
        transactions: response.data || [],
        unit,
        selectedDate: deliveryDate,
        stockId,
        sellerId: SellerId,
      });
    } catch (error: any) {
      console.error("Error fetching transactions:", error);
      throw new Error("Failed to fetch transactions");
    }
  }

  // Action function to handle form submission
  export async function action({ request }: ActionFunctionArgs) {
    const formData = await request.formData();
    const intent = formData.get("intent");
    const sellerId = parseInt(formData.get("sellerId")?.toString() || "0");
    const isSeller = sellerId === 0 || isNaN(sellerId); // Determine if the user is a seller

    const selectedStockId = (typeof (arguments[0]?.params?.stockId) !== "undefined") ? parseInt(arguments[0].params.stockId) : 0; // Get stockId from params


    if (intent === "filter") {
      const deliveryDate = formData.get("deliveryDate")?.toString();
      if (!deliveryDate) {
        return json<ActionData>({ error: "Delivery date is required" }, { status: 400 });
      }
      return json<ActionData>({ success: true });
    }

    if (intent === "create") {
      const stockId = selectedStockId; // Replace with dynamic stockId
      const isSellectedSeller = isSeller; // Replace with dynamic isSeller
      const stockCreationbody: CreateStockTransactionInput = {
        stockTransactionType: formData.get("transactionType") as StockTransactionType,
        narration: formData.get("narration")?.toString() || "",
        quantity: Number(formData.get("quantity")) || 0,
        deliveryDate: formData.get("deliveryDate")?.toString() || "",
      };

      if (!stockCreationbody.stockTransactionType || !stockCreationbody.narration || stockCreationbody.quantity <= 0 || !stockCreationbody.deliveryDate) {
        return json<ActionData>({ error: "All fields are required and must be valid" }, { status: 400 });
      }
      
      try {
        const response =  await createStockTransactions(isSellectedSeller, stockId, sellerId,stockCreationbody, request);
        return  withResponse<ActionData>({
          success: true,
          message: "Transaction created successfully",

        },response.headers);
      }
      catch (error: any) {
        throw new Error("Failed to fetch transactions");
      }
    }

    return json<ActionData>({ error: "Invalid intent" }, { status: 400 });
  }

  export default function Transactions() {
    const { transactions, unit, selectedDate, stockId ,sellerId} = useLoaderData<LoaderData>();
    const navigation = useNavigation();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [date, setDate] = useState(selectedDate); 
    const fetcher = useFetcher();
    // const contentRef = useRef<HTMLDivElement>(null);
    // const reactToPrintFn = useReactToPrint({ contentRef });

    // useEffect(() => {
    //   const data = fetcher.data as ActionData | undefined;
    //   if (data?.success) {
    //     setIsModalOpen(false);
    //     toast({ description: data.message || "Transaction created successfully" });
    //   }
    //   if (data?.error) {
    //     toast({ description: data.error });
    //   }
    // }, [(fetcher.data as ActionData | undefined)?.success, (fetcher.data as ActionData | undefined)?.error]); 

    const getTypeColor = (type: string) => {
      switch (type) {
        case StockTransactionType.RECEIVED:
          return "bg-green-100 text-green-800";
        case StockTransactionType.DELIVERED:
          return "bg-blue-100 text-blue-800";
        case StockTransactionType.SPOILED:
          return "bg-red-100 text-red-800";
        case StockTransactionType.RETURNED:
          return "bg-amber-100 text-amber-800";
        case StockTransactionType.CORRECTION:
          return "bg-purple-100 text-purple-800";
        case StockTransactionType.CONVERTED:
          return "bg-indigo-100 text-indigo-800";
        default:
          return "bg-gray-100 text-gray-800";
      }
    };
    // const formatDate = (dateString: string) => {
    //   return new Date(dateString).toLocaleDateString("en-US", {
    //     year: "numeric",
    //     month: "short",
    //     day: "numeric",
    //   });
    // };
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(amount);
    };
    const handlePrint = () => {
      window.print();
    };
    const handleAddEntry = async (data: CreateStockTransactionInput) => {
      const formData = new FormData();
      formData.append("intent", "create");
      formData.append("transactionType", data.stockTransactionType);
      formData.append("narration", data.narration);
      formData.append("quantity", data.quantity.toString());
      formData.append("deliveryDate", data.deliveryDate);
      if (sellerId) {
        formData.append("sellerId", sellerId.toString());
      }
      fetcher.submit(formData, {method: "POST",
      });
    };
    const loading= fetcher.state !=="idle"

    return (
      <div className="space-y-6">
        {/* Header with Actions */}
        {loading && ( <SpinnerLoader loading={loading} />)}
        <div className="flex items-center justify-between no-print">
          <h2 className="text-2xl font-bold text-gray-900">Transaction History</h2>
          <div className="flex gap-3">
            <Form method="get" className="flex items-center gap-2">
              <input type="hidden" name="intent" value="filter" />
              {sellerId ? (
  <input type="hidden" name="sellerId" value={sellerId.toString()} />
) : null}  
              <input
                type="date"
                name="deliveryDate"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                className="border rounded-md p-2"
              />
              <Button
                type="submit"
                variant="outline"
                size="sm"
                disabled={navigation.state === "submitting" && navigation.formData?.get("intent") === "filter"}
              >
                {navigation.state === "submitting" && navigation.formData?.get("intent") === "filter" ? "Filtering..." : "Filter"}
              </Button>
            </Form>
            {/* <Button
              variant="outline"
              size="sm"
              onClick={() => setIsModalOpen(true)}
              className="flex items-center gap-2 hover:bg-primary hover:text-white transition-colors"
            >
              <Plus className="h-4 w-4" />
              Add Entry
            </Button> */}
          
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 hover:bg-gray-100 transition-colors"
              onClick={handlePrint}
              >
                <Printer className="h-4 w-4" />
                Print
              </Button>
          
        
          </div>
        </div>

        {/* Main Transaction Table */}
        <Card className="printable-area shadow-sm" >
          <CardContent className="p-0">
          <div className="overflow-x-auto max-h-[600px] print:overflow-visible print:max-h-none">
          <Table className="min-w-full ">
          <TableHeader className="sticky top-0 bg-white z-10 print:static">
                  <TableRow className=" hover:bg-gray-50  bg-white ">
                    <TableHead className="font-semibold text-gray-700 whitespace-nowrap">Type</TableHead>
                    <TableHead className="font-semibold text-gray-700 whitespace-nowrap">Narration</TableHead>
                    {/* <TableHead className="text-right font-semibold text-gray-700 whitespace-nowrap">Qty-U.Price</TableHead> */}
                    {/* <TableHead className="text-right font-semibold text-gray-700 whitespace-nowrap">U.Price</TableHead> */}
                    {/* <TableHead className="text-right font-semibold text-gray-700 whitespace-nowrap">T.Value</TableHead> */}
                    <TableHead className="text-right font-semibold text-gray-700 whitespace-nowrap">Balance</TableHead>
                    <TableHead className="text-right font-semibold text-gray-700 whitespace-nowrap">T(Rcvd-Del)</TableHead>
                    <TableHead className="text-right font-semibold text-gray-700 whitespace-nowrap">T(Ret-Spoil)</TableHead>
                    <TableHead className="text-right font-semibold text-gray-700 whitespace-nowrap">T(Conv-Corr)</TableHead>
                    <TableHead className="text-right font-semibold text-gray-700 whitespace-nowrap">ItemTotal</TableHead>
                    <TableHead className="text-right font-semibold text-gray-700 whitespace-nowrap">T.Dist.Charges</TableHead>
                    <TableHead className="text-right font-semibold text-gray-700 whitespace-nowrap">SalesComm</TableHead>
                    <TableHead className="text-right font-semibold text-gray-700 whitespace-nowrap">SupNetAmt</TableHead>
                    <TableHead className="font-semibold text-gray-700 whitespace-nowrap">User</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="overflow-y-auto">
                  {transactions.length > 0 ? (
                    transactions.map((transaction, index) => (
                      <TableRow
                        key={transaction.invStockTransactionId}
                        className={`hover:bg-gray-50 transition-colors ${
                          index % 2 === 0 ? "bg-white" : "bg-gray-25"
                        }`}
                      >
                        <TableCell>
                          <Badge
                            className={`${getTypeColor(transaction.transactionType)} border-0 font-medium transition-colors cursor-default`}
                          >
                            {transaction.transactionType}
                          </Badge>
                        </TableCell>
                        <TableCell className="max-w-[300px] break-words text-gray-700">
                          {transaction.narration}
                        </TableCell>
                        {/* <TableCell className="text-right font-medium flex gap-2 ">
                          {transaction.quantity > 0 ? (
                            <span className="text-green-600">
                              +{transaction.quantity} 
                            </span>
                          ) : transaction.quantity < 0 ? (
                            <span className="text-red-600">
                              {transaction.quantity} 
                            </span>
                          ) : (
                            <span className="text-gray-400"></span>
                          )}
                           {transaction.unitPrice > 0 ? (
                            <span className="font-medium text-gray-900">
                              {formatCurrency(transaction.unitPrice)}
                            </span>
                          ) : (
                            <span className="text-gray-400"></span>
                          )}
                        </TableCell> */}
                        {/* <TableCell className="text-right font-bold">
                          {transaction.totalValue > 0 ? (
                            <span className="text-gray-900">
                              {formatCurrency(transaction.totalValue)}
                            </span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell> */}
                        <TableCell className="text-right">
                          <span className="font-medium text-blue-600">
                            {transaction.balanceAfter||"-"}
                          </span>
                        </TableCell>
                        <TableCell className="text-sm text-gray-600 text-right">
                          {(transaction.totalReceived || transaction.totalDelivered) > 0
                            ? `${transaction.totalReceived || 0} - ${transaction.totalDelivered || 0}`
                            : "-"}
                        </TableCell>
                        <TableCell className="text-sm text-gray-600 text-right">
                          {(transaction.totalReturned || transaction.totalSpoiled) >0
                          ? `${transaction.totalReturned || 0} - ${transaction.totalSpoiled || 0}`
                            : "-"}
                          
                        </TableCell>
                        <TableCell className="text-sm text-gray-600 text-right">
                           {(transaction.totalConverted || transaction.totalCorrection) > 0
                            ? `${transaction.totalConverted || 0} - ${transaction.totalCorrection || 0}`
                            : "-"}
                        </TableCell>
                        <TableCell className="text-right font-bold">
                          {transaction.itemTotalAmount > 0 ? (
                            <span className="text-gray-900">
                              {formatCurrency(transaction.itemTotalAmount)}
                            </span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                       
                        <TableCell className="text-right font-bold">
                          {transaction.totalDistributionCharges > 0 ? (
                            <span className="text-gray-900">
                              {formatCurrency(transaction.totalDistributionCharges)}
                            </span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell className="text-right font-bold">
                          {transaction.totalSalesComm > 0 ? (
                            <span className="text-gray-900">
                              {`% ${transaction.totalSalesComm}`}
                            </span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell className="text-right font-bold">
                          {transaction.supplierNetAmount > 0 ? (
                            <span className="text-gray-900">
                              {formatCurrency(transaction.supplierNetAmount)}
                            </span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {transaction.username}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-12">
                        <div className="flex flex-col items-center gap-2">
                          <div className="text-gray-400 text-lg">📋</div>
                          <p className="text-gray-500 font-medium">No transactions found</p>
                          <p className="text-gray-400 text-sm">Add your first transaction to get started</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Add Entry Modal */}
        <AddEntryModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          itemId={stockId}
          onAddEntry={handleAddEntry}
        />
      </div>
    );
  }