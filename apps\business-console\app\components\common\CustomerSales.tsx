import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from "recharts";
import { SellerSales } from "~/types/api/businessConsoleService/BuyerAccountingResponse";
import { formatCurrency } from "~/utils/format";

interface CustomerSalesProps {
  sales: SellerSales;
}

interface CustomTickProps {
  x?: number;
  y?: number;
  payload?: { value: string };
  index?: number;
  chartData?: { week: string; range: string; sales: number }[];
}

const CustomTick = ({
  x = 0,
  y = 0,
  payload,
  index = 0,
  chartData = []
}: CustomTickProps) => {
  const week = payload?.value || "";
  const range = chartData[index]?.range || "";

  return (
    <g transform={`translate(${x},${y})`}>
      <text
        x={0}
        y={0}
        dy={8}
        textAnchor="middle"
        fill="#666"
        fontSize={12}
        className="font-medium"
      >
        {week}
      </text>
      <text
        x={0}
        y={20}
        dy={8}
        textAnchor="middle"
        fill="#999"
        fontSize={8}
        className="font-light"
      >
        {range}
      </text>
    </g>
  );
};

export default function CustomerSales({ sales }: CustomerSalesProps) {
  const chartData = (sales?.weeklySales || []).map((weekData) => ({
    week: weekData?.week || "N/A",
    range: weekData?.dateRange || "",
    sales: weekData?.totalDeliveredOrders || 0,
  }));

  return (
    <div className="w-full my-3 bg-white rounded-xl shadow-lg p-6  ">
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-gray-800">
          Sales Overview
        </h3>

        <div className="flex items-center gap-6">
          <div>
            <p className="text-2xl font-bold text-blue-600">
              {formatCurrency(sales?.totalRevenue || 0)}
            </p>
            <p className="text-sm text-gray-600">
              {sales?.totalOrders?.toLocaleString() || 0} Orders
            </p>
          </div>
        </div>
        <div className="mt-6 -mx-6"> {/* Negative margin to offset parent padding */}          <ResponsiveContainer width="100%" height={250}>
          <BarChart
            data={chartData}
            margin={{ top: 20, right: 20, bottom: 20, left: 0 }}
          >
            <CartesianGrid
              vertical={false}
              stroke="#e5e7eb"
              strokeDasharray="3 3"
            />
            <XAxis
              dataKey="week"
              tickLine={false}
              tickMargin={15}
              axisLine={{ stroke: "#e5e7eb" }}
              height={60}
              tick={<CustomTick chartData={chartData} />}
            />
            <YAxis
              dataKey="sales"
              allowDecimals={false}
              tickLine={false}
              axisLine={{ stroke: "#e5e7eb" }}
              tickFormatter={(value) => `${value}`}
              className="text-gray-600"
            />
            <Tooltip
              formatter={(value: number) => (value)}
              contentStyle={{
                backgroundColor: "#fff",
                borderRadius: "8px",
                border: "1px solid #e5e7eb",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              }}
            />
            <Bar
              dataKey="sales"
              fill="#6B8EF1"
              barSize={20}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}