import { useState } from "react"
import { Input } from "~/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table"


export default function SellerConfig() {
      const [searchTerm, setSearchTerm] = useState('')

      return (
            <div className="container mx-auto p-6">
                  <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold">Seller Config</h1>
                  </div>

                  <div className="flex justify-between mb-4">
                        <Input
                              placeholder="Search by name or owner"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"
                        />
                  </div>
                  <div className="rounded-md border">
                        <Table>
                              <TableHeader>
                                    <TableRow>
                                          <TableHead>ID</TableHead>
                                          <TableHead>Name</TableHead>
                                          <TableHead> Owner Mobile</TableHead>
                                          <TableHead>Enable</TableHead>
                                    </TableRow>
                              </TableHeader>
                              <TableBody>

                              </TableBody>
                              <TableRow>
                                    <TableCell
                                          colSpan={9}
                                          className="h-24 text-center"
                                    >
                                          No results.
                                    </TableCell>
                              </TableRow>
                        </Table>
                  </div>
            </div>
      )
}