export interface BuyerSummaryDetailsResponseItem {
  nBuyerId: number;
  buyerId: number;
  buyerName: string;
  ownerName: string;
  Address: string;
  mobileNumber: string;
  totalOrders: number;
  totalAmount: number;
  pendingAmount: number;
  lastOrderedDate: string;
  discount: number;
  fbDiscount: number;
  contractPriceExpDate: "29-12-2024" | null;
}

export interface OrderDetail {
  id: number;
  sellerName: string;
  deliveryDate: string;
  deliveryTime: string;
  estDeliveryTime: string;
  status: string;
  totalItemCount: number;
  deliveredItemCount: number;
  cancelledItemCount: number;
  totalWeight: number;
  totalOrderAmount: number;
  deliveryCharges: number;
  codAmount: number;
  discountAmount: number;
  totalAmount: number;
  isPending: boolean;
  farmers: Farmer[];
  delayPaymentPendingAmount: number;
  sellerId: number;
  deliveryCode?: string;
}

export interface Farmer {
  farmerId: number;
  farmerName: string;
  farmerRating: number;
  items: Item[];
}

export interface Item {
  orderId: number;
  itemName: string;
  itemUrl: string;
  qty: number;
  price: number;
  amount: number;
  status: string;
  unit: string;
  itemRegionalLanguageName?: string;
}
