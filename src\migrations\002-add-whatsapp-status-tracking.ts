/**
 * Migration 002: Add WhatsApp Status Tracking Index
 * 
 * This migration adds the WhatsAppStatusIndex to the existing webhook_logs table. 
 * The MessageCodeIndex should already exist from migration 001.
 * 
 * New Features:
 * - WhatsAppStatusIndex for efficient status-based queries
 * - Enhanced analytics with delivery tracking
 * - Backward compatibility with existing webhook data
 * 
 * Version: 2.1.0
 * Created: 2024-01-04
 * Dependencies: 001-create-webhook-log-table.ts
 */

import { 
    DynamoDBClient, 
    DescribeTableCommand, 
    UpdateTableCommand,
    GlobalSecondaryIndex 
} from '@aws-sdk/client-dynamodb';
import { KeyType, ScalarAttributeType, ProjectionType } from '@aws-sdk/client-dynamodb';
import dotenv from 'dotenv';

dotenv.config();

export const migrationInfo = {
    id: '002',
    name: 'Add WhatsApp Status Tracking Index',
    description: 'Adds WhatsAppStatusIndex for efficient WhatsApp delivery status queries',
    version: '2.1.0',
    dependencies: ['001'],
    createdAt: '2024-01-04T00:00:00.000Z'
};

const nodeEnv = process.env.SERVER_ENV === 'production' ? 'prod' : 'uat';
const webhookLogsTableName = `webhook_logs_${nodeEnv}`;

/**
 * WhatsApp Status Index for tracking delivery status
 */
const WhatsAppStatusIndex: GlobalSecondaryIndex = {
    IndexName: 'WhatsAppStatusIndex',
    KeySchema: [
        {
            AttributeName: 'whatsappStatus',
            KeyType: KeyType.HASH
        },
        {
            AttributeName: 'timestamp',
            KeyType: KeyType.RANGE
        }
    ],
    Projection: {
        ProjectionType: ProjectionType.ALL
    }
};

export async function up(): Promise<void> {
    const client = new DynamoDBClient({});
    
    try {
        console.log(`🚀 Migration ${migrationInfo.id}: ${migrationInfo.name}`);
        console.log(`🔧 Updating table: ${webhookLogsTableName}`);
        console.log('✨ New Features:');
        console.log('   - WhatsAppStatusIndex for efficient status queries');
        console.log('   - Enhanced analytics with delivery tracking');
        console.log('   - Full backward compatibility with existing data\n');
        
        // Check if table exists
        let tableDescription;
        try {
            const describeResult = await client.send(new DescribeTableCommand({
                TableName: webhookLogsTableName
            }));
            tableDescription = describeResult.Table;
            
            if (!tableDescription) {
                throw new Error(`Table ${webhookLogsTableName} not found`);
            }
            
            console.log(`📋 Current table status: ${tableDescription.TableStatus}`);
            console.log(`📋 Current GSI count: ${tableDescription.GlobalSecondaryIndexes?.length || 0}`);
            
        } catch (error: any) {
            if (error.name === 'ResourceNotFoundException') {
                throw new Error(`Table ${webhookLogsTableName} does not exist. Please run migration 001 first.`);
            }
            throw error;
        }

        // Check if WhatsAppStatusIndex already exists
        const existingIndexes = tableDescription.GlobalSecondaryIndexes || [];
        const whatsappStatusIndexExists = existingIndexes.some(
            index => index.IndexName === 'WhatsAppStatusIndex'
        );

        if (whatsappStatusIndexExists) {
            console.log('⚠️  WhatsAppStatusIndex already exists!');
            console.log('⏭️  Skipping migration - index already exists.');
            return;
        }

        // Check if whatsappStatus attribute is already defined
        const existingAttributes = tableDescription.AttributeDefinitions || [];
        const whatsappStatusAttributeExists = existingAttributes.some(
            attr => attr.AttributeName === 'whatsappStatus'
        );

        console.log('\n📝 Adding WhatsApp status tracking index...');
        
        // Prepare update command - include all existing attribute definitions plus the new one
        const updateCommand = new UpdateTableCommand({
            TableName: webhookLogsTableName,
            AttributeDefinitions: [
                // Existing attributes used by current indexes
                { AttributeName: 'webhookId', AttributeType: ScalarAttributeType.S },
                { AttributeName: 'timestamp', AttributeType: ScalarAttributeType.N },
                { AttributeName: 'businessNumber', AttributeType: ScalarAttributeType.S },
                { AttributeName: 'customerNumber', AttributeType: ScalarAttributeType.S },
                { AttributeName: 'messageType', AttributeType: ScalarAttributeType.S },
                { AttributeName: 'status', AttributeType: ScalarAttributeType.S },
                { AttributeName: 'messageCode', AttributeType: ScalarAttributeType.S },
                // New attribute for WhatsAppStatusIndex
                { AttributeName: 'whatsappStatus', AttributeType: ScalarAttributeType.S }
            ],
            GlobalSecondaryIndexUpdates: [
                {
                    Create: WhatsAppStatusIndex
                }
            ]
        });

        console.log('⏳ Creating WhatsAppStatusIndex...');
        const result = await client.send(updateCommand);
        
        console.log(`✅ Index creation initiated successfully!`);
        console.log(`   Table Status: ${result.TableDescription?.TableStatus}`);
        
        // Wait for index to become active
        console.log('\n⌛ Waiting for WhatsAppStatusIndex to become active...');
        let indexStatus = 'CREATING';
        let attempt = 0;
        const maxAttempts = 60; // 10 minutes max
        
        while (indexStatus === 'CREATING' && attempt < maxAttempts) {
            attempt++;
            await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
            
            const describeResult = await client.send(new DescribeTableCommand({
                TableName: webhookLogsTableName
            }));
            
            const indexes = describeResult.Table?.GlobalSecondaryIndexes || [];
            const whatsappIndex = indexes.find(index => index.IndexName === 'WhatsAppStatusIndex');
            indexStatus = whatsappIndex?.IndexStatus || 'UNKNOWN';
            
            console.log(`   Attempt ${attempt}: WhatsAppStatusIndex Status = ${indexStatus}`);
        }
        
        if (indexStatus === 'ACTIVE') {
            console.log('\n🎉 Migration completed successfully!');
            console.log('\n📊 Migration Summary:');
            console.log(`   ✅ Added WhatsAppStatusIndex`);
            
            if (!whatsappStatusAttributeExists) {
                console.log(`   ✅ Added whatsappStatus attribute definition`);
            }
            
            console.log('\n📋 New Capabilities:');
            console.log('   • Track WhatsApp message delivery status');
            console.log('   • Monitor error notifications with detailed codes');
            console.log('   • Query webhooks by delivery status efficiently');
            console.log('   • Generate delivery analytics and health metrics');
            console.log('\n💡 Usage:');
            console.log('   - Webhook logs now capture whatsappStatus field');
            console.log('   - Error webhooks populate whatsappErrors array');
            console.log('   - Use getWebhooksByWhatsAppStatus() for queries');
            console.log('   - Call getDeliveryStats() for analytics');
            
        } else {
            throw new Error(`Index creation timeout or failed. Final status: ${indexStatus}`);
        }
        
    } catch (error) {
        console.error(`\n❌ Migration ${migrationInfo.id} failed:`, error);
        throw error;
    } finally {
        client.destroy();
    }
}

export async function down(): Promise<void> {
    const client = new DynamoDBClient({});
    
    try {
        console.log(`🔄 Rolling back migration ${migrationInfo.id}: ${migrationInfo.name}`);
        console.log(`⚠️  This will remove WhatsApp status tracking capabilities`);
        
        // Remove the WhatsAppStatusIndex
        const updateCommand = new UpdateTableCommand({
            TableName: webhookLogsTableName,
            GlobalSecondaryIndexUpdates: [
                {
                    Delete: {
                        IndexName: 'WhatsAppStatusIndex'
                    }
                }
            ]
        });

        console.log('🗑️  Removing WhatsAppStatusIndex...');
        await client.send(updateCommand);
        
        console.log('✅ Migration rollback completed');
        console.log('⚠️  Note: Existing whatsappStatus and whatsappErrors data remains in records');
        
    } catch (error) {
        console.error(`\n❌ Migration rollback ${migrationInfo.id} failed:`, error);
        throw error;
    } finally {
        client.destroy();
    }
}

export async function validate(): Promise<boolean> {
    const client = new DynamoDBClient({});
    
    try {
        const describeResult = await client.send(new DescribeTableCommand({
            TableName: webhookLogsTableName
        }));
        
        const table = describeResult.Table;
        if (!table) {
            return false;
        }

        // Check if WhatsAppStatusIndex exists and is active
        const indexes = table.GlobalSecondaryIndexes || [];
        const whatsappIndex = indexes.find(index => index.IndexName === 'WhatsAppStatusIndex');
        
        const isValid = whatsappIndex?.IndexStatus === 'ACTIVE';
        
        if (isValid) {
            console.log(`✅ Migration ${migrationInfo.id} validation passed`);
            console.log(`   - WhatsAppStatusIndex is ACTIVE`);
            console.log(`   - Table has ${indexes.length} total indexes`);
        } else {
            console.log(`❌ Migration ${migrationInfo.id} validation failed`);
            console.log(`   - WhatsAppStatusIndex status: ${whatsappIndex?.IndexStatus || 'NOT_FOUND'}`);
        }
        
        return isValid;
        
    } catch (error) {
        console.error(`❌ Migration ${migrationInfo.id} validation error:`, error);
        return false;
    } finally {
        client.destroy();
    }
}

// Export for direct execution
if (import.meta.url === `file://${process.argv[1]}`) {
    const command = process.argv[2];
    
    switch (command) {
        case 'up':
            up().catch(console.error);
            break;
        case 'down':
            down().catch(console.error);
            break;
        case 'validate':
            validate().then(isValid => {
                process.exit(isValid ? 0 : 1);
            }).catch(() => {
                process.exit(1);
            });
            break;
        default:
            console.log('Usage: npx tsx src/migrations/002-add-whatsapp-status-tracking.ts [up|down|validate]');
            console.log('');
            console.log('Commands:');
            console.log('  up       - Apply the migration');
            console.log('  down     - Rollback the migration');
            console.log('  validate - Check if migration was applied correctly');
    }
} 