import { Loader2, Trash, Upload } from "lucide-react";
import { Button } from "./button";
import { useEffect, useRef, useState } from "react";
import { useToast } from "./ToastProvider";
import { useFetcher } from "@remix-run/react";
import { Label } from "./label";
import { Input } from "./input";

interface ImageUploadProps {
      onChange: (urls: string | string[]) => void;
      multiple?: boolean;
}

const ImageUploadComponent: React.FC<ImageUploadProps> = ({ onChange, multiple = false }) => {
      const fileInputRef = useRef<HTMLInputElement>(null);
      const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
      const [previewUrls, setPreviewUrls] = useState<string[]>([]);
      const [uploadError, setUploadError] = useState<string | null>(null);
      const [uploading, setUploading] = useState(false);
      const { showToast } = useToast();
      const uploadFetcher = useFetcher<{ success: boolean; fileUrl: string; error?: string }>();
      const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
            const files = Array.from(event.target.files || []);
            if (files.length === 0) return;
            setUploadError(null);
            const MAX_FILE_SIZE = 500 * 1024;
            const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
            const validFiles = files.filter((file) => {
                  if (file.size > MAX_FILE_SIZE) {
                        setUploadError("File size exceeds 500Kb limit.");
                        return false;
                  }
                  if (!allowedTypes.includes(file.type)) {
                        setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed.");
                        return false;
                  }
                  return true;
            });

            if (validFiles.length === 0) return;

            setSelectedFiles(multiple ? [...selectedFiles, ...validFiles] : [validFiles[0]]);
            setPreviewUrls(multiple ? [...previewUrls, ...validFiles.map((file) => URL.createObjectURL(file))] : [URL.createObjectURL(validFiles[0])]);
      };

      const handleUpload = () => {
            if (selectedFiles.length === 0) return;

            setUploading(true);
            const formData = new FormData();
            formData.append("_action", "uploadImage");

            selectedFiles.forEach((file) => {
                  formData.append("file", file, file.name);
            });

            uploadFetcher.submit(formData, {
                  method: "post",
                  action: "/home/<USER>",
                  encType: "multipart/form-data",
            });
      };

      useEffect(() => {
            if (uploadFetcher.data) {
                  if (uploadFetcher.data.error) {
                        setUploadError(uploadFetcher.data.error);
                  } else if (uploadFetcher.data.fileUrl) {
                        const uploadedUrls = multiple ? [...previewUrls, uploadFetcher.data.fileUrl] : [uploadFetcher.data.fileUrl];

                        onChange(multiple ? uploadedUrls : uploadFetcher.data.fileUrl);
                        // showToast("Image Uploaded To S3 Success", "success");

                        setSelectedFiles([]);
                        setPreviewUrls(uploadedUrls);
                        setUploadError(null);
                        if (fileInputRef.current) fileInputRef.current.value = "";
                  }
                  setUploading(false);
            }
      }, [uploadFetcher.data, onChange]);

      const handleRemoveImage = (index: number, e?: React.MouseEvent) => {
            e?.preventDefault();
            e?.stopPropagation();

            const updatedFiles = selectedFiles.filter((_, i) => i !== index);
            const updatedPreviews = previewUrls.filter((_, i) => i !== index);

            setSelectedFiles(updatedFiles);
            setPreviewUrls(updatedPreviews);

            onChange(multiple ? updatedPreviews : "");
      };

      return (
            <div className="p-6 border border-gray-200 rounded-xl shadow-lg bg-gradient-to-br from-gray-50 to-gray-100 transition-all duration-300 hover:shadow-xl">
                  <Label className="block text-lg font-semibold text-gray-800 mb-3">Upload Image</Label>

                  <div className="mt-4 flex flex-col items-center gap-6">
                        {/* Image Previews */}
                        {previewUrls.length > 0 && (
                              <div className="flex flex-wrap gap-4 justify-center">
                                    {previewUrls.map((url, index) => (
                                          <div
                                                key={index}
                                                className="relative w-44 h-44 group transition-transform duration-300 hover:scale-105"
                                          >
                                                <img
                                                      src={url}
                                                      alt="Selected Preview"
                                                      className="w-full h-full object-cover rounded-lg border border-gray-200 shadow-md"
                                                />
                                                <button
                                                      onClick={(e) => handleRemoveImage(index, e)}
                                                      className="absolute -top-3 -right-3 bg-red-600 text-white p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-700"
                                                >
                                                      <Trash className="h-5 w-5" />
                                                </button>
                                          </div>
                                    ))}
                              </div>
                        )}

                        {/* Input and Buttons */}
                        <div className="flex gap-3 items-center w-full max-w-lg">
                              <Input
                                    type="text"
                                    readOnly
                                    value={selectedFiles.length > 0 ? `${selectedFiles.length} file(s) selected` : "No file selected"}
                                    className="flex-grow cursor-pointer bg-gray-50 border-gray-200 rounded-lg px-4 py-2 text-gray-600 focus:ring-2 focus:ring-blue-300 transition-all duration-200"
                                    onClick={() => fileInputRef.current?.click()}
                              />
                              <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    multiple={multiple}
                                    onChange={handleFileSelect}
                                    className="hidden"
                              />
                              <Button
                                    type="button"
                                    onClick={selectedFiles.length > 0 ? handleUpload : () => fileInputRef.current?.click()}
                                    disabled={uploading}
                                    className={`flex items-center gap-2 ${uploading
                                          ? "bg-gray-400 cursor-not-allowed"
                                          : "bg-blue-600 hover:bg-blue-700"
                                          } text-white px-5 py-2 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg`}
                              >
                                    {uploading ? (
                                          <Loader2 className="h-5 w-5 animate-spin" />
                                    ) : (
                                          <Upload className="h-5 w-5" />
                                    )}
                                    {uploading ? "Uploading..." : selectedFiles.length > 0 ? "Upload" : "Add Image"}
                              </Button>
                        </div>

                        {/* Error Message */}
                        {uploadError && (
                              <p className="text-red-500 mt-2 text-sm font-medium animate-pulse">{uploadError}</p>
                        )}
                  </div>
            </div>
      );
};

export default ImageUploadComponent;