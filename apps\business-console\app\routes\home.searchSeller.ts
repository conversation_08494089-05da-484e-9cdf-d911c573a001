import { LoaderFunction } from "@remix-run/node";
import { getSellers } from "~/services/businessConsoleService";
import { getSearchFilters } from "~/services/tripsSummary";
import { withAuth, withResponse } from "~/utils/auth-utils";

export const loader: LoaderFunction = withAuth(async ({ request }) => {
  console.log("apicalled...........................");
  try {
    const response = await getSellers(request);

    return withResponse({ sellers: response?.data }, response?.headers);
  } catch (error) {
    console.error("Error fetching getSellers:", error);
    throw new Response("Failed to fetch getSellers", { status: 500 });
  }
});
