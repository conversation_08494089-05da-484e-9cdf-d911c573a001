import type React from "react"
import { useState, useMemo, useCallback } from "react"
import { GoogleMap, Marker, Circle, HeatmapLayer, useLoadScript, InfoWindow } from "@react-google-maps/api"
import { Card } from "~/components/ui/card"
import { Calendar, MapPin, ShoppingBag, Users } from "lucide-react"

interface Customer {
  id: string
  name: string
  lat: number
  lng: number
  orderCount?: number
  lastOrderDate?: string
}

interface Shop {
  id: string
  name: string
  lat: number
  lng: number
}

interface CustomerHeatMapProps {
  shop: Shop
  customers: Customer[]
  radiusKm: number
}

interface InfoWindowState {
  type: "customer" | "shop" | null
  data: Customer | Shop | null
  position: { lat: number; lng: number } | null
}

const googleMapsApiKey = "AIzaSyDBh6D6NIEiH08bj01ybByaayfM1T7W6XY"
const libraries: ("places" | "geometry" | "visualization")[] = ["places", "visualization"]

const CustomerHeatMap: React.FC<CustomerHeatMapProps> = ({ shop, customers, radiusKm }) => {
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: googleMapsApiKey,
    libraries: libraries,
  })
  const [isMapReady, setIsMapReady] = useState(false);
  const [infoWindow, setInfoWindow] = useState<InfoWindowState>({
    type: null,
    data: null,
    position: null,
  })
  const [zoom, setZoom] = useState<number>(13);

  const mapContainerStyle = {
    width: "100%",
    height: "600px",
    borderRadius: "12px",
  }

  const center = useMemo(
    () => ({
      lat: shop.lat,
      lng: shop.lng,
    }),
    [shop.lat, shop.lng],
  )

  // Handle map load
  const onMapLoad = useCallback((map: google.maps.Map) => {
    setTimeout(() => {
      setIsMapReady(true)
    }, 300)

    setZoom(map.getZoom() || 13);

    map.addListener("zoom_changed", () => {
      setZoom(map.getZoom() || 13);
    });
  }, [])

  // Create heatmap data
  const heatmapData = useMemo(() => {
    if (!isMapReady || !google.maps.LatLng) {
      return []
    }
    return customers.map((customer) => new google.maps.LatLng(customer.lat, customer.lng))
  }, [customers, isMapReady])

  // Handle customer marker click
  const handleCustomerClick = useCallback((customer: Customer) => {
    setInfoWindow({
      type: "customer",
      data: customer,
      position: { lat: customer.lat, lng: customer.lng },
    })
  }, [])

  // Handle shop marker click
  const handleShopClick = useCallback(() => {
    setInfoWindow({
      type: "shop",
      data: shop,
      position: { lat: shop.lat, lng: shop.lng },
    })
  }, [shop])

  // Handle InfoWindow close
  const handleInfoWindowClose = useCallback(() => {
    setInfoWindow({
      type: null,
      data: null,
      position: null,
    })
  }, [])

  if (!isLoaded) {
    return (
      <Card className="p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading Map...</p>
        </div>
      </Card>
    )
  }

  if (loadError) {
    return (
      <Card className="p-8">
        <div className="text-center text-red-500">
          <MapPin className="mx-auto mb-2 h-8 w-8" />
          <p>Error: {loadError.message}</p>
        </div>
      </Card>
    )
  }

  if (!shop || !shop.lat || !shop.lng) {
    return (
      <Card className="p-8">
        <div className="text-center text-red-500">
          <MapPin className="mx-auto mb-2 h-8 w-8" />
          <p>Error: Shop location data is missing</p>
        </div>
      </Card>
    )
  }

  if (!customers || customers.length === 0) {
    return (
      <Card className="p-8">
        <div className="text-center text-yellow-500">
          <Users className="mx-auto mb-2 h-8 w-8" />
          <p>No customer data available</p>
        </div>
      </Card>
    )
  }

  return (
    <div>
      <GoogleMap
        mapContainerStyle={mapContainerStyle}
        center={center}
        zoom={zoom}
        onLoad={onMapLoad}
        options={{
          streetViewControl: false
        }}
      >
        {isMapReady && (
          <>
            <Marker
              position={center}
              onClick={handleShopClick}
              options={{
                title: shop.name,
              }}
            />
            <Circle
              center={center}
              radius={radiusKm * 1000}
              options={{
                fillColor: "#da61f2",
                fillOpacity: 0,
                strokeColor: "#da61f2",
                strokeOpacity: 0.8,
                strokeWeight: 2,
              }}
            />
            {heatmapData.length > 0 && (
              <HeatmapLayer
                data={heatmapData}
                options={{
                  radius: 20,
                  opacity: 0.6,
                }}
              />
            )}

            {/* Invisible Customer Markers for Click Events */}
            {customers.map((customer) => (
              <Marker
                key={`customer-${customer.id}`}
                position={{ lat: customer.lat, lng: customer.lng }}
                onClick={() => handleCustomerClick(customer)}
                options={{
                  opacity: 0, // Make markers invisible
                  clickable: true,
                  title: customer.name,
                  icon: {
                    url: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGNgYAAAAAYAAjCB0C8AAAAASUVORK5CYII=", // 1x1 transparent PNG
                    scaledSize: new google.maps.Size(zoom * 2, zoom * 2),
                    anchor: new google.maps.Point(zoom, zoom),
                  },
                }}
              />
            ))}

            {/* InfoWindow */}
            {infoWindow.type && infoWindow.data && infoWindow.position && (
              <InfoWindow
                position={infoWindow.position}
                onCloseClick={handleInfoWindowClose}
                options={{
                  pixelOffset: new google.maps.Size(0, -10),
                }}
              >
                <div className="min-w-[200px]">
                  {infoWindow.type === "customer" ? (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Users className="h-4 w-4 text-blue-600" />
                        <h3 className="font-semibold text-lg">{(infoWindow.data as Customer).name}</h3>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <ShoppingBag className="h-3 w-3 text-gray-500" />
                          <span className="text-gray-600">Orders:</span>
                          <span className="font-medium">{(infoWindow.data as Customer).orderCount || 0}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-3 w-3 text-gray-500" />
                          <span className="text-gray-600">Last Order:</span>
                          <span className="font-medium">{(infoWindow.data as Customer).lastOrderDate || ""}</span>
                        </div>
                        <div className="text-xs text-gray-500 mt-2">
                          Customer ID: {(infoWindow.data as Customer).id}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <MapPin className="h-4 w-4 text-red-600" />
                        <h3 className="font-semibold text-lg">{(infoWindow.data as Shop).name}</h3>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="text-gray-600">Shop Location</div>
                        <div className="text-xs text-gray-500">Shop ID: {(infoWindow.data as Shop).id}</div>
                        <div className="text-xs text-gray-500">
                          Lat: {(infoWindow.data as Shop).lat.toFixed(6)}, Lng:{" "}
                          {(infoWindow.data as Shop).lng.toFixed(6)}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </InfoWindow>
            )}
          </>
        )}
      </GoogleMap>
    </div>
  )
}

export default CustomerHeatMap;
