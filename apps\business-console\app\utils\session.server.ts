// app/utils/session.server.ts

import { createCookieSessionStorage } from '@remix-run/node';

const sessionSecret = process.env.SESSION_SECRET;
if (!sessionSecret) {
    throw new Error('SESSION_SECRET must be set');
}

export const sessionStorage = createCookieSessionStorage({
    cookie: {
        name: '__session',
        secure: false, // Disable Secure flag for non-HTTPS
        secrets: [sessionSecret],
        sameSite: 'lax', // Keep SameSite as 'lax' for broader compatibility
        path: '/',
        maxAge: 60 * 60 * 24 * 7, // 7 days
        httpOnly: true,
    },
});

export const { getSession, commitSession, destroySession } = sessionStorage;
