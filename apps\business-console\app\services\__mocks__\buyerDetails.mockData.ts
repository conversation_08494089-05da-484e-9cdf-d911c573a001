import {BuyerDetailsResponse} from "~/types/api/businessConsoleService/BuyerDetailsResponse";

export const buyerDetailsMockData:BuyerDetailsResponse = {
    "buyerId": 507,
    "buyerName": "Day by day fresh ( GDP)",
    "ownerName": "Day by day fresh ( GDP) ",
    "Address": "Kodigehalli main road",
    "mobileNumber": "8880268030",
    "totalOrders": 1,
    "totalAmount": 1152,
    "pendingAmount": 0,
    "lastOrderedDate": "2024-10-11",
    "orderDetails": [
        {
            "id": 262,
            "sellerName": "Jetta Agro",
            "deliveryDate": "2024-10-12",
            "deliveryTime": "06:00:00",
            "estDeliveryTime": "06:00:00",
            "status": "Delivered",
            "totalItemCount": 1,
            "deliveredItemCount": 1,
            "cancelledItemCount": 0,
            "totalWeight": 36,
            "totalOrderAmount": 1152,
            "deliveryCharges": 0,
            "codAmount": 1152,
            "discountAmount": 0,
            "totalAmount": 1152,
            "isPending": false,
            "farmers": [
                {
                    "farmerId": 1,
                    "farmerName": "Jetta Agro",
                    "farmerRating": 4,
                    "items": [
                        {
                            "orderId": 456,
                            "itemName": "Banana Robusta Premium(18kg Crate)",
                            "itemUrl": "https://fm-assets.s3.ap-south-1.amazonaws.com/prod/items/rob%20new%20%281%29_1720943354124.png",
                            "qty": 36,
                            "price": 32,
                            "amount": 1152,
                            "status": "Delivered",
                            "unit": "KG"
                        }
                    ]
                }
            ],
            "delayPaymentPendingAmount": 0,
            "sellerId": 1
        }
    ]
}
