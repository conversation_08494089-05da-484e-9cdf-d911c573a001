import { Link, useLocation } from "@remix-run/react";
import { Tabs, TabsList, TabsTrigger } from "@components/ui/tabs";
import { cn } from "~/lib/utils";

interface Tab {
  label: string;
  value: string;
  path: string;
}

const tabs: Tab[] = [
  {
    label: "Campaigns",
    value: "campaigns",
    path: "/seller/marketing/campaigns",
  },
  {
    label: "Templates",
    value: "templates",
    path: "/seller/marketing/templates",
  },
  {
    label: "Analytics",
    value: "analytics",
    path: "/seller/marketing/analytics",
  },
];

export function MarketingTabs() {
  const location = useLocation();
  const currentTab = tabs.find((tab) => location.pathname.includes(tab.value))?.value || "templates";

  return (
    <Tabs value={currentTab} className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        {tabs.map((tab) => (
          <TabsTrigger
            key={tab.value}
            value={tab.value}
            className={cn(
              "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",
              "transition-colors"
            )}
            asChild
          >
            <Link to={tab.path}>{tab.label}</Link>
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
} 