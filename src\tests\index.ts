/**
 * Master Test Runner
 * Executes all webhook logging system tests
 */

import { runWebhookTypesTests } from './webhook-types.test.js';
import { runISTTimestampTests } from './ist-timestamps.test.js';

interface TestSuite {
    name: string;
    description: string;
    runner: () => Promise<void>;
}

const testSuites: TestSuite[] = [
    {
        name: "Webhook Types & Classification",
        description: "Type-safe WhatsApp webhook implementation based on Meta documentation",
        runner: runWebhookTypesTests
    },
    {
        name: "IST Timestamp Integration", 
        description: "Timezone support with real DynamoDB operations",
        runner: runISTTimestampTests
    }
];

export async function runAllTests() {
    console.log("🧪 Starting Webhook Logging System Test Suite\n");
    console.log("=".repeat(60));
    console.log("This test suite validates:");
    console.log("• Type-safe webhook processing");
    console.log("• Message classification (GREETING, ORDER_STATUS)");
    console.log("• IST timezone functionality");
    console.log("• DynamoDB integration");
    console.log("• Real webhook logging workflows");
    console.log("=".repeat(60));
    console.log("");
    
    let totalPassed = 0;
    let totalFailed = 0;
    const results: { suite: string; status: 'PASSED' | 'FAILED'; error?: string }[] = [];
    
    for (let i = 0; i < testSuites.length; i++) {
        const suite = testSuites[i];
        console.log(`\n📦 Test Suite ${i + 1}/${testSuites.length}: ${suite.name}`);
        console.log(`📄 ${suite.description}`);
        console.log("-".repeat(50));
        
        try {
            const startTime = Date.now();
            await suite.runner();
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            console.log(`✅ Suite "${suite.name}" PASSED (${duration}ms)`);
            results.push({ suite: suite.name, status: 'PASSED' });
            totalPassed++;
            
        } catch (error) {
            console.error(`❌ Suite "${suite.name}" FAILED:`, error);
            results.push({ 
                suite: suite.name, 
                status: 'FAILED', 
                error: error instanceof Error ? error.message : String(error)
            });
            totalFailed++;
        }
        
        console.log("-".repeat(50));
    }
    
    // Final summary
    console.log("\n" + "=".repeat(60));
    console.log("🏁 FINAL TEST RESULTS");
    console.log("=".repeat(60));
    
    results.forEach((result, index) => {
        const icon = result.status === 'PASSED' ? '✅' : '❌';
        console.log(`${icon} ${index + 1}. ${result.suite}: ${result.status}`);
        if (result.error) {
            console.log(`     Error: ${result.error}`);
        }
    });
    
    console.log("");
    console.log("📊 Summary:");
    console.log(`   ✅ Test Suites Passed: ${totalPassed}`);
    console.log(`   ❌ Test Suites Failed: ${totalFailed}`);
    console.log(`   📈 Success Rate: ${Math.round((totalPassed / testSuites.length) * 100)}%`);
    console.log("");
    
    if (totalFailed > 0) {
        console.log("❗ Some tests failed. Please check the error details above.");
        console.log("💡 Common solutions:");
        console.log("   • Ensure DynamoDB table is created: npm run create-webhook-table");
        console.log("   • Check AWS credentials and region configuration");
        console.log("   • Verify all dependencies are installed");
        console.log("");
        throw new Error(`${totalFailed} test suite(s) failed`);
    }
    
    console.log("🎉 All webhook logging system tests passed!");
    console.log("🚀 System is ready for production use!");
    console.log("");
}

// Health check function
export async function healthCheck() {
    console.log("🏥 Running Quick Health Check...\n");
    
    try {
        // Import and test basic functionality
        const { WebhookLogService } = await import('../services/webhookLog.service.js');
        
        // Test service instantiation
        const service = new WebhookLogService();
        if (!service) {
            throw new Error("WebhookLogService instantiation failed");
        }
        console.log("✅ WebhookLogService instantiation working");
        
        // Test basic types import
        const { MessageType } = await import('../types/webhook.types.js');
        if (!MessageType.TEXT) {
            throw new Error("Types import failed");
        }
        console.log("✅ Types import working");
        
        console.log("\n🎉 Health check passed! System is ready.");
        return true;
        
    } catch (error) {
        console.error("\n❌ Health check failed:", error);
        return false;
    }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const command = process.argv[2];
    
    if (command === 'health') {
        healthCheck()
            .then((success) => {
                process.exit(success ? 0 : 1);
            })
            .catch(() => {
                process.exit(1);
            });
    } else {
        runAllTests()
            .then(() => {
                console.log("✅ All tests completed successfully!");
                process.exit(0);
            })
            .catch((error) => {
                console.error("💥 Test suite failed:", error);
                process.exit(1);
            });
    }
} 