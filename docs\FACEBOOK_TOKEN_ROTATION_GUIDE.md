# Facebook Token Rotation Service

This service provides automated token rotation for Facebook system user access tokens used by sellers in the WhatsApp Business API integration.

## Overview

The Facebook Token Rotation Service automatically refreshes and rotates Facebook system user access tokens for all sellers in the `facebook-connects` collection. This ensures continuous access to WhatsApp Business API without manual intervention.

## Features

- 🔄 **Automatic Token Rotation**: Refreshes tokens for all sellers
- 🗑️ **Token Revocation**: Securely revokes old tokens after successful rotation
- 🔗 **Mnet Service Sync**: Automatically syncs rotated tokens with mnet service
- 📊 **Health Monitoring**: Provides health checks and status monitoring
- 🛡️ **Error Handling**: Comprehensive error handling and logging
- ⏰ **Scheduled Execution**: Runs every 15 days automatically via cron
- 🔍 **Manual Trigger**: Can be triggered manually via API
- 🏗️ **Centralized Architecture**: Follows project structure with centralized types and API calls

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Firebase      │    │   Facebook       │    │   Token         │
│   Collection    │◄──►│   Graph API      │◄──►│   Rotation      │
│                 │    │                  │    │   Service       │
└─────────────────┘    └──────────────────┘    └─────────┬───────┘
                                ▲                        │
                                │                        │
                    ┌──────────────────┐                 │
                    │   Centralized    │                 │
                    │   API Service    │                 │
                    │   (whatsapp.ts)  │                 │
                    └──────────────────┘                 │
                                                        │
                                              ┌─────────▼───────┐
                                              │   Mnet API      │
                                              │   Gateway       │
                                              │   Service       │
                                              └─────────────────┘
```

## Project Structure

```
src/
├── services/
│   ├── facebookTokenRotationService.ts  # Main token rotation service
│   ├── whatsappService.ts               # Centralized Facebook/Meta API calls
│   └── mnetApiGateway.service.ts        # Mnet API integration service
├── types/
│   └── whatsapp.ts                      # All WhatsApp and Facebook types
├── controllers/
│   └── facebookTokenRotationController.ts # API controller
└── routes/
    └── facebookTokenRotation.ts         # API routes
```

## Setup

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Facebook App Configuration
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# Firebase Configuration (Business Console)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_CLIENT_EMAIL=your_firebase_service_account_email
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# Mnet API Configuration
CORE_API_AUTH=your_mnet_api_auth_token
API_BASE_URL=http://localhost:6001
```

### Installation

The service is already integrated into the main application. No additional installation is required.

## Usage

### 1. Manual Execution

Run the token rotation script manually:

```bash
# Run token rotation for all sellers
npm run token-rotation

# Run health check
npm run token-rotation health

# Run tests
npm run test:token-rotation
```

### 2. API Endpoints

The service provides REST API endpoints for programmatic access:

#### Rotate Specific Seller Token
```http
POST /api/facebook/token-rotation/:sellerId
Content-Type: application/json
Authorization: Bearer YOUR_AUTH_TOKEN

{
  "revokeOldToken": true
}
```

#### Trigger Manual Rotation for All Expiring Tokens
```http
POST /api/facebook/token-rotation/trigger
Authorization: Bearer YOUR_AUTH_TOKEN
```

#### Health Check
```http
GET /api/facebook/token-rotation/health
Authorization: Bearer YOUR_AUTH_TOKEN
```

#### Get Cron Service Status
```http
GET /api/facebook/token-rotation/status
Authorization: Bearer YOUR_AUTH_TOKEN
```

### 3. Automated Execution

The service automatically runs every 15 days at 2:00 AM IST using `node-cron`:

- **Cron Expression**: `0 2 */15 * *`
- **Timezone**: Asia/Kolkata (IST)
- **Startup Check**: Runs immediately if tokens need rotation

## API Reference

### POST /api/facebook/token-rotation/:sellerId

Rotates token for a specific seller.

**Parameters:**
- `sellerId` (string): The seller ID

**Request Body:**
```json
{
  "revokeOldToken": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Token rotation completed successfully",
  "data": {
    "sellerId": "123",
    "success": true,
    "oldToken": "EAAB3rQQz...",
    "newToken": "EAAB3rQQz...",
    "expiresIn": 5183944,
    "mnetSyncSuccess": true
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### POST /api/facebook/token-rotation/trigger

Manually triggers token rotation for all sellers with expiring tokens.

**Response:**
```json
{
  "success": true,
  "message": "Manual token rotation completed successfully",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### GET /api/facebook/token-rotation/health

Returns the health status of the token rotation service.

**Response:**
```json
{
  "success": true,
  "data": {
    "service": "Facebook Token Rotation Service",
    "status": "healthy",
    "cronStatus": {
      "isRunning": true,
      "lastRunTime": "2024-01-15T02:00:00.000Z",
      "nextRunTime": "2024-01-30T02:00:00.000Z",
      "isCurrentlyExecuting": false,
      "cronExpression": "0 2 */15 * *"
    },
    "tokenRotationStatus": {
      "totalSellers": 25,
      "sellersNeedingRotation": 3,
      "mnetSyncStatus": {
        "sellersWithSyncStatus": 20,
        "sellersWithSuccessfulSync": 18,
        "sellersWithFailedSync": 2
      }
    },
    "environment": "production"
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### GET /api/facebook/token-rotation/status

Returns the cron service status.

**Response:**
```json
{
  "success": true,
  "data": {
    "service": "Facebook Token Rotation Cron Service",
    "status": "running",
    "lastRunTime": "2024-01-15T02:00:00.000Z",
    "nextRunTime": "2024-01-30T02:00:00.000Z",
    "isCurrentlyExecuting": false
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Error Handling

The service includes comprehensive error handling:

### Common Error Responses

**Invalid Seller ID:**
```json
{
  "success": false,
  "error": "Seller ID is required",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**Seller Not Found:**
```json
{
  "success": false,
  "error": "Seller 123 not found in facebook-connects collection",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**Token Refresh Failed:**
```json
{
  "success": false,
  "error": "Token refresh failed after all retry attempts",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Monitoring and Logging

### Log Levels

The service uses structured logging with the following levels:

- **INFO**: Normal operations, successful token rotations
- **WARN**: Non-critical issues (e.g., failed token revocation)
- **ERROR**: Critical failures, API errors

### Key Log Messages

```
🔄 Starting token rotation for seller: 123
✅ Token refreshed successfully
🔄 Syncing token with mnet service for seller 123
✅ Successfully synced token with mnet service for seller 123
✅ Updated sync status in Firebase for seller 123: success
🗑️ Successfully revoked old token for seller 123
✅ Successfully rotated and synced token for seller 123
🎉 Token rotation completed: 5 successful, 0 failed
⚠️ Token rotation is already running, skipping this execution
```

### Health Monitoring

Monitor the service health using:

```bash
# Check service health
curl http://localhost:3000/api/facebook/token-rotation/health \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN"

# Check cron status
curl http://localhost:3000/api/facebook/token-rotation/status \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN"
```

## Firebase Data Structure

### Mnet Sync Status

The service stores mnet sync status in the `facebook-connects` collection under each seller document:

```json
{
  "access": {
    "access_token": "EAAHaZBPu8IZBM...",
    "token_type": "bearer",
    "expires_in": 5183944
  },
  "businessName": "Example Business",
  "sellerId": 123,
  "mnetSyncStatus": {
    "lastSyncSuccess": true,
    "lastSyncAttempt": "2024-01-15T10:30:00.000Z",
    "lastSuccessfulSync": "2024-01-15T10:30:00.000Z"
  },
  "lastTokenRotation": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}
```

### Fields Explanation

- **`mnetSyncStatus.lastSyncSuccess`**: Boolean indicating if the last sync attempt was successful
- **`mnetSyncStatus.lastSyncAttempt`**: ISO timestamp of the last sync attempt (success or failure)
- **`mnetSyncStatus.lastSuccessfulSync`**: ISO timestamp of the last successful sync (only present if sync was successful)

## Security Considerations

### Token Security

- **App Secret Proof**: All API calls include HMAC SHA256 app secret proof
- **Token Revocation**: Old tokens are securely revoked after rotation
- **No Token Exposure**: Tokens are never logged or exposed in responses
- **Environment Variables**: Sensitive data stored in environment variables

### API Security

- **Authentication**: All endpoints require valid authentication token
- **Rate Limiting**: Built-in delays between API calls to respect rate limits
- **Timeout Handling**: 30-second timeout for token refresh, 15-second for revocation

## Troubleshooting

### Common Issues

**1. Service Not Starting**
```bash
# Check environment variables
echo $FACEBOOK_APP_ID
echo $FACEBOOK_APP_SECRET

# Check logs for initialization errors
```

**2. Token Refresh Failures**
```bash
# Check Facebook App permissions
# Verify app secret is correct
# Check if tokens are valid
```

**3. Cron Service Not Running**
```bash
# Check cron status
curl http://localhost:3000/api/facebook/token-rotation/status

# Restart the application
npm run dev
```

### Debug Mode

Enable debug logging by setting the log level:

```bash
LOG_LEVEL=debug npm run dev
```

## Development

### Running Tests

```bash
# Run all tests
npm run test:token-rotation

# Run specific test file
npm test src/tests/facebookTokenRotation.test.ts
```

### Adding New Features

1. **Types**: Add new types to `src/types/whatsapp.ts`
2. **API Calls**: Add new Facebook API methods to `src/services/whatsappService.ts`
3. **Service Logic**: Update `src/services/facebookTokenRotationService.ts`
4. **API Endpoints**: Add new routes in `src/routes/facebookTokenRotation.ts`

### Code Style

The service follows the existing project patterns:

- **Centralized Types**: All types in `src/types/whatsapp.ts`
- **Centralized API Calls**: All Facebook/Meta API calls in `src/services/whatsappService.ts`
- **Singleton Pattern**: Single service instance
- **Comprehensive Logging**: Structured logging with emojis
- **Error Handling**: Proper error responses and logging

## Support

For issues or questions:

1. Check the logs for error messages
2. Verify environment variables are set correctly
3. Test API endpoints manually
4. Review the health check endpoint for service status

The service is designed to be self-healing and will continue running even if individual token rotations fail. 

## Implementation Summary

### What Was Implemented

- Core token rotation flow in `src/services/facebookTokenRotationService.ts`
  - Token refresh via centralized Facebook API helpers
  - Firebase update for new tokens and sync status
  - Failure-safe mnet sync to `/wab/{bMobile}/c/{cMobile}/wab_token/rotate`
  - Optional old token revocation
- Centralized Facebook/Meta API calls in `src/services/whatsappService.ts`
  - `refreshFacebookSystemUserToken()` with retries and timeouts
  - `revokeFacebookSystemUserToken()`
- Mnet API gateway in `src/services/mnetApiGateway.service.ts`
  - `syncFacebookToken(bMobile, cMobile, token, expiryTime)` with structured logging
- Controller and routes
  - `src/controllers/facebookTokenRotationController.ts`
  - `src/routes/facebookTokenRotation.ts`
- Health monitoring and cron scheduling
  - `node-cron` for 15-day schedule at 2:00 AM IST
  - Health/status endpoints and structured logs

### Files Created/Modified

- Services
  - `src/services/facebookTokenRotationService.ts`
  - `src/services/whatsappService.ts`
  - `src/services/mnetApiGateway.service.ts`
- Types
  - `src/types/whatsapp.ts` (centralized interfaces and rotation types)
- API layer
  - `src/controllers/facebookTokenRotationController.ts`
  - `src/routes/facebookTokenRotation.ts`
- App integration
  - `src/index.ts` (cron startup)
  - `src/routes/index.ts` (route mounting)

### Key Benefits

- Centralized types and API calls
- Failure-safe rotation with mnet sync telemetry persisted in Firebase
- Production-grade scheduling with timezone support
- Strong observability: logs, health, and status metrics