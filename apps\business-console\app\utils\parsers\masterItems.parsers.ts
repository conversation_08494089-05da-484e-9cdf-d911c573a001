import type { MasterItemDto, FormState, MasterItemRequest } from "~/types/home/<USER>";

/**
 * Converts a MasterItemDto from the API to FormState for the form
 * Preserves all original data and transforms it into the format expected by the form
 */
export function masterItemDtoToFormState(dto: MasterItemDto): FormState {
  // Convert comma-separated picture string to images array
  const images = dto.picture?.split(',').map((url: string, index: number) => ({
    id: Date.now() + index,
    url: url.trim(),
    sequence: index + 1,
    isDefault: index === 0
  })) || [];

  // Convert comma-separated searchTag to array
  const searchTags = dto.searchTag?.split(',').map(tag => tag.trim()).filter(Boolean) || [];

  // Extract category IDs from the categories array
  const assignedCategories = (dto.categories || []).map(cat => cat.id);

  const validOndcDomain = (domain: string | undefined): "RET10" | "RET11" => {
    if (domain === "RET11") return "RET11";
    return "RET10"; // Default to "RET10" if not "RET11"
  };


  // Extract item configuration from DTO
  const itemConfig = {
    type: dto.b2b ? ("B2B" as const) : ("B2C" as const),
    unit: dto.defaultUnit,
    minimumOrderQty: dto.minimumOrderQty || 0,
    incrementOrderQty: dto.incrementOrderQty || 0,
    weightFactor: dto.defaultWeightFactor || 0.001,
    packaging: dto.packaging || "",
    mrpPerUnit: dto.mrp || 0,
    maximumOrderQty: dto.maximumOrderQty || 0,
    maxAvailableQty: dto.maxAvailableQty || 0,
    productId: dto.productId || "",
    originalProductId: dto.productId || "",
    isDefaultVariant: false,
    sequencePriority: "",
    gstEligible: dto.gstHsnCode ? ("yes" as const) : ("no" as const),
    gstHsnCode: dto.gstHsnCode || "",
    gstRate: dto.gstRate || 0,
    disabled: dto.disabled ?? false,
    ondcDomain: validOndcDomain(dto.ondcDomain),
    taxExempt: dto.taxExempt ?? false, // Optional in target type
    // Use validated value    taxExempt:dto.taxExempt||false
    description: dto.description || "",
    diet: dto.diet || "",
  };

  // Cast to FormState to ensure type safety
  const formState: FormState = {
    // Start with all original fields from DTO
    ...dto,
    // UI-specific transformations
    itemName: dto.name,
    images,
    searchTags,
    assignedCategories,
    groupId: dto.groupId || "",
    groupSeq: dto.groupSeq,
    translations: {
      kanadaName: dto.nameInKannada || "",
      hindiName: dto.nameInHindi || "",
      tamilName: dto.nameInTamil || "",
      teluguName: dto.nameInTelugu || "",
      bengaliName: dto.nameInBangla || "",
      malyalumName: dto.nameInMalayalam || "",
      marathiName: dto.nameInMarathi || "",
      gujaratiName: dto.nameInGujarati || "",
      assamiName: dto.nameInAssame || "",
    },
    itemConfig
  };

  return formState;
}

/**
 * Converts FormState back to MasterItemRequest for API submission
 * Only includes changed fields to minimize data transfer
 */
export function formStateToMasterItemRequest(
  formState: FormState,
  originalDto?: MasterItemDto
): MasterItemRequest {
  // Start with required fields
  const request: Partial<MasterItemRequest> = {
    // name: formState.itemName as string || originalDto?.name || "",
    // picture: formState.images.map(img => img.url).join(",") || originalDto?.picture || "",
    // defaultUnit: formState.defaultUnit || formState.itemConfig.unit || originalDto?.defaultUnit || "",
    // b2b: formState.itemConfig.type === "B2B",
    // b2c: formState.itemConfig.type === "B2C",
    ondcDomain: formState.itemConfig.ondcDomain,
    // taxExempt: formState.itemConfig.taxExempt || originalDto?.taxExempt || false,

    // mrp: formState.itemConfig.mrpPerUnit || originalDto?.mrp || 0,
    // Add itemConfig fields
    // minimumOrderQty: formState.itemConfig.minimumOrderQty,
    // incrementOrderQty: formState.itemConfig.incrementOrderQty,
    // maximumOrderQty: formState.itemConfig.maximumOrderQty,
    // maxAvailableQty: formState.itemConfig.maxAvailableQty,
    // disabled: formState.itemConfig.disabled,
    // productId: formState.itemConfig.productId || originalDto?.productId,
  };

  if (request.ondcDomain === "RET11") {
    request.b2b = false;
    request.b2c = true;
  } else {
    request.b2b = formState.itemConfig.type === "B2B";
    request.b2c = formState.itemConfig.type === "B2C";
  }

  // if(!originalDto?.b2b || originalDto.b2b !== formState.itemConfig.type === "B2B"){
  //   request.b2b = formState.itemConfig.type === "B2B" ? true : false;
  // }

  // if(!originalDto?.b2c || originalDto.b2c !== formState.itemConfig.type === "B2C"){
  //   request.b2c = formState.itemConfig.type === "B2C" ? true : false;
  // }

  if (!originalDto?.name || originalDto.name !== formState.itemName) {
    request.name = formState.itemName as string;
  }

  if (request.ondcDomain === "RET10" && (!originalDto?.brandName || originalDto.brandName !== formState.brandName)) {
    request.brandName = formState.brandName;
  }

  if (!originalDto?.groupId || originalDto.groupId !== formState.groupId) {
    request.groupId = formState.groupId;
  }

  if (!originalDto?.groupSeq || originalDto.groupSeq !== formState.groupSeq) {
    request.groupSeq = formState.groupSeq;
  }

  if (!originalDto?.source || originalDto.source !== formState.source) {
    request.source = formState.source ? formState.source : request.ondcDomain === "RET11" ? "rnet" : "mnet";
  }

  if (!originalDto?.sourceKey || originalDto.sourceKey !== formState.sourceKey) {
    request.sourceKey = formState.sourceKey as string || crypto.randomUUID();
  }

  if (!originalDto?.picture || originalDto.picture !== formState.images.map(img => img.url).join(",")) {
    request.picture = formState.images.map(img => img.url).join(",");
  }

  // Add translations only if they differ from original or are non-empty
  if (request.ondcDomain === "RET10") {
    if (!originalDto?.nameInKannada || originalDto.nameInKannada !== formState.translations.kanadaName) {
      request.nameInKannada = formState.translations.kanadaName;
    }
    if (!originalDto?.nameInHindi || originalDto.nameInHindi !== formState.translations.hindiName) {
      request.nameInHindi = formState.translations.hindiName;
    }
    if (!originalDto?.nameInTamil || originalDto.nameInTamil !== formState.translations.tamilName) {
      request.nameInTamil = formState.translations.tamilName;
    }
    if (!originalDto?.nameInTelugu || originalDto.nameInTelugu !== formState.translations.teluguName) {
      request.nameInTelugu = formState.translations.teluguName;
    }
    if (!originalDto?.nameInBangla || originalDto.nameInBangla !== formState.translations.bengaliName) {
      request.nameInBangla = formState.translations.bengaliName;
    }
    if (!originalDto?.nameInMalayalam || originalDto.nameInMalayalam !== formState.translations.malyalumName) {
      request.nameInMalayalam = formState.translations.malyalumName;
    }
    if (!originalDto?.nameInMarathi || originalDto.nameInMarathi !== formState.translations.marathiName) {
      request.nameInMarathi = formState.translations.marathiName;
    }
    if (!originalDto?.nameInGujarati || originalDto.nameInGujarati !== formState.translations.gujaratiName) {
      request.nameInGujarati = formState.translations.gujaratiName;
    }
    if (!originalDto?.nameInAssame || originalDto.nameInAssame !== formState.translations.assamiName) {
      request.nameInAssame = formState.translations.assamiName;
    }
  }

  // Handle arrays and complex objects
  if (formState.searchTags) {
    const existingTags = originalDto?.searchTag?.split(",") || [];
    const addedTags = formState.searchTags.filter(tag => !existingTags.includes(tag));
    const removedTags = existingTags.filter(tag => !formState.searchTags.includes(tag));

    if (addedTags.length > 0) {
      request.searchTag = (existingTags.concat(addedTags)).join(",");
    }

    if (removedTags.length > 0) {
      request.searchTag = (existingTags.filter(tag => !removedTags.includes(tag))).join(",");
    }
  }

  if (formState.assignedCategories) {
    const existingCategories = originalDto?.categories?.map(category => category.id) || [];
    const addedCategories = formState.assignedCategories.filter(id => !existingCategories.includes(id));
    const removedCategories = existingCategories.filter(id => !formState.assignedCategories.includes(id));

    if (addedCategories.length > 0) {
      request.categories = (originalDto?.categories || []).concat(addedCategories.map(id => ({ id })));
    }

    if (removedCategories.length > 0) {
      request.categories = (originalDto?.categories || []).filter(category => !removedCategories.includes(category.id));
    }
  }

  if (request.ondcDomain === "RET11" && (!originalDto?.description || originalDto.description !== formState.itemConfig.description)) {
    request.description = formState.itemConfig.description;
  }

  if (request.ondcDomain === "RET11" && (!originalDto?.diet || originalDto.diet !== formState.itemConfig.diet)) {
    request.diet = formState.itemConfig.diet;
  }

  if (!originalDto?.defaultUnit || originalDto.defaultUnit !== formState.defaultUnit) {
    request.defaultUnit = formState.defaultUnit as string;
  }

  if (!originalDto?.packaging || originalDto.packaging !== formState.itemConfig.packaging) {
    request.packaging = formState.itemConfig.packaging;
  }

  if (!originalDto?.mrp || originalDto.mrp !== formState.itemConfig.mrpPerUnit) {
    request.mrp = formState.itemConfig.mrpPerUnit as number;
  }

  if (request.ondcDomain === "RET10" && (!originalDto?.minimumOrderQty || originalDto.minimumOrderQty !== formState.itemConfig.minimumOrderQty)) {
    request.minimumOrderQty = formState.itemConfig.minimumOrderQty as number;
  }

  if (request.ondcDomain === "RET10" && (!originalDto?.incrementOrderQty || originalDto.incrementOrderQty !== formState.itemConfig.incrementOrderQty)) {
    request.incrementOrderQty = formState.itemConfig.incrementOrderQty as number;
  }

  if (request.ondcDomain === "RET10" && (!originalDto?.maximumOrderQty || originalDto.maximumOrderQty !== formState.itemConfig.maximumOrderQty)) {
    request.maximumOrderQty = formState.itemConfig.maximumOrderQty as number;
  }

  if (request.ondcDomain === "RET10" && (!originalDto?.maxAvailableQty || originalDto.maxAvailableQty !== formState.itemConfig.maxAvailableQty)) {
    request.maxAvailableQty = formState.itemConfig.maxAvailableQty as number;
  }

  if (originalDto?.disabled === undefined || originalDto.disabled !== formState.itemConfig.disabled) {
    request.disabled = formState.itemConfig.disabled as boolean;
  }

  if (request.ondcDomain === "RET10" && (!originalDto?.productId || originalDto.productId !== formState.itemConfig.productId)) {
    request.productId = formState.itemConfig.productId as string;
  }

  // Add other fields only if they differ from original or are non-empty
  if (request.ondcDomain === "RET10" && (!originalDto?.defaultWeightFactor || originalDto.defaultWeightFactor !== formState.itemConfig.weightFactor)) {
    request.defaultWeightFactor = formState.itemConfig.weightFactor;
  }

  if (formState.itemConfig.gstEligible === "yes") {
    request.gstHsnCode = formState.itemConfig.gstHsnCode;
    request.gstRate = formState.itemConfig.gstRate;
  } else {
    request.gstHsnCode = undefined;
    request.gstRate = undefined;
  }

  if (originalDto?.taxExempt === undefined || originalDto.taxExempt !== formState.itemConfig.taxExempt) {
    request.taxExempt = formState.itemConfig.taxExempt as boolean;
  }

  // Remove undefined and empty string values from request
  (Object.keys(request) as Array<keyof typeof request>).forEach(key => {
    if (request[key] === undefined || request[key] === '') {
      delete request[key];
    }
  });

  return request as MasterItemRequest;
} 