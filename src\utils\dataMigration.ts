import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, ScanCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { NotificationLogTable } from '../database/entities/NotificationLog.js';
import { webhookLogTableConfig } from '../database/entities/WebhookLog.js';

export class DataMigrationUtility {
    private dynamoDB: DynamoDBDocumentClient;

    constructor() {
        const client = new DynamoDBClient({});
        this.dynamoDB = DynamoDBDocumentClient.from(client);
    }

    /**
     * Add date partitioning fields to existing notification logs (last 30 days)
     * Run this once to optimize existing data
     */
    async addDatePartitioningToNotificationLogs(): Promise<void> {
        console.log('Starting notification logs date partitioning migration (last 30 days)...');
        
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const cutoffTimestamp = thirtyDaysAgo.getTime();
        
        let lastEvaluatedKey: Record<string, any> | undefined;
        let processedCount = 0;
        let updatedCount = 0;

        do {
            try {
                const scanParams: any = {
                    TableName: NotificationLogTable.TableName,
                    Limit: 100,
                    ProjectionExpression: 'notificationId, timestamp, businessId',
                    FilterExpression: '#timestamp >= :cutoffTimestamp',
                    ExpressionAttributeNames: {
                        '#timestamp': 'timestamp'
                    },
                    ExpressionAttributeValues: {
                        ':cutoffTimestamp': cutoffTimestamp
                    }
                };

                if (lastEvaluatedKey) {
                    scanParams.ExclusiveStartKey = lastEvaluatedKey;
                }

                const result = await this.dynamoDB.send(new ScanCommand(scanParams));
                const items = result.Items || [];
                
                // Process items in batches
                for (const item of items) {
                    if (!item.datePartition || !item.monthPartition) {
                        await this.updateNotificationLogWithDatePartitioning(item);
                        updatedCount++;
                    }
                    processedCount++;
                }

                lastEvaluatedKey = result.LastEvaluatedKey;
                
                console.log(`Processed ${processedCount} notification items, updated ${updatedCount} items`);
                
                // Add delay to avoid throttling
                await this.delay(100);
                
            } catch (error) {
                console.error('Error during notification logs migration:', error);
                break;
            }
        } while (lastEvaluatedKey);

        console.log(`Notification logs migration completed. Processed: ${processedCount}, Updated: ${updatedCount}`);
    }

    /**
     * Add date partitioning fields to existing webhook logs (last 30 days)
     * Run this once to optimize existing data
     */
    async addDatePartitioningToWebhookLogs(): Promise<void> {
        console.log('Starting webhook logs date partitioning migration (last 30 days)...');
        
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const cutoffTimestamp = thirtyDaysAgo.getTime();
        
        let lastEvaluatedKey: Record<string, any> | undefined;
        let processedCount = 0;
        let updatedCount = 0;

        do {
            try {
                const scanParams: any = {
                    TableName: webhookLogTableConfig.TableName,
                    Limit: 100,
                    ProjectionExpression: 'webhookId, timestamp, businessNumber',
                    FilterExpression: '#timestamp >= :cutoffTimestamp',
                    ExpressionAttributeNames: {
                        '#timestamp': 'timestamp'
                    },
                    ExpressionAttributeValues: {
                        ':cutoffTimestamp': cutoffTimestamp
                    }
                };

                if (lastEvaluatedKey) {
                    scanParams.ExclusiveStartKey = lastEvaluatedKey;
                }

                const result = await this.dynamoDB.send(new ScanCommand(scanParams));
                const items = result.Items || [];
                
                // Process items in batches
                for (const item of items) {
                    if (!item.datePartition || !item.monthPartition) {
                        await this.updateWebhookLogWithDatePartitioning(item);
                        updatedCount++;
                    }
                    processedCount++;
                }

                lastEvaluatedKey = result.LastEvaluatedKey;
                
                console.log(`Processed ${processedCount} webhook items, updated ${updatedCount} items`);
                
                // Add delay to avoid throttling
                await this.delay(100);
                
            } catch (error) {
                console.error('Error during webhook logs migration:', error);
                break;
            }
        } while (lastEvaluatedKey);

        console.log(`Webhook logs migration completed. Processed: ${processedCount}, Updated: ${updatedCount}`);
    }

    /**
     * Run migration for both tables (last 30 days)
     */
    async migrateBothTables(): Promise<void> {
        console.log('Starting migration for both tables (last 30 days)...');
        
        try {
            await this.addDatePartitioningToNotificationLogs();
            await this.addDatePartitioningToWebhookLogs();
            
            console.log('Migration completed for both tables!');
        } catch (error) {
            console.error('Error during migration:', error);
        }
    }

    /**
     * Update a single notification log item with date partitioning fields
     */
    private async updateNotificationLogWithDatePartitioning(item: any): Promise<void> {
        try {
            const timestamp = item.timestamp;
            const date = new Date(timestamp);
            
            const datePartition = date.toISOString().split('T')[0]; // YYYY-MM-DD
            const monthPartition = date.toISOString().slice(0, 7); // YYYY-MM

            await this.dynamoDB.send(new UpdateCommand({
                TableName: NotificationLogTable.TableName,
                Key: {
                    notificationId: item.notificationId,
                    timestamp: item.timestamp
                },
                UpdateExpression: 'SET datePartition = :datePartition, monthPartition = :monthPartition',
                ExpressionAttributeValues: {
                    ':datePartition': datePartition,
                    ':monthPartition': monthPartition
                }
            }));
        } catch (error) {
            console.error(`Error updating notification log ${item.notificationId}:`, error);
        }
    }

    /**
     * Update a single webhook log item with date partitioning fields
     */
    private async updateWebhookLogWithDatePartitioning(item: any): Promise<void> {
        try {
            const timestamp = item.timestamp;
            const date = new Date(timestamp);
            
            const datePartition = date.toISOString().split('T')[0]; // YYYY-MM-DD
            const monthPartition = date.toISOString().slice(0, 7); // YYYY-MM

            await this.dynamoDB.send(new UpdateCommand({
                TableName: webhookLogTableConfig.TableName,
                Key: {
                    webhookId: item.webhookId,
                    timestamp: item.timestamp
                },
                UpdateExpression: 'SET datePartition = :datePartition, monthPartition = :monthPartition',
                ExpressionAttributeValues: {
                    ':datePartition': datePartition,
                    ':monthPartition': monthPartition
                }
            }));
        } catch (error) {
            console.error(`Error updating webhook log ${item.webhookId}:`, error);
        }
    }

    /**
     * Utility to add delay between operations
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Create a new notification log with proper date partitioning
     */
    static createNotificationLogWithPartitioning(data: any): any {
        const timestamp = data.timestamp || Date.now();
        const date = new Date(timestamp);
        
        return {
            ...data,
            datePartition: date.toISOString().split('T')[0],
            monthPartition: date.toISOString().slice(0, 7)
        };
    }

    /**
     * Create a new webhook log with proper date partitioning
     */
    static createWebhookLogWithPartitioning(data: any): any {
        const timestamp = data.timestamp || Date.now();
        const date = new Date(timestamp);
        
        return {
            ...data,
            datePartition: date.toISOString().split('T')[0],
            monthPartition: date.toISOString().slice(0, 7)
        };
    }

    /**
     * Get migration statistics for both tables
     */
    async getMigrationStats(): Promise<{
        notificationLogs: { total: number; needsUpdate: number };
        webhookLogs: { total: number; needsUpdate: number };
    }> {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const cutoffTimestamp = thirtyDaysAgo.getTime();

        // Count notification logs
        let notificationTotal = 0;
        let notificationNeedsUpdate = 0;
        let lastKey: any = undefined;

        do {
            const result = await this.dynamoDB.send(new ScanCommand({
                TableName: NotificationLogTable.TableName,
                Limit: 1000,
                ProjectionExpression: 'notificationId, timestamp, datePartition, monthPartition',
                FilterExpression: '#timestamp >= :cutoffTimestamp',
                ExpressionAttributeNames: { '#timestamp': 'timestamp' },
                ExpressionAttributeValues: { ':cutoffTimestamp': cutoffTimestamp },
                ExclusiveStartKey: lastKey
            }));

            const items = result.Items || [];
            notificationTotal += items.length;
            notificationNeedsUpdate += items.filter(item => !item.datePartition || !item.monthPartition).length;
            lastKey = result.LastEvaluatedKey;
        } while (lastKey);

        // Count webhook logs
        let webhookTotal = 0;
        let webhookNeedsUpdate = 0;
        lastKey = undefined;

        do {
            const result = await this.dynamoDB.send(new ScanCommand({
                TableName: webhookLogTableConfig.TableName,
                Limit: 1000,
                ProjectionExpression: 'webhookId, timestamp, datePartition, monthPartition',
                FilterExpression: '#timestamp >= :cutoffTimestamp',
                ExpressionAttributeNames: { '#timestamp': 'timestamp' },
                ExpressionAttributeValues: { ':cutoffTimestamp': cutoffTimestamp },
                ExclusiveStartKey: lastKey
            }));

            const items = result.Items || [];
            webhookTotal += items.length;
            webhookNeedsUpdate += items.filter(item => !item.datePartition || !item.monthPartition).length;
            lastKey = result.LastEvaluatedKey;
        } while (lastKey);

        return {
            notificationLogs: { total: notificationTotal, needsUpdate: notificationNeedsUpdate },
            webhookLogs: { total: webhookTotal, needsUpdate: webhookNeedsUpdate }
        };
    }
} 