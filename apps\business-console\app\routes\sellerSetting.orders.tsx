import type React from "react"

import { useState, useEffect, useMemo, useCallback } from "react"
import { Badge } from "~/components/ui/badge"
import { But<PERSON> } from "~/components/ui/button"
import { Card, CardContent } from "~/components/ui/card"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select"
import { Clock, AlertCircle, Search, Filter, Phone, Store, Truck, MapPin, User, Eye, Package, AlertTriangle, MessageCircleQuestionIcon, RefreshCw, ArrowRight, CalendarIcon, Timer } from "lucide-react"
import { usePolling } from "~/hooks/usePolling"
import type { rNETOrder, OrderStatus } from "~/types/api/businessConsoleService/rNETOrder"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "~/components/ui/dialog"
import { Separator } from "~/components/ui/separator"
import { Textarea } from "@headlessui/react"
import { json, ActionFunction } from "@remix-run/node"
import { useFetcher } from "@remix-run/react"
import { withAuth, withResponse } from "~/utils/auth-utils"
import { getrNETOrders, cancelrNETOrder, updateLiveOrderStatus, markDelivered } from "~/services/rNETOrders"
import dayjs from "dayjs"
import { useToast } from "~/hooks/use-toast"
import { DateRange } from "react-day-picker"
import { Popover, PopoverContent, PopoverTrigger, PopoverClose } from "~/components/ui/popover"
import { cn } from "lib/utils"
import { format } from "date-fns"
import { Calendar } from "~/components/ui/calendar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table"

type ActionIntent = "Fetch Orders" | "Cancel Order" | "Mark Delivered" | "Update LiveOrder Status";

interface OrderStatusCount {
  status: string;
  count: number;
}

interface ActionData {
  intent: ActionIntent;
  errorMessage: string;
  success: boolean;
  data: { orders: rNETOrder[], totalElements: number, pageSize: number, currentPage: number, orderStatusCounts: OrderStatusCount[] };
}
export const action: ActionFunction = withAuth(async ({ request, user }) => {
  const formData = await request.formData();
  const intent = formData.get("intent") as ActionIntent;
  const data = formData.get("data") as any;

  if (!intent) {
    return json({ success: false, errorMessage: "Invalid request", intent: intent }, { status: 400 });
  }

  if (!user.sellerId) {
    return json({ success: false, errorMessage: "Seller ID not found", intent: intent }, { status: 400 });
  }

  if (intent === "Fetch Orders") {
    const { activeTab, searchType, searchTerm, filterDate, filterStatus, pageSize, currentPage } = JSON.parse(data);

    // Convert dates to IST (UTC+5:30)
    const fromDate = filterDate?.from
      ? dayjs(filterDate.from).startOf('day').add(5, 'hours').add(30, 'minutes').toISOString()
      : "";
    const toDate = filterDate?.to
      ? dayjs(filterDate.to).endOf("day").add(5, 'hours').add(30, 'minutes').toISOString()
      : filterDate?.from ? dayjs(filterDate.from).endOf("day").add(5, 'hours').add(30, 'minutes').toISOString() : "";
    const searchQuery = searchType === "OrderID" ? `&orderGroupId=${searchTerm}` : searchType === "BuyerID" ? `&buyerId=${searchTerm}` : searchType === "BuyerMobile" ? `&buyerMobile=${searchTerm}` : searchType === "Name" ? `&searchTerm=${searchTerm}` : "";
    const queryParams = `&sellerId=${user.sellerId}&fromDate=${fromDate}&toDate=${toDate}${searchQuery}${filterStatus ? `&status=${filterStatus}` : ""}&pageSize=${pageSize}&currentPage=${currentPage}`;

    try {
      const response = await getrNETOrders(request, queryParams, "seller");
      return withResponse({ success: true, intent: intent, data: response?.data }, response?.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to fetch orders" }, { status: 400 })
    }
  }
  if (intent === "Cancel Order") {
    try {
      const { order, formData } = JSON.parse(data);

      const response = await cancelrNETOrder(request, order.tripId, order.orderGroupId);
      return withResponse({ success: true, intent: intent }, response.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to cancel order" }, { status: 400 })
    }
  }
  if (intent === "Update LiveOrder Status") {
    try {
      const { order, formData } = JSON.parse(data);
      const status = order.orderStatus === "Created" ? "Accepted" : order.orderStatus === "Accepted" ? "Packed" : order.orderStatus === "Packed" ? "Assigned" : order.orderStatus === "Assigned" ? "PickedUp" : "Dispatched";
      const queryParams = status === "Packed" ? `${formData.boxes !== undefined ? `&boxes=${formData.boxes}` : ""}${formData.bags !== undefined ? `&bags=${formData.bags}` : ""}` : "";

      const response = await updateLiveOrderStatus(request, order.orderGroupId, status);
      return withResponse({ success: true, intent: intent }, response.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to update order status" }, { status: 400 })
    }
  }
  if (intent === "Mark Delivered") {
    try {
      const { order, formData } = JSON.parse(data);
      const queryParams = `${formData.deliveryCode !== undefined ? `&deliveryCode=${formData.deliveryCode}` : ""}${formData.creditAmount !== undefined ? `&creditAmount=${formData.creditAmount}` : ""}${formData.boxesGiven !== undefined ? `&boxesGiven=${formData.boxesGiven}` : ""}${formData.boxesTaken !== undefined ? `&boxesTaken=${formData.boxesTaken}` : ""}`;

      const response = await markDelivered(request, order.tripId, order.orderGroupId, queryParams);
      return withResponse({ success: true, intent: intent }, response.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to mark order as delivered" }, { status: 400 })
    }
  }

  return json({ success: false, intent: intent, errorMessage: "Invalid intent" }, { status: 400 });
});


export default function LiveOrderDashboard() {
  const [allOrders, setAllOrders] = useState<rNETOrder[]>()
  const [selectedOrder, setSelectedOrder] = useState<rNETOrder | null>(null)
  const [orderStatusCounts, setOrderStatusCounts] = useState<OrderStatusCount[]>([])
  const [activeTab, setActiveTab] = useState<"live" | "all">("live")
  const fetcher = useFetcher<ActionData>()

  // action
  const [actionType, setActionType] = useState<string>("")
  const [actionSelectedOrder, setActionSelectedOrder] = useState<rNETOrder | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Search and Filters
  const [searchType, setSearchType] = useState<"OrderID" | "BuyerID" | "BuyerMobile" | "Name">("OrderID")
  const [searchTerm, setSearchTerm] = useState<string>("")
  const [filterDate, setFilterDate] = useState<DateRange | undefined>({ from: new Date(), to: new Date() });  // date object
  const [filterStatus, setFilterStatus] = useState<OrderStatus | "">("")
  const [pageSize, setPageSize] = useState(20)
  const [currentPage, setCurrentPage] = useState(0)
  const [totalElements, setTotalElements] = useState(0)

  // local state
  const [dateRange, setDateRange] = useState<DateRange | undefined>({ from: new Date(), to: new Date() });  //date object

  // debounce on search term
  const [debounceSearchTerm, setDebounceSearchTerm] = useState<string>("");
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebounceSearchTerm(searchTerm);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, [searchTerm]);

  // Animation state for smooth transitions
  const [animationKey, setAnimationKey] = useState(0)

  // polling for auto-refresh
  const { isPolling, startPolling, stopPolling } = usePolling(() => refreshOrders(true), 59000);

  const refreshOrders = useCallback((isAutoRefresh?: boolean) => {
    console.log("Refreshing orders...")

    const formData = new FormData();
    formData.append("intent", "Fetch Orders");
    const data = { activeTab, searchType, searchTerm: debounceSearchTerm, filterDate, filterStatus, pageSize, currentPage }
    formData.append("data", JSON.stringify(data))
    fetcher.submit(formData, { method: "post" })
  }, [activeTab, debounceSearchTerm, filterDate, filterStatus, pageSize, currentPage])

  useEffect(() => {
    refreshOrders()
  }, [activeTab, debounceSearchTerm, filterDate, filterStatus, pageSize, currentPage])

  useEffect(() => {
    if (activeTab === "live") {
      startPolling()
    } else {
      stopPolling()
    }
    return () => stopPolling()
  }, [activeTab, startPolling, stopPolling])

  // pause and resume polling when the tab is inactive
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && activeTab === "live") {
        startPolling()
      } else {
        stopPolling()
      }
    }
    document.addEventListener("visibilitychange", handleVisibilityChange)
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [activeTab, startPolling, stopPolling])

  const handleTabChange = (newTab: string) => {
    setSearchType("OrderID")
    setSearchTerm("")
    setDebounceSearchTerm("")
    if (newTab === "live") {
      setFilterDate({ from: new Date(), to: new Date() })
      setDateRange({ from: new Date(), to: new Date() })
    } else {
      setFilterDate(undefined)
      setDateRange(undefined)
    }
    setFilterStatus("")
    setPageSize(20)
    setCurrentPage(0)
    setAllOrders([])
    setOrderStatusCounts([])
    setActiveTab(newTab as "live" | "all")
  }

  const searchTypeFilters = [
    { label: "Order ID", value: "OrderID" },
    { label: "Buyer ID", value: "BuyerID" },
    { label: "Buyer Mobile", value: "BuyerMobile" },
    { label: "Name", value: "Name" },
  ]

  const statusFilters = [
    { label: "Created", value: "Created" },
    { label: "Accepted", value: "Accepted" },
    { label: "Packed", value: "Packed" },
    { label: "Assigned", value: "Assigned" },
    { label: "Picked Up", value: "PickedUp" },
    { label: "Dispatched", value: "Dispatched" },
    { label: "Delivered", value: "Delivered" },
    { label: "Cancelled", value: "Cancelled" },
  ]

  // Separate live and completed orders
  const { liveOrders, completedOrders } = useMemo(() => {
    const live = allOrders?.filter((order) => !["Delivered", "Cancelled"].includes(order.orderStatus))
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())

    const completed = allOrders?.filter((order) => ["Delivered", "Cancelled"].includes(order.orderStatus))

    return { liveOrders: live, completedOrders: completed }
  }, [allOrders])

  // Statistics from API response
  const stats = {
    total: totalElements,
    created: orderStatusCounts.find((s) => s.status === "Created")?.count || 0,
    accepted: orderStatusCounts.find((s) => s.status === "Accepted")?.count || 0,
    packed: orderStatusCounts.find((s) => s.status === "Packed")?.count || 0,
    assigned: orderStatusCounts.find((s) => s.status === "Assigned")?.count || 0,
    dispatched: orderStatusCounts.find((s) => s.status === "Dispatched")?.count || 0,
    delivered: orderStatusCounts.find((s) => s.status === "Delivered")?.count || 0,
    cancelled: orderStatusCounts.find((s) => s.status === "Cancelled")?.count || 0,
  }

  const handleAction = (order: rNETOrder, action: string) => {
    setActionSelectedOrder(order)
    setActionType(action)
  }

  const handleSubmitAction = (formData: any) => {
    const actionData = new FormData();
    actionData.append("intent", actionType);
    actionData.append("data", JSON.stringify({ order: actionSelectedOrder, formData }))
    fetcher.submit(actionData, { method: "post" })
    setIsSubmitting(true)
  }

  useEffect(() => {
    if (fetcher.data?.intent === "Fetch Orders") {
      if (fetcher.data?.success) {
        fetcher.data?.data?.orders ? setAllOrders(fetcher.data.data.orders) : setAllOrders([])
        fetcher.data?.data?.totalElements ? setTotalElements(fetcher.data.data.totalElements) : setTotalElements(0)
        fetcher.data?.data?.orderStatusCounts ? setOrderStatusCounts(fetcher.data.data.orderStatusCounts) : setOrderStatusCounts([])
      } else if (fetcher.data?.success === false) {
        console.log(fetcher.data?.errorMessage)
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage,
          variant: "destructive"
        })
      }
    }
    if (fetcher.data?.intent === "Cancel Order") {
      if (fetcher.data?.success) {
        toast({
          title: "Order Cancelled",
          description: "Order cancelled successfully",
          style: {
            backgroundColor: "#00A33F",
            color: "#ffffff"
          }
        })
        setActionSelectedOrder(null)
        setSelectedOrder(null)
        setActionType("")
        setIsSubmitting(false)
        refreshOrders(false)
      } else if (fetcher.data?.success === false) {
        console.log(fetcher.data?.errorMessage)
        setIsSubmitting(false)
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage,
          variant: "destructive"
        })
      }
    }
    if (fetcher.data?.intent === "Update LiveOrder Status") {
      if (fetcher.data?.success) {
        toast({
          title: "Order Status Updated",
          description: "Order status updated successfully",
          style: {
            backgroundColor: "#00A33F",
            color: "#ffffff"
          }
        })
        setActionSelectedOrder(null)
        setSelectedOrder(null)
        setActionType("")
        setIsSubmitting(false)
        refreshOrders(false)
      } else if (fetcher.data?.success === false) {
        console.log(fetcher.data?.errorMessage)
        setIsSubmitting(false)
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage,
          variant: "destructive"
        })
      }
    }
    if (fetcher.data?.intent === "Mark Delivered") {
      if (fetcher.data?.success) {
        toast({
          title: "Order Marked as Delivered",
          description: "Order marked as delivered successfully",
          style: {
            backgroundColor: "#00A33F",
            color: "#ffffff"
          }
        })
        setActionSelectedOrder(null)
        setSelectedOrder(null)
        setActionType("")
        setIsSubmitting(false)
        refreshOrders(false)
      } else if (fetcher.data?.success === false) {
        console.log(fetcher.data?.errorMessage)
        setIsSubmitting(false)
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage,
          variant: "destructive"
        })
      }
    }
  }, [fetcher.data])

  return (
    <div className="min-h-screen p-6">
      <div className="mx-auto mb-6">

        {/* Header */}
        <div className="mb-4">
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-xl md:text-3xl font-bold text-gray-900">Orders</h1>
              <p className="mt-2 text-gray-600">Manage and track all your restaurant orders</p>
            </div>
            <div className="flex flex-row items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refreshOrders(false)}
                disabled={(fetcher.state === "loading" || fetcher.state === "submitting") && fetcher.data?.intent === "Fetch Orders"}
                className="w-fit sm:ml-auto flex items-center gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${(fetcher.state === "loading" || fetcher.state === "submitting") && fetcher.data?.intent === "Fetch Orders" ? "animate-spin" : ""}`} />
                {(fetcher.state === "loading" || fetcher.state === "submitting") && fetcher.data?.intent === "Fetch Orders" ? "Refreshing..." : "Refresh"}
              </Button>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className={`w-2 h-2 rounded-full ${isPolling ? "bg-green-500 animate-pulse" : "bg-gray-400"}`} />
                Auto-refresh {isPolling ? "ON" : "OFF"}
              </div>
            </div>
          </div>
        </div>

        {/* Orders Type */}
        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="w-full h-10 mb-2">
            <TabsTrigger value="live" className="w-1/2 h-8 py-1 font-semibold">⏱️ Live Orders</TabsTrigger>
            <TabsTrigger value="all" className="w-1/2 h-8 py-1 font-semibold">📦 All Orders</TabsTrigger>
          </TabsList>

          {/* Search and Filters */}
          <Card className="mb-1 bg-gray-100">
            <CardContent className="p-2">
              <div className="space-y-1">
                {/* Search */}
                <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-2">
                  <div className="relative lg:col-span-1">
                    <Select value={searchType} onValueChange={(value: typeof searchType) => setSearchType(value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Search by" />
                      </SelectTrigger>
                      <SelectContent>
                        {searchTypeFilters.map((filter) => (
                          <SelectItem key={filter.value} value={filter.value}>
                            {filter.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="relative lg:col-span-2">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder={searchType === "Name" ? "Search name" : (searchType === "BuyerMobile") ? "Search by Mobile" : "Search by ID"}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <div className="relative sm:col-span-2 lg:col-span-1 ">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          id="filterDate"
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !filterDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon />
                          {filterDate?.from ? (
                            filterDate.to ? (
                              <>
                                {format(filterDate.from, "LLL dd, y")} - {format(filterDate.to, "LLL dd, y")}
                              </>
                            ) : (
                              format(filterDate.from, "LLL dd, y")
                            )
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          initialFocus
                          selected={dateRange}
                          mode="range"
                          onSelect={(range: DateRange | undefined) => {
                            if (!range?.from) return;
                            setDateRange({
                              from: range.from,
                              to: range.to || undefined,
                            });
                          }}
                        />
                        <PopoverClose className="w-full">
                          <Button
                            variant="ghost"
                            className="w-full text-blue-500 hover:text-blue-500 justify-center"
                            onClick={() => setFilterDate(dateRange)}
                          >
                            Set
                          </Button>
                        </PopoverClose>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Filters */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium">Status</Label>
                    <Select value={filterStatus} onValueChange={(value: OrderStatus) => setFilterStatus(value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {statusFilters.map((filter) => (
                          <SelectItem key={filter.value} value={filter.value}>
                            {filter.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-1 lg:col-start-4 flex items-end">
                    <Button
                      onClick={() => {
                        setSearchType("OrderID")
                        setSearchTerm("")
                        setDebounceSearchTerm("")
                        if (activeTab === "live") {
                          setFilterDate({ from: new Date(), to: new Date() })
                          setDateRange({ from: new Date(), to: new Date() })
                        } else {
                          setFilterDate(undefined)
                          setDateRange(undefined)
                        }
                        setFilterStatus("")
                        setPageSize(20)
                        setCurrentPage(0)
                      }}
                      className="w-full"
                    >
                      <Filter className="w-4 h-4 mr-2" />
                      Clear Filters
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pagination */}
          <Card className="p-0 mb-1">
            <CardContent className="px-1.5 py-1">
              <div className="flex flex-row items-center justify-end gap-1.5">
                <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                  <SelectTrigger className="w-[140px] h-[36px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="20">20 per Page</SelectItem>
                    <SelectItem value="50">50 per Page</SelectItem>
                    <SelectItem value="100">100 per Page</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={currentPage.toString()} onValueChange={(value) => setCurrentPage(Number(value))}>
                  <SelectTrigger className="w-[140px] h-[36px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: Math.ceil(totalElements / pageSize) }, (_, i) => (
                      <SelectItem key={i} value={i.toString()}>
                        Page {i + 1}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Statistics Cards */}
          {activeTab === "live" && <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-8 gap-1 sm:gap-2 mb-4">
            <Card className="shadow-sm">
              <CardContent className="p-1 sm:p-2 text-center">
                <div className="text-xs sm:text-sm text-gray-800">Total</div>
                <div className="text-lg leading-6 font-semibold text-gray-900">{stats.total}</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-1 sm:p-2 text-center">
                <div className="text-xs sm:text-sm text-gray-800">Created</div>
                <div className="text-lg leading-6 font-semibold text-purple-600">{stats.created}</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-1 sm:p-2 text-center">
                <div className="text-xs sm:text-sm text-gray-800">Accepted</div>
                <div className="text-lg leading-6 font-semibold text-purple-600">{stats.accepted}</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-1 sm:p-2 text-center">
                <div className="text-xs sm:text-sm text-gray-800">Packed</div>
                <div className="text-lg leading-6 font-semibold text-orange-600">{stats.packed}</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-1 sm:p-2 text-center">
                <div className="text-xs sm:text-sm text-gray-800">Assigned</div>
                <div className="text-lg leading-6 font-semibold text-orange-600">{stats.assigned}</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-1 sm:p-2 text-center">
                <div className="text-xs sm:text-sm text-gray-800">Dispatched</div>
                <div className="text-lg leading-6 font-semibold text-yellow-600">{stats.dispatched}</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-1 sm:p-2 text-center">
                <div className="text-xs sm:text-sm text-gray-800">Delivered</div>
                <div className="text-lg leading-6 font-semibold text-green-600">{stats.delivered}</div>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardContent className="p-1 sm:p-2 text-center">
                <div className="text-xs sm:text-sm text-gray-800">Cancelled</div>
                <div className="text-lg leading-6 font-semibold text-red-600">{stats.cancelled}</div>
              </CardContent>
            </Card>
          </div>}

          <TabsContent value="live">
            {/* Live Orders Section */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl sm:text-2xl font-bold flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                  Live Orders
                  <Badge variant="secondary" className="ml-2">
                    {totalElements - (stats.delivered + stats.cancelled) || 0}
                  </Badge>
                </h2>
              </div>

              {liveOrders?.length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <div className="text-gray-500">
                      <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium">No live orders</p>
                      <p className="text-sm">No active orders for the selected date and filters.</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div
                  key={`live-orders-${animationKey}`}
                  className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 transition-all duration-300 ease-out"
                  style={{
                    transform: 'translateY(0)',
                    transition: 'transform 0.3s ease-out'
                  }}
                >
                  {liveOrders?.map((order, index) => (
                    <div
                      key={`${order.orderGroupId}-${animationKey}`}
                      className="animate-in fade-in slide-in-from-bottom-4 duration-500 ease-out transform transition-all hover:scale-[1.01]"
                      style={{
                        animationDelay: `${index * 50}ms`,
                        animationFillMode: 'both',
                        willChange: 'transform'
                      }}
                    >
                      <OrderCard
                        order={order}
                        onViewDetails={setSelectedOrder}
                        onAction={handleAction}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Completed Orders Section */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl sm:text-2xl font-bold flex items-center gap-2">
                  <div className="w-3 h-3 bg-gray-500 rounded-full" />
                  Completed Orders
                  <Badge variant="secondary" className="ml-2">
                    {stats.delivered + stats.cancelled || 0}
                  </Badge>
                </h2>
              </div>

              {completedOrders?.length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <div className="text-gray-500">
                      <AlertCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium">No completed orders</p>
                      <p className="text-sm">No completed orders for the selected date and filters.</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="overflow-x-auto">
                  <div
                    key={`completed-orders-${animationKey}`}
                    className="flex gap-4 pb-4 min-w-max transition-all duration-300 ease-out"
                    style={{
                      transform: 'translateX(0)',
                      transition: 'transform 0.3s ease-out'
                    }}
                  >
                    {completedOrders?.map((order, index) => (
                      <div
                        key={`${order.orderGroupId}-${animationKey}`}
                        className="flex-shrink-0 w-72 sm:w-80 animate-in fade-in slide-in-from-right-4 duration-500 ease-out transform transition-all hover:scale-[1.01]"
                        style={{
                          animationDelay: `${index * 50}ms`,
                          animationFillMode: 'both',
                          willChange: 'transform'
                        }}
                      >
                        <OrderCard order={order} onViewDetails={setSelectedOrder} onAction={handleAction} />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="all">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="h-10 px-2 py-1">Order ID</TableHead>
                  <TableHead className="h-10 px-2 py-1">Customer</TableHead>
                  <TableHead className="h-10 px-2 py-1">Amount</TableHead>
                  <TableHead className="h-10 px-2 py-1">Payment Status</TableHead>
                  <TableHead className="h-10 px-2 py-1">Order Status</TableHead>
                  <TableHead className="h-10 px-2 py-1">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {allOrders?.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No results.
                    </TableCell>
                  </TableRow>
                ) : (
                  allOrders?.map((order) => (
                    <TableRow key={order.orderGroupId}>
                      <TableCell className="px-2 py-2">
                        <p>{order.orderGroupId}</p>
                        <p className="text-xs text-gray-600">{dayjs(order.createdAt).format("DD/MM/YYYY, h:mm A")}</p>
                      </TableCell>
                      <TableCell className="px-2 py-2 break-all">
                        <p>{order.buyerName}</p>
                        <p className="text-xs text-gray-600">{order.bAreaName}</p>
                      </TableCell>
                      <TableCell className="whitespace-nowrap px-2 py-2">₹ {order.totalOrderGroupAmount.toLocaleString()}</TableCell>
                      <TableCell className="px-2 py-2">{order.paymentCompleted ? "Paid" : "Pending"}</TableCell>
                      <TableCell className="px-2 py-2">
                        <Badge
                          variant={
                            order.orderStatus === "Delivered"
                              ? "default"
                              : order.orderStatus === "Cancelled"
                                ? "destructive"
                                : "secondary"
                          }
                        >
                          {order.orderStatus}
                        </Badge>
                      </TableCell>
                      <TableCell className="px-2 py-2">
                        <Button variant="outline" size="sm" onClick={() => setSelectedOrder(order)}>
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TabsContent>
        </Tabs>

        {/* Order Details Modal */}
        <OrderDetailsModal order={selectedOrder} onClose={() => setSelectedOrder(null)} onAction={handleAction} />

        {/* Action Modal */}
        <ActionModal
          order={actionSelectedOrder}
          actionType={actionType}
          onClose={() => {
            setActionSelectedOrder(null)
            setActionType("")
          }}
          isSubmitting={isSubmitting}
          onSubmit={handleSubmitAction}
        />
      </div>
    </div >
  )
}


// Order Card
interface OrderCardProps {
  order: rNETOrder
  onViewDetails: (order: rNETOrder) => void
  onAction: (order: rNETOrder, action: string) => void
}

export function OrderCard({ order, onViewDetails, onAction }: OrderCardProps) {
  const [currentTime, setCurrentTime] = useState(Date.now())

  // Update timer every second for live orders
  useEffect(() => {
    if (!["Delivered", "Cancelled"].includes(order.orderStatus)) {
      const interval = setInterval(() => {
        setCurrentTime(Date.now())
      }, 1000)
      return () => clearInterval(interval)
    }
  }, [order.orderStatus])

  const styling = getOrderCardStyling(order)
  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)
  const isNewOrder = timeDiff <= 60

  //play sound if it is new order
  useEffect(() => {
    if (isNewOrder) {
      const audio = new Audio("/new-order.mp3")
      audio.play().catch(console.warn)
      return () => {
        audio.pause()
        audio.src = ""
      }
    }
  }, [isNewOrder])

  const handlePhoneClick = (phoneNumber: string, e: React.MouseEvent) => {
    e.stopPropagation()
    window.open(`tel:${phoneNumber}`, "_self")
  }

  const getActionButtons = () => {
    const buttons = []

    if (order.orderStatus === "Created") {
      buttons.push(
        <Button
          key="cancel"
          variant="destructive"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            onAction(order, "Cancel Order")
          }}
          className="text-xs"
        >
          Cancel Order
        </Button>,
      )
    }

    if (showUpdateStatusButton(order)) {
      buttons.push(
        <Button
          key="updateStatus"
          variant="default"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            onAction(order, "Update LiveOrder Status")
          }}
          className="text-xs"
        >
          Update to {order.orderStatus === "Created" ? "Accepted" : order.orderStatus === "Accepted" ? "Packed" : order.orderStatus === "Packed" ? "Assigned" : order.orderStatus === "Assigned" ? "PickedUp" : "Dispatched"} <ArrowRight className="w-3 h-3" />
        </Button>,
      )
    }

    if (order.orderStatus === "Dispatched") {
      buttons.push(
        <Button
          key="markDelivered"
          variant="default"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            onAction(order, "Mark Delivered")
          }}
          className="text-xs bg-blue-500 hover:bg-blue-500"
        >
          Mark Delivered
        </Button>,
      )
    }

    return buttons
  }

  return (
    <Card
      className={`${styling.className} will-change-transform cursor-pointer transition-all duration-500 h-full hover:shadow-lg`}
      onClick={() => onViewDetails(order)}
      onMouseEnter={(e) => {
        e.currentTarget.style.animationPlayState = "paused"
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.animationPlayState = "running"
      }}
    >
      <CardContent className="p-3 sm:p-4">
        {/* Header with Order ID and Time */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3">
          <div className="flex flex-wrap items-center gap-2">
            <div className="font-semibold text-base sm:text-lg">#{order.orderGroupId}</div>
            {isNewOrder && (
              <Badge variant="secondary" className="bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 text-xs border border-emerald-200 shadow-sm font-semibold animate-bounce">
                ✨ NEW
              </Badge>
            )}
            {order.supportTickets && order.supportTickets.length > 0 && (
              <Badge variant="destructive" className="flex items-center gap-1 text-xs">
                <MessageCircleQuestionIcon className="w-3 h-3" />
                {order.supportTickets.length}
              </Badge>
            )}
          </div>
          <div className="text-xs sm:text-sm text-gray-500 flex items-center gap-1">
            <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
            {["Cancelled"].includes(order.orderStatus)
              ? formatTimeElapsed(getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt))
              : ["Delivered"].includes(order.orderStatus) ? formatTimeElapsed(getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt)) : formatTimeElapsed(timeDiff)}
          </div>
        </div>

        {/* Customer Info */}
        <div className="mb-3">
          <div className="flex items-center gap-2 mb-1">
            <Timer className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
            <span className="text-xs sm:text-sm text-gray-500">
              {dayjs(order.createdAt).format("DD MMM YY - h:mm A")}
            </span>
          </div>
          <div className="flex items-center gap-2 mb-1">
            <User className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
            <span className="font-medium text-sm sm:text-base truncate">{order.buyerName}</span>
            <button
              onClick={(e) => handlePhoneClick(order.buyerMobile, e)}
              className="text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50"
            >
              <Phone className="w-3 h-3 sm:w-4 sm:h-4" />
            </button>
          </div>
          <div className="text-xs sm:text-sm text-gray-600 flex items-start gap-2">
            <MapPin className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 mt-0.5 flex-shrink-0" />
            <span className="line-clamp-2">{order.bAddress}</span>
          </div>
        </div>

        {/* Restaurant Info */}
        <div className="mb-3">
          <div className="flex items-center gap-2 flex-wrap">
            <Store className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
            <span className="font-medium text-sm sm:text-base truncate">{order.sellerName}</span>
            <button
              onClick={(e) => handlePhoneClick(order.sellerMobile, e)}
              className="text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50"
            >
              <Phone className="w-3 h-3 sm:w-4 sm:h-4" />
            </button>
            <Badge variant="outline" className="text-xs">
              {order.logisticProvider}
            </Badge>
            {(order.pos !== "none" && order.pos !== undefined) && (
              <Badge variant="outline" className="text-xs">
                {order.pos}
              </Badge>
            )}
          </div>
        </div>

        {/* Delivery Info */}
        {order.logisticProvider === "MP2" && order.logisticDetails && (
          <div className="mb-3">
            <div className="flex items-center gap-2 flex-wrap">
              <Truck className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
              <span className="text-xs sm:text-sm truncate">{order.logisticDetails.riderName}</span>
              {order.logisticDetails.riderPhone && (
                <button
                  onClick={(e) => handlePhoneClick(order.logisticDetails?.riderPhone.toString() || "", e)}
                  className="text-blue-600 hover:text-blue-800 flex-shrink-0 p-1 rounded hover:bg-blue-50"
                >
                  <Phone className="w-3 h-3 sm:w-4 sm:h-4" />
                </button>
              )}
              {order.logStatus && (
                <Badge variant="outline" className="text-xs">
                  {getLogisticStatusDisplayName(order.logStatus)}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Order Summary */}
        <div className="mb-3">
          <div className="text-sm sm:text-base font-medium text-gray-900 mb-1">
            {order.totalItems} item(s) • ₹{order.totalOrderGroupAmount}
          </div>
          {order.orderDetails && (
            <div className="text-xs text-gray-500 line-clamp-2">
              {order.orderDetails.slice(0, 3).map((item, idx) => (
                <span key={item.orderId}>
                  {item.itemName} x{item.qty}
                  {idx < Math.min(order.orderDetails!.length, 3) - 1 ? ", " : ""}
                </span>
              ))}
              {order.orderDetails.length > 3 && "..."}
            </div>
          )}
        </div>

        {/* Status and Actions */}
        <div className="flex flex-col gap-2">
          <div className="flex justify-between items-center">
            <Badge
              variant={
                order.orderStatus === "Delivered"
                  ? "default"
                  : order.orderStatus === "Cancelled"
                    ? "destructive"
                    : "secondary"
              }
              className="capitalize text-xs"
            >
              {getStatusDisplayName(order.orderStatus)}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onViewDetails(order)
              }}
              className="flex items-center gap-1 text-xs sm:text-sm"
            >
              <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
              Details
            </Button>
          </div>

          {/* Action Buttons */}
          {getActionButtons().length > 0 && <div className="flex gap-2 flex-wrap">{getActionButtons()}</div>}
        </div>

        {/* Helper Text */}
        {styling.helperText && (
          <div className="mt-3 p-3 bg-gradient-to-r from-amber-50 via-amber-25 to-amber-50 border border-amber-200 rounded-lg text-xs text-amber-900 shadow-sm backdrop-blur-sm">
            <div className="flex items-start gap-2">
              <AlertCircle className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
              <span className="leading-relaxed font-medium">{styling.helperText}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}


// Order Details Modal
interface OrderDetailsModalProps {
  order: rNETOrder | null
  onClose: () => void
  onAction: (order: rNETOrder, action: string) => void
}

export function OrderDetailsModal({ order, onClose, onAction }: OrderDetailsModalProps) {
  if (!order) return null

  const handlePhoneClick = (phoneNumber: string) => {
    window.open(`tel:${phoneNumber}`, "_self")
  }

  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)

  return (
    <Dialog open={!!order} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full rounded-md">
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl flex items-center gap-2">
            Order Details - #{order.orderGroupId}
            <Badge
              variant={
                order.orderStatus === "Delivered"
                  ? "default"
                  : order.orderStatus === "Cancelled"
                    ? "destructive"
                    : "secondary"
              }
              className="capitalize"
            >
              {getStatusDisplayName(order.orderStatus)}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Timeline */}
          <div>
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Order Timeline
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Order Placed:</span>
                <span>{dayjs(order.createdAt).format("ddd, DD MMM YYYY - h:mm:ss A")}</span>
              </div>
              {order.acceptedTime && (
                <div className="flex justify-between">
                  <span>Accepted:</span>
                  <span>{dayjs(order.acceptedTime).format("ddd, DD MMM YYYY - h:mm:ss A")}</span>
                </div>
              )}
              {order.packedTime && (
                <div className="flex justify-between">
                  <span>Packed:</span>
                  <span>{dayjs(order.packedTime).format("ddd, DD MMM YYYY - h:mm:ss A")}</span>
                </div>
              )}
              {order.assignedTime && (
                <div className="flex justify-between">
                  <span>Assigned:</span>
                  <span>{dayjs(order.assignedTime).format("ddd, DD MMM YYYY - h:mm:ss A")}</span>
                </div>
              )}
              {order.pickedUpTime && (
                <div className="flex justify-between">
                  <span>Picked Up:</span>
                  <span>{dayjs(order.pickedUpTime).format("ddd, DD MMM YYYY - h:mm:ss A")}</span>
                </div>
              )}
              {order.deliveryStartTime && (
                <div className="flex justify-between">
                  <span>Dispatched:</span>
                  <span>{dayjs(order.deliveryStartTime).format("ddd, DD MMM YYYY - h:mm:ss A")}</span>
                </div>
              )}
              {order.deliveredTime && (
                <div className="flex justify-between">
                  <span>Delivered:</span>
                  <span>{dayjs(order.deliveredTime).format("ddd, DD MMM YYYY - h:mm:ss A")}</span>
                </div>
              )}
              <div className="flex justify-between font-medium">
                <span>Total Time:</span>
                <span>
                  {["Delivered", "Cancelled"].includes(order.orderStatus)
                    ? formatTimeElapsed(
                      getTimeDifferenceInSeconds(order.createdAt, order.deliveredTime || order.updatedAt),
                    )
                    : formatTimeElapsed(timeDiff)}
                </span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Basic Order Info */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Package className="w-4 h-4" />
                Order Information
              </h3>
              <div className="space-y-2 text-sm">
                <div>
                  <strong>Order ID:</strong> #{order.orderGroupId}
                </div>
                <div>
                  <strong>Network:</strong> {order.networkName}
                </div>
                <div>
                  <strong>Delivery Type:</strong> {order.deliveryType}
                </div>
                <div>
                  <strong>Logistics Provider:</strong> {order.logisticProvider}
                </div>
                {order.pos !== "none" && order.pos !== undefined && (
                  <div>
                    <strong>POS:</strong> {order.pos}
                  </div>
                )}
                {order.deliveryOtp && (
                  <div>
                    <strong>Delivery OTP:</strong> {order.deliveryOtp}
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <User className="w-4 h-4" />
                Customer Information
              </h3>
              <div className="space-y-2 text-sm">
                <div>
                  <strong>Name:</strong> {order.buyerName}
                </div>
                <div className="flex items-center gap-2">
                  <strong>Phone:</strong>
                  <span>{order.buyerMobile}</span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePhoneClick(order.buyerMobile)}
                    className="h-6 w-6 p-0"
                  >
                    <Phone className="w-3 h-3" />
                  </Button>
                </div>
                <div>
                  <strong>Address:</strong> {order.bAddress}
                </div>
                <div>
                  <strong>Area:</strong> {order.bAreaName}
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Restaurant Info */}
          <div>
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Store className="w-4 h-4" />
              Restaurant Information
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div>
                <div>
                  <strong>Name:</strong> {order.sellerName}
                </div>
                <div className="flex items-center gap-2">
                  <strong>Phone:</strong>
                  <span>{order.sellerMobile}</span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePhoneClick(order.sellerMobile)}
                    className="h-6 w-6 p-0"
                  >
                    <Phone className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              <div>
                {order.agentName && (
                  <div>
                    <strong>Agent:</strong> {order.agentName}
                  </div>
                )}
                {order.sellerMessage && (
                  <div>
                    <strong>Message:</strong> {order.sellerMessage}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Delivery Partner Info */}
          {order.logisticProvider === "MP2" && order.logisticDetails?.riderName && (
            <>
              <Separator />
              <div>
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Truck className="w-4 h-4" />
                  Delivery Partner
                </h3>
                <div className="text-sm space-y-2">
                  <div>
                    <strong>Name:</strong> {order.logisticDetails.riderName}
                  </div>
                  {order.logisticDetails.riderPhone && (
                    <div className="flex items-center gap-2">
                      <strong>Phone:</strong>
                      <span>{order.logisticDetails.riderPhone}</span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handlePhoneClick(order.logisticDetails!.riderPhone.toString())}
                        className="h-6 w-6 p-0"
                      >
                        <Phone className="w-3 h-3" />
                      </Button>
                    </div>
                  )}
                  {order.logStatus && (
                    <div>
                      <strong>Logistics Status:</strong>
                      <Badge className="ml-2">{getLogisticStatusDisplayName(order.logStatus)}</Badge>
                    </div>
                  )}
                  {order.logisticDetails.trackingUrl && (
                    <div>
                      <strong>Tracking:</strong>
                      <a
                        href={order.logisticDetails.trackingUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="ml-2 text-blue-600 hover:underline"
                      >
                        Track Order
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Order Items */}
          {order.orderDetails && order.orderDetails.length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="font-semibold mb-3">Order Items</h3>
                <div className="space-y-3">
                  {order.orderDetails.map((item) => (
                    <div key={item.orderId} className="border rounded p-3 bg-gray-50">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="font-medium">{item.itemName}</div>
                          {item.itemRegionalLanguageName && <div className="text-sm text-gray-600">{item.itemRegionalLanguageName}</div>}
                          {item.variationName && (
                            <div className="text-sm text-gray-600">Variation: {item.variationName}</div>
                          )}
                          {item.addOns && (
                            <div className="text-sm text-gray-600">
                              Add-ons:{" "}
                              {item.addOns
                                .flatMap((aog) => aog.addOnItemList.map((addon) => `${addon.name} (+₹${addon.price})`))
                                .join(", ")}
                            </div>
                          )}
                          <div className="text-xs text-gray-500 mt-1">
                            {item.diet && (
                              <span
                                className={`inline-block w-2 h-2 rounded-full mr-1 ${item.diet === "veg"
                                  ? "bg-green-500"
                                  : item.diet === "nonveg"
                                    ? "bg-red-500"
                                    : "bg-yellow-500"
                                  }`}
                              />
                            )}
                            {item.diet} • {item.unit}
                          </div>
                        </div>
                        <div className="text-right ml-4">
                          <div className="text-sm">Qty: {item.qty}</div>
                          <div className="text-sm">
                            ₹{item.pricePerUnit} per {item.unit}
                          </div>
                          <div className="font-semibold">
                            ₹{item.amount}
                            {(item.strikeOffAmount && item.strikeOffAmount !== item.amount) && (
                              <span className="ml-0.5 text-sm text-gray-600 line-through">
                                ₹{item.strikeOffAmount}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          <Separator />

          {/* Payment Breakdown */}
          <div>
            <h3 className="font-semibold mb-3">Payment Breakdown</h3>
            <div className="bg-gray-50 rounded p-4 space-y-2 text-sm">
              {(order.totalItemsStrikeoffAmount && (order.totalItemsStrikeoffAmount !== order.itemsTotalAmount)) && (
                <div className="flex justify-between">
                  <span>Item Total (strikeoff):</span>
                  <span>₹{order.totalItemsStrikeoffAmount}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span>Item Total:</span>
                <span>₹{order.itemsTotalAmount}</span>
              </div>
              <div className="flex justify-between">
                <span>Delivery Charge:</span>
                <span>₹{order.totalDeliveryCharge}</span>
              </div>
              <div className="flex justify-between">
                <span>Packaging Charges:</span>
                <span>₹{order.packagingCharges}</span>
              </div>
              <div className="flex justify-between">
                <span>Platform Fee:</span>
                <span>₹{order.platformFee}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax Amount:</span>
                <span>₹{order.totalTaxAmount}</span>
              </div>
              <div className="flex justify-between text-red-600">
                <span>Discount:</span>
                <span>-₹{order.totalDiscountAmount}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-semibold text-lg">
                <span>Total Amount:</span>
                <span>₹{order.totalOrderGroupAmount}</span>
              </div>
              <div className="flex justify-between text-sm text-gray-600">
                <span>COD Amount:</span>
                <span>₹{order.codAmount}</span>
              </div>
              {order.walletAmount > 0 && (
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Wallet Amount:</span>
                  <span>₹{order.walletAmount}</span>
                </div>
              )}
            </div>
          </div>

          {/* Support Tickets */}
          {order.supportTickets && order.supportTickets.length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="font-semibold mb-3">Support Tickets</h3>
                <div className="space-y-3">
                  {order.supportTickets.map((ticket) => (
                    <div key={ticket.ticketId} className="border rounded p-3">
                      <div className="flex justify-between items-start mb-2">
                        <div className="font-medium">Ticket #{ticket.ticketId}</div>
                        <Badge
                          variant={
                            ticket.status === "OPEN" ? "destructive" : ticket.status === "WIP" ? "default" : "secondary"
                          }
                        >
                          {ticket.status}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 mb-2">{ticket.description}</div>
                      <div className="text-xs text-gray-500">
                        Created: {new Date(ticket.createdDate).toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            {order.orderStatus === "Created" && (
              <Button variant="destructive" onClick={() => onAction(order, "Cancel Order")}>
                Cancel Order
              </Button>
            )}
            {showUpdateStatusButton(order) && (
              <Button variant="default" onClick={() => onAction(order, "Update LiveOrder Status")}>
                Update to {order.orderStatus === "Created" ? "Accepted" : order.orderStatus === "Accepted" ? "Packed" : order.orderStatus === "Packed" ? "Assigned" : order.orderStatus === "Assigned" ? "PickedUp" : "Dispatched"} <ArrowRight className="w-3 h-3" />
              </Button>
            )}
            {(order.orderStatus === "Dispatched") && (
              <Button variant="default" className="bg-blue-500 hover:bg-blue-500" onClick={() => onAction(order, "Mark Delivered")}>
                Mark Delivered
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}


// Action Modal
interface ActionModalProps {
  order: rNETOrder | null
  actionType: string
  onClose: () => void
  isSubmitting: boolean
  onSubmit: (formData: any) => void
}

export function ActionModal({ order, actionType, onClose, isSubmitting, onSubmit }: ActionModalProps) {
  const [formData, setFormData] = useState({
    // order cancel
    reason: "",
    // update status to packed
    boxes: "",
    bags: "",
    // mark delivered
    deliveryCode: "",
    creditAmount: "",
    boxesGiven: "",
    boxesTaken: ""
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  if (!order || !actionType) return null

  const renderCancelModal = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded">
        <AlertTriangle className="w-5 h-5 text-red-600" />
        <div>
          <p className="font-medium text-red-800">Cancel Order</p>
          <p className="text-sm text-red-600">
            This action is irreversible. The order will be cancelled from all systems.
          </p>
        </div>
      </div>

      <div>
        <Label htmlFor="reason">Cancellation Reason</Label>
        <Textarea
          id="reason"
          value={formData.reason}
          onChange={(e) => setFormData((prev) => ({ ...prev, reason: e.target.value }))}
          placeholder="Enter reason for cancellation..."
          className="mt-1 w-72 block border-2 rounded-md"
        />
      </div>

      <div className="flex gap-2 pt-4">
        <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button variant="destructive" onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting ? "Cancelling..." : "Confirm Cancellation"}
        </Button>
      </div>
    </div>
  )

  const renderUpdateStatusModal = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <AlertTriangle className="w-5 h-5 text-yellow-600" />
        <div>
          <p className="text-yellow-700">This will update the status of the order to {order.orderStatus === "Created" ? "Accepted" : order.orderStatus === "Accepted" ? "Packed" : order.orderStatus === "Packed" ? "Assigned" : order.orderStatus === "Assigned" ? "PickedUp" : "Dispatched"}.</p>
        </div>
      </div>
      {/* {order.orderStatus === "Accepted" && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="boxes">Number of Boxes</Label>
            <Input
              id="boxes"
              type="number"
              min={0}
              value={formData.boxes}
              onChange={(e) => setFormData((prev) => ({ ...prev, boxes: e.target.value }))}
              placeholder="Enter number of boxes"
            />
          </div>

          <div>
            <Label htmlFor="bags">Number of Bags</Label>
            <Input
              id="bags"
              type="number"
              min={0}
              value={formData.bags}
              onChange={(e) => setFormData((prev) => ({ ...prev, bags: e.target.value }))}
              placeholder="Enter number of bags"
            />
          </div>
        </div>
      )} */}

      <div className="flex gap-2 pt-4">
        <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Updating..." : "Confirm Update"}
        </Button>
      </div>
    </div>
  )

  const renderMarkDeliveredModal = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <AlertTriangle className="w-5 h-5 text-yellow-600" />
        <div>
          <p className="font-medium text-yellow-800">Mark Order as Delivered</p>
          <p className="text-sm text-yellow-600">This will mark the order as delivered.</p>
        </div>
      </div>
      {/*
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="deliveryCode">Delivery Code</Label>
          <Input
            id="deliveryCode"
            value={formData.deliveryCode}
            onChange={(e) => setFormData((prev) => ({ ...prev, deliveryCode: e.target.value }))}
            placeholder="Enter delivery code"
          />
        </div>

        <div>
          <Label htmlFor="creditAmount">Credit Amount</Label>
          <Input
            id="creditAmount"
            type="number"
            value={formData.creditAmount}
            onChange={(e) => setFormData((prev) => ({ ...prev, creditAmount: e.target.value }))}
            placeholder="Enter credit amount"
          />
        </div>

        <div>
          <Label htmlFor="boxesGiven">Number of Boxes Given</Label>
          <Input
            id="boxesGiven"
            type="number"
            min={0}
            value={formData.boxesGiven}
            onChange={(e) => setFormData((prev) => ({ ...prev, boxesGiven: e.target.value }))}
            placeholder="Enter number of boxes given"
          />
        </div>

        <div>
          <Label htmlFor="boxesTaken">Number of Boxes Taken</Label>
          <Input
            id="boxesTaken"
            type="number"
            min={0}
            value={formData.boxesTaken}
            onChange={(e) => setFormData((prev) => ({ ...prev, boxesTaken: e.target.value }))}
            placeholder="Enter number of boxes taken"
          />
        </div>
      </div> */}

      <div className="flex gap-2 pt-4">
        <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="bg-blue-500 hover:bg-blue-500"
        >
          {isSubmitting ? "Marking..." : "Confirm Delivery"}
        </Button>
      </div>
    </div>
  )

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl rounded-md">
        <DialogHeader>
          <DialogTitle>
            {actionType === "Cancel Order" && "Cancel Order"}
            {actionType === "Update LiveOrder Status" && "Update LiveOrder Status"}
            {actionType === "Mark Delivered" && "Mark Delivered"}
          </DialogTitle>
        </DialogHeader>

        {actionType === "Cancel Order" && renderCancelModal()}
        {actionType === "Update LiveOrder Status" && renderUpdateStatusModal()}
        {actionType === "Mark Delivered" && renderMarkDeliveredModal()}
      </DialogContent>
    </Dialog>
  )
}


export const getTimeDifferenceInSeconds = (orderTime: string, endTime?: string): number => {
  const orderDate = new Date(orderTime)
  const compareDate = endTime ? new Date(endTime) : new Date()
  return Math.floor((compareDate.getTime() - orderDate.getTime()) / 1000)
}


export const formatTimeElapsed = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    return `${days}d ${hours % 24}h`
  }
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  }
  return `${minutes}m`
}


export const getOrderCardStyling = (order: rNETOrder) => {
  const timeDiff = getTimeDifferenceInSeconds(order.createdAt)
  const baseClasses = "border rounded-lg transition-all duration-500 "
  let pulseClasses = ""
  let bgClasses = "bg-white hover:shadow-md "
  let helperText = ""

  // Status-based logic according to PRD with modern animations
  switch (order.orderStatus) {
    case "Created":
      if (timeDiff > 180) {
        bgClasses = "bg-gradient-to-br from-red-200 to-red-50 "
        pulseClasses = "card-alert-breathing "
        helperText = "🚨 URGENT: Order not yet accepted. Please inform the seller to either accept or cancel the order."
      } else if (timeDiff > 120) {
        bgClasses = "bg-gradient-to-br from-amber-200 to-amber-50 "
        pulseClasses = "card-alert-warning "
        helperText = "⚠️ Order yet to be accepted. Please inform the seller again."
      }
      break

    case "Accepted":
      if (order.logisticProvider === "MP2") {
        if (
          timeDiff > 900 &&
          order.logStatus &&
          ["LOG_CREATED", "LOG_PENDING", "LOG_SEARCHING_AGENT"].includes(order.logStatus)
        ) {
          bgClasses = "bg-gradient-to-br from-red-200 to-red-50 "
          pulseClasses = "card-alert-animated "
          helperText = "🚨 CRITICAL: Please highlight the order with mp2 team or start manual fulfilment of the order."
        } else if (
          timeDiff > 600 &&
          order.logStatus &&
          ["LOG_CREATED", "LOG_PENDING", "LOG_SEARCHING_AGENT"].includes(order.logStatus)
        ) {
          bgClasses = "bg-gradient-to-br from-orange-200 to-orange-50 "
          pulseClasses = "card-alert-warning "
          helperText = "⚡ Please highlight the order to mp2 team and start preparing for manual deliveries."
        } else if (timeDiff >= 600 && order.logStatus === "LOG_AGENT_ASSIGNED") {
          pulseClasses = "card-alert-warning "
          helperText = "📞 Please follow up with the rider and check if he is moving toward the restaurant."
        }
      } else if (order.logisticProvider === "SELF" && timeDiff > 900) {
        pulseClasses = "card-alert-warning "
        helperText = "🏪 Please follow up with the seller for self delivery."
      }
      break

    case "Packed":
      if (order.logisticProvider === "MP2") {
        if (timeDiff > 1200 && ["LOG_CREATED", "LOG_PENDING", "LOG_SEARCHING_AGENT"].includes(order.logStatus)) {
          bgClasses = "bg-gradient-to-br from-red-200 to-red-50 "
          pulseClasses = "card-alert-breathing "
          helperText = "🚨 URGENT: Please check with the delivery partner and raise the issue with the mp2 team."
        } else if (timeDiff > 1200 && order.logStatus === "LOG_AGENT_ASSIGNED") {
          bgClasses = "bg-gradient-to-br from-red-200 to-red-50 "
          pulseClasses = "card-alert-animated "
          helperText = "🏃‍♂️ Please follow up with the rider and check if he is moving toward the restaurant."
        }
      } else if (order.logisticProvider === "SELF" && timeDiff > 1200) {
        pulseClasses = "card-alert-warning "
        helperText = "🏃‍♂️ Please follow up with the seller for self delivery."
      }
      break
  }

  return {
    className: baseClasses + bgClasses + pulseClasses,
    helperText,
  }
}


export const getStatusDisplayName = (status: string): string => {
  switch (status) {
    case "Created":
      return "Acceptance Pending"
    case "Packed":
      return "Ready for Pickup"
    default:
      return status
  }
}


export const getLogisticStatusDisplayName = (logStatus: string): string => {
  return logStatus
    .replace("LOG_", "")
    .replace("_", " ")
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ")
}

export const showUpdateStatusButton = (order: rNETOrder) => {
  const isPos = order.pos !== "none" && order.pos !== undefined
  const isLogisticProvider = order.logisticProvider !== "SELF" && order.logisticProvider !== undefined
  if (isPos && isLogisticProvider) {
    return (order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "Packed" || order.orderStatus === "Assigned" || order.orderStatus === "PickedUp")
    // return false
  }
  if (!isPos && !isLogisticProvider) {
    return (order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "Packed" || order.orderStatus === "Assigned" || order.orderStatus === "PickedUp")
    // return (order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "Assigned" || order.orderStatus === "PickedUp")
  }
  if (isPos && !isLogisticProvider) {
    return (order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "Packed" || order.orderStatus === "Assigned" || order.orderStatus === "PickedUp")
    // return (order.orderStatus === "Assigned")
  }
  if (!isPos && isLogisticProvider) {
    return (order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "Packed" || order.orderStatus === "Assigned" || order.orderStatus === "PickedUp")
    // return (order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "PickedUp")
  }
  return (order.orderStatus === "Created" || order.orderStatus === "Accepted" || order.orderStatus === "Packed" || order.orderStatus === "Assigned" || order.orderStatus === "PickedUp")
}