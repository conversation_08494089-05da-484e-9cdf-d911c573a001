// File: app/constants/whatsappTemplates.ts
export const REQUIRED_TEMPLATES = [
  {
    name: "f_business_order_confirm",
    language: "en",
    category: "UTILITY",
    components: [
      {
        type: "BODY",
        text: "Hello {{1}} 👋,\nThank you for your order! 🛒 We've received your request for the following items:\n\n{{2}}\n\nYour order *#{{3}}* will be delivered tomorrow morning. 🚚\nTotal payable: ₹{{4}}. 💰\n\nThanks for choosing farmersMandi ! 🙏\n\nBest,\nfarmersMandi",
        example: {
          body_text: [
            ["John", "• Tomatoes + 1 PC  • Potatoes + 2 PC", "1234", "500"],
          ],
        },
      },
    ],
  },
  {
    name: "mnet_seller_order_placed",
    language: "en",
    category: "UTILITY",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "New order recieved ",
      },
      {
        type: "BODY",
        text: `:new: New Order Alert! \n Hi {{4}}, \nYou have a new order from {{3}}! \n :shopping_trolley: Order ID: {{1}} \n :moneybag: Amount: ₹{{2}} \n Please start processing the order promptly! :rocket:`,
        example: {
          body_text: [["100", "1024", "SellerName", "buyerName"]],
        },
      },
    ],
  },
  {
    name: "pending_dues_buyer_1",
    language: "en",
    category: "UTILITY",
    components: [
      {
        type: "BODY",
        text: "Hi {{1}},\nYou have pending dues with us. Please open the app to review and clear them as soon as possible. Ignore if already paid.\nThank you !",
        example: {
          body_text: [["John"]],
        },
      },
    ],
  },
  {
    name: "hello_mnet",
    language: "en",
    category: "UTILITY",
    components: [
      {
        type: "BODY",
        text: "Hello, thanks for requesting an introductory call from us. We will be in touch soon.",
      },
    ],
  },
  {
    name: "hello_world",
    language: "en_US",
    category: "UTILITY",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Hello World",
      },
      {
        type: "BODY",
        text: "Welcome and congratulations!! This message demonstrates your ability to send a WhatsApp message notification from the Cloud API, hosted by Meta. Thank you for taking the time to test with us.",
      },
      {
        type: "FOOTER",
        text: "WhatsApp Business Platform sample message",
      },
    ],
  },
  {
    name: "delivery_completed",
    language: "en",
    category: "UTILITY",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Delivery Completed",
      },
      {
        type: "BODY",
        text: "Hi {{1}} 👋,\nYour order *#{{2}}* has been delivered. ✅ We hope you're happy with the fresh produce! 🥗\n\nTotal amount paid: *₹{{3}}*. 💰\nThanks for choosing us! 🙏\n\nBest,\nfarmersMandi",
        example: {
          body_text: [["John", "123456", "4005.70"]],
        },
      },
    ],
  },
  {
    name: "delivery_confirmation_with_credit",
    language: "en",
    category: "UTILITY",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Delivery Confirmation",
      },
      {
        type: "BODY",
        text: "Hi {{1}} 👋,\nYour order #{{2}} has been delivered. ✅\nOrder Amount : ₹{{3}}\n\nTotal Amount due: *₹{{4}}*. 💰\nPlease pay soon. 💳\n\nBest,\nfarmersMandi",
        example: {
          body_text: [["John", "1234", "100.86", "8045.76"]],
        },
      },
    ],
  },
  {
    name: "complete_order_cancellation",
    language: "en",
    category: "UTILITY",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order Cancellation",
      },
      {
        type: "BODY",
        text: "Dear {{1}},\n\nWe're sorry, but your order #{{2}} couldn't be fulfilled due to stock unavailability.❌\n\nWe apologize for the inconvenience and hope to serve you again. 🙏\n\nBest,\nfarmersMandi",
        example: {
          body_text: [["John", "12345"]],
        },
      },
    ],
  },
  {
    name: "out_for_delivery",
    language: "en",
    category: "UTILITY",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Out for Delivery",
      },
      {
        type: "BODY",
        text: "Good morning {{1}}! 🌅\nYour order #{{2}} is on the way and will be delivered soon. 🚚\n\nTotal payable on delivery: *₹{{3}}*\n\nThanks for choosing us! 🙏\n\nBest,\nfarmersMandi",
        example: {
          body_text: [["John", "12345", "5090.86"]],
        },
      },
    ],
  },
  {
    "name": "early_pending_payment_special_offer",
    "language": "en",
    "category": "UTILITY",
    "allow_category_change": true,
    "parameter_format": "NAMED",
    "components": [
      {
        "type": "HEADER",
        "format": "TEXT",
        "text": "Settle Balance & Save!"
      },
      {
        "type": "BODY",
        "text": "*🎉 Hi {{customer_name}},*\n\nYour account shows an outstanding balance of *₹{{pending_amount}}*. We’re offering an *exclusive discount* or priority services for settling this balance by *{{due_date}}* !\n\nThis balance reflects all partial payments made and outstanding amounts. Act now to unlock the benefits!",
        "example": {
          "body_text_named_params": [
            {
              "param_name": "customer_name",
              "example": "John"
            },
            {
              "param_name": "pending_amount",
              "example": "5032.45"
            },
            {
              "param_name": "due_date",
              "example": "2nd Feb '25"
            }
          ]
        }
      },
      {
        "type": "FOOTER",
        "text": "Don’t miss this limited-time offer!"
      },
      {
        "type": "BUTTONS",
        "buttons": [
          {
            "type": "QUICK_REPLY",
            "text": "Pay Now"
          },
          {
            "type": "QUICK_REPLY",
            "text": "Request Details"
          }
        ]
      }
    ]
  },
  {
    "name": "pending_payment_reminder",
    "language": "en",
    "category": "UTILITY",
    "allow_category_change": true,
    "parameter_format": "NAMED",
    "components": [
      {
        "type": "HEADER",
        "format": "TEXT",
        "text": "Outstanding Balance: ₹{{pending_amount}}",
        "example": {
          "header_text_named_params": [
            {
              "param_name": "pending_amount",
              "example": "250"
            }
          ]
        }
      },
      {
        "type": "BODY",
        "text": "*👋 Dear {{customer_name}},*\n\nWe’d like to remind you that your current outstanding balance with us is *₹{{pending_amount}}* as of *{{today_date}}*.\n\nThis may include multiple orders, and we appreciate your partial payments made. To ensure your account remains in good standing, we kindly request you to settle the remaining amount.\n\nTap below to clear your balance or reach out for a detailed breakdown.",
        "example": {
          "body_text_named_params": [
            {
              "param_name": "customer_name",
              "example": "John"
            },
            {
              "param_name": "pending_amount",
              "example": "250"
            },
            {
              "param_name": "today_date",
              "example": "28 Jan '25"
            }
          ]
        }
      },
      {
        "type": "FOOTER",
        "text": "We’re here to assist with any questions."
      },
      {
        "type": "BUTTONS",
        "buttons": [
          {
            "type": "QUICK_REPLY",
            "text": "Clear Dues"
          },
          {
            "type": "QUICK_REPLY",
            "text": "Request Details"
          }
        ]
      }
    ]
  },
  {
    "name": "open_for_ordering",
    "language": "en",
    "category": "MARKETING",
    "allow_category_change": true,
    "parameter_format": "NAMED",
    "components": [
      {
        "type": "HEADER",
        "format": "TEXT",
        "text": "Fresh, Fast, and Affordable!"
      },
      {
        "type": "BODY",
        "text": "*🍎 Hello,*\n\nWe’re open for orders! Explore our wide range of products:\n\n🍅 Fresh Fruits & Vegetables\n🥖 Daily Groceries & Staples\n🧴 Personal Care Products\n🧃 Beverages and more!\n\nShop today for premium quality and hassle-free delivery."
      },
      {
        "type": "FOOTER",
        "text": "Shop now, and let us deliver to your doorstep."
      },
      {
        "type": "BUTTONS",
        "buttons": [
          {
            "type": "QUICK_REPLY",
            "text": "Start Shopping"
          },
          {
            "type": "QUICK_REPLY",
            "text": "Contact Us"
          }
        ]
      }
    ]
  },
  {
    "name": "item_discount_trial_offer",
    "language": "en",
    "category": "MARKETING",
    "parameter_format": "NAMED",
    "components": [
        {
            "type": "HEADER",
            "format": "IMAGE",
            "example": {
                "header_handle": [
                    "4:dG9tYXRvLmpwZw==:aW1hZ2UvanBlZw==:ARa6rojXaNTQ_RzAjAWcDdba8XD-pb3o_Ofzv_c4t4V1qqWsKT8jYbKYHo68H59GyH-KMEYXCsBVqKa09Z6mRd4gNg-J7fy0kuOBxt0qdBLR_w:e:1740257819:522237886866403:61564876907669:ARayWjrjjP_GfZltzcg"
                ]
            }
        },
        {
            "type": "BODY",
            "text": "*🎉 Exclusive Offer Just for You!* Grab a limited-time deal on *{{item_name}}* now! 🎁 Save more while enjoying the best quality products. 🛒 Don't miss out—this offer ends soon!",
            "example": {
                "body_text_named_params": [
                    {
                        "param_name": "item_name",
                        "example": "Potato"
                    }
                ]
            }
        },
        {
            "type": "BUTTONS",
            "buttons": [
                {
                    "type": "QUICK_REPLY",
                    "text": "Claim Discount Now"
                },
                {
                    "type": "URL",
                    "text": "Visit website",
                    "url": "https://mnetonline.in/{{1}}",
                    "example": [
                        "https://mnetonline.in/wa_marketing?seller_id=4"
                    ]
                }
            ]
        }
    ]
  },
];


export const B2B_TEMPLATES = [  // ===================================================
  // B2B Templates
  // ===================================================

  // 1. B2B Order Placed (Quick Commerce)
  {
    name: "b2b_order_placed_confirmed_quick_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Great news we - just got your order!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nThanks a lot for your order! Your order *#{{order_id}}* is confirmed and will be on its way by *{{expt_delivery_time}}* today.\n\n*Order Details:*\n{{order_details}}\n\nTotal: *₹{{total_amount}}*\n\nWe really appreciate you working with us.\n\nCheers,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Acme Corp" },
            { param_name: "business_name", example: "FreshTech Services" },
            { param_name: "order_id", example: "B12345" },
            { param_name: "expt_delivery_time", example: "7:30 PM" },
            { param_name: "order_details", example: "- 10 Apples\n- 5 Oranges" },
            { param_name: "total_amount", example: "1500" }
          ]
        }
      }
    ]
  },

  // 2. B2B Order Placed (Standard / Next Day Delivery)
  // {
  //   name: "b2b_order_placed_confirmed_standard_v2",
  //   language: "en",
  //   category: "UTILITY",
  //   allow_category_change: true,
  //   parameter_format: "NAMED",
  //   components: [
  //     {
  //       type: "HEADER",
  //       format: "TEXT",
  //       text: "🛒 New order received!"
  //     },
  //     {
  //       type: "BODY",
  //       text: "Hi *{{customer_name}}*,\n\nThanks for your order! Your order *#{{order_id}}* is confirmed. Just a quick note: orders placed after *8 PM* will be delivered by *6 AM* tomorrow.\n\n*Order Details:*\n{{order_details}}\n\nTotal: *₹{{total_amount}}*\n\nWe’re grateful for your continued trust.\n\nBest,\n*{{business_name}}*",
  //       example: {
  //         body_text_named_params: [
  //           { param_name: "customer_name", example: "Acme Corp" },
  //           { param_name: "business_name", example: "FreshTech Services" },
  //           { param_name: "order_id", example: "B67890" },
  //           { param_name: "order_details", example: "- 20 Bananas\n- 2 Mangoes" },
  //           { param_name: "total_amount", example: "2300" }
  //         ]
  //       }
  //     }
  //   ]
  // },

  // 3. B2B Order Out for Delivery
  {
    name: "b2b_order_out_for_delivery_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Your order is on its way!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nYour order *#{{order_id}}* is out for delivery and should reach you by *{{delivery_time}}*.\n\nThanks for partnering with us.\n\nRegards,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Acme Corp" },
            { param_name: "business_name", example: "FreshTech Services" },
            { param_name: "order_id", example: "B12345" },
            { param_name: "delivery_time", example: "8:00 PM" }
          ]
        }
      }
    ]
  },

  // 4. B2B Order Delivered (Standard – Payment Received)
  {
    name: "b2b_order_delivered_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Delivery complete!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe’re happy to let you know that your order *#{{order_id}}* has been delivered successfully. We hope everything meets your expectations!\n\n*Total Paid:* *₹{{paid_amount}}*\n\nThank you for doing business with us.\n\nWarm regards,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Acme Corp" },
            { param_name: "business_name", example: "FreshTech Services" },
            { param_name: "order_id", example: "B12345" },
            { param_name: "paid_amount", example: "1500" }
          ]
        }
      }
    ]
  },

  // 4a. B2B Order Delivered with Credit (Payment to be repaid later)
  {
    name: "b2b_order_delivered_credit_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order delivered – Payment on Credit"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nYour order *#{{order_id}}* has been delivered on credit. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\n\nThank you for your continued partnership.\n\nBest regards,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Acme Corp" },
            { param_name: "business_name", example: "FreshTech Services" },
            { param_name: "order_id", example: "B12345" },
            { param_name: "order_amount", example: "1500" },
            { param_name: "total_pending_amount", example: "500" }
          ]
        }
      }
    ]
  },

  // 5. B2B Order Cancelled (with Refund, if applicable)
  {
    name: "b2b_order_cancelled_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order Cancellation Notice"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe’re sorry to inform you that your order *#{{order_id}}* has been cancelled due to *{{cancellation_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\n\nWe apologize for any inconvenience and thank you for your understanding.\n\nBest regards,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Acme Corp" },
            { param_name: "business_name", example: "FreshTech Services" },
            { param_name: "order_id", example: "B98765" },
            { param_name: "cancellation_reason", example: "stock issues" },
            { param_name: "refund_amount", example: "1500" }
          ]
        }
      }
    ]
  },

  // 6. B2B Payment Successful
  {
    name: "b2b_payment_success_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Payment received – Thank you!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe’ve received your payment of *₹{{paid_amount}}* for order *#{{order_id}}*. Everything is set on our end!\n\nThanks for your prompt action.\n\nCheers,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Acme Corp" },
            { param_name: "business_name", example: "FreshTech Services" },
            { param_name: "order_id", example: "B12345" },
            { param_name: "paid_amount", example: "1500" }
          ]
        }
      }
    ]
  },

  // 7. B2B Payment Failed
  {
    name: "b2b_payment_failed_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Payment issue – Lets sort it out"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe couldn’t process the payment for order *#{{order_id}}* (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\n\nThank you for your patience,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Acme Corp" },
            { param_name: "business_name", example: "FreshTech Services" },
            { param_name: "order_id", example: "B12345" },
            { param_name: "attempted_amount", example: "1500" }
          ]
        }
      }
    ]
  },

  // 8. B2B Prepaid but Order Unconfirmed (Cancelled & Refund Processed)
  {
    name: "b2b_prepaid_unconfirmed_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order not confirmed – Cancelled"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe received your payment of *₹{{paid_amount}}* for order *#{{order_id}}*, but we couldn’t confirm the order due to *{{reason}}*. As a result, the order has been cancelled and a refund of *₹{{refund_amount}}* has been processed.\n\nThanks for your patience,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Acme Corp" },
            { param_name: "business_name", example: "FreshTech Services" },
            { param_name: "order_id", example: "B12345" },
            { param_name: "paid_amount", example: "1500" },
            { param_name: "reason", example: "technical issues" },
            { param_name: "refund_amount", example: "1500" }
          ]
        }
      }
    ]
  }
]

export const B2C_TEMPLATES = [
  // ===================================================
  // B2C Grocery / Supermarket Templates
  // ===================================================

  // 1. B2C Grocery Order Placed / Confirmed
  {
    name: "b2c_grocery_order_placed_confirmed_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order confirmed – Thank you!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nThanks so much for your order! Your order *#{{order_id}}* is confirmed and will reach you by *{{expt_delivery_time}}*.\n\n*Your Items:*\n{{order_details}}\n\nTotal: *₹{{total_amount}}*\n\nWe’re excited to serve you and hope you enjoy your shopping experience with us.\n\nTake care,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Jane Doe" },
            { param_name: "business_name", example: "MarketFresh" },
            { param_name: "order_id", example: "C54321" },
            { param_name: "expt_delivery_time", example: "5:30 PM" },
            { param_name: "order_details", example: "- 2 Loaves of Bread\n- 1 Liter Milk" },
            { param_name: "total_amount", example: "350" }
          ]
        }
      }
    ]
  },

  // 2. B2C Grocery Order Out for Delivery
  {
    name: "b2c_grocery_order_out_for_delivery_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Your order is on the way!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nYour order *#{{order_id}}* is out for delivery and should reach you by *{{delivery_time}}*. We’re looking forward to delighting you with our service!\n\nBest,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Jane Doe" },
            { param_name: "business_name", example: "MarketFresh" },
            { param_name: "order_id", example: "C54321" },
            { param_name: "delivery_time", example: "6:00 PM" }
          ]
        }
      }
    ]
  },

  // 3. B2C Grocery Order Delivered (Standard – Payment Received)
  {
    name: "b2c_grocery_order_delivered_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Delivery complete – Enjoy your day!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nGreat news – your order *#{{order_id}}* has been delivered. We hope everything is perfect!\n\n*Total Paid:* *₹{{paid_amount}}*\n\nThank you for choosing us. We value your trust!\n\nWarm wishes,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Jane Doe" },
            { param_name: "business_name", example: "MarketFresh" },
            { param_name: "order_id", example: "C54321" },
            { param_name: "paid_amount", example: "350" }
          ]
        }
      }
    ]
  },

  // 3a. B2C Grocery Order Delivered with Credit (Payment to be repaid later)
  {
    name: "b2c_grocery_order_delivered_credit_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order delivered – Payment on Credit"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nYour order *#{{order_id}}* has been delivered on credit. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\n\nThank you for shopping with us.\n\nWarm regards,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Jane Doe" },
            { param_name: "business_name", example: "MarketFresh" },
            { param_name: "order_id", example: "C54321" },
            { param_name: "order_amount", example: "350" },
            { param_name: "total_pending_amount", example: "100" }
          ]
        }
      }
    ]
  },

  // 4. B2C Grocery Order Cancelled (with Refund, if applicable)
  {
    name: "b2c_grocery_order_cancelled_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order cancelled"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe’re sorry, but your order *#{{order_id}}* has been cancelled due to *{{cancellation_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\n\nThank you for understanding,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Jane Doe" },
            { param_name: "business_name", example: "MarketFresh" },
            { param_name: "order_id", example: "C54321" },
            { param_name: "cancellation_reason", example: "unexpected delay" },
            { param_name: "refund_amount", example: "350" }
          ]
        }
      }
    ]
  },

  // 5. B2C Grocery Payment Successful
  {
    name: "b2c_grocery_payment_success_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Payment received – Thank you!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe’ve received your payment of *₹{{paid_amount}}* for order *#{{order_id}}*. Your order is now being processed for delivery.\n\nThank you for shopping with us.\n\nBest,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Jane Doe" },
            { param_name: "business_name", example: "MarketFresh" },
            { param_name: "order_id", example: "C54321" },
            { param_name: "paid_amount", example: "350" }
          ]
        }
      }
    ]
  },

  // 6. B2C Grocery Payment Failed
  {
    name: "b2c_grocery_payment_failed_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Payment issue – Lets fix it"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe couldn’t process the payment for order *#{{order_id}}* (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\n\nThanks for your understanding,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Jane Doe" },
            { param_name: "business_name", example: "MarketFresh" },
            { param_name: "order_id", example: "C54321" },
            { param_name: "attempted_amount", example: "350" }
          ]
        }
      }
    ]
  },

  // 7. B2C Grocery Prepaid but Order Unconfirmed (Cancelled & Refund Processed)
  {
    name: "b2c_grocery_prepaid_unconfirmed_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order not confirmed – Cancelled"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe received your payment of *₹{{paid_amount}}* for order *#{{order_id}}*, but we couldn’t confirm the order due to *{{reason}}*. Consequently, the order has been cancelled{{#if refund_amount}} and a refund of *₹{{refund_amount}}* has been processed{{/if}}.\n\nThanks for your patience,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Jane Doe" },
            { param_name: "business_name", example: "MarketFresh" },
            { param_name: "order_id", example: "C54321" },
            { param_name: "paid_amount", example: "350" },
            { param_name: "reason", example: "order volume" },
            { param_name: "refund_amount", example: "350" }
          ]
        }
      }
    ]
  },

  // 8. B2C Grocery Item Cancellation (with Refund, if applicable)
  {
    name: "b2c_grocery_item_cancelled_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Item cancellation notice"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe’re sorry to let you know that the item *{{item_name}}* in your order *#{{order_id}}* has been cancelled due to *{{cancel_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* for this item has been processed.{{/if}}\n\nWe appreciate your understanding.\n\nRegards,\n*{{business_name}}*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "Jane Doe" },
            { param_name: "business_name", example: "MarketFresh" },
            { param_name: "item_name", example: "Organic Bananas" },
            { param_name: "order_id", example: "C54321" },
            { param_name: "cancel_reason", example: "stock shortage" },
            { param_name: "refund_amount", example: "150" }
          ]
        }
      }
    ]
  },

  // 9. B2C Grocery Payment Refund (handled within cancellation scenarios)
  // (Optional extra reminder message if needed; merge as desired)
]

export const B2C_RESTAURANT_TEMPLATES = [

  // ===================================================
  // B2C Restaurant Templates
  // ===================================================

  // 1. B2C Restaurant Order Placed / Confirmed
  {
    name: "b2c_restaurant_order_placed_confirmed_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order confirmed – Bon appetit!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}* 👋,\n\nThanks so much for your order! 🙏 \n  ✅  Your order *#{{order_id}}* has been confirmed and your delicious meal will be delivered in 🕒 *{{expt_delivery_time}} mins*.\n\n* 🍽 Meal Details:*\n{{order_details}}\n\nTotal: *₹{{total_amount}}*\n\n 😋 We can’t wait for you to enjoy your meal.\n\nWarm regards,\n*_business_name_*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "John Doe" },
            // { param_name: "business_name", example: "DineFine" },
            { param_name: "order_id", example: "R12345" },
            { param_name: "expt_delivery_time", example: "45" },
            { param_name: "order_details", example: "- Caesar Salad \n- Grilled Chicken" },
            { param_name: "total_amount", example: "800" }
          ]
        }
      }
    ]
  },

  // 2. B2C Restaurant Order Out for Delivery
  {
    name: "b2c_restaurant_order_out_for_delivery_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Your meal is on its way!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}* 👋,\n\n 📦 Your order _#{{order_id}}_ is now out for delivery and should arrive in 🕢 *{{delivery_time}} mins*.\n\n 🍽️ We hope you enjoy your meal!\n\nCheers,\n*_business_name_*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "John Doe" },
            // { param_name: "business_name", example: "DineFine" },
            { param_name: "order_id", example: "R12345" },
            { param_name: "delivery_time", example: "7:30 PM" }
          ]
        }
      }
    ]
  },

  // 3. B2C Restaurant Order Delivered (Standard – Payment Received)
  {
    name: "b2c_restaurant_order_delivered_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Enjoy your meal – It’s delivered!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}* 🎉,\n\n ✅ We’re delighted to let you know that your order _#{{order_id}}_ has been delivered 🚚. \n 🍽️ We hope the food is delicious and meets your expectations!\n\n*Total Paid:* *₹{{paid_amount}}*\n\n 🙏 Thank you for choosing us. Bon appétit!\n\nBest,\n*_business_name_*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "John Doe" },
            // { param_name: "business_name", example: "DineFine" },
            { param_name: "order_id", example: "R12345" },
            { param_name: "paid_amount", example: "800" }
          ]
        }
      }
    ]
  },

  // 3a. B2C Restaurant Order Delivered with Credit (Payment to be repaid later)
  {
    name: "b2c_restaurant_order_delivered_credit_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order delivered – Payment on Credit"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}* 🎉,\n\nYour order _#{{order_id}}_ has been delivered on credit ✅. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\n\nWe appreciate your patronage and look forward to serving you again soon.\n\nBest,\n*_business_name_*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "John Doe" },
            // { param_name: "business_name", example: "DineFine" },
            { param_name: "order_id", example: "R12345" },
            { param_name: "order_amount", example: "800" },
            { param_name: "total_pending_amount", example: "200" }
          ]
        }
      }
    ]
  },

  // 4. B2C Restaurant Order Cancelled (with Refund, if applicable)
  {
    name: "b2c_restaurant_order_cancelled_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order cancelled"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe’re sorry to inform you that your order *#{{order_id}}* has been cancelled due to *{{cancellation_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\n\nWe apologize for the inconvenience and hope to serve you better next time.\n\nRegards,\n*_business_name_*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "John Doe" },
            // { param_name: "business_name", example: "DineFine" },
            { param_name: "order_id", example: "R12345" },
            { param_name: "cancellation_reason", example: "kitchen delay" },
            { param_name: "refund_amount", example: "800" }
          ]
        }
      }
    ]
  },

  // 5. B2C Restaurant Payment Successful
  {
    name: "b2c_restaurant_payment_success_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Payment received – Thank you!"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\n We’ve received your payment of *₹{{paid_amount}}* for order _*#{{order_id}}*_. Your meal is now being prepared and will soon be on its way to you.\n\nThank you for dining with us,\n*_business_name_*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "John Doe" },
            // { param_name: "business_name", example: "DineFine" },
            { param_name: "order_id", example: "R12345" },
            { param_name: "paid_amount", example: "800" }
          ]
        }
      }
    ]
  },

  // 6. B2C Restaurant Payment Failed
  {
    name: "b2c_restaurant_payment_failed_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Payment issue – Lets fix it"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\n We couldn’t process the payment for order *#{{order_id}}* (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\n\nThanks for your understanding,\n*_business_name_*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "John Doe" },
            // { param_name: "business_name", example: "DineFine" },
            { param_name: "order_id", example: "R12345" },
            { param_name: "attempted_amount", example: "800" }
          ]
        }
      }
    ]
  },

  // 7. B2C Restaurant Prepaid but Order Unconfirmed (Cancelled & Refund Processed)
  {
    name: "b2c_restaurant_prepaid_unconfirmed_v2",
    language: "en",
    category: "UTILITY",
    allow_category_change: true,
    parameter_format: "NAMED",
    components: [
      {
        type: "HEADER",
        format: "TEXT",
        text: "Order not confirmed – Cancelled"
      },
      {
        type: "BODY",
        text: "Hi *{{customer_name}}*,\n\nWe received your payment of *₹{{paid_amount}}* for order *#{{order_id}}*, but we couldn’t confirm the order due to *{{reason}}*. Consequently, the order has been cancelled{{#if refund_amount}} and a refund of *₹{{refund_amount}}* has been processed{{/if}}.\n\nThanks for your patience,\n*_business_name_*",
        example: {
          body_text_named_params: [
            { param_name: "customer_name", example: "John Doe" },
            // { param_name: "business_name", example: "DineFine" },
            { param_name: "order_id", example: "R12345" },
            { param_name: "paid_amount", example: "800" },
            { param_name: "reason", example: "busy kitchen" },
            { param_name: "refund_amount", example: "800" }
          ]
        }
      }
    ]
  }
];