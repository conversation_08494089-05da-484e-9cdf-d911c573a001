import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as mime from 'mime-types';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Replace these with your actual values
const APP_ID: string = process.env.FACEBOOK_APP_ID || '';
const ACCESS_TOKEN: string = process.env.WHATSAPP_TOKEN || '';

console.log("APP_ID",APP_ID, "ACCESS_TOKEN" , ACCESS_TOKEN);

// Path to the local file you want to upload
const filePath: string = '/Users/<USER>/Downloads/tomato.jpg'; // Update with your file path
const fileName: string = path.basename(filePath);
const fileType: string = (mime.lookup(filePath) as string) || 'application/octet-stream';
const fileStats = fs.statSync(filePath);
const fileLength: number = fileStats.size;

interface StartSessionResponse {
  id: string;
  // Additional response fields can be added here if needed
}

async function uploadImage(): Promise<void> {
  try {
    // STEP 1: Start an upload session
    const startSessionUrl = `https://graph.facebook.com/v22.0/${APP_ID}/uploads`;
    const startParams = {
      file_name: fileName,
      file_length: fileLength,
      file_type: fileType,
      access_token: ACCESS_TOKEN,
    };

    console.log('Starting upload session...');
    const startResponse = await axios.post<StartSessionResponse>(startSessionUrl, null, {
      params: startParams,
    });
    console.log('Upload session response:', startResponse.data);

    // Extract the upload session ID from the response.
    const uploadSessionId: string | undefined = startResponse.data.id;
    if (!uploadSessionId) {
      throw new Error('No upload_session_id found in the response.');
    }

    // STEP 2: Upload the file binary
    const uploadUrl = `https://graph.facebook.com/v22.0/${uploadSessionId}`;
    const fileBuffer: Buffer = fs.readFileSync(filePath);

    console.log('Uploading file...');
    const uploadResponse = await axios.post(uploadUrl, fileBuffer, {
      headers: {
        'Authorization': `OAuth ${ACCESS_TOKEN}`,
        'file_offset': '0', // Starting at offset 0
        'Content-Type': 'application/octet-stream',
      },
    });

    console.log('File upload response:', uploadResponse.data);

    // Optionally, save the response to a file
    fs.writeFileSync('upload_response.json', JSON.stringify(uploadResponse.data, null, 2));
    console.log('Upload response saved to upload_response.json');
  } catch (error: any) {
    if (axios.isAxiosError(error) && error.response) {
      console.error('Error response:', error.response.data);
    } else if (error instanceof Error) {
      console.error('Error:', error.message);
    } else {
      console.error('Unknown error occurred.');
    }
  }
}

uploadImage().then(() => {
  console.log('Upload completed');
}).catch((error) => {
  console.error('Error:', error);
});
