import React, { useEffect } from "react";
import { FreeItemFormData } from "~/types/api/businessConsoleService/FreeItem";

interface FreeItemFormProps {
  initialData?: FreeItemFormData;
  onSubmit: (data: FreeItemFormData) => void;
}

const defaultValues: FreeItemFormData = {
  discountType: "percentage",
  discountPercentage: null,
  discountFlat: null,
  discountUpto: null,
  discountMinOrderQty: null,
  validFrom: new Date(),
  validTo: new Date(new Date().setMonth(new Date().getMonth() + 1)),
  discountDisabled: false,
  freeItemId: null,
  freeItemQty: null,
};

const FreeItemForm: React.FC<FreeItemFormProps> = ({ initialData, onSubmit }) => {
  const [form, setForm] = React.useState<FreeItemFormData>(initialData || defaultValues);

  useEffect(() => {
    if (initialData) setForm(initialData);
  }, [initialData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const target = e.target as HTMLInputElement;
    const { name, value, type } = target;
    setForm((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? target.checked : value,
    }));
  };

  const handleDateChange = (name: keyof FreeItemFormData, value: string) => {
    setForm((prev) => ({ ...prev, [name]: new Date(value) }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(form);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block font-medium mb-1">Discount Type</label>
        <select
          name="discountType"
          value={form.discountType}
          onChange={handleChange}
          className="w-full border rounded px-3 py-2"
        >
          <option value="percentage">Percentage</option>
          <option value="flat">Flat</option>
          <option value="freeitem">Free Item</option>
        </select>
      </div>

      {form.discountType === "percentage" && (
        <>
          <div>
            <label className="block font-medium mb-1">Discount Percentage (%)</label>
            <input
              type="number"
              name="discountPercentage"
              min={0}
              max={100}
              value={form.discountPercentage ?? ""}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              placeholder="Enter percentage"
            />
          </div>
          <div>
            <label className="block font-medium mb-1">Discount Up To (₹)</label>
            <input
              type="number"
              name="discountUpto"
              min={0}
              value={form.discountUpto ?? ""}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              placeholder="Enter maximum discount amount"
            />
          </div>
        </>
      )}

      {form.discountType === "flat" && (
        <div>
          <label className="block font-medium mb-1">Flat Discount Amount (₹)</label>
          <input
            type="number"
            name="discountFlat"
            min={0}
            value={form.discountFlat ?? ""}
            onChange={handleChange}
            className="w-full border rounded px-3 py-2"
            placeholder="Enter flat discount amount"
          />
        </div>
      )}

      {form.discountType === "freeitem" && (
        <>
          <div>
            <label className="block font-medium mb-1">Free Item</label>
            <input
              type="text"
              name="freeItemId"
              value={form.freeItemId ?? ""}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              placeholder="Enter free item ID"
            />
          </div>
          <div>
            <label className="block font-medium mb-1">Free Item Quantity</label>
            <input
              type="number"
              name="freeItemQty"
              min={1}
              value={form.freeItemQty ?? ""}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2"
              placeholder="Enter quantity"
            />
          </div>
        </>
      )}

      <div>
        <label className="block font-medium mb-1">Minimum Order Amount (₹)</label>
        <input
          type="number"
          name="discountMinOrderQty"
          min={0}
          value={form.discountMinOrderQty ?? ""}
          onChange={handleChange}
          className="w-full border rounded px-3 py-2"
          placeholder="Enter minimum order amount"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block font-medium mb-1">Valid From</label>
          <input
            type="date"
            name="validFrom"
            value={form.validFrom ? new Date(form.validFrom).toISOString().split("T")[0] : ""}
            onChange={(e) => handleDateChange("validFrom", e.target.value)}
            className="w-full border rounded px-3 py-2"
          />
        </div>
        <div>
          <label className="block font-medium mb-1">Valid To</label>
          <input
            type="date"
            name="validTo"
            value={form.validTo ? new Date(form.validTo).toISOString().split("T")[0] : ""}
            onChange={(e) => handleDateChange("validTo", e.target.value)}
            className="w-full border rounded px-3 py-2"
          />
        </div>
      </div>

      <div className="flex items-center gap-2">
        <input
          type="checkbox"
          name="discountDisabled"
          checked={form.discountDisabled}
          onChange={handleChange}
          className="h-4 w-4"
        />
        <label className="font-medium">Disable Discount</label>
      </div>

      <div className="flex justify-end space-x-4 pt-4">
        <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded">
          {initialData?.id ? "Update" : "Create"} Free Item Offer
        </button>
      </div>
    </form>
  );
};

export default FreeItemForm;