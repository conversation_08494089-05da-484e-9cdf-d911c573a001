export interface NetWorkDetails {
  networkSellerId: number;
  sellerId ?: number;
  seller: string;
  network: string;
  disabled: boolean;
}

export interface NetWorkAreaDetails {
  networkAreaId: number;
  networkAreaName: string;
  agentName: string;
  networkName: string;
}

export interface AgentUser {
  agentUserId: number;
  fullName: string;
  businessName: string;
}
export interface Agent {
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
  address: string;
  password: string;
  businessId: number;
  roles: string[];
}

export interface SellerUser {
  userId: number;
  userName: string;
  businessName: string;
  mobileNumber: string;
  buyerId: number;
  sellerId: number;
  agentId: number;
  minVersion: number;
  roles: string[];
  cashWithUser: number;
  isBusinessOwner: boolean;
  disabled: boolean;
}
