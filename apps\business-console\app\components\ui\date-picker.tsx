import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Calendar } from './calendar';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { Button } from './button';
import { cn } from '~/utils/cn';
import { CalendarIcon } from 'lucide-react';

interface DatePickerProps {
  date: Date | null;
  onSelect: (date: Date | null) => void;
  className?: string;
  id?: string;
}

export function DatePicker({ date, onSelect, className, id }: DatePickerProps) {
  const [open, setOpen] = useState(false);

  // Make sure the internal state is synchronized with the prop
  useEffect(() => {
    if (!open) {
      // No changes needed, component is closed
      return;
    }
  }, [open, date]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'w-[240px] justify-start text-left font-normal',
            !date && 'text-muted-foreground',
            className
          )}
          id={id}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, 'PPP') : <span>Select date</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date || undefined}
          onSelect={(newDate) => {
            onSelect(newDate || null);
            setOpen(false);
          }}
          initialFocus
        />
        {date && (
          <div className="p-3 border-t border-border">
            <Button
              variant="ghost"
              className="w-full justify-center"
              onClick={() => {
                onSelect(null);
                setOpen(false);
              }}
            >
              Clear
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
} 