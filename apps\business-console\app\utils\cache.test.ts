/**
 * Cache Service Tests
 */

import { CacheService, extractUserIdFromToken, generateCacheKey } from './cache';

describe('CacheService', () => {
  let cache: CacheService<string>;
  
  beforeEach(() => {
    cache = new CacheService<string>({
      ttl: 1000,
      maxSize: 5,
      cleanupInterval: 0, // Disable cleanup for testing
    });
  });

  afterEach(() => {
    cache.destroy();
  });

  describe('Basic Operations', () => {
    test('should set and get value correctly', () => {
      cache.set('test-key', 'test-value');
      const result = cache.get('test-key');
      expect(result).toBe('test-value');
    });

    test('should return null for non-existent key', () => {
      const result = cache.get('non-existent');
      expect(result).toBeNull();
    });

    test('should check if key exists', () => {
      cache.set('test-key', 'test-value');
      expect(cache.has('test-key')).toBe(true);
      expect(cache.has('non-existent')).toBe(false);
    });

    test('should delete key correctly', () => {
      cache.set('test-key', 'test-value');
      expect(cache.delete('test-key')).toBe(true);
      expect(cache.get('test-key')).toBeNull();
      expect(cache.delete('non-existent')).toBe(false);
    });

    test('should clear all entries', () => {
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');
      cache.clear();
      expect(cache.get('key1')).toBeNull();
      expect(cache.get('key2')).toBeNull();
    });
  });

  describe('Memory Management', () => {
    test('should respect maxSize limit', () => {
      // Add more items than maxSize (5)
      for (let i = 0; i < 7; i++) {
        cache.set(`key${i}`, `value${i}`);
      }
      
      // Should have exactly maxSize items after eviction
      const stats = cache.getStats();
      expect(stats.total).toBe(5);
      
      // Oldest items should be evicted
      expect(cache.get('key0')).toBeNull();
      expect(cache.get('key1')).toBeNull();
      expect(cache.get('key5')).toBe('value5');
      expect(cache.get('key6')).toBe('value6');
    });

    test('should provide accurate statistics', () => {
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');
      
      const stats = cache.getStats();
      expect(stats.total).toBe(2);
      expect(stats.valid).toBe(2);
      expect(stats.expired).toBe(0);
      expect(stats.maxSize).toBe(5);
    });
  });

  describe('getOrSet', () => {
    test('should return cached value if available', async () => {
      cache.set('cached-key', 'cached-value');
      
      const result = await cache.getOrSet('cached-key', async () => {
        throw new Error('Should not be called');
      });
      
      expect(result).toBe('cached-value');
    });

    test('should fetch and cache new value if not available', async () => {
      let callCount = 0;
      
      const fetchFn = async () => {
        callCount++;
        return 'fetched-value';
      };
      
      const result = await cache.getOrSet('new-key', fetchFn);
      
      expect(result).toBe('fetched-value');
      expect(callCount).toBe(1);
      
      // Should be cached now
      const cached = cache.get('new-key');
      expect(cached).toBe('fetched-value');
    });

    test('should handle fetch errors gracefully', async () => {
      const fetchFn = async () => {
        throw new Error('API Error');
      };
      
      await expect(cache.getOrSet('error-key', fetchFn)).rejects.toThrow('API Error');
    });
  });
});

describe('Token Utilities', () => {
  test('should extract userId from valid token', () => {
    const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyRGV0YWlscyI6eyJ1c2VySWQiOjEyMywidXNlck5hbWUiOiJKb2huIERvZSIsImJ1c2luZXNzTmFtZSI6IlRlc3QgQnVzaW5lc3MiLCJidXllcklkIjoxLCJzZWxsZXJJZCI6MSwicm9sZXMiOlsidXNlciJdfSwiaWF0IjoxNjM5NzI5NjAwLCJleHAiOjE2Mzk3MzMyMDB9.mock_signature';
    const userId = extractUserIdFromToken(mockToken);
    expect(userId).toBe('123');
  });

  test('should return null for null token', () => {
    const userId = extractUserIdFromToken(null);
    expect(userId).toBeNull();
  });

  test('should return null for invalid token', () => {
    const userId = extractUserIdFromToken('invalid.token.here');
    expect(userId).toBeNull();
  });

  test('should generate cache key with userId', () => {
    const key = generateCacheKey('example.com', '123');
    expect(key).toBe('theme:example.com:user:123');
  });

  test('should generate cache key for anonymous user', () => {
    const key = generateCacheKey('example.com', null);
    expect(key).toBe('theme:example.com:anonymous');
  });
}); 