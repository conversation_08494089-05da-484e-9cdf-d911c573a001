import { j<PERSON>, LoaderFunction } from "@remix-run/node";
import { Form, useFetcher, use<PERSON>oaderD<PERSON>, useNavigate, useSearchParams } from "@remix-run/react";
import * as React from "react";
import { ArrowLeft, CalendarIcon } from "lucide-react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import {
      Table,
      TableBody,
      TableCell,
      TableHead,
      TableHeader,
      TableRow,
} from "~/components/ui/table";
import {
      Popover,
      PopoverContent,
      PopoverTrigger,
} from "~/components/ui/popover";
import { Calendar } from "~/components/ui/calendar";
import { format, parseISO } from "date-fns";
import { getSession } from "~/utils/session.server";
// import { getAgentSales } from "~/services/salesinfoDetails";
import { AgentSalesInfo } from "~/types/api/businessConsoleService/salesinfo";
import {
      Select,
      SelectContent,
      SelectItem,
      SelectTrigger,
      SelectValue,
} from "~/components/ui/select";
import { withAuth, withResponse } from "@utils/auth-utils";


interface LoaderData {
      agentSales: AgentSalesInfo[]
      sellerName: string | null,
      date: string
}

export const loader: LoaderFunction = withAuth(async ({ request }) => {
      const url = new URL(request.url);
      const date = url.searchParams.get("date") as string;
      const formatDate = format(date, "yyyy-MM-dd")
      const sellerName = url.searchParams.get("sellerName")
            ? decodeURIComponent(url.searchParams.get("sellerName") as string)
            : null;
      if (!formatDate) {
            throw new Error("Date is required");
      }

      const session = await getSession(request.headers.get("Cookie"));
      const access_token = session.get("access_token") as string | null;
      try {
            // const response = await getAgentSales(sellerId, formatDate, access_token);
            return withResponse({
                  // agentSales: response.data,
                  agentSales: [],


            })
      }
      catch (error) {
            throw new Error(`Error fetching transaction details: ${error}`);
      }
});

export default function AgentWiseSales() {
      const navigate = useNavigate();
      const loaderData = useLoaderData<LoaderData>();
      const initialDate = parseISO(loaderData.date);
      const [date, setDate] = React.useState<Date | undefined>(initialDate);
      const [data, setData] = React.useState<AgentSalesInfo[]>(loaderData.agentSales);
      const [loading, setLoading] = React.useState(false);
      const [searchTerm, setSearchTerm] = React.useState("");
      const [pageSize, setPageSize] = React.useState("10");
      const [currentPage, setCurrentPage] = React.useState(1);
      const fetcher = useFetcher<LoaderData>();
      React.useEffect(() => {

            if (fetcher.data?.agentSales) {


                  setData(fetcher.data?.agentSales);
            }
      }, [fetcher.data?.agentSales]);

      const filteredData = React.useMemo(() => {
            return data.filter((item) => {
                  const searchFields = [
                        item.agentId.toString().toLowerCase(),
                        item.agentName.toLowerCase(),
                  ];
                  return searchTerm === "" || searchFields.some((field) =>
                        field.includes(searchTerm.toLowerCase())
                  );
            });
      }, [data, searchTerm]);

      const paginatedData = React.useMemo(() => {
            const start = (currentPage - 1) * Number(pageSize);
            const end = start + Number(pageSize);
            return [...filteredData]
                  .sort((a, b) => a.agentName.localeCompare(b.agentName))
                  .slice(start, end);
      }, [filteredData, currentPage, pageSize]);

      const totalPages = Math.ceil(filteredData.length / Number(pageSize));


      const handleSubmit = (sellerId: number, deliveryDate: Date | undefined) => {
            const formData = new FormData();
            formData.append("sellerId", sellerId.toString())
            formData.append("date", deliveryDate as unknown as string)
            fetcher.submit(formData, { method: "GET" })

      }

      return (
            <div className="container mx-auto">
                  <div className="flex items-center gap-2 mb-6 my-6">
                        <Button variant="ghost" size="sm" onClick={() => navigate(-1)}>
                              <ArrowLeft className="h-4 w-4 mr-2" />
                              Back to Sales Reports
                        </Button>
                        <span className="text-muted-foreground">/</span>
                        <span className="font-semibold">{loaderData.sellerName}</span>
                  </div>
                  <div className="flex space-x-5  my-5">


                        <div className=" flex space-x-2">



                              <Popover>
                                    <PopoverTrigger asChild>
                                          <Button variant="outline" className="w-[280px]">
                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                {date ? format(date, "PPP") : "Pick a date"}
                                          </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0" >
                                          <Calendar
                                                mode="single"
                                                selected={date}
                                                onSelect={setDate}
                                                initialFocus
                                          />
                                    </PopoverContent>
                              </Popover>
                              <Input type="hidden" name="date" value={date ? format(date, "yyyy-MM-dd") : ""} />
                              <Input type="hidden" name="sellerId" value={loaderData.sellerId} />

                              <Button type="submit" onClick={() => handleSubmit(loaderData.sellerId, date)}>
                                    {loading ? "Submitting" : "View Report"}
                              </Button>
                        </div>
                        <Input
                              placeholder="Search by Agent Name or ID..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"
                        />
                        <Select value={pageSize} onValueChange={setPageSize}>
                              <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="Rows per page" />
                              </SelectTrigger>
                              <SelectContent>
                                    <SelectItem value="5">5 per page</SelectItem>
                                    <SelectItem value="10">10 per page</SelectItem>
                                    <SelectItem value="20">20 per page</SelectItem>
                                    <SelectItem value="50">50 per page</SelectItem>
                              </SelectContent>
                        </Select>
                  </div>

                  <div className="rounded-md border">
                        <Table>
                              <TableHeader>
                                    <TableRow>
                                          <TableHead className="text-right">Booked Qty</TableHead>
                                          <TableHead className="text-right">Delivered Qty</TableHead>
                                          <TableHead className="text-right">Return Qty</TableHead>
                                          <TableHead className="text-right">Cancel Qty</TableHead>
                                          <TableHead className="text-right">Shop Count</TableHead>
                                    </TableRow>
                              </TableHeader>
                              <TableBody>
                                    {paginatedData.length > 0 ? (
                                          paginatedData.map((row) => (
                                                <TableRow key={row.agentId}>
                                                      <TableCell className="font-medium">{row.agentId}</TableCell>
                                                      <TableCell>{row.agentName}</TableCell>
                                                      <TableCell className="text-right">
                                                            {row.totalBookedWeight.toFixed(2)}
                                                      </TableCell>
                                                      <TableCell className="text-right">
                                                            {row.totalDeliveredWeight.toFixed(2)}
                                                      </TableCell>
                                                      <TableCell className="text-right">
                                                            {row.totalReturnedWeight.toFixed(2)}
                                                      </TableCell>
                                                      <TableCell className="text-right">
                                                            {row.totalCancelledWeight.toFixed(2)}
                                                      </TableCell>
                                                      <TableCell className="text-right">
                                                            {row.shopCount.toFixed(2)}
                                                      </TableCell>
                                                </TableRow>
                                          ))
                                    ) : (
                                          <TableRow>
                                                <TableCell
                                                      colSpan={7}
                                                      className="h-24 text-center"
                                                >
                                                      No results.
                                                </TableCell>
                                          </TableRow>
                                    )}
                              </TableBody>
                        </Table>
                  </div>

                  <div className="flex items-center justify-between px-2 py-4">
                        <div className="text-sm text-gray-500">
                              Showing {(currentPage - 1) * Number(pageSize) + 1} to{" "}
                              {Math.min(currentPage * Number(pageSize), filteredData.length)} of{" "}
                              {filteredData.length} results
                        </div>
                        <div className="flex items-center space-x-2">
                              <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                                    disabled={currentPage === 1}
                              >
                                    Previous
                              </Button>
                              <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                          setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                                    }
                                    disabled={currentPage === totalPages}
                              >
                                    Next
                              </Button>
                        </div>
                  </div>
            </div>
      );
}
