import { isRouteErrorResponse, useRouteError } from "@remix-run/react";
import Customalert from "./ui/customalert";

interface ErrorBoundaryProps {
  title?: string;
  message?: string;
  onClose?: () => void;
  onContinue?: () => void;
}

export default function ErrorBoundary({
  title,
  message,
  onClose,
  onContinue,
}: ErrorBoundaryProps) {
  const error = useRouteError();

  if (isRouteErrorResponse(error)) {
    return <div></div>;
  } else if (error instanceof Error) {
    return (
      <Customalert
        title={title || "Uh oh ..."}
        message={message || "Something went wrong!"}
        onClose={() => onClose}
        onContinue={() => onContinue}
      />
    );
  }
}
