import { CirclePlus } from "lucide-react";
import { Button } from "../ui/button";
import { Form, useFetcher } from "@remix-run/react";
import { BuyerNetworkBanners } from "~/types/api/businessConsoleService/BuyerAccountingResponse";



interface InActiveBannersProps {
  bannerDetails: BuyerNetworkBanners;

}

export default function InActiveBanners({ bannerDetails }: InActiveBannersProps) {


  const fetcher = useFetcher();

  const loading = fetcher.state != "idle"

  return (
    <div className="flex flex-col shadow-md bg-white p-4 w-full  border-r-8 border-transparent rounded-lg gap-2  h-full">
      {/* Image Section */}
      <div className="flex gap-8 border-b border-dashed border-gray-300 pb-4 w-full">
        <img
          src={bannerDetails?.bannerUrl}
          alt="Banner"
          className="rounded-lg h-[160px] object-cover shadow-sm  sm: w-full md:max-w-md"
        />
      </div>
      {/* Push the button to the bottom */}
      <div className="flex flex-col w-full">
        <Form method="post" className="flex justify-end w-full">
          <input type="hidden" name="actionType" value="updateBannerStatus" />
          <input type="hidden" name="bannerId" value={bannerDetails?.id} />
          <input
            type="hidden"
            name="status"
            value={bannerDetails?.active != null ? String(bannerDetails?.active) : "false"}
          />
          <input type="hidden" name="sequence" value={bannerDetails?.sequenceId} />
          <Button
            className="flex items-center gap-2 py-2 px-4 text-white bg-primary-600 hover:bg-red-700 rounded-md shadow-md transition-all disabled:opacity-50"
            type="submit"
            disabled={loading}
          >
            {loading ? (
              "Updating..."
            ) : (
              <>
                <CirclePlus size={20} />
                <span>          Enable Banner
                </span>
              </>
            )}
          </Button>
        </Form>
      </div>
    </div>

  )
}