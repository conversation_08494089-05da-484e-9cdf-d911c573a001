import { Button } from "@components/ui/button"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@components/ui/table"
import { ExternalLink } from "lucide-react"
import {Link} from "@remix-run/react";

type Template = {
    name: string
    category: string
    language: string
    content: string
    status: string
    messagesDelivered: number
    messageReadRate: string
    topBlockReason: string
    lastEdited: string
}

const templates: Template[] = [
    {
        name: "complete_order_cancellation",
        category: "Utility",
        language: "English",
        content: "Dear {{1}}, We're sorry, but your order #{{2}} couldn't be fulfilled due to stock unavailability.❌ We apologize for the inconvenience and hope to serve you again. 🙏 Best, farmersMandi",
        status: "Active – Quality pending",
        messagesDelivered: 0,
        messageReadRate: "1",
        topBlockReason: "––",
        lastEdited: "4 Sep 2024",
    },
    {
        name: "out_for_delivery",
        category: "Utility",
        language: "English",
        content: "Good morning {{1}}! 🌅 Your order #{{2}} is on the way and will be delivered soon. 🚚 Total payable on delivery: *₹{{3}}* Thanks for choosing us! 🙏 Best, farmersMandi",
        status: "Active – Quality pending",
        messagesDelivered: 0,
        messageReadRate: "2",
        topBlockReason: "––",
        lastEdited: "4 Sep 2024",
    },
    {
        name: "delivery_confirmation_with_credit",
        category: "Utility",
        language: "English",
        content: "Hi {{1}} 👋, Your order #{{2}} has been delivered. ✅ Order Amount : ₹{{3}} Total Amount due: *₹{{4}}*. 💰 Please pay soon. 💳 Best, farmersMandi",
        status: "Active – Quality pending",
        messagesDelivered: 0,
        messageReadRate: "0",
        topBlockReason: "––",
        lastEdited: "4 Sep 2024",
    },
    {
        name: "delivery_completed",
        category: "Utility",
        language: "English",
        content: "Hi {{1}} 👋, Your order *#{{2}}* has been delivered. ✅ We hope you're happy with the fresh produce! 🥗 Total amount paid: *₹{{3}}*. 💰 Thanks for choosing us! 🙏 Best, farmersMandi",
        status: "Active – Quality pending",
        messagesDelivered: 0,
        messageReadRate: "2",
        topBlockReason: "––",
        lastEdited: "4 Sep 2024",
    },
    {
        name: "f_business_order_confirm",
        category: "Utility",
        language: "English",
        content: "Hello {{1}} 👋, Thank you for your order! 🛒 We've received your request for the following items: {{2}} Your order *#{{3}}* will be delivered tomorrow morning. 🚚 Total payable: ₹{{4}}. 💰 Thanks for choosing farmersMandi ! 🙏 Best, farmersMandi",
        status: "Active – Quality pending",
        messagesDelivered: 0,
        messageReadRate: "1",
        topBlockReason: "––",
        lastEdited: "4 Sep 2024",
    },
    {
        name: "hello_world",
        category: "Utility",
        language: "English (US)",
        content: "Welcome and congratulations!! This message demonstrates your ability to send a WhatsApp message notification from the Cloud API, hosted by Meta. Thank you for taking the time to test with us.",
        status: "Active – Quality pending",
        messagesDelivered: 1,
        messageReadRate: "0% (0)",
        topBlockReason: "––",
        lastEdited: "31 Aug 2024",
    },
]

export default function WhatsAppTemplates() {
    return (
        <div className="container mx-auto py-10">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">WhatsApp Templates</h1>
                <Button asChild>
                    <Link to="https://business.facebook.com/latest/whatsapp_manager/message_templates" target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="mr-2 h-4 w-4" /> Templates Manager
                    </Link>
                </Button>
            </div>
            <div className="rounded-md border overflow-x-auto">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Template Name</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead>Language</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Messages Delivered</TableHead>
                            <TableHead>Message Read Rate</TableHead>
                            <TableHead>Top Block Reason</TableHead>
                            <TableHead>Last Edited</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {templates.map((template) => (
                            <TableRow key={template.name}>
                                <TableCell className="font-medium">{template.name}</TableCell>
                                <TableCell>{template.category}</TableCell>
                                <TableCell>{template.language}</TableCell>
                                <TableCell>
                  <span
                      className="px-2 py-1 rounded-full text-xs font-semibold bg-yellow-100 text-yellow-800"
                  >
                    {template.status}
                  </span>
                                </TableCell>
                                <TableCell>{template.messagesDelivered}</TableCell>
                                <TableCell>{template.messageReadRate}</TableCell>
                                <TableCell>{template.topBlockReason}</TableCell>
                                <TableCell>{template.lastEdited}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    )
}
