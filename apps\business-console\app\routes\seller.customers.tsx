import { useState } from "react";
import { Input } from "@components/ui/input";
import { But<PERSON> } from "@components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/card";
import { ScrollArea } from "@components/ui/scroll-area";
import { Phone, Mail, MapPin } from "lucide-react";

// Mock data - replace with actual API call
const mockCustomers = [
  {
    id: 1,
    name: "<PERSON>",
    phone: "+91 9876543210",
    email: "<EMAIL>",
    address: "123 Main St, City",
  },
  {
    id: 2,
    name: "<PERSON>",
    phone: "+91 9876543211",
    email: "<EMAIL>",
    address: "456 Oak St, City",
  },
  // Add more mock data as needed
];

export default function Customers() {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredCustomers = mockCustomers.filter((customer) =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="container mx-auto p-4 space-y-4">
      <div className="flex flex-col space-y-4 md:flex-row md:justify-between md:items-center">
        <h1 className="text-2xl font-bold">My Customers</h1>
        <div className="w-full md:w-1/3">
          <Input
            type="search"
            placeholder="Search customers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
        </div>
      </div>

      <ScrollArea className="h-[calc(100vh-200px)]">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredCustomers.map((customer) => (
            <Card key={customer.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle>{customer.name}</CardTitle>
                <CardDescription>Customer ID: #{customer.id}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span>{customer.phone}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>{customer.email}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span>{customer.address}</span>
                  </div>
                  <Button className="w-full mt-4">View Details</Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
} 