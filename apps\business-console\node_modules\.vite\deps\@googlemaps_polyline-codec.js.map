{"version": 3, "sources": ["../../@googlemaps/polyline-codec/src/index.ts"], "sourcesContent": ["/**\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Object with lat and lng properties. */\nexport interface LatLng {\n  lat: number;\n  lng: number;\n}\n\n/**\n * Array with lat and lng elements.\n */\nexport type LatLngTuple = [number, number];\n\n/**\n * Decodes an encoded path string into a sequence of LatLngs.\n *\n * See {@link https://developers.google.com/maps/documentation/utilities/polylinealgorithm}\n *\n *  #### Example\n *\n * ```js\n * import { decode } from \"@googlemaps/polyline-codec\";\n *\n * const encoded = \"_p~iF~ps|U_ulLnnqC_mqNvxq`@\";\n * console.log(decode(encoded, 5));\n * // [\n * //   [38.5, -120.2],\n * //   [40.7, -120.95],\n * //   [43.252, -126.453],\n * // ]\n * ```\n */\nexport const decode = function (\n  encodedPath: string,\n  precision = 5\n): LatLngTuple[] {\n  const factor = Math.pow(10, precision);\n\n  const len = encodedPath.length;\n\n  // For speed we preallocate to an upper bound on the final length, then\n  // truncate the array before returning.\n  const path = new Array(Math.floor(encodedPath.length / 2));\n  let index = 0;\n  let lat = 0;\n  let lng = 0;\n  let pointIndex = 0;\n\n  // This code has been profiled and optimized, so don't modify it without\n  // measuring its performance.\n  for (; index < len; ++pointIndex) {\n    // Fully unrolling the following loops speeds things up about 5%.\n    let result = 1;\n    let shift = 0;\n    let b: number;\n    do {\n      // Invariant: \"result\" is current partial result plus (1 << shift).\n      // The following line effectively clears this bit by decrementing \"b\".\n      b = encodedPath.charCodeAt(index++) - 63 - 1;\n      result += b << shift;\n      shift += 5;\n    } while (b >= 0x1f); // See note above.\n    lat += result & 1 ? ~(result >> 1) : result >> 1;\n\n    result = 1;\n    shift = 0;\n    do {\n      b = encodedPath.charCodeAt(index++) - 63 - 1;\n      result += b << shift;\n      shift += 5;\n    } while (b >= 0x1f);\n    lng += result & 1 ? ~(result >> 1) : result >> 1;\n\n    path[pointIndex] = [lat / factor, lng / factor];\n  }\n  // truncate array\n  path.length = pointIndex;\n\n  return path;\n};\n\n/**\n * Polyline encodes an array of objects having lat and lng properties.\n *\n * See {@link https://developers.google.com/maps/documentation/utilities/polylinealgorithm}\n *\n * #### Example\n *\n * ```js\n * import { encode } from \"@googlemaps/polyline-codec\";\n *\n * const path = [\n *   [38.5, -120.2],\n *   [40.7, -120.95],\n *   [43.252, -126.453],\n * ];\n * console.log(encode(path, 5));\n * // \"_p~iF~ps|U_ulLnnqC_mqNvxq`@\"\n * ```\n */\nexport const encode = function (\n  path: (number[] | LatLng | LatLngTuple)[],\n  precision = 5\n): string {\n  const factor = Math.pow(10, precision);\n\n  const transform = function latLngToFixed(\n    latLng: LatLng | LatLngTuple\n  ): [number, number] {\n    if (!Array.isArray(latLng)) {\n      latLng = [latLng.lat, latLng.lng];\n    }\n\n    return [round(latLng[0] * factor), round(latLng[1] * factor)];\n  };\n\n  return polylineEncodeLine(path, transform);\n};\n\n/**\n * Encodes a generic polyline; optionally performing a transform on each point\n * before encoding it.\n *\n * @ignore\n */\nexport const polylineEncodeLine = function (\n  array: (number[] | LatLng | LatLngTuple)[],\n  transform: (latLng: number[] | LatLng | LatLngTuple) => [number, number]\n): string {\n  const v: string[] = [];\n  let start = [0, 0];\n  let end;\n  for (let i = 0, I = array.length; i < I; ++i) {\n    // In order to prevent drift (from quantizing deltas), we explicitly convert\n    // coordinates to fixed-precision to obtain integer deltas.\n    end = transform(array[i]);\n\n    // Push the next edge\n    polylineEncodeSigned(round(end[0]) - round(start[0]), v); // lat\n    polylineEncodeSigned(round(end[1]) - round(start[1]), v); // lng\n    start = end;\n  }\n\n  return v.join(\"\");\n};\n\n/**\n * Encodes the given value in our compact polyline format, appending the\n * encoded value to the given array of strings.\n *\n * @ignore\n */\nconst polylineEncodeSigned = function (\n  value: number,\n  array: string[]\n): string[] {\n  return polylineEncodeUnsigned(value < 0 ? ~(value << 1) : value << 1, array);\n};\n\n/**\n * Helper function for encodeSigned.\n *\n * @ignore\n */\nconst polylineEncodeUnsigned = function (\n  value: number,\n  array: string[]\n): string[] {\n  while (value >= 0x20) {\n    array.push(String.fromCharCode((0x20 | (value & 0x1f)) + 63));\n    value >>= 5;\n  }\n  array.push(String.fromCharCode(value + 63));\n  return array;\n};\n\n/**\n * @ignore\n */\nconst round = (v: number) => {\n  return Math.floor(Math.abs(v) + 0.5) * (v >= 0 ? 1 : -1);\n};\n"], "mappings": ";;;AA8Ca,IAAA,SAAS,SACpB,aACA,WAAa;AAAb,MAAA,cAAA,QAAA;AAAA,gBAAa;EAAA;AAEb,MAAM,SAAS,KAAK,IAAI,IAAI,SAAS;AAErC,MAAM,MAAM,YAAY;AAIxB,MAAM,OAAO,IAAI,MAAM,KAAK,MAAM,YAAY,SAAS,CAAC,CAAC;AACzD,MAAI,QAAQ;AACZ,MAAI,MAAM;AACV,MAAI,MAAM;AACV,MAAI,aAAa;AAIjB,SAAO,QAAQ,KAAK,EAAE,YAAY;AAEhC,QAAI,SAAS;AACb,QAAI,QAAQ;AACZ,QAAI,IAAC;AACL,OAAG;AAGD,UAAI,YAAY,WAAW,OAAO,IAAI,KAAK;AAC3C,gBAAU,KAAK;AACf,eAAS;IACV,SAAQ,KAAK;AACd,WAAO,SAAS,IAAI,EAAE,UAAU,KAAK,UAAU;AAE/C,aAAS;AACT,YAAQ;AACR,OAAG;AACD,UAAI,YAAY,WAAW,OAAO,IAAI,KAAK;AAC3C,gBAAU,KAAK;AACf,eAAS;aACF,KAAK;AACd,WAAO,SAAS,IAAI,EAAE,UAAU,KAAK,UAAU;AAE/C,SAAK,UAAU,IAAI,CAAC,MAAM,QAAQ,MAAM,MAAM;EAC/C;AAED,OAAK,SAAS;AAEd,SAAO;AACT;AAqBa,IAAA,SAAS,SACpB,MACA,WAAa;AAAb,MAAA,cAAA,QAAA;AAAA,gBAAa;EAAA;AAEb,MAAM,SAAS,KAAK,IAAI,IAAI,SAAS;AAErC,MAAM,YAAY,SAAS,cACzB,QAA4B;AAE5B,QAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,eAAS,CAAC,OAAO,KAAK,OAAO,GAAG;IACjC;AAED,WAAO,CAAC,MAAM,OAAO,CAAC,IAAI,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,MAAM,CAAC;EAC9D;AAEA,SAAO,mBAAmB,MAAM,SAAS;AAC3C;AAQa,IAAA,qBAAqB,SAChC,OACA,WAAwE;AAExE,MAAM,IAAc,CAAA;AACpB,MAAI,QAAQ,CAAC,GAAG,CAAC;AACjB,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAG5C,UAAM,UAAU,MAAM,CAAC,CAAC;AAGxB,yBAAqB,MAAM,IAAI,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC,CAAC,GAAG,CAAC;AACvD,yBAAqB,MAAM,IAAI,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC,CAAC,GAAG,CAAC;AACvD,YAAQ;EACT;AAED,SAAO,EAAE,KAAK,EAAE;AAClB;AAQA,IAAM,uBAAuB,SAC3B,OACA,OAAe;AAEf,SAAO,uBAAuB,QAAQ,IAAI,EAAE,SAAS,KAAK,SAAS,GAAG,KAAK;AAC7E;AAOA,IAAM,yBAAyB,SAC7B,OACA,OAAe;AAEf,SAAO,SAAS,IAAM;AACpB,UAAM,KAAK,OAAO,cAAc,KAAQ,QAAQ,MAAS,EAAE,CAAC;AAC5D,cAAU;EACX;AACD,QAAM,KAAK,OAAO,aAAa,QAAQ,EAAE,CAAC;AAC1C,SAAO;AACT;AAKA,IAAM,QAAQ,SAAC,GAAS;AACtB,SAAO,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,IAAI,IAAI;AACvD;", "names": []}