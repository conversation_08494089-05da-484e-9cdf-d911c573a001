import { create } from 'zustand';
import type { Template } from '~/schemas/marketing';
import type { SellerItem } from '~/types/api/businessConsoleService/MyItemList';
import type { BuyerSummaryDetailsResponseItem } from '~/types/api/businessConsoleService/BuyerSummaryDetailsResponseItem';

interface MessageDialogState {
  isOpen: boolean;
  template: Template | null;
  step: 'variables' | 'targeting' | 'confirm';
  variables: Record<string, string>;
  selectedGroup: string;
  selectedItems: SellerItem[];
  selectedCustomers: BuyerSummaryDetailsResponseItem[];
  items: SellerItem[];
  customers: BuyerSummaryDetailsResponseItem[];
  searchParams: {
    search: string;
    page: number;
    pageSize: number;
  };
  setTemplate: (template: Template | null) => void;
  setStep: (step: 'variables' | 'targeting' | 'confirm') => void;
  setVariables: (variables: Record<string, string>) => void;
  setSelectedGroup: (group: string) => void;
  toggleItem: (item: SellerItem) => void;
  toggleCustomer: (customer: BuyerSummaryDetailsResponseItem) => void;
  setItems: (items: SellerItem[]) => void;
  setCustomers: (customers: BuyerSummaryDetailsResponseItem[]) => void;
  setSearchParams: (params: Partial<MessageDialogState['searchParams']>) => void;
  reset: () => void;
}

const initialState = {
  isOpen: false,
  template: null,
  step: 'variables' as const,
  variables: {},
  selectedGroup: '',
  selectedItems: [],
  selectedCustomers: [],
  items: [],
  customers: [],
  searchParams: {
    search: '',
    page: 1,
    pageSize: 10,
  },
};

export const useMessageDialog = create<MessageDialogState>((set) => ({
  ...initialState,

  setTemplate: (template) => 
    set({ 
      ...initialState,
      template, 
      isOpen: !!template,
      // Initialize variables with empty strings for each template variable
      variables: template ? Object.fromEntries(template.variables.map(v => [v, ''])) : {},
    }),

  setStep: (step) => set({ step }),

  setVariables: (variables) => set({ variables }),

  setSelectedGroup: (selectedGroup) => set({ selectedGroup }),

  toggleItem: (item) =>
    set((state) => {
      const exists = state.selectedItems.some((i) => i.Id === item.Id);
      const selectedItems = exists
        ? state.selectedItems.filter((i) => i.Id !== item.Id)
        : [...state.selectedItems, item];
      return { selectedItems };
    }),

  toggleCustomer: (customer) =>
    set((state) => {
      const exists = state.selectedCustomers.some((c) => c.buyerId === customer.buyerId);
      const selectedCustomers = exists
        ? state.selectedCustomers.filter((c) => c.buyerId !== customer.buyerId)
        : [...state.selectedCustomers, customer];
      return { selectedCustomers };
    }),

  setItems: (items) => set({ items }),

  setCustomers: (customers) => set({ customers }),

  setSearchParams: (params) =>
    set((state) => ({
      searchParams: { ...state.searchParams, ...params },
    })),

  reset: () => set(initialState),
})); 