import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ci<PERSON>, Trash2, X } from "lucide-react";
import { NetworkAreas } from "~/types/api/businessConsoleService/Network";
import { Switch } from "./switch";
import { useCallback, useState } from "react";
import { useFetcher } from "@remix-run/react";
import { But<PERSON> } from "./button";
import { networkAgents } from "~/types/api/businessConsoleService/SellerManagement";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"

interface NetworkAreaCardProps {
    networkAreaDetail: NetworkAreas;
    agentDetails?: networkAgents[];
    networkId?: number;
    color?: string;
    netWorks: NetworkAreas[],
    isSalesPermission: boolean
}

const NetworkAreaCard: React.FC<NetworkAreaCardProps> = ({
    networkAreaDetail,
    agentDetails,
    networkId,
    color,
    netWorks,
    isSalesPermission


}) => {
    const [networkStatus, setNetworkStatus] = useState<Record<number, boolean>>(() =>
        (netWorks ?? []).reduce((acc, network) => {
            if (network?.networkAreaId !== undefined) {
                acc[network.networkAreaId] = network?.disabled ?? false;
            }
            return acc;
        }, {} as Record<number, boolean>)
    );
    const [areaId, setAreaId] = useState<number>(0);
    const [editNetworkId, setEditNetworkId] = useState<number>(0);
    const [agentUserId, setAgentUserId] = useState<number>(0);
    const handleNewLocalities2 = useCallback(
        (id?: number, networkId?: number, agentUserId?: string) => {
            if (id && networkId && agentUserId) {
                setAreaId(id);
                setEditNetworkId(networkId);
                setAgentUserId(parseFloat(agentUserId));
            }
        }, []);

    const [editLocality, setEditLocality] = useState<number>(0);
    const fetcher = useFetcher()

    const handleSwitchNetwork = async (networkAreaId: number) => {
        setNetworkStatus((prev) => ({
            ...prev,
            [networkAreaId]: !prev[networkAreaId],
        }));
        const formData = new FormData();
        formData.append("nAreaId", networkAreaId.toString());
        formData.append("actionType", "updateNetWorkAreaStatus");
        fetcher.submit(formData, { method: "put" });
    }

    const handleEditNetwork = async () => {

        const formData = new FormData();
        formData.append("masterAreaId", areaId.toString());
        formData.append("editNetworkId", editNetworkId.toString());
        formData.append("agentUserId", agentUserId.toString());
        formData.append("actionType", "updateNetWorkAreaEdit");
        console.log("handleEditNetwork formData.....", formData);
        for (const [key, value] of formData.entries()) {
            console.log(key, value);
        }
        fetcher.submit(formData, { method: "POST" });
        setEditLocality(0);
    }

    const editLocalityClicked = useCallback((areaId: number) => {
        setEditLocality(areaId);
    }, []);
    const resetEditLocalityClicked = useCallback(() => {
        setEditLocality(0);
    }, []);

    return (
        <div className="flex flex-col w-full gap-3 bg-white p-2 rounded-lg shadow">
            <div className="flex gap-2  justify-between align-center items-center">
                <div className="flex flex-col gap-0">
                    <div className="text-sm text-typography-400 leading-tight" >
                        id:  {networkAreaDetail.networkAreaId}
                    </div>
                    <div className="text-lg text-typography-800 leading-tight">
                        {networkAreaDetail.networkAreaName}
                    </div>
                </div>
                {color && (<Circle size={"1rem"} fill={`${color}`} color={`${color}`} />)}
            </div>
            <div className="text-md text-typography-600 flex gap-4 w-full items-center">
                {networkAreaDetail.agentName && (<div className="w-full"> {
                    editLocality === networkAreaDetail.networkAreaId ? (
                        <>
                            <Select
                                value={agentUserId.toString()}
                                onValueChange={(newDistrict) => {
                                    handleNewLocalities2(networkAreaDetail.areaId, networkId, newDistrict);

                                }} >
                                <SelectTrigger>
                                    <SelectValue placeholder="Please select an agent" />
                                </SelectTrigger>
                                <SelectContent>
                                    {agentDetails?.map((district) => (
                                        <SelectItem key={district.agentUserId} value={district.agentUserId.toString()}>
                                            {district.agentUserId} - {district.fullName}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </>
                    ) : (<> <span className="text-typography-300 font-thin text-sm">Sales Agent : </span>
                        {networkAreaDetail.agentName} </>)}</div>)}
                {!isSalesPermission && <div className="flex gap-0">
                    {editLocality === networkAreaDetail.networkAreaId ? (<>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="text-primary hover:text-primary-800"
                            onClick={() => handleEditNetwork()}
                            disabled={agentUserId === 0}
                        >
                            <CircleCheckBig size={16} />
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="text-typography-300 hover:text-typography-600"
                            onClick={() => resetEditLocalityClicked()}
                        >
                            <X size={16} />
                        </Button> </>
                    ) :
                        (<>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="text-indigo-600 hover:text-indigo-900"
                                onClick={() => editLocalityClicked(networkAreaDetail.networkAreaId)}
                            >
                                <Pencil size={16} />
                            </Button>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-500 hover:text-red-900"
                                onClick={() => handleSwitchNetwork(networkAreaDetail.networkAreaId)}
                            >
                                <Trash2 size={16} />
                            </Button></>)}
                    {/* <Switch
                    checked={networkStatus[networkAreaDetail.networkAreaId]}  // Use sellerStates to track toggle status
                    onCheckedChange={() => handleSwitchNetwork(networkAreaDetail.networkAreaId)}
                /> */}
                </div>}
            </div>
        </div>
    );
};

export default NetworkAreaCard;