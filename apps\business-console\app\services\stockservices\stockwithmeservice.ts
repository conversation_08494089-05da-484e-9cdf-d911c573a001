import { ApiResponse } from "~/types/api/Api";
import { ItemStock } from "~/types/api/businessConsoleService/ItemStock";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getStocksWithMe(
  isSeller?:boolean,
  sellerId?: number,
  pageNo?: number,
  size?: number,
  matchBy?: string,
  request?: Request
): Promise<ApiResponse<ItemStock[]>> {
  
      if(!isSeller){
            if (!sellerId || typeof sellerId !== "number" || sellerId <= 0) {
                  throw new Response("Invalid sellerId provided");
                }
      }
 

  if (pageNo !== undefined && (typeof pageNo !== "number" || pageNo < 0)) {
    throw new Response("Invalid pageNo provided");
  }

  if (size !== undefined && (typeof size !== "number" || size <= 0)) {
    throw new Response("Invalid size provided");
  }

  if (matchBy !== undefined && typeof matchBy !== "string") {
    throw new Response("matchBy must be a string if provided");
  }

  try {
    const queryParams = new URLSearchParams();
    if(!isSeller){
      if (!sellerId || typeof sellerId !== "number" || sellerId <= 0) {
            throw new Response("Invalid sellerId provided");
          }
      queryParams.append("sellerId", sellerId.toString());
}
    if (pageNo !== undefined) queryParams.append("pageNo", pageNo.toString());
    if (size !== undefined) queryParams.append("size", size.toString());
    if (matchBy && matchBy.trim().length > 0) {
      queryParams.append("matchBy", matchBy.trim());
    }
    const url = isSeller ? `${API_BASE_URL}/bc/seller/stockwithme?${queryParams.toString()}` : `${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/stockwithme/?${queryParams.toString()}`;
    const response = await apiRequest<ItemStock[]>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
          throw new Response("Failed to fetch stockWithMe");
    }
  } catch (error: any) {
    // Log error or handle it accordingly
    throw new Error(`Error in  fetching stockWithMe: ${error.message}`);
  }
}
export async function getSelectedMyStockWithMe(
      isSeller?:boolean,
      stockId?: number,
      sellerId?: number,
      request?: Request
    ): Promise<ApiResponse<ItemStock[]>> {
          if(!stockId){
                if (!stockId || typeof stockId !== "number" || stockId <= 0) {
                      throw new Response("Invalid sellerId provided");
                    }
          }
     
    
      try {
            

            const url = isSeller ? `${API_BASE_URL}/bc/seller/stock/${stockId}` :
             `${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/stock/${stockId}`;
        const response = await apiRequest<ItemStock[]>(
          url,
          "GET",
          undefined,
          {},
          true,
          request
        );
    
        if (response) {
          return response;
        } else {
              throw new Response("Failed to fetch MyStocks");
        }
      } catch (error: any) {
        // Log error or handle it accordingly
        throw new Error(`Error in MyStocks: ${error.message}`);
      }
    }


export async function updateStockWithMe(
      isSeller: boolean,
      stockId?:number,
      sellerId?: number,
      requestBody?: ItemStock,
      request?: Request
    ): Promise<ApiResponse<ItemStock[]>> {

      if (!stockId || typeof stockId !== "number" || stockId <= 0) {
        throw new Response("Invalid stockId provided");
      }


      
      try {

   
       


        const url = isSeller ? `${API_BASE_URL}/bc/seller/mystock/${stockId}` : `${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/mystock/${stockId}`;
        const response = await apiRequest<ItemStock[]>(
          url,
          "PUT",
          requestBody,
          {},
          true,
          request
        );
    
        if (response) {
          return response;
        } else {
              throw new Response("Failed to fetch MyStocks");
        }
      } catch (error: any) {
        // Log error or handle it accordingly
        throw new Error(`Error in MyStocks: ${error.message}`);
      }
    }
    