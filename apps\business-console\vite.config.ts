import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import path from "path";


declare module "@remix-run/node" {
  interface Future {
    v3_singleFetch: true;
  }
}

export default defineConfig({
  plugins: [
    remix({
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
        v3_singleFetch: true,
        v3_lazyRouteDiscovery: true,
      },
    }),
    tsconfigPaths(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./app"),
      "@components": path.resolve(__dirname, "./app/components"),
      "@styles": path.resolve(__dirname, "./app/styles"),
      "@utils": path.resolve(__dirname, "./app/utils"),
      "@routes": path.resolve(__dirname, "./app/routes"),
      "@stores": path.resolve(__dirname, "./app/stores")
    }
  },
  build: {
    minify: process.env.NODE_ENV === 'production' ? 'esbuild' : false,
    sourcemap: true,
  },
  server: {
    hmr: process.env.NODE_ENV !== 'production',
    allowedHosts: [".ngrok-free.app"]
  },
});
