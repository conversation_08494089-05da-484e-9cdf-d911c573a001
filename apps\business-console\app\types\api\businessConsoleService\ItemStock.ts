export interface ItemStock {
  stockId:number
      itemName: string;
      distributor: string;
      supplier: string;
      maxAvailableQty: number;
      pricePerUnit: number;
      active: boolean;
    }
    
    export enum StockTransactionType {
      RECEIVED = 'RECEIVED',
      DELIVERED = 'DELIVERED',
      SPOILED = 'SPOILED',
      RETURNED = 'RETURNED',
      CONVERTED = 'CONVERTED',
      CORRECTION = 'CORRECTION'
    }
    export interface InvStockTransaction {
      deliveryDate: string;
      invStockTransactionId: number;
      transactionType: StockTransactionType;
      quantity: number;
      unitPrice: number;
      totalValue: number;
      narration: string;
      username: string;
      balanceAfter: number;
      totalReceived: number;
      totalDelivered: number;
      totalReturned: number;
      totalSpoiled: number;
      totalConverted: number;
      totalCorrection: number;
      itemTotalAmount:number,
      totalDistributionCharges:number,
      totalSalesComm:number;
      supplierNetAmount:number;
    }
    export interface CreateStockTransactionInput {
          stockTransactionType: StockTransactionType;
          quantity: number;
          narration: string;
          deliveryDate: string; // or use `Date` if you prefer
        }

       
    
