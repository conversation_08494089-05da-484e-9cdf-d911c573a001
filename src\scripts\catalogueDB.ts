// src/utils/dynamoDBUtils.ts

import {
    PutItemCommand,
    GetItemCommand,
    PutItemCommandInput,
    GetItemCommandInput,
    QueryCommandInput,
    QueryCommand,
  } from "@aws-sdk/client-dynamodb";
  import { marshall, unmarshall } from "@aws-sdk/util-dynamodb";
  import { dynamoDBClient } from "@utils/aws/aws.utils.js";
  import logger from "@utils/express-logger.js";
  import { ProductItem } from "@/types/catalogue.type.js";

  
  class DynamoDBUtils {
    private tableName: string;
  
    constructor(tableName: string) {
      this.tableName = tableName;
    }
  
    public async exists(variationId: string): Promise<ProductItem | null> {
      const params: QueryCommandInput = {
          TableName: this.tableName,
          IndexName: "variationId-index", // Replace with your actual index name
          KeyConditionExpression: "variationId = :variationId",
          ExpressionAttributeValues: {
              ":variationId": { S: variationId },
          },
          Limit: 1, // We only need to check if one record exists
      };
  
      try {
        const data = await dynamoDBClient.send(new QueryCommand(params));

        if (data.Items && data.Items.length > 0) {
            const item = unmarshall(data.Items[0]) as ProductItem; // Convert the DynamoDB item to a ProductItem object
            return item; // Check if the status is "Success"
        }

        return null; // No matching items found
    } catch (error: any) {
        logger.error(`Error checking existence of variationId ${variationId}: ${error.message}`);
        throw error;
    }
  }
  
    /**
     * Inserts a product item into DynamoDB
     * @param item Product item to insert
     */
    public async insert(item: ProductItem): Promise<void> {
      const params: PutItemCommandInput = {
        TableName: this.tableName,
        Item: marshall(item),
        // ConditionExpression: "attribute_not_exists(ProductID)", // Prevent duplicates
      };
  
      try {
        await dynamoDBClient.send(new PutItemCommand(params));
        logger.info(`Inserted ProductID ${item.variationId} into DynamoDB.`);
      } catch (error: any) {
        if (error.name === "ConditionalCheckFailedException") {
          logger.warn(`variationId ${item.variationId} already exists. Skipping insert.`);
        } else {
          logger.error(`Error inserting variationId ${item.variationId}: ${error.message}`);
          throw error;
        }
      }
    }
  
    /**
     * Logs the upload status for a product
     * @param logItem Log item to insert
     */
    public async logUploadStatus(logItem: ProductItem): Promise<void> {
      const params: PutItemCommandInput = {
        TableName: this.tableName,
        Item: marshall(logItem),
      };
  
      try {
        await dynamoDBClient.send(new PutItemCommand(params));
        logger.info(`Logged upload status for variationId ${logItem.variationId}.`);
      } catch (error: any) {
        logger.error(`Error logging upload status for variationId ${logItem.variationId}: ${error.message}`);
        throw error;
      }
    }
  }
  
  export default DynamoDBUtils;
  