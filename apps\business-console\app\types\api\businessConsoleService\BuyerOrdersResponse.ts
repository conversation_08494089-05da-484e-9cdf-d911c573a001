export interface OrderSummary {
  orderGroupId: number;
  orderDateTime: string;
  orderStatus: string;
  deliveryDate: string;
  orderAmount: number;
  paymentStatus: boolean;
}

export interface ContractPrice {
  contractPriceId: number;
  enabled: boolean;
  cbItemPrice: number;
  sellerItemId: number;
  itemName: string;
  picture: string;
  unit: string;
  buyerId: number;
  buyerName: string;
  buyerMobile: string;
  newItem: boolean;
}
export interface SelectedItemSummary {
  itemId: number;
  itemName: string;
  itemImage: string;
  itemMinOrderQty: number;
  itemIncrementQty: number;
  revenue: number;
  totalOrders: number;
  returnedQty: number;
  returnedAmount: number;
  totalDaysOfDelivery: number;
  unit: string;
}
