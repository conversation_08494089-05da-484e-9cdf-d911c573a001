import React, { useState } from 'react';
import { Input } from '~/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import ResponsivePagination from '~/components/ui/responsivePagination';
import { DeleteIcon, Pencil, Trash } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { ResponsiveTable } from '../ui/responsiveTable';
import AddonsModal from './AddonsModal';
import { useFetcher } from '@remix-run/react';
import { MyAddonData } from '~/types/api/businessConsoleService/SellerManagement';

interface MyAddonsTabProps {
      addonsList: MyAddonData[];
      searchTerm: string;
      setSearchTerm: (value: string) => void;
      pageSize: string;
      setPageSize: (value: string) => void;
      pageNum: number;
      setPageNum: (value: number) => void;
      isItemModalOpen: boolean;
      setIsItemModalOpen: (open: boolean) => void;
      selectedItem: any;
      setSelectedItem: (item: any) => void;
      sellerId: number
}

const myAddonsHeaders = [
      "Id",
      "Name",
      "Diet",
      "Active",
      "",
      ""
];
const MyAddonsTab: React.FC<MyAddonsTabProps> = ({
      addonsList,
      searchTerm,
      setSearchTerm,
      pageSize,
      setPageSize,
      pageNum,
      setPageNum,
      isItemModalOpen,
      setIsItemModalOpen,
      selectedItem,
      setSelectedItem,
      sellerId
}) => {
      const handlePageSearch = (value: string) => {
            setSearchTerm(value);
      };
      const handlePageSizeChange = (newPageSize: string) => {
            setPageSize(newPageSize);
      };
      const handlePageChange = (newPageNum: number) => {
            setPageNum(newPageNum);
      };
      const addonFetcher = useFetcher()
      const handleSave = (addonsData: MyAddonData) => {
            const formData = new FormData();
            console.log("kkkkkkkkkkkkkkkkk")
            formData.append("actionType", "addonsAdd");
            formData.append("addonData", JSON.stringify(addonsData))
            addonFetcher.submit(formData, { method: "POST" })
      }
      const handleDelete = (addonsData: MyAddonData) => {
            const formData = new FormData();
            formData.append("actionType", "addonsdelete");
            formData.append("addonId", addonsData.id.toString())
            formData.append("sellerId", sellerId.toString())
            addonFetcher.submit(formData, { method: 'post' })
      }

      return (
            <>
                  <div className="flex justify-between mb-4">
                        <Input
                              placeholder="Search by Item Name"
                              value={searchTerm}
                              onChange={(e) => handlePageSearch(e.target.value)}
                              className="max-w-sm rounded-full"
                        />
                        <Select value={pageSize} onValueChange={handlePageSizeChange}>
                              <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="Items per page" />
                              </SelectTrigger>
                              <SelectContent>
                                    <SelectItem value="5">5 per page</SelectItem>
                                    <SelectItem value="10">10 per page</SelectItem>
                                    <SelectItem value="20">20 per page</SelectItem>
                                    <SelectItem value="50">50 per page</SelectItem>
                              </SelectContent>
                        </Select>
                  </div>
                  <ResponsiveTable
                        headers={myAddonsHeaders}
                        data={addonsList}
                        renderRow={(row) => (
                              <tr key={row.id} className="border-b">
                                    <td className="py-2 px-3 text-center whitespace-normal break-words ">{row.id}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words ">{row?.name}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.diet}</td>
                                    <td className="py-2 px-3 text-center whitespace-normal break-words">
                                          {row?.active ? (
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                      <svg
                                                            className="w-4 h-4 mr-1 text-green-500"
                                                            fill="currentColor"
                                                            viewBox="0 0 20 20"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                      >
                                                            <path
                                                                  fillRule="evenodd"
                                                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                                  clipRule="evenodd"
                                                            />
                                                      </svg>
                                                      Active
                                                </span>
                                          ) : (
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                      <svg
                                                            className="w-4 h-4 mr-1 text-red-500"
                                                            fill="currentColor"
                                                            viewBox="0 0 20 20"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                      >
                                                            <path
                                                                  fillRule="evenodd"
                                                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                                  clipRule="evenodd"
                                                            />
                                                      </svg>
                                                      Inactive
                                                </span>
                                          )}
                                    </td>
                                    <td className="py-2 px-3 text-center cursor-pointer">
                                          <Button
                                                variant="ghost"
                                                size="sm"
                                                className="text-red-500 hover:text-red-900"
                                                onClick={() => {
                                                      if (confirm("Are you sure you want to delete this area?")) {
                                                            handleDelete(row)
                                                      }
                                                }}
                                                style={{ alignSelf: "flex-end" }}
                                          >
                                                <Trash size={20} />
                                          </Button>
                                    </td>
                                    <td className="py-2 px-3 text-center cursor-pointer">
                                          <Pencil color='blue' size={20} onClick={() => {
                                                setSelectedItem(row);
                                                setIsItemModalOpen(true);
                                          }} />
                                    </td>
                              </tr>
                        )}
                  />
                  <div className="flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap">
                        <h2 className="shrink-0">Current Page: {pageNum + 1}</h2>
                        <div className="overflow-x-auto">
                              <ResponsivePagination
                                    totalPages={Number(pageSize)}
                                    currentPage={pageNum}
                                    onPageChange={handlePageChange}
                              />
                        </div>
                  </div>
                  <AddonsModal
                        isOpen={isItemModalOpen}
                        data={selectedItem || {}}
                        onClose={() => {
                              setIsItemModalOpen(false)
                              setSelectedItem({});
                        }}
                        onSave={() => handleSave}
                        sellerId={sellerId}
                        header={selectedItem ? 'Edit Addons' : "Add Addons"}
                  />
                  <Button className="fixed bottom-5 right-5 rounded-full cursor-pointer" onClick={() => setIsItemModalOpen(true)}>+ Add Addons</Button>
            </>
      );
};

export default MyAddonsTab;
