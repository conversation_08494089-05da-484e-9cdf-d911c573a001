import { LoaderFunction, redirect } from "@remix-run/node";
import { Link, Navigate, Outlet, useLoaderData, useLocation, Form } from "@remix-run/react";
import {
      Image,
      Menu,
      LogOut,
      ChevronDown,
      ChevronRight,
      LayoutDashboard,
      ShoppingCart,
      UtensilsCrossed,
      Megaphone,
      Users,
      BarChart3,
      Zap,
      CreditCard,
      Settings,
      Gift,
      Truck,
      Cog
} from "lucide-react"

import { useState, useEffect } from "react";
import { FaWhatsapp } from "react-icons/fa";
// eslint-disable-next-line import/no-unresolved
import { Button } from "~/components/ui/button";
// eslint-disable-next-line import/no-unresolved
import { Sheet, SheetContent, SheetTrigger } from "~/components/ui/sheet";
// eslint-disable-next-line import/no-unresolved
import { withAuth, withResponse } from "~/utils/auth-utils";
// eslint-disable-next-line import/no-unresolved
import { destroySession, getSession } from "~/utils/session.server";

interface LoaderData {
      userPermissions: string[];
      userDetails: {
            userDetails: {
                  businessName?: string;
            };
      };
}
export const loader: LoaderFunction = withAuth(
      async ({ user }) => {
            console.log("user:::", user);
            return withResponse({
                  userPermissions: user?.userPermissions || [],
                  userDetails: user || { userDetails: {} },
            });
      }
)
export const action = withAuth(
      async ({ request }) => {
            const session = await getSession(request.headers.get("Cookie"))
            return redirect("/login", {
                  headers: {
                        "Set-Cookie": await destroySession(session),
                  },
            })
      }
);
export default function SellerSetting() {
      const loaderData = useLoaderData<LoaderData>();
      const location = useLocation();
      const [isOpen, setIsOpen] = useState(false);
      const [shouldRedirect, setShouldRedirect] = useState(false);
      const [sidebarWidth, setSidebarWidth] = useState(256); // Default width in pixels
      const [isResizing, setIsResizing] = useState(false);
      const [isSettingsExpanded, setIsSettingsExpanded] = useState(false);
      const activeSection = location.pathname.split("/")[2];

      useEffect(() => {
            if (location.pathname === "/sellerSetting") {
                  setShouldRedirect(true);
            } else {
                  setShouldRedirect(false);
            }
      }, [location.pathname]);

      // Handle mouse events for resizing
      const handleMouseDown = () => {
            setIsResizing(true);
      };

      const handleMouseMove = (e: MouseEvent) => {
            if (!isResizing) return;
            const newWidth = Math.max(200, Math.min(400, e.clientX));
            setSidebarWidth(newWidth);
      };

      const handleMouseUp = () => {
            setIsResizing(false);
      };

      useEffect(() => {
            if (isResizing) {
                  document.addEventListener('mousemove', handleMouseMove);
                  document.addEventListener('mouseup', handleMouseUp);
                  document.body.style.cursor = 'col-resize';
                  document.body.style.userSelect = 'none';
            } else {
                  document.removeEventListener('mousemove', handleMouseMove);
                  document.removeEventListener('mouseup', handleMouseUp);
                  document.body.style.cursor = '';
                  document.body.style.userSelect = '';
            }

            return () => {
                  document.removeEventListener('mousemove', handleMouseMove);
                  document.removeEventListener('mouseup', handleMouseUp);
                  document.body.style.cursor = '';
                  document.body.style.userSelect = '';
            };
      }, [isResizing]);

      if (shouldRedirect) {
            return <Navigate to="/sellerSetting/dashboard" replace />;
      }

      const NavContent = () => (
            <div className="flex flex-col h-full bg-white border-r border-border">
                  {/* Header Section */}
                  <div className="p-6 border-b border-border">
                        <div className="flex items-center space-x-4">
                              <div className="h-10 w-10 rounded-full bg-primary flex items-center justify-center text-xl font-bold text-primary-foreground">
                                    {loaderData?.userDetails?.userDetails?.businessName?.[0]?.toUpperCase() || "B"}
                              </div>
                              <div className="text-foreground text-xl font-semibold truncate">
                                    {loaderData?.userDetails?.userDetails?.businessName || "Business Name"}
                              </div>
                        </div>
                  </div>

                  {/* Navigation Section */}
                  <nav className="flex-1 p-4 space-y-2">
                        <div className="space-y-1">
                              {/* New Restaurant Dashboard Navigation Items */}
                              <Link to="/sellerSetting/dashboard" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "dashboard" ? "secondary" : "ghost"}
                                          className="w-full justify-start"
                                    >
                                          <div className="flex items-center mr-3">
                                                <LayoutDashboard className="w-5 h-5" />
                                                <span className="ml-2">Dashboard</span>
                                          </div>
                                    </Button>
                              </Link>

                              <Link to="/sellerSetting/orders" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "orders" ? "secondary" : "ghost"}
                                          className="w-full justify-start"
                                    >
                                          <div className="flex items-center mr-3">
                                                <ShoppingCart className="w-5 h-5" />
                                                <span className="ml-2">Orders</span>
                                          </div>
                                    </Button>
                              </Link>

                              <Link to="/sellerSetting/menu" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "menu" ? "secondary" : "ghost"}
                                          className="w-full justify-start"
                                    >
                                          <div className="flex items-center mr-3">
                                                <UtensilsCrossed className="w-5 h-5" />
                                                <span className="ml-2">Menu</span>
                                          </div>
                                    </Button>
                              </Link>

                              <Link to="/sellerSetting/campaigns" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "campaigns" ? "secondary" : "ghost"}
                                          className="w-full justify-start"
                                    >
                                          <div className="flex items-center mr-3">
                                                <Megaphone className="w-5 h-5" />
                                                <span className="ml-2">Campaigns</span>
                                          </div>
                                    </Button>
                              </Link>

                              <Link to="/sellerSetting/customers" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "customers" ? "secondary" : "ghost"}
                                          className="w-full justify-start"
                                    >
                                          <div className="flex items-center mr-3">
                                                <Users className="w-5 h-5" />
                                                <span className="ml-2">Customers</span>
                                          </div>
                                    </Button>
                              </Link>

                              <Link to="/sellerSetting/analytics" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "analytics" ? "secondary" : "ghost"}
                                          className="w-full justify-start"
                                    >
                                          <div className="flex items-center mr-3">
                                                <BarChart3 className="w-5 h-5" />
                                                <span className="ml-2">Analytics</span>
                                          </div>
                                    </Button>
                              </Link>

                              <Link to="/sellerSetting/integrations" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "integrations" ? "secondary" : "ghost"}
                                          className="w-full justify-start"
                                    >
                                          <div className="flex items-center mr-3">
                                                <Zap className="w-5 h-5" />
                                                <span className="ml-2">Integrations</span>
                                          </div>
                                    </Button>
                              </Link>

                              <Link to="/sellerSetting/payouts" onClick={() => setIsOpen(false)}>
                                    <Button
                                          variant={activeSection === "payouts" ? "secondary" : "ghost"}
                                          className="w-full justify-start"
                                    >
                                          <div className="flex items-center mr-3">
                                                <CreditCard className="w-5 h-5" />
                                                <span className="ml-2">Payouts</span>
                                          </div>
                                    </Button>
                              </Link>

                              <div>
                                    <Button
                                          variant={activeSection === "settings" ? "secondary" : "ghost"}
                                          className="w-full justify-start"
                                          onClick={() => setIsSettingsExpanded(!isSettingsExpanded)}
                                    >
                                          <div className="flex items-center justify-between w-full">
                                                <div className="flex items-center mr-3">
                                                      <Settings className="w-5 h-5" />
                                                      <span className="ml-2">Settings</span>
                                                </div>
                                                {isSettingsExpanded ? (
                                                      <ChevronDown className="w-4 h-4 text-muted-foreground" />
                                                ) : (
                                                      <ChevronRight className="w-4 h-4 text-muted-foreground" />
                                                )}
                                          </div>
                                    </Button>

                                    {/* Settings Sub-menu */}
                                    {isSettingsExpanded && (
                                          <div className="ml-6 space-y-1 mt-1">
                                                <Link to="/sellerSetting/settings" onClick={() => setIsOpen(false)}>
                                                      <Button
                                                            variant={activeSection === "settings" ? "secondary" : "ghost"}
                                                            className="w-full justify-start text-sm"
                                                      >
                                                            <div className="flex items-center mr-3">
                                                                  <Cog className="w-4 h-4" />
                                                                  <span className="ml-2">General Settings</span>
                                                            </div>
                                                      </Button>
                                                </Link>

                                                <Link to="/sellerSetting/deliveryConfig" onClick={() => setIsOpen(false)}>
                                                      <Button
                                                            variant={activeSection === "deliveryConfig" ? "secondary" : "ghost"}
                                                            className="w-full justify-start text-sm"
                                                      >
                                                            <div className="flex items-center mr-3">
                                                                  <Truck className="w-4 h-4" />
                                                                  <span className="ml-2">Delivery Config</span>
                                                            </div>
                                                      </Button>
                                                </Link>

                                                <Link to="/sellerSetting/nbanners" onClick={() => setIsOpen(false)}>
                                                      <Button
                                                            variant={activeSection === "nbanners" ? "secondary" : "ghost"}
                                                            className="w-full justify-start text-sm"
                                                      >
                                                            <div className="flex items-center mr-3">
                                                                  <Image className="w-4 h-4" />
                                                                  <span className="ml-2">Banners & Sequence</span>
                                                            </div>
                                                      </Button>
                                                </Link>

                                                <Link to="/sellerSetting/whatsappprofile" onClick={() => setIsOpen(false)}>
                                                      <Button
                                                            variant={activeSection === "whatsappprofile" ? "secondary" : "ghost"}
                                                            className="w-full justify-start text-sm"
                                                      >
                                                            <div className="flex items-center mr-3">
                                                                  <FaWhatsapp className="" size={16} />
                                                                  <span className="ml-2">WhatsApp Settings</span>
                                                            </div>
                                                      </Button>
                                                </Link>

                                                <Link to="/sellerSetting/coupons" onClick={() => setIsOpen(false)}>
                                                      <Button
                                                            variant={activeSection === "coupons" ? "secondary" : "ghost"}
                                                            className="w-full justify-start text-sm"
                                                      >
                                                            <div className="flex items-center mr-3">
                                                                  <Gift className="w-4 h-4" />
                                                                  <span className="ml-2">Coupons</span>
                                                            </div>
                                                      </Button>
                                                </Link>
                                          </div>
                                    )}
                              </div>
                        </div>
                  </nav>

                  {/* Logout Button Section */}
                  <div className="p-4 border-t border-border">
                        <Form method="post">
                              <Button
                                    type="submit"
                                    variant="outline"
                                    className="w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10 border-destructive/20 hover:border-destructive/30"
                              >
                                    <LogOut className="w-4 h-4 mr-3" />
                                    <span>Logout</span>
                              </Button>
                        </Form>
                  </div>

                  {/* Optional Footer Section */}
                  <div className="px-4 pb-2 pt-0">
                        <p className="text-xs text-muted-foreground text-center">
                              © {new Date().getFullYear()} mNET
                        </p>
                  </div>
            </div>
      );

      return (
            <div className="h-screen bg-white flex overflow-hidden">
                  {/* Mobile Menu Button */}
                  <div className="md:hidden fixed top-4 left-4 z-50">
                        <Sheet open={isOpen} onOpenChange={setIsOpen}>
                              <SheetTrigger asChild>
                                    <Button variant="outline" size="icon" className="bg-white shadow-md">
                                          <Menu className="h-4 w-4" />
                                    </Button>
                              </SheetTrigger>
                              <SheetContent side="left" className="w-64 p-0">
                                    <NavContent />
                              </SheetContent>
                        </Sheet>
                  </div>

                  {/* Desktop Static Sidebar */}
                  <div
                        className="hidden md:flex relative h-screen"
                        style={{ width: `${sidebarWidth}px` }}
                  >
                        <div className="w-full h-full overflow-y-auto">
                              <NavContent />
                        </div>

                        {/* Resize Handle */}
                        <button
                              type="button"
                              className="absolute right-0 top-0 w-1 h-full bg-transparent hover:bg-gray-300 cursor-col-resize transition-colors duration-200 group border-none outline-none focus:ring-2 focus:ring-primary"
                              onMouseDown={handleMouseDown}
                              aria-label="Resize sidebar"
                              onKeyDown={(e) => {
                                    if (e.key === 'ArrowLeft') {
                                          setSidebarWidth(prev => Math.max(200, prev - 10));
                                    } else if (e.key === 'ArrowRight') {
                                          setSidebarWidth(prev => Math.min(400, prev + 10));
                                    }
                              }}
                        >
                              <div className="w-full h-full relative">
                                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-16 bg-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-full"></div>
                              </div>
                        </button>
                  </div>

                  {/* Main Content */}
                  <div className="flex-1 min-w-0 h-screen overflow-y-auto">
                        <main className="h-full pt-16 md:pt-0 px-4 md:px-6">
                              <Outlet context={{ setIsOpen }} />
                        </main>
                  </div>
            </div>
      );
}
