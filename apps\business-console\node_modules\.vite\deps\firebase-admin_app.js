import {
  require_app
} from "./chunk-JMGCBF2D.js";
import "./chunk-DL7RI63B.js";
import "./chunk-BPR5XVLN.js";
import "./chunk-HTIRVODT.js";
import "./chunk-W43E3XBW.js";
import "./chunk-SJGIY2NI.js";
import {
  __toESM
} from "./chunk-N5SXXOWC.js";

// node_modules/firebase-admin/lib/esm/app/index.js
var import_app = __toESM(require_app());
var AppErrorCodes = import_app.default.AppErrorCodes;
var FirebaseAppError = import_app.default.FirebaseAppError;
var SDK_VERSION = import_app.default.SDK_VERSION;
var applicationDefault = import_app.default.applicationDefault;
var cert = import_app.default.cert;
var deleteApp = import_app.default.deleteApp;
var getApp = import_app.default.getApp;
var getApps = import_app.default.getApps;
var initializeApp = import_app.default.initializeApp;
var refreshToken = import_app.default.refreshToken;
export {
  AppErrorCodes,
  FirebaseAppError,
  SDK_VERSION,
  applicationDefault,
  cert,
  deleteApp,
  getApp,
  getApps,
  initializeApp,
  refreshToken
};
//# sourceMappingURL=firebase-admin_app.js.map
