import { ActionFunction, LoaderFunction } from "@remix-run/node";
import { use<PERSON><PERSON>cher, use<PERSON>oaderD<PERSON>, useNavigate, useSearchParams } from "@remix-run/react";
import { format, isValid } from "date-fns";
import { CalendarIcon, CircleX, PhoneCall, Search, Store } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { Button } from "~/components/ui/button";
import { Calendar } from "~/components/ui/calendar"; import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import ResponsivePagination from "~/components/ui/responsivePagination";
import { ResponsiveTable } from "~/components/ui/responsiveTable";
import { SalesAnalysisDashBoard } from "~/components/ui/salesAnalysisDashBoard";
import SalesDynamicSearchFilters from "~/components/ui/salesDynamicSearchFilters";
import SalesSearchFilters from "~/components/ui/salesSearchFilters";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "~/components/ui/tooltip";
import { getNetWorkSeller } from "~/services/netWorks";
import { getSalesAnalysis, getSearchFilters } from "~/services/tripsSummary";
import { NetWorkDetails } from "~/types/api/businessConsoleService/netWorkinfo";
import { BcSalesDashboardDto, SearchItems } from "~/types/api/businessConsoleService/SalesAnalysis"
import { withAuth, withResponse } from "~/utils/auth-utils";

export interface ActionData {
      selectedData: SearchItems[],
      sellerList: NetWorkDetails[],

}

interface LoaderData {
      salesAnalysis: BcSalesDashboardDto;
      selectedDate: string,
      dataTypeWise: string,
      selectedDays: number,
      selectedSellerId: number,
      selectedAgentId: number,
      selectedBuyerId: number,
      selectedLocalityId: number,
      matchBy: string,
      pageNo: number,
      pageSize: number
}
export const loader: LoaderFunction = withAuth(async ({ request }) => {
      const url = new URL(request.url);
      const date =
            url.searchParams.get("date") ||
            new Date(Date.now() + 24 * 60 * 60 * 1000)?.toISOString()?.split("T")[0];
      const dataType = url.searchParams.get("dataType") as string || "sellerwise";
      const selectedDays = Number(url.searchParams.get("days") || "8");
      const selectedSellerId = Number(url.searchParams.get("sellerId")) || null;
      const selectedAgentId = Number(url.searchParams.get("agentId")) || null;
      const selectedBuyerId = Number(url.searchParams.get("buyerId")) || null;
      const selectedLocalityId = Number(url.searchParams.get("areaId")) || null;
      const matchBy = url.searchParams.get("matchBy") || "";
      const pageNo = Number(url.searchParams.get("pageNo")) || 0;
      const pageSize = Number(url.searchParams.get("pageSize")) || 50;
      const dDate = url.searchParams.get("dDate") || ""


      try {
            let response = await getSalesAnalysis(date, dataType, selectedDays, selectedSellerId, selectedAgentId, selectedBuyerId, selectedLocalityId, matchBy, pageNo, pageSize, dDate, request)
            return withResponse({
                  salesAnalysis: response?.data,
                  selectedDate: date,
                  dataTypeWise: dataType,
                  selectedDays: selectedDays,
                  selectedSellerId: selectedSellerId,
                  selectedAgentId: selectedAgentId,
                  selectedBuyerId: selectedBuyerId,
                  selectedLocalityId: selectedLocalityId


            }, response?.headers)
      } catch (error) {
            console.error("Seller sales error:", error);
            throw new Response("Failed to fetch seller sales", { status: 500 });
      }
});

export const action: ActionFunction = async ({ request }) => {
      const formData = await request.formData();
      const intent = formData.get("intent") as string;
      const type = formData.get("type") as string
      const pageNo = Number(formData.get("pageNo")) || 0;
      const pageSize = Number(formData.get("size")) || 50;
      const matchBy = formData.get("matchBy") as string;
      const action = formData.get("action") as string;




      if (action === "sellerWise") {
            try {
                  const response = await getNetWorkSeller(1,
                        request
                  );

                  return withResponse({
                        sellerList: response?.data,



                  }, response?.headers);


            }
            catch (error) {
                  throw new Response("Failed to fetch get Sellers", { status: 500 });

            }
      }
      if (intent === type) {
            try {
                  const response = await getSearchFilters(
                        type,
                        matchBy,
                        pageNo,
                        pageSize,
                        request
                  );

                  return withResponse({
                        selectedData: response?.data,



                  }, response?.headers);


            }
            catch (error) {
                  throw new Response("Failed to fetch seller sales", { status: 500 });

            }
      }

};


export default function SalesAnalysis() {

      const { salesAnalysis, selectedDate, dataTypeWise, selectedDays, selectedSellerId, selectedAgentId, selectedBuyerId, selectedLocalityId, pageNo, pageSize } = useLoaderData<LoaderData>()
      const [searchParams] = useSearchParams();
      const searchParamsDataType = useMemo(() => searchParams.get("dataType") ?? dataTypeWise, [searchParams, dataTypeWise]);
      const searchParamsDays = useMemo(() => searchParams.get("days") ?? selectedDays?.toString(), [searchParams, selectedDays]);
      const searchParamsDate = useMemo(() => searchParams.get("date") ?? selectedDate, [searchParams, selectedDate]);
      const searchParamsDDate = searchParams.get("dDate")

      const searchParamsSellerId = Number(searchParams.get("sellerId") ?? selectedSellerId);
      const searchParamsAgentId = Number(searchParams.get("agentId") ?? selectedAgentId);
      const searchParamsBuyerId = Number(searchParams.get("buyerId") ?? selectedBuyerId);
      const searchParamsAreaId = Number(searchParams.get("areaId") ?? selectedLocalityId);
      const searchParamsPageNo = Number(searchParams.get("pageNo") ?? pageNo);
      const searchParamsPageSize = Number(searchParams.get("pageSize") ?? pageSize);
      // const location = useLocation();
      const selectedSellerName = searchParams.get("sellerName") || "";
      const selectedAgentName = searchParams.get("agentName") || "";
      const selectedBuyerName = searchParams.get("buyerName") || "";
      const selectedAreaName = searchParams.get("areaName") || "";

      const [dataType, setDataType] = useState(searchParamsDataType);
      useEffect(() => {
            if (dataType !== searchParamsDataType) setDataType(searchParamsDataType);
            if (days !== searchParamsDays) setDays(searchParamsDays);
            if (date.toISOString().split("T")[0] !== searchParamsDate) {
                  setDate(new Date(searchParamsDate));
            }
            if (searchParamsDDate) {
                  const formattedDDate = new Date(searchParamsDDate).toISOString().split("T")[0];

                  if (formattedDDate !== dDate?.toISOString().split("T")[0]) {
                        setDDate(new Date(searchParamsDDate)); // Only update if the date is actually different
                  }
            }

            if (selectedSellerData.id !== searchParamsSellerId) {
                  setSelectedSellerData({ id: searchParamsSellerId, name: selectedSellerName });
            }
            if (selectedAgentData.id !== searchParamsAgentId) {
                  setSelectedAgentData({ id: searchParamsAgentId, name: selectedAgentName });
            }
            if (selectedBuyerData.id !== searchParamsBuyerId) {
                  setSelectedBuyerData({ id: searchParamsBuyerId, name: selectedBuyerName });
            }
            if (selectedAreaData.id !== searchParamsAreaId) {
                  setSelectedAreaData({ id: searchParamsAreaId, name: selectedAreaName });
            }

      }, [searchParamsDataType, searchParamsDays, searchParamsDate, searchParamsSellerId, searchParamsAgentId, searchParamsBuyerId, searchParamsAreaId]);

      const [days, setDays] = useState(selectedDays.toString())
      const [dashboardData, setDashboardData] = useState<BcSalesDashboardDto>(salesAnalysis || {});
      const fetcher = useFetcher<BcSalesDashboardDto | null>();
      const navigate = useNavigate();
      const [date, setDate] = useState<Date>(
            selectedDate ? new Date(selectedDate) : new Date(new Date().setDate(new Date().getDate() + 1))
      );

      const [dDate, setDDate] = useState<Date | undefined>(
            searchParamsDDate ? new Date(searchParamsDDate) : undefined
      );
      const pageTotalSize = 50;

      const handleSubmit = () => {
            const formattedDate = new Date(date); // Format selected date
            const dformattedDate = dDate ? new Date(dDate) : undefined; // Keep dDate unchanged
            setDashboardData({


                  date: "",
                  dataType: "",
                  days: 0,
                  rows: [],
                  totalSales: [],
            }
            );

            const queryString = `?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=0&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}`;

            // Append dDate only if it's valid
            const finalUrl = dformattedDate ? `${queryString}&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : queryString;

            navigate(finalUrl);
      };

      useEffect(() => {
            if (salesAnalysis) {
                  setDashboardData(salesAnalysis);
            }
      }, [salesAnalysis]);

      console.log(salesAnalysis, "88888888")

      const groupedData = dashboardData?.rows?.reduce((acc, row) => {
            if (!acc[row.name]) {
                  acc[row.name] = {
                        id: row?.id?.toString(), sales: {},
                        lastOrderDate: row?.lastOrderedDate,
                        agentName: row?.agentName,
                        buyerMobile: (row?.buyerMobileNumber ?? '').toString()


                  };
            }

            row.cols.forEach(col => {
                  acc[row?.name].sales[col?.deliveryDate] = {
                        salesQty: col?.salesQty,
                        salesAmount: col?.salesAmount,
                        visit: col?.visits,
                        searchCount: col?.searchCount,
                        calls: col?.calls
                  };
            });

            return acc;
      }, {} as Record<string, { id: string; lastOrderDate: string; agentName: string, buyerMobile: string, sales: Record<string, { salesQty: number; salesAmount: number; visit: number; searchCount: number; calls: number }> }>);

      const uniqueDates = Array.from(
            new Set(dashboardData?.rows?.flatMap(row => row.cols.map(col => col.deliveryDate)))
      );


      const headers = [
            "Name",
            ...uniqueDates.map(date => `${format(date, "EEE dd/MM")}`)
      ];
      type FormattedRow = { id: string; name: string; lastOrderDate: string, agentName: string, buyerMobile: string } & Record<string, string | boolean>;

      const formattedTableData: FormattedRow[] = Object.entries(groupedData || {}).map(([name, group]) => {
            const { id, sales = {}, lastOrderDate, agentName, buyerMobile } = group || {};
            return {
                  id,
                  name,
                  lastOrderDate,
                  agentName,
                  buyerMobile,
                  // Add lastOrderDate
                  ...uniqueDates.reduce((acc, date) => {
                        acc[`${date}_qty`] = sales?.[date]?.salesQty ? String(sales[date].salesQty) : "-";
                        acc[`${date}_visit`] = sales?.[date]?.visit > 0;
                        acc[`${date}_search`] = sales?.[date]?.searchCount > 0;
                        acc[`${date}_call`] = sales?.[date]?.calls > 0;
                        acc[`${date}_qtycheck`] = sales?.[date]?.salesQty > 0;
                        return acc;
                  }, {} as Record<string, string | boolean>)
            };
      });


      const [showSearchForm, setShowSearchForm] = useState(false);
      const [showSearchAgentForm, setShowSearchAgentForm] = useState(false);
      const [showSearchBuyerForm, setShowSearchBuyerForm] = useState(false);
      const [showSearchAreaForm, setShowSearchAreaForm] = useState(false);


      const [selectedSellerData, setSelectedSellerData] = useState<SearchItems>({
            id: searchParamsSellerId,
            name: selectedSellerName
      });

      const [selectedAgentData, setSelectedAgentData] = useState<SearchItems>({
            id: searchParamsAgentId,
            name: selectedAgentName
      });

      const [selectedBuyerData, setSelectedBuyerData] = useState<SearchItems>({
            id: searchParamsBuyerId,
            name: selectedBuyerName
      });

      const [selectedAreaData, setSelectedAreaData] = useState<SearchItems>({
            id: searchParamsAreaId,
            name: selectedAreaName
      });
      // const [selectedSellerData, setSelectedSellerData] = useState<SearchItems>({ id: selectedSellerId, name: selectedSellerName });
      // const [selectedAgentData, setSelectedAgentData] = useState<SearchItems>({ id: selectedAgentId, name: selectedAgentName });
      // const [selectedBuyerData, setSelectedBuyerData] = useState<SearchItems>({ id: selectedBuyerId, name: selectedBuyerName });
      // const [selectedAreaData, setSelectedAreaData] = useState<SearchItems>({ id: selectedLocalityId, name: selectedAreaName });
      const [pageNum, setPageNum] = useState(0)
      const handleAddFilers = async (value: string) => {
            if (value === "seller") {

                  const formData = new FormData();
                  formData.append("action", "sellerWise");
                  sellerFetcher.submit(formData, { method: "POST" });

                  await setShowSearchForm(true)
            }
            else if (value === "agent") {
                  setShowSearchAgentForm(true)

                  const formData = new FormData();
                  formData.append("intent", value);
                  formData.append("type", "agent");
                  formData.append("pageNo", "0") as unknown as number;
                  formData.append("size", "100") as unknown as number;
                  agentFetcher.submit(formData, { method: "POST" });
            }
            else if (value === "buyer") {
                  setShowSearchBuyerForm(true)
                  const formData = new FormData();
                  formData.append("intent", value);
                  formData.append("type", "buyer");
                  formData.append("pageNo", "0") as unknown as number;
                  formData.append("size", "100") as unknown as number;

                  buyerFetcher.submit(formData, { method: "POST" });

            }
            else if (value === "locality") {
                  setShowSearchAreaForm(true)
                  const formData = new FormData();
                  formData.append("intent", value);
                  formData.append("type", "locality");
                  formData.append("pageNo", "0") as unknown as number;
                  formData.append("size", "100") as unknown as number;

                  localityFetcher.submit(formData, { method: "POST" });

            }


      }
      const handleSelectedSeller = (seller: SearchItems) => {
            setSelectedSellerData(seller);
            const formattedDate = new Date(date);
            const dformattedDate = dDate ? new Date(dDate) : "";

            navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${seller?.id}&agentId=${selectedAgentData?.id}&buyerId=${selectedBuyerData?.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${seller?.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`
            );
      }
      const handleSelectedAgent = (agent: SearchItems) => {
            setSelectedAgentData(agent);
            const formattedDate = new Date(date);
            const dformattedDate = dDate ? new Date(dDate) : "";
            navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${agent?.id}&buyerId=${selectedBuyerData?.id}&areaId=${selectedAreaData?.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${agent?.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
      }
      const handleSelectedBuyer = (buyer: SearchItems) => {
            setSelectedBuyerData(buyer);
            const formattedDate = new Date(date);
            const dformattedDate = dDate ? new Date(dDate) : "";

            navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${selectedAgentData?.id}&buyerId=${buyer?.id}&areaId=${selectedAreaData?.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData?.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
      }
      const handleSelectedArea = (locality: SearchItems) => {
            setSelectedAreaData(locality);
            const formattedDate = new Date(date);
            const dformattedDate = dDate ? new Date(dDate) : "";

            navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${selectedAgentData?.id}&buyerId=${selectedBuyerData?.id}&areaId=${locality?.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${locality?.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`

            );
      }
      const handleClearFilter = async (value: string) => {
            if (value === "seller") {
                  setSelectedSellerData({ id: null, name: "" })
                  const formattedDate = new Date(date);
                  const dformattedDate = dDate ? new Date(dDate) : "";

                  navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${null}&agentId=${selectedAgentData?.id}&buyerId=${selectedBuyerData?.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${""}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`
                  );

            }
            else if (value === "agent") {
                  setSelectedAgentData({ id: null, name: "" })
                  const formattedDate = new Date(date);
                  const dformattedDate = dDate ? new Date(dDate) : "";

                  navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${null}&buyerId=${selectedBuyerData?.id}&areaId=${selectedAreaData?.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${""}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`

                  )
            }
            else if (value === "buyer") {
                  setSelectedBuyerData({ id: null, name: "" })
                  const formattedDate = new Date(date);
                  const dformattedDate = dDate ? new Date(dDate) : "";

                  navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${selectedAgentData?.id}&buyerId=${null}&areaId=${selectedAreaData?.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${""}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`

                  );
            }
            else if (value === "locality") {
                  setSelectedAreaData({ id: null, name: "" })
                  const formattedDate = new Date(date);
                  const dformattedDate = dDate ? new Date(dDate) : "";

                  navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData?.id}&agentId=${selectedAgentData?.id}&buyerId=${selectedBuyerData?.id}&areaId=${null}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${""}&${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`

                  );
            }

      }
      const localityFetcher = useFetcher<ActionData>();
      const [localityList, setLocalityList] = useState<SearchItems[]>([]);

      useEffect(() => {
            if (localityFetcher.data?.selectedData) {
                  setLocalityList(localityFetcher.data.selectedData);
            }
      }, [localityFetcher.data]);
      const buyerFetcher = useFetcher<ActionData>();
      const [buyerList, setBuyerList] = useState<SearchItems[]>([]);
      const handlePageSizeChange = (newSize: string) => {
            setPageNum(Number(newSize))
            const formattedDate = new Date(date);
            const dformattedDate = dDate ? new Date(dDate) : "";

            navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=${dataType}&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${newSize}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
      };
      useEffect(() => {
            if (buyerFetcher.data?.selectedData) {
                  setBuyerList(buyerFetcher.data.selectedData);
            }
      }, [buyerFetcher.data?.selectedData]);

      // console.log(buyerList, "eeeeeeeeeeeeeee")
      const agentFetcher = useFetcher<ActionData>();
      const [agentList, setAgentList] = useState<SearchItems[]>([]);

      useEffect(() => {
            if (agentFetcher.data?.selectedData) {
                  setAgentList(agentFetcher.data.selectedData);
            }
      }, [agentFetcher.data?.selectedData]);


      const sellerFetcher = useFetcher<ActionData>();


      const [sellerList, setSellerList] = useState<NetWorkDetails[]>([]);

      const mappedSellerList = sellerList.map(({ sellerId, seller }) => ({
            id: sellerId ?? null, // If sellerId is undefined, set it to null
            name: seller,
      }));

      useEffect(() => {
            console.log("555555555555555")

            if (sellerFetcher.data?.sellerList) {
                  setSellerList(sellerFetcher.data.sellerList);
                  console.log(sellerFetcher.data.sellerList, "00000000000y7777")
            }
      }, [sellerFetcher.data?.sellerList]);


      console.log(sellerList, "#################33");



      useEffect(() => {
            if (fetcher.data && fetcher.state === "idle" && fetcher.data !== dashboardData) {
                  setDashboardData(fetcher.data);
            }
      }, [fetcher.data, fetcher.state]);
      const [keyword, setKeyword] = useState(""); // State for keyword input
      const [isKeywordEnabled, setIsKeywordEnabled] = useState(false); //s
      const [dDateSelected, setdDateSelected] = useState(false)
      const [open, setOpen] = useState(false); // Manage popover open state



      const handleClickItem = (value: string, selectedId: number, name: string) => {

            if (value === "sellerwise") {
                  setDataType("buyerwise");
                  const formattedDate = new Date(date);
                  const dformattedDate = dDate ? new Date(dDate) : "";

                  setSelectedSellerData({ id: selectedId, name: name })


                  navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=buyerwise&days=${days}&sellerId=${selectedId}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`,
                  );
            }
            else if (value === "agentwise") {
                  setDataType("buyerwise");
                  const formattedDate = new Date(date);
                  const dformattedDate = dDate ? new Date(dDate) : "";

                  setSelectedAgentData({ id: selectedId, name: name })


                  navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=buyerwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedId}&buyerId=${selectedBuyerData.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${name}&buyerName=${selectedBuyerData.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
            }
            else if (value === "localitywise") {
                  setDataType("buyerwise");
                  const formattedDate = new Date(date);
                  const dformattedDate = dDate ? new Date(dDate) : "";

                  setSelectedAreaData({ id: selectedId, name: name })


                  navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=buyerwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedBuyerData.id}&areaId=${selectedId}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${selectedBuyerData.name}&areaName=${name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
            }
            else if (value === "buyerwise") {
                  setDataType("itemwise");
                  const formattedDate = new Date(date);
                  const dformattedDate = dDate ? new Date(dDate) : "";

                  setSelectedBuyerData({ id: selectedId, name: name })


                  navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=itemwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${selectedId}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
            }



      }
      useEffect(() => {
            if (date || dataType || days || keyword?.length >= 3 || dDate) {
                  handleSubmit()
            }
      }, [date, dataType, days, keyword, dDate]);

      const [isOpen, setIsOpen] = useState(false);
      const [dDateOpen, setDdateOpen] = useState(false);


      const handleQtyClick = async (deliveryDate: string, row: unknown) => {
            const formattedDate = new Date(date);
            const dformattedDate = deliveryDate ? new Date(deliveryDate) : "";
            navigate(`?date=${format(formattedDate, "yyyy-MM-dd")}&dataType=sellerwise&days=${days}&sellerId=${selectedSellerData.id}&agentId=${selectedAgentData.id}&buyerId=${row?.id}&areaId=${selectedAreaData.id}&matchBy=${keyword}&pageNo=${0}&pageSize=${pageTotalSize}&sellerName=${selectedSellerData.name}&agentName=${selectedAgentData.name}&buyerName=${row?.name}&areaName=${selectedAreaData.name}${isValid(dformattedDate) ? `&dDate=${format(dformattedDate, "yyyy-MM-dd")}` : ""}`);
            await setdDateSelected(true)

      }

      return (

            <div>
                  <div className="flex flex-col my-5">
                        <h1 className="text-2xl font-bold">Sales Analysis</h1>
                  </div>

                  {/* Form Section */}
                  <div className="flex flex-col sm:flex-row sm:space-x-3 my-3">
                        <fetcher.Form
                              method="post"
                              className="flex flex-col sm:flex-row sm:space-x-2 space-y-2 sm:space-y-0 mb-3 sm:mb-0 w-full"
                        >
                              {/* Date Picker */}
                              <Popover open={isOpen} onOpenChange={setIsOpen}>
                                    <PopoverTrigger asChild>
                                          <Button variant="outline" className="w-full sm:w-[280px]" onClick={() => setIsOpen((prev) => !prev)}
                                          >
                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                {date ? format(date, "PPP") : "Pick a date"}
                                          </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0">
                                          <Calendar
                                                mode="single"
                                                selected={date}
                                                onSelect={(day) => {
                                                      if (day) setDate(day);
                                                      setIsOpen(false); // Close the popover

                                                }}
                                                initialFocus

                                          />
                                    </PopoverContent>
                              </Popover>

                              {/* Data Type Selection */}
                              <Select value={dataType} onValueChange={setDataType}>
                                    <SelectTrigger className="w-full sm:w-[180px]">
                                          <SelectValue placeholder="Select Data Type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                          <SelectItem value="sellerwise">Seller Wise</SelectItem>
                                          <SelectItem value="buyerwise">Shop Wise</SelectItem>
                                          <SelectItem value="localitywise">Locality Wise</SelectItem>
                                          <SelectItem value="agentwise">Agent Wise</SelectItem>
                                          <SelectItem value="itemwise">Item Wise</SelectItem>
                                          <SelectItem value="all">All</SelectItem>

                                    </SelectContent>
                              </Select>

                              {/* Days Selection */}
                              <Select value={days ? days : selectedDays.toString()} onValueChange={setDays}>
                                    <SelectTrigger className="w-full sm:w-[180px]">
                                          <SelectValue placeholder="Select Days" />
                                    </SelectTrigger>
                                    <SelectContent>
                                          <SelectItem value="8">7 days</SelectItem>
                                          <SelectItem value="15">14 days</SelectItem>
                                    </SelectContent>
                              </Select>
                        </fetcher.Form>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:flex-wrap gap-3 my-5 w-full p-4 bg-white rounded-xl shadow-md">
                        {[
                              { label: "Seller", data: selectedSellerData, type: "seller" },
                              { label: "Agent", data: selectedAgentData, type: "agent" },
                              { label: "Shop", data: selectedBuyerData, type: "buyer" },
                              { label: "Area", data: selectedAreaData, type: "locality" },

                        ].map(({ label, data, type }) => (
                              <div
                                    key={type}
                                    className="flex items-center gap-3 bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 shadow-sm hover:shadow-md transition"
                              >
                                    <p className="text-sm font-semibold text-gray-700">
                                          {label}: <span className="font-bold text-gray-900">{data?.name}</span>
                                    </p>
                                    {!data?.id ? (
                                          <Button
                                                variant="outline"
                                                size="sm"
                                                className="rounded-full text-xs bg-blue-500 text-white hover:bg-blue-600"
                                                onClick={() => handleAddFilers(type)}
                                          >
                                                + Select
                                          </Button>
                                    ) : (
                                          <Button
                                                variant="outline"
                                                size="sm"
                                                className="rounded-full text-xs bg-red-500 text-white hover:bg-red-600"
                                                onClick={() => handleClearFilter(type)}
                                          >
                                                <CircleX className="w-4 h-4" />
                                          </Button>
                                    )}
                              </div>
                        ))}

                        {/* Item Search Section */}
                        <div className="flex items-center gap-3 bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 shadow-sm hover:shadow-md transition">
                              <p className="text-sm font-semibold text-gray-700">Item:</p>
                              {isKeywordEnabled ? (
                                    <div className="flex items-center gap-2">
                                          <input
                                                type="text"
                                                value={keyword}
                                                onChange={(e) => setKeyword(e.target.value)}
                                                className="border border-gray-300 rounded-md px-3 py-1 text-sm w-32 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                          />
                                          <Button
                                                variant="outline"
                                                size="sm"
                                                className="rounded-full text-xs bg-red-500 text-white hover:bg-red-600"
                                                onClick={() => {
                                                      setKeyword("");
                                                      setIsKeywordEnabled(false);
                                                }}
                                          >
                                                <CircleX className="w-4 h-4" />
                                          </Button>
                                    </div>
                              ) : (
                                    <Button
                                          variant="outline"
                                          size="sm"
                                          className="rounded-full text-xs bg-blue-500 text-white hover:bg-blue-600"
                                          onClick={() => setIsKeywordEnabled(true)}
                                    >
                                          + Search
                                    </Button>
                              )}
                        </div>
                        <div className="flex items-center gap-3 bg-gray-100 border border-gray-200 rounded-lg px-4 py-3 shadow-sm hover:shadow-md transition">
                              <p className="text-sm font-semibold text-gray-700">D.Date:</p>
                              {dDateSelected ? (
                                    <div className="flex items-center gap-2">
                                          <Popover open={dDateOpen} onOpenChange={setDdateOpen}>
                                                <PopoverTrigger asChild>
                                                      <Button variant="outline" className="w-full sm:w-[280px]" onClick={() => setDdateOpen((prev) => !prev)}>
                                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                                            {dDate ? format(dDate, "PPP") : "Pick a date"}
                                                      </Button>
                                                </PopoverTrigger>
                                                <PopoverContent className="w-auto p-0">
                                                      <Calendar
                                                            mode="single"
                                                            selected={dDate}
                                                            onSelect={(day) => {
                                                                  if (day) setDDate(day);
                                                                  setDdateOpen(false)
                                                            }}
                                                            initialFocus
                                                      />
                                                </PopoverContent>
                                          </Popover>
                                          <Button
                                                variant="outline"
                                                size="sm"
                                                className="rounded-full text-xs bg-red-500 text-white hover:bg-red-600"
                                                onClick={() => {

                                                      setDDate(undefined)

                                                      setdDateSelected(false);
                                                }}
                                          >
                                                <CircleX className="w-4 h-4" />
                                          </Button>
                                    </div>
                              ) : (
                                    <Button
                                          variant="outline"
                                          size="sm"
                                          className="rounded-full text-xs bg-blue-500 text-white hover:bg-blue-600"
                                          onClick={() => setdDateSelected(true)}
                                    >
                                          + Select
                                    </Button>
                              )}
                        </div>
                  </div>
                  <div className="overflow-x-auto rounded-md border max-w-full">
                        <div className="overflow-x-auto rounded-md border max-w-full">
                              {uniqueDates.length === 1 ? (
                                    <table className="w-full border-collapse">
                                          <thead>
                                                <tr className="bg-gray-100">
                                                      <th className="py-2 px-2 text-center w-1/5">Name</th> {/* Reduced padding */}
                                                      <th className="py-2 px-2 text-center w-2/3">{format(uniqueDates[0], "EEE dd/MM")}</th> {/* Reduced padding */}
                                                </tr>
                                          </thead>
                                          <tbody>
                                                {formattedTableData.map(row => (

                                                      <tr key={row.id} className="text-center">
                                                            {/* Name Column with flex-wrap */}
                                                            <td
                                                                  className="py-2 px-4 text-center cursor-pointer text-blue-300 font-bold sticky left-0 bg-white z-10 shadow-md"
                                                                  onClick={() => handleClickItem(dashboardData?.dataType, Number(row.id), row.name)}
                                                            >
                                                                  <TooltipProvider>
                                                                        <Tooltip>
                                                                              <TooltipTrigger asChild>
                                                                                    <div className="flex flex-wrap justify-center items-center break-words whitespace-normal ">
                                                                                          {row.name}
                                                                                    </div>
                                                                              </TooltipTrigger>
                                                                              {dataType === "buyerwise" && <TooltipContent>
                                                                                    <p>AgentName: {row?.agentName || "-"}</p>
                                                                                    <p>BuyerMobile: {row?.buyerMobileNumber || "-"}</p>
                                                                              </TooltipContent>}
                                                                        </Tooltip>
                                                                  </TooltipProvider>
                                                            </td>

                                                            {/* Data Column */}
                                                            <td className="py-2 px-2 text-center w-2/3 ">
                                                                  <div className={`flex flex-row gap-1 justify-center items-center ${row?.lastOrderDate?.split("T")[0] >= uniqueDates[0] && row[`${uniqueDates[0]}_qtycheck`] ? "bg-yellow-100" : ""} `}>
                                                                        <button
                                                                              className={`font-medium cursor-pointer ${dataType !== "buyerwise" ? "pointer-events-none opacity-50" : ""}`}
                                                                              onClick={() => handleQtyClick(uniqueDates[0], row)}
                                                                              aria-disabled={dataType !== "buyerwise"}
                                                                        >

                                                                              {row[`${uniqueDates[0]}_qty`] || "-"}
                                                                        </button>
                                                                        <span className="font-medium">{row[`${uniqueDates[0]}_visit`] && <Store color="red" size={15} />}</span>
                                                                        <span className="font-medium">{row[`${uniqueDates[0]}_search`] && !row[`${uniqueDates[0]}_qtycheck`] && <Search color="orange" size={15} />}</span>
                                                                        <span className="font-medium">{row[`${uniqueDates[0]}_call`] && <PhoneCall color="green" size={15} />}</span>
                                                                  </div>
                                                            </td>
                                                      </tr>
                                                ))}
                                          </tbody>
                                    </table>
                              ) : (
                                    <ResponsiveTable
                                          headers={headers}
                                          data={formattedTableData}
                                          renderRow={(row) => (


                                                <tr key={row.id} className="text-center">
                                                      <td
                                                            className="py-2 px-4 text-center cursor-pointer text-blue-300 font-bold sticky left-0 bg-white z-10 shadow-md"
                                                            onClick={() => handleClickItem(dashboardData?.dataType, Number(row.id), row.name)}
                                                      >
                                                            <TooltipProvider>
                                                                  <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                              <span className="cursor-pointer underline">{row.name}</span>
                                                                        </TooltipTrigger>
                                                                        {dataType === "buyerwise" && <TooltipContent>
                                                                              <p>AgentName: {row?.agentName || "-"}</p>
                                                                              <p>BuyerMobile: {row?.buyerMobile || "-"}</p>
                                                                        </TooltipContent>}
                                                                  </Tooltip>
                                                            </TooltipProvider>
                                                      </td>

                                                      {uniqueDates.map(date => (
                                                            <td key={`${row.id}-${date}`} className="py-2 px-7 text-center w-auto min-w-[100px]">
                                                                  <div className={`flex flex-row gap-1 justify-center items-center ${row?.lastOrderDate?.split("T")[0] >= date && row[`${date}_qtycheck`] ? "bg-yellow-100" : ""}`}>
                                                                        <button
                                                                              className={`font-medium ${dataType !== "buyerwise" ? "pointer-events-none opacity-50" : "cursor-pointer"}`}
                                                                              onClick={() => handleQtyClick(date, row)}
                                                                              disabled={dataType !== "buyerwise"}
                                                                        >
                                                                              {row[`${date}_qty`] || "-"}
                                                                        </button>
                                                                        <span className="font-medium">{row[`${date}_visit`] && <Store color="red" size={15} />}</span>
                                                                        <span className="font-medium">{row[`${date}_search`] && !row[`${date}_qtycheck`] && <Search color="orange" size={15} />}</span>
                                                                        <span className="font-medium">{row[`${date}_call`] && <PhoneCall color="green" size={15} />}</span>
                                                                  </div>
                                                            </td>
                                                      ))}
                                                </tr>
                                          )}
                                    />
                              )}
                        </div>






                        <div className="flex items-center justify-end space-x-2 py-4 overflow-x-auto whitespace-nowrap">
                              <h2 className="shrink-0">Current Page: {pageNum + 1}</h2>
                              <div className="overflow-x-auto">
                                    <ResponsivePagination
                                          totalPages={50}
                                          currentPage={pageNum}
                                          onPageChange={(pageNum) => handlePageSizeChange(pageNum.toString())}

                                    />
                              </div>
                        </div>
                  </div>
                  <div>
                        <SalesAnalysisDashBoard salesData={dashboardData} />
                  </div>


                  <SalesSearchFilters title={"Select Seller Filter"} isOpen={showSearchForm} onClose={() => setShowSearchForm(false)} items={mappedSellerList || []} onSelect={(seller) => handleSelectedSeller(seller)} />
                  <SalesSearchFilters title={"Select Agent Filter"} isOpen={showSearchAgentForm} onClose={() => setShowSearchAgentForm(false)} items={agentList || []} onSelect={(agent) => handleSelectedAgent(agent)} />
                  <SalesDynamicSearchFilters title={"Select Buyer Filter"} isOpen={showSearchBuyerForm} onClose={() => setShowSearchBuyerForm(false)} itemList={buyerList || []} onSelect={(buyer) => handleSelectedBuyer(buyer)} type="buyer" />
                  <SalesDynamicSearchFilters title={"Select Locality Filter"} isOpen={showSearchAreaForm} onClose={() => setShowSearchAreaForm(false)} itemList={localityList || []} onSelect={(locality) => handleSelectedArea(locality)} type="area" />

            </div>
      )

}



