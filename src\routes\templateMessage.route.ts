import express from 'express';
import { TemplateMessageController } from '@/controllers/templateMessageController.js';

class TemplateMessageRoute {
    public router = express.Router();
    private templateMessageController = new TemplateMessageController();

    constructor() {
        this.initializeRoutes();
    }

    private initializeRoutes() {
        // Route to send a template message
        this.router.post(
            '/api/whatsapp/send-template',
            this.templateMessageController.sendTemplateMessage.bind(this.templateMessageController)
        );

        // Route to get available templates
        this.router.get(
            '/api/whatsapp/templates',
            this.templateMessageController.getAvailableTemplates.bind(this.templateMessageController)
        );
    }
}

export default TemplateMessageRoute; 