import { ActionFunctionArgs, json, redirect } from "@remix-run/node";
import { addTicketNote } from "~/services/ticketService";

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const ticketId = Number(formData.get("ticketId"));
  const note = formData.get("note") as string;
  
  if (!ticketId || !note) {
    return json({ 
      success: false, 
      error: "Missing required fields" 
    });
  }
  
  try {
    const result = await addTicketNote(ticketId, { ticketId, note }, request);
    
    if (result.statusCode >= 400) {
      return json({ 
        success: false, 
        error: "Failed to add note" 
      });
    }
    
    return json({ 
      success: true,
      message: "Note added successfully",
      note: result.data
    });
  } catch (error) {
    console.error("Error adding note:", error);
    return json({ 
      success: false, 
      error: "Failed to add note" 
    });
  }
};

// Redirect direct visits to this route back to the tickets page
export const loader = () => redirect("/home/<USER>"); 