

import { LoaderFunction } from "@remix-run/node";
import { use<PERSON><PERSON><PERSON>, useLoaderData, useNavigate } from "@remix-run/react";
import { BcBuyerDto, } from "~/types/api/businessConsoleService/salesinfo";
import { getBuyerData, updateBuyerAttribute } from "~/services/salesinfoDetails";
import { withAuth, withResponse } from "@utils/auth-utils";
import { useState } from "react";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "~/components/ui/pagination";
import { ResponsiveTable } from "~/components/ui/responsiveTable";
import SearchBar from "~/components/ui/searchBar";
import { Pencil, Save, X } from "lucide-react";
import { Input } from "~/components/ui/input";
import { Switch } from "~/components/ui/switch";
import SpinnerLoader from "~/components/loader/SpinnerLoader";

export interface LoaderData {
      buyerData: BcBuyerDto[],
}
export const loader: LoaderFunction = withAuth(async ({ request }) => {
      const url = new URL(request.url);
      const searchBy = url.searchParams.get("search") || ""
      const page = Number(url.searchParams.get("pageNo")) || 0;
      const pageSize = Number(url.searchParams.get("pageSize")) || 10;
      try {
            let response = null;

            response = await getBuyerData(page, pageSize, searchBy, request)

            return withResponse({ buyerData: response?.data }, response?.headers)
      } catch (error) {
            console.error("Seller sales error:", error);
            throw new Response("Failed to fetch seller sales", { status: 500 });
      }
});
export const action = withAuth(async ({ request }) => {
      const formData = await request.formData();
      const intent = formData.get("_intent") as string;

      const buyerId = Number(formData.get("buyerId"));
      if (isNaN(buyerId)) {
            return new Response("Invalid buyer ID", { status: 400 });
      }

      const AttributeType = formData.get("attribute") as string;
      if (!AttributeType) {
            return new Response("Missing attribute type", { status: 400 });
      }

      try {
            let response;
            if (intent === "updateBuyerName") {
                  const buyerName = formData.get("buyerName") as string;
                  if (!buyerName) return new Response("Missing buyer name", { status: 400 });

                  response = await updateBuyerAttribute(buyerId, AttributeType, buyerName, request);
            }
            else if (intent === "updateLatLon") {
                  const latitude = formData.get("latitude");
                  const longitude = formData.get("longitude");
                  if (!latitude || !longitude) return new Response("Missing lat/lon", { status: 400 });

                  response = await updateBuyerAttribute(buyerId, AttributeType, `${latitude}, ${longitude}`, request);
            }
            else if (intent === "updateStatus") {
                  const status = formData.get("status") as string;
                  if (status === null) return new Response("Missing status", { status: 400 });

                  response = await updateBuyerAttribute(buyerId, AttributeType, status, request);
            }
            else {
                  return new Response("Invalid intent", { status: 400 });
            }

            return withResponse({ buyerData: response?.data || {} }, response.headers);
      } catch (error) {
            console.error("Update failed:", error);
            return new Response("Failed to update", { status: 500 });
      }
});


export default function BuyerManagement() {
      const navigate = useNavigate();
      const { buyerData } = useLoaderData<LoaderData>()
      const [currentPage, setCurrentPage] = useState(0)
      const itemsPerPage = 50;
      const [searchTerm, setSearchTerm] = useState('')
      const handlePageChange = (newPage: number) => {
            setCurrentPage(newPage);
            navigate(`?search=${searchTerm}&pageNo=${newPage}&pageSize=${itemsPerPage}`);
      };
      const BuyerManagementHeader = [
            "BuyerID",
            "BuyerName",
            "MobileNumber",
            "Address",
            "Lat/Lan",
            "W.Balance",
            "Status",
      ];

      const fetcher = useFetcher<BcBuyerDto[]>()
      const isLoading = fetcher.state !== "idle";

      const handleSearch = async (query: string) => {
            setSearchTerm(query);
            if (query.length >= 3) {
                  navigate(`?search=${query}&pageNo=${0}&pageSize=${itemsPerPage}`)

            }
            else if (query.length === 0) {
                  // Reset when query is empty
                  navigate(`?pageNo=${0}&pageSize=${itemsPerPage}`);
                  setSearchTerm("")

            }
      };
      const handleCancel = () => {
            navigate(`?&pageNo=${0}&pageSize=${itemsPerPage}`)
            setSearchTerm("")

      };

      const [updateBuyerName, setUpdateBuyerName] = useState<{ [key: number]: string }>({});
      const [isBuyerUpdate, SetIsBuyerUpdate] = useState<{ [key: number]: boolean }>({});

      const handleUpdateName = (buyerId: number, val: string) => {
            setUpdateBuyerName((prev) => ({ ...prev, [buyerId]: val }));
      };
      const [updateLatLon, setUpdateLatLon] = useState<{ [key: number]: { lat: string, lon: string } }>({});
      const [isBuyerLatLonUpdate, setIsBuyerLatLonUpdate] = useState<{ [key: number]: boolean }>({});
      const handleSave = (buyerId: number,) => {
            const formData = new FormData()
            formData.append("_intent", "updateBuyerName");
            formData.append("buyerId", buyerId.toString());
            formData.append("buyerName", updateBuyerName[buyerId]);
            formData.append("attribute", "name")
            fetcher.submit(formData, { method: "put" })


            SetIsBuyerUpdate((prev) => ({ ...prev, [buyerId]: false }));
      };
      const [buyerStatus, setBuyerStatus] = useState<{ [key: number]: boolean }>({});
      const handleUpdateLatLon = (buyerId: number, field: "lat" | "lon", val: string) => {
            setUpdateLatLon((prev) => ({
                  ...prev,
                  [buyerId]: { ...prev[buyerId], [field]: val }
            }));
      };
      const handleSaveLatLon = (buyerId: number) => {
            const latLon = updateLatLon[buyerId];
            if (!latLon?.lat || !latLon?.lon) return;
            const formData = new FormData();
            formData.append("_intent", "updateLatLon");
            formData.append("buyerId", buyerId.toString());
            formData.append("latitude", latLon.lat);
            formData.append("longitude", latLon.lon);
            formData.append("attribute", "latlong");

            fetcher.submit(formData, { method: "put" });
            setIsBuyerLatLonUpdate((prev) => ({ ...prev, [buyerId]: false }));
      };
      const handleToggleStatus = (buyerId: number, currentStatus: boolean) => {
            const newStatus = !currentStatus;
            setBuyerStatus((prev) => ({ ...prev, [buyerId]: newStatus }));
            const formData = new FormData();
            formData.append("_intent", "updateStatus");
            formData.append("buyerId", buyerId.toString());
            formData.append("status", newStatus.toString()); // Convert boolean to string
            formData.append("attribute", "status");

            fetcher.submit(formData, { method: "put" });
      };
      return (
            <div className="container mx-auto w-full p-6">
                  <div className="flex flex-row justify-between items-center">
                        <div className=" items-center my-3">
                              <h1 className="text-2xl font-bold">BuyerManagement</h1>
                        </div>
                        <div className="flex my-3 w-full justify-start">
                              <SearchBar onSearch={handleSearch} onCancel={handleCancel} isLoading={isLoading} />
                        </div>

                  </div>
                  <div>
                        <SpinnerLoader loading={isLoading} />
                        <ResponsiveTable
                              headers={BuyerManagementHeader}
                              data={buyerData
                              }
                              renderRow={(row) => (
                                    <tr key={row.buyerId} className="border-b">
                                          <td className="py-2 px-3 font-medium text-center">{row.buyerId}</td>
                                          <td className="py-2 px-3 cursor-pointer text-center w-full">

                                                {isBuyerUpdate[row.buyerId] ? <>
                                                      <div className="flex flex-row gap-2 justify-center ">
                                                            <Input
                                                                  type="text"
                                                                  value={updateBuyerName[row.buyerId] ?? row.name}
                                                                  onChange={(e) => handleUpdateName(row.buyerId, e.target.value)}
                                                                  className=" px-2 py-1 border border-gray-300 rounded-md"

                                                            />

                                                            <Save size={24} onClick={() => handleSave(row.buyerId)} />
                                                            <X
                                                                  color="red"
                                                                  size={24}
                                                                  className="cursor-pointer text-red-500"
                                                                  onClick={() => SetIsBuyerUpdate({})} // Close all inputs
                                                            />
                                                      </div>


                                                </> : <div className="flex flex-row gap-2 items-center justify-center">
                                                      {row.name || "-"}
                                                      <Pencil size={15} onClick={() => SetIsBuyerUpdate({ [row.buyerId]: true })} />
                                                </div>}


                                          </td>
                                          <td className="py-2 px-3 text-center">
                                                {row?.mobileNumber || "-"}
                                          </td>
                                          <td className="py-2 px-3 text-center">
                                                {row?.address || "-"}
                                          </td>
                                          <td className="py-2 px-3 text-center w-40">
                                                {isBuyerLatLonUpdate[row.buyerId] ? (
                                                      <div className="flex flex-row gap-2 justify-center items-center">
                                                            <Input
                                                                  type="text"
                                                                  value={updateLatLon[row.buyerId]?.lat ?? row.latitude}
                                                                  onChange={(e) => handleUpdateLatLon(row.buyerId, "lat", e.target.value)}
                                                                  className="w-20 px-2 py-1 border border-gray-300 rounded-md"
                                                                  placeholder="Lat"
                                                            />
                                                            <Input
                                                                  type="text"
                                                                  value={updateLatLon[row.buyerId]?.lon ?? row.longitude}
                                                                  onChange={(e) => handleUpdateLatLon(row.buyerId, "lon", e.target.value)}
                                                                  className="w-20 px-2 py-1 border border-gray-300 rounded-md"
                                                                  placeholder="Lon"
                                                            />
                                                            <Save size={24} onClick={() => handleSaveLatLon(row.buyerId)} />
                                                            <X color="red" size={24} className="cursor-pointer text-red-500"
                                                                  onClick={() => setIsBuyerLatLonUpdate({})} />
                                                      </div>
                                                ) : (
                                                      <div className="flex flex-row gap-2 justify-center items-center">
                                                            {row.latitude}, {row.longitude}
                                                            <Pencil size={24} onClick={() => setIsBuyerLatLonUpdate({ [row.buyerId]: true })} className="cursor-pointer" />
                                                      </div>
                                                )}
                                          </td>


                                          <td className="py-2 px-3 text-center">
                                                {row.walletBalance > 0 ? row?.walletBalance.toFixed(1) : "-"}
                                          </td>

                                          <td className="py-2 px-3 text-center">
                                                <div className="flex items-center justify-center space-x-2">
                                                      <Switch
                                                            checked={!(buyerStatus[row.buyerId] || row.status)}
                                                            onCheckedChange={() => handleToggleStatus(row.buyerId, buyerStatus[row.buyerId])}
                                                      />


                                                </div>
                                          </td>


                                    </tr>
                              )
                              }
                              emptyMessage="No data available for the selected filters."
                        />
                  </div>
                  <div className="flex justify-center items-center mt-6">
                        <Pagination className="flex items-center space-x-2 bg-white shadow-md rounded-lg px-4 py-2">
                              <PaginationContent className="flex items-center space-x-2">
                                    {currentPage > 0 && (
                                          <PaginationItem>
                                                <PaginationPrevious
                                                      onClick={() => handlePageChange(currentPage - 1)}
                                                      className="px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition cursor-pointer"
                                                />
                                          </PaginationItem>
                                    )}
                                    <PaginationItem>
                                          <PaginationLink className="px-4 py-1 bg-blue-500 text-white rounded-md shadow-md">
                                                {currentPage + 1}
                                          </PaginationLink>
                                    </PaginationItem>
                                    <PaginationItem>
                                          <PaginationNext
                                                onClick={() => handlePageChange(currentPage + 1)}
                                                className="px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition cursor-pointer"
                                          />
                                    </PaginationItem>
                              </PaginationContent>
                        </Pagination>
                  </div>

            </div >

      );
}
