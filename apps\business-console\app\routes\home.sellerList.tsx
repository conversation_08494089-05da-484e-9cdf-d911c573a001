import { json, <PERSON>aderFunction } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { ResponsiveTable } from "~/components/ui/responsiveTable";
import { Switch } from "~/components/ui/switch";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { getSellerList } from "~/services/masterItemCategories";
import { Seller } from "~/types/api/businessConsoleService/MasterItemCategory";
import { NetWorkDetails } from "~/types/api/businessConsoleService/netWorkinfo";
import { withAuth, withResponse } from "~/utils/auth-utils";
export const loader = withAuth(async ({ request }) => {
      try {
            const response = await getSellerList(request);
            return withResponse({ data: response.data }, response.headers);
      }
      catch (error) {
            if (error instanceof Response && error.status === 404) {
                  throw json({ error: "SellerList pg Not found" }, { status: 404 });
            }
            throw new Response("Failed to fetch SellerList ", { status: 500 });
      }

})

export default function SellerList() {

      const sellerList = useLoaderData<{ data: Seller[] }>()
      const [searchTerm, setSearchTerm] = useState('')

      const handleSearch = (x: Seller) => {
            return (x.name.toLowerCase().includes(searchTerm.toLowerCase())

            )
      }
      const navigate = useNavigate()

      const SellerHeaders = [
            "Id",
            "Seller Name",
            "Status",
            "",
            "",

      ];
      return (
            <div className="container mx-auto p-6">
                  <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold">Sellers</h1>
                  </div>

                  <ResponsiveTable
                        headers={SellerHeaders}
                        data={
                              sellerList?.data
                        }
                        renderRow={(row) => (
                              <tr key={row.id} className="border-b">
                                    <td className="py-2 px-3 font-medium text-left">{row.id}</td>
                                    <td className="py-2  text-right text-blue-500 cursor-pointer flex-wrap">
                                          <span
                                                onClick={() =>
                                                      navigate(`/home/<USER>
                                                }
                                          >
                                                {row.name}
                                          </span>  {row?.name || "-"}
                                    </td>

                                    <td className="py-2 px-3 text-right">
                                          <span className={`px-2 py-1 rounded-full text-xs ${row.enabled === true ? 'bg-red-100 text-red-800' : 'bg-green-200 text-green-800'}`}>{row?.enabled === true ? "Disabled" : "Active"}</span>
                                    </td>
                                    <td className="py-2 px-20 text-right">

                                    </td>


                              </tr>
                        )}
                  />
                  {/* <div className="flex justify-between mb-4">
                        <Input
                              placeholder="Search By Seller Name"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"
                        />
                  </div>
                  <Table>
                        <TableHeader>
                              <TableRow>
                                    <TableHead className="cursor-pointer" >Id</TableHead>
                                    <TableHead className="cursor-pointer" >Seller Name </TableHead>
                                    <TableHead className="cursor-pointer" >Status</TableHead>
                              </TableRow>
                        </TableHeader>
                        <TableBody>
                              {sellerList.data?.filter((x) => handleSearch(x)).sort((a, b) => a.name.localeCompare(b.name)).map((item) => {

                                    return (
                                          <TableRow key={item.id}>
                                                <TableCell>{item.id}</TableCell>
                                                <TableCell className="cursor-pointer text-blue-500 font-bold text-md" onClick={() => navigate(`/home/<USER>/TableCell>
                                                <TableCell> <Switch checked={item.enabled} /></TableCell>
                                          </TableRow>
                                    )
                              })}
                        </TableBody>
                  </Table> */}
                  <Button className="fixed bottom-5 right-5 rounded-full" onClick={() => navigate("/home/<USER>")}>+ Add Seller</Button>


            </div>
      )



}