import { useState, useEffect } from "react";

interface SearchBarProps {
      onSearch: (query: string) => void;
      onCancel: () => void;
      isLoading: boolean;
}

export default function SearchBar({ onSearch, onCancel, isLoading }: SearchBarProps) {
      const [query, setQuery] = useState("");

      useEffect(() => {
            const handler = setTimeout(() => {
                  if (query.trim() !== "" && query.length >= 3) {
                        onSearch(query);
                  }
            }, 500);

            return () => clearTimeout(handler);
      }, [query]);

      return (
            <div className="relative w-full max-w-md mx-auto">
                  <input
                        type="text"
                        placeholder="Search..."
                        value={query}
                        onChange={(e) => setQuery(e.target.value)}
                        className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {query && (
                        <button
                              onClick={() => {
                                    setQuery("");
                                    onCancel();
                              }}
                              className="absolute right-3 top-2 text-gray-600 hover:text-red-500"
                        >
                              ✕
                        </button>
                  )}
                  {isLoading && <span className="absolute right-10 top-2 animate-spin">⏳</span>}
            </div>
      );
}
