// app/types/index.ts

export type NodeEnv = "production" | "development";
export type ServerEnv = "production" | "development" | "uat";

export interface User {
  userId: number;
  userName: string;
  businessName: string;
  buyerId: number;
  sellerId: number;
  userPermissions?: string[];
  userDetails: UserDetails;
}

export interface UserDetails {
  userId: number;
  sellerId: number;
  mobileNumber: string;
  roles?: string[];
}

export interface IframeData {
  buyerData: {
    buyerId: number;
    mobileNumber: string;
  };
  sellerData: {
    sellerId: number;
    mobileNumber: string;
  };
}
