/**
 * NotificationLog Entity Tests
 * 
 * Tests for enhanced NotificationLog entity with campaign support,
 * WhatsApp tracking, and ISO timestamp features.
 */

// Mock testing functions since no test framework is configured
const describe = (name: string, fn: () => void) => {
    console.log(`\n🧪 ${name}`);
    fn();
};

const test = (name: string, fn: () => void) => {
    console.log(`  ⚙️ ${name}`);
    try {
        fn();
        console.log(`  ✅ ${name} - PASSED`);
    } catch (err) {
        console.error(`  ❌ ${name} - FAILED:`, err);
    }
};

const beforeEach = (fn: () => void) => {
    // Initialize before each test
    fn();
};

const expect = (actual: any) => ({
    toBe: (expected: any) => {
        if (actual !== expected) {
            throw new Error(`Expected ${expected}, got ${actual}`);
        }
    },
    toEqual: (expected: any) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
            throw new Error(`Expected ${JSON.stringify(expected)}, got ${JSON.stringify(actual)}`);
        }
    },
    toBeDefined: () => {
        if (actual === undefined || actual === null) {
            throw new Error(`Expected value to be defined, got ${actual}`);
        }
    },
    toBeUndefined: () => {
        if (actual !== undefined) {
            throw new Error(`Expected value to be undefined, got ${actual}`);
        }
    },
    toContain: (item: any) => {
        if (!actual || !actual.includes(item)) {
            throw new Error(`Expected array to contain ${item}`);
        }
    }
});

import { 
    NotificationLog, 
    NotificationChannel, 
    NotificationStatus, 
    CampaignType, 
    MessageCategory, 
    CustomerSegment,
    NotificationLogTable
} from '../../database/entities/NotificationLog.js';

describe('NotificationLog Entity', () => {
    describe('Enums', () => {
        test('NotificationChannel enum should have correct values', () => {
            expect(NotificationChannel.WHATSAPP).toBe('WHATSAPP');
            expect(NotificationChannel.FIREBASE).toBe('FIREBASE');
            // Only test values that actually exist
        });

        test('NotificationStatus enum should have correct values', () => {
            expect(NotificationStatus.PENDING).toBe('PENDING');
            expect(NotificationStatus.SENT).toBe('SENT');
            expect(NotificationStatus.DELIVERED).toBe('DELIVERED');
            expect(NotificationStatus.READ).toBe('READ');
            expect(NotificationStatus.FAILED).toBe('FAILED');
        });

        test('CampaignType enum should have correct values', () => {
            expect(CampaignType.MARKETING).toBe('MARKETING');
            expect(CampaignType.PROMOTIONAL).toBe('PROMOTIONAL');
            expect(CampaignType.TRANSACTIONAL).toBe('TRANSACTIONAL');
            // Only test values that actually exist
        });

        test('MessageCategory enum should have correct values', () => {
            expect(MessageCategory.DELIVERY_UPDATE).toBe('DELIVERY_UPDATE');
            expect(MessageCategory.PROMOTIONAL_OFFER).toBe('PROMOTIONAL_OFFER');
            expect(MessageCategory.GENERAL).toBe('GENERAL');
            // Only test values that actually exist
        });

        test('CustomerSegment enum should have correct values', () => {
            expect(CustomerSegment.NEW_CUSTOMER).toBe('NEW_CUSTOMER');
            expect(CustomerSegment.RETURNING_CUSTOMER).toBe('RETURNING_CUSTOMER');
            expect(CustomerSegment.VIP_CUSTOMER).toBe('VIP_CUSTOMER');
            expect(CustomerSegment.HIGH_VALUE).toBe('HIGH_VALUE');
            expect(CustomerSegment.LOW_ENGAGEMENT).toBe('LOW_ENGAGEMENT');
            expect(CustomerSegment.DORMANT).toBe('DORMANT');
            expect(CustomerSegment.GENERAL).toBe('GENERAL');
        });
    });

    describe('NotificationLog Structure', () => {
        let mockNotificationLog: NotificationLog;

        beforeEach(() => {
            const timestamp = Date.now();
            mockNotificationLog = {
                // Core fields
                notificationId: 'test-notification-123',
                timestamp,
                timestampISO: '2024-01-15T10:30:00.000+05:30',
                businessId: 'business-123',
                mobileNumber: '+919876543210',
                channel: NotificationChannel.WHATSAPP,
                recipient: '+919876543210',
                
                // Campaign fields
                campaignId: 'summer_sale_2024',
                campaignName: 'Summer Sale Campaign',
                campaignType: CampaignType.PROMOTIONAL,
                messageCategory: MessageCategory.PROMOTIONAL_OFFER,
                customerSegment: CustomerSegment.HIGH_VALUE,
                tags: ['summer', 'sale', 'premium'],
                
                // WhatsApp tracking
                whatsappMessageId: 'wamid.abc123',
                whatsappStatus: NotificationStatus.SENT,
                
                // Webhook integration
                webhookLogId: 'webhook-log-456',
                
                // Message data
                inputPayload: { templateName: 'summer_offer' },
                providerRequest: { messaging_product: 'whatsapp' },
                providerResponse: { messages: [{ id: 'wamid.abc123' }] },
                
                // Status tracking
                status: NotificationStatus.SENT,
                errorMessage: undefined, // Changed from null to undefined
                retryCount: 0,
                lastUpdated: timestamp,
                lastUpdatedISO: '2024-01-15T10:30:00.000+05:30',
                
                // Analytics timestamps
                sentAt: timestamp,
                sentAtISO: '2024-01-15T10:30:00.000+05:30',
                deliveredAt: timestamp + 5000,
                deliveredAtISO: '2024-01-15T10:30:05.000+05:30',
                readAt: timestamp + 10000,
                readAtISO: '2024-01-15T10:30:10.000+05:30',
                failedAt: undefined,
                failedAtISO: undefined
            };
        });

        test('should have all required core fields', () => {
            expect(mockNotificationLog.notificationId).toBeDefined();
            expect(mockNotificationLog.timestamp).toBeDefined();
            expect(mockNotificationLog.businessId).toBeDefined();
            expect(mockNotificationLog.mobileNumber).toBeDefined();
            expect(mockNotificationLog.channel).toBeDefined();
            expect(mockNotificationLog.recipient).toBeDefined();
            expect(mockNotificationLog.status).toBeDefined();
        });

        test('should have ISO timestamp fields for all timestamp fields', () => {
            expect(mockNotificationLog.timestampISO).toBeDefined();
            expect(mockNotificationLog.lastUpdatedISO).toBeDefined();
            expect(mockNotificationLog.sentAtISO).toBeDefined();
            expect(mockNotificationLog.deliveredAtISO).toBeDefined();
            expect(mockNotificationLog.readAtISO).toBeDefined();
        });

        test('should support campaign fields', () => {
            expect(mockNotificationLog.campaignId).toBe('summer_sale_2024');
            expect(mockNotificationLog.campaignName).toBe('Summer Sale Campaign');
            expect(mockNotificationLog.campaignType).toBe(CampaignType.PROMOTIONAL);
            expect(mockNotificationLog.messageCategory).toBe(MessageCategory.PROMOTIONAL_OFFER);
            expect(mockNotificationLog.customerSegment).toBe(CustomerSegment.HIGH_VALUE);
            expect(mockNotificationLog.tags).toEqual(['summer', 'sale', 'premium']);
        });

        test('should support WhatsApp tracking fields', () => {
            expect(mockNotificationLog.whatsappMessageId).toBe('wamid.abc123');
            expect(mockNotificationLog.whatsappStatus).toBe(NotificationStatus.SENT);
        });

        test('should support webhook integration field', () => {
            expect(mockNotificationLog.webhookLogId).toBe('webhook-log-456');
        });

        test('should support analytics timestamp fields', () => {
            expect(mockNotificationLog.sentAt).toBeDefined();
            expect(mockNotificationLog.deliveredAt).toBeDefined();
            expect(mockNotificationLog.readAt).toBeDefined();
            expect(mockNotificationLog.failedAt).toBeUndefined();
        });
    });

    describe('Table Configuration', () => {
        test('should have correct table name', () => {
            expect(NotificationLogTable.TableName).toBe('notification-logs');
        });

        test('should have all required GSI indexes', () => {
            const indexes = NotificationLogTable.GlobalSecondaryIndexes || [];
            const indexNames = indexes.map(index => index.IndexName);
            
            expect(indexNames).toContain('BusinessIndex');
            expect(indexNames).toContain('CampaignIndex');
            expect(indexNames).toContain('WhatsAppMessageIndex');
            expect(indexNames).toContain('CustomerSegmentIndex');
            expect(indexNames).toContain('MessageCategoryIndex');
            expect(indexNames).toContain('WhatsAppStatusIndex');
        });

        test('BusinessIndex should have correct key schema', () => {
            const businessIndex = NotificationLogTable.GlobalSecondaryIndexes?.find(
                index => index.IndexName === 'BusinessIndex'
            );
            
            expect(businessIndex).toBeDefined();
            expect(businessIndex?.KeySchema).toEqual([
                { AttributeName: 'businessId', KeyType: 'HASH' },
                { AttributeName: 'timestamp', KeyType: 'RANGE' }
            ]);
        });

        test('CampaignIndex should have correct key schema', () => {
            const campaignIndex = NotificationLogTable.GlobalSecondaryIndexes?.find(
                index => index.IndexName === 'CampaignIndex'
            );
            
            expect(campaignIndex).toBeDefined();
            expect(campaignIndex?.KeySchema).toEqual([
                { AttributeName: 'campaignId', KeyType: 'HASH' },
                { AttributeName: 'timestamp', KeyType: 'RANGE' }
            ]);
        });

        test('WhatsAppMessageIndex should have correct key schema', () => {
            const whatsappIndex = NotificationLogTable.GlobalSecondaryIndexes?.find(
                index => index.IndexName === 'WhatsAppMessageIndex'
            );
            
            expect(whatsappIndex).toBeDefined();
            expect(whatsappIndex?.KeySchema).toEqual([
                { AttributeName: 'whatsappMessageId', KeyType: 'HASH' }
            ]);
        });
    });

    describe('Enhanced Features', () => {
        test('should support flexible error message types', () => {
            const logWithStringError: NotificationLog = {
                notificationId: 'test-1',
                timestamp: Date.now(),
                timestampISO: '2024-01-15T10:30:00.000+05:30',
                businessId: 'business-123',
                mobileNumber: '+919876543210',
                channel: NotificationChannel.WHATSAPP,
                recipient: '+919876543210',
                inputPayload: {},
                providerRequest: {},
                providerResponse: {},
                status: NotificationStatus.FAILED,
                errorMessage: 'Simple error message',
                retryCount: 1,
                lastUpdated: Date.now(),
                lastUpdatedISO: '2024-01-15T10:30:00.000+05:30'
            };

            const logWithObjectError: NotificationLog = {
                notificationId: 'test-2',
                timestamp: Date.now(),
                timestampISO: '2024-01-15T10:30:00.000+05:30',
                businessId: 'business-123',
                mobileNumber: '+919876543210',
                channel: NotificationChannel.WHATSAPP,
                recipient: '+919876543210',
                inputPayload: {},
                providerRequest: {},
                providerResponse: {},
                status: NotificationStatus.FAILED,
                errorMessage: { 
                    code: 'WEBHOOK_ERROR',
                    message: 'Detailed error information',
                    details: { source: 'WhatsApp API' }
                },
                retryCount: 1,
                lastUpdated: Date.now(),
                lastUpdatedISO: '2024-01-15T10:30:00.000+05:30'
            };

            expect(typeof logWithStringError.errorMessage).toBe('string');
            expect(typeof logWithObjectError.errorMessage).toBe('object');
        });

        test('should handle optional fields correctly', () => {
            const minimalLog: NotificationLog = {
                notificationId: 'minimal-1',
                timestamp: Date.now(),
                timestampISO: '2024-01-15T10:30:00.000+05:30',
                businessId: 'business-123',
                mobileNumber: '+919876543210',
                channel: NotificationChannel.WHATSAPP,
                recipient: '+919876543210',
                inputPayload: {},
                providerRequest: {},
                providerResponse: {},
                status: NotificationStatus.PENDING,
                retryCount: 0,
                lastUpdated: Date.now(),
                lastUpdatedISO: '2024-01-15T10:30:00.000+05:30'
            };

            expect(minimalLog.campaignId).toBeUndefined();
            expect(minimalLog.whatsappMessageId).toBeUndefined();
            expect(minimalLog.sentAt).toBeUndefined();
        });
    });
}); 