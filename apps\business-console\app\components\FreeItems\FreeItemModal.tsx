
import React from "react";
import { FreeItemFormData } from "~/types/api/businessConsoleService/FreeItem";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import FreeItemForm from "./freeItemForm";
import { toast } from "~/hooks/use-toast";

interface FreeItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: FreeItemFormData) => void;
  initialData?: FreeItemFormData;
}

const FreeItemModal: React.FC<FreeItemModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
}) => {
  const handleSubmit = (data: FreeItemFormData) => {
    onSubmit(data);
    toast({
      description: initialData?.id ? "Free item offer updated!" : "Free item offer created!"
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {initialData?.id ? "Edit" : "Create"} Free Item Offer
          </DialogTitle>
        </DialogHeader>
        <FreeItemForm initialData={initialData} onSubmit={handleSubmit} />
      </DialogContent>
    </Dialog>
  );
};

export default FreeItemModal;