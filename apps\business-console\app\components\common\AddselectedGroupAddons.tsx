import React, { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import { MyAddonData, MyAddOnGroupAddOn, MyAddonGroupData } from "~/types/api/businessConsoleService/SellerManagement";
import { Form, useFetcher } from "@remix-run/react";
import { SquareX } from "lucide-react";
import { useToast } from "../ui/ToastProvider";



interface AddselectedGroupAddonsProps {
      isOpen: boolean;
      items: MyAddonData[];
      onClose: () => void;
      header: string;
      groupData?: MyAddOnGroupAddOn,
      sellerId?: number,
      groupId?: number
}

const AddselectedGroupAddons: React.FC<AddselectedGroupAddonsProps> = ({
      isOpen,
      items,
      onClose,
      header,
      groupData,
      sellerId,
      groupId
}) => {
      const [selectedId, setSelectedId] = useState<string | number | null>(null);
      const [searchTerm, setSearchTerm] = useState('');
      const [filteredAddon, setFilteredAddon] = useState<MyAddonData[]>(items)
      const [choosenAddon, setChoosenAddon] = useState<boolean>(false);
      const [choossenAddonName, setChoosenAddonName] = useState<string>('');
      const [formData, setFormData] = useState({
            price: groupData?.price.toString() || "0",
            seq: groupData?.seq.toString() || "0",
            active: groupData?.active ?? true,
      });
      const { showToast } = useToast()
      useEffect(() => {
            if (searchTerm.length >= 3 && searchTerm !== "") {
                  setFilteredAddon(items?.filter(addon => addon?.name.toLowerCase().includes(searchTerm.toLowerCase())))
            }
            else {
                  setFilteredAddon(items)
            } ``
      }, [searchTerm, items]);

      useEffect(() => {
            if (!isOpen) {
                  setSelectedId(null);
                  setSearchTerm("");
                  setChoosenAddon(false);
                  setFormData({
                        price: "0",
                        seq: "0",
                        active: false,
                  })
            }

      }, [isOpen]);
      useEffect(() => {
            if (groupData) {
                  setChoosenAddon(true);
                  setSelectedId(groupData.myAddOnId)
                  setChoosenAddonName(groupData.myAddOnName)
                  setFormData(prev => ({
                        ...prev,
                        price: groupData.price.toString(),
                        seq: groupData.seq.toString(),
                        active: groupData.active ?? true,
                  }))
            }

      }, [groupData]);

      const handleSelect = (addon: MyAddonData) => {
            setSelectedId(addon.id);
            setChoosenAddon(true)
            setChoosenAddonName(addon.name)

      }
      const deselectAddon = () => {
            setSelectedId(null)
            setChoosenAddon(false)
      }
      const groupMapfetcher = useFetcher();
      useEffect(() => {
            if (groupMapfetcher.data) {
                  if (groupMapfetcher.data?.sucess) {
                        showToast("sucess to Map GroupData", 'success')
                        onClose()
                        setFormData({
                              price: "0",
                              seq: "0",
                              active: false,
                        })

                  }
                  else if (groupMapfetcher.data?.sucess === false) {
                        showToast("failed to  Map  GroupData", 'success')
                  }

            }

      }, [groupMapfetcher?.data])



      if (!isOpen) return null;
      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl">
                        <DialogTitle className="text-2xl font-bold text-gray-900 mb-4">{header}</DialogTitle>
                        <div className="space-y-6">
                              {choosenAddon === false && selectedId === null && <>
                                    <div>
                                          <input
                                                placeholder="Search by Addon Name"
                                                type="search"
                                                className="w-full p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors"
                                                autoFocus
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                          />
                                    </div>
                                    <div className="mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                          <ul className="space-y-2">
                                                {filteredAddon.length === 0 ? (
                                                      <p className="p-4 text-gray-500 text-center">No add-ons found</p>
                                                ) : (
                                                      filteredAddon.map((item) => (
                                                            <li key={item.id} className="flex items-center gap-3">
                                                                  <input
                                                                        type="checkbox"
                                                                        id={`item-${item.id}`}
                                                                        name="selectedItem"
                                                                        value={item.id}
                                                                        checked={selectedId === item.id}
                                                                        onChange={() => handleSelect(item)}
                                                                        className="h-5 w-5 text-blue-600 focus:ring-blue-500 rounded"
                                                                  />
                                                                  <label
                                                                        htmlFor={`item-${item.id}`}
                                                                        className={`cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id ? 'bg-blue-50 border-blue-200' : 'border-gray-200'} text-gray-800 hover:bg-gray-50 transition-colors`}
                                                                  >
                                                                        {item?.name} <span className="text-gray-500">({item?.diet})</span>
                                                                  </label>
                                                            </li>
                                                      ))
                                                )}
                                          </ul>
                                    </div>
                              </>
                              }

                              {choosenAddon && selectedId && <div className="space-y-4">
                                    {selectedId && (
                                          <div className="flex items-center justify-between bg-blue-50 p-3 rounded-lg">
                                                <p className="font-medium text-gray-800 truncate max-w-[80%]">{choossenAddonName}</p>
                                                <SquareX
                                                      color="red"
                                                      className="cursor-pointer hover:scale-110 transition-transform"
                                                      onClick={() => deselectAddon()}
                                                />
                                          </div>
                                    )}

                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                          <div>
                                                <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                                                      Price
                                                </label>
                                                <input
                                                      type="number"
                                                      id="price"
                                                      name="price"
                                                      value={formData.price}
                                                      onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                                                      min="0"
                                                      step="0.01"
                                                      required
                                                      className="w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
                                                />
                                          </div>
                                          <div>
                                                <label htmlFor="seq" className="block text-sm font-medium text-gray-700 mb-1">
                                                      Sequence
                                                </label>
                                                <input
                                                      type="number"
                                                      id="seq"
                                                      name="seq"
                                                      value={formData.seq}
                                                      onChange={(e) => setFormData({ ...formData, seq: e.target.value })}
                                                      min="0"
                                                      required
                                                      className="w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
                                                />
                                          </div>
                                          <div className=" flex gap-2">
                                                <label htmlFor="active" className="block text-sm font-medium text-gray-700 mb-1">
                                                      Active :
                                                </label>
                                                <input
                                                      type="checkbox"
                                                      id="active"
                                                      name="active"
                                                      checked={formData.active}
                                                      onChange={(e) => setFormData({ ...formData, active: e.target.checked })}
                                                      className="h-5 w-5 text-blue-600 rounded focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                                />
                                          </div>
                                    </div>
                              </div>}
                        </div>

                        <Form method="POST" className="mt-6 flex flex-col sm:flex-row gap-3 justify-end">
                              <input type="hidden" name="addonId" value={selectedId?.toString()} />
                              <input type="hidden" name="addonGroupId" value={groupId?.toString()} />
                              <input type="hidden" name="price" value={formData.price?.toString()} />
                              <input type="hidden" name="sequence" value={formData.seq?.toString()} />
                              <input type="hidden" name="active" value={formData.active?.toString()} />
                              <input type="hidden" name="sellerId" value={sellerId?.toString()} />
                              <input type="hidden" name="addonName" value={choossenAddonName?.toString()} />
                              <input type="hidden" name="actionType" value={"actionAddonforGroup"} />
                              <button
                                    onClick={onClose}
                                    className="w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium"
                              >
                                    Cancel
                              </button>
                              <button
                                    type="submit"
                                    disabled={selectedId === null}
                                    className={`w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${selectedId === null ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
                              >
                                    Confirm
                              </button>
                        </Form>
                  </DialogContent>
            </Dialog>
      );
};

export default AddselectedGroupAddons;
