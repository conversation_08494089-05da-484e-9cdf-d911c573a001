#!/usr/bin/env node

import { DataMigrationUtility } from '../utils/dataMigration.js';

/**
 * Data Migration Script for DynamoDB Optimization
 * 
 * This script adds date partitioning fields to existing records in both
 * NotificationLog and WebhookLog tables for the last 30 days.
 * 
 * Usage:
 * npm run migrate-data
 * 
 * This will:
 * 1. Add datePartition and monthPartition fields to existing records
 * 2. Only process records from the last 30 days to minimize cost
 * 3. Process both NotificationLog and WebhookLog tables
 * 4. Provide detailed progress logging
 */

async function runMigration() {
    console.log('🚀 Starting DynamoDB Data Migration for Optimization');
    console.log('📅 Processing last 30 days of data for both tables');
    console.log('⏰ Started at:', new Date().toISOString());
    console.log('');

    const migrationUtility = new DataMigrationUtility();

    try {
        // Get migration statistics first
        console.log('📊 Getting migration statistics...');
        const stats = await migrationUtility.getMigrationStats();
        
        console.log('📈 Migration Statistics:');
        console.log(`   Notification Logs: ${stats.notificationLogs.total} total, ${stats.notificationLogs.needsUpdate} need update`);
        console.log(`   Webhook Logs: ${stats.webhookLogs.total} total, ${stats.webhookLogs.needsUpdate} need update`);
        console.log('');

        if (stats.notificationLogs.needsUpdate === 0 && stats.webhookLogs.needsUpdate === 0) {
            console.log('✅ No records need migration. All data is already optimized!');
            return;
        }

        // Run migration for both tables
        console.log('🔄 Starting migration process...');
        await migrationUtility.migrateBothTables();

        console.log('');
        console.log('✅ Migration completed successfully!');
        console.log('⏰ Completed at:', new Date().toISOString());
        
        // Get final statistics
        console.log('');
        console.log('📊 Final Statistics:');
        const finalStats = await migrationUtility.getMigrationStats();
        console.log(`   Notification Logs: ${finalStats.notificationLogs.total} total, ${finalStats.notificationLogs.needsUpdate} need update`);
        console.log(`   Webhook Logs: ${finalStats.webhookLogs.total} total, ${finalStats.webhookLogs.needsUpdate} need update`);

        if (finalStats.notificationLogs.needsUpdate === 0 && finalStats.webhookLogs.needsUpdate === 0) {
            console.log('');
            console.log('🎉 All records have been successfully migrated!');
            console.log('💡 Your DynamoDB queries will now be much more efficient.');
        }

    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
}

// Run migration if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runMigration().catch((error) => {
        console.error('❌ Unhandled error during migration:', error);
        process.exit(1);
    });
}

export { runMigration }; 