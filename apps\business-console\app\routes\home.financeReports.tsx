import { useState } from "react"
import { Input } from "~/components/ui/input"


export default function FinanceConfig() {
      const [searchTerm, setSearchTerm] = useState('')

      return (
            <div className="container mx-auto p-6">
                  <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold">mNet Users</h1>
                  </div>

                  <div className="flex justify-between mb-4">
                        <Input
                              placeholder="Search by name or owner"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"
                        />
                  </div>
            </div>
      )
}