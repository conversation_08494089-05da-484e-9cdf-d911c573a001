// src/types/productTypes.ts

export interface ProductItem {
    id: string;
    productId: string;
    productDisplayName: string;
    brand: string;
    brandId: string;
    variationDisplayName: string;
    variationId: string;
    imagesStr: string;
    images: string[];
    quantity: number;
    unitOfMeasure: string;
    weightInGrams: number;
    productNameWithoutBrand: string;
    mrp: number;
    storePrice: number;
    s3ImageUrls: string[];
    packaging: string;
    secondaryPackaging: string;
    finalPackaging: string;
    status?: ProductRowStatus;
    successImages?: number;
    failedImages?: number;
    errorMessages?: string[];
    timestamp?: string;
    mnetMasterItemId?: number;
    mnetSyncStatus?: "success" | "failed";
    mnetSyncMessage?: string;
  }

  export type ProductRowStatus ="success" | "failed" | "partial_success" | null;
  