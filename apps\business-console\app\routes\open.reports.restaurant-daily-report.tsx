import { json, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData  } from "@remix-run/react";
import { metabaseService } from "../utils/metabase";

export async function loader({ request }: LoaderFunctionArgs) {
      const url = new URL(request.url);
      const sellerId = url.searchParams.get("seller_id");

      if (!sellerId) {
            throw new Error("Seller ID is required.");
      }

      const orderDate = url.searchParams.get("order_date") || new Date().toISOString().split('T')[0];

      const embedUrl = metabaseService.generateQuestionUrl(70, {
            seller_id: sellerId,
            order_date: orderDate,
      });

      return json({ embedUrl, orderDate });
}

export default function MetaRestaurantReport() {
      const { embedUrl, orderDate } = useLoaderData<typeof loader>();
   

      return (
            <div className="container mx-auto p-4">
                  <h1 className="text-2xl font-bold mb-2">Daily Report</h1>
                  {/* <p className="text-gray-500 mb-2">Seller ID: {sellerId}</p> */}
                  <p className="text-gray-500 mb-2">Order Date: {orderDate}</p>

                  <div className="w-full">
                        {embedUrl ? (
                              <iframe
                                    id="metabase-iframe"
                                    src={embedUrl}
                                    title="Metabase Dashboard"
                                    className="w-full h-screen border-0"
                              />
                        ) : (
                              <div className="p-6 text-center text-red-500">
                                    Failed to load the dashboard.
                              </div>
                        )}
                  </div>
            </div>
      );
}