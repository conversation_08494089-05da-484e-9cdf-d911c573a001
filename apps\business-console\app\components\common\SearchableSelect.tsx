import { useCallback, useState, useEffect, useRef } from "react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@components/ui/popover";
import { Button } from "@components/ui/button";
import { Badge } from "@components/ui/badge";
import { Check, ChevronsUpDown, X } from "lucide-react";
import { cn } from "~/lib/utils";

export interface SearchableSelectProps<T> {
  items: T[];
  selectedItems: T[];
  onSearch: (search: string) => Promise<void>;
  onToggleItem: (item: T) => void;
  onLoadMore: () => void;
  hasMore: boolean;
  isLoading: boolean;
  getItemId: (item: T) => string | number;
  getItemLabel: (item: T) => string;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  loadMoreText?: string;
  loadingText?: string;
  className?: string;
}

export function SearchableSelect<T>({
  items,
  selectedItems,
  onSearch,
  onToggleItem,
  onLoadMore,
  hasMore,
  isLoading,
  getItemId,
  getItemLabel,
  placeholder = "Select items...",
  searchPlaceholder = "Search items...",
  emptyMessage = "No items found.",
  loadMoreText = "Load More",
  loadingText = "Loading...",
  className,
}: SearchableSelectProps<T>) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const initialLoadRef = useRef(false);

  // Handle open/close
  const handleOpenChange = useCallback((newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen && !initialLoadRef.current) {
      initialLoadRef.current = true;
      onSearch("");
    }
    if (!newOpen) {
      setSearchTerm("");
      initialLoadRef.current = false;
    }
  }, [onSearch]);

  const handleSearch = useCallback((value: string) => {
    setSearchTerm(value);
    
    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for search
    searchTimeoutRef.current = setTimeout(() => {
      onSearch(value);
    }, 300);
  }, [onSearch]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            className="w-full justify-between"
            aria-expanded={open}
          >
            {selectedItems.length > 0
              ? `${selectedItems.length} items selected`
              : placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start" onWheel={(e) => e.stopPropagation()}
        onTouchMove={(e) => e.stopPropagation()}>
          <Command shouldFilter={false}>
            <CommandInput
              placeholder={searchPlaceholder}
              value={searchTerm}
              onValueChange={handleSearch}
            />
            <CommandGroup className="max-h-[300px] overflow-y-scroll">
              {isLoading ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  {loadingText}
                </div>
              ) : items.length > 0 ? (
                items.map((item) => (
                  <CommandItem
                    key={getItemId(item)}
                    value={String(getItemId(item))}
                    onSelect={() => onToggleItem(item)}
                  >
                    <div className="flex items-center gap-2">
                      <Check
                        className={cn(
                          "h-4 w-4",
                          selectedItems.some(
                            (selected) => getItemId(selected) === getItemId(item)
                          )
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      <span>{getItemLabel(item)}</span>
                    </div>
                  </CommandItem>
                ))
              ) : (
                <CommandEmpty>{emptyMessage}</CommandEmpty>
              )}
            </CommandGroup>
            {hasMore && items.length > 0 && !isLoading && (
              <div className="p-2 text-center">
                <Button variant="ghost" size="sm" onClick={onLoadMore}>
                  {loadMoreText}
                </Button>
              </div>
            )}
          </Command>
        </PopoverContent>
      </Popover>
      {selectedItems.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {selectedItems.map((item) => (
            <Badge
              key={getItemId(item)}
              variant="secondary"
              className="flex items-center gap-1"
            >
              {getItemLabel(item)}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleItem(item);
                }}
              />
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
} 