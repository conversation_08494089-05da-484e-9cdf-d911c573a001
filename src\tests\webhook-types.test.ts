/**
 * Webhook Types & Message Classification Tests
 * Tests type-safe WhatsApp webhook implementation based on Meta documentation
 */

import { 
    WhatsAppWebhookPayload,
    WhatsAppMessage,
    isTextMessage,
    isLocationMessage,
    isInteractiveMessage,
    isButtonMessage,
    isImageMessage,
    isDocumentMessage,
    isContactMessage
} from '../types/whatsapp-webhook.types.js';
import { WebhookLogService } from '../services/webhookLog.service.js';
import { 
    mapWhatsAppMessageType, 
    hasValidMessage, 
    hasStatusUpdates, 
    hasErrors, 
    MessageCode, 
    MessageType, 
    WhatsAppMessageStatus 
} from '../types/webhook.types.js';
import { createHmac } from 'crypto';

// Test data: Sample webhook payloads based on Meta documentation
export const testWebhooks = {
    textMessage: {
        object: "whatsapp_business_account",
        entry: [
            {
                id: "WHATSAPP_BUSINESS_ACCOUNT_ID",
                changes: [
                    {
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "***********",
                                phone_number_id: "*********"
                            },
                            contacts: [
                                {
                                    profile: { name: "<PERSON>e" },
                                    wa_id: "***********"
                                }
                            ],
                            messages: [
                                {
                                    from: "***********",
                                    id: "wamid.ID",
                                    timestamp: "**********",
                                    type: "text",
                                    text: { body: "Hello! I'd like to place an order." }
                                }
                            ]
                        },
                        field: "messages"
                    }
                ]
            }
        ]
    } as WhatsAppWebhookPayload,

    greetingMessage: {
        object: "whatsapp_business_account",
        entry: [
            {
                id: "WHATSAPP_BUSINESS_ACCOUNT_ID",
                changes: [
                    {
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "***********",
                                phone_number_id: "*********"
                            },
                            contacts: [
                                {
                                    profile: { name: "Alice Green" },
                                    wa_id: "***********"
                                }
                            ],
                            messages: [
                                {
                                    from: "***********",
                                    id: "wamid.GREETING_ID",
                                    timestamp: "**********",
                                    type: "text",
                                    text: { body: "Hi there!" }
                                }
                            ]
                        },
                        field: "messages"
                    }
                ]
            }
        ]
    } as WhatsAppWebhookPayload,

    orderStatusMessage: {
        object: "whatsapp_business_account",
        entry: [
            {
                id: "WHATSAPP_BUSINESS_ACCOUNT_ID",
                changes: [
                    {
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "***********",
                                phone_number_id: "*********"
                            },
                            contacts: [
                                {
                                    profile: { name: "Sarah Connor" },
                                    wa_id: "***********"
                                }
                            ],
                            messages: [
                                {
                                    from: "***********",
                                    id: "wamid.STATUS_ID",
                                    timestamp: "**********",
                                    type: "text",
                                    text: { body: "What's the status of my order?" }
                                }
                            ]
                        },
                        field: "messages"
                    }
                ]
            }
        ]
    } as WhatsAppWebhookPayload,

    sayHelloButton: {
        object: "whatsapp_business_account",
        entry: [
            {
                id: "WHATSAPP_BUSINESS_ACCOUNT_ID",
                changes: [
                    {
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "***********",
                                phone_number_id: "*********"
                            },
                            contacts: [
                                {
                                    profile: { name: "Charlie Wilson" },
                                    wa_id: "***********"
                                }
                            ],
                            messages: [
                                {
                                    from: "***********",
                                    id: "wamid.SAY_HELLO_ID",
                                    timestamp: "**********",
                                    type: "interactive",
                                    interactive: {
                                        type: "button_reply",
                                        button_reply: {
                                            id: "say_hello",
                                            title: "Hello 👋"
                                        }
                                    }
                                }
                            ]
                        },
                        field: "messages"
                    }
                ]
            }
        ]
    } as WhatsAppWebhookPayload,

    locationMessage: {
        object: "whatsapp_business_account",
        entry: [
            {
                id: "WHATSAPP_BUSINESS_ACCOUNT_ID",
                changes: [
                    {
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "***********",
                                phone_number_id: "*********"
                            },
                            contacts: [
                                {
                                    profile: { name: "Jane Smith" },
                                    wa_id: "***********"
                                }
                            ],
                            messages: [
                                {
                                    from: "***********",
                                    id: "wamid.LOCATION_ID",
                                    timestamp: "**********",
                                    type: "location",
                                    location: {
                                        latitude: 37.7749,
                                        longitude: -122.4194,
                                        name: "San Francisco Office",
                                        address: "123 Market St, San Francisco, CA 94105"
                                    }
                                }
                            ]
                        },
                        field: "messages"
                    }
                ]
            }
        ]
    } as WhatsAppWebhookPayload,

    freeGiftClaimMessage: {
        object: "whatsapp_business_account",
        entry: [
            {
                id: "WHATSAPP_BUSINESS_ACCOUNT_ID",
                changes: [
                    {
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "***********",
                                phone_number_id: "*********"
                            },
                            contacts: [
                                {
                                    profile: { name: "Mike Johnson" },
                                    wa_id: "***********"
                                }
                            ],
                            messages: [
                                {
                                    from: "***********",
                                    id: "wamid.FREE_GIFT_ID",
                                    timestamp: "**********",
                                    type: "text",
                                    text: { body: "claim my free gift!" }
                                }
                            ]
                        },
                        field: "messages"
                    }
                ]
            }
        ]
    } as WhatsAppWebhookPayload,

    imageMessage: {
        object: "whatsapp_business_account",
        entry: [
            {
                id: "WHATSAPP_BUSINESS_ACCOUNT_ID",
                changes: [
                    {
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "***********",
                                phone_number_id: "*********"
                            },
                            contacts: [
                                {
                                    profile: { name: "Jane Smith" },
                                    wa_id: "***********"
                                }
                            ],
                            messages: [
                                {
                                    from: "***********",
                                    id: "wamid.IMAGE_ID",
                                    timestamp: "**********",
                                    type: "image",
                                    image: {
                                        id: "image_id",
                                        mime_type: "image/jpeg",
                                        sha256: "example_hash"
                                    }
                                }
                            ]
                        },
                        field: "messages"
                    }
                ]
            }
        ]
    } as WhatsAppWebhookPayload,

    contactMessage: {
        object: "whatsapp_business_account",
        entry: [
            {
                id: "WHATSAPP_BUSINESS_ACCOUNT_ID",
                changes: [
                    {
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "***********",
                                phone_number_id: "*********"
                            },
                            contacts: [
                                {
                                    profile: { name: "Mike Johnson" },
                                    wa_id: "***********"
                                }
                            ],
                            messages: [
                                {
                                    from: "***********",
                                    id: "wamid.CONTACT_ID",
                                    timestamp: "**********",
                                    type: "contacts",
                                    contacts: [
                                        {
                                            name: {
                                                formatted_name: "Sarah Connor",
                                                first_name: "Sarah",
                                                last_name: "Connor"
                                            },
                                            phones: [
                                                {
                                                    phone: "+****************",
                                                    type: "HOME",
                                                    wa_id: "***********"
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        field: "messages"
                    }
                ]
            }
        ]
    } as unknown as WhatsAppWebhookPayload,

    // 🆕 WhatsApp Status Update Webhooks
    statusUpdateWebhook: {
        object: "whatsapp_business_account",
        entry: [
            {
                id: "WHATSAPP_BUSINESS_ACCOUNT_ID",
                changes: [
                    {
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "***********",
                                phone_number_id: "*********"
                            },
                            statuses: [
                                {
                                    id: "wamid.MESSAGE_ID_1",
                                    status: "delivered",
                                    timestamp: "**********",
                                    recipient_id: "***********"
                                },
                                {
                                    id: "wamid.MESSAGE_ID_2", 
                                    status: "read",
                                    timestamp: "**********",
                                    recipient_id: "***********"
                                }
                            ]
                        },
                        field: "messages"
                    }
                ]
            }
        ]
    } as WhatsAppWebhookPayload,

    // 🆕 WhatsApp Error Notification Webhook
    errorNotificationWebhook: {
        object: "whatsapp_business_account",
        entry: [
            {
                id: "WHATSAPP_BUSINESS_ACCOUNT_ID",
                changes: [
                    {
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "***********",
                                phone_number_id: "*********"
                            },
                            errors: [
                                {
                                    code: 131047,
                                    title: "Re-engagement message",
                                    message: "Re-engagement message was not delivered because 24 hour window has expired"
                                },
                                {
                                    code: 100,
                                    title: "Invalid parameter",
                                    message: "Parameter value is not valid"
                                }
                            ]
                        },
                        field: "messages"
                    }
                ]
            }
        ]
    } as WhatsAppWebhookPayload
};

export async function testTypeGuards() {
    console.log("🔍 Testing Type Guards...");
    
    const message = testWebhooks.textMessage.entry[0]?.changes[0]?.value.messages?.[0];
    if (!message) {
        throw new Error("Test message not found");
    }
    
    // Test text message type guard
    if (!isTextMessage(message)) {
        throw new Error("Text message type guard failed");
    }
    console.log("   ✅ Text message type guard passed");
    
    const locationMessage = testWebhooks.locationMessage.entry[0]?.changes[0]?.value.messages?.[0];
    if (!locationMessage) {
        throw new Error("Location test message not found");
    }
    
    // Test location message type guard
    if (!isLocationMessage(locationMessage)) {
        throw new Error("Location message type guard failed");
    }
    console.log("   ✅ Location message type guard passed");
    
    const buttonMessage = testWebhooks.sayHelloButton.entry[0]?.changes[0]?.value.messages?.[0];
    if (!buttonMessage) {
        throw new Error("Button test message not found");
    }
    
    // Test interactive message type guard
    if (!isInteractiveMessage(buttonMessage)) {
        throw new Error("Interactive message type guard failed");
    }
    console.log("   ✅ Interactive message type guard passed");
    
    console.log("✅ All type guards passed!\n");
}

export async function testMessageTypeMapping() {
    console.log("🗂 Testing Message Type Mapping...");
    
    const textMsg = testWebhooks.textMessage.entry[0]?.changes[0]?.value.messages?.[0];
    const locationMsg = testWebhooks.locationMessage.entry[0]?.changes[0]?.value.messages?.[0];
    const buttonMsg = testWebhooks.sayHelloButton.entry[0]?.changes[0]?.value.messages?.[0];
    
    if (!textMsg || !locationMsg || !buttonMsg) {
        throw new Error("Test messages not found");
    }
    
    console.log(`   ✅ Text message mapped to: ${mapWhatsAppMessageType(textMsg.type)}`);
    console.log(`   ✅ Location message mapped to: ${mapWhatsAppMessageType(locationMsg.type)}`);
    console.log(`   ✅ Interactive message mapped to: ${mapWhatsAppMessageType(buttonMsg.type)}`);
    
    console.log("✅ Message type mapping passed!\n");
}

export async function testWebhookValidation() {
    console.log("🔐 Testing Webhook Validation...");
    
    // Test valid webhook
    if (!hasValidMessage(testWebhooks.textMessage)) {
        throw new Error("Valid webhook failed validation");
    }
    console.log("   ✅ Valid webhook validation passed");
    
    // Test invalid webhook (no messages)
    const invalidWebhook = {
        object: "whatsapp_business_account",
        entry: [
            {
                id: "WHATSAPP_BUSINESS_ACCOUNT_ID",
                changes: [
                    {
                        value: {
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "***********",
                                phone_number_id: "*********"
                            },
                            contacts: []
                        },
                        field: "messages"
                    }
                ]
            }
        ]
    } as WhatsAppWebhookPayload;
    
    if (hasValidMessage(invalidWebhook)) {
        throw new Error("Invalid webhook passed validation");
    }
    console.log("   ✅ Invalid webhook validation passed");
    
    console.log("✅ Webhook validation tests passed!\n");
}

export async function testMessageCodeClassification() {
    console.log("🏷 Testing Message Code Classification...");
    
    const webhookService = new WebhookLogService();
    
    const mockReq = {
        get: (header: string) => header === 'User-Agent' ? 'Test-Agent' : undefined,
        headers: { 'x-forwarded-for': '*************' },
        connection: { remoteAddress: '*************' },
        socket: { remoteAddress: '*************' }
    } as any;
    
    // Test greeting classification
    const greetingLog = await webhookService.logIncomingWebhook(mockReq, testWebhooks.greetingMessage);
    if (greetingLog.messageCode !== MessageCode.GREETING) {
        throw new Error(`Expected GREETING, got ${greetingLog.messageCode}`);
    }
    console.log("   ✅ Greeting message classified correctly");
    
    // Test order status classification
    const statusLog = await webhookService.logIncomingWebhook(mockReq, testWebhooks.orderStatusMessage);
    if (statusLog.messageCode !== MessageCode.ORDER_STATUS) {
        throw new Error(`Expected ORDER_STATUS, got ${statusLog.messageCode}`);
    }
    console.log("   ✅ Order status message classified correctly");
    
    // Test say_hello button classification
    const helloButtonLog = await webhookService.logIncomingWebhook(mockReq, testWebhooks.sayHelloButton);
    if (helloButtonLog.messageCode !== MessageCode.GREETING) {
        throw new Error(`Expected GREETING for say_hello button, got ${helloButtonLog.messageCode}`);
    }
    console.log("   ✅ Say hello button classified correctly");
    
    // Test free gift claim classification
    const freeGiftLog = await webhookService.logIncomingWebhook(mockReq, testWebhooks.freeGiftClaimMessage);
    if (freeGiftLog.messageCode !== MessageCode.FREE_GIFT_CLAIM) {
        throw new Error(`Expected FREE_GIFT_CLAIM, got ${freeGiftLog.messageCode}`);
    }
    console.log("   ✅ Free gift claim message classified correctly");
    
    // Test regular message (no classification)
    const regularLog = await webhookService.logIncomingWebhook(mockReq, testWebhooks.textMessage);
    if (regularLog.messageCode !== undefined) {
        throw new Error(`Expected undefined for regular message, got ${regularLog.messageCode}`);
    }
    console.log("   ✅ Regular message correctly has no classification");
    
    console.log("✅ Message code classification tests passed!\n");
}

export async function testSignatureValidation() {
    console.log("🔏 Testing HMAC Signature Validation...");
    
    // Note: Signature validation would be implemented at the controller level
    // For now, we'll test the signature generation process
    const payload = JSON.stringify(testWebhooks.textMessage);
    const secret = 'test_secret_key_123';
    const timestamp = Math.floor(Date.now() / 1000).toString();
    
    // Create valid signature
    const signaturePayload = timestamp + payload;
    const expectedSignature = createHmac('sha256', secret)
        .update(signaturePayload, 'utf8')
        .digest('hex');
    
    if (!expectedSignature || expectedSignature.length !== 64) {
        throw new Error("Signature generation failed");
    }
    console.log("   ✅ HMAC signature generation working");
    
    // Test signature format
    const formattedSignature = `sha256=${expectedSignature}`;
    if (!formattedSignature.startsWith('sha256=')) {
        throw new Error("Signature format invalid");
    }
    console.log("   ✅ Signature format validation passed");
    
    console.log("✅ Signature validation tests passed!\n");
}

export async function testStatusUpdateLogging() {
    console.log("🔄 Testing WhatsApp Status Update Logging...");
    
    const webhookService = new WebhookLogService();
    const mockReq = {
        get: (header: string) => header === 'User-Agent' ? 'Test-Agent' : undefined,
        headers: { 'x-forwarded-for': '*************' },
        connection: { remoteAddress: '*************' },
        socket: { remoteAddress: '*************' }
    } as any;
    
    // Test status update webhook detection
    if (!hasStatusUpdates(testWebhooks.statusUpdateWebhook)) {
        throw new Error("Status update webhook not detected");
    }
    console.log("   ✅ Status update webhook detection passed");
    
    // Test status update logging
    const statusLog = await webhookService.logIncomingWebhook(
        mockReq, 
        testWebhooks.statusUpdateWebhook, 
        '**********'
    );
    
    if (statusLog.messageType !== MessageType.STATUS_UPDATE) {
        throw new Error(`Expected STATUS_UPDATE, got ${statusLog.messageType}`);
    }
    
    if (!statusLog.whatsappStatus) {
        throw new Error("WhatsApp status not captured");
    }
    
    console.log(`   ✅ Status update logged: ${statusLog.whatsappStatus}`);
    console.log(`   ✅ Message ID captured: ${statusLog.messageId}`);
    
    console.log("✅ Status update logging tests passed!\n");
}

export async function testErrorNotificationLogging() {
    console.log("🚨 Testing WhatsApp Error Notification Logging...");
    
    const webhookService = new WebhookLogService();
    const mockReq = {
        get: (header: string) => header === 'User-Agent' ? 'Test-Agent' : undefined,
        headers: { 'x-forwarded-for': '*************' },
        connection: { remoteAddress: '*************' },
        socket: { remoteAddress: '*************' }
    } as any;
    
    // Test error notification webhook detection
    if (!hasErrors(testWebhooks.errorNotificationWebhook)) {
        throw new Error("Error notification webhook not detected");
    }
    console.log("   ✅ Error notification webhook detection passed");
    
    // Test error notification logging
    const errorLog = await webhookService.logIncomingWebhook(
        mockReq, 
        testWebhooks.errorNotificationWebhook, 
        '**********'
    );
    
    if (errorLog.messageType !== MessageType.ERROR_NOTIFICATION) {
        throw new Error(`Expected ERROR_NOTIFICATION, got ${errorLog.messageType}`);
    }
    
    if (errorLog.whatsappStatus !== WhatsAppMessageStatus.FAILED) {
        throw new Error(`Expected FAILED status, got ${errorLog.whatsappStatus}`);
    }
    
    if (!errorLog.whatsappErrors || errorLog.whatsappErrors.length === 0) {
        throw new Error("WhatsApp errors not captured");
    }
    
    console.log(`   ✅ Error notification logged with ${errorLog.whatsappErrors.length} errors`);
    console.log(`   ✅ Error status: ${errorLog.whatsappStatus}`);
    
    console.log("✅ Error notification logging tests passed!\n");
}

export async function testDeliveryAnalytics() {
    console.log("📈 Testing Delivery Analytics...");
    
    const webhookService = new WebhookLogService();
    
    try {
        // Test delivery statistics
        const deliveryStats = await webhookService.getDeliveryStats('**********', 24);
        
        console.log(`   ✅ Delivery stats retrieved - Total: ${deliveryStats.totalMessages}`);
        console.log(`   ✅ Delivery rate: ${deliveryStats.deliveryRate.toFixed(2)}%`);
        console.log(`   ✅ Read rate: ${deliveryStats.readRate.toFixed(2)}%`);
        
        // Test system health with WhatsApp metrics
        const health = await webhookService.getSystemHealth();
        
        console.log(`   ✅ System health status: ${health.status}`);
        console.log(`   ✅ WhatsApp delivery rate: ${health.metrics.whatsappDeliveryRate.toFixed(2)}%`);
        console.log(`   ✅ WhatsApp error rate: ${health.metrics.whatsappErrorRate.toFixed(2)}%`);
        
        console.log("✅ Delivery analytics tests passed!\n");
        
    } catch (error) {
        console.error("   ❌ Delivery analytics failed:", error);
        throw error;
    }
}

export async function runWebhookTypesTests() {
    console.log("🚀 Starting Webhook Types & Classification Tests...\n");
    
    const tests = [
        { name: "Type Guards", fn: testTypeGuards },
        { name: "Message Type Mapping", fn: testMessageTypeMapping },
        { name: "Webhook Validation", fn: testWebhookValidation },
        { name: "Message Code Classification", fn: testMessageCodeClassification },
        { name: "Status Update Logging", fn: testStatusUpdateLogging },
        { name: "Error Notification Logging", fn: testErrorNotificationLogging },
        { name: "Delivery Analytics", fn: testDeliveryAnalytics },
        { name: "Signature Validation", fn: testSignatureValidation }
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        try {
            await test.fn();
            passed++;
        } catch (error) {
            console.error(`❌ ${test.name} failed:`, error);
            failed++;
        }
    }
    
    console.log("\n📊 Webhook Types Test Summary:");
    console.log(`   ✅ Passed: ${passed}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   📈 Success Rate: ${Math.round((passed / tests.length) * 100)}%`);
    
    if (failed > 0) {
        throw new Error(`${failed} tests failed`);
    }
    
    console.log("\n🎉 All webhook types tests passed!");
}

// Export test data for use in other test files
export { testWebhooks as textMessageWebhook }; 