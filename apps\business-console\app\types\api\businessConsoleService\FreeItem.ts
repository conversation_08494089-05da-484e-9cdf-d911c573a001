export type DiscountType = "percentage" | "flat" | "freeitem" | "buyonegetone";

export interface FreeItem {
  id: string;
  discountPercentage?: number;
  discountFlat?: number;
  discountUpto?: number;
  discountMinOrderQty?: number;
  validFrom: Date;
  validTo: Date;
  discountDisabled: boolean;
  discountType: DiscountType;
  freeItemId?: string;
  freeItemQty?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface FreeItemFormData {
  id?: string;
  discountPercentage?: number | null;
  discountFlat?: number | null;
  discountUpto?: number | null;
  discountMinOrderQty?: number | null;
  validFrom: Date;
  validTo: Date;
  discountDisabled: boolean;
  discountType: DiscountType;
  freeItemId?: string | null;
  freeItemQty?: number | null;
}