import { z } from "zod";

// API Response schema
export const apiResponseSchema = z.object({
  data: z.array(z.any()),
  totalCount: z.number(),
  currentPage: z.number(),
  pageSize: z.number(),
});

// Search params schema
export const searchParamsSchema = z.object({
  search: z.string().optional(),
  page: z.coerce.number().default(1),
  pageSize: z.coerce.number().default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

export const ctasSchema = z.object({
  id: z.number().optional(),
  label: z.string(),
  value: z.enum(["pay_dues", "order_history", "help", "place_update_order"]),
  type: z.enum(["url", "quick_reply", "copy_code", "call", "location"]),
});

// Template schema
export const templateSchema = z.object({
  id: z.number(),
  templateId: z.string(),
  name: z.string(),
  header: z.string().optional(),
  content: z.string(),
  preview: z.string().optional(),
  type: z.enum(["SMS", "WhatsApp", "hybrid"]),
  ctas: z.array(ctasSchema).optional(),
  phone: z.string(),
  variables: z.array(z.string()),
  lastUsed: z.string().optional(),
});

// Customer group schema
export const customerGroupSchema = z.object({
  type: z.enum(["OneOrder", "FreqOrders", "All", "Custom"]),
  name: z.string(),
  customerCount: z.number(),
});

// Send message schema
export const sendMessageSchema = z.object({
  templateId: z.number(),
  variables: z.record(z.string()).optional(),
  selectedGroup: z.enum(["OneOrder", "FreqOrders", "All", "Custom"]),
  selectedItems: z.array(z.number()).optional(),
  selectedCustomers: z.array(z.number()).optional(),
});

// Infer types from schemas
export type ApiResponse<T> = {
  data: T;
  totalCount: number;
  currentPage: number;
  pageSize: number;
};

export type Template = z.infer<typeof templateSchema>;
export type CustomerGroup = z.infer<typeof customerGroupSchema>;
export type SearchParams = z.infer<typeof searchParamsSchema>;
export type SendMessagePayload = z.infer<typeof sendMessageSchema>;
export type WaTemplateCtas = z.infer<typeof ctasSchema>;
