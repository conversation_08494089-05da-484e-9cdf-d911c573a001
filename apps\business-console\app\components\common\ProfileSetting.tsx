import { X } from "lucide-react";
import { Input } from "../ui/input";



// interface InActiveBannersProps {
//       bannerDetails: {
//             id: number;
//             image: string;
//             sequence: number;
//             status: string;
//       };
// }

export default function ProfileSetting() {

      return (
            <div className="flex flex-col shadow-md bg-white p-4 w-full  border-r-8 border-transparent rounded-xl gap-2 items-start h-full my-4">
                  <h1>Profile Settings</h1>

                  <div className="relative w-full mt-3 border border-gray-300 rounded-md p-2  ">
                        <label className="absolute left-3 top-0 bg-white px-1 text-sm font-medium text-gray-700 transform -translate-y-1/2">Name*</label>
                        <Input
                              defaultValue={"VenkatTest"}
                              type="text"
                              className="w-full pr-10 border-none focus:ring-0 outline-none"

                        />
                        <button className="absolute top-1/2 right-3 transform -translate-y-1/2 text-gray-500">
                              <X size={16} />
                        </button>
                  </div>
                  <Input
                        placeholder="Phone Number"
                        type="text"
                        className="w-full pr-10 border border-gray-300 rounded-md p-2"
                        required

                  />
                  <Input
                        placeholder="Email (optional)"
                        type="email"
                        className="w-full pr-10 border border-gray-300 rounded-md p-2"

                  />

            </div>

      )
}