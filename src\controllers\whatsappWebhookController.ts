/**
 * WhatsApp Webhook Controller
 * 
 * Handles incoming WhatsApp webhooks with comprehensive logging, type safety,
 * and IST timezone support for better readability.
 * 
 * 📚 **Complete Documentation**: {@link ../docs/WHATSAPP_WEBHOOK_COMPREHENSIVE_GUIDE.md}
 * 
 * Features:
 * - Type-safe webhook processing following Meta documentation
 * - Automatic logging with both Unix and IST timestamps
 * - HMAC signature validation for security
 * - Comprehensive error handling and status tracking
 * - 🆕 Notification log sync for message status updates
 * 
 * @see {@link ../docs/WHATSAPP_WEBHOOK_COMPREHENSIVE_GUIDE.md} - Complete Guide
 */

import dotenv from "dotenv";
dotenv.config();

import { Request, Response } from "express";
import axios from "axios";
import WaWebSessionService from "@/services/waWebSession.service.js";
import MnetApiGatewayService from "@/services/mnetApiGateway.service.js";
import { WebhookLogService } from "@/services/webhookLog.service.js";
import { WebhookNotificationSyncService } from "@/services/webhookNotificationSync.service.js";
import { GetUserDataResponse } from "@/types/mnet/response.types.js";
import { 
  WhatsAppWebhookPayload, 
  WhatsAppMessage, 
  WhatsAppContact,
  WebhookVerificationQuery,
  WhatsAppStatus
} from "@/types/whatsapp-webhook.types.js";
import { hasValidMessage, hasStatusUpdates, extractStatusUpdates } from "@/types/webhook.types.js";
import { TimeUtil } from "@/utils/time.util.js";
import { getFirebaseAdmin } from "@/utils/firebase.utils.js";
import { WhatsAppConnectionData } from "@/types/whatsapp.js";

const waWebSessionService = new WaWebSessionService();
const webhookLogService = new WebhookLogService();
const webhookNotificationSyncService = new WebhookNotificationSyncService();

// Get seller data from Firebase using bMobile
async function getSellerDataByBMobile(bMobile: string): Promise<WhatsAppConnectionData | null> {
    try {
        console.log(`🔍 Searching for seller data with bMobile: ${bMobile}`);
        
        const db = getFirebaseAdmin();
        console.log(`📱 Business console Firebase initialized successfully`);
        
        const querySnapshot = await db.collection("facebook-connects")
            .where("mNetConnectedPhoneNumber", "==", bMobile)
            .limit(1)
            .get();

        console.log(`📊 Query executed, documents found: ${querySnapshot.size}`);

        if (querySnapshot.empty) {
            console.warn(`⚠️  No WhatsApp connection found for seller ${bMobile}`);
            return null;
        }

        const doc = querySnapshot.docs[0];
        const connectionData = doc.data() as WhatsAppConnectionData;
        
        console.log(`✅ Successfully retrieved seller data for ${bMobile}, document ID: ${doc.id}`);
        return connectionData;
    } catch (error) {
        console.error(`❌ Error fetching seller data for ${bMobile}:`, error);
        return null;
    }
}

/* ------------------------------------------------------------------
   1. Environment variables
------------------------------------------------------------------ */
const WHATSAPP_TOKEN = process.env.WHATSAPP_TOKEN || "";
const WEBHOOK_VERIFY_TOKEN = process.env.WEBHOOK_VERIFY_TOKEN || "";
const CORE_API_AUTH = process.env.CORE_API_AUTH || "";

/* ------------------------------------------------------------------
   2. Type definitions (Interfaces) - Now using official Meta types
------------------------------------------------------------------ */
// Legacy interfaces for backward compatibility with existing code
interface WhatsAppMessage_Legacy {
  type: "button" | "interactive" | "location" | "text";
  from: string;
  id: string;
  text?: { body: string };
  interactive?: {
    type: string;
    button_reply?: { id: string; title: string };
    list_reply?: { id: string; title: string };
  };
  button: {
    payload: string;
    text: string;
  }
  location?: {
    address?: string;
    latitude?: string;
    longitude?: string;
    name?: string;
  };
}

interface WhatsAppContact_Legacy {
  profile?: {
    name?: string;
  };
  wa_id?: string;
}

interface WebhookEntry_Legacy {
  changes?: {
    value?: {
      messages?: WhatsAppMessage_Legacy[];
      contacts?: WhatsAppContact_Legacy[];
      metadata?: {
        display_phone_number?: string;
        phone_number_id?: string;
      };
    };
  }[];
}

interface WebhookBody_Legacy {
  entry?: WebhookEntry_Legacy[];
}

const mnetApiGatewayService = new MnetApiGatewayService();
const API_BASE_URL = process.env.API_BASE_URL || "";

const createWebLink = async ({ baseUrl, path, cMobile, bMobile, ctwaClid }: { baseUrl: string; path: string; cMobile: string; bMobile: string; ctwaClid?: string }) => {
  const token = await waWebSessionService.generateTotpToken(cMobile, bMobile);
  
  // Get seller data using bMobile to extract ctwaToken
  let sellerDataFromFirebase: WhatsAppConnectionData | null = null;
  if (ctwaClid) {
    sellerDataFromFirebase = await getSellerDataByBMobile(bMobile);
  }
  
  // TODO: hide query params from the URL so user can't understand the URL structure
  let url = `https://${baseUrl}${path}`
  url += `?mobileNumber=${cMobile}`
  if (ctwaClid) {
    url += `&ctwa_clid=${ctwaClid}`
  }
  if (token.ok) {
    url += `&token=${token.data?.token}`
  }

  // Add ctwaToken from seller data if available
  if (ctwaClid && sellerDataFromFirebase?.ctwaToken) {
    url += `&ctwa_token=${sellerDataFromFirebase.ctwaToken}&waba_id=${sellerDataFromFirebase.wabaId}`
  }
  
  return url;
}

/* ------------------------------------------------------------------
   3. Exports: getReq & postReq
   - These are the Express handlers for WhatsApp's webhook
------------------------------------------------------------------ */

/**
 * GET: Webhook Verification
 * Based on Meta documentation for webhook verification
 */
export const getReq = (req: Request, res: Response): void => {
  const query = req.query as WebhookVerificationQuery;
  const mode = query['hub.mode'];
  const token = query['hub.verify_token'];
  const challenge = query['hub.challenge'];

  if (mode === "subscribe" && token === WEBHOOK_VERIFY_TOKEN) {
    // Webhook verified
    res.status(200).send(challenge);
    console.log("Webhook verified successfully!");
  } else {
    // Verification failed
    res.sendStatus(403);
  }
};

/**
 * POST: Webhook Handler
 * Now with type-safe payload handling, comprehensive logging, and notification sync
 */
export const postReq = async (req: Request, res: Response): Promise<void> => {
  let webhookLog: any = null;
  
  try {
    console.log("Incoming Webhook JSON:", JSON.stringify(req.body));
  } catch (e) {
    console.error("Error logging incoming webhook:", e);
  }

  try {
    // Type-safe payload parsing
    const body = req.body as WhatsAppWebhookPayload;

    // Validate webhook structure
    if (!body.object || body.object !== 'whatsapp_business_account') {
      console.error("Invalid webhook object type:", body.object);
      res.sendStatus(400);
      return;
    }

    if (!body.entry || body.entry.length === 0) {
      console.error("No entries found in webhook payload");
      res.sendStatus(400);
      return;
    }

    // Log incoming webhook with automatic IST timestamp conversion
    const webhookLog = await webhookLogService.logIncomingWebhook(req, body);
    
    // IST timestamps provide human-readable format for operations team
    // Example: webhookLog.receivedAtISO = "2023-12-30T15:30:45.123+05:30"
    console.log(`📥 Webhook received at ${webhookLog.receivedAtISO} (ID: ${webhookLog.webhookId})`);

    // 🆕 Check for WhatsApp status updates first (message delivery status)
    if (hasStatusUpdates(body)) {
      await webhookLogService.markAsProcessing(webhookLog.webhookId, webhookLog.timestamp);
      
      try {
        const statusUpdates = extractStatusUpdates(body);
        console.log(`🔄 Processing ${statusUpdates.length} status updates for webhook ${webhookLog.webhookId}`);
        
        // Sync status updates to notification logs
        const syncResult = await webhookNotificationSyncService.processStatusUpdates(
          webhookLog.webhookId, 
          statusUpdates
        );
        
        console.log(`✅ Status sync completed: ${syncResult.successfulUpdates}/${syncResult.totalUpdatesProcessed} successful`);
        
        await webhookLogService.markAsProcessed(webhookLog.webhookId, webhookLog.timestamp);
        res.sendStatus(200);
        return;
      } catch (syncError) {
        console.error('❌ Error syncing status updates:', syncError);
        await webhookLogService.markAsFailed(
          webhookLog.webhookId, 
          webhookLog.timestamp, 
          `Status sync failed: ${syncError instanceof Error ? syncError.message : String(syncError)}`
        );
        // Continue processing as normal message if sync fails
      }
    }

    // Check if the webhook contains a valid message to process
    if (!hasValidMessage(body)) {
        await webhookLogService.markAsIgnored(webhookLog.webhookId, webhookLog.timestamp, 'No valid message found');
        console.log(`ℹ️  Webhook ignored - no valid message content`);
        res.sendStatus(200);
        return;
    }

    // Mark as processing with timestamp tracking
    await webhookLogService.markAsProcessing(webhookLog.webhookId, webhookLog.timestamp);

    // Process the webhook message using existing legacy conversion
    const webhookValue = body.entry[0]?.changes?.[0]?.value;
    const message = webhookValue?.messages?.[0];
    const contact = webhookValue?.contacts?.[0];
    
    if (message) {
        // Convert to legacy format for existing message handler
        const legacyMessage = convertToLegacyMessage(message);
        const legacyContact = convertToLegacyContact(contact);
        const legacyBody = convertToLegacyBody(body);
        
        // Handle the incoming WhatsApp message using existing logic
        await handleIncomingMessage(message, contact, body);
    }

    // Mark as successfully processed with IST timestamp
    await webhookLogService.markAsProcessed(webhookLog.webhookId, webhookLog.timestamp);
    console.log(`✅ Webhook processed successfully at ${TimeUtil.formatForLogging()}`);

    res.sendStatus(200);
  } catch (error) {
    console.error('❌ Error processing webhook:', error);
    
    // Log error with detailed context and IST timestamp
    if (webhookLog) {
        await webhookLogService.markAsFailed(
            webhookLog.webhookId, 
            webhookLog.timestamp, 
            error instanceof Error ? error.message : String(error),
            { stack: error instanceof Error ? error.stack : undefined }
        );
    }

    // Always return 200 to prevent WhatsApp from retrying
    res.sendStatus(200);
  }
};

/* ------------------------------------------------------------------
   4. Type Conversion Helpers
   - Convert new type-safe interfaces to legacy format for existing handlers
------------------------------------------------------------------ */

function convertToLegacyMessage(message: WhatsAppMessage): WhatsAppMessage_Legacy {
  const legacy: any = {
    type: message.type as any,
    from: message.from,
    id: message.id
  };

  // Convert message content based on type
  if (message.text) {
    legacy.text = { body: message.text.body };
  }

  if (message.interactive) {
    legacy.interactive = {
      type: message.interactive.type,
      button_reply: message.interactive.button_reply,
      list_reply: message.interactive.list_reply
    };
  }

  if (message.button) {
    legacy.button = {
      payload: message.button.payload,
      text: message.button.text
    };
  }

  if (message.location) {
    legacy.location = {
      address: message.location.address,
      latitude: message.location.latitude.toString(),
      longitude: message.location.longitude.toString(),
      name: message.location.name
    };
  }

  return legacy;
}

function convertToLegacyContact(contact?: WhatsAppContact): WhatsAppContact_Legacy | undefined {
  if (!contact) return undefined;

  return {
    profile: {
      name: contact.profile.name
    },
    wa_id: contact.wa_id
  };
}

function convertToLegacyBody(body: WhatsAppWebhookPayload): WebhookBody_Legacy {
  const webhookValue = body.entry[0]?.changes?.[0]?.value;
  
  return {
    entry: [{
      changes: [{
        value: {
          messages: webhookValue?.messages?.map(convertToLegacyMessage),
          contacts: webhookValue?.contacts?.map(convertToLegacyContact).filter(Boolean) as WhatsAppContact_Legacy[],
          metadata: {
            display_phone_number: webhookValue?.metadata?.display_phone_number,
            phone_number_id: webhookValue?.metadata?.phone_number_id
          }
        }
      }]
    }]
  };
}

/* ------------------------------------------------------------------
   5. Core Logic: handleIncomingMessage & sub-handlers
   - Existing logic remains unchanged for backward compatibility
------------------------------------------------------------------ */

async function handleIncomingMessage(
  message: WhatsAppMessage,
  contact: WhatsAppContact | undefined,
  body: WhatsAppWebhookPayload
) {
  // 1. Extract relevant details
  const displayPhoneNumber =
    body.entry?.[0]?.changes?.[0]?.value?.metadata?.display_phone_number;
  const phoneNumberId =
    body.entry?.[0]?.changes?.[0]?.value?.metadata?.phone_number_id;
  const from = message.from;

  const customerName = contact?.profile?.name;

  // 2. Derive businessNumber & customerNumber
  const businessNumber = displayPhoneNumber
    ? displayPhoneNumber.slice(-10)
    : null;
  const customerNumber = from ? from.slice(-10) : null;

  const ctwaClid = (message as WhatsAppMessage)?.referral?.ctwa_clid;

  if (!businessNumber || !customerNumber || !phoneNumberId) {
    console.error(
      "Missing businessNumber, customerNumber, or phoneNumberId in the webhook payload"
    );
    return;
  }

  // 3. Handle by message type or content
  if (message.type === "location") {
    // The user just sent a location
    await handleLocationMessage(businessNumber, customerNumber, message, phoneNumberId, from, contact);
  } else if (isGreeting(message) || isInteractiveSayHello(message)) {
    // The user typed a greeting ("hi", "hello", etc.) or clicked "say_hello"
    await handleGreeting(businessNumber, customerNumber, phoneNumberId, from, customerName, ctwaClid);
  } 
  else if (isOrderStatus(message)) {
    // The user typed a greeting ("status", "order status", etc.)"
    await handleOrderStatus(businessNumber, customerNumber, phoneNumberId, from);
  }
  else if (isFreeGiftClaim(message)) {
    // The user wants to claim their free gift
    await handleFreeGiftClaim(businessNumber, customerNumber, phoneNumberId, from, customerName);
  }
  else if (
    message.type === "interactive" &&
    message.interactive?.button_reply || message.type === "button" && message?.button?.payload
  ) {
    // The user pressed an interactive button that you previously sent
    await handleButtonReply(
      businessNumber,
      customerNumber,
      phoneNumberId,
      from,
      message
    );
  } else {
    // Optionally handle other message types or do a fallback
    console.warn("No handler found for message: ", JSON.stringify(message, null, 2));
    // await sendReplyButtons(
    //   from,
    //   phoneNumberId,
    //   "🤔 I didn't quite understand that. Please type 'Hi' or 'Hello'!",
    //   [
    //     {
    //       type: "reply",
    //       reply: {
    //         id: "say_hello",
    //         title: "Hello 👋",
    //       },
    //     },
    //   ]
    // );
  }
}

/* --------------- Sub-Handlers --------------- */

/** handleLocationMessage
 * Called when the user sends their location
 */
async function handleLocationMessage(
  businessNumber: string,
  customerNumber: string,
  message: WhatsAppMessage,
  phoneNumberId: string,
  from: string,
  contact?: WhatsAppContact
) {
  const { latitude, longitude } = message.location || {};
  if (!latitude || !longitude) {
    const messageText = "We're sorry! 😔 Something went wrong during with your location capturing. Please try again. Thank you for your patience! 🙏"
    await sendLocationButton(from, phoneNumberId, messageText);
    console.log("Location missing latitude/longitude");
    return;
  }

  // 1. Register or update the user's location on your external API
  const apiResponse = await registerUserLocation(businessNumber, customerNumber, {
    fullName: contact?.profile?.name,
    mobileNumber: customerNumber,
    address: "",
    latitude: latitude.toString(),
    longitude: longitude.toString(), // fix: was previously 'message.location?.latitude'
  }, from, phoneNumberId);

  // 2. Destructure the fields if needed
  const {
    businessName = "our service",
    customerName = "",
    baseUrl = "",
    registered = false,
    pendingDuesAmount = 0,
    bookingOpen = true,
    withinServiceArea = true,
  } = apiResponse || {};

  // 3. Optional: you might want to proceed with further logic
  // e.g., ask them for something else or show them a menu
  // For now, let's keep it minimal
  if (registered) {
    await sendReplyButtons(
      from,
      phoneNumberId,
      "You're all set! 🎉 , we're excited to serve you. 🛒 Start exploring now and place your first order today. We can't wait to deliver fresh to your doorstep! 🚚",
      [
        {
          type: "reply",
          reply: {
            id: "place_update_order",
            title: "🛒 Place Order",
          },
        }
      ]);
  } else {
    const messageText = "We're sorry! 😔 Something went wrong during your registration due to an unexpected error. Please try again, and we'll ensure a seamless experience. Thank you for your patience! 🙏"
    sendLocationButton(from, phoneNumberId, messageText);
  }

  console.log(
    `Location updated for ${customerName}. withinServiceArea=${withinServiceArea}, bookingOpen=${bookingOpen}`
  );
}

/** handleGreeting
 * Called when user typed a greeting or clicked "say_hello" button
 */
async function handleGreeting(
  businessNumber: string,
  customerNumber: string,
  phoneNumberId: string,
  from: string,
  waIncomingCustomerName?: string,
  ctwaClid?: string
) {
  // 1. Get user data from external API
  const apiResponse = await getUserData(businessNumber, customerNumber, waIncomingCustomerName);

  if(apiResponse === null) {
    await sendErrorMessage(from, phoneNumberId);
    return;
  }

  const {
    businessName = "our service",
    customerName = "",
    baseUrl = "",
    registered = true,
    pendingDuesAmount = 0,
    bookingOpen = true,
    withinServiceArea = true,
  } = apiResponse || {};

  // 2. If user is not registered => prompt location
  if (!registered) {
    let text = `Hi there! 👋 Welcome to ${businessName}. Please share your location to check service availability.`;

   await sendLocationButton(from, phoneNumberId, text);
   return;
  }

  // 3. If registered => show them the main menu
  // const text = `Hi ${customerName}! 👋 Welcome back to ${businessName}. What would you like to do today?`;
  // const buttons = [
  //   {
  //     type: "reply",
  //     reply: { id: "place_update_order", title: "🛒 Place Order" },
  //   },
  //   {
  //     type: "reply",
  //     reply: {
  //       id:
  //         pendingDuesAmount > 0 && Math.ceil(pendingDuesAmount) < 100000
  //           ? "pay_dues"
  //           : "order_history",
  //       title:
  //         pendingDuesAmount > 0 && Math.ceil(pendingDuesAmount) < 100000
  //           ? `💰 Pay dues ₹${Math.ceil(pendingDuesAmount)}`
  //           : "📜 Order History",
  //     },
  //   },
  //   {
  //     type: "reply",
  //     reply: { id: "help", title: "❓ Help" },
  //   },
  // ];
    // await sendReplyButtons(from, phoneNumberId, text, buttons);

  let text =`Hi ${customerName}! 👋 Welcome back to ${businessName}. 🛒 Ready to shop? Click below to place or update your order.\n For order status, please type "status" in chat:`;
  const ret10Text = `Hi ${customerName}! 👋 Welcome back to ${businessName}. \n\nWe've stocked up on the freshest veggies and daily essentials just for you! 🥦🥕🛒\n\n🛍️ Ready to shop? Click below to place or update your order:\n\n👉 *Place Order* \n\n📦 To check your order status, just type *status* in the chat.`
   const ret11Text = `Hi ${customerName}! 👋 Welcome to ${businessName}.\n\nCraving something delicious today? 🍽️🍕 Your favorites are just a tap away!\n\n🍴 Tap below to place or update your order:\n\n👉 *Place Order*\n\n📦 To check your order status, type *status* anytime.`
   
   if(apiResponse.ondcDomain === "RET10") {
    text = ret10Text;
  }
  else if(apiResponse.ondcDomain === "RET11") {
    text = ret11Text;
  }

  
  // Provide link to place/update order
  const url = await createWebLink({ baseUrl: baseUrl, path: '/home', cMobile: customerNumber, bMobile: businessNumber, ctwaClid: ctwaClid });
  await sendCTAUrlButtonMessage(
    from,
    phoneNumberId,
    text,
    url,
    "Place Order"
  );  
}

async function handleOrderStatus(
  businessNumber: string,
  customerNumber: string,
  phoneNumberId: string,
  from: string,
) {
  try {
    const apiResponse = await getUserData(businessNumber, customerNumber);
    const {
      baseUrl = "",
    } = apiResponse || {};
    const orderUrl = await createWebLink({ baseUrl: baseUrl, path: '/home/<USER>', cMobile: customerNumber, bMobile: businessNumber });
    await sendCTAUrlButtonMessage(
      from,
      phoneNumberId,
      "📜 View your past orders:",
      orderUrl,
      "Order History"
    ); 
  } catch (error) {
    console.error("Error in handleOrderStatus:", error);
    await sendErrorMessage(from, phoneNumberId);
  }
}

/** handleButtonReply
 * Called when user presses one of your interactive reply buttons
 */
async function handleButtonReply(
  businessNumber: string,
  customerNumber: string,
  phoneNumberId: string,
  from: string,
  message: WhatsAppMessage
) {

  let selectedId = message.interactive?.button_reply?.id;

  if(message.type === "button" && message?.button?.payload) {
    selectedId = message.button?.payload;
  }

  if(!selectedId) return;

  // 1. Get user data from external API
  const apiResponse = await getUserData(businessNumber, customerNumber);

  if (apiResponse === null) {
    await sendErrorMessage(from, phoneNumberId);
    return;
  }

  const {
    businessName = "our service",
    customerName = "",
    baseUrl = "",
    registered = false,
    pendingDuesAmount = 0,
    bookingOpen = true,
    withinServiceArea = true,
    hasActiveOrder = false,
  } = apiResponse || {};

  // 2. Switch on the user's selection
  switch (selectedId) {
    case "place_order":
    case "place_update_order":
      if (withinServiceArea && bookingOpen) {
        // Provide link to place/update order
        const url = await createWebLink({ baseUrl: baseUrl, path: '/home', cMobile: customerNumber, bMobile: businessNumber });
        await sendCTAUrlButtonMessage(
          from,
          phoneNumberId,
          "🛒 Ready to shop? Click below to place or update your order:",
          url,
          "Place Order"
        );
      } else if (!bookingOpen) {
        // Store is currently not accepting orders
        const messageText = `We're currently not accepting orders. 🛑 Please check back later or try other options.`;
        const button = [
          {
            type: "reply",
            reply: {
              id:
                pendingDuesAmount > 0 && Math.ceil(pendingDuesAmount) < 100000
                  ? "pay_dues"
                  : "order_history",
              title:
                pendingDuesAmount > 0 && Math.ceil(pendingDuesAmount) < 100000
                  ? `💰 Pay dues ₹${Math.ceil(pendingDuesAmount)}`
                  : "📜 Order History",
            },
          },
          {
            type: "reply",
            reply: {
              id: "help",
              title: "❓ Help",
            },
          },
        ];
        await sendReplyButtons(from, phoneNumberId, messageText, button);
      } else if (!withinServiceArea) {
        // Outside delivery area
        const messageText = `Your address is outside our delivery area. 🚫 Please update your address or try again later.`;
        const button = [
          {
            type: "reply",
            reply: {
              id: "update_address",
              title: "📍 Change Address",
            },
          },
          {
            type: "reply",
            reply: {
              id: "say_hello",
              title: "🔙 Back to Menu",
            },
          },
        ];
        await sendReplyButtons(from, phoneNumberId, messageText, button);
      }
      break;

    case "pay_dues":
      // Attempt to create a deposit link
      try {
        const depositApiUrl = `${API_BASE_URL}/wab/${businessNumber}/c/${customerNumber}/deposit/amount/${pendingDuesAmount}`;
        const depositRes = await axios.post(depositApiUrl, {}, {
          headers: {
            accept: "application/json",
            Authorization: `Bearer ${CORE_API_AUTH}`,
          },
        });
        const paymentData = depositRes.data || {};
        if (paymentData.queryString) {
          await sendCTAUrlButtonMessage(
            from,
            phoneNumberId,
            `💳 It's time to settle up! Click below to pay ₹${pendingDuesAmount} via UPI:`,
            paymentData.queryString,
            `Pay ₹${pendingDuesAmount}`
          );
        } else {
          throw new Error("Invalid payment data");
        }
      } catch (paymentError: any) {
        console.error("Error processing payment:", paymentError.message);
        await sendErrorMessage(from, phoneNumberId);
      }
      break;

    case "order_history":
      const orderUrl = await createWebLink({ baseUrl: baseUrl, path: '/home/<USER>', cMobile: customerNumber, bMobile: businessNumber });
      await sendCTAUrlButtonMessage(
        from,
        phoneNumberId,
        "📜 View your past orders:",
        orderUrl,
        "Order History"
      );
      break;

    case "update_address":
      const addressUrl = await createWebLink({ baseUrl: baseUrl, path: '/changeaddress', cMobile: customerNumber, bMobile: businessNumber });
      await sendCTAUrlButtonMessage(
        from,
        phoneNumberId,
        "📍 Need to update your delivery address? Click below:",
        addressUrl,
        "Update Address"
      );
      break;

    case "help":
      const text = `Please let us know what you need help with, ${customerName}:`;
      const buttons = [
        {
          type: "reply",
          reply: { id: "update_address", title: "📍 Change Address" },
        },
        {
          type: "reply",
          reply: {
            id:
              hasActiveOrder
                ? "order_tracking"
                : "request_callback",
            title:
              hasActiveOrder
                ? `👀 Track Order`
                : "📞 Request Callback",
          },
        },
        {
          type: "reply",
          reply: { id: "support_ticket", title: "❓ Raise Ticket" },
        },
      ];
      await sendReplyButtons(from, phoneNumberId, text, buttons);
      break;

    case "request_callback":

      if (!apiResponse) {
        await sendErrorMessage(from, phoneNumberId);
        return;
      }

      const ticketResponse = await mnetApiGatewayService.createSupportTicket({
        userId: apiResponse.userId,
        requestedCallBack: true,
        ticketType: "others",
        status: "OPEN",
      }, businessNumber, customerNumber)

      if (ticketResponse.ok) {
        await sendReplyButtons(
          from,
          phoneNumberId,
          "We have received your request for a callback. We'll get back to you shortly.",
          [
            {
              type: "reply",
              reply: {
                id: "say_hello",
                title: "Hello 👋",
              },
            },
          ]
        );
      }
      else {
        await sendErrorMessage(from, phoneNumberId);
      }
      break;

    case "support_ticket":
      const helpUrl = await createWebLink({ baseUrl, path: "/help", cMobile: customerNumber, bMobile: businessNumber });
      await sendCTAUrlButtonMessage(
        from,
        phoneNumberId,
        "💡 Need assistance? Click below to reach our support:",
        helpUrl,
        "Get Help"
      );
      break;

    default:
      // Fallback
      await sendReplyButtons(
        from,
        phoneNumberId,
        "🤔 Unrecognized option. Please pick from below:",
        [
          {
            type: "reply",
            reply: {
              id: "place_update_order",
              title: "🛒 Place Order",
            },
          },
          {
            type: "reply",
            reply: {
              id: "order_history",
              title: "📜 Order History",
            },
          },
          {
            type: "reply",
            reply: {
              id: "help",
              title: "❓ Help",
            },
          },
        ]
      );
      break;
  }
}

/** handleFreeGiftClaim
 * Called when user wants to claim their free gift
 */
async function handleFreeGiftClaim(
  businessNumber: string,
  customerNumber: string,
  phoneNumberId: string,
  from: string,
  waIncomingCustomerName?: string
) {
  try {
    // 1. Get user data first
    const apiResponse = await mnetApiGatewayService.getUserInfo(businessNumber, customerNumber, waIncomingCustomerName);
    if (!apiResponse.ok || !apiResponse.data) {
      await sendErrorMessage(from, phoneNumberId);
      return;
    }

    // 2. Get free item details
    const freeItemResponse = await mnetApiGatewayService.getFreeItem(businessNumber, customerNumber);
    if (!freeItemResponse.ok || !freeItemResponse.data) {
      await sendErrorMessage(from, phoneNumberId);
      return;
    }

    const freeItem = freeItemResponse.data;

    if (freeItem.status === "DEFAULT") {
      const token = await waWebSessionService.generateTotpToken(customerNumber, businessNumber);
      // TODO: hide query params from the URL so user can't understand the URL structure
      let deepLink = freeItem.deepLink;
      deepLink += `&mobileNumber=${customerNumber}`
      if (token.ok) {
        deepLink += `&token=${token.data?.token}`
      }
        // Success case - show the free gift message with deeplink
      const expiresAtDate = new Date(freeItem.expiresAt).toLocaleDateString();
      const successMessage = `🎉 Congrats! You've unlocked a *FREE  ${freeItem.sellerItemName}!* 🎁\n\n _Show this voucher to your captain to redeem_ 👨‍🍳\n\n • *Valid until:* ${expiresAtDate} \n • *Your code:* ${freeItem.code} \n\n 👇 Tap *Show Voucher* to open your voucher.\n📲 Type *Hi* to see our full menu!`;
      await sendCTAUrlButtonMessage(
        from,
        phoneNumberId,
        successMessage,
        deepLink,
        "View Your Voucher"
      );
    } else if (freeItem.status === "REDEEMED") {
      // Already claimed case
      const failureMessage = "❌🎁 You've already claimed your free gift! 😊 Enjoy your meal 🍔🍟🎉";
      await sendReplyButtons(
        from,
        phoneNumberId,
        failureMessage,
        [
          {
            type: "reply",
            reply: {
              id: "say_hello",
              title: "🔙 Back to Menu",
            },
          },
        ]
      );
    } else {
      // Unknown status case
      await sendErrorMessage(from, phoneNumberId);
    }
  } catch (error) {
    console.error("Error handling free gift claim:", error);
    await sendErrorMessage(from, phoneNumberId);
  }
}

/* --------------- Helper Checks --------------- */

/** isGreeting - check if user typed any greeting words anywhere in the message */
function isGreeting(message: WhatsAppMessage): boolean {
  if (message.type !== "text" || !message.text?.body) return false;
  const txt = message.text.body.trim().toLowerCase();
  
  // Common greeting patterns with word boundaries to match greetings anywhere in text
  const greetingPatterns = [
    /\b(hi+|hello+|hey+)\b/,           // Basic greetings (hi, hello, hey with optional repeated letters)
    /\b(good\s+(morning|afternoon|evening|night))\b/, // Time-based greetings
    /\b(namaste|namaskar)\b/,          // Indian greetings
    /\b(howdy|greetings)\b/,           // Other casual greetings
    /\b(what'?s\s+up|whats\s+up|wassup)\b/, // What's up variations
    /\b(good\s+day)\b/,                // Good day
    /\b(how\s+(are\s+you|r\s+u))\b/,   // How are you variations
    /\b(hola|bonjour|ciao)\b/          // International greetings
  ];
  
  // Check if any greeting pattern matches
  return greetingPatterns.some(pattern => pattern.test(txt));
}

/** isOrderStatus - check if user typed "status", "track", "where", "update", "history", or "check" */
function isOrderStatus(message: WhatsAppMessage): boolean {
  if (message.type !== "text" || !message.text?.body) return false;
  const txt = message.text.body.trim().toLowerCase();
  return /^(status|track|history|where|check)$|\b(order|delivery).*?(status|track|where|update|history|check)\b|\b(track|where).*?(order|delivery)\b/i.test(txt);
}

/** isFreeGiftClaim - check if user typed something similar to "Claim my free gift!" */
function isFreeGiftClaim(message: WhatsAppMessage): boolean {
  if (message.type !== "text" || !message.text?.body) return false;
  const txt = message.text.body.trim().toLowerCase();
  
  // Match variations of "claim", "free", and "gift" in the message
  const claimPattern = /\b(claim|get|redeem|take)\b/i;
  const freePattern = /\bfree\b/i;
  const giftPattern = /\b(gift|reward|bonus|offer)\b/i;
  
  // Check if the message contains all three concepts or the exact phrase
  const hasAllConcepts = claimPattern.test(txt) || freePattern.test(txt) || giftPattern.test(txt);
  const isExactMatch = txt === "claim my free gift!" || txt === "claim my free gift";
  
  return hasAllConcepts || isExactMatch;
}

/** isInteractiveSayHello - check if user tapped a "say_hello" button */
function isInteractiveSayHello(message: WhatsAppMessage): boolean {
  return (
    message.type === "interactive" &&
    message.interactive?.button_reply?.id === "say_hello"
  );
}

/* ------------------------------------------------------------------
   5. External API calls (mnet-api.farmersmandi.in)
------------------------------------------------------------------ */
async function getUserData(businessNumber: string, customerNumber: string, waIncomingCustomerName?: string): Promise<GetUserDataResponse | null> {
  let apiUrl = `${API_BASE_URL}/wab/${businessNumber}/c/${customerNumber}`;
  if(waIncomingCustomerName) {
    apiUrl += `?cname=${waIncomingCustomerName}`;
  }
  try {
    console.log("API REQUEST", {
      apiUrl,
      businessNumber,
      customerNumber,
      waIncomingCustomerName,
    })
    const response = await axios.get<GetUserDataResponse>(apiUrl, {
      headers: {
        accept: "application/json",
        Authorization: `Bearer ${CORE_API_AUTH}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error fetching user data:", error.response?.data || error.message);
    // Return fallback data
    return null;
  }
}

async function registerUserLocation(
  businessNumber: string,
  customerNumber: string,
  locationData: {
    fullName?: string;
    mobileNumber?: string;
    address?: string;
    latitude?: string;
    longitude?: string;
  },
  from: string,
  phoneNumberId: string
) {
  const apiUrl = `${API_BASE_URL}/wab/${businessNumber}/c/${customerNumber}/buyer`;
  try {
    const response = await axios.post(apiUrl, locationData, {
      headers: {
        accept: "application/json",
        Authorization: `Bearer ${CORE_API_AUTH}`,
      },
    });
    return response.data;
  } catch (error: any) {
    // const messageText = "We're sorry! 😔 Something went wrong during your registration due to an unexpected error. Please try again, and we'll ensure a seamless experience. Thank you for your patience! 🙏"
    // sendLocationButton(from, phoneNumberId, messageText);
    console.error("Error updating user location:", error.response?.data || error.message);
    return { registered: false };
  }
}

/* ------------------------------------------------------------------
   6. WhatsApp Send-Message Helpers
------------------------------------------------------------------ */

/** sendCTAUrlButtonMessage - sends a CTA button with a URL */
async function sendCTAUrlButtonMessage(
  to: string,
  phoneNumberId: string,
  text: string,
  url: string,
  buttonText: string
) {
  const messageData = {
    messaging_product: "whatsapp",
    recipient_type: "individual",
    to: formatPhone(to),
    type: "interactive",
    interactive: {
      type: "cta_url",
      body: { text },
      action: {
        name: "cta_url",
        parameters: {
          display_text: buttonText,
          url,
        },
      },
    },
  };
  await postToWhatsApp(phoneNumberId, messageData);
}

/** sendLocationButton - requests location from the user */
async function sendLocationButton(to: string, phoneNumberId: string, text: string) {
  const messageData = {
    messaging_product: "whatsapp",
    recipient_type: "individual",
    to: formatPhone(to),
    type: "interactive",
    interactive: {
      type: "location_request_message",
      body: { text },
      action: {
        name: "send_location",
      },
    },
  };
  await postToWhatsApp(phoneNumberId, messageData);
}

/** sendReplyButtons - up to 3 quick reply buttons */
async function sendReplyButtons(
  to: string,
  phoneNumberId: string,
  text: string,
  buttons: { type: string; reply: { id: string; title: string } }[]
) {
  const finalButtons = buttons.slice(0, 3);
  const messageData = {
    messaging_product: "whatsapp",
    recipient_type: "individual",
    to: formatPhone(to),
    type: "interactive",
    interactive: {
      type: "button",
      body: { text },
      action: {
        buttons: finalButtons.map(b => ({
          type: "reply",
          reply: {
            id: b.reply.id,
            title: b.reply.title,
          },
        })),
      },
    },
  };
  await postToWhatsApp(phoneNumberId, messageData);
}

/** sendErrorMessage - plain text error fallback */
async function sendErrorMessage(to: string, phoneNumberId: string) {
  const messageData = {
    messaging_product: "whatsapp",
    recipient_type: "individual",
    to: formatPhone(to),
    type: "text",
    text: {
      body: "Sorry, we are unable to process your request at the moment. Please try again later.",
    },
  };
  try {
    await postToWhatsApp(phoneNumberId, messageData);
  } catch (e) {
    console.error("Error sending error message:", e);
  }
}

/* ------------------------------------------------------------------
   7. Low-level function to send data to WhatsApp Cloud API
------------------------------------------------------------------ */
async function postToWhatsApp(phoneNumberId: string, messageData: any) {
  try {
    await axios.post(
      `https://graph.facebook.com/v17.0/${phoneNumberId}/messages`,
      messageData,
      {
        headers: {
          Authorization: `Bearer ${WHATSAPP_TOKEN}`,
          "Content-Type": "application/json",
        },
      }
    );
  } catch (error: any) {
    console.error("Error posting to WhatsApp:", error.response?.data || error.message);
    throw error;
  }
}

/* ------------------------------------------------------------------
   8. Utility: Ensure phone numbers always have a "+" prefix
------------------------------------------------------------------ */
function formatPhone(phone: string) {
  return phone.startsWith("+") ? phone : `+${phone}`;
}
