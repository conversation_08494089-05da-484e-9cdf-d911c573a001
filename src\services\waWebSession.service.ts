import { IServiceResponse } from '@/interfaces/service.interface.js';
import logger from '@utils/express-logger.js'

import { v4 as uuidv4 } from 'uuid';
import { WaWebSessionRepository } from '@repository/dynamoDB/waWebSessions.repository.js';
import { WaWebSession } from '@entity/dynamoDB/waWebSession.entity.js';
import MnetApiGatewayService from './mnetApiGateway.service.js';
import { generateTotpToken, verifyTotpToken } from '@utils/totp.js';



class WaWebSessionService {
    private sessionRepository: WaWebSessionRepository;
    private mnetApiGatewayService: MnetApiGatewayService;
    private TOTP_SECRET = process.env.AUTH_TOKEN;
    private TOTP_TIMEOUT = 15 * 60 * 1000;


    constructor() {
        this.sessionRepository = new WaWebSessionRepository("sessions-uat");
        this.mnetApiGatewayService = new MnetApiGatewayService();
    }

    async createSession(bMobile: string, cMobile: string): Promise<IServiceResponse<{ token: string }>> {
        try {
            const token = uuidv4();
            const expiryTime = Date.now() + 15 * 60 * 1000; // Expires in 15 minutes
            const session: WaWebSession = {
                bMobile,
                cMobile,
                token,
                expiryTime,
                expiryTimeISO: new Date(expiryTime).toISOString(),
                createdAt: Date.now(),
                createdAtISO: new Date().toISOString(),
            };

            await this.sessionRepository.createSession(session);
            return {
                ok: true,
                data: { token },
            };
        } catch (err) {
            const error = err as Error;
            logger.error(error);
            logger.error(`Error Creating Session:' ${JSON.stringify(error.message)}`);
            return { ok: false, err: `Error Creating Session:' ${JSON.stringify(error.message)}` };
        }
    }

    async validateSession(token: string, newAccessToken: boolean): Promise<IServiceResponse<{ token?: string }>> {
        try {
            console.log(token)
            const session = await this.sessionRepository.getSession(token);

            if (!session) {
                return {
                    ok: false,
                    err: 'Invalid session token.',
                }
            }

            if (Date.now() > session.expiryTime) {
                return {
                    ok: false,
                    err: 'Session has expired.',
                }
            }

            if (!newAccessToken) {
                // Case a: Token is valid, newAccessToken is false
                // await this.sessionRepository.deleteSession(token);
                return {
                    ok: true,
                    data: {},
                }
            } else {
                // Case b: Token is valid, newAccessToken is true
                const accessToken = await this.mnetApiGatewayService.getAccessToken(session.bMobile, session.cMobile);

                if (accessToken.ok) {
                    // await this.sessionRepository.deleteSession(token);
                    return { ok: true, data: { token: accessToken.data } };
                }
                return { ok: false, err: 'Failed to get access token.' };
            }
        } catch (err) {
            const error = err as Error;
            logger.error(error);
            logger.error(`Error Vaildating token:' ${JSON.stringify(error.message)}`);
            return { ok: false, err: error.message };
        }
    }

    // TODO: handle unique token per session
    // TODO: handle totp timeout
    // TODO: handle totp secret
    async generateTotpToken(bMobile: string, cMobile: string): Promise<IServiceResponse<{ token: string }>> {
        try {
            const totpTimeout = this.TOTP_TIMEOUT;
            const createdAt = Date.now();
            const expiryTime = createdAt + totpTimeout;
            // Generate TOTP token with 15-minute step
            const { token } = generateTotpToken({ 
                customSecret: this.TOTP_SECRET,
                step: this.TOTP_TIMEOUT  // 15 minutes in seconds
            });
            const session: WaWebSession = {
                bMobile,
                cMobile,
                token,
                expiryTime,
                expiryTimeISO: new Date(expiryTime).toISOString(),
                createdAt,
                createdAtISO: new Date().toISOString(),
            };

            await this.sessionRepository.createSession(session);
            return {
                ok: true,
                data: { token },
            };
        } catch (err) {
            const error = err as Error;
            logger.error(error);
            logger.error(`Error Creating Session:' ${JSON.stringify(error.message)}`);
            return { ok: false, err: `Error Creating Session:' ${JSON.stringify(error.message)}` };
        }
    }

    // TODO: handle unique token per session
    async verifyTotpToken(token: string): Promise<IServiceResponse<{ valid: boolean }>> {
        try {
            const session = await this.sessionRepository.getSession(token);
            if (!session) {
                return { ok: false, err: 'Invalid session token.' };
            }

            if (Date.now() > session.expiryTime) {
                return { ok: false, err: 'Session has expired.' };
            }

            // Verify using 15-minute step
            const valid = verifyTotpToken(token, this.TOTP_SECRET!, this.TOTP_TIMEOUT);
            return { ok: true, data: { valid } };
        } catch (err) {
            const error = err as Error;
            logger.error(error);
            logger.error(`Error Verifying TOTP Token: ${JSON.stringify(error.message)}`);
            return { ok: false, err: `Error Verifying TOTP Token: ${JSON.stringify(error.message)}` };
        }
    }
}

export default WaWebSessionService;
