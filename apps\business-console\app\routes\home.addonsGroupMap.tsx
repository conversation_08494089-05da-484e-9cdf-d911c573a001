import type { LoaderFunction } from "@remix-run/node";
import { Form, useActionData, useFetcher, useLoaderData, useNavigate } from "@remix-run/react";
import type { MyAddonData, MyAddOnGroupAddOn, MyAddonGroupData } from "~/types/api/businessConsoleService/SellerManagement";
import { createAddonMap, deleteAddonMap, getAddons, getAddonsGroupsMap } from "~/services/businessConsoleService";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { ResponsiveTable } from "~/components/ui/responsiveTable";
import { Button } from "~/components/ui/button";
import { Pencil, Trash } from "lucide-react";
import AddselectedGroupAddons from "~/components/common/AddselectedGroupAddons";
import React, { useEffect, useState } from "react";
interface LoaderData {
  selectedGruoupData: MyAddOnGroupAddOn[],
  groupName: string,
  sellerId: number,
  groupId: number
}
interface ActionData {
  selectedAddonsData: MyAddonData[],
  sucessMessage: string,
  ErrorMessage: string,
  sucess?: boolean,
  error?: string

}
export const loader: LoaderFunction = withAuth(async ({ request }) => {
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get("page") || "0");
  const pageSize = parseInt(url.searchParams.get("pageSize") || "50");
  const matchBy = url.searchParams.get("matchBy") || "";
  const sellerId = Number(url.searchParams.get("sellerId"));
  const groupId = Number(url.searchParams.get("groupId"));
  const groupName = url.searchParams.get("groupName");
  try {
    const addonsGroupsResponse = await getAddonsGroupsMap(sellerId, groupId, page, pageSize, matchBy, request);
    const selectedGruoupData = addonsGroupsResponse.data;
    return withResponse({ selectedGruoupData, groupName, sellerId, groupId }, addonsGroupsResponse.headers);
  } catch (error) {
    console.error("Error loading addons groups:", error);
    throw new Response('Failed to load addons', { status: 500 });
  }
});

export const action = withAuth(async ({ request }) => {
  const formData = await request.formData();
  const sellerId = Number(formData.get("sellerId"))
  const pageSize = Number(formData.get("pageSize") || "50");
  const page = Number(formData.get("page") || "0");
  const matchBy = formData.get("matchBy") as string
  const actionType = formData.get("actionType") as string

  if (actionType === "getAddons") {
    try {
      const addonsList = await getAddons(sellerId, page, pageSize, matchBy, request);
      const selectedAddonsData = addonsList.data;
      return withResponse({ selectedAddonsData }, addonsList.headers);
    } catch (error) {
      console.error("Error loading addons groups:", error);
      throw new Response('Failed to load addons', { status: 500 });
    }
  }
  else if (
    actionType === "actionAddonforGroup"
  ) {

    const addonGroupId = Number(formData.get("addonGroupId"));
    const price = Number(formData.get("price"));
    const active = (formData.get("active")) as unknown as boolean;
    const sequence = Number(formData.get("sequence"));
    const addonId = Number(formData.get("addonId"));
    const addonName = formData.get("addonName") as string

    const payload: MyAddOnGroupAddOn = {
      myAddOnGroupId: addonGroupId,
      myAddOnId: addonId,
      myAddOnName: addonName,
      price: price,
      seq: sequence,
      active: active,
      groupName: "",

    }
    try {
      const addonsList = await createAddonMap(sellerId, payload, request);
      return withResponse({ sucess: addonsList.statusCode === 200 }, addonsList.headers);
    } catch (error) {
      console.error("Error loading addons groups:", error);
      throw new Response('Failed to load addons', { status: 500 });
    }
  }
  else if (
    actionType == "addonsMapdelete"
  ) {
    const addonmapId = Number(formData.get("addonmapId"));

    try {
      const addonsList = await deleteAddonMap(sellerId, addonmapId, request);
      const selectedAddonsData = addonsList.data;
      return withResponse({ selectedAddonsData }, addonsList.headers);
    } catch (error) {
      console.error("Error loading addons groups:", error);
      throw new Response('Failed to load addons', { status: 500 });
    }
  }
})
const AddonsGroupMap: React.FC = () => {
  const { selectedGruoupData, groupName, sellerId, groupId } = useLoaderData<LoaderData>();
  const navigate = useNavigate();
  const addonMapfetcher = useFetcher<ActionData>()

  const [isAddselectedGroupAddonsOpen, setIsAddselectedGroupAddonsOpen] = useState(false);
  const [selectedGdata, setSelectedGdata] = useState<MyAddOnGroupAddOn>();
  const actionData = useActionData<ActionData>();
  const [isEditopen, setIsEditOpen] = useState(false)

  const [selectedAddonsData, setSelectedAddonsData] = useState<MyAddonData[]>()
  const selectedGroupHeader = [
    "Id",
    "myAddOnId",
    "myAddOnName",
    "price",
    "seq",
    "active",
    "",
    ""
  ];
  useEffect(() => {
    if (addonMapfetcher.state === "idle") {

      if (actionData?.selectedAddonsData) {
        setSelectedAddonsData(actionData.selectedAddonsData)
        setIsAddselectedGroupAddonsOpen(true)
        setIsEditOpen(false)

      }
      else {
        setSelectedAddonsData([])

        setIsAddselectedGroupAddonsOpen(false)
        setIsEditOpen(false)

      }
    }

  }, [actionData])

  const handleSelectedGroupData = (row: MyAddOnGroupAddOn) => {
    setSelectedGdata(row);
    setIsEditOpen(true);
    setIsAddselectedGroupAddonsOpen(true)
  }
  const handleDelete = (addonsmapData: MyAddOnGroupAddOn) => {
    const formData = new FormData();
    formData.append("actionType", "addonsMapdelete");
    formData.append("addonmapId", addonsmapData.id.toString())
    formData.append("sellerId", sellerId.toString())
    addonMapfetcher.submit(formData, { method: 'post' })
  }
  return (
    <div className="h-full">
      <h1 className=" mb-4 font-bold cursor-pointer" onClick={() => navigate(-1)}> <span className="text-2xl">MyAddonsGroup / </span> <span className="text-xl">{groupName} </span> </h1>
      <div className="flex flex-wrap gap-4 border-2 ">
      </div>

      <ResponsiveTable
        headers={selectedGroupHeader}
        data={selectedGruoupData}
        renderRow={(row) => (
          <tr key={row.id} className="border-b">
            <td className="py-2 px-3 text-center whitespace-normal break-words ">{row.id}</td>
            <td className="py-2 px-3 text-center whitespace-normal break-words ">{row?.myAddOnId}</td>
            <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.myAddOnName}</td>
            <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.price}</td>
            <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.seq}</td>
            <td className="py-2 px-3 text-center whitespace-normal break-words">
              {row?.active ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <svg
                    className="w-4 h-4 mr-1 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Active
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <svg
                    className="w-4 h-4 mr-1 text-red-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Inactive
                </span>
              )}
            </td>
            <td className="py-2 px-3 text-center cursor-pointer">
              <Button
                variant="ghost"
                size="sm"
                className="text-red-500 hover:text-red-900"
                onClick={() => {
                  if (confirm("Are you sure you want to delete this Addon Group Map?")) {
                    handleDelete(row)
                  }
                }}
                style={{ alignSelf: "flex-end" }}
              >
                <Trash size={20} />
              </Button>
            </td>
            <td className="py-2 px-3 text-center cursor-pointer">
              <Pencil color='blue' size={20} onClick={() => handleSelectedGroupData(row)} />
            </td>
          </tr>
        )}
      />
      <Form method="post" >
        <input name="sellerId" value={sellerId} hidden />
        <input name="matchBy" value={""} hidden />
        <input name="actionType" value={"getAddons"} hidden />

        <Button
          className="fixed bottom-5 right-5 rounded-full cursor-pointer"
          type="submit"
        >
          + Create Addon from Group
        </Button>
      </Form>
      <AddselectedGroupAddons
        isOpen={isAddselectedGroupAddonsOpen}
        items={selectedAddonsData || []}
        onClose={() => setIsAddselectedGroupAddonsOpen(false)}
        header={isEditopen ? `Edit Addon for${groupName?.slice(0, 15)}` : `Create Addon for ${groupName?.slice(0, 15)} `} groupData={selectedGdata}
        sellerId={sellerId}
        groupId={groupId}
      />
    </div>
  );
};
export default AddonsGroupMap;
