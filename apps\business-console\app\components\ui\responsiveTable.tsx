import { ReactNode } from "react";

interface TableProps<T> {
      headers: string[]; // Header labels
      data: T[]; // Data for table rows
      renderRow: (item: T) => ReactNode; // Function to render each row
      footerTotals?: (string | number)[]; // Optional footer totals
      emptyMessage?: string; // Custom message when data is empty
      maxHeight?: string; // Optional max height for table container
}
export const ResponsiveTable = <T,>({
      headers,
      data,
      renderRow,
      footerTotals,
      emptyMessage = "No results found.",
      maxHeight = "400px"
}: TableProps<T>) => {
      return (
            <div className="rounded-md border overflow-x-auto">
                  <div
                        className="overflow-y-auto"
                        style={{ maxHeight: maxHeight }}
                  >


                        <table className="w-full min-w-[600px] border-collapse">
                              {/* Table Header */}
                              <thead className="sticky top-0 z-10">
                                    <tr className="bg-gradient-to-r from-blue-50 to-indigo-50">
                                          {headers.map((header, index) => (
                                                <th
                                                      key={index}
                                                      className="py-2 px-7 text-center font-semibold whitespace-normal break-words"

                                                >
                                                      {header}
                                                </th>
                                          ))}
                                    </tr>
                              </thead>

                              {/* Table Body */}
                              <tbody className=" divide-gray-200">
                                    {Array.isArray(data) && data.length > 0
                                          ? data.map((item, index) => (
                                                // Use a debugging step if needed to check what `item` looks like
                                                renderRow(item)

                                          )
                                          ) : (
                                                <tr>
                                                      <td
                                                            colSpan={headers.length}
                                                            className="py-2 px-3 text-center whitespace-normal break-words"
                                                      >
                                                            {emptyMessage}
                                                      </td>
                                                </tr>
                                          )}
                              </tbody>

                              {/* Table Footer */}
                              {footerTotals && (
                                    <tfoot>
                                          <tr className="bg-gray-50 font-medium">
                                                {footerTotals.map((total, index) => (
                                                      <td
                                                            key={index}
                                                            className={`py-2 px-3 ${index === 0 ? "text-left" : "text-right"
                                                                  }`}
                                                      >
                                                            {total}
                                                      </td>
                                                ))}
                                          </tr>
                                    </tfoot>
                              )}
                        </table>
                  </div>
            </div>
      );
};
