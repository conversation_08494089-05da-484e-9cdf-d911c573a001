import { useLoaderD<PERSON>, useSubmit, useSearchParams } from "@remix-run/react";
import { LoaderFunction, LoaderFunctionArgs, json } from "@remix-run/node";
import jwt from "jsonwebtoken";
import { useEffect, useState } from "react";
import { withAuth } from "~/utils/auth-utils";

const METABASE_SECRET_KEY = process.env.METABASE_SECRET_KEY || "";
const METABASE_SITE_URL = process.env.METABASE_SITE_URL || "http://43.205.118.52:4001";

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
      const sellerId = user.sellerId;

      // Get the date range from query parameters
      const url = new URL(request.url);
      const dateRange = url.searchParams.get("dateRange") || "7"; // Default to weekly (7)

      if (!METABASE_SECRET_KEY) {
            throw new Error("Metabase secret key is not configured.");
      }

      const payload = {
            resource: { dashboard: 7 },
            params: {
                  seller_id: sellerId,
                  date: dateRange // last 7 days
            },
            exp: Math.round(Date.now() / 1000) + (10 * 60) // expires in 10 minutes
      };

      const token = jwt.sign(payload, METABASE_SECRET_KEY);
      const embedUrl = `${METABASE_SITE_URL}/embed/dashboard/${token}#bordered=false&titled=false`;

      return json({ embedUrl, dateRange });
});

export default function MetaDashboard() {
      const { embedUrl, dateRange } = useLoaderData<typeof loader>();
      const [isLoading, setIsLoading] = useState(true);
      const submit = useSubmit();
      const [searchParams] = useSearchParams();


      // Handle iframe loading state
      useEffect(() => {
            if (embedUrl) {
                  setIsLoading(false);
            }
      }, [embedUrl]);

      // Scroll synchronization
      useEffect(() => {
            const handleScroll = () => {
                  const iframe = document.getElementById("metabase-iframe") as HTMLIFrameElement;
                  if (iframe) {
                        iframe.contentWindow?.scrollTo(0, window.scrollY);
                  }
            };

            window.addEventListener("scroll", handleScroll);
            return () => window.removeEventListener("scroll", handleScroll);
      }, []);

      // Handle dropdown change
      const handleDateRangeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
            const newDateRange = event.target.value;
            submit({ dateRange: newDateRange }, { method: "get", action: "." });
      };

      return (
            <div className="flex min-h-screen">
                  <main className="flex-1 overflow-y-auto">
                        <div className="p-4 sm:p-6">
                              <div className="flex justify-between items-center mb-4">
                                    <h1 className="text-2xl font-bold text-gray-800">Sales Dashboard</h1>
                                    <div>
                                          <label htmlFor="dateRange" className="text-gray-700 font-bold mr-2">
                                                Range:
                                          </label>
                                          <select
                                                value={dateRange}
                                                onChange={handleDateRangeChange}
                                                className="p-2 border rounded-md bg-white text-gray-800"
                                          >
                                                <option value="1">Daily</option>
                                                <option value="7">Weekly</option>
                                                <option value="30">Monthly</option>
                                          </select>
                                    </div>
                              </div>

                              <div className="bg-white shadow-md rounded-md overflow-hidden">
                                    {isLoading ? (
                                          <div className="flex justify-center items-center h-96">
                                                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                                          </div>
                                    ) : embedUrl ? (
                                          <iframe
                                                id="metabase-iframe"
                                                src={embedUrl}
                                                title="Metabase Dashboard"
                                                className="w-full h-[2000px] border-0"
                                                allowTransparency
                                          />
                                    ) : (
                                          <div className="p-6 text-center text-red-500">
                                                Failed to load the dashboard.
                                          </div>
                                    )}
                              </div>
                        </div>
                  </main>
            </div>
      );
}