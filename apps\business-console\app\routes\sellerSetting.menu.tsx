import { use<PERSON><PERSON>back, useEffect, use<PERSON>emo, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Switch } from "~/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import {
  Search,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Clock,
  DollarSign,
  TrendingUp,
  AlertCircle,
  Package,
  ToggleLeft,
  ToggleRight
} from "lucide-react";
import { ActionFunction, json } from "@remix-run/node";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { getInventoryItems, getSellerCategories, updateInventoryItemStatus } from "~/services/inventoryItems";
import { useFetcher } from "@remix-run/react";
import { useToast } from "~/hooks/use-toast"
import { Category } from "~/types/home/<USER>";
import { InventoryItem } from "~/types/api/businessConsoleService/inventoryItems";
import { Separator } from "~/components/ui/separator"
import { Label } from "~/components/ui/label"
import dayjs from "dayjs";
import SpinnerLoader from "~/components/loader/SpinnerLoader";

type ActionIntent = "Fetch Menu" | "Fetch Categories" | "Edit Item" | "Update Item Status" | "Update Quantity";

interface ActionData {
  intent: ActionIntent;
  errorMessage: string;
  success: boolean;
  data: { inventoryItems: InventoryItem[], categories: Category[] };
}

export const action: ActionFunction = withAuth(async ({ request, user }) => {
  const formData = await request.formData();
  const intent = formData.get("intent") as ActionIntent;
  const data = formData.get("data") as any;

  if (!intent) {
    return json({ success: false, errorMessage: "Invalid request", intent: intent }, { status: 400 });
  }

  if (!user.sellerId) {
    return json({ success: false, errorMessage: "Seller ID not found", intent: intent }, { status: 400 });
  }

  if (intent === "Fetch Menu") {
    const { filterDate, filterCategory, filterStatus } = JSON.parse(data);
    const queryParams = `&userId=${user.userId}&deliveryDate=${filterDate ? dayjs(filterDate).format("YYYY-MM-DD") : ""}${filterCategory ? filterCategory === "0" ? "" : `&categoryId=${filterCategory}` : ""}${filterStatus ? filterStatus === "all" ? "" : `&type=${filterStatus}` : ""}`;

    try {
      const response = await getInventoryItems(request, queryParams);
      return withResponse({ success: true, intent: intent, data: response?.data }, response?.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to fetch menu" }, { status: 400 })
    }
  }
  if (intent === "Fetch Categories") {
    try {
      const response = await getSellerCategories(request, `&userId=${user.userId}`);
      return withResponse({ success: true, intent: intent, data: response?.data }, response?.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to fetch categories" }, { status: 400 })
    }
  }
  if (intent === "Update Item Status") {
    try {
      const { item, formData } = JSON.parse(data);
      const payload = {
        inventoryId: item.inventoryId,
        sellerItemId: item.sellerItemId,
        holdBooking: !item.holdBooking,
      }

      const response = await updateInventoryItemStatus(request, payload);
      return withResponse({ success: true, intent: intent }, response.headers);
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to update item status" }, { status: 400 })
    }
  }
  if (intent === "Edit Item") {
    try {
      const { item, formData } = JSON.parse(data);

      // TODO: Implement actual API call to update inventory item
      // const response = await updateInventoryItem(request, item.id, formData);
      return withResponse({ success: true, intent: intent }, undefined); //response.headers
    }
    catch (error) {
      return json({ success: false, intent: intent, errorMessage: "Failed to update inventory item" }, { status: 400 })
    }
  }

  return json({ success: false, intent: intent, errorMessage: "Invalid intent" }, { status: 400 });
});


export default function Menu() {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [fetching, setFetching] = useState(false);

  // action
  const fetcher = useFetcher<ActionData>()
  const fetcher1 = useFetcher<ActionData>()
  const [actionType, setActionType] = useState("")
  const [actionSelectedItem, setActionSelectedItem] = useState<InventoryItem | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Search and Filters
  const [searchTerm, setSearchTerm] = useState("");
  const [filterDate, setFilterDate] = useState<Date | undefined>(new Date());
  const [filterCategory, setFilterCategory] = useState("0");
  const [filterStatus, setFilterStatus] = useState("all");

  const { filteredInventoryItems } = useMemo(() => {
    return {
      filteredInventoryItems: inventoryItems.filter((item) =>
        item.itemName.toLowerCase().includes(searchTerm.toLowerCase())
      )
    };
  }, [inventoryItems, searchTerm]);

  const fetchMenu = useCallback(() => {
    console.log("Fetching menu...")

    const formData = new FormData();
    formData.append("intent", "Fetch Menu");
    const data = { filterDate, filterCategory, filterStatus }
    formData.append("data", JSON.stringify(data))
    fetcher.submit(formData, { method: "post" })
    setFetching(true)
  }, [filterDate, filterCategory, filterStatus])

  const fetchCategories = useCallback(() => {
    console.log("Fetching categories...")

    const formData = new FormData();
    formData.append("intent", "Fetch Categories");
    fetcher1.submit(formData, { method: "post" })
  }, [])

  useEffect(() => {
    fetchMenu()
    fetchCategories()
  }, [filterDate, filterCategory, filterStatus])

  const handleAction = (item: InventoryItem, action: string) => {
    setActionSelectedItem(item)
    setActionType(action)
  }

  const handleSubmitAction = (formData: any) => {
    const actionData = new FormData();
    actionData.append("intent", actionType);
    actionData.append("data", JSON.stringify({ item: actionSelectedItem, formData }))
    fetcher.submit(actionData, { method: "post" })
    setIsSubmitting(true)
  }

  useEffect(() => {
    if (fetcher.data?.intent === "Fetch Menu") {
      if (fetcher.data?.success) {
        fetcher.data?.data?.inventoryItems ? setInventoryItems(fetcher.data.data.inventoryItems) : setInventoryItems([])
        setFetching(false)
      } else if (fetcher.data?.success === false) {
        console.log(fetcher.data?.errorMessage)
        setFetching(false)
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage,
          variant: "destructive"
        })
      }
    }
    if (fetcher.data?.intent === "Edit Item") {
      if (fetcher.data?.success) {
        toast({
          title: "Item Update",
          description: "Item updated successfully",
          style: {
            backgroundColor: "#00A33F",
            color: "#ffffff"
          }
        })
        setActionSelectedItem(null)
        setSelectedItem(null)
        setActionType("")
        setIsSubmitting(false)
        fetchMenu()
      } else if (fetcher.data?.success === false) {
        console.log(fetcher.data?.errorMessage)
        setIsSubmitting(false)
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage,
          variant: "destructive"
        })
      }
    }
    if (fetcher.data?.intent === "Update Item Status") {
      if (fetcher.data?.success) {
        toast({
          title: "Item Status",
          description: "Item Status updated successfully",
          style: {
            backgroundColor: "#00A33F",
            color: "#ffffff"
          }
        })
        setActionSelectedItem(null)
        setSelectedItem(null)
        setActionType("")
        setIsSubmitting(false)
        fetchMenu()
      } else if (fetcher.data?.success === false) {
        console.log(fetcher.data?.errorMessage)
        setIsSubmitting(false)
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage,
          variant: "destructive"
        })
      }
    } else if (fetcher.data?.intent === "Update Quantity") {
      if (fetcher.data?.success) {
        toast({
          title: "Item Quantity",
          description: "Item Quantity updated successfully",
          style: {
            backgroundColor: "#00A33F",
            color: "#ffffff"
          }
        })
        setActionSelectedItem(null)
        setSelectedItem(null)
        setActionType("")
        setIsSubmitting(false)
        fetchMenu()
      } else if (fetcher.data?.success === false) {
        console.log(fetcher.data?.errorMessage)
        setIsSubmitting(false)
        toast({
          title: "Error",
          description: fetcher.data?.errorMessage,
          variant: "destructive"
        })
      }
    }
  }, [fetcher.data])

  useEffect(() => {
    if (fetcher1.data?.intent === "Fetch Categories") {
      if (fetcher1.data?.success) {
        fetcher1.data?.data?.categories ? setCategories(fetcher1.data.data.categories) : setCategories([])
      } else if (fetcher1.data?.success === false) {
        console.log(fetcher1.data?.errorMessage)
        setIsSubmitting(false)
        toast({
          title: "Error",
          description: fetcher1.data?.errorMessage,
          variant: "destructive"
        })
      }
    }
  }, [fetcher1.data])

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Menu Management</h1>
        <p className="text-gray-600 mt-2">Manage your restaurant menu items</p>
      </div>

      <SpinnerLoader loading={fetching} globalSpinner={true} />

      <div className="space-y-5">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Total Items</p>
                  <p className="text-2xl font-bold">{inventoryItems.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <Eye className="h-4 w-4 text-green-600" />
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Live Items</p>
                  <p className="text-2xl font-bold text-green-600">
                    {inventoryItems.filter(item => !item.holdBooking).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-orange-500" />
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Low Stock</p>
                  <p className="text-2xl font-bold text-orange-500">
                    {inventoryItems.filter(item => item.totalQty < 10).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          {/* <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-primary" />
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Avg Margin</p>
                  <p className="text-2xl font-bold">
                    {inventoryItems.length > 0 ? (inventoryItems.reduce((acc, item) => acc + item.discPerc, 0) / inventoryItems.length).toFixed(1) : 0}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card> */}
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search menu items..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">All Categories</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>{category.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Live</SelectItem>
                  <SelectItem value="inactive">Offline</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Menu Items Table */}
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-black font-semibold">Item</TableHead>
                  <TableHead className="text-black font-semibold">Tag</TableHead>
                  <TableHead className="text-black font-semibold">Price</TableHead>
                  <TableHead className="text-black font-semibold">Discount</TableHead>
                  <TableHead className="text-black font-semibold">Quantity</TableHead>
                  <TableHead className="text-black font-semibold">Status</TableHead>
                  <TableHead className="text-black font-semibold">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInventoryItems.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <img
                          src={item.itemImage}
                          alt={item.itemName}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                        <div>
                          <p className="font-medium">{item.itemName}</p>
                          <div className="flex items-center gap-2">
                            {/* <Badge variant="outline" className="text-xs">
                              {item.unit}
                            </Badge> */}
                            {item.freeItem && (
                              <Badge variant="secondary" className="text-xs">
                                Free Item
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{item.itemTag || "N/A"}</TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">₹{item.price}</p>
                        {item.strikeOffPrice > 0 && item.strikeOffPrice !== item.price && (
                          <p className="text-sm text-muted-foreground line-through">
                            ₹{item.strikeOffPrice}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <TrendingUp className="h-3 w-3 text-green-600" />
                        <span className="text-sm text-green-600">{item.discPerc.toFixed(1)}%</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="text-sm font-medium">{item.totalQty} {item.unit}</p>
                        <p className="text-xs text-muted-foreground">Sold: {item.soldQty}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={!item.holdBooking}
                          onCheckedChange={() => handleAction(item, "Update Item Status")}
                        />
                        {!item.holdBooking ? (
                          <Eye className="h-3 w-3 text-green-600" />
                        ) : (
                          <EyeOff className="h-3 w-3 text-muted-foreground" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedItem(item)}
                          title="View Details"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAction(item, "Edit Item")}
                          title="Edit Item"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Inventory Item Details Modal */}
        <InventoryItemDetailsModal
          item={selectedItem}
          onClose={() => setSelectedItem(null)}
          onAction={handleAction}
        />

        {/* Inventory Item Action Modal */}
        <InventoryItemActionModal
          item={actionSelectedItem}
          actionType={actionType}
          onClose={() => {
            setActionSelectedItem(null)
            setActionType("")
          }}
          isSubmitting={isSubmitting}
          onSubmit={handleSubmitAction}
        />
      </div>
    </div >
  );
}


interface InventoryItemDetailsModalProps {
  item: InventoryItem | null
  onClose: () => void
  onAction?: (item: InventoryItem, action: string) => void
}

export function InventoryItemDetailsModal({ item, onClose, onAction }: InventoryItemDetailsModalProps) {
  if (!item) return null

  const handleAction = (action: string) => {
    if (onAction) {
      onAction(item, action)
    }
  }

  return (
    <Dialog open={!!item} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full rounded-md">
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl flex items-center gap-2">
            <img
              src={item.itemImage}
              alt={item.itemName}
              className="w-8 h-8 rounded object-cover"
              onError={(e) => {
                e.currentTarget.src = "/placeholder-image.png"
              }}
            />
            {item.itemName}
            <Badge variant={!item.holdBooking ? "default" : "secondary"}>
              {!item.holdBooking ? "Active" : "Inactive"}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Package className="w-4 h-4" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm text-muted-foreground">Item Name</label>
                  <p className="font-medium">{item.itemName}</p>
                </div>
                {item.itemRegionalName && (
                  <div>
                    <label className="text-sm text-muted-foreground">Regional Name</label>
                    <p>{item.itemRegionalName}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm text-muted-foreground">Unit</label>
                  <p>{item.unit}</p>
                </div>
                <div>
                  <label className="text-sm text-muted-foreground">Packaging</label>
                  <p>{item.packaging}</p>
                </div>
                <div>
                  <label className="text-sm text-muted-foreground">Item Tag</label>
                  <p>{item.itemTag || "N/A"}</p>
                </div>
                <div>
                  <label className="text-sm text-muted-foreground">Rating</label>
                  <div className="flex items-center gap-1">
                    <span className="font-medium">{item.itemRating.toFixed(1)}</span>
                    <span className="text-yellow-500">★</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <DollarSign className="w-4 h-4" />
                  Pricing & Financial
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm text-muted-foreground">Current Price</label>
                  <p className="text-xl font-bold">₹{item.price}</p>
                </div>
                {item.strikeOffPrice > 0 && item.strikeOffPrice !== item.price && (
                  <div>
                    <label className="text-sm text-muted-foreground">Strike Off Price</label>
                    <p className="text-lg line-through text-gray-500">₹{item.strikeOffPrice}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm text-muted-foreground">Average Price</label>
                  <p>₹{item.avgPrice}</p>
                </div>
                <div>
                  <label className="text-sm text-muted-foreground">Discount %</label>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-green-600 font-medium">{item.discPerc.toFixed(1)}%</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm text-muted-foreground">Revenue</label>
                  <p className="font-medium">₹{item.revenue.toLocaleString()}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Inventory & Sales */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Inventory Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm text-muted-foreground">Total Quantity</label>
                  <p className="font-medium">{item.totalQty} {item.unit}</p>
                </div>
                <div>
                  <label className="text-sm text-muted-foreground">Total Boxes</label>
                  <p>{item.totalBoxes}</p>
                </div>
                <div>
                  <label className="text-sm text-muted-foreground">Box Quantity</label>
                  <p>{item.boxQty} {item.unit}/box</p>
                </div>
                <div>
                  <label className="text-sm text-muted-foreground">Hold Booking</label>
                  <Badge variant={item.holdBooking ? "destructive" : "default"}>
                    {item.holdBooking ? "Yes" : "No"}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Sales Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm text-muted-foreground">Sold Quantity</label>
                  <p className="font-medium">{item.soldQty} {item.unit}</p>
                </div>
                <div>
                  <label className="text-sm text-muted-foreground">Shop Count</label>
                  <p>{item.shopCount}</p>
                </div>
                <div>
                  <label className="text-sm text-muted-foreground">Supplier</label>
                  <p>{item.supplierName || "N/A"}</p>
                </div>
                <div>
                  <label className="text-sm text-muted-foreground">Group ID</label>
                  <p>{item.groupId || "N/A"}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Permissions & Settings */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Permissions & Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm">Distribution</span>
                  {item.isDistributionAllowed ? (
                    <Eye className="w-4 h-4 text-green-600" />
                  ) : (
                    <EyeOff className="w-4 h-4 text-gray-400" />
                  )}
                </div>
                <div className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm">Price Edit</span>
                  {item.isPriceEditAllowed ? (
                    <Edit className="w-4 h-4 text-green-600" />
                  ) : (
                    <Edit className="w-4 h-4 text-gray-400" />
                  )}
                </div>
                <div className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm">Qty Edit</span>
                  {item.isQtyEditAllowed ? (
                    <Edit className="w-4 h-4 text-green-600" />
                  ) : (
                    <Edit className="w-4 h-4 text-gray-400" />
                  )}
                </div>
                <div className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm">Free Item</span>
                  {item.freeItem ? (
                    <ToggleRight className="w-4 h-4 text-green-600" />
                  ) : (
                    <ToggleLeft className="w-4 h-4 text-gray-400" />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* Action Buttons */}
          <div className="flex gap-2 flex-wrap">
            <Button
              onClick={() => handleAction("Edit Item")}
              className="flex-1"
            >
              Edit Item
            </Button>
            <Button
              variant="outline"
              onClick={() => handleAction("Update Item Status")}
              className="flex-1"
            >
              {!item.holdBooking ? "Deactivate" : "Activate"}
            </Button>
            <Button
              variant="outline"
              onClick={() => handleAction("Update Quantity")}
              className="flex-1"
            >
              Update Quantity
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}


interface InventoryItemActionModalProps {
  item: InventoryItem | null
  actionType: string
  onClose: () => void
  isSubmitting: boolean
  onSubmit: (formData: any) => void
}

export function InventoryItemActionModal({
  item,
  actionType,
  onClose,
  isSubmitting,
  onSubmit
}: InventoryItemActionModalProps) {
  const [formData, setFormData] = useState({
    // Edit Item fields
    itemName: item?.itemName || "",
    price: item?.price || 0,
    strikeOffPrice: item?.strikeOffPrice || 0,
    totalQty: item?.totalQty || 0,
    packaging: item?.packaging || "",
    itemTag: item?.itemTag || "",

    // Update Status fields
    isLive: !item?.holdBooking,

    // Update Quantity fields
    newQuantity: item?.totalQty || 0,
  })

  useEffect(() => {
    setFormData({
      itemName: item?.itemName || "",
      price: item?.price || 0,
      strikeOffPrice: item?.strikeOffPrice || 0,
      totalQty: item?.totalQty || 0,
      packaging: item?.packaging || "",
      itemTag: item?.itemTag || "",
      isLive: !item?.holdBooking,
      newQuantity: item?.totalQty || 0,
    })
  }, [item])

  const handleSubmit = () => {
    onSubmit(formData)
  }

  if (!item || !actionType) return null

  const renderEditItemModal = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 p-4 bg-blue-50 border border-blue-200 rounded">
        <Edit className="w-5 h-5 text-blue-600" />
        <div>
          <p className="font-medium text-blue-800">Edit Item Details</p>
          <p className="text-sm text-blue-600">Update the item information below</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="itemName">Item Name</Label>
          <Input
            id="itemName"
            value={formData.itemName}
            onChange={(e) => setFormData({ ...formData, itemName: e.target.value })}
            placeholder="Enter item name"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="price">Price (₹)</Label>
          <Input
            id="price"
            type="number"
            value={formData.price}
            onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
            placeholder="Enter price"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="strikeOffPrice">Strike Off Price (₹)</Label>
          <Input
            id="strikeOffPrice"
            type="number"
            value={formData.strikeOffPrice}
            onChange={(e) => setFormData({ ...formData, strikeOffPrice: Number(e.target.value) })}
            placeholder="Enter strike off price"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="totalQty">Total Quantity</Label>
          <Input
            id="totalQty"
            type="number"
            value={formData.totalQty}
            onChange={(e) => setFormData({ ...formData, totalQty: Number(e.target.value) })}
            placeholder="Enter total quantity"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="packaging">Packaging</Label>
          <Input
            id="packaging"
            value={formData.packaging}
            onChange={(e) => setFormData({ ...formData, packaging: e.target.value })}
            placeholder="Enter packaging details"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="itemTag">Item Tag</Label>
          <Input
            id="itemTag"
            value={formData.itemTag}
            onChange={(e) => setFormData({ ...formData, itemTag: e.target.value })}
            placeholder="Enter item tag"
          />
        </div>
      </div>

      <div className="flex gap-2 pt-4">
        <Button onClick={onClose} variant="outline" className="flex-1">
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting} className="flex-1">
          {isSubmitting ? "Updating..." : "Update Item"}
        </Button>
      </div>
    </div>
  )

  const renderUpdateStatusModal = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <ToggleLeft className="w-5 h-5 text-yellow-600" />
        <div>
          <p className="font-medium text-yellow-800">Update Item Status</p>
          <p className="text-sm text-yellow-600">
            This will {formData.isLive ? "deactivate" : "activate"} the item for customers.
          </p>
        </div>
      </div>

      <div className="flex gap-2 pt-4">
        <Button onClick={onClose} variant="outline" className="flex-1">
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting} className="flex-1" variant={formData.isLive ? "destructive" : "default"}>
          {isSubmitting ? "Updating..." : `${formData.isLive ? "Deactivate" : "Activate"} Item`}
        </Button>
      </div>
    </div>
  )

  const renderUpdateQuantityModal = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 p-4 bg-green-50 border border-green-200 rounded">
        <Package className="w-5 h-5 text-green-600" />
        <div>
          <p className="font-medium text-green-800">Update Quantity</p>
          <p className="text-sm text-green-600">
            Current quantity: {item.totalQty} {item.unit}
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="newQuantity">New Quantity ({item.unit})</Label>
          <Input
            id="newQuantity"
            type="number"
            value={formData.newQuantity}
            onChange={(e) => setFormData({ ...formData, newQuantity: Number(e.target.value) })}
            placeholder="Enter new quantity"
          />
          <p className="text-sm text-muted-foreground">
            Change: {formData.newQuantity - item.totalQty > 0 ? "+" : ""}{formData.newQuantity - item.totalQty} {item.unit}
          </p>
        </div>
      </div>

      <div className="flex gap-2 pt-4">
        <Button onClick={onClose} variant="outline" className="flex-1">
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="flex-1"
        >
          {isSubmitting ? "Updating..." : "Update Quantity"}
        </Button>
      </div>
    </div>
  )

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl rounded-md">
        <DialogHeader>
          <DialogTitle>
            {actionType === "Edit Item" && "Edit Item Details"}
            {actionType === "Update Item Status" && "Update Item Status"}
            {actionType === "Update Quantity" && "Update Item Quantity"}
          </DialogTitle>
        </DialogHeader>

        {actionType === "Edit Item" && renderEditItemModal()}
        {actionType === "Update Item Status" && renderUpdateStatusModal()}
        {actionType === "Update Quantity" && renderUpdateQuantityModal()}
      </DialogContent>
    </Dialog>
  )
}
