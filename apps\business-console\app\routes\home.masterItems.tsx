import { json, type TypedResponse } from "@remix-run/node";
import {
  useLoaderData,
  useNavigate,
  useFetcher,
} from "@remix-run/react";
import * as React from "react";
import { useCallback, useEffect } from "react";
import { useDebounce } from "~/hooks/useDebounce";
import s3Service from "~/services/s3.service";
import { z } from "zod";

// shadcn/ui components
import { Button } from "@components/ui/button";
import { Input } from "@components/ui/input";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@components/ui/tabs";
import {
  Dialog,
  DialogHeader,
  DialogTrigger,
  DialogContent,
  DialogFooter,
  DialogTitle,
} from "@components/ui/dialog";

import ItemTable from "~/components/masterItems/ItemTable";
import StepOne from "~/components/masterItems/StepOne";
import StepTwo from "~/components/masterItems/StepTwo";
import StepThree from "~/components/masterItems/StepThree";
import { getMasterItems, createMasterItem, updateMasterItem, getCategories } from "~/services/masterItems.service";
import { withAuth, withResponse } from "@utils/auth-utils";
import type { MasterItemRequest, MasterItemDto, FormState, LoaderData, ActionData } from "~/types/home/<USER>";
import { getInitialFormState, itemSchema, stepOneSchema, stepTwoSchema, stepThreeSchema, editItemSchema, getInitialRestaurantFormState } from "~/schemas/masterItems.schemas";
import { masterItemDtoToFormState, formStateToMasterItemRequest } from "~/utils/parsers/masterItems.parsers";
import { PageSizeSelector } from "~/components/ui/pageSizeSelector";

// Define a type for the loader function
type LoaderFunctionReturn = Promise<TypedResponse<LoaderData>>;

// Mock data for brands
const MOCK_BRANDS = Array.from({ length: 50 }, (_, i) => ({
  id: String(i + 1),
  name: `Brand ${i + 1}`
}));

// Helper to paginate and search mock data
function paginateAndSearch<T extends { name: string }>(
  items: T[],
  search: string,
  page: number,
  size: number
) {
  const filtered = search
    ? items.filter(item => item.name.toLowerCase().includes(search.toLowerCase()))
    : items;

  const start = (page - 1) * size;
  const end = start + size;
  return filtered.slice(start, end);
}

// -------------- Loader --------------
export const loader = withAuth(async ({ request }): LoaderFunctionReturn => {
  const url = new URL(request.url);
  const tab = url.searchParams.get("tab") || "b2b";
  const search = url.searchParams.get("search") || "";
  const page = parseInt(url.searchParams.get("page") || "0", 10);
  const pageSize = parseInt(url.searchParams.get("pageSize") || "20", 10);

  // Handle brands search
  if (url.searchParams.has("searchBrands")) {
    const brandSearch = url.searchParams.get("searchBrands") || "";
    const brandPage = parseInt(url.searchParams.get("brandPage") || "1", 10);
    const brands = paginateAndSearch(MOCK_BRANDS, brandSearch, brandPage, 10);
    return json<LoaderData>({ brands, brandPage });
  }

  // Handle categories search
  if (url.searchParams.has("searchCategories")) {
    const categorySearch = url.searchParams.get("searchCategories") || "";
    const categoryPage = parseInt(url.searchParams.get("categoryPage") || "0", 10);
    const level = parseInt(url.searchParams.get("level") || "1");
    const ondcDomain = url.searchParams.get("ondcDomain") || "";
    try {
      const response = await getCategories(request, {
        level: level,
        matchBy: categorySearch,
        ondcDomain: ondcDomain as "RET10" | "RET11",
        pageNo: categoryPage,
        size: 20
      });
      return json<LoaderData>({
        categories: response.data,
        categoryPage
      });
    } catch (error) {
      console.error("Categories fetch error:", error);
      return json<LoaderData>({ categories: [], categoryPage });
    }
  }

  try {
    const response = (await getMasterItems(request, {
      page,
      limit: pageSize,
      search,
      type: tab
    }));

    const responseHeaders = new Headers();

    if (response && response.headers?.has('Set-Cookie')) {
      responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);
    }

    return json<LoaderData>({
      items: response.data?.items || [],
      tab,
      search,
      currentPage: page,
      totalPages: response.data.totalPages,
      totalItems: response.data.items.length,
      itemsPerPage: pageSize
    }, {
      headers: responseHeaders
    });
  } catch (error) {
    console.error("Master items error:", error);
    if (error instanceof Response && error.status === 404) {
      throw json({ error: "Items not found" }, { status: 404 });
    }
    throw new Response("Failed to fetch master items", { status: 500 });
  }
});

// -------------- Action --------------
export const action = withAuth(async ({ user, request }) => {
  const formData = await request.formData();
  const intent = formData.get("_intent");

  if (formData.get("_action") === "uploadImage") {
    try {
      const file = formData.get("file");
      console.log("Received file:", {
        type: file?.constructor.name,
        isBlob: file instanceof Blob,
        size: file instanceof Blob ? file.size : 'N/A',
        contentType: file instanceof Blob ? file.type : 'N/A'
      });

      if (!file || !(file instanceof Blob)) {
        return json({ success: false, error: "No file provided" }, { status: 400 });
      }

      // Validate file size
      const MAX_FILE_SIZE = 500 * 1024 * 1024; // 5MB
      if (file.size > MAX_FILE_SIZE) {
        return json({
          success: false,
          error: "File size exceeds 5MB limit"
        }, { status: 400 });
      }

      // Read file as buffer
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      const fileUrl = await s3Service.uploadFile({
        file: buffer,
        fileName: (file as File).name || 'image.jpg',
        contentType: file.type || 'image/jpeg',
      });

      return json({ success: true, fileUrl, intent });
    } catch (error) {
      console.error("File upload error:", error);
      if (error instanceof Error) {
        return json({
          success: false,
          error: error.message || "Failed to upload file"
        }, { status: 500 });
      }
      return json({
        success: false,
        error: "An unexpected error occurred while uploading the file"
      }, { status: 500 });
    }
  }

  // Get the request data directly as MasterItemRequest
  const requestDataStr = formData.get("requestData")?.toString();
  if (!requestDataStr) {
    return json({ error: "No request data provided" }, { status: 400 });
  }

  let apiRequestData: MasterItemRequest;
  try {
    apiRequestData = JSON.parse(requestDataStr);
  } catch (error) {
    return json({ error: "Invalid request data format" }, { status: 400 });
  }

  // Validate with Zod schema

  if (intent === "edit") {
    const parseResult = editItemSchema.safeParse(apiRequestData);
    if (!parseResult.success) {
      return json({ errors: parseResult.error.formErrors.fieldErrors }, { status: 400 });
    }
  } else {
    const parseResult = itemSchema.safeParse(apiRequestData);
    if (!parseResult.success) {
      return json({ errors: parseResult.error.formErrors.fieldErrors }, { status: 400 });
    }
  }

  try {
    let response;
    const responseHeaders = new Headers();

    if (intent === "edit") {
      const itemId = formData.get("itemId");
      if (!itemId) {
        throw json({ error: "Item ID is required for editing" }, { status: 400 });

      }
      console.log(apiRequestData, "updatedd................")

      response = await updateMasterItem(Number(itemId), apiRequestData, request);
    } else if (intent === "create" || intent === "duplicate") {
      console.log(apiRequestData, "create................")
      response = await createMasterItem(user.userId.toString(), apiRequestData, request);
    } else {
      throw json({ error: "Invalid intent" }, { status: 400 });
    }

    if (response && response.headers?.has('Set-Cookie')) {
      responseHeaders.set('Set-Cookie', response.headers.get('Set-Cookie')!);
    }

    return withResponse({ success: true, intent }, responseHeaders);
  } catch (error) {
    console.error("Master item operation error:", error);
    throw json({ error: `Failed to ${intent} master item` }, { status: 500 });
  }
});

// -------------- MAIN COMPONENT --------------
export default function MasterItemsRoute() {
  // Get loader data
  const loaderData = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const fetcher = useFetcher<ActionData>();
  const actionData = fetcher.data;

  // For opening/closing the 3-step wizard
  const [openDialog, setOpenDialog] = React.useState(false);
  // Keep track of which step the wizard is on
  const [currentStep, setCurrentStep] = React.useState<1 | 2 | 3>(1);
  // Selected item for edit/duplicate
  const [selectedItem, setSelectedItem] = React.useState<MasterItemDto | null>(null);
  // Mode: 'create' | 'edit' | 'duplicate'
  const [mode, setMode] = React.useState<'create' | 'edit' | 'duplicate'>('create');

  // Form data state matching MasterItemRequest type
  const [formDataState, setFormDataState] = React.useState<FormState>(getInitialFormState());

  // Add debounced search state
  const [searchInput, setSearchInput] = React.useState(loaderData.search || "");
  const debouncedSearch = useDebounce<string>(searchInput, 500);

  // Add page size state
  const [pageSize, setPageSize] = React.useState(loaderData.itemsPerPage?.toString() || "20");

  // Update URL when debounced search changes
  React.useEffect(() => {
    // Only search if length > 3 or empty
    if (debouncedSearch.length === 0 || debouncedSearch.length >= 3) {
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.set("tab", loaderData.tab || "b2b");
      if (debouncedSearch) {
        searchParams.set("search", debouncedSearch);
      } else {
        searchParams.delete("search");
      }
      searchParams.set("page", "0"); // Reset to page 0 on search
      navigate(`?${searchParams.toString()}`);
    }
  }, [debouncedSearch, loaderData.tab, navigate]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data?.success && fetcher.data?.intent !== "uploadImage") {
      setOpenDialog(false);
      setFormDataState(getInitialFormState());
      setCurrentStep(1);
      // Reset form state and reload data
      navigate(`?tab=${loaderData.tab}&search=${loaderData.search || ''}&page=${loaderData.currentPage || 0}`, {
        replace: true,
      });
    }
  }, [fetcher.state, fetcher.data, loaderData.tab, loaderData.search, loaderData.currentPage, navigate]);

  // Handle form data changes
  const handleFormDataChange = (data: Partial<FormState>) => {
    setFormDataState(prev => ({
      ...prev,
      ...data
    }));
  };

  // Handle edit item
  const handleEditItem = useCallback((item: MasterItemDto) => {
    console.log("item", item);
    setFormDataState(masterItemDtoToFormState(item));
    setMode('edit');
    setSelectedItem(item);
    setCurrentStep(1);
    setOpenDialog(true);
  }, []);

  // Handle duplicate item
  const handleDuplicateItem = useCallback((item: MasterItemDto) => {
    const formState = masterItemDtoToFormState(item);
    // Override specific fields for duplicate
    formState.id = undefined;
    formState.itemName = `${item.name} (Copy)`;
    formState.name = `${item.name} (Copy)`;
    formState.source = "mnet";
    formState.sourceKey = crypto.randomUUID();

    setFormDataState(formState);
    setMode('duplicate');
    setSelectedItem(item);
    setCurrentStep(1);
    setOpenDialog(true);
  }, []);

  // Reset dialog state when closing
  const handleDialogChange = (open: boolean) => {
    if (!open) {
      setSelectedItem(null);
      setMode('create');
      setCurrentStep(1);
      setFormDataState(getInitialFormState());
    }
    setOpenDialog(open);
  };

  // For switching tabs
  function handleTabChange(newTab: string) {
    navigate(`?tab=${newTab}&search=${loaderData.search}&page=0&pageSize=${pageSize}`);
  }

  // For handling page changes
  function handlePageChange(newPage: number) {
    navigate(`?tab=${loaderData.tab}&search=${loaderData.search}&page=${newPage}&pageSize=${pageSize}`);
  }

  // Handle page size change
  const handlePageSizeChange = (newSize: string) => {
    setPageSize(newSize);
    navigate(`?tab=${loaderData.tab}&search=${loaderData.search || ''}&page=0&pageSize=${newSize}`);
  };

  // Render step content based on current step
  const renderStepContent = (step: number) => {
    switch (step) {
      case 1:
        return (
          <StepOne
            formData={formDataState}
            onChange={handleFormDataChange}
            errors={actionData?.errors}
            mode={mode}
            renderRETInput={renderRETInput}
          />
        );
      case 2:
        return (
          <StepTwo
            formData={formDataState}
            onChange={handleFormDataChange}
            errors={actionData?.errors}
            mode={mode}
            renderRETInput={renderRETInput}
          />
        );
      case 3:
        return (
          <StepThree
            formData={formDataState}
            onChange={handleFormDataChange}
            errors={actionData?.errors}
            mode={mode}
            renderRETInput={renderRETInput}
          />
        );
      default:
        return null;
    }
  };

  // conditionally render an input whether restaurant or non-restaurant
  const renderRETInput = useCallback((field: string) => {
    const RET10INPUTNAMES = ["brandName", "translations", "type", "unit", "minimumOrderQty", "maximumOrderQty", "maxAvailableQty", "incrementOrderQty", "weightFactor", "productId", "gstEligible", "groupId"];
    const RET11INPUTNAMES = ["description", "diet", "groupId"];

    if (formDataState?.itemConfig?.ondcDomain === "RET10") {
      return RET10INPUTNAMES.includes(field);
    }
    if (formDataState?.itemConfig?.ondcDomain === "RET11") {
      return RET11INPUTNAMES.includes(field);
    }
    return false
  }, [formDataState?.itemConfig?.ondcDomain]);

  useEffect(() => {
    if (formDataState?.itemConfig?.ondcDomain === "RET11" && mode === "create") {
      setFormDataState(getInitialRestaurantFormState());
    }
  }, [formDataState?.itemConfig?.ondcDomain])

  // Validation states
  const [validationErrors, setValidationErrors] = React.useState<Record<string, string[]>>({});
  const [isCurrentStepValid, setIsCurrentStepValid] = React.useState(false);

  // Validate current step
  const validateCurrentStep = useCallback(() => {
    try {
      switch (currentStep) {
        case 1:
          stepOneSchema.parse({
            ondcDomain: formDataState.itemConfig.ondcDomain,
            itemName: formDataState.itemName,
            brandName: formDataState.brandName,
            groupId: formDataState.groupId
          });
          setValidationErrors({});
          return true;

        case 2:
          stepTwoSchema.parse({
            ondcDomain: formDataState.itemConfig.ondcDomain,
            images: formDataState.images,
            translations: formDataState.translations,
            searchTags: formDataState.searchTags,
            assignedCategories: formDataState.assignedCategories
          });
          setValidationErrors({});
          return true;

        case 3:
          stepThreeSchema.parse({
            itemConfig: formDataState.itemConfig
          });
          setValidationErrors({});
          return true;

        default:
          return false;
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Convert undefined arrays to empty arrays
        const fieldErrors: Record<string, string[]> = {};
        Object.entries(error.formErrors.fieldErrors).forEach(([key, value]) => {
          fieldErrors[key] = value || [];
        });
        setValidationErrors(fieldErrors);
        return false;
      }
      return false;
    }
  }, [currentStep, formDataState]);

  // Run validation whenever form data or step changes
  React.useEffect(() => {
    const isValid = validateCurrentStep();
    setIsCurrentStepValid(isValid);
  }, [validateCurrentStep]);

  // For switching steps
  const goNext = () => {
    if (isCurrentStepValid) {
      setCurrentStep((step) => (step < 3 ? (step + 1) as 1 | 2 | 3 : step));
    }
  };
  const goBack = () => setCurrentStep((step) => (step > 1 ? (step - 1) as 1 | 2 | 3 : step));

  // Update handleSubmit to use validation state
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!isCurrentStepValid) {
      return;
    }

    const formData = new FormData();

    // Convert form state to API request format
    const apiRequest = formStateToMasterItemRequest(
      formDataState,
      mode === 'edit' && selectedItem ? selectedItem : undefined
    );

    console.log(apiRequest, "pppppppppppppp")

    // Add the serialized request data
    formData.set("requestData", JSON.stringify(apiRequest));
    formData.set("_intent", mode);

    if (mode === "edit" && selectedItem) {
      formData.set("itemId", selectedItem.id.toString());
    }

    fetcher.submit(formData, {
      method: "post",
    });
  };

  // // Show error toast/alert when server returns error
  React.useEffect(() => {
    if (fetcher.data?.errors) {
      // Convert undefined arrays to empty arrays
      const fieldErrors: Record<string, string[]> = {};
      Object.entries(fetcher.data.errors).forEach(([key, value]) => {
        fieldErrors[key] = value || [];
      });
      setValidationErrors(fieldErrors);
    }
  }, [fetcher.data]);

  console.log("actionData", actionData);

  return (
    <div className="p-6">
      <h1 className="text-xl font-bold mb-4">Master Items</h1>

      {/* TABS: B2B / B2C */}
      <Tabs value={loaderData.tab} onValueChange={handleTabChange}>
        <TabsList className="mb-4">
          <TabsTrigger value="b2b">B2B</TabsTrigger>
          <TabsTrigger value="b2c">B2C</TabsTrigger>
        </TabsList>

        <TabsContent value="b2b">
          <div className="">
            <div className="flex justify-between mb-4">
              <Input
                placeholder="Search Items (min. 3 characters)"
                value={searchInput}
                onChange={handleSearchChange}
                className="w-72"
              />
              <PageSizeSelector
                value={pageSize}
                onValueChange={handlePageSizeChange}
              />
            </div>
            <ItemTable
              items={loaderData.items || []}
              currentPage={loaderData.currentPage || 0}
              totalPages={loaderData.totalPages || 1}
              itemsPerPage={loaderData.itemsPerPage || 10}
              onPageChange={handlePageChange}
              onEditItem={handleEditItem}
              onDuplicateItem={handleDuplicateItem}
            />
          </div>
        </TabsContent>

        <TabsContent value="b2c">
          <div className="">
            <div className="mb-4">
              <Input
                placeholder="Search Items (min. 3 characters)"
                value={searchInput}
                onChange={handleSearchChange}
                className="w-72"
              />
            </div>
            <ItemTable
              items={loaderData.items || []}
              currentPage={loaderData.currentPage || 0}
              totalPages={loaderData.totalPages || 1}
              itemsPerPage={loaderData.itemsPerPage || 10}
              onPageChange={handlePageChange}
              onEditItem={handleEditItem}
              onDuplicateItem={handleDuplicateItem}
            />
          </div>
        </TabsContent>
      </Tabs>

      {/* Create/Edit/Duplicate Dialog */}

      <Dialog open={openDialog} onOpenChange={handleDialogChange}>
        <DialogTrigger asChild>
          <Button className="fixed bottom-5 right-5 rounded-full">+ Add Item</Button>
        </DialogTrigger>
        <DialogContent
          className="max-w-[95vw] w-full md:max-w-2xl h-[90vh] flex flex-col"
          aria-describedby="dialog-description"
          onInteractOutside={(e) => {
            const target = e.target as HTMLElement;
            if (target.closest('[role="listbox"], [role="combobox"]')) {
              e.preventDefault();
            }
          }}
        >
          <DialogHeader>
            <h2 className="text-lg font-semibold">
              {mode === 'edit' ? 'Edit' : mode === 'duplicate' ? 'Duplicate' : 'Add'} Master Item
            </h2>
          </DialogHeader>
          <DialogTitle>Step {currentStep}: {getStepName(currentStep)}</DialogTitle>

          <div id="dialog-description" className="sr-only">
            {mode === 'edit' ? 'Edit existing' : mode === 'duplicate' ? 'Create duplicate of' : 'Add new'} master item using a {currentStep}-step form process
          </div>

          <form onSubmit={handleSubmit} className="flex flex-col flex-grow overflow-y-auto">
            <input type="hidden" name="_intent" value={mode} />
            {mode === 'edit' && <input type="hidden" name="itemId" value={selectedItem?.id} />}

            {/* Show server errors if any */}
            {validationErrors && Object.values(validationErrors).flat().length > 0 && (
              <div className="bg-red-50 border border-red-200 text-sm text-red-700 px-4 py-2 rounded mb-2">
                {Object.values(validationErrors).flat().map((error, index) => (
                  <p key={index}>{error}</p>
                ))}
              </div>
            )}

            {/* Scrollable content area */}
            <div className="flex-grow overflow-y-auto">
              {renderStepContent(currentStep)}
            </div>

            {/* Footer stays at bottom */}
            <DialogFooter className="mt-4 border-t pt-4">
              {currentStep > 1 && (
                <Button type="button" variant="outline" onClick={goBack}>
                  Back
                </Button>
              )}
              {currentStep < 3 && (
                <Button
                  type="button"
                  onClick={goNext}
                  disabled={!isCurrentStepValid}
                >
                  Next
                </Button>
              )}
              {currentStep === 3 && (
                <Button
                  type="submit"
                  disabled={fetcher.state === "submitting" || !isCurrentStepValid}
                >
                  {fetcher.state === "submitting" ? "Submitting..." : "Submit"}
                </Button>
              )}
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Helper function to get step name
function getStepName(step: number) {
  switch (step) {
    case 1:
      return "Item Name";
    case 2:
      return "Item Details";
    case 3:
      return "Item Configuration";
    default:
      return "";
  }
}
