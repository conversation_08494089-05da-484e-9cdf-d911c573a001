import { Dayjs } from "dayjs";
import { Edit, Save, X } from "lucide-react";
import { useCallback, useState } from "react";
import { preview } from "vite";
import { BankConfig, BusinessData } from "~/types/api/businessConsoleService/SellerManagement";

interface BillingConfigProps {
      billingConfig: BusinessData; // Make it optional
      onSellerAttributeUpdate: (attribute: string, value: any, isSeller?: boolean) => void;
}





export default function BillingConfig({ billingConfig, onSellerAttributeUpdate }: BillingConfigProps) {

      const bankConfigFields = [
            { label: "Branch", key: "baBranch", type: "text", prefix: "" },
            { label: "Account Number", key: "baNumber", type: "number", prefix: "" },
            { label: "Verified By", key: "baVerifiedBy", type: "text", prefix: "" },
            { label: "IFSC Code", key: "baIfsc", type: "text", prefix: "" },
            { label: "Account Name", key: "baName", type: "text", prefix: "" },
            { label: "Bank Name", key: "baBank", type: "text", prefix: "" },
            { label: "Bank Account Verified", key: "baVerified", type: "checkbox", prefix: "" }
      ];
      const plaFormConfigFields = [

            { label: "Platform Commission Percentage:  ", key: "pcBasicPc", type: "number", prefix: "%" },
            { label: "Fixed Platform Commission per month :  ", key: "pcBasicPmFixed", type: "number", prefix: "₹" },
            { label: "Minimum Platform Commission per month:  ", key: "pcBasicPmMin", type: "number", prefix: "₹" },
            { label: "Sales Commission Percentage:  ", key: "salesCommPc", type: "number", prefix: "%" },
            { label: ".Sales Commission per Kg:  ", key: "salesCommPkg", type: "number", prefix: "₹" },
            { label: "Fixed Sales Commission per month:  ", key: "salesCommPmFixed", type: "number", prefix: "₹" },
            { label: "Minimum Sales Commission per month:  ", key: "salesCommPmMin", type: "number", prefix: "₹" },
            { label: "Agent Commision Percentage :  ", key: "agentCommPc", type: "number", prefix: "%" },
            { label: "Agent Commision per Kg:  ", key: "agentCommPkg", type: "number", prefix: "₹" },



      ]

      const [bankConfigEditable, setBankConfigEditable] = useState(false);
      const [platFormEditable, setPlatFormEditable] = useState(false);

      const [config, setConfig] = useState(billingConfig);

      const setBankEditableClick = useCallback(() => {

            setBankConfigEditable((prevState) => {
                  if (prevState) {

                        return false;

                  } else {
                        return true;
                  }
            })
      }, []);
      const setPlatformEditableClick = useCallback(() => {
            setPlatFormEditable((prevState) => {
                  if (prevState) {
                        return false;
                  } else {
                        return true;
                  }
            })
      }, []);

      const [pendingConfig, setPendingConfig] = useState<Partial<BusinessData>>({});

      const [visibleSaveButtons, setVisibleSaveButtons] = useState<Record<string, boolean>>({});
      const keyMappingPlatform: Record<keyof BusinessData, string> = {
            businessId: "businessId",
            bankConfig: "bankConfig",
            pcBasicPc: "pcBasicPc",
            pcBasicPmFixed: "pcBasicPmFixed",
            pcBasicPmMin: "pcBasicPmMin",
            salesCommPc: "salesCommPc",
            salesCommPkg: "salesCommPkg",
            salesCommPmFixed: "salesCommPmFixed",
            salesCommPmMin: "salesCommPmMin",
            agentCommPc: "agentCommPc",
            agentCommPkg: "agentCommPkg",
      };
      const bankConfigKeyMapping: Record<keyof BankConfig, string> = {
            lubyName: "lubyName",
            lubyId: "lubyId",
            bid: "bid",
            baVerified: "baVerified",
            baBranch: "baBranch",
            baNumber: "baNumber",
            baVerifiedBy: "baVerifiedBy",
            baIfsc: "baIfsc",
            baName: "baName",
            baBank: "baBank",
      };

      const handleConfigChange = (key: keyof BusinessData | keyof BankConfig, value: string | number | boolean | null) => {
            console.log(value, key, "Config Change Triggered");

            setPendingConfig((prev: any) => {
                  let newValue: string | number | boolean | null = value;

                  if (typeof value === "string") {
                        if (value.toLowerCase() === "yes") {
                              newValue = true;
                        } else if (value.toLowerCase() === "no") {
                              newValue = false;
                        } else if (!isNaN(Number(value))) {
                              newValue = Number(value);
                        } else if (value === "0") {
                              newValue = ""; // Prevent showing 0
                        }
                  }

                  if (key in bankConfigKeyMapping) {
                        return {
                              ...prev,
                              bankConfig: {
                                    ...prev.bankConfig,
                                    [key]: newValue,
                              },
                        };
                  } else {
                        return {
                              ...prev,
                              [key]: newValue,
                        };
                  }
            });

            setVisibleSaveButtons((prev) => ({
                  ...prev,
                  [key]: true,
            }));
      };


      const handleSave = (key: keyof BusinessData | keyof BankConfig) => {
            let updatedValue;

            if (key in bankConfigKeyMapping) {
                  updatedValue = pendingConfig.bankConfig?.[key as keyof BankConfig];
                  onSellerAttributeUpdate(`${key}`, updatedValue);
            } else {
                  updatedValue = pendingConfig[key as keyof BusinessData];
                  onSellerAttributeUpdate(key, updatedValue);
            }

            setConfig((prevConfig) => ({
                  ...prevConfig,
                  ...(key in bankConfigKeyMapping
                        ? {
                              bankConfig: {
                                    ...prevConfig.bankConfig,
                                    [key]: updatedValue,
                              },
                        }
                        : { [key]: updatedValue }),
            }));

            setVisibleSaveButtons((prev) => ({
                  ...prev,
                  [key]: false,
            }));

            // Clear only the saved key from pendingConfig
            setPendingConfig((prev) => {
                  const updatedConfig = { ...prev };
                  if (key in bankConfigKeyMapping) {
                        delete updatedConfig.bankConfig?.[key as keyof BankConfig];
                  } else {
                        delete updatedConfig[key as keyof BusinessData];
                  }
                  return updatedConfig;
            });
      };

      return (
            <div className='flex flex-col gap-4 border bg-white rounded-xl border-neutral-200 my-4 p-4'>
                  <div className="flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow">
                        <div className="flex justify-between w-full">
                              <div className="text-lg font-semibold text-typography-700">Bank Configurations</div>
                              <button
                                    className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800"
                                    onClick={() => {
                                          if (!bankConfigEditable) {

                                                setPendingConfig({
                                                      bankConfig: { ...billingConfig.bankConfig }
                                                });
                                          }


                                          setBankEditableClick()
                                    }
                                    }
                              >
                                    {bankConfigEditable ? (
                                          <>
                                                <X className="h-4 w-4" />
                                                Cancel
                                          </>
                                    ) : (
                                          <>
                                                <Edit className="h-4 w-4" />
                                                Edit
                                          </>
                                    )}
                              </button>
                        </div>
                        {/* Business ID Display */}
                        <div className="flex gap-4 items-center">
                              <div className="text-md text-typography-500 w-[400px]">Business ID:</div>
                              {billingConfig.businessId}
                        </div>

                        {/* Bank Config Section */}
                        <div>
                              {bankConfigFields.map(({ label, key, type, prefix }) => (

                                    <div key={key} className="flex flex-col gap-2 text-md text-typography-400">
                                          <div className="flex gap-4 items-center">
                                                <div className="text-md text-typography-500 w-[400px] mt-2">{label}:</div>
                                                <span className="font-semibold text-typography-800 flex items-center">
                                                      {prefix}


                                                      {/* Render input based on type */}



                                                      {type === 'checkbox' ? (
                                                            <input
                                                                  type="checkbox"
                                                                  checked={Boolean(
                                                                        (pendingConfig?.bankConfig?.[key as keyof BankConfig] ?? billingConfig?.bankConfig?.[key as keyof BankConfig]) ?? false
                                                                  )} onChange={(e) =>
                                                                        handleConfigChange(
                                                                              key as keyof BankConfig,
                                                                              e.target.checked
                                                                        )
                                                                  }
                                                                  className="border border-neutral-400 rounded-md p-1 mt-2"
                                                                  disabled={!bankConfigEditable}
                                                            />
                                                      ) : type === 'number' ? (
                                                            <input
                                                                  type="number"
                                                                  value={
                                                                        pendingConfig?.bankConfig?.[key as keyof BankConfig] ??
                                                                              billingConfig?.bankConfig?.[key as keyof BankConfig]
                                                                              ? String(
                                                                                    pendingConfig?.bankConfig?.[key as keyof BankConfig] ??
                                                                                    billingConfig?.bankConfig?.[key as keyof BankConfig]
                                                                              )
                                                                              : ''
                                                                  }
                                                                  onChange={(e) => {
                                                                        const value = e.target.value.trim();
                                                                        if (value === '') {
                                                                              handleConfigChange(key as keyof BankConfig, '');
                                                                              return;
                                                                        }
                                                                        const numericValue = Math.max(0, Number(value)); // Ensures no negative numbers
                                                                        handleConfigChange(key as keyof BankConfig, numericValue);
                                                                  }}
                                                                  className="border border-neutral-400 rounded-md p-1 px-2 mt-2"
                                                                  disabled={!bankConfigEditable}
                                                            />
                                                      ) : (
                                                            <input
                                                                  type="text"

                                                                  value={
                                                                        pendingConfig?.bankConfig?.[key as keyof BankConfig] ??
                                                                              billingConfig?.bankConfig?.[key as keyof BankConfig]
                                                                              ? String(
                                                                                    pendingConfig?.bankConfig?.[key as keyof BankConfig] ??
                                                                                    billingConfig?.bankConfig?.[key as keyof BankConfig]
                                                                              )
                                                                              : ''
                                                                  }
                                                                  onChange={(e) => {
                                                                        const value = e.target.value;

                                                                        // Ensure empty string is passed when cleared
                                                                        handleConfigChange(key as keyof BankConfig, value === "0" || value.trim() === "" ? "" : value);
                                                                  }}
                                                                  className="border border-neutral-400 rounded-md p-1 px-2 mt-2"
                                                                  disabled={!bankConfigEditable}
                                                            />
                                                      )}
                                                </span>

                                                {bankConfigEditable && visibleSaveButtons[key] && (
                                                      <button
                                                            className="flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold"
                                                            onClick={() => handleSave(key as keyof BankConfig)}
                                                      >
                                                            <Save className="h-5 w-5" />
                                                      </button>
                                                )}
                                          </div>
                                    </div>
                              ))}

                        </div>
                  </div>
                  <div className='flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow'>
                        <div className='flex justify-between w-full'>
                              <div className='text-lg font-semibold text-typography-700'>
                                    PlatForm Configurations
                              </div>
                              <button
                                    className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800"
                                    onClick={() => setPlatformEditableClick()}
                              >
                                    {platFormEditable ? <>
                                          <X className='h-4 w-4' />
                                          Cancel
                                    </> : (
                                          <>

                                                <Edit className="h-4 w-4" />
                                                Edit
                                          </>
                                    )}
                              </button>
                        </div>


                        {plaFormConfigFields.map(({ label, key, type, prefix }) => (

                              <div key={key} className="flex flex-col gap-2 text-md text-typography-400">
                                    <div className="flex gap-4 items-center">
                                          <div className="text-md text-typography-500 w-[400px]">{label}
                                          </div>
                                          <span className="font-semibold text-typography-800 flex gap-2 items-center">

                                                <input
                                                      type={type}
                                                      value={String(pendingConfig[key as keyof BusinessData] ?? config[key as keyof BusinessData])}


                                                      // Convert to string
                                                      onChange={(e) => {
                                                            const numericValue = Math.max(0, Number(e.target.value)); // Ensures no negative numbers


                                                            handleConfigChange(key as keyof BusinessData, numericValue)
                                                      }}
                                                      className="border border-neutral-400 rounded-md p-1 px-2"

                                                      disabled={!platFormEditable}
                                                />
                                                {prefix}
                                          </span>
                                          {plaFormConfigFields && visibleSaveButtons[key] && (
                                                <button
                                                      className="flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold"
                                                      onClick={() => handleSave(key as keyof BusinessData)}
                                                >
                                                      <Save className="h-5 w-5" />
                                                </button>
                                          )}
                                    </div>

                              </div>
                        ))}
                  </div>
            </div>
      )
}