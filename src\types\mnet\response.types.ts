export interface CreateSupportTicketResponse {
    ticketId: number;
    ticketType: string;
    createdDate: string;
    status: string;
    appSellerId: number;
    description: string;
}

export interface GetUserDataResponse {
    networkType: "B2B" | "B2C";
    ondcDomain: "RET11" | "RET10";
    businessName: string;
    customerName: string;
    baseUrl: string;
    registered: boolean;
    withinServiceArea: boolean;
    bookingOpen: boolean;
    pendingDuesAmount: number;
    userId: number;
    hasActiveOrder?: boolean;
}

export interface ErrorDto {
    code: string;
    message: string;
}

export interface ResponseDto<T> {
    success: boolean;
    data?: T;
    error?: ErrorDto;
}

export interface FreeItemResponse {
    id: number;
    uuid: string;
    networkBuyerId: number;
    sellerItemId: number;
    sellerItemName: string;
    sellerItemPackaging: string;
    sellerItemPicture: string;
    status: 'DEFAULT' | 'REDEEMED';
    code: string;
    expiresAt: string;
    deepLink: string;
    active: boolean;
}
