// File: app/routes/home.templates.tsx
import {ActionFunction, json, type LoaderFunction} from "@remix-run/node";
import {use<PERSON><PERSON>cher, useLoaderData, useNavigate} from "@remix-run/react";
import { withAuth } from "~/utils/auth-utils";
import { getFirebaseAdmin } from "~/services/firebase.server";
import { Card, CardContent, CardHeader, CardTitle } from "@components/ui/card";
import { Button } from "@components/ui/button";
import { Badge } from "@components/ui/badge";
import {getWhatsAppTemplates, registerTemplate} from "~/services/whatsappApi.server";
import {WhatsAppConnectionData, WhatsAppTemplate} from "~/types/whatsapp";
import {B2B_TEMPLATES, B2C_RESTAURANT_TEMPLATES, B2C_TEMPLATES, REQUIRED_TEMPLATES} from "~/constants/whatsappTemplates";
import { getNetworkTheme } from "~/services/netWorks";
import { NetworkTheme } from "~/types/api/common";

type LoaderData = {
    templates: WhatsAppTemplate[];
    error?: string;
    connectionData?: WhatsAppConnectionData;
    networkConfig?: NetworkTheme | null;
};

// Function to get appropriate templates based on network type and ONDC domain
const getTemplatesForNetwork = (networkConfig: NetworkTheme | null) => {
    if (!networkConfig) return REQUIRED_TEMPLATES;
    
    const { networkType, ondcDomain } = networkConfig;
    
    if (networkType === "B2B") {
        return B2B_TEMPLATES;
    } else if (networkType === "B2C") {
        if (ondcDomain === "RET11") {
            return B2C_RESTAURANT_TEMPLATES;
        } else if (ondcDomain === "RET10") {
            return B2C_TEMPLATES;
        }
    }
    
    return REQUIRED_TEMPLATES;
};

export const loader: LoaderFunction = withAuth(async ({ user, request }) => {
    if (!user) return json({ templates: [], error: "User not authenticated" });

    const db = getFirebaseAdmin();
    const doc = await db.collection("facebook-connects")
        .doc(user.userDetails.sellerId.toString())
        .get();

    if (!doc.exists) {
        return json({ templates: [], error: "WhatsApp connection not found" });
    }

    const connectionData = doc.data() as WhatsAppConnectionData;

    if (!connectionData.wabaId) {
        return json({ templates: [], error: "WhatsApp Business Account ID not found" });
    }

    const response = await getWhatsAppTemplates(
        connectionData.access.access_token,
        connectionData.wabaId
    );

    if (response.error) {
        return json({ templates: [], error: response.error });
    }

    let networkConfig: NetworkTheme | null = null;

    try {
        const networkConfigResponse = await getNetworkTheme(request);
        networkConfig = networkConfigResponse.data || null;
    } catch (error) {
        console.log(error, "error");
    }

    return json({
        templates: response.data,
        connectionData,
        networkConfig
    });
});

export const action: ActionFunction = withAuth(async ({ request, user }) => {
    const formData = await request.formData();
    const actionType = formData.get("actionType");

    if (actionType === "register-templates") {
        const db = getFirebaseAdmin();
        const doc = await db.collection("facebook-connects")
            .doc(user.userDetails.sellerId.toString())
            .get();

        if (!doc.exists) {
            return json({ error: "WhatsApp connection not found" });
        }

        const connectionData = doc.data() as WhatsAppConnectionData;

        if (!connectionData.wabaId) {
            return json({ error: "WhatsApp Business Account ID not found" });
        }

        // Get network config to determine which templates to use
        let networkConfig: NetworkTheme | null = null;
        try {
            const networkConfigResponse = await getNetworkTheme(request);
            networkConfig = networkConfigResponse.data || null;
        } catch (error) {
            console.log(error, "error");
        }

        const templates = (await getWhatsAppTemplates(connectionData.access.access_token, connectionData.wabaId)).data || [];
        
        const approvedTemplateNames = new Set(
            templates.filter(t => t.status === "APPROVED").map(t => t.name)
        );

        // Get the appropriate templates based on network type
        const templatesForNetwork = getTemplatesForNetwork(networkConfig);
        
        const missingTemplates = templatesForNetwork.filter(
            t => !approvedTemplateNames.has(t.name)
        );

        const results = await Promise.allSettled(
            missingTemplates.map(template => {
                // For B2C templates, replace _business_name_ with the actual business name
                let processedTemplate = template;
                if (networkConfig?.networkType === "B2C" && connectionData.businessName) {
                    processedTemplate = {
                        ...template,
                        components: template.components.map(component => {
                            if (component.type === "BODY" && component.text) {
                                const updatedText = component.text.replace(/\*_business_name_\*/g, `*${connectionData.businessName}*`);
                                return {
                                    ...component,
                                    text: updatedText
                                };
                            }
                            return component;
                        })
                    } as typeof REQUIRED_TEMPLATES[0];
                }
                
                return registerTemplate(
                    connectionData.access.access_token,
                    connectionData.wabaId!,
                    processedTemplate
                );
            })
        );

        return json({ success: true, results });
    }

    return json({ error: "Invalid action" });
});

export default function Templates() {
    const { templates, error, networkConfig } = useLoaderData<LoaderData>();
    const navigate = useNavigate();
    const fetcher = useFetcher();

    // Get appropriate templates based on network type
    const templatesForNetwork = getTemplatesForNetwork(networkConfig || null);

    // Consider both missing and rejected templates as needing registration
    const approvedTemplateNames = new Set(
        templates.filter(t => t.status === "APPROVED").map(t => t.name)
    );
    
    const missingTemplates = templatesForNetwork.filter(
        t => !approvedTemplateNames.has(t.name)
    );

    return (
        <div className="p-6">
            <div className="max-w-3xl mx-auto">
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h2 className="text-2xl font-bold">WhatsApp Templates</h2>
                        {missingTemplates.length > 0 && (
                            <p className="text-yellow-600 mt-2">
                                Templates to register: {missingTemplates.length}
                            </p>
                        )}
                        {networkConfig && (
                            <p className="text-sm text-gray-500 mt-1">
                                Network: {networkConfig.networkType || "Default"}
                                {networkConfig.ondcDomain && ` • Domain: ${networkConfig.ondcDomain}`}
                            </p>
                        )}
                    </div>
                    <div className="flex gap-2">
                        {missingTemplates.length > 0 && (
                            <fetcher.Form method="post">
                                <input type="hidden" name="actionType" value="register-templates"/>
                                <Button
                                    type="submit"
                                    disabled={fetcher.state !== "idle"}
                                    className="bg-green-600 hover:bg-green-700"
                                >
                                    {fetcher.state !== "idle" ? "Registering..." : "Register Templates"}
                                </Button>
                            </fetcher.Form>
                        )}
                        <Button
                            variant="outline"
                            onClick={() => navigate("/home/<USER>")}
                        >
                            Back to WhatsApp
                        </Button>
                    </div>
                </div>

                {error ? (
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-center text-red-500">
                                {error}
                            </div>
                        </CardContent>
                    </Card>
                ) : templates.length === 0 ? (
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-center text-gray-500">
                                No templates found
                            </div>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="space-y-4">
                        {templates.map((template) => (
                            <Card key={template.id}>
                                <CardHeader>
                                    <div className="flex justify-between items-center flex-wrap gap-4">
                                        <CardTitle>{template.name}</CardTitle>
                                        <div className="flex gap-2 items-center flex-wrap">
                                            <Badge className={
                                                template.status === "APPROVED" ? "bg-green-500" :
                                                    template.status === "PENDING" ? "bg-yellow-500" :
                                                        template.status === "REJECTED" ? "bg-red-500" :
                                                            "bg-gray-500"
                                            }>
                                                {template.status}
                                            </Badge>
                                            <Badge variant="outline">{template.category}</Badge>
                                            <Badge variant="outline">{template.language}</Badge>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    {template.components.map((component, idx) => (
                                        <div key={idx} className="mb-4 last:mb-0">
                                            <div className="text-sm text-gray-500 mb-1">
                                                {component.type} {component.format && `(${component.format})`}
                                            </div>
                                            <div className="text-gray-700">{component.text}</div>
                                        </div>
                                    ))}
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}
