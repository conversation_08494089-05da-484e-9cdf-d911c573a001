import { ApiResponse } from '~/types/api/Api';
import { API_BASE_URL, apiRequest } from '~/utils/api';
import { AddOnGroup, SelectedItem } from '~/types/api/businessConsoleService/MyItemList';
import { MyVariationData } from '~/types/api/businessConsoleService/SellerManagement';

export async function getSellerItemVariation(
  sellerId: number,
  pageNo?: number,
  size?: number,
  matchBy?: string,
  itemId?:number,
  request?: Request
): Promise<ApiResponse<SelectedItem[]>> {
 
  // Basic validation
  if (!sellerId || typeof sellerId !== "number" || sellerId <= 0) {
    throw new Response("Invalid sellerId provided");
  }

  if (pageNo !== undefined && (typeof pageNo !== "number" || pageNo < 0)) {
    throw new Response("Invalid pageNo provided");
  }

  if (size !== undefined && (typeof size !== "number" || size <= 0)) {
    throw new Response("Invalid size provided");
  }

  if (matchBy !== undefined && typeof matchBy !== "string") {
    throw new Response("matchBy must be a string if provided");
  }

  try {
    const queryParams = new URLSearchParams();
    if (pageNo !== undefined) queryParams.append("pageNo", pageNo.toString());
    if (size !== undefined) queryParams.append("size", size.toString());
    if (matchBy && matchBy.trim().length > 0) {
      queryParams.append("matchBy", matchBy.trim());
    }
    const url = `${API_BASE_URL}/bc/catalog/seller/${sellerId}/item/${itemId}/variations?${queryParams.toString()}`;

    const response = await apiRequest<SelectedItem[]>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Response("Failed to fetch variation groups");
    }
  } catch (error: any) {
    // Log error or handle it accordingly
    throw new Error(`Error in Variations: ${error.message}`);
  }
}
export async function getSellerAddonGroups(
      sellerId: number,
      pageNo?: number,
      size?: number,
      matchBy?: string,
      itemId?:number,
      request?: Request
    ): Promise<ApiResponse<AddOnGroup[]>> {
     
      // Basic validation
      if (!sellerId || typeof sellerId !== "number" || sellerId <= 0) {
        throw new Response("Invalid sellerId provided");
      }
    
      if (pageNo !== undefined && (typeof pageNo !== "number" || pageNo < 0)) {
        throw new Response("Invalid pageNo provided");
      }
    
      if (size !== undefined && (typeof size !== "number" || size <= 0)) {
        throw new Response("Invalid size provided");
      }
    
      if (matchBy !== undefined && typeof matchBy !== "string") {
        throw new Response("matchBy must be a string if provided");
      }
    
      try {
        const queryParams = new URLSearchParams();
        if (pageNo !== undefined) queryParams.append("pageNo", pageNo.toString());
        if (size !== undefined) queryParams.append("size", size.toString());
        if (matchBy && matchBy.trim().length > 0) {
          queryParams.append("matchBy", matchBy.trim());
        }
        const url = `${API_BASE_URL}/bc/catalog/seller/${sellerId}/item/${itemId}/addongroups?${queryParams.toString()}`;
    
        const response = await apiRequest<AddOnGroup[]>(
          url,
          "GET",
          undefined,
          {},
          true,
          request
        );
        if (response) {
          return response;
        } else {
          throw new Response("Failed to fetch variation groups");
        }
      } catch (error: any) {
        // Log error or handle it accordingly
        throw new Error(`Error in Variations: ${error.message}`);
      }
    };

    export async function createItemVariation(
      sellerId: number,
      itemId: number,
      updateData: any,
      request?: Request
    ): Promise<ApiResponse<SelectedItem>> {
      const response = await apiRequest<SelectedItem>(
          `${API_BASE_URL}/bc/catalog/seller/${sellerId}/item/${itemId}/variation`,
          "POST",
          updateData,
          {},
          true,
          request
        );
    
        if (response) {
          return response;
        } else {
          throw new Response("Failed to update addons group");
        }
      
    }
    
    export async function createItemAddonGroup(
      sellerId: number,
      itemId: number,
      updateData: any,
      request?: Request
    ): Promise<ApiResponse<AddOnGroup>> {
     
        const response = await apiRequest<AddOnGroup>(
    
          `${API_BASE_URL}/bc/catalog/seller/${sellerId}/item/${itemId}/addongroup`,
          "POST",
          updateData,
          {},
          true,
          request
        );
    
        if (response) {
          return response;
        } else {
          throw new Response("Failed to update addons group");
        }
      
    }
    export async function deleteItemAddonGroup(
      sellerId: number,
      addonmapId: number,
      itemId:number,
      request?: Request
    ): Promise<ApiResponse<AddOnGroup>> {
      
        const response = await apiRequest<AddOnGroup>(
          `${API_BASE_URL}/bc/catalog/seller/${sellerId}/item/${itemId}/addongroup/${addonmapId}`,
          "DELETE",
          undefined,
          {},
          true,
          request
        );
    
        if (response) {
          if (response.statusCode === 200) {
            return response;
          } else if (response.statusCode === 400) {
            throw new Response("Bad request: Invalid addon ID or seller ID");
          } else {
            throw new Response(`Unexpected response status: ${response.statusCode}`);
          }
        } else {
          throw new Response("Failed to delete AddonMap: No response received");
        }
      
    }
    export async function deleteItemVarition(
      sellerId: number,
      addonmapId: number,
      itemId:number,
      request?: Request
    ): Promise<ApiResponse<MyVariationData>> {
      
        const response = await apiRequest<MyVariationData>(
          `${API_BASE_URL}/bc/catalog/seller/${sellerId}/item/${itemId}/variation/${addonmapId}`,
          "DELETE",
          undefined,
          {},
          true,
          request
        );
    
        if (response) {
          if (response.statusCode === 200) {
            return response;
          } else if (response.statusCode === 400) {
            throw new Response("Bad request: Invalid addon ID or seller ID");
          } else {
            throw new Response(`Unexpected response status: ${response.statusCode}`);
          }
        } else {
          throw new Response("Failed to delete variation: No response received");
        }
      
    }