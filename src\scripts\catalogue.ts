import * as fs from "fs";
import * as path from "path";
import XLSX from "xlsx";
import S3Utils from "@utils/aws/s3.utils.js";
import DynamoDBUtils from "./catalogueDB.js";
import logger from "@utils/express-logger.js";
import dotenv from "dotenv";
import { ProductItem, ProductRowStatus } from "@/types/catalogue.type.js";
import { v4 as uuidv4 } from "uuid";
import { MasterItemDto } from "@/types/mnet/request.types.js";
import MnetApiGatewayService from "@/services/mnetApiGateway.service.js";

const mnetApiGatewayService = new MnetApiGatewayService();

// Load environment variables
dotenv.config();

// Extract environment variables

const S3_BUCKET_NAME = "mneti";
const SWIGGY_IMAGE_BASE_URL = "https://instamart-media-assets.swiggy.com/swiggy/image/upload";


const EXCEL_FILE_PATH = "/Users/<USER>/Downloads/final-sheet.xlsx";
const DYNAMODB_TABLE_NAME = "swiggy_catalogue_prod";

if (!EXCEL_FILE_PATH || !S3_BUCKET_NAME || !SWIGGY_IMAGE_BASE_URL || !DYNAMODB_TABLE_NAME) {
  logger.error("Missing required environment variables. Please check your .env file.");
  process.exit(1);
}

// Initialize DynamoDB Utility
const dynamoDBUtils = new DynamoDBUtils(DYNAMODB_TABLE_NAME);

// Define the structure of the Excel sheet columns
// Assuming the first row contains headers as provided
const expectedHeaders = [
  "Product Display Name",
  "Brand",
  "Brand ID",
  "Variation Display Name",
  "Variation ID",
  "Images",
  "Quantity",
  "Unit of Measure",
  "Weight in Grams",
  "Product ID",
  "Product Name Without Brand",
  "MRP",
  "Store Price",
  "Packaging",
  "Secondary Packaging",
  "final Packaging",
];

// Define interface for product
interface ProductRow {
  Product_Display_Name: string;
  Brand: string;
  Brand_ID: string;
  Variation_Display_Name: string;
  Variation_ID: string;
  Images: string;
  Quantity: number;
  Unit_of_Measure: string;
  Weight_in_Grams: number;
  ProductID: string;
  Product_Name_Without_Brand: string;
  MRP: number;
  Store_Price: number;
  packaging: string;
  secondaryPackaging: string
  finalPackaging: string;
  // Columns for status and logs
  productItemId?: string;
  s3ImageUrls?: string[];
  Status?: ProductRowStatus;
  mnetMasterItemId?: number;
  mnetSyncStatus?: "success" | "failed";
  mnetSyncMessage?: string;
  SuccessImages?: number;
  FailedImages?: number;
  ErrorMessages?: string;
}

// Function to parse Excel and return rows as ProductRow[]
const parseExcel = (filePath: string): ProductRow[] => {
  const workbook = XLSX.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const sheet = workbook.Sheets[sheetName];
  const jsonData: any[] = XLSX.utils.sheet_to_json(sheet, { defval: "" });

  // Validate headers
  const sheetHeaders: string[] = XLSX.utils.sheet_to_json(sheet, { header: 1 })[0] as string[];
  for (const header of expectedHeaders) {
    if (!sheetHeaders.includes(header)) {
      logger.error(`Missing expected header: ${header}`);
      process.exit(1);
    }
  }

  // Add new headers for status and logs if not present
  const newHeaders = ["Status", "SuccessImages", "FailedImages", "ErrorMessages"];
  newHeaders.forEach((header) => {
    if (!sheetHeaders.includes(header)) {
      sheetHeaders.push(header);
    }
  });


  // Map Excel rows to ProductRow interface
  const products: ProductRow[] = jsonData.map((row) => ({
    Product_Display_Name: row["Product Display Name"],
    Brand: row["Brand"],
    Brand_ID: row["Brand ID"],
    Variation_Display_Name: row["Variation Display Name"],
    Variation_ID: row["Variation ID"],
    Images: row["Images"],
    Quantity: Number(row["Quantity"]),
    Unit_of_Measure: row["Unit of Measure"],
    Weight_in_Grams: Number(row["Weight in Grams"]),
    ProductID: row["Product ID"],
    Product_Name_Without_Brand: row["Product Name Without Brand"],
    MRP: Number(row["MRP"]),
    Store_Price: Number(row["Store Price"]),
    packaging: row["Packaging"],
    secondaryPackaging: row["Secondary Packaging"],
    finalPackaging: row["final Packaging"],
  }));

  logger.info(`Parsed ${products.length} products from Excel.`);
  return products;
};

// Function to update Excel sheet with logs
const updateExcel = (filePath: string, products: ProductRow[]): void => {
  const workbook = XLSX.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const sheet = workbook.Sheets[sheetName];

  // Convert sheet to JSON
  const data = XLSX.utils.sheet_to_json(sheet, { defval: "" }) as any[];

  // Update each row with status and logs
  products.forEach((product, index) => {
    data[index]["productItemId"] = product.productItemId;
    data[index]["s3ImageUrls"] = JSON.stringify(product.s3ImageUrls);
    data[index]["Status"] = product.Status;
    data[index]["SuccessImages"] = product.SuccessImages;
    data[index]["FailedImages"] = product.FailedImages;
    data[index]["ErrorMessages"] = product.ErrorMessages;
    data[index]["mnetMasterItemId"] = product.mnetMasterItemId;
    data[index]["mnetSyncStatus"] = product.mnetSyncStatus;
    data[index]["mnetSyncMessage"] = product.mnetSyncMessage;
  });

  // Convert JSON back to sheet
  const newSheet = XLSX.utils.json_to_sheet(data, { header: [...expectedHeaders, "productItemId", "s3ImageUrls", "Status", "SuccessImages", "FailedImages", "ErrorMessages", "mnetMasterItemId", "mnetSyncStatus", "mnetSyncMessage"] });

  // Replace the sheet in workbook
  workbook.Sheets[sheetName] = newSheet;

  // Write back to file
  XLSX.writeFile(workbook, filePath);
  logger.info("Excel sheet updated with upload logs.");
};

// Function to process each product
const processProduct = async (product: ProductRow, baseUrl: string, bucketName: string): Promise<void> => {
  const {
    Variation_ID,
    Images,
    ...otherAttributes
  } = product;

  // Check if the product already exists

  let productItem: ProductItem | null = null;

  const exists = await dynamoDBUtils.exists(Variation_ID);
  if (exists) {
    if (exists.status && exists.status === "success" && exists.mnetSyncStatus === "success") {
      productItem = exists;
      // Update attributes that are different
      product.productItemId = productItem.id;
      product.s3ImageUrls = productItem.s3ImageUrls;
      product.Status = productItem.status;
      product.SuccessImages = productItem.successImages;
      product.FailedImages = productItem.failedImages;
      product.ErrorMessages = productItem.errorMessages?.join("; ");
      product.mnetMasterItemId = productItem.mnetMasterItemId;
      product.mnetSyncStatus = productItem.mnetSyncStatus;
      product.mnetSyncMessage = productItem.mnetSyncMessage
      logger.info(`VariationId ${Variation_ID} already exists with status: ${exists.status} and mnetSyncStatus: ${exists.mnetSyncStatus} and is out of sync. Updating...`);
      return;
    } else {
      productItem = exists;
      // Update attributes that are different
      product.productItemId = productItem.id;
      product.s3ImageUrls = productItem.s3ImageUrls;
      product.Status = productItem.status;
      product.SuccessImages = productItem.successImages;
      product.FailedImages = productItem.failedImages;
      product.ErrorMessages = productItem.errorMessages?.join("; ");
      product.mnetMasterItemId = productItem.mnetMasterItemId;
      product.mnetSyncStatus = productItem.mnetSyncStatus;
      product.mnetSyncMessage = productItem.mnetSyncMessage
      logger.info(`VariationId ${Variation_ID} already exists with status: ${exists.status} and mnetSyncStatus: ${exists.mnetSyncStatus} and is out of sync. Updating...`);
    }
  } else {
    // Prepare item for DynamoDB
    productItem = {
      id: uuidv4(),
      productId: product.ProductID,
      productDisplayName: product.Product_Display_Name,
      brand: product.Brand,
      brandId: product.Brand_ID,
      variationDisplayName: product.Variation_Display_Name,
      variationId: product.Variation_ID,
      imagesStr: product.Images,
      images: [],
      quantity: product.Quantity,
      unitOfMeasure: product.Unit_of_Measure,
      weightInGrams: product.Weight_in_Grams,
      productNameWithoutBrand: product.Product_Name_Without_Brand,
      mrp: product.MRP,
      storePrice: product.Store_Price,
      packaging: product.packaging,
      secondaryPackaging: product.secondaryPackaging,
      finalPackaging: product.finalPackaging,
      s3ImageUrls: [],
    };
  }

  // Process images
  if (productItem.s3ImageUrls.length === 0) {
    const imageNames = Images.split(";").map((img) => img.trim()).filter(Boolean);
    const uploadedImageUrls: string[] = [];
    let imageUploadStatus: ProductRowStatus = "success";
    let imageUploadErrors: string[] = [];
    let successImages = 0;
    let failedImages = 0;

    for (const [index, imageName] of imageNames.entries()) {
      try {
        const extention = imageName.split(".");
        let type = "png";
        if (extention.length > 1) {
          type = extention[1];
        }
        const key = `p/prod/${productItem.id}/${index + 1}${type ? `.${type}` : ""}`;
        const s3Url = await S3Utils.fetchAndUploadImage(baseUrl, imageName, key, bucketName, type);
        uploadedImageUrls.push(s3Url);
        successImages += 1;
      } catch (error: any) {
        imageUploadStatus = imageUploadStatus === "success" ? "partial_success" : imageUploadStatus;
        imageUploadErrors.push(`Image ${imageName}: ${error.message}`);
        failedImages += 1;
        // Continue processing other images
      }
    }

    // update image urls
    productItem.images = imageNames;
    productItem.s3ImageUrls = uploadedImageUrls;
    // Determine final status
    if (imageUploadStatus === "success") {
      product.Status = "success";
    } else if (imageUploadStatus === "partial_success") {
      product.Status = "partial_success";
    } else {
      product.Status = "failed";
    }

    product.productItemId = productItem.id;
    product.s3ImageUrls = uploadedImageUrls;
    product.SuccessImages = successImages;
    product.FailedImages = failedImages;
    product.ErrorMessages = imageUploadErrors.join("; ");

    // Log upload status in DynamoDB

    productItem.status = product.Status
    productItem.successImages = successImages
    productItem.failedImages = failedImages
    productItem.errorMessages = imageUploadErrors
    productItem.timestamp = new Date().toISOString()

    // Insert into DynamoDB
    try {
      await dynamoDBUtils.insert(productItem);
    } catch (error: any) {
      logger.error(`Failed to insert variationId ${Variation_ID}: ${error.message}`);
      imageUploadStatus = "failed";
      imageUploadErrors.push(`DynamoDB Insert: ${error.message}`);
    }
  }



  try {
    if (!productItem.mnetMasterItemId || (productItem.mnetSyncStatus && productItem.mnetSyncStatus === "failed")) {
      const itemDetails: MasterItemDto = {
        name: productItem.variationDisplayName,
        defaultUnit: productItem.unitOfMeasure,
        picture: productItem.s3ImageUrls.join(","),
        source: "swiggy",
        sourceKey: productItem.variationId,
        productId: productItem.productId,
        brandName: productItem.brand,
        defaultWeightFactor: productItem.weightInGrams > 1 ? productItem.weightInGrams/1000 : 1,
        packaging: productItem.finalPackaging,
        mrp: productItem.mrp,
      };
      const response = await mnetApiGatewayService.addMasterItem(itemDetails);
      if (!response.ok) {
        logger.error(`Failed to add Master Item for variationId ${Variation_ID}: ${response.err}`);
        productItem.mnetSyncStatus = "failed";
        productItem.mnetSyncMessage = response.err?.toString();

        product.mnetSyncStatus = "failed";
        product.mnetSyncMessage = response.err?.toString();
      } else {
        productItem.mnetSyncStatus = "success";
        productItem.mnetSyncMessage = "Success";
        productItem.mnetMasterItemId = response.data!.id;

        product.mnetSyncStatus = "success";
        product.mnetSyncMessage = "Success";
        product.mnetMasterItemId = response.data!.id;
        logger.info(`variationId ${productItem.variationId} processed successfully.`);
      }

      await dynamoDBUtils.logUploadStatus(productItem);
    }
  } catch (error: any) {
    logger.error(`Failed to log upload status for variationId ${Variation_ID}: ${error.message}`);
  }
};

// Main Function
const main = async () => {
  const excelPath = EXCEL_FILE_PATH;
  if (!fs.existsSync(excelPath)) {
    logger.error(`Excel file not found at path: ${excelPath}`);
    process.exit(1);
  }

  const products = parseExcel(excelPath);

  // Process each product sequentially or in controlled concurrency
  // For better performance, consider using a concurrency library like p-limit
  for (let i = 0; i < products.length; i++) {
    const product = products[i];
    logger.info(`Processing ${i + 1}/${products.length}: variationId ${product.Variation_ID}`);

    try {
      await processProduct(product, SWIGGY_IMAGE_BASE_URL!, S3_BUCKET_NAME!);
    } catch (error: any) {
      logger.error(`Unhandled error processing variationId ${product.Variation_ID}: ${error.message}`);
      product.Status = "failed";
      product.ErrorMessages = `Unhandled Error: ${error.message}`;
    }
  }

  // Update Excel sheet with logs
  updateExcel(excelPath, products);

  logger.info("Processing completed.");
};

main().catch((error) => {
  logger.error(`Script failed: ${error.message}`);
  process.exit(1);
});
