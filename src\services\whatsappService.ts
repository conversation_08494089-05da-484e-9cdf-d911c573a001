// whatsappService.ts
import axios, { AxiosError } from 'axios';
import { 
    WhatsAppMessageData, 
    WhatsAppTemplateMessageData, 
    WhatsAppTemplate, 
    ComponentParameter, 
    ButtonComponent,
    FacebookTokenRefreshResponse,
    FacebookTokenRevokeResponse
} from "@/types/whatsapp.js";
import { NotificationLogService } from './notificationLogService.js';
import { NotificationStatus, NotificationLog, CampaignType, MessageCategory, CustomerSegment } from '../database/entities/NotificationLog.js';
import { CampaignNotificationRequest } from '../types/notification.types.js';
import crypto from 'crypto';

/* ====================================================================
   📝 ENHANCED WHATSAPP MESSAGING FUNCTIONS
   
   These functions now support both regular and campaign messaging:
   
   🔹 REGULAR MESSAGE USAGE:
   await sendWhatsAppMessage({
       phoneNo: "+1234567890",
       templateName: "order_confirmation", 
       templateValues: ["John", "12345"],
       businessId: "business123"
   });
   
   🔹 CAMPAIGN MESSAGE USAGE:
   await sendWhatsAppMessage({
       phoneNo: "+1234567890",
       templateName: "promotional_offer",
       templateValues: ["John", "20% OFF"],
       businessId: "business123",
       // 🆕 Campaign parameters
       campaignId: "summer_sale_2024",
       campaignName: "Summer Sale Campaign",
       campaignType: CampaignType.PROMOTIONAL,
       messageCategory: MessageCategory.PROMOTIONAL_OFFER,
       customerSegment: CustomerSegment.HIGH_VALUE,
       tags: ["summer", "sale", "premium_customers"]
   });
   
   📊 Enhanced return includes:
   - whatsappMessageId: For webhook tracking
   - notificationId: For analytics queries  
   - isCampaignMessage: Boolean flag for campaign detection
   
   🎯 Benefits:
   - Backward compatible with existing code
   - Campaign analytics when campaign data provided
   - Intelligent logging (campaign vs regular)
   - Full webhook integration support
==================================================================== */

const DEFAULT_PHONE_ID = "432313896624537";
export const mNET_PHONE_ID = "499359123260177";

// Helper function to remove undefined values from objects
const removeUndefined = (obj: any): any => {
    if (Array.isArray(obj)) {
        return obj.map(removeUndefined).filter(item => item !== undefined);
    }
    if (obj !== null && typeof obj === 'object') {
        return Object.fromEntries(
            Object.entries(obj)
                .filter(([_, v]) => v !== undefined)
                .map(([k, v]) => [k, removeUndefined(v)])
        );
    }
    return obj;
};

export const sendWhatsAppMessage = async (messageData: WhatsAppMessageData & {
    // 🆕 Optional campaign parameters
    campaignId?: string;
    campaignName?: string;
    campaignType?: CampaignType;
    messageCategory?: MessageCategory;
    customerSegment?: CustomerSegment;
    tags?: string[];
}) => {
    const phoneNumberId = messageData.phoneNumberId || mNET_PHONE_ID;
    const notificationLogService = new NotificationLogService();
    let logEntry: NotificationLog | null = null;

    try {
        const providerRequest = {
            messaging_product: "whatsapp",
            to: messageData.phoneNo,
            type: "template",
            template: {
                name: messageData.templateName,
                language: { code: messageData.lang ? messageData.lang : "en" },
                components: [
                    {
                        type: "body",
                        parameters: messageData.templateValues.map(value => ({
                            type: "text",
                            text: value
                        }))
                    }
                ]
            }
        };

        // 🆕 Intelligent logging: Use campaign logging if campaign data is provided
        if (messageData.campaignId) {
            // Log as campaign notification
            logEntry = await notificationLogService.logCampaignNotification({
                businessId: messageData.businessId || phoneNumberId,
                campaignId: messageData.campaignId,
                campaignName: messageData.campaignName || messageData.campaignId,
                campaignType: messageData.campaignType || CampaignType.MARKETING,
                messageCategory: messageData.messageCategory || MessageCategory.GENERAL,
                customerSegment: messageData.customerSegment || CustomerSegment.GENERAL,
                tags: messageData.tags || [],
                recipients: [messageData.phoneNo],
                templateName: messageData.templateName,
                templateValues: messageData.templateValues,
                recipient: messageData.phoneNo,
                providerRequest,
                status: NotificationStatus.PENDING
            });
        } else {
            // Log as regular notification
            logEntry = await notificationLogService.logWhatsAppNotification(
                messageData.businessId || phoneNumberId,
                messageData.phoneNo,
                phoneNumberId,
                messageData,
                providerRequest,
                null,
                NotificationStatus.PENDING,
                '' // Empty string for initial pending status
            );
        }

        const response = await axios.post(
            `https://graph.facebook.com/v20.0/${phoneNumberId}/messages`,
            providerRequest,
            {
                headers: {
                    'Authorization': `Bearer ${messageData.accessToken || process.env.WHATSAPP_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        // 🆕 Extract WhatsApp message ID from response
        const whatsappMessageId = response.data?.messages?.[0]?.id;

        // Update log with success response and WhatsApp message ID
        if (logEntry) {
            await notificationLogService.updateNotificationStatus(
                logEntry.notificationId,
                logEntry.timestamp,
                NotificationStatus.SENT,
                response.data,
                '', // Empty string for success case
                whatsappMessageId // 🆕 Pass WhatsApp message ID for tracking
            );
        }

        return { 
            success: true, 
            message: 'WhatsApp message sent successfully', 
            details: response.data,
            whatsappMessageId, // 🆕 Return message ID for caller
            notificationId: logEntry?.notificationId, // 🆕 Return notification ID
            isCampaignMessage: !!messageData.campaignId // 🆕 Indicate if this was a campaign message
        };
    } catch (error: any) {
        // Log the error response
        if (logEntry && error instanceof AxiosError) {
            const errorMessage = error.response?.data?.error?.message || error.message || 'Unknown error occurred';
            await notificationLogService.updateNotificationStatus(
                logEntry.notificationId,
                logEntry.timestamp,
                NotificationStatus.FAILED,
                error.response?.data,
                errorMessage,
                undefined // No WhatsApp message ID in error case
            );
        }

        console.error('Error sending WhatsApp message:', error.response ? error.response.data : error.message);
        throw {
            success: false,
            message: 'Failed to send WhatsApp message',
            error: error.response ? error.response.data : error.message,
            notificationId: logEntry?.notificationId
        };
    }
};

export const sendWhatsAppTemplateMessage = async (messageData: WhatsAppTemplateMessageData & {
    // 🆕 Optional campaign parameters
    campaignId?: string;
    campaignName?: string;
    campaignType?: CampaignType;
    messageCategory?: MessageCategory;
    customerSegment?: CustomerSegment;
    tags?: string[];
}) => {
    const phoneNumberId = messageData.wabPhoneNumberId || mNET_PHONE_ID;
    const notificationLogService = new NotificationLogService();
    let logEntry: NotificationLog | null = null;

    try {
        const providerRequest = removeUndefined({
            messaging_product: "whatsapp",
            recipient_type: "individual",
            to: messageData.targetPhoneNumber,
            type: "template",
            template: {
                name: messageData.template.name,
                language: messageData.template.language,
                components: messageData.template.components
            }
        });

        // Clean the message data before logging
        const cleanMessageData = removeUndefined(messageData);

        // 🆕 Intelligent logging: Use campaign logging if campaign data is provided
        if (messageData.campaignId) {
            // Log as campaign notification
            logEntry = await notificationLogService.logCampaignNotification({
                businessId: messageData.businessId || phoneNumberId,
                campaignId: messageData.campaignId,
                campaignName: messageData.campaignName || messageData.campaignId,
                campaignType: messageData.campaignType || CampaignType.MARKETING,
                messageCategory: messageData.messageCategory || MessageCategory.GENERAL,
                customerSegment: messageData.customerSegment || CustomerSegment.GENERAL,
                tags: messageData.tags || [],
                recipients: [messageData.targetPhoneNumber],
                templateName: messageData.template.name,
                messageContent: JSON.stringify(messageData.template),
                recipient: messageData.targetPhoneNumber,
                providerRequest,
                status: NotificationStatus.PENDING
            });
        } else {
            // Log as regular notification
            logEntry = await notificationLogService.logWhatsAppNotification(
                messageData.businessId || phoneNumberId,
                messageData.targetPhoneNumber,
                phoneNumberId,
                cleanMessageData,
                providerRequest,
                null,
                NotificationStatus.PENDING,
                '' // Empty string for initial pending status
            );
        }

        const response = await axios.post(
            `https://graph.facebook.com/v20.0/${phoneNumberId}/messages`,
            providerRequest,
            {
                headers: {
                    'Authorization': `Bearer ${messageData.accessToken || process.env.WHATSAPP_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        // Clean the response data before logging
        const cleanResponse = removeUndefined(response.data);

        // 🆕 Extract WhatsApp message ID from response
        const whatsappMessageId = response.data?.messages?.[0]?.id;

        // Update log with success response and WhatsApp message ID
        if (logEntry) {
            await notificationLogService.updateNotificationStatus(
                logEntry.notificationId,
                logEntry.timestamp,
                NotificationStatus.SENT,
                cleanResponse,
                '', // Empty string for success case
                whatsappMessageId // 🆕 Pass WhatsApp message ID for tracking
            );
        }

        return { 
            success: true, 
            message: 'WhatsApp template message sent successfully', 
            details: response.data,
            whatsappMessageId, // 🆕 Return message ID for caller
            notificationId: logEntry?.notificationId, // 🆕 Return notification ID
            isCampaignMessage: !!messageData.campaignId // 🆕 Indicate if this was a campaign message
        };
    } catch (error: any) {
        // Log the error response
        if (logEntry && error instanceof AxiosError) {
            const errorMessage = error.response?.data?.error?.message || error.message || 'Unknown error occurred';
            const cleanErrorResponse = error.response?.data ? removeUndefined(error.response.data) : null;
            
            await notificationLogService.updateNotificationStatus(
                logEntry.notificationId,
                logEntry.timestamp,
                NotificationStatus.FAILED,
                cleanErrorResponse,
                errorMessage,
                undefined // No WhatsApp message ID in error case
            );
        }

        console.error('Error sending WhatsApp template message:', error.response ? error.response.data : error.message);
        throw {
            success: false,
            message: 'Failed to send WhatsApp template message',
            error: error.response ? error.response.data : error.message,
            notificationId: logEntry?.notificationId
        };
    }
};

// 🆕 New campaign message sending function
export const sendCampaignMessage = async (campaignRequest: CampaignNotificationRequest & {
    accessToken?: string;
    wabPhoneNumberId?: string;
}): Promise<{
    success: boolean;
    message: string;
    details: any;
    whatsappMessageId?: string;
    notificationId?: string;
}> => {
    const phoneNumberId = campaignRequest.wabPhoneNumberId || mNET_PHONE_ID;
    const notificationLogService = new NotificationLogService();
    let logEntry: NotificationLog | null = null;

    try {
        // Build the WhatsApp message request
        const providerRequest = removeUndefined({
            messaging_product: "whatsapp",
            recipient_type: "individual",
            to: campaignRequest.recipients[0], // Assuming single recipient for now
            type: "template",
            template: campaignRequest.templateName ? {
                name: campaignRequest.templateName,
                language: { code: "en" },
                components: [
                    {
                        type: "body",
                        parameters: (campaignRequest.templateValues || []).map(value => ({
                            type: "text",
                            text: value
                        }))
                    }
                ]
            } : undefined
        });

        // Create initial campaign notification log
        logEntry = await notificationLogService.logCampaignNotification({
            ...campaignRequest,
            recipient: campaignRequest.recipients[0],
            providerRequest,
            status: NotificationStatus.PENDING
        });

        // Send the message
        const response = await axios.post(
            `https://graph.facebook.com/v20.0/${phoneNumberId}/messages`,
            providerRequest,
            {
                headers: {
                    'Authorization': `Bearer ${campaignRequest.accessToken || process.env.WHATSAPP_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        const cleanResponse = removeUndefined(response.data);
        const whatsappMessageId = response.data?.messages?.[0]?.id;

        // Update log with success response
        if (logEntry) {
            await notificationLogService.updateNotificationStatus(
                logEntry.notificationId,
                logEntry.timestamp,
                NotificationStatus.SENT,
               cleanResponse,
                '',
                whatsappMessageId
            );
        }

        return {
            success: true,
            message: 'Campaign message sent successfully',
            details: response.data,
            whatsappMessageId,
            notificationId: logEntry?.notificationId
        };

    } catch (error: any) {
        // Log the error response
        if (logEntry && error instanceof AxiosError) {
            const errorMessage = error.response?.data?.error?.message || error.message || 'Unknown error occurred';
            const cleanErrorResponse = error.response?.data ? removeUndefined(error.response.data) : null;
            
            await notificationLogService.updateNotificationStatus(
                logEntry.notificationId,
                logEntry.timestamp,
                NotificationStatus.FAILED,
                cleanErrorResponse,
                errorMessage,
                undefined // No WhatsApp message ID in error case
            );
        }

        console.error('Error sending campaign message:', error.response ? error.response.data : error.message);
        throw {
            success: false,
            message: 'Failed to send campaign message',
            error: error.response ? error.response.data : error.message,
            notificationId: logEntry?.notificationId
        };
    }
};

// 🆕 Batch campaign message sending
export const sendBatchCampaignMessages = async (
    campaignRequest: CampaignNotificationRequest & { accessToken?: string; wabPhoneNumberId?: string },
    batchSize: number = 10
): Promise<{
    success: boolean;
    totalMessages: number;
    sentCount: number;
    failedCount: number;
    results: Array<{
        recipient: string;
        success: boolean;
        whatsappMessageId?: string;
        notificationId?: string;
        error?: string;
    }>;
}> => {
    const results: Array<{
        recipient: string;
        success: boolean;
        whatsappMessageId?: string;
        notificationId?: string;
        error?: string;
    }> = [];

    let sentCount = 0;
    let failedCount = 0;

    console.log(`📤 Starting batch campaign: ${campaignRequest.campaignName} for ${campaignRequest.recipients.length} recipients`);

    // Process recipients in batches
    for (let i = 0; i < campaignRequest.recipients.length; i += batchSize) {
        const batch = campaignRequest.recipients.slice(i, i + batchSize);
        
        // Process batch in parallel
        const batchPromises = batch.map(async (recipient) => {
            try {
                const singleRequest = {
                    ...campaignRequest,
                    recipients: [recipient]
                };
                
                const result = await sendCampaignMessage(singleRequest);
                
                sentCount++;
                return {
                    recipient,
                    success: true,
                    whatsappMessageId: result.whatsappMessageId,
                    notificationId: result.notificationId
                };
            } catch (error: any) {
                failedCount++;
                return {
                    recipient,
                    success: false,
                    error: error.message || 'Failed to send message'
                };
            }
        });

        const batchResults = await Promise.allSettled(batchPromises);
        
        batchResults.forEach((result) => {
            if (result.status === 'fulfilled') {
                results.push(result.value);
            } else {
                failedCount++;
                results.push({
                    recipient: 'unknown',
                    success: false,
                    error: result.reason?.message || 'Unknown error'
                });
            }
        });

        // Add delay between batches to respect rate limits
        if (i + batchSize < campaignRequest.recipients.length) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
        }
    }

    console.log(`✅ Batch campaign completed: ${sentCount} sent, ${failedCount} failed`);

    return {
        success: sentCount > 0,
        totalMessages: campaignRequest.recipients.length,
        sentCount,
        failedCount,
        results
    };
};

export const getWhatsAppTemplates = async () => {
    try {
        const response = await axios.get(
            "https://graph.facebook.com/v20.0/432313896624537/message_templates",
            {
                params: {
                    fields: "name,status,language,category,components",
                    limit: 100  // adjust this number based on how many templates you want to fetch
                },
                headers: {
                    'Authorization': `Bearer ${process.env.WHATSAPP_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        return { success: true, message: 'Templates fetched successfully', data: response.data };
    } catch (error: any) {
        console.log("Failed to get templates", error.response?.data || error.message);
        throw {
            success: false,
            message: 'Failed to get templates',
            error: error.response ? error.response.data : error.message
        };
    }
};

// Helper function to create a template message with header image
export const createImageHeaderTemplate = (
    templateName: string, 
    language: string, 
    imageUrl: string, 
    bodyParams: ComponentParameter[] = [],
    buttons?: ButtonComponent[]
): WhatsAppTemplate => {
    return {
        name: templateName,
        language: { code: language },
        components: [
            {
                type: 'header',
                parameters: [
                    {
                        type: 'image',
                        image: {
                            link: imageUrl
                        }
                    }
                ]
            },
            {
                type: 'body',
                parameters: bodyParams
            },
            ...(buttons || [])
        ]
    };
};

// Helper function to create quick reply buttons
export const createQuickReplyButton = (
    index: number,
    payload: string
): ButtonComponent => {
    return {
        type: 'button',
        sub_type: 'quick_reply',
        index: index.toString(),
        parameters: [
            {
                type: 'payload',
                payload
            }
        ]
    };
};


// Whatsapp Service Not in use

export class WhatsappService {
    private notificationLogService: NotificationLogService;
    private metaApiUrl: string;
    private accessToken: string;

    constructor() {
        this.notificationLogService = new NotificationLogService();
        this.metaApiUrl = process.env.META_API_URL || 'https://graph.facebook.com/v17.0';
        this.accessToken = process.env.META_ACCESS_TOKEN || '';
    }

    async sendMessage(businessId: string, phoneNumber: string, messagePayload: any): Promise<any> {
        let logEntry: NotificationLog | null = null;
        
        try {
            // Prepare the request for Meta API
            const providerRequest = {
                messaging_product: "whatsapp",
                recipient_type: "individual",
                to: phoneNumber,
                ...messagePayload
            };

            // Create initial log entry with PENDING status
            logEntry = await this.notificationLogService.logWhatsAppNotification(
                businessId,
                phoneNumber,
                phoneNumber,
                messagePayload,
                providerRequest,
                null,
                NotificationStatus.PENDING
            );

            // Send message to WhatsApp
            const response = await axios.post(
                `${this.metaApiUrl}/messages`,
                providerRequest,
                {
                    headers: {
                        'Authorization': `Bearer ${this.accessToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            // 🆕 Extract WhatsApp message ID and update log
            const whatsappMessageId = response.data?.messages?.[0]?.id;
            await this.notificationLogService.updateNotificationStatus(
                logEntry.notificationId,
                logEntry.timestamp,
                NotificationStatus.SENT,
               response.data,
                '', // Empty string for success case
                whatsappMessageId // 🆕 Pass WhatsApp message ID for tracking
            );

            return response.data;
        } catch (error) {
            // Log the error response
            if (logEntry && error instanceof AxiosError) {
                await this.notificationLogService.updateNotificationStatus(
                    logEntry.notificationId,
                    logEntry.timestamp,
                    NotificationStatus.FAILED,
                    error.response?.data,
                    error.message,
                    undefined // No WhatsApp message ID in error case
                );
            }
            throw error;
        }
    }

    async handleWebhook(payload: any): Promise<void> {
        try {
            const entries = payload.entry || [];
            for (const entry of entries) {
                const changes = entry.changes || [];
                for (const change of changes) {
                    if (change.value?.messages) {
                        for (const message of change.value.messages) {
                            // Handle message status updates
                            if (message.status) {
                                const { id, status, recipient_id } = message;
                                // Note: You'll need to store the mapping of WhatsApp message ID to your notification ID
                                // This could be done by storing the WhatsApp message ID in the providerResponse
                                const notificationLog = await this.findNotificationByWhatsAppMessageId(id);
                                
                                if (notificationLog) {
                                    await this.notificationLogService.updateNotificationStatus(
                                        notificationLog.notificationId,
                                        notificationLog.timestamp,
                                        this.mapWhatsAppStatusToNotificationStatus(status),
                                        message,
                                        undefined, // No error message in webhook status updates
                                        id // Pass the WhatsApp message ID
                                    );
                                }
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error processing WhatsApp webhook:', error);
            throw error;
        }
    }

    private mapWhatsAppStatusToNotificationStatus(whatsappStatus: string): NotificationStatus {
        switch (whatsappStatus) {
            case 'sent':
                return NotificationStatus.SENT;
            case 'delivered':
                return NotificationStatus.DELIVERED;
            case 'failed':
                return NotificationStatus.FAILED;
            default:
                return NotificationStatus.PENDING;
        }
    }

    private async findNotificationByWhatsAppMessageId(whatsappMessageId: string): Promise<NotificationLog | null> {
        // Implementation to find notification log by WhatsApp message ID
        // This would require additional database queries or maintaining a separate mapping
        // You might want to store this mapping in Redis or another fast lookup store
        return null;
    }
}

// ==================== FACEBOOK TOKEN ROTATION API METHODS ====================

/**
 * Generate appsecret_proof for Facebook API calls
 * @param accessToken The access token to generate proof for
 * @param appSecret The Facebook app secret
 * @returns HMAC SHA256 hash of the access token
 */
export const generateAppSecretProof = (accessToken: string, appSecret: string): string => {
    return crypto
        .createHmac('sha256', appSecret)
        .update(accessToken)
        .digest('hex');
};

/**
 * Refresh a Facebook system user access token with retry logic
 * @param currentToken The current access token to refresh
 * @param appId Facebook App ID
 * @param appSecret Facebook App Secret
 * @param graphApiVersion Graph API version (default: v21.0)
 * @returns New token response
 */
export const refreshFacebookSystemUserToken = async (
    currentToken: string,
    appId: string,
    appSecret: string,
    graphApiVersion: string = 'v21.0'
): Promise<FacebookTokenRefreshResponse> => {
    const maxRetries = 3;
    const retryDelay = 2000; // 2 seconds
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const appSecretProof = generateAppSecretProof(currentToken, appSecret);
            
            const refreshUrl = new URL(`https://graph.facebook.com/${graphApiVersion}/oauth/access_token`);
            refreshUrl.searchParams.set('grant_type', 'fb_exchange_token');
            refreshUrl.searchParams.set('client_id', appId);
            refreshUrl.searchParams.set('client_secret', appSecret);
            refreshUrl.searchParams.set('set_token_expires_in_60_days', 'true');
            refreshUrl.searchParams.set('fb_exchange_token', currentToken);

            console.log(`🔄 Refreshing Facebook system user access token (attempt ${attempt}/${maxRetries})`);

            const response = await axios.get(refreshUrl.toString(), {
                timeout: 30000, // 30 second timeout
            });

            if (!response.data || !response.data.access_token) {
                throw new Error('Invalid token response from Facebook API');
            }

            const tokenData: FacebookTokenRefreshResponse = response.data;
            
            // Validate token response
            if (!tokenData.access_token || tokenData.access_token.length < 10) {
                throw new Error('Invalid token received from Facebook API');
            }

            console.log('✅ Token refreshed successfully', {
                expiresIn: tokenData.expires_in,
                tokenType: tokenData.token_type,
                newTokenPrefix: tokenData.access_token.substring(0, 10) + '...'
            });

            return tokenData;
        } catch (error) {
            lastError = error as Error;
            console.warn(`⚠️ Token refresh attempt ${attempt} failed:`, error);

            if (attempt < maxRetries) {
                console.log(`⏳ Retrying in ${retryDelay}ms...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay * attempt)); // Exponential backoff
            }
        }
    }

    throw lastError || new Error('Token refresh failed after all retry attempts');
};

/**
 * Revoke a Facebook system user access token
 * @param tokenToRevoke The token to revoke
 * @param accessTokenForAuth The access token used for authentication
 * @param appId Facebook App ID
 * @param appSecret Facebook App Secret
 * @param graphApiVersion Graph API version (default: v21.0)
 * @returns Revocation response
 */
export const revokeFacebookSystemUserToken = async (
    tokenToRevoke: string,
    accessTokenForAuth: string,
    appId: string,
    appSecret: string,
    graphApiVersion: string = 'v21.0'
): Promise<FacebookTokenRevokeResponse> => {
    try {
        const revokeUrl = new URL(`https://graph.facebook.com/${graphApiVersion}/oauth/revoke`);
        revokeUrl.searchParams.set('client_id', appId);
        revokeUrl.searchParams.set('client_secret', appSecret);
        revokeUrl.searchParams.set('revoke_token', tokenToRevoke);
        revokeUrl.searchParams.set('access_token', accessTokenForAuth);

        console.log('🗑️ Revoking Facebook system user access token');

        const response = await axios.get(revokeUrl.toString(), {
            timeout: 15000, // 15 second timeout
        });

        if (!response.data) {
            throw new Error('Invalid response from Facebook API');
        }

        const revokeData: FacebookTokenRevokeResponse = response.data;
        
        console.log('✅ Token revoked successfully');
        return revokeData;
    } catch (error) {
        console.error('❌ Error revoking token:', error);
        throw error;
    }
};

