import { error } from "console";
import {
  AgentSalesInfo,
  BcBuyerDto,
  SalesData,
  SellerSalesInfo,
} from "~/types/api/businessConsoleService/salesinfo";
import { API_BASE_URL, apiRequest } from "~/utils/api";
import { ApiResponse } from "~/types/api/Api";

export async function getSalesData(
  pageNo: number,
  pageSize: number,
  type: string,
  startDate: string,
  endDate: string,

  request: Request,
  sellerId?: number,
  agentId?: number,
  areaId?: number
): Promise<ApiResponse<SalesData[]>> {
  try {
    // Making the API request

    const baseUrl = `${API_BASE_URL}/bc/mnetmanager/salesdata/${type}/date/${startDate}/${endDate}`;

    // Query parameters with dynamic filtering
    const queryParams = new URLSearchParams({
      ...(pageNo !== undefined && pageNo !== null
        ? { pageNo: pageNo.toString() }
        : {}),
      ...(pageSize !== undefined && pageSize !== null
        ? { size: pageSize.toString() }
        : {}),
      ...(sellerId ? { sellerId: sellerId.toString() } : {}),
      ...(agentId !== undefined && agentId !== null
        ? { agentId: agentId.toString() }
        : {}),
      ...(areaId ? { areaId: areaId.toString() } : {}),
    });

    // Final URL with query parameters
    const finalUrl = queryParams.toString()
      ? `${baseUrl}?${queryParams.toString()}`
      : baseUrl;
    const response = await apiRequest<SalesData[]>(
      finalUrl,
      "GET",
      undefined,
      {},
      true,
      request
    );

    // Check if response is undefined or has errors
    if (!response) {
      throw new Error("Response is undefined");
    }

    // Return the successful response
    return response;
  } catch (error) {
    // If an error occurs, throw with specific message
    console.error("Error fetching sales data:", error);

    // Optionally, add more sophisticated error handling depending on the error
    throw new Response("Failed to fetch sales data", {
      status: 500,
      statusText: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
export async function getPrivateSellerSales(
  pageNo: number,
  pageSize: number,
  type: string,
  startDate: string,
  endDate: string,
  request: Request,
  sellerId?: number,
  agentId?: number,
  areaId?: number
): Promise<ApiResponse<SalesData[]>> {
  try {
    // Making the API request

    const baseUrl = `${API_BASE_URL}/bc/seller/salesdata/${type}/date/${startDate}/${endDate}`;

    // Query parameters with dynamic filtering
    const queryParams = new URLSearchParams({
      ...(pageNo !== undefined && pageNo !== null
        ? { pageNo: pageNo.toString() }
        : {}),
      ...(pageSize !== undefined && pageSize !== null
        ? { size: pageSize.toString() }
        : {}),
      ...(sellerId ? { sellerId: sellerId.toString() } : {}),
      ...(agentId !== undefined && agentId !== null
        ? { agentId: agentId.toString() }
        : {}),
      ...(areaId ? { areaId: areaId.toString() } : {}),
    });

    // Final URL with query parameters
    const finalUrl = queryParams.toString()
      ? `${baseUrl}?${queryParams.toString()}`
      : baseUrl;
    const response = await apiRequest<SalesData[]>(
      finalUrl,
      "GET",
      undefined,
      {},
      true,
      request
    );

    // Check if response is undefined or has errors
    if (!response) {
      throw new Error("Response is undefined");
    }

    // Return the successful response
    return response;
  } catch (error) {
    // If an error occurs, throw with specific message
    console.error("Error fetching sales data:", error);

    // Optionally, add more sophisticated error handling depending on the error
    throw new Response("Failed to fetch sales data", {
      status: 500,
      statusText: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
export async function getBuyerData(
  pageNo: number,
  pageSize: number,
  searchBy: string,
  request: Request
): Promise<ApiResponse<SalesData[]>> {
  try {
    // Base API URL
    const baseUrl = `${API_BASE_URL}/bc/mnetadmin/buyers`;

    // Constructing the dynamic URL
    let finalUrl = `${baseUrl}/page/${pageNo}/pagesize/${pageSize}`;

    // Query parameters for sorting and searching
    const queryParams = new URLSearchParams();

    // queryParams.append("sortBy", "name"); // Assuming default sorting by name
    // queryParams.append("sortByOrder", "asc"); // Default ascending order

    if (searchBy && searchBy !== null) {
      queryParams.append("searchBy", encodeURIComponent(searchBy.trim()));
    }

    // Append query parameters if present
    if (queryParams.toString()) {
      finalUrl += `?${queryParams.toString()}`;
    }

    // Making the API request
    const response = await apiRequest<SalesData[]>(
      finalUrl,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (!response) {
      throw new Error("Response is undefined");
    }

    return response;
  } catch (error) {
    console.error("Error fetching buyer data:", error);
    throw new Response("Failed to fetch buyer data", {
      status: 500,
      statusText: error instanceof Error ? error.message : "Unknown error",
    });
  }
}

export async function updateBuyerAttribute(
  buyerId: number,
  attributeTye: string,
  value: any,
  request?: Request
): Promise<ApiResponse<BcBuyerDto>> {
  const response = await apiRequest<BcBuyerDto>(
    `${API_BASE_URL}/bc/mnetadmin/buyer/${buyerId}/attr/${attributeTye}/${value}`,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to create ContractPrice");
  }
}

export async function getSelectedSales(
  pageNo: number,
  pageSize: number,
  type: string,
  startDate: string,
  selectedId: string,
  endDate: string,
  request: Request
): Promise<ApiResponse<SalesData[]>> {
  try {
    // Making the API request
    const response = await apiRequest<SalesData[]>(
      // const url = type ==="AgentWise"?`${API_BASE_URL}/bc/mnetmanager/salesdata/${type}/date/${startDate}/${endDate}?pageNo=${pageNo}&size=${pageSize}`: type ==="SellerWise"?
      // `${API_BASE_URL}/bc/mnetmanager/salesdata/${type}/date/${startDate}/${endDate}?pageNo=${pageNo}&size=${pageSize}`:type ==="LocalityWise" ?`${API_BASE_URL}/bc/mnetmanager/salesdata/${type}/date/${startDate}/${endDate}?pageNo=${pageNo}&size=${pageSize}`:""

      `${API_BASE_URL}/bc/mnetmanager/salesdata/${type}/date/${startDate}/${endDate}?pageNo=${pageNo}&size=${pageSize}`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    // Check if response is undefined or has errors
    if (!response) {
      throw new Error("Response is undefined");
    }

    // Return the successful response
    return response;
  } catch (error) {
    // If an error occurs, throw with specific message
    console.error("Error fetching sales data:", error);

    // Optionally, add more sophisticated error handling depending on the error
    throw new Response("Failed to fetch sales data", {
      status: 500,
      statusText: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
