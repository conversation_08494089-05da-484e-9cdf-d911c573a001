import { json, LoaderFunction } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { error } from "console";
import { useState } from "react";
import { Input } from "~/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { getMasterItemCategory, getNetWorkItemCategories, getSellerItemCategories } from "~/services/masterItemCategories";
import { SellerItemCategories } from "~/types/api/businessConsoleService/MasterItemCategory";
import { withAuth, withResponse } from "~/utils/auth-utils";



export const loader = withAuth(async ({ user, request }) => {

      const networkId = 6;

      try {
            const SellerItemCategoryResponse = await getSellerItemCategories(networkId, request);
            return withResponse({ data: SellerItemCategoryResponse.data }, SellerItemCategoryResponse.headers)
      }
      catch (error) {
            if (error instanceof Response && error.status === 404) {
                  throw json({ error: "MasterItemCategory pg Not found" }, { status: 404 });
            }
            throw new Response("Failed to fetch MasterItemCategory ", { status: 500 });
      }

})


export default function SellerItemCategory() {

      const categories = useLoaderData<{ data: SellerItemCategories[] }>()
      const [searchTerm, setSearchTerm] = useState('')

      return (
            <div className="container mx-auto p-6">
                  <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold">Seller Item Category</h1>
                  </div>

                  <div className="flex justify-between mb-4">
                        <Input
                              placeholder="Search by Item Name"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"
                        />
                  </div>
                  <Table>
                        <TableHeader>
                              <TableRow>
                                    <TableHead className="cursor-pointer" >Item CategoryId </TableHead>
                                    <TableHead className="cursor-pointer" >SICId</TableHead>
                                    <TableHead className="cursor-pointer" > Image</TableHead>
                                    <TableHead className="cursor-pointer" > Name </TableHead>
                                    <TableHead className="cursor-pointer" >picturex </TableHead>
                                    <TableHead className="cursor-pointer" >picturexx </TableHead>
                                    <TableHead className="cursor-pointer" >level </TableHead>

                              </TableRow>
                        </TableHeader>
                        <TableBody>
                              {categories.data?.filter((x) => x.name.toLowerCase().includes(searchTerm.toLowerCase())).sort((a, b) => a.name.localeCompare(b.name)).map((item) => {


                                    return (
                                          <TableRow key={item.sICId}>
                                                <TableCell>{item.itemCategoryId}</TableCell>
                                                <TableCell>{item.sICId}</TableCell>

                                                <TableCell> <img src={item?.picture}
                                                      alt="ItemImage"
                                                      className="h-10 w-10" /></TableCell>
                                                <TableCell>{item.name}</TableCell>
                                                <TableCell> <img src={item?.picturex}
                                                      alt="ItemImage"
                                                      className="h-10 w-10" /></TableCell>
                                                <TableCell> <img src={item?.picturexx}
                                                      alt="ItemImage"
                                                      className="h-10 w-10" /></TableCell>

                                                <TableCell>{item.level}</TableCell>

                                          </TableRow>
                                    )
                              })}
                        </TableBody>
                  </Table>


            </div>
      )



}