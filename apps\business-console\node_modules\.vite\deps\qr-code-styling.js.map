{"version": 3, "sources": ["../../qr-code-styling/lib/webpack:/QRCodeStyling/webpack/universalModuleDefinition", "../../qr-code-styling/lib/webpack:/QRCodeStyling/node_modules/qrcode-generator/qrcode.js", "../../qr-code-styling/lib/webpack:/QRCodeStyling/webpack/bootstrap", "../../qr-code-styling/lib/webpack:/QRCodeStyling/webpack/runtime/compat get default export", "../../qr-code-styling/lib/webpack:/QRCodeStyling/webpack/runtime/define property getters", "../../qr-code-styling/lib/webpack:/QRCodeStyling/webpack/runtime/hasOwnProperty shorthand", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/constants/modes.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/tools/merge.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/tools/downloadURI.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/constants/errorCorrectionPercents.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/figures/dot/QRDot.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/constants/dotTypes.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/constants/cornerSquareTypes.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/figures/cornerSquare/QRCornerSquare.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/constants/cornerDotTypes.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/figures/cornerDot/QRCornerDot.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/constants/gradientTypes.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/constants/shapeTypes.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/core/QRSVG.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/tools/calculateImageSize.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/tools/toDataUrl.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/constants/drawTypes.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/constants/qrTypes.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/core/QROptions.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/constants/errorCorrectionLevels.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/tools/sanitizeOptions.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/tools/getMimeType.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/core/QRCodeStyling.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/tools/getMode.ts", "../../qr-code-styling/lib/webpack:/QRCodeStyling/src/index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"QRCodeStyling\"] = factory();\n\telse\n\t\troot[\"QRCodeStyling\"] = factory();\n})(this, () => {\nreturn ", "//---------------------------------------------------------------------\n//\n// QR Code Generator for JavaScript\n//\n// Copyright (c) 2009 <PERSON><PERSON><PERSON>\n//\n// URL: http://www.d-project.com/\n//\n// Licensed under the MIT license:\n//  http://www.opensource.org/licenses/mit-license.php\n//\n// The word 'QR Code' is registered trademark of\n// DENSO WAVE INCORPORATED\n//  http://www.denso-wave.com/qrcode/faqpatent-e.html\n//\n//---------------------------------------------------------------------\n\nvar qrcode = function() {\n\n  //---------------------------------------------------------------------\n  // qrcode\n  //---------------------------------------------------------------------\n\n  /**\n   * qrcode\n   * @param typeNumber 1 to 40\n   * @param errorCorrectionLevel 'L','M','Q','H'\n   */\n  var qrcode = function(typeNumber, errorCorrectionLevel) {\n\n    var PAD0 = 0xEC;\n    var PAD1 = 0x11;\n\n    var _typeNumber = typeNumber;\n    var _errorCorrectionLevel = QRErrorCorrectionLevel[errorCorrectionLevel];\n    var _modules = null;\n    var _moduleCount = 0;\n    var _dataCache = null;\n    var _dataList = [];\n\n    var _this = {};\n\n    var makeImpl = function(test, maskPattern) {\n\n      _moduleCount = _typeNumber * 4 + 17;\n      _modules = function(moduleCount) {\n        var modules = new Array(moduleCount);\n        for (var row = 0; row < moduleCount; row += 1) {\n          modules[row] = new Array(moduleCount);\n          for (var col = 0; col < moduleCount; col += 1) {\n            modules[row][col] = null;\n          }\n        }\n        return modules;\n      }(_moduleCount);\n\n      setupPositionProbePattern(0, 0);\n      setupPositionProbePattern(_moduleCount - 7, 0);\n      setupPositionProbePattern(0, _moduleCount - 7);\n      setupPositionAdjustPattern();\n      setupTimingPattern();\n      setupTypeInfo(test, maskPattern);\n\n      if (_typeNumber >= 7) {\n        setupTypeNumber(test);\n      }\n\n      if (_dataCache == null) {\n        _dataCache = createData(_typeNumber, _errorCorrectionLevel, _dataList);\n      }\n\n      mapData(_dataCache, maskPattern);\n    };\n\n    var setupPositionProbePattern = function(row, col) {\n\n      for (var r = -1; r <= 7; r += 1) {\n\n        if (row + r <= -1 || _moduleCount <= row + r) continue;\n\n        for (var c = -1; c <= 7; c += 1) {\n\n          if (col + c <= -1 || _moduleCount <= col + c) continue;\n\n          if ( (0 <= r && r <= 6 && (c == 0 || c == 6) )\n              || (0 <= c && c <= 6 && (r == 0 || r == 6) )\n              || (2 <= r && r <= 4 && 2 <= c && c <= 4) ) {\n            _modules[row + r][col + c] = true;\n          } else {\n            _modules[row + r][col + c] = false;\n          }\n        }\n      }\n    };\n\n    var getBestMaskPattern = function() {\n\n      var minLostPoint = 0;\n      var pattern = 0;\n\n      for (var i = 0; i < 8; i += 1) {\n\n        makeImpl(true, i);\n\n        var lostPoint = QRUtil.getLostPoint(_this);\n\n        if (i == 0 || minLostPoint > lostPoint) {\n          minLostPoint = lostPoint;\n          pattern = i;\n        }\n      }\n\n      return pattern;\n    };\n\n    var setupTimingPattern = function() {\n\n      for (var r = 8; r < _moduleCount - 8; r += 1) {\n        if (_modules[r][6] != null) {\n          continue;\n        }\n        _modules[r][6] = (r % 2 == 0);\n      }\n\n      for (var c = 8; c < _moduleCount - 8; c += 1) {\n        if (_modules[6][c] != null) {\n          continue;\n        }\n        _modules[6][c] = (c % 2 == 0);\n      }\n    };\n\n    var setupPositionAdjustPattern = function() {\n\n      var pos = QRUtil.getPatternPosition(_typeNumber);\n\n      for (var i = 0; i < pos.length; i += 1) {\n\n        for (var j = 0; j < pos.length; j += 1) {\n\n          var row = pos[i];\n          var col = pos[j];\n\n          if (_modules[row][col] != null) {\n            continue;\n          }\n\n          for (var r = -2; r <= 2; r += 1) {\n\n            for (var c = -2; c <= 2; c += 1) {\n\n              if (r == -2 || r == 2 || c == -2 || c == 2\n                  || (r == 0 && c == 0) ) {\n                _modules[row + r][col + c] = true;\n              } else {\n                _modules[row + r][col + c] = false;\n              }\n            }\n          }\n        }\n      }\n    };\n\n    var setupTypeNumber = function(test) {\n\n      var bits = QRUtil.getBCHTypeNumber(_typeNumber);\n\n      for (var i = 0; i < 18; i += 1) {\n        var mod = (!test && ( (bits >> i) & 1) == 1);\n        _modules[Math.floor(i / 3)][i % 3 + _moduleCount - 8 - 3] = mod;\n      }\n\n      for (var i = 0; i < 18; i += 1) {\n        var mod = (!test && ( (bits >> i) & 1) == 1);\n        _modules[i % 3 + _moduleCount - 8 - 3][Math.floor(i / 3)] = mod;\n      }\n    };\n\n    var setupTypeInfo = function(test, maskPattern) {\n\n      var data = (_errorCorrectionLevel << 3) | maskPattern;\n      var bits = QRUtil.getBCHTypeInfo(data);\n\n      // vertical\n      for (var i = 0; i < 15; i += 1) {\n\n        var mod = (!test && ( (bits >> i) & 1) == 1);\n\n        if (i < 6) {\n          _modules[i][8] = mod;\n        } else if (i < 8) {\n          _modules[i + 1][8] = mod;\n        } else {\n          _modules[_moduleCount - 15 + i][8] = mod;\n        }\n      }\n\n      // horizontal\n      for (var i = 0; i < 15; i += 1) {\n\n        var mod = (!test && ( (bits >> i) & 1) == 1);\n\n        if (i < 8) {\n          _modules[8][_moduleCount - i - 1] = mod;\n        } else if (i < 9) {\n          _modules[8][15 - i - 1 + 1] = mod;\n        } else {\n          _modules[8][15 - i - 1] = mod;\n        }\n      }\n\n      // fixed module\n      _modules[_moduleCount - 8][8] = (!test);\n    };\n\n    var mapData = function(data, maskPattern) {\n\n      var inc = -1;\n      var row = _moduleCount - 1;\n      var bitIndex = 7;\n      var byteIndex = 0;\n      var maskFunc = QRUtil.getMaskFunction(maskPattern);\n\n      for (var col = _moduleCount - 1; col > 0; col -= 2) {\n\n        if (col == 6) col -= 1;\n\n        while (true) {\n\n          for (var c = 0; c < 2; c += 1) {\n\n            if (_modules[row][col - c] == null) {\n\n              var dark = false;\n\n              if (byteIndex < data.length) {\n                dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);\n              }\n\n              var mask = maskFunc(row, col - c);\n\n              if (mask) {\n                dark = !dark;\n              }\n\n              _modules[row][col - c] = dark;\n              bitIndex -= 1;\n\n              if (bitIndex == -1) {\n                byteIndex += 1;\n                bitIndex = 7;\n              }\n            }\n          }\n\n          row += inc;\n\n          if (row < 0 || _moduleCount <= row) {\n            row -= inc;\n            inc = -inc;\n            break;\n          }\n        }\n      }\n    };\n\n    var createBytes = function(buffer, rsBlocks) {\n\n      var offset = 0;\n\n      var maxDcCount = 0;\n      var maxEcCount = 0;\n\n      var dcdata = new Array(rsBlocks.length);\n      var ecdata = new Array(rsBlocks.length);\n\n      for (var r = 0; r < rsBlocks.length; r += 1) {\n\n        var dcCount = rsBlocks[r].dataCount;\n        var ecCount = rsBlocks[r].totalCount - dcCount;\n\n        maxDcCount = Math.max(maxDcCount, dcCount);\n        maxEcCount = Math.max(maxEcCount, ecCount);\n\n        dcdata[r] = new Array(dcCount);\n\n        for (var i = 0; i < dcdata[r].length; i += 1) {\n          dcdata[r][i] = 0xff & buffer.getBuffer()[i + offset];\n        }\n        offset += dcCount;\n\n        var rsPoly = QRUtil.getErrorCorrectPolynomial(ecCount);\n        var rawPoly = qrPolynomial(dcdata[r], rsPoly.getLength() - 1);\n\n        var modPoly = rawPoly.mod(rsPoly);\n        ecdata[r] = new Array(rsPoly.getLength() - 1);\n        for (var i = 0; i < ecdata[r].length; i += 1) {\n          var modIndex = i + modPoly.getLength() - ecdata[r].length;\n          ecdata[r][i] = (modIndex >= 0)? modPoly.getAt(modIndex) : 0;\n        }\n      }\n\n      var totalCodeCount = 0;\n      for (var i = 0; i < rsBlocks.length; i += 1) {\n        totalCodeCount += rsBlocks[i].totalCount;\n      }\n\n      var data = new Array(totalCodeCount);\n      var index = 0;\n\n      for (var i = 0; i < maxDcCount; i += 1) {\n        for (var r = 0; r < rsBlocks.length; r += 1) {\n          if (i < dcdata[r].length) {\n            data[index] = dcdata[r][i];\n            index += 1;\n          }\n        }\n      }\n\n      for (var i = 0; i < maxEcCount; i += 1) {\n        for (var r = 0; r < rsBlocks.length; r += 1) {\n          if (i < ecdata[r].length) {\n            data[index] = ecdata[r][i];\n            index += 1;\n          }\n        }\n      }\n\n      return data;\n    };\n\n    var createData = function(typeNumber, errorCorrectionLevel, dataList) {\n\n      var rsBlocks = QRRSBlock.getRSBlocks(typeNumber, errorCorrectionLevel);\n\n      var buffer = qrBitBuffer();\n\n      for (var i = 0; i < dataList.length; i += 1) {\n        var data = dataList[i];\n        buffer.put(data.getMode(), 4);\n        buffer.put(data.getLength(), QRUtil.getLengthInBits(data.getMode(), typeNumber) );\n        data.write(buffer);\n      }\n\n      // calc num max data.\n      var totalDataCount = 0;\n      for (var i = 0; i < rsBlocks.length; i += 1) {\n        totalDataCount += rsBlocks[i].dataCount;\n      }\n\n      if (buffer.getLengthInBits() > totalDataCount * 8) {\n        throw 'code length overflow. ('\n          + buffer.getLengthInBits()\n          + '>'\n          + totalDataCount * 8\n          + ')';\n      }\n\n      // end code\n      if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {\n        buffer.put(0, 4);\n      }\n\n      // padding\n      while (buffer.getLengthInBits() % 8 != 0) {\n        buffer.putBit(false);\n      }\n\n      // padding\n      while (true) {\n\n        if (buffer.getLengthInBits() >= totalDataCount * 8) {\n          break;\n        }\n        buffer.put(PAD0, 8);\n\n        if (buffer.getLengthInBits() >= totalDataCount * 8) {\n          break;\n        }\n        buffer.put(PAD1, 8);\n      }\n\n      return createBytes(buffer, rsBlocks);\n    };\n\n    _this.addData = function(data, mode) {\n\n      mode = mode || 'Byte';\n\n      var newData = null;\n\n      switch(mode) {\n      case 'Numeric' :\n        newData = qrNumber(data);\n        break;\n      case 'Alphanumeric' :\n        newData = qrAlphaNum(data);\n        break;\n      case 'Byte' :\n        newData = qr8BitByte(data);\n        break;\n      case 'Kanji' :\n        newData = qrKanji(data);\n        break;\n      default :\n        throw 'mode:' + mode;\n      }\n\n      _dataList.push(newData);\n      _dataCache = null;\n    };\n\n    _this.isDark = function(row, col) {\n      if (row < 0 || _moduleCount <= row || col < 0 || _moduleCount <= col) {\n        throw row + ',' + col;\n      }\n      return _modules[row][col];\n    };\n\n    _this.getModuleCount = function() {\n      return _moduleCount;\n    };\n\n    _this.make = function() {\n      if (_typeNumber < 1) {\n        var typeNumber = 1;\n\n        for (; typeNumber < 40; typeNumber++) {\n          var rsBlocks = QRRSBlock.getRSBlocks(typeNumber, _errorCorrectionLevel);\n          var buffer = qrBitBuffer();\n\n          for (var i = 0; i < _dataList.length; i++) {\n            var data = _dataList[i];\n            buffer.put(data.getMode(), 4);\n            buffer.put(data.getLength(), QRUtil.getLengthInBits(data.getMode(), typeNumber) );\n            data.write(buffer);\n          }\n\n          var totalDataCount = 0;\n          for (var i = 0; i < rsBlocks.length; i++) {\n            totalDataCount += rsBlocks[i].dataCount;\n          }\n\n          if (buffer.getLengthInBits() <= totalDataCount * 8) {\n            break;\n          }\n        }\n\n        _typeNumber = typeNumber;\n      }\n\n      makeImpl(false, getBestMaskPattern() );\n    };\n\n    _this.createTableTag = function(cellSize, margin) {\n\n      cellSize = cellSize || 2;\n      margin = (typeof margin == 'undefined')? cellSize * 4 : margin;\n\n      var qrHtml = '';\n\n      qrHtml += '<table style=\"';\n      qrHtml += ' border-width: 0px; border-style: none;';\n      qrHtml += ' border-collapse: collapse;';\n      qrHtml += ' padding: 0px; margin: ' + margin + 'px;';\n      qrHtml += '\">';\n      qrHtml += '<tbody>';\n\n      for (var r = 0; r < _this.getModuleCount(); r += 1) {\n\n        qrHtml += '<tr>';\n\n        for (var c = 0; c < _this.getModuleCount(); c += 1) {\n          qrHtml += '<td style=\"';\n          qrHtml += ' border-width: 0px; border-style: none;';\n          qrHtml += ' border-collapse: collapse;';\n          qrHtml += ' padding: 0px; margin: 0px;';\n          qrHtml += ' width: ' + cellSize + 'px;';\n          qrHtml += ' height: ' + cellSize + 'px;';\n          qrHtml += ' background-color: ';\n          qrHtml += _this.isDark(r, c)? '#000000' : '#ffffff';\n          qrHtml += ';';\n          qrHtml += '\"/>';\n        }\n\n        qrHtml += '</tr>';\n      }\n\n      qrHtml += '</tbody>';\n      qrHtml += '</table>';\n\n      return qrHtml;\n    };\n\n    _this.createSvgTag = function(cellSize, margin, alt, title) {\n\n      var opts = {};\n      if (typeof arguments[0] == 'object') {\n        // Called by options.\n        opts = arguments[0];\n        // overwrite cellSize and margin.\n        cellSize = opts.cellSize;\n        margin = opts.margin;\n        alt = opts.alt;\n        title = opts.title;\n      }\n\n      cellSize = cellSize || 2;\n      margin = (typeof margin == 'undefined')? cellSize * 4 : margin;\n\n      // Compose alt property surrogate\n      alt = (typeof alt === 'string') ? {text: alt} : alt || {};\n      alt.text = alt.text || null;\n      alt.id = (alt.text) ? alt.id || 'qrcode-description' : null;\n\n      // Compose title property surrogate\n      title = (typeof title === 'string') ? {text: title} : title || {};\n      title.text = title.text || null;\n      title.id = (title.text) ? title.id || 'qrcode-title' : null;\n\n      var size = _this.getModuleCount() * cellSize + margin * 2;\n      var c, mc, r, mr, qrSvg='', rect;\n\n      rect = 'l' + cellSize + ',0 0,' + cellSize +\n        ' -' + cellSize + ',0 0,-' + cellSize + 'z ';\n\n      qrSvg += '<svg version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\"';\n      qrSvg += !opts.scalable ? ' width=\"' + size + 'px\" height=\"' + size + 'px\"' : '';\n      qrSvg += ' viewBox=\"0 0 ' + size + ' ' + size + '\" ';\n      qrSvg += ' preserveAspectRatio=\"xMinYMin meet\"';\n      qrSvg += (title.text || alt.text) ? ' role=\"img\" aria-labelledby=\"' +\n          escapeXml([title.id, alt.id].join(' ').trim() ) + '\"' : '';\n      qrSvg += '>';\n      qrSvg += (title.text) ? '<title id=\"' + escapeXml(title.id) + '\">' +\n          escapeXml(title.text) + '</title>' : '';\n      qrSvg += (alt.text) ? '<description id=\"' + escapeXml(alt.id) + '\">' +\n          escapeXml(alt.text) + '</description>' : '';\n      qrSvg += '<rect width=\"100%\" height=\"100%\" fill=\"white\" cx=\"0\" cy=\"0\"/>';\n      qrSvg += '<path d=\"';\n\n      for (r = 0; r < _this.getModuleCount(); r += 1) {\n        mr = r * cellSize + margin;\n        for (c = 0; c < _this.getModuleCount(); c += 1) {\n          if (_this.isDark(r, c) ) {\n            mc = c*cellSize+margin;\n            qrSvg += 'M' + mc + ',' + mr + rect;\n          }\n        }\n      }\n\n      qrSvg += '\" stroke=\"transparent\" fill=\"black\"/>';\n      qrSvg += '</svg>';\n\n      return qrSvg;\n    };\n\n    _this.createDataURL = function(cellSize, margin) {\n\n      cellSize = cellSize || 2;\n      margin = (typeof margin == 'undefined')? cellSize * 4 : margin;\n\n      var size = _this.getModuleCount() * cellSize + margin * 2;\n      var min = margin;\n      var max = size - margin;\n\n      return createDataURL(size, size, function(x, y) {\n        if (min <= x && x < max && min <= y && y < max) {\n          var c = Math.floor( (x - min) / cellSize);\n          var r = Math.floor( (y - min) / cellSize);\n          return _this.isDark(r, c)? 0 : 1;\n        } else {\n          return 1;\n        }\n      } );\n    };\n\n    _this.createImgTag = function(cellSize, margin, alt) {\n\n      cellSize = cellSize || 2;\n      margin = (typeof margin == 'undefined')? cellSize * 4 : margin;\n\n      var size = _this.getModuleCount() * cellSize + margin * 2;\n\n      var img = '';\n      img += '<img';\n      img += '\\u0020src=\"';\n      img += _this.createDataURL(cellSize, margin);\n      img += '\"';\n      img += '\\u0020width=\"';\n      img += size;\n      img += '\"';\n      img += '\\u0020height=\"';\n      img += size;\n      img += '\"';\n      if (alt) {\n        img += '\\u0020alt=\"';\n        img += escapeXml(alt);\n        img += '\"';\n      }\n      img += '/>';\n\n      return img;\n    };\n\n    var escapeXml = function(s) {\n      var escaped = '';\n      for (var i = 0; i < s.length; i += 1) {\n        var c = s.charAt(i);\n        switch(c) {\n        case '<': escaped += '&lt;'; break;\n        case '>': escaped += '&gt;'; break;\n        case '&': escaped += '&amp;'; break;\n        case '\"': escaped += '&quot;'; break;\n        default : escaped += c; break;\n        }\n      }\n      return escaped;\n    };\n\n    var _createHalfASCII = function(margin) {\n      var cellSize = 1;\n      margin = (typeof margin == 'undefined')? cellSize * 2 : margin;\n\n      var size = _this.getModuleCount() * cellSize + margin * 2;\n      var min = margin;\n      var max = size - margin;\n\n      var y, x, r1, r2, p;\n\n      var blocks = {\n        '██': '█',\n        '█ ': '▀',\n        ' █': '▄',\n        '  ': ' '\n      };\n\n      var blocksLastLineNoMargin = {\n        '██': '▀',\n        '█ ': '▀',\n        ' █': ' ',\n        '  ': ' '\n      };\n\n      var ascii = '';\n      for (y = 0; y < size; y += 2) {\n        r1 = Math.floor((y - min) / cellSize);\n        r2 = Math.floor((y + 1 - min) / cellSize);\n        for (x = 0; x < size; x += 1) {\n          p = '█';\n\n          if (min <= x && x < max && min <= y && y < max && _this.isDark(r1, Math.floor((x - min) / cellSize))) {\n            p = ' ';\n          }\n\n          if (min <= x && x < max && min <= y+1 && y+1 < max && _this.isDark(r2, Math.floor((x - min) / cellSize))) {\n            p += ' ';\n          }\n          else {\n            p += '█';\n          }\n\n          // Output 2 characters per pixel, to create full square. 1 character per pixels gives only half width of square.\n          ascii += (margin < 1 && y+1 >= max) ? blocksLastLineNoMargin[p] : blocks[p];\n        }\n\n        ascii += '\\n';\n      }\n\n      if (size % 2 && margin > 0) {\n        return ascii.substring(0, ascii.length - size - 1) + Array(size+1).join('▀');\n      }\n\n      return ascii.substring(0, ascii.length-1);\n    };\n\n    _this.createASCII = function(cellSize, margin) {\n      cellSize = cellSize || 1;\n\n      if (cellSize < 2) {\n        return _createHalfASCII(margin);\n      }\n\n      cellSize -= 1;\n      margin = (typeof margin == 'undefined')? cellSize * 2 : margin;\n\n      var size = _this.getModuleCount() * cellSize + margin * 2;\n      var min = margin;\n      var max = size - margin;\n\n      var y, x, r, p;\n\n      var white = Array(cellSize+1).join('██');\n      var black = Array(cellSize+1).join('  ');\n\n      var ascii = '';\n      var line = '';\n      for (y = 0; y < size; y += 1) {\n        r = Math.floor( (y - min) / cellSize);\n        line = '';\n        for (x = 0; x < size; x += 1) {\n          p = 1;\n\n          if (min <= x && x < max && min <= y && y < max && _this.isDark(r, Math.floor((x - min) / cellSize))) {\n            p = 0;\n          }\n\n          // Output 2 characters per pixel, to create full square. 1 character per pixels gives only half width of square.\n          line += p ? white : black;\n        }\n\n        for (r = 0; r < cellSize; r += 1) {\n          ascii += line + '\\n';\n        }\n      }\n\n      return ascii.substring(0, ascii.length-1);\n    };\n\n    _this.renderTo2dContext = function(context, cellSize) {\n      cellSize = cellSize || 2;\n      var length = _this.getModuleCount();\n      for (var row = 0; row < length; row++) {\n        for (var col = 0; col < length; col++) {\n          context.fillStyle = _this.isDark(row, col) ? 'black' : 'white';\n          context.fillRect(row * cellSize, col * cellSize, cellSize, cellSize);\n        }\n      }\n    }\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // qrcode.stringToBytes\n  //---------------------------------------------------------------------\n\n  qrcode.stringToBytesFuncs = {\n    'default' : function(s) {\n      var bytes = [];\n      for (var i = 0; i < s.length; i += 1) {\n        var c = s.charCodeAt(i);\n        bytes.push(c & 0xff);\n      }\n      return bytes;\n    }\n  };\n\n  qrcode.stringToBytes = qrcode.stringToBytesFuncs['default'];\n\n  //---------------------------------------------------------------------\n  // qrcode.createStringToBytes\n  //---------------------------------------------------------------------\n\n  /**\n   * @param unicodeData base64 string of byte array.\n   * [16bit Unicode],[16bit Bytes], ...\n   * @param numChars\n   */\n  qrcode.createStringToBytes = function(unicodeData, numChars) {\n\n    // create conversion map.\n\n    var unicodeMap = function() {\n\n      var bin = base64DecodeInputStream(unicodeData);\n      var read = function() {\n        var b = bin.read();\n        if (b == -1) throw 'eof';\n        return b;\n      };\n\n      var count = 0;\n      var unicodeMap = {};\n      while (true) {\n        var b0 = bin.read();\n        if (b0 == -1) break;\n        var b1 = read();\n        var b2 = read();\n        var b3 = read();\n        var k = String.fromCharCode( (b0 << 8) | b1);\n        var v = (b2 << 8) | b3;\n        unicodeMap[k] = v;\n        count += 1;\n      }\n      if (count != numChars) {\n        throw count + ' != ' + numChars;\n      }\n\n      return unicodeMap;\n    }();\n\n    var unknownChar = '?'.charCodeAt(0);\n\n    return function(s) {\n      var bytes = [];\n      for (var i = 0; i < s.length; i += 1) {\n        var c = s.charCodeAt(i);\n        if (c < 128) {\n          bytes.push(c);\n        } else {\n          var b = unicodeMap[s.charAt(i)];\n          if (typeof b == 'number') {\n            if ( (b & 0xff) == b) {\n              // 1byte\n              bytes.push(b);\n            } else {\n              // 2bytes\n              bytes.push(b >>> 8);\n              bytes.push(b & 0xff);\n            }\n          } else {\n            bytes.push(unknownChar);\n          }\n        }\n      }\n      return bytes;\n    };\n  };\n\n  //---------------------------------------------------------------------\n  // QRMode\n  //---------------------------------------------------------------------\n\n  var QRMode = {\n    MODE_NUMBER :    1 << 0,\n    MODE_ALPHA_NUM : 1 << 1,\n    MODE_8BIT_BYTE : 1 << 2,\n    MODE_KANJI :     1 << 3\n  };\n\n  //---------------------------------------------------------------------\n  // QRErrorCorrectionLevel\n  //---------------------------------------------------------------------\n\n  var QRErrorCorrectionLevel = {\n    L : 1,\n    M : 0,\n    Q : 3,\n    H : 2\n  };\n\n  //---------------------------------------------------------------------\n  // QRMaskPattern\n  //---------------------------------------------------------------------\n\n  var QRMaskPattern = {\n    PATTERN000 : 0,\n    PATTERN001 : 1,\n    PATTERN010 : 2,\n    PATTERN011 : 3,\n    PATTERN100 : 4,\n    PATTERN101 : 5,\n    PATTERN110 : 6,\n    PATTERN111 : 7\n  };\n\n  //---------------------------------------------------------------------\n  // QRUtil\n  //---------------------------------------------------------------------\n\n  var QRUtil = function() {\n\n    var PATTERN_POSITION_TABLE = [\n      [],\n      [6, 18],\n      [6, 22],\n      [6, 26],\n      [6, 30],\n      [6, 34],\n      [6, 22, 38],\n      [6, 24, 42],\n      [6, 26, 46],\n      [6, 28, 50],\n      [6, 30, 54],\n      [6, 32, 58],\n      [6, 34, 62],\n      [6, 26, 46, 66],\n      [6, 26, 48, 70],\n      [6, 26, 50, 74],\n      [6, 30, 54, 78],\n      [6, 30, 56, 82],\n      [6, 30, 58, 86],\n      [6, 34, 62, 90],\n      [6, 28, 50, 72, 94],\n      [6, 26, 50, 74, 98],\n      [6, 30, 54, 78, 102],\n      [6, 28, 54, 80, 106],\n      [6, 32, 58, 84, 110],\n      [6, 30, 58, 86, 114],\n      [6, 34, 62, 90, 118],\n      [6, 26, 50, 74, 98, 122],\n      [6, 30, 54, 78, 102, 126],\n      [6, 26, 52, 78, 104, 130],\n      [6, 30, 56, 82, 108, 134],\n      [6, 34, 60, 86, 112, 138],\n      [6, 30, 58, 86, 114, 142],\n      [6, 34, 62, 90, 118, 146],\n      [6, 30, 54, 78, 102, 126, 150],\n      [6, 24, 50, 76, 102, 128, 154],\n      [6, 28, 54, 80, 106, 132, 158],\n      [6, 32, 58, 84, 110, 136, 162],\n      [6, 26, 54, 82, 110, 138, 166],\n      [6, 30, 58, 86, 114, 142, 170]\n    ];\n    var G15 = (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0);\n    var G18 = (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0);\n    var G15_MASK = (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1);\n\n    var _this = {};\n\n    var getBCHDigit = function(data) {\n      var digit = 0;\n      while (data != 0) {\n        digit += 1;\n        data >>>= 1;\n      }\n      return digit;\n    };\n\n    _this.getBCHTypeInfo = function(data) {\n      var d = data << 10;\n      while (getBCHDigit(d) - getBCHDigit(G15) >= 0) {\n        d ^= (G15 << (getBCHDigit(d) - getBCHDigit(G15) ) );\n      }\n      return ( (data << 10) | d) ^ G15_MASK;\n    };\n\n    _this.getBCHTypeNumber = function(data) {\n      var d = data << 12;\n      while (getBCHDigit(d) - getBCHDigit(G18) >= 0) {\n        d ^= (G18 << (getBCHDigit(d) - getBCHDigit(G18) ) );\n      }\n      return (data << 12) | d;\n    };\n\n    _this.getPatternPosition = function(typeNumber) {\n      return PATTERN_POSITION_TABLE[typeNumber - 1];\n    };\n\n    _this.getMaskFunction = function(maskPattern) {\n\n      switch (maskPattern) {\n\n      case QRMaskPattern.PATTERN000 :\n        return function(i, j) { return (i + j) % 2 == 0; };\n      case QRMaskPattern.PATTERN001 :\n        return function(i, j) { return i % 2 == 0; };\n      case QRMaskPattern.PATTERN010 :\n        return function(i, j) { return j % 3 == 0; };\n      case QRMaskPattern.PATTERN011 :\n        return function(i, j) { return (i + j) % 3 == 0; };\n      case QRMaskPattern.PATTERN100 :\n        return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0; };\n      case QRMaskPattern.PATTERN101 :\n        return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0; };\n      case QRMaskPattern.PATTERN110 :\n        return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0; };\n      case QRMaskPattern.PATTERN111 :\n        return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0; };\n\n      default :\n        throw 'bad maskPattern:' + maskPattern;\n      }\n    };\n\n    _this.getErrorCorrectPolynomial = function(errorCorrectLength) {\n      var a = qrPolynomial([1], 0);\n      for (var i = 0; i < errorCorrectLength; i += 1) {\n        a = a.multiply(qrPolynomial([1, QRMath.gexp(i)], 0) );\n      }\n      return a;\n    };\n\n    _this.getLengthInBits = function(mode, type) {\n\n      if (1 <= type && type < 10) {\n\n        // 1 - 9\n\n        switch(mode) {\n        case QRMode.MODE_NUMBER    : return 10;\n        case QRMode.MODE_ALPHA_NUM : return 9;\n        case QRMode.MODE_8BIT_BYTE : return 8;\n        case QRMode.MODE_KANJI     : return 8;\n        default :\n          throw 'mode:' + mode;\n        }\n\n      } else if (type < 27) {\n\n        // 10 - 26\n\n        switch(mode) {\n        case QRMode.MODE_NUMBER    : return 12;\n        case QRMode.MODE_ALPHA_NUM : return 11;\n        case QRMode.MODE_8BIT_BYTE : return 16;\n        case QRMode.MODE_KANJI     : return 10;\n        default :\n          throw 'mode:' + mode;\n        }\n\n      } else if (type < 41) {\n\n        // 27 - 40\n\n        switch(mode) {\n        case QRMode.MODE_NUMBER    : return 14;\n        case QRMode.MODE_ALPHA_NUM : return 13;\n        case QRMode.MODE_8BIT_BYTE : return 16;\n        case QRMode.MODE_KANJI     : return 12;\n        default :\n          throw 'mode:' + mode;\n        }\n\n      } else {\n        throw 'type:' + type;\n      }\n    };\n\n    _this.getLostPoint = function(qrcode) {\n\n      var moduleCount = qrcode.getModuleCount();\n\n      var lostPoint = 0;\n\n      // LEVEL1\n\n      for (var row = 0; row < moduleCount; row += 1) {\n        for (var col = 0; col < moduleCount; col += 1) {\n\n          var sameCount = 0;\n          var dark = qrcode.isDark(row, col);\n\n          for (var r = -1; r <= 1; r += 1) {\n\n            if (row + r < 0 || moduleCount <= row + r) {\n              continue;\n            }\n\n            for (var c = -1; c <= 1; c += 1) {\n\n              if (col + c < 0 || moduleCount <= col + c) {\n                continue;\n              }\n\n              if (r == 0 && c == 0) {\n                continue;\n              }\n\n              if (dark == qrcode.isDark(row + r, col + c) ) {\n                sameCount += 1;\n              }\n            }\n          }\n\n          if (sameCount > 5) {\n            lostPoint += (3 + sameCount - 5);\n          }\n        }\n      };\n\n      // LEVEL2\n\n      for (var row = 0; row < moduleCount - 1; row += 1) {\n        for (var col = 0; col < moduleCount - 1; col += 1) {\n          var count = 0;\n          if (qrcode.isDark(row, col) ) count += 1;\n          if (qrcode.isDark(row + 1, col) ) count += 1;\n          if (qrcode.isDark(row, col + 1) ) count += 1;\n          if (qrcode.isDark(row + 1, col + 1) ) count += 1;\n          if (count == 0 || count == 4) {\n            lostPoint += 3;\n          }\n        }\n      }\n\n      // LEVEL3\n\n      for (var row = 0; row < moduleCount; row += 1) {\n        for (var col = 0; col < moduleCount - 6; col += 1) {\n          if (qrcode.isDark(row, col)\n              && !qrcode.isDark(row, col + 1)\n              &&  qrcode.isDark(row, col + 2)\n              &&  qrcode.isDark(row, col + 3)\n              &&  qrcode.isDark(row, col + 4)\n              && !qrcode.isDark(row, col + 5)\n              &&  qrcode.isDark(row, col + 6) ) {\n            lostPoint += 40;\n          }\n        }\n      }\n\n      for (var col = 0; col < moduleCount; col += 1) {\n        for (var row = 0; row < moduleCount - 6; row += 1) {\n          if (qrcode.isDark(row, col)\n              && !qrcode.isDark(row + 1, col)\n              &&  qrcode.isDark(row + 2, col)\n              &&  qrcode.isDark(row + 3, col)\n              &&  qrcode.isDark(row + 4, col)\n              && !qrcode.isDark(row + 5, col)\n              &&  qrcode.isDark(row + 6, col) ) {\n            lostPoint += 40;\n          }\n        }\n      }\n\n      // LEVEL4\n\n      var darkCount = 0;\n\n      for (var col = 0; col < moduleCount; col += 1) {\n        for (var row = 0; row < moduleCount; row += 1) {\n          if (qrcode.isDark(row, col) ) {\n            darkCount += 1;\n          }\n        }\n      }\n\n      var ratio = Math.abs(100 * darkCount / moduleCount / moduleCount - 50) / 5;\n      lostPoint += ratio * 10;\n\n      return lostPoint;\n    };\n\n    return _this;\n  }();\n\n  //---------------------------------------------------------------------\n  // QRMath\n  //---------------------------------------------------------------------\n\n  var QRMath = function() {\n\n    var EXP_TABLE = new Array(256);\n    var LOG_TABLE = new Array(256);\n\n    // initialize tables\n    for (var i = 0; i < 8; i += 1) {\n      EXP_TABLE[i] = 1 << i;\n    }\n    for (var i = 8; i < 256; i += 1) {\n      EXP_TABLE[i] = EXP_TABLE[i - 4]\n        ^ EXP_TABLE[i - 5]\n        ^ EXP_TABLE[i - 6]\n        ^ EXP_TABLE[i - 8];\n    }\n    for (var i = 0; i < 255; i += 1) {\n      LOG_TABLE[EXP_TABLE[i] ] = i;\n    }\n\n    var _this = {};\n\n    _this.glog = function(n) {\n\n      if (n < 1) {\n        throw 'glog(' + n + ')';\n      }\n\n      return LOG_TABLE[n];\n    };\n\n    _this.gexp = function(n) {\n\n      while (n < 0) {\n        n += 255;\n      }\n\n      while (n >= 256) {\n        n -= 255;\n      }\n\n      return EXP_TABLE[n];\n    };\n\n    return _this;\n  }();\n\n  //---------------------------------------------------------------------\n  // qrPolynomial\n  //---------------------------------------------------------------------\n\n  function qrPolynomial(num, shift) {\n\n    if (typeof num.length == 'undefined') {\n      throw num.length + '/' + shift;\n    }\n\n    var _num = function() {\n      var offset = 0;\n      while (offset < num.length && num[offset] == 0) {\n        offset += 1;\n      }\n      var _num = new Array(num.length - offset + shift);\n      for (var i = 0; i < num.length - offset; i += 1) {\n        _num[i] = num[i + offset];\n      }\n      return _num;\n    }();\n\n    var _this = {};\n\n    _this.getAt = function(index) {\n      return _num[index];\n    };\n\n    _this.getLength = function() {\n      return _num.length;\n    };\n\n    _this.multiply = function(e) {\n\n      var num = new Array(_this.getLength() + e.getLength() - 1);\n\n      for (var i = 0; i < _this.getLength(); i += 1) {\n        for (var j = 0; j < e.getLength(); j += 1) {\n          num[i + j] ^= QRMath.gexp(QRMath.glog(_this.getAt(i) ) + QRMath.glog(e.getAt(j) ) );\n        }\n      }\n\n      return qrPolynomial(num, 0);\n    };\n\n    _this.mod = function(e) {\n\n      if (_this.getLength() - e.getLength() < 0) {\n        return _this;\n      }\n\n      var ratio = QRMath.glog(_this.getAt(0) ) - QRMath.glog(e.getAt(0) );\n\n      var num = new Array(_this.getLength() );\n      for (var i = 0; i < _this.getLength(); i += 1) {\n        num[i] = _this.getAt(i);\n      }\n\n      for (var i = 0; i < e.getLength(); i += 1) {\n        num[i] ^= QRMath.gexp(QRMath.glog(e.getAt(i) ) + ratio);\n      }\n\n      // recursive call\n      return qrPolynomial(num, 0).mod(e);\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // QRRSBlock\n  //---------------------------------------------------------------------\n\n  var QRRSBlock = function() {\n\n    var RS_BLOCK_TABLE = [\n\n      // L\n      // M\n      // Q\n      // H\n\n      // 1\n      [1, 26, 19],\n      [1, 26, 16],\n      [1, 26, 13],\n      [1, 26, 9],\n\n      // 2\n      [1, 44, 34],\n      [1, 44, 28],\n      [1, 44, 22],\n      [1, 44, 16],\n\n      // 3\n      [1, 70, 55],\n      [1, 70, 44],\n      [2, 35, 17],\n      [2, 35, 13],\n\n      // 4\n      [1, 100, 80],\n      [2, 50, 32],\n      [2, 50, 24],\n      [4, 25, 9],\n\n      // 5\n      [1, 134, 108],\n      [2, 67, 43],\n      [2, 33, 15, 2, 34, 16],\n      [2, 33, 11, 2, 34, 12],\n\n      // 6\n      [2, 86, 68],\n      [4, 43, 27],\n      [4, 43, 19],\n      [4, 43, 15],\n\n      // 7\n      [2, 98, 78],\n      [4, 49, 31],\n      [2, 32, 14, 4, 33, 15],\n      [4, 39, 13, 1, 40, 14],\n\n      // 8\n      [2, 121, 97],\n      [2, 60, 38, 2, 61, 39],\n      [4, 40, 18, 2, 41, 19],\n      [4, 40, 14, 2, 41, 15],\n\n      // 9\n      [2, 146, 116],\n      [3, 58, 36, 2, 59, 37],\n      [4, 36, 16, 4, 37, 17],\n      [4, 36, 12, 4, 37, 13],\n\n      // 10\n      [2, 86, 68, 2, 87, 69],\n      [4, 69, 43, 1, 70, 44],\n      [6, 43, 19, 2, 44, 20],\n      [6, 43, 15, 2, 44, 16],\n\n      // 11\n      [4, 101, 81],\n      [1, 80, 50, 4, 81, 51],\n      [4, 50, 22, 4, 51, 23],\n      [3, 36, 12, 8, 37, 13],\n\n      // 12\n      [2, 116, 92, 2, 117, 93],\n      [6, 58, 36, 2, 59, 37],\n      [4, 46, 20, 6, 47, 21],\n      [7, 42, 14, 4, 43, 15],\n\n      // 13\n      [4, 133, 107],\n      [8, 59, 37, 1, 60, 38],\n      [8, 44, 20, 4, 45, 21],\n      [12, 33, 11, 4, 34, 12],\n\n      // 14\n      [3, 145, 115, 1, 146, 116],\n      [4, 64, 40, 5, 65, 41],\n      [11, 36, 16, 5, 37, 17],\n      [11, 36, 12, 5, 37, 13],\n\n      // 15\n      [5, 109, 87, 1, 110, 88],\n      [5, 65, 41, 5, 66, 42],\n      [5, 54, 24, 7, 55, 25],\n      [11, 36, 12, 7, 37, 13],\n\n      // 16\n      [5, 122, 98, 1, 123, 99],\n      [7, 73, 45, 3, 74, 46],\n      [15, 43, 19, 2, 44, 20],\n      [3, 45, 15, 13, 46, 16],\n\n      // 17\n      [1, 135, 107, 5, 136, 108],\n      [10, 74, 46, 1, 75, 47],\n      [1, 50, 22, 15, 51, 23],\n      [2, 42, 14, 17, 43, 15],\n\n      // 18\n      [5, 150, 120, 1, 151, 121],\n      [9, 69, 43, 4, 70, 44],\n      [17, 50, 22, 1, 51, 23],\n      [2, 42, 14, 19, 43, 15],\n\n      // 19\n      [3, 141, 113, 4, 142, 114],\n      [3, 70, 44, 11, 71, 45],\n      [17, 47, 21, 4, 48, 22],\n      [9, 39, 13, 16, 40, 14],\n\n      // 20\n      [3, 135, 107, 5, 136, 108],\n      [3, 67, 41, 13, 68, 42],\n      [15, 54, 24, 5, 55, 25],\n      [15, 43, 15, 10, 44, 16],\n\n      // 21\n      [4, 144, 116, 4, 145, 117],\n      [17, 68, 42],\n      [17, 50, 22, 6, 51, 23],\n      [19, 46, 16, 6, 47, 17],\n\n      // 22\n      [2, 139, 111, 7, 140, 112],\n      [17, 74, 46],\n      [7, 54, 24, 16, 55, 25],\n      [34, 37, 13],\n\n      // 23\n      [4, 151, 121, 5, 152, 122],\n      [4, 75, 47, 14, 76, 48],\n      [11, 54, 24, 14, 55, 25],\n      [16, 45, 15, 14, 46, 16],\n\n      // 24\n      [6, 147, 117, 4, 148, 118],\n      [6, 73, 45, 14, 74, 46],\n      [11, 54, 24, 16, 55, 25],\n      [30, 46, 16, 2, 47, 17],\n\n      // 25\n      [8, 132, 106, 4, 133, 107],\n      [8, 75, 47, 13, 76, 48],\n      [7, 54, 24, 22, 55, 25],\n      [22, 45, 15, 13, 46, 16],\n\n      // 26\n      [10, 142, 114, 2, 143, 115],\n      [19, 74, 46, 4, 75, 47],\n      [28, 50, 22, 6, 51, 23],\n      [33, 46, 16, 4, 47, 17],\n\n      // 27\n      [8, 152, 122, 4, 153, 123],\n      [22, 73, 45, 3, 74, 46],\n      [8, 53, 23, 26, 54, 24],\n      [12, 45, 15, 28, 46, 16],\n\n      // 28\n      [3, 147, 117, 10, 148, 118],\n      [3, 73, 45, 23, 74, 46],\n      [4, 54, 24, 31, 55, 25],\n      [11, 45, 15, 31, 46, 16],\n\n      // 29\n      [7, 146, 116, 7, 147, 117],\n      [21, 73, 45, 7, 74, 46],\n      [1, 53, 23, 37, 54, 24],\n      [19, 45, 15, 26, 46, 16],\n\n      // 30\n      [5, 145, 115, 10, 146, 116],\n      [19, 75, 47, 10, 76, 48],\n      [15, 54, 24, 25, 55, 25],\n      [23, 45, 15, 25, 46, 16],\n\n      // 31\n      [13, 145, 115, 3, 146, 116],\n      [2, 74, 46, 29, 75, 47],\n      [42, 54, 24, 1, 55, 25],\n      [23, 45, 15, 28, 46, 16],\n\n      // 32\n      [17, 145, 115],\n      [10, 74, 46, 23, 75, 47],\n      [10, 54, 24, 35, 55, 25],\n      [19, 45, 15, 35, 46, 16],\n\n      // 33\n      [17, 145, 115, 1, 146, 116],\n      [14, 74, 46, 21, 75, 47],\n      [29, 54, 24, 19, 55, 25],\n      [11, 45, 15, 46, 46, 16],\n\n      // 34\n      [13, 145, 115, 6, 146, 116],\n      [14, 74, 46, 23, 75, 47],\n      [44, 54, 24, 7, 55, 25],\n      [59, 46, 16, 1, 47, 17],\n\n      // 35\n      [12, 151, 121, 7, 152, 122],\n      [12, 75, 47, 26, 76, 48],\n      [39, 54, 24, 14, 55, 25],\n      [22, 45, 15, 41, 46, 16],\n\n      // 36\n      [6, 151, 121, 14, 152, 122],\n      [6, 75, 47, 34, 76, 48],\n      [46, 54, 24, 10, 55, 25],\n      [2, 45, 15, 64, 46, 16],\n\n      // 37\n      [17, 152, 122, 4, 153, 123],\n      [29, 74, 46, 14, 75, 47],\n      [49, 54, 24, 10, 55, 25],\n      [24, 45, 15, 46, 46, 16],\n\n      // 38\n      [4, 152, 122, 18, 153, 123],\n      [13, 74, 46, 32, 75, 47],\n      [48, 54, 24, 14, 55, 25],\n      [42, 45, 15, 32, 46, 16],\n\n      // 39\n      [20, 147, 117, 4, 148, 118],\n      [40, 75, 47, 7, 76, 48],\n      [43, 54, 24, 22, 55, 25],\n      [10, 45, 15, 67, 46, 16],\n\n      // 40\n      [19, 148, 118, 6, 149, 119],\n      [18, 75, 47, 31, 76, 48],\n      [34, 54, 24, 34, 55, 25],\n      [20, 45, 15, 61, 46, 16]\n    ];\n\n    var qrRSBlock = function(totalCount, dataCount) {\n      var _this = {};\n      _this.totalCount = totalCount;\n      _this.dataCount = dataCount;\n      return _this;\n    };\n\n    var _this = {};\n\n    var getRsBlockTable = function(typeNumber, errorCorrectionLevel) {\n\n      switch(errorCorrectionLevel) {\n      case QRErrorCorrectionLevel.L :\n        return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0];\n      case QRErrorCorrectionLevel.M :\n        return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1];\n      case QRErrorCorrectionLevel.Q :\n        return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2];\n      case QRErrorCorrectionLevel.H :\n        return RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3];\n      default :\n        return undefined;\n      }\n    };\n\n    _this.getRSBlocks = function(typeNumber, errorCorrectionLevel) {\n\n      var rsBlock = getRsBlockTable(typeNumber, errorCorrectionLevel);\n\n      if (typeof rsBlock == 'undefined') {\n        throw 'bad rs block @ typeNumber:' + typeNumber +\n            '/errorCorrectionLevel:' + errorCorrectionLevel;\n      }\n\n      var length = rsBlock.length / 3;\n\n      var list = [];\n\n      for (var i = 0; i < length; i += 1) {\n\n        var count = rsBlock[i * 3 + 0];\n        var totalCount = rsBlock[i * 3 + 1];\n        var dataCount = rsBlock[i * 3 + 2];\n\n        for (var j = 0; j < count; j += 1) {\n          list.push(qrRSBlock(totalCount, dataCount) );\n        }\n      }\n\n      return list;\n    };\n\n    return _this;\n  }();\n\n  //---------------------------------------------------------------------\n  // qrBitBuffer\n  //---------------------------------------------------------------------\n\n  var qrBitBuffer = function() {\n\n    var _buffer = [];\n    var _length = 0;\n\n    var _this = {};\n\n    _this.getBuffer = function() {\n      return _buffer;\n    };\n\n    _this.getAt = function(index) {\n      var bufIndex = Math.floor(index / 8);\n      return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1;\n    };\n\n    _this.put = function(num, length) {\n      for (var i = 0; i < length; i += 1) {\n        _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);\n      }\n    };\n\n    _this.getLengthInBits = function() {\n      return _length;\n    };\n\n    _this.putBit = function(bit) {\n\n      var bufIndex = Math.floor(_length / 8);\n      if (_buffer.length <= bufIndex) {\n        _buffer.push(0);\n      }\n\n      if (bit) {\n        _buffer[bufIndex] |= (0x80 >>> (_length % 8) );\n      }\n\n      _length += 1;\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // qrNumber\n  //---------------------------------------------------------------------\n\n  var qrNumber = function(data) {\n\n    var _mode = QRMode.MODE_NUMBER;\n    var _data = data;\n\n    var _this = {};\n\n    _this.getMode = function() {\n      return _mode;\n    };\n\n    _this.getLength = function(buffer) {\n      return _data.length;\n    };\n\n    _this.write = function(buffer) {\n\n      var data = _data;\n\n      var i = 0;\n\n      while (i + 2 < data.length) {\n        buffer.put(strToNum(data.substring(i, i + 3) ), 10);\n        i += 3;\n      }\n\n      if (i < data.length) {\n        if (data.length - i == 1) {\n          buffer.put(strToNum(data.substring(i, i + 1) ), 4);\n        } else if (data.length - i == 2) {\n          buffer.put(strToNum(data.substring(i, i + 2) ), 7);\n        }\n      }\n    };\n\n    var strToNum = function(s) {\n      var num = 0;\n      for (var i = 0; i < s.length; i += 1) {\n        num = num * 10 + chatToNum(s.charAt(i) );\n      }\n      return num;\n    };\n\n    var chatToNum = function(c) {\n      if ('0' <= c && c <= '9') {\n        return c.charCodeAt(0) - '0'.charCodeAt(0);\n      }\n      throw 'illegal char :' + c;\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // qrAlphaNum\n  //---------------------------------------------------------------------\n\n  var qrAlphaNum = function(data) {\n\n    var _mode = QRMode.MODE_ALPHA_NUM;\n    var _data = data;\n\n    var _this = {};\n\n    _this.getMode = function() {\n      return _mode;\n    };\n\n    _this.getLength = function(buffer) {\n      return _data.length;\n    };\n\n    _this.write = function(buffer) {\n\n      var s = _data;\n\n      var i = 0;\n\n      while (i + 1 < s.length) {\n        buffer.put(\n          getCode(s.charAt(i) ) * 45 +\n          getCode(s.charAt(i + 1) ), 11);\n        i += 2;\n      }\n\n      if (i < s.length) {\n        buffer.put(getCode(s.charAt(i) ), 6);\n      }\n    };\n\n    var getCode = function(c) {\n\n      if ('0' <= c && c <= '9') {\n        return c.charCodeAt(0) - '0'.charCodeAt(0);\n      } else if ('A' <= c && c <= 'Z') {\n        return c.charCodeAt(0) - 'A'.charCodeAt(0) + 10;\n      } else {\n        switch (c) {\n        case ' ' : return 36;\n        case '$' : return 37;\n        case '%' : return 38;\n        case '*' : return 39;\n        case '+' : return 40;\n        case '-' : return 41;\n        case '.' : return 42;\n        case '/' : return 43;\n        case ':' : return 44;\n        default :\n          throw 'illegal char :' + c;\n        }\n      }\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // qr8BitByte\n  //---------------------------------------------------------------------\n\n  var qr8BitByte = function(data) {\n\n    var _mode = QRMode.MODE_8BIT_BYTE;\n    var _data = data;\n    var _bytes = qrcode.stringToBytes(data);\n\n    var _this = {};\n\n    _this.getMode = function() {\n      return _mode;\n    };\n\n    _this.getLength = function(buffer) {\n      return _bytes.length;\n    };\n\n    _this.write = function(buffer) {\n      for (var i = 0; i < _bytes.length; i += 1) {\n        buffer.put(_bytes[i], 8);\n      }\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // qrKanji\n  //---------------------------------------------------------------------\n\n  var qrKanji = function(data) {\n\n    var _mode = QRMode.MODE_KANJI;\n    var _data = data;\n\n    var stringToBytes = qrcode.stringToBytesFuncs['SJIS'];\n    if (!stringToBytes) {\n      throw 'sjis not supported.';\n    }\n    !function(c, code) {\n      // self test for sjis support.\n      var test = stringToBytes(c);\n      if (test.length != 2 || ( (test[0] << 8) | test[1]) != code) {\n        throw 'sjis not supported.';\n      }\n    }('\\u53cb', 0x9746);\n\n    var _bytes = stringToBytes(data);\n\n    var _this = {};\n\n    _this.getMode = function() {\n      return _mode;\n    };\n\n    _this.getLength = function(buffer) {\n      return ~~(_bytes.length / 2);\n    };\n\n    _this.write = function(buffer) {\n\n      var data = _bytes;\n\n      var i = 0;\n\n      while (i + 1 < data.length) {\n\n        var c = ( (0xff & data[i]) << 8) | (0xff & data[i + 1]);\n\n        if (0x8140 <= c && c <= 0x9FFC) {\n          c -= 0x8140;\n        } else if (0xE040 <= c && c <= 0xEBBF) {\n          c -= 0xC140;\n        } else {\n          throw 'illegal char at ' + (i + 1) + '/' + c;\n        }\n\n        c = ( (c >>> 8) & 0xff) * 0xC0 + (c & 0xff);\n\n        buffer.put(c, 13);\n\n        i += 2;\n      }\n\n      if (i < data.length) {\n        throw 'illegal char at ' + (i + 1);\n      }\n    };\n\n    return _this;\n  };\n\n  //=====================================================================\n  // GIF Support etc.\n  //\n\n  //---------------------------------------------------------------------\n  // byteArrayOutputStream\n  //---------------------------------------------------------------------\n\n  var byteArrayOutputStream = function() {\n\n    var _bytes = [];\n\n    var _this = {};\n\n    _this.writeByte = function(b) {\n      _bytes.push(b & 0xff);\n    };\n\n    _this.writeShort = function(i) {\n      _this.writeByte(i);\n      _this.writeByte(i >>> 8);\n    };\n\n    _this.writeBytes = function(b, off, len) {\n      off = off || 0;\n      len = len || b.length;\n      for (var i = 0; i < len; i += 1) {\n        _this.writeByte(b[i + off]);\n      }\n    };\n\n    _this.writeString = function(s) {\n      for (var i = 0; i < s.length; i += 1) {\n        _this.writeByte(s.charCodeAt(i) );\n      }\n    };\n\n    _this.toByteArray = function() {\n      return _bytes;\n    };\n\n    _this.toString = function() {\n      var s = '';\n      s += '[';\n      for (var i = 0; i < _bytes.length; i += 1) {\n        if (i > 0) {\n          s += ',';\n        }\n        s += _bytes[i];\n      }\n      s += ']';\n      return s;\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // base64EncodeOutputStream\n  //---------------------------------------------------------------------\n\n  var base64EncodeOutputStream = function() {\n\n    var _buffer = 0;\n    var _buflen = 0;\n    var _length = 0;\n    var _base64 = '';\n\n    var _this = {};\n\n    var writeEncoded = function(b) {\n      _base64 += String.fromCharCode(encode(b & 0x3f) );\n    };\n\n    var encode = function(n) {\n      if (n < 0) {\n        // error.\n      } else if (n < 26) {\n        return 0x41 + n;\n      } else if (n < 52) {\n        return 0x61 + (n - 26);\n      } else if (n < 62) {\n        return 0x30 + (n - 52);\n      } else if (n == 62) {\n        return 0x2b;\n      } else if (n == 63) {\n        return 0x2f;\n      }\n      throw 'n:' + n;\n    };\n\n    _this.writeByte = function(n) {\n\n      _buffer = (_buffer << 8) | (n & 0xff);\n      _buflen += 8;\n      _length += 1;\n\n      while (_buflen >= 6) {\n        writeEncoded(_buffer >>> (_buflen - 6) );\n        _buflen -= 6;\n      }\n    };\n\n    _this.flush = function() {\n\n      if (_buflen > 0) {\n        writeEncoded(_buffer << (6 - _buflen) );\n        _buffer = 0;\n        _buflen = 0;\n      }\n\n      if (_length % 3 != 0) {\n        // padding\n        var padlen = 3 - _length % 3;\n        for (var i = 0; i < padlen; i += 1) {\n          _base64 += '=';\n        }\n      }\n    };\n\n    _this.toString = function() {\n      return _base64;\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // base64DecodeInputStream\n  //---------------------------------------------------------------------\n\n  var base64DecodeInputStream = function(str) {\n\n    var _str = str;\n    var _pos = 0;\n    var _buffer = 0;\n    var _buflen = 0;\n\n    var _this = {};\n\n    _this.read = function() {\n\n      while (_buflen < 8) {\n\n        if (_pos >= _str.length) {\n          if (_buflen == 0) {\n            return -1;\n          }\n          throw 'unexpected end of file./' + _buflen;\n        }\n\n        var c = _str.charAt(_pos);\n        _pos += 1;\n\n        if (c == '=') {\n          _buflen = 0;\n          return -1;\n        } else if (c.match(/^\\s$/) ) {\n          // ignore if whitespace.\n          continue;\n        }\n\n        _buffer = (_buffer << 6) | decode(c.charCodeAt(0) );\n        _buflen += 6;\n      }\n\n      var n = (_buffer >>> (_buflen - 8) ) & 0xff;\n      _buflen -= 8;\n      return n;\n    };\n\n    var decode = function(c) {\n      if (0x41 <= c && c <= 0x5a) {\n        return c - 0x41;\n      } else if (0x61 <= c && c <= 0x7a) {\n        return c - 0x61 + 26;\n      } else if (0x30 <= c && c <= 0x39) {\n        return c - 0x30 + 52;\n      } else if (c == 0x2b) {\n        return 62;\n      } else if (c == 0x2f) {\n        return 63;\n      } else {\n        throw 'c:' + c;\n      }\n    };\n\n    return _this;\n  };\n\n  //---------------------------------------------------------------------\n  // gifImage (B/W)\n  //---------------------------------------------------------------------\n\n  var gifImage = function(width, height) {\n\n    var _width = width;\n    var _height = height;\n    var _data = new Array(width * height);\n\n    var _this = {};\n\n    _this.setPixel = function(x, y, pixel) {\n      _data[y * _width + x] = pixel;\n    };\n\n    _this.write = function(out) {\n\n      //---------------------------------\n      // GIF Signature\n\n      out.writeString('GIF87a');\n\n      //---------------------------------\n      // Screen Descriptor\n\n      out.writeShort(_width);\n      out.writeShort(_height);\n\n      out.writeByte(0x80); // 2bit\n      out.writeByte(0);\n      out.writeByte(0);\n\n      //---------------------------------\n      // Global Color Map\n\n      // black\n      out.writeByte(0x00);\n      out.writeByte(0x00);\n      out.writeByte(0x00);\n\n      // white\n      out.writeByte(0xff);\n      out.writeByte(0xff);\n      out.writeByte(0xff);\n\n      //---------------------------------\n      // Image Descriptor\n\n      out.writeString(',');\n      out.writeShort(0);\n      out.writeShort(0);\n      out.writeShort(_width);\n      out.writeShort(_height);\n      out.writeByte(0);\n\n      //---------------------------------\n      // Local Color Map\n\n      //---------------------------------\n      // Raster Data\n\n      var lzwMinCodeSize = 2;\n      var raster = getLZWRaster(lzwMinCodeSize);\n\n      out.writeByte(lzwMinCodeSize);\n\n      var offset = 0;\n\n      while (raster.length - offset > 255) {\n        out.writeByte(255);\n        out.writeBytes(raster, offset, 255);\n        offset += 255;\n      }\n\n      out.writeByte(raster.length - offset);\n      out.writeBytes(raster, offset, raster.length - offset);\n      out.writeByte(0x00);\n\n      //---------------------------------\n      // GIF Terminator\n      out.writeString(';');\n    };\n\n    var bitOutputStream = function(out) {\n\n      var _out = out;\n      var _bitLength = 0;\n      var _bitBuffer = 0;\n\n      var _this = {};\n\n      _this.write = function(data, length) {\n\n        if ( (data >>> length) != 0) {\n          throw 'length over';\n        }\n\n        while (_bitLength + length >= 8) {\n          _out.writeByte(0xff & ( (data << _bitLength) | _bitBuffer) );\n          length -= (8 - _bitLength);\n          data >>>= (8 - _bitLength);\n          _bitBuffer = 0;\n          _bitLength = 0;\n        }\n\n        _bitBuffer = (data << _bitLength) | _bitBuffer;\n        _bitLength = _bitLength + length;\n      };\n\n      _this.flush = function() {\n        if (_bitLength > 0) {\n          _out.writeByte(_bitBuffer);\n        }\n      };\n\n      return _this;\n    };\n\n    var getLZWRaster = function(lzwMinCodeSize) {\n\n      var clearCode = 1 << lzwMinCodeSize;\n      var endCode = (1 << lzwMinCodeSize) + 1;\n      var bitLength = lzwMinCodeSize + 1;\n\n      // Setup LZWTable\n      var table = lzwTable();\n\n      for (var i = 0; i < clearCode; i += 1) {\n        table.add(String.fromCharCode(i) );\n      }\n      table.add(String.fromCharCode(clearCode) );\n      table.add(String.fromCharCode(endCode) );\n\n      var byteOut = byteArrayOutputStream();\n      var bitOut = bitOutputStream(byteOut);\n\n      // clear code\n      bitOut.write(clearCode, bitLength);\n\n      var dataIndex = 0;\n\n      var s = String.fromCharCode(_data[dataIndex]);\n      dataIndex += 1;\n\n      while (dataIndex < _data.length) {\n\n        var c = String.fromCharCode(_data[dataIndex]);\n        dataIndex += 1;\n\n        if (table.contains(s + c) ) {\n\n          s = s + c;\n\n        } else {\n\n          bitOut.write(table.indexOf(s), bitLength);\n\n          if (table.size() < 0xfff) {\n\n            if (table.size() == (1 << bitLength) ) {\n              bitLength += 1;\n            }\n\n            table.add(s + c);\n          }\n\n          s = c;\n        }\n      }\n\n      bitOut.write(table.indexOf(s), bitLength);\n\n      // end code\n      bitOut.write(endCode, bitLength);\n\n      bitOut.flush();\n\n      return byteOut.toByteArray();\n    };\n\n    var lzwTable = function() {\n\n      var _map = {};\n      var _size = 0;\n\n      var _this = {};\n\n      _this.add = function(key) {\n        if (_this.contains(key) ) {\n          throw 'dup key:' + key;\n        }\n        _map[key] = _size;\n        _size += 1;\n      };\n\n      _this.size = function() {\n        return _size;\n      };\n\n      _this.indexOf = function(key) {\n        return _map[key];\n      };\n\n      _this.contains = function(key) {\n        return typeof _map[key] != 'undefined';\n      };\n\n      return _this;\n    };\n\n    return _this;\n  };\n\n  var createDataURL = function(width, height, getPixel) {\n    var gif = gifImage(width, height);\n    for (var y = 0; y < height; y += 1) {\n      for (var x = 0; x < width; x += 1) {\n        gif.setPixel(x, y, getPixel(x, y) );\n      }\n    }\n\n    var b = byteArrayOutputStream();\n    gif.write(b);\n\n    var base64 = base64EncodeOutputStream();\n    var bytes = b.toByteArray();\n    for (var i = 0; i < bytes.length; i += 1) {\n      base64.writeByte(bytes[i]);\n    }\n    base64.flush();\n\n    return 'data:image/gif;base64,' + base64;\n  };\n\n  //---------------------------------------------------------------------\n  // returns qrcode function.\n\n  return qrcode;\n}();\n\n// multibyte support\n!function() {\n\n  qrcode.stringToBytesFuncs['UTF-8'] = function(s) {\n    // http://stackoverflow.com/questions/18729405/how-to-convert-utf8-string-to-byte-array\n    function toUTF8Array(str) {\n      var utf8 = [];\n      for (var i=0; i < str.length; i++) {\n        var charcode = str.charCodeAt(i);\n        if (charcode < 0x80) utf8.push(charcode);\n        else if (charcode < 0x800) {\n          utf8.push(0xc0 | (charcode >> 6),\n              0x80 | (charcode & 0x3f));\n        }\n        else if (charcode < 0xd800 || charcode >= 0xe000) {\n          utf8.push(0xe0 | (charcode >> 12),\n              0x80 | ((charcode>>6) & 0x3f),\n              0x80 | (charcode & 0x3f));\n        }\n        // surrogate pair\n        else {\n          i++;\n          // UTF-16 encodes 0x10000-0x10FFFF by\n          // subtracting 0x10000 and splitting the\n          // 20 bits of 0x0-0xFFFFF into two halves\n          charcode = 0x10000 + (((charcode & 0x3ff)<<10)\n            | (str.charCodeAt(i) & 0x3ff));\n          utf8.push(0xf0 | (charcode >>18),\n              0x80 | ((charcode>>12) & 0x3f),\n              0x80 | ((charcode>>6) & 0x3f),\n              0x80 | (charcode & 0x3f));\n        }\n      }\n      return utf8;\n    }\n    return toUTF8Array(s);\n  };\n\n}();\n\n(function (factory) {\n  if (typeof define === 'function' && define.amd) {\n      define([], factory);\n  } else if (typeof exports === 'object') {\n      module.exports = factory();\n  }\n}(function () {\n    return qrcode;\n}));\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "import { Mode } from \"../types\";\n\ninterface Modes {\n  [key: string]: Mode;\n}\n\nexport default {\n  numeric: \"Numeric\",\n  alphanumeric: \"Alphanumeric\",\n  byte: \"Byte\",\n  kanji: \"Kanji\"\n} as Modes;\n", "import { UnknownObject } from \"../types\";\n\nconst isObject = (obj: Record<string, unknown>): boolean => !!obj && typeof obj === \"object\" && !Array.isArray(obj);\n\nexport default function mergeDeep(target: UnknownObject, ...sources: UnknownObject[]): UnknownObject {\n  if (!sources.length) return target;\n  const source = sources.shift();\n  if (source === undefined || !isObject(target) || !isObject(source)) return target;\n  target = { ...target };\n  Object.keys(source).forEach((key: string): void => {\n    const targetValue = target[key];\n    const sourceValue = source[key];\n\n    if (Array.isArray(targetValue) && Array.isArray(sourceValue)) {\n      target[key] = sourceValue;\n    } else if (isObject(targetValue) && isObject(sourceValue)) {\n      target[key] = mergeDeep(Object.assign({}, targetValue), sourceValue);\n    } else {\n      target[key] = sourceValue;\n    }\n  });\n\n  return mergeDeep(target, ...sources);\n}\n", "export default function downloadURI(uri: string, name: string): void {\n  const link = document.createElement(\"a\");\n  link.download = name;\n  link.href = uri;\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n}\n", "interface ErrorCorrectionPercents {\n  [key: string]: number;\n}\n\nexport default {\n  L: 0.07,\n  M: 0.15,\n  Q: 0.25,\n  H: 0.3\n} as ErrorCorrectionPercents;\n", "import dotTypes from \"../../constants/dotTypes\";\nimport { DotType, GetN<PERSON><PERSON>bor, DrawArgs, BasicFigureDrawArgs, RotateFigureArgs, Window } from \"../../types\";\n\nexport default class QRDot {\n  _element?: SVGElement;\n  _svg: SVGElement;\n  _type: DotType;\n  _window: Window;\n\n  constructor({ svg, type, window }: { svg: SVGElement; type: DotType; window: Window }) {\n    this._svg = svg;\n    this._type = type;\n    this._window = window;\n  }\n\n  draw(x: number, y: number, size: number, getNeighbor: GetNeighbor): void {\n    const type = this._type;\n    let drawFunction;\n\n    switch (type) {\n      case dotTypes.dots:\n        drawFunction = this._drawDot;\n        break;\n      case dotTypes.classy:\n        drawFunction = this._drawClassy;\n        break;\n      case dotTypes.classyRounded:\n        drawFunction = this._drawClassyRounded;\n        break;\n      case dotTypes.rounded:\n        drawFunction = this._drawRounded;\n        break;\n      case dotTypes.extraRounded:\n        drawFunction = this._drawExtraRounded;\n        break;\n      case dotTypes.square:\n      default:\n        drawFunction = this._drawSquare;\n    }\n\n    drawFunction.call(this, { x, y, size, getNeighbor });\n  }\n\n  _rotateFigure({ x, y, size, rotation = 0, draw }: RotateFigureArgs): void {\n    const cx = x + size / 2;\n    const cy = y + size / 2;\n\n    draw();\n    this._element?.setAttribute(\"transform\", `rotate(${(180 * rotation) / Math.PI},${cx},${cy})`);\n  }\n\n  _basicDot(args: BasicFigureDrawArgs): void {\n    const { size, x, y } = args;\n\n    this._rotateFigure({\n      ...args,\n      draw: () => {\n        this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"circle\");\n        this._element.setAttribute(\"cx\", String(x + size / 2));\n        this._element.setAttribute(\"cy\", String(y + size / 2));\n        this._element.setAttribute(\"r\", String(size / 2));\n      }\n    });\n  }\n\n  _basicSquare(args: BasicFigureDrawArgs): void {\n    const { size, x, y } = args;\n\n    this._rotateFigure({\n      ...args,\n      draw: () => {\n        this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"rect\");\n        this._element.setAttribute(\"x\", String(x));\n        this._element.setAttribute(\"y\", String(y));\n        this._element.setAttribute(\"width\", String(size));\n        this._element.setAttribute(\"height\", String(size));\n      }\n    });\n  }\n\n  //if rotation === 0 - right side is rounded\n  _basicSideRounded(args: BasicFigureDrawArgs): void {\n    const { size, x, y } = args;\n\n    this._rotateFigure({\n      ...args,\n      draw: () => {\n        this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n        this._element.setAttribute(\n          \"d\",\n          `M ${x} ${y}` + //go to top left position\n            `v ${size}` + //draw line to left bottom corner\n            `h ${size / 2}` + //draw line to left bottom corner + half of size right\n            `a ${size / 2} ${size / 2}, 0, 0, 0, 0 ${-size}` // draw rounded corner\n        );\n      }\n    });\n  }\n\n  //if rotation === 0 - top right corner is rounded\n  _basicCornerRounded(args: BasicFigureDrawArgs): void {\n    const { size, x, y } = args;\n\n    this._rotateFigure({\n      ...args,\n      draw: () => {\n        this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n        this._element.setAttribute(\n          \"d\",\n          `M ${x} ${y}` + //go to top left position\n            `v ${size}` + //draw line to left bottom corner\n            `h ${size}` + //draw line to right bottom corner\n            `v ${-size / 2}` + //draw line to right bottom corner + half of size top\n            `a ${size / 2} ${size / 2}, 0, 0, 0, ${-size / 2} ${-size / 2}` // draw rounded corner\n        );\n      }\n    });\n  }\n\n  //if rotation === 0 - top right corner is rounded\n  _basicCornerExtraRounded(args: BasicFigureDrawArgs): void {\n    const { size, x, y } = args;\n\n    this._rotateFigure({\n      ...args,\n      draw: () => {\n        this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n        this._element.setAttribute(\n          \"d\",\n          `M ${x} ${y}` + //go to top left position\n            `v ${size}` + //draw line to left bottom corner\n            `h ${size}` + //draw line to right bottom corner\n            `a ${size} ${size}, 0, 0, 0, ${-size} ${-size}` // draw rounded top right corner\n        );\n      }\n    });\n  }\n\n  //if rotation === 0 - left bottom and right top corners are rounded\n  _basicCornersRounded(args: BasicFigureDrawArgs): void {\n    const { size, x, y } = args;\n\n    this._rotateFigure({\n      ...args,\n      draw: () => {\n        this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n        this._element.setAttribute(\n          \"d\",\n          `M ${x} ${y}` + //go to left top position\n            `v ${size / 2}` + //draw line to left top corner + half of size bottom\n            `a ${size / 2} ${size / 2}, 0, 0, 0, ${size / 2} ${size / 2}` + // draw rounded left bottom corner\n            `h ${size / 2}` + //draw line to right bottom corner\n            `v ${-size / 2}` + //draw line to right bottom corner + half of size top\n            `a ${size / 2} ${size / 2}, 0, 0, 0, ${-size / 2} ${-size / 2}` // draw rounded right top corner\n        );\n      }\n    });\n  }\n\n  _drawDot({ x, y, size }: DrawArgs): void {\n    this._basicDot({ x, y, size, rotation: 0 });\n  }\n\n  _drawSquare({ x, y, size }: DrawArgs): void {\n    this._basicSquare({ x, y, size, rotation: 0 });\n  }\n\n  _drawRounded({ x, y, size, getNeighbor }: DrawArgs): void {\n    const leftNeighbor = getNeighbor ? +getNeighbor(-1, 0) : 0;\n    const rightNeighbor = getNeighbor ? +getNeighbor(1, 0) : 0;\n    const topNeighbor = getNeighbor ? +getNeighbor(0, -1) : 0;\n    const bottomNeighbor = getNeighbor ? +getNeighbor(0, 1) : 0;\n\n    const neighborsCount = leftNeighbor + rightNeighbor + topNeighbor + bottomNeighbor;\n\n    if (neighborsCount === 0) {\n      this._basicDot({ x, y, size, rotation: 0 });\n      return;\n    }\n\n    if (neighborsCount > 2 || (leftNeighbor && rightNeighbor) || (topNeighbor && bottomNeighbor)) {\n      this._basicSquare({ x, y, size, rotation: 0 });\n      return;\n    }\n\n    if (neighborsCount === 2) {\n      let rotation = 0;\n\n      if (leftNeighbor && topNeighbor) {\n        rotation = Math.PI / 2;\n      } else if (topNeighbor && rightNeighbor) {\n        rotation = Math.PI;\n      } else if (rightNeighbor && bottomNeighbor) {\n        rotation = -Math.PI / 2;\n      }\n\n      this._basicCornerRounded({ x, y, size, rotation });\n      return;\n    }\n\n    if (neighborsCount === 1) {\n      let rotation = 0;\n\n      if (topNeighbor) {\n        rotation = Math.PI / 2;\n      } else if (rightNeighbor) {\n        rotation = Math.PI;\n      } else if (bottomNeighbor) {\n        rotation = -Math.PI / 2;\n      }\n\n      this._basicSideRounded({ x, y, size, rotation });\n      return;\n    }\n  }\n\n  _drawExtraRounded({ x, y, size, getNeighbor }: DrawArgs): void {\n    const leftNeighbor = getNeighbor ? +getNeighbor(-1, 0) : 0;\n    const rightNeighbor = getNeighbor ? +getNeighbor(1, 0) : 0;\n    const topNeighbor = getNeighbor ? +getNeighbor(0, -1) : 0;\n    const bottomNeighbor = getNeighbor ? +getNeighbor(0, 1) : 0;\n\n    const neighborsCount = leftNeighbor + rightNeighbor + topNeighbor + bottomNeighbor;\n\n    if (neighborsCount === 0) {\n      this._basicDot({ x, y, size, rotation: 0 });\n      return;\n    }\n\n    if (neighborsCount > 2 || (leftNeighbor && rightNeighbor) || (topNeighbor && bottomNeighbor)) {\n      this._basicSquare({ x, y, size, rotation: 0 });\n      return;\n    }\n\n    if (neighborsCount === 2) {\n      let rotation = 0;\n\n      if (leftNeighbor && topNeighbor) {\n        rotation = Math.PI / 2;\n      } else if (topNeighbor && rightNeighbor) {\n        rotation = Math.PI;\n      } else if (rightNeighbor && bottomNeighbor) {\n        rotation = -Math.PI / 2;\n      }\n\n      this._basicCornerExtraRounded({ x, y, size, rotation });\n      return;\n    }\n\n    if (neighborsCount === 1) {\n      let rotation = 0;\n\n      if (topNeighbor) {\n        rotation = Math.PI / 2;\n      } else if (rightNeighbor) {\n        rotation = Math.PI;\n      } else if (bottomNeighbor) {\n        rotation = -Math.PI / 2;\n      }\n\n      this._basicSideRounded({ x, y, size, rotation });\n      return;\n    }\n  }\n\n  _drawClassy({ x, y, size, getNeighbor }: DrawArgs): void {\n    const leftNeighbor = getNeighbor ? +getNeighbor(-1, 0) : 0;\n    const rightNeighbor = getNeighbor ? +getNeighbor(1, 0) : 0;\n    const topNeighbor = getNeighbor ? +getNeighbor(0, -1) : 0;\n    const bottomNeighbor = getNeighbor ? +getNeighbor(0, 1) : 0;\n\n    const neighborsCount = leftNeighbor + rightNeighbor + topNeighbor + bottomNeighbor;\n\n    if (neighborsCount === 0) {\n      this._basicCornersRounded({ x, y, size, rotation: Math.PI / 2 });\n      return;\n    }\n\n    if (!leftNeighbor && !topNeighbor) {\n      this._basicCornerRounded({ x, y, size, rotation: -Math.PI / 2 });\n      return;\n    }\n\n    if (!rightNeighbor && !bottomNeighbor) {\n      this._basicCornerRounded({ x, y, size, rotation: Math.PI / 2 });\n      return;\n    }\n\n    this._basicSquare({ x, y, size, rotation: 0 });\n  }\n\n  _drawClassyRounded({ x, y, size, getNeighbor }: DrawArgs): void {\n    const leftNeighbor = getNeighbor ? +getNeighbor(-1, 0) : 0;\n    const rightNeighbor = getNeighbor ? +getNeighbor(1, 0) : 0;\n    const topNeighbor = getNeighbor ? +getNeighbor(0, -1) : 0;\n    const bottomNeighbor = getNeighbor ? +getNeighbor(0, 1) : 0;\n\n    const neighborsCount = leftNeighbor + rightNeighbor + topNeighbor + bottomNeighbor;\n\n    if (neighborsCount === 0) {\n      this._basicCornersRounded({ x, y, size, rotation: Math.PI / 2 });\n      return;\n    }\n\n    if (!leftNeighbor && !topNeighbor) {\n      this._basicCornerExtraRounded({ x, y, size, rotation: -Math.PI / 2 });\n      return;\n    }\n\n    if (!rightNeighbor && !bottomNeighbor) {\n      this._basicCornerExtraRounded({ x, y, size, rotation: Math.PI / 2 });\n      return;\n    }\n\n    this._basicSquare({ x, y, size, rotation: 0 });\n  }\n}\n", "import { DotTypes } from \"../types\";\n\nexport default {\n  dots: \"dots\",\n  rounded: \"rounded\",\n  classy: \"classy\",\n  classyRounded: \"classy-rounded\",\n  square: \"square\",\n  extraRounded: \"extra-rounded\"\n} as DotTypes;\n", "import { CornerSquareTypes } from \"../types\";\n\nexport default {\n  dot: \"dot\",\n  square: \"square\",\n  extraRounded: \"extra-rounded\"\n} as CornerSquareTypes;\n", "import cornerSquareTypes from \"../../constants/cornerSquareTypes\";\nimport { CornerSquareType, DrawArgs, BasicFigureDrawArgs, RotateFigureArgs, Window } from \"../../types\";\n\nexport const availableCornerSquareTypes = Object.values(cornerSquareTypes);\n\nexport default class QRCornerSquare {\n  _element?: SVGElement;\n  _svg: SVGElement;\n  _type: CornerSquareType;\n  _window: Window;\n\n  constructor({ svg, type, window }: { svg: SVGElement; type: CornerSquareType; window: Window }) {\n    this._svg = svg;\n    this._type = type;\n    this._window = window;\n  }\n\n  draw(x: number, y: number, size: number, rotation: number): void {\n    const type = this._type;\n    let drawFunction;\n\n    switch (type) {\n      case cornerSquareTypes.square:\n        drawFunction = this._drawSquare;\n        break;\n      case cornerSquareTypes.extraRounded:\n        drawFunction = this._drawExtraRounded;\n        break;\n      case cornerSquareTypes.dot:\n      default:\n        drawFunction = this._drawDot;\n    }\n\n    drawFunction.call(this, { x, y, size, rotation });\n  }\n\n  _rotateFigure({ x, y, size, rotation = 0, draw }: RotateFigureArgs): void {\n    const cx = x + size / 2;\n    const cy = y + size / 2;\n\n    draw();\n    this._element?.setAttribute(\"transform\", `rotate(${(180 * rotation) / Math.PI},${cx},${cy})`);\n  }\n\n  _basicDot(args: BasicFigureDrawArgs): void {\n    const { size, x, y } = args;\n    const dotSize = size / 7;\n\n    this._rotateFigure({\n      ...args,\n      draw: () => {\n        this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n        this._element.setAttribute(\"clip-rule\", \"evenodd\");\n        this._element.setAttribute(\n          \"d\",\n          `M ${x + size / 2} ${y}` + // M cx, y //  Move to top of ring\n            `a ${size / 2} ${size / 2} 0 1 0 0.1 0` + // a outerRadius, outerRadius, 0, 1, 0, 1, 0 // Draw outer arc, but don't close it\n            `z` + // Z // Close the outer shape\n            `m 0 ${dotSize}` + // m -1 outerRadius-innerRadius // Move to top point of inner radius\n            `a ${size / 2 - dotSize} ${size / 2 - dotSize} 0 1 1 -0.1 0` + // a innerRadius, innerRadius, 0, 1, 1, -1, 0 // Draw inner arc, but don't close it\n            `Z` // Z // Close the inner ring. Actually will still work without, but inner ring will have one unit missing in stroke\n        );\n      }\n    });\n  }\n\n  _basicSquare(args: BasicFigureDrawArgs): void {\n    const { size, x, y } = args;\n    const dotSize = size / 7;\n\n    this._rotateFigure({\n      ...args,\n      draw: () => {\n        this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n        this._element.setAttribute(\"clip-rule\", \"evenodd\");\n        this._element.setAttribute(\n          \"d\",\n          `M ${x} ${y}` +\n            `v ${size}` +\n            `h ${size}` +\n            `v ${-size}` +\n            `z` +\n            `M ${x + dotSize} ${y + dotSize}` +\n            `h ${size - 2 * dotSize}` +\n            `v ${size - 2 * dotSize}` +\n            `h ${-size + 2 * dotSize}` +\n            `z`\n        );\n      }\n    });\n  }\n\n  _basicExtraRounded(args: BasicFigureDrawArgs): void {\n    const { size, x, y } = args;\n    const dotSize = size / 7;\n\n    this._rotateFigure({\n      ...args,\n      draw: () => {\n        this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n        this._element.setAttribute(\"clip-rule\", \"evenodd\");\n        this._element.setAttribute(\n          \"d\",\n          `M ${x} ${y + 2.5 * dotSize}` +\n            `v ${2 * dotSize}` +\n            `a ${2.5 * dotSize} ${2.5 * dotSize}, 0, 0, 0, ${dotSize * 2.5} ${dotSize * 2.5}` +\n            `h ${2 * dotSize}` +\n            `a ${2.5 * dotSize} ${2.5 * dotSize}, 0, 0, 0, ${dotSize * 2.5} ${-dotSize * 2.5}` +\n            `v ${-2 * dotSize}` +\n            `a ${2.5 * dotSize} ${2.5 * dotSize}, 0, 0, 0, ${-dotSize * 2.5} ${-dotSize * 2.5}` +\n            `h ${-2 * dotSize}` +\n            `a ${2.5 * dotSize} ${2.5 * dotSize}, 0, 0, 0, ${-dotSize * 2.5} ${dotSize * 2.5}` +\n            `M ${x + 2.5 * dotSize} ${y + dotSize}` +\n            `h ${2 * dotSize}` +\n            `a ${1.5 * dotSize} ${1.5 * dotSize}, 0, 0, 1, ${dotSize * 1.5} ${dotSize * 1.5}` +\n            `v ${2 * dotSize}` +\n            `a ${1.5 * dotSize} ${1.5 * dotSize}, 0, 0, 1, ${-dotSize * 1.5} ${dotSize * 1.5}` +\n            `h ${-2 * dotSize}` +\n            `a ${1.5 * dotSize} ${1.5 * dotSize}, 0, 0, 1, ${-dotSize * 1.5} ${-dotSize * 1.5}` +\n            `v ${-2 * dotSize}` +\n            `a ${1.5 * dotSize} ${1.5 * dotSize}, 0, 0, 1, ${dotSize * 1.5} ${-dotSize * 1.5}`\n        );\n      }\n    });\n  }\n\n  _drawDot({ x, y, size, rotation }: DrawArgs): void {\n    this._basicDot({ x, y, size, rotation });\n  }\n\n  _drawSquare({ x, y, size, rotation }: DrawArgs): void {\n    this._basicSquare({ x, y, size, rotation });\n  }\n\n  _drawExtraRounded({ x, y, size, rotation }: DrawArgs): void {\n    this._basicExtraRounded({ x, y, size, rotation });\n  }\n}\n", "import { CornerDotTypes } from \"../types\";\n\nexport default {\n  dot: \"dot\",\n  square: \"square\"\n} as CornerDotTypes;", "import cornerDotTypes from \"../../constants/cornerDotTypes\";\nimport { CornerDotType, RotateFigureArgs, BasicFigureDrawArgs, DrawArgs, Window } from \"../../types\";\n\nexport const availableCornerDotTypes = Object.values(cornerDotTypes);\n\nexport default class QRCornerDot {\n  _element?: SVGElement;\n  _svg: SVGElement;\n  _type: CornerDotType;\n  _window: Window;\n\n  constructor({ svg, type, window }: { svg: SVGElement; type: CornerDotType; window: Window }) {\n    this._svg = svg;\n    this._type = type;\n    this._window = window;\n  }\n\n  draw(x: number, y: number, size: number, rotation: number): void {\n    const type = this._type;\n    let drawFunction;\n\n    switch (type) {\n      case cornerDotTypes.square:\n        drawFunction = this._drawSquare;\n        break;\n      case cornerDotTypes.dot:\n      default:\n        drawFunction = this._drawDot;\n    }\n\n    drawFunction.call(this, { x, y, size, rotation });\n  }\n\n  _rotateFigure({ x, y, size, rotation = 0, draw }: RotateFigureArgs): void {\n    const cx = x + size / 2;\n    const cy = y + size / 2;\n\n    draw();\n    this._element?.setAttribute(\"transform\", `rotate(${(180 * rotation) / Math.PI},${cx},${cy})`);\n  }\n\n  _basicDot(args: BasicFigureDrawArgs): void {\n    const { size, x, y } = args;\n\n    this._rotateFigure({\n      ...args,\n      draw: () => {\n        this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"circle\");\n        this._element.setAttribute(\"cx\", String(x + size / 2));\n        this._element.setAttribute(\"cy\", String(y + size / 2));\n        this._element.setAttribute(\"r\", String(size / 2));\n      }\n    });\n  }\n\n  _basicSquare(args: BasicFigureDrawArgs): void {\n    const { size, x, y } = args;\n\n    this._rotateFigure({\n      ...args,\n      draw: () => {\n        this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"rect\");\n        this._element.setAttribute(\"x\", String(x));\n        this._element.setAttribute(\"y\", String(y));\n        this._element.setAttribute(\"width\", String(size));\n        this._element.setAttribute(\"height\", String(size));\n      }\n    });\n  }\n\n  _drawDot({ x, y, size, rotation }: DrawArgs): void {\n    this._basicDot({ x, y, size, rotation });\n  }\n\n  _drawSquare({ x, y, size, rotation }: DrawArgs): void {\n    this._basicSquare({ x, y, size, rotation });\n  }\n}\n", "import { GradientTypes } from \"../types\";\n\nexport default {\n  radial: \"radial\",\n  linear: \"linear\"\n} as GradientTypes;\n", "import { ShapeTypes } from \"../types\";\n\nexport default {\n  square: \"square\",\n  circle: \"circle\"\n} as ShapeTypes;\n", "import calculateImageSize from \"../tools/calculateImageSize\";\nimport toDataUrl from \"../tools/toDataUrl\";\nimport errorCorrectionPercents from \"../constants/errorCorrectionPercents\";\nimport QRDot from \"../figures/dot/QRDot\";\nimport QRCornerSquare, { availableCornerSquareTypes } from \"../figures/cornerSquare/QRCornerSquare\";\nimport QRCornerDot, { availableCornerDotTypes } from \"../figures/cornerDot/QRCornerDot\";\nimport { RequiredOptions } from \"./QROptions\";\nimport gradientTypes from \"../constants/gradientTypes\";\nimport shapeTypes from \"../constants/shapeTypes\";\nimport { DotType, QRCode, FilterFunction, Gradient, Window } from \"../types\";\nimport { Image } from \"canvas\";\n\nconst squareMask = [\n  [1, 1, 1, 1, 1, 1, 1],\n  [1, 0, 0, 0, 0, 0, 1],\n  [1, 0, 0, 0, 0, 0, 1],\n  [1, 0, 0, 0, 0, 0, 1],\n  [1, 0, 0, 0, 0, 0, 1],\n  [1, 0, 0, 0, 0, 0, 1],\n  [1, 1, 1, 1, 1, 1, 1]\n];\n\nconst dotMask = [\n  [0, 0, 0, 0, 0, 0, 0],\n  [0, 0, 0, 0, 0, 0, 0],\n  [0, 0, 1, 1, 1, 0, 0],\n  [0, 0, 1, 1, 1, 0, 0],\n  [0, 0, 1, 1, 1, 0, 0],\n  [0, 0, 0, 0, 0, 0, 0],\n  [0, 0, 0, 0, 0, 0, 0]\n];\n\nexport default class QRSVG {\n  _window: Window;\n  _element: SVGElement;\n  _defs: SVGElement;\n  _backgroundClipPath?: SVGElement;\n  _dotsClipPath?: SVGElement;\n  _cornersSquareClipPath?: SVGElement;\n  _cornersDotClipPath?: SVGElement;\n  _options: RequiredOptions;\n  _qr?: QRCode;\n  _image?: HTMLImageElement | Image;\n  _imageUri?: string;\n  _instanceId: number;\n\n  static instanceCount = 0;\n\n  //TODO don't pass all options to this class\n  constructor(options: RequiredOptions, window: Window) {\n    this._window = window;\n    this._element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n    this._element.setAttribute(\"width\", String(options.width));\n    this._element.setAttribute(\"height\", String(options.height));\n    this._element.setAttribute(\"xmlns:xlink\", \"http://www.w3.org/1999/xlink\");\n    if (!options.dotsOptions.roundSize) {\n      this._element.setAttribute(\"shape-rendering\", \"crispEdges\");\n    }\n    this._element.setAttribute(\"viewBox\", `0 0 ${options.width} ${options.height}`);\n    this._defs = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"defs\");\n    this._element.appendChild(this._defs);\n    this._imageUri = options.image;\n    this._instanceId = QRSVG.instanceCount++;\n    this._options = options;\n  }\n\n  get width(): number {\n    return this._options.width;\n  }\n\n  get height(): number {\n    return this._options.height;\n  }\n\n  getElement(): SVGElement {\n    return this._element;\n  }\n\n  async drawQR(qr: QRCode): Promise<void> {\n    const count = qr.getModuleCount();\n    const minSize = Math.min(this._options.width, this._options.height) - this._options.margin * 2;\n    const realQRSize = this._options.shape === shapeTypes.circle ? minSize / Math.sqrt(2) : minSize;\n    const dotSize = this._roundSize(realQRSize / count);\n    let drawImageSize = {\n      hideXDots: 0,\n      hideYDots: 0,\n      width: 0,\n      height: 0\n    };\n\n    this._qr = qr;\n\n    if (this._options.image) {\n      //We need it to get image size\n      await this.loadImage();\n      if (!this._image) return;\n      const { imageOptions, qrOptions } = this._options;\n      const coverLevel = imageOptions.imageSize * errorCorrectionPercents[qrOptions.errorCorrectionLevel];\n      const maxHiddenDots = Math.floor(coverLevel * count * count);\n\n      drawImageSize = calculateImageSize({\n        originalWidth: this._image.width,\n        originalHeight: this._image.height,\n        maxHiddenDots,\n        maxHiddenAxisDots: count - 14,\n        dotSize\n      });\n    }\n\n    this.drawBackground();\n    this.drawDots((row: number, col: number): boolean => {\n      if (this._options.imageOptions.hideBackgroundDots) {\n        if (\n          row >= (count - drawImageSize.hideYDots) / 2 &&\n          row < (count + drawImageSize.hideYDots) / 2 &&\n          col >= (count - drawImageSize.hideXDots) / 2 &&\n          col < (count + drawImageSize.hideXDots) / 2\n        ) {\n          return false;\n        }\n      }\n\n      if (squareMask[row]?.[col] || squareMask[row - count + 7]?.[col] || squareMask[row]?.[col - count + 7]) {\n        return false;\n      }\n\n      if (dotMask[row]?.[col] || dotMask[row - count + 7]?.[col] || dotMask[row]?.[col - count + 7]) {\n        return false;\n      }\n\n      return true;\n    });\n    this.drawCorners();\n\n    if (this._options.image) {\n      await this.drawImage({ width: drawImageSize.width, height: drawImageSize.height, count, dotSize });\n    }\n  }\n\n  drawBackground(): void {\n    const element = this._element;\n    const options = this._options;\n\n    if (element) {\n      const gradientOptions = options.backgroundOptions?.gradient;\n      const color = options.backgroundOptions?.color;\n      let height = options.height;\n      let width = options.width;\n\n      if (gradientOptions || color) {\n        const element = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"rect\");\n        this._backgroundClipPath = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"clipPath\");\n        this._backgroundClipPath.setAttribute(\"id\", `clip-path-background-color-${this._instanceId}`);\n        this._defs.appendChild(this._backgroundClipPath);\n\n        if (options.backgroundOptions?.round) {\n          height = width = Math.min(options.width, options.height);\n          element.setAttribute(\"rx\", String((height / 2) * options.backgroundOptions.round));\n        }\n\n        element.setAttribute(\"x\", String(this._roundSize((options.width - width) / 2)));\n        element.setAttribute(\"y\", String(this._roundSize((options.height - height) / 2)));\n        element.setAttribute(\"width\", String(width));\n        element.setAttribute(\"height\", String(height));\n\n        this._backgroundClipPath.appendChild(element);\n\n        this._createColor({\n          options: gradientOptions,\n          color: color,\n          additionalRotation: 0,\n          x: 0,\n          y: 0,\n          height: options.height,\n          width: options.width,\n          name: `background-color-${this._instanceId}`\n        });\n      }\n    }\n  }\n\n  drawDots(filter?: FilterFunction): void {\n    if (!this._qr) {\n      throw \"QR code is not defined\";\n    }\n\n    const options = this._options;\n    const count = this._qr.getModuleCount();\n\n    if (count > options.width || count > options.height) {\n      throw \"The canvas is too small.\";\n    }\n\n    const minSize = Math.min(options.width, options.height) - options.margin * 2;\n    const realQRSize = options.shape === shapeTypes.circle ? minSize / Math.sqrt(2) : minSize;\n    const dotSize = this._roundSize(realQRSize / count);\n    const xBeginning = this._roundSize((options.width - count * dotSize) / 2);\n    const yBeginning = this._roundSize((options.height - count * dotSize) / 2);\n    const dot = new QRDot({\n      svg: this._element,\n      type: options.dotsOptions.type,\n      window: this._window\n    });\n\n    this._dotsClipPath = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"clipPath\");\n    this._dotsClipPath.setAttribute(\"id\", `clip-path-dot-color-${this._instanceId}`);\n    this._defs.appendChild(this._dotsClipPath);\n\n    this._createColor({\n      options: options.dotsOptions?.gradient,\n      color: options.dotsOptions.color,\n      additionalRotation: 0,\n      x: 0,\n      y: 0,\n      height: options.height,\n      width: options.width,\n      name: `dot-color-${this._instanceId}`\n    });\n\n    for (let row = 0; row < count; row++) {\n      for (let col = 0; col < count; col++) {\n        if (filter && !filter(row, col)) {\n          continue;\n        }\n        if (!this._qr?.isDark(row, col)) {\n          continue;\n        }\n\n        dot.draw(\n          xBeginning + col * dotSize,\n          yBeginning + row * dotSize,\n          dotSize,\n          (xOffset: number, yOffset: number): boolean => {\n            if (col + xOffset < 0 || row + yOffset < 0 || col + xOffset >= count || row + yOffset >= count) return false;\n            if (filter && !filter(row + yOffset, col + xOffset)) return false;\n            return !!this._qr && this._qr.isDark(row + yOffset, col + xOffset);\n          }\n        );\n\n        if (dot._element && this._dotsClipPath) {\n          this._dotsClipPath.appendChild(dot._element);\n        }\n      }\n    }\n\n    if (options.shape === shapeTypes.circle) {\n      const additionalDots = this._roundSize((minSize / dotSize - count) / 2);\n      const fakeCount = count + additionalDots * 2;\n      const xFakeBeginning = xBeginning - additionalDots * dotSize;\n      const yFakeBeginning = yBeginning - additionalDots * dotSize;\n      const fakeMatrix: number[][] = [];\n      const center = this._roundSize(fakeCount / 2);\n\n      for (let row = 0; row < fakeCount; row++) {\n        fakeMatrix[row] = [];\n        for (let col = 0; col < fakeCount; col++) {\n          if (\n            row >= additionalDots - 1 &&\n            row <= fakeCount - additionalDots &&\n            col >= additionalDots - 1 &&\n            col <= fakeCount - additionalDots\n          ) {\n            fakeMatrix[row][col] = 0;\n            continue;\n          }\n\n          if (Math.sqrt((row - center) * (row - center) + (col - center) * (col - center)) > center) {\n            fakeMatrix[row][col] = 0;\n            continue;\n          }\n\n          //Get random dots from QR code to show it outside of QR code\n          fakeMatrix[row][col] = this._qr.isDark(\n            col - 2 * additionalDots < 0 ? col : col >= count ? col - 2 * additionalDots : col - additionalDots,\n            row - 2 * additionalDots < 0 ? row : row >= count ? row - 2 * additionalDots : row - additionalDots\n          )\n            ? 1\n            : 0;\n        }\n      }\n\n      for (let row = 0; row < fakeCount; row++) {\n        for (let col = 0; col < fakeCount; col++) {\n          if (!fakeMatrix[row][col]) continue;\n\n          dot.draw(\n            xFakeBeginning + col * dotSize,\n            yFakeBeginning + row * dotSize,\n            dotSize,\n            (xOffset: number, yOffset: number): boolean => {\n              return !!fakeMatrix[row + yOffset]?.[col + xOffset];\n            }\n          );\n          if (dot._element && this._dotsClipPath) {\n            this._dotsClipPath.appendChild(dot._element);\n          }\n        }\n      }\n    }\n  }\n\n  drawCorners(): void {\n    if (!this._qr) {\n      throw \"QR code is not defined\";\n    }\n\n    const element = this._element;\n    const options = this._options;\n\n    if (!element) {\n      throw \"Element code is not defined\";\n    }\n\n    const count = this._qr.getModuleCount();\n    const minSize = Math.min(options.width, options.height) - options.margin * 2;\n    const realQRSize = options.shape === shapeTypes.circle ? minSize / Math.sqrt(2) : minSize;\n    const dotSize = this._roundSize(realQRSize / count);\n    const cornersSquareSize = dotSize * 7;\n    const cornersDotSize = dotSize * 3;\n    const xBeginning = this._roundSize((options.width - count * dotSize) / 2);\n    const yBeginning = this._roundSize((options.height - count * dotSize) / 2);\n\n    [\n      [0, 0, 0],\n      [1, 0, Math.PI / 2],\n      [0, 1, -Math.PI / 2]\n    ].forEach(([column, row, rotation]) => {\n      const x = xBeginning + column * dotSize * (count - 7);\n      const y = yBeginning + row * dotSize * (count - 7);\n      let cornersSquareClipPath = this._dotsClipPath;\n      let cornersDotClipPath = this._dotsClipPath;\n\n      if (options.cornersSquareOptions?.gradient || options.cornersSquareOptions?.color) {\n        cornersSquareClipPath = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"clipPath\");\n        cornersSquareClipPath.setAttribute(\"id\", `clip-path-corners-square-color-${column}-${row}-${this._instanceId}`);\n        this._defs.appendChild(cornersSquareClipPath);\n        this._cornersSquareClipPath = this._cornersDotClipPath = cornersDotClipPath = cornersSquareClipPath;\n\n        this._createColor({\n          options: options.cornersSquareOptions?.gradient,\n          color: options.cornersSquareOptions?.color,\n          additionalRotation: rotation,\n          x,\n          y,\n          height: cornersSquareSize,\n          width: cornersSquareSize,\n          name: `corners-square-color-${column}-${row}-${this._instanceId}`\n        });\n      }\n\n      if (options.cornersSquareOptions?.type && availableCornerSquareTypes.includes(options.cornersSquareOptions.type)) {\n        const cornersSquare = new QRCornerSquare({\n          svg: this._element,\n          type: options.cornersSquareOptions.type,\n          window: this._window\n        });\n\n        cornersSquare.draw(x, y, cornersSquareSize, rotation);\n\n        if (cornersSquare._element && cornersSquareClipPath) {\n          cornersSquareClipPath.appendChild(cornersSquare._element);\n        }\n      } else {\n        const dot = new QRDot({\n          svg: this._element,\n          type: (options.cornersSquareOptions?.type as DotType) || options.dotsOptions.type,\n          window: this._window\n        });\n\n        for (let row = 0; row < squareMask.length; row++) {\n          for (let col = 0; col < squareMask[row].length; col++) {\n            if (!squareMask[row]?.[col]) {\n              continue;\n            }\n\n            dot.draw(\n              x + col * dotSize,\n              y + row * dotSize,\n              dotSize,\n              (xOffset: number, yOffset: number): boolean => !!squareMask[row + yOffset]?.[col + xOffset]\n            );\n\n            if (dot._element && cornersSquareClipPath) {\n              cornersSquareClipPath.appendChild(dot._element);\n            }\n          }\n        }\n      }\n\n      if (options.cornersDotOptions?.gradient || options.cornersDotOptions?.color) {\n        cornersDotClipPath = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"clipPath\");\n        cornersDotClipPath.setAttribute(\"id\", `clip-path-corners-dot-color-${column}-${row}-${this._instanceId}`);\n        this._defs.appendChild(cornersDotClipPath);\n        this._cornersDotClipPath = cornersDotClipPath;\n\n        this._createColor({\n          options: options.cornersDotOptions?.gradient,\n          color: options.cornersDotOptions?.color,\n          additionalRotation: rotation,\n          x: x + dotSize * 2,\n          y: y + dotSize * 2,\n          height: cornersDotSize,\n          width: cornersDotSize,\n          name: `corners-dot-color-${column}-${row}-${this._instanceId}`\n        });\n      }\n\n      if (options.cornersDotOptions?.type && availableCornerDotTypes.includes(options.cornersDotOptions.type)) {\n        const cornersDot = new QRCornerDot({\n          svg: this._element,\n          type: options.cornersDotOptions.type,\n          window: this._window\n        });\n\n        cornersDot.draw(x + dotSize * 2, y + dotSize * 2, cornersDotSize, rotation);\n\n        if (cornersDot._element && cornersDotClipPath) {\n          cornersDotClipPath.appendChild(cornersDot._element);\n        }\n      } else {\n        const dot = new QRDot({\n          svg: this._element,\n          type: (options.cornersDotOptions?.type as DotType) || options.dotsOptions.type,\n          window: this._window\n        });\n\n        for (let row = 0; row < dotMask.length; row++) {\n          for (let col = 0; col < dotMask[row].length; col++) {\n            if (!dotMask[row]?.[col]) {\n              continue;\n            }\n\n            dot.draw(\n              x + col * dotSize,\n              y + row * dotSize,\n              dotSize,\n              (xOffset: number, yOffset: number): boolean => !!dotMask[row + yOffset]?.[col + xOffset]\n            );\n\n            if (dot._element && cornersDotClipPath) {\n              cornersDotClipPath.appendChild(dot._element);\n            }\n          }\n        }\n      }\n    });\n  }\n\n  loadImage(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const options = this._options;\n\n      if (!options.image) {\n        return reject(\"Image is not defined\");\n      }\n\n      if (options.nodeCanvas?.loadImage) {\n        options.nodeCanvas\n          .loadImage(options.image)\n          .then((image: Image) => {\n            this._image = image;\n            if (this._options.imageOptions.saveAsBlob) {\n              const canvas = options.nodeCanvas?.createCanvas( this._image.width,  this._image.height);\n              canvas?.getContext('2d')?.drawImage(image, 0, 0);\n              this._imageUri = canvas?.toDataURL();\n            }\n            resolve();\n          })\n          .catch(reject);\n      } else {\n        const image = new this._window.Image();\n\n        if (typeof options.imageOptions.crossOrigin === \"string\") {\n          image.crossOrigin = options.imageOptions.crossOrigin;\n        }\n\n        this._image = image;\n        image.onload = async () => {\n          if (this._options.imageOptions.saveAsBlob) {\n            this._imageUri = await toDataUrl(options.image || \"\", this._window);\n          }\n          resolve();\n        };\n        image.src = options.image;\n      }\n    });\n  }\n\n  async drawImage({\n    width,\n    height,\n    count,\n    dotSize\n  }: {\n    width: number;\n    height: number;\n    count: number;\n    dotSize: number;\n  }): Promise<void> {\n    const options = this._options;\n    const xBeginning = this._roundSize((options.width - count * dotSize) / 2);\n    const yBeginning = this._roundSize((options.height - count * dotSize) / 2);\n    const dx = xBeginning + this._roundSize(options.imageOptions.margin + (count * dotSize - width) / 2);\n    const dy = yBeginning + this._roundSize(options.imageOptions.margin + (count * dotSize - height) / 2);\n    const dw = width - options.imageOptions.margin * 2;\n    const dh = height - options.imageOptions.margin * 2;\n\n    const image = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"image\");\n    image.setAttribute(\"href\", this._imageUri || \"\");\n    image.setAttribute(\"xlink:href\", this._imageUri || \"\");\n    image.setAttribute(\"x\", String(dx));\n    image.setAttribute(\"y\", String(dy));\n    image.setAttribute(\"width\", `${dw}px`);\n    image.setAttribute(\"height\", `${dh}px`);\n\n    this._element.appendChild(image);\n  }\n\n  _createColor({\n    options,\n    color,\n    additionalRotation,\n    x,\n    y,\n    height,\n    width,\n    name\n  }: {\n    options?: Gradient;\n    color?: string;\n    additionalRotation: number;\n    x: number;\n    y: number;\n    height: number;\n    width: number;\n    name: string;\n  }): void {\n    const size = width > height ? width : height;\n    const rect = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"rect\");\n    rect.setAttribute(\"x\", String(x));\n    rect.setAttribute(\"y\", String(y));\n    rect.setAttribute(\"height\", String(height));\n    rect.setAttribute(\"width\", String(width));\n    rect.setAttribute(\"clip-path\", `url('#clip-path-${name}')`);\n\n    if (options) {\n      let gradient: SVGElement;\n      if (options.type === gradientTypes.radial) {\n        gradient = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"radialGradient\");\n        gradient.setAttribute(\"id\", name);\n        gradient.setAttribute(\"gradientUnits\", \"userSpaceOnUse\");\n        gradient.setAttribute(\"fx\", String(x + width / 2));\n        gradient.setAttribute(\"fy\", String(y + height / 2));\n        gradient.setAttribute(\"cx\", String(x + width / 2));\n        gradient.setAttribute(\"cy\", String(y + height / 2));\n        gradient.setAttribute(\"r\", String(size / 2));\n      } else {\n        const rotation = ((options.rotation || 0) + additionalRotation) % (2 * Math.PI);\n        const positiveRotation = (rotation + 2 * Math.PI) % (2 * Math.PI);\n        let x0 = x + width / 2;\n        let y0 = y + height / 2;\n        let x1 = x + width / 2;\n        let y1 = y + height / 2;\n\n        if (\n          (positiveRotation >= 0 && positiveRotation <= 0.25 * Math.PI) ||\n          (positiveRotation > 1.75 * Math.PI && positiveRotation <= 2 * Math.PI)\n        ) {\n          x0 = x0 - width / 2;\n          y0 = y0 - (height / 2) * Math.tan(rotation);\n          x1 = x1 + width / 2;\n          y1 = y1 + (height / 2) * Math.tan(rotation);\n        } else if (positiveRotation > 0.25 * Math.PI && positiveRotation <= 0.75 * Math.PI) {\n          y0 = y0 - height / 2;\n          x0 = x0 - width / 2 / Math.tan(rotation);\n          y1 = y1 + height / 2;\n          x1 = x1 + width / 2 / Math.tan(rotation);\n        } else if (positiveRotation > 0.75 * Math.PI && positiveRotation <= 1.25 * Math.PI) {\n          x0 = x0 + width / 2;\n          y0 = y0 + (height / 2) * Math.tan(rotation);\n          x1 = x1 - width / 2;\n          y1 = y1 - (height / 2) * Math.tan(rotation);\n        } else if (positiveRotation > 1.25 * Math.PI && positiveRotation <= 1.75 * Math.PI) {\n          y0 = y0 + height / 2;\n          x0 = x0 + width / 2 / Math.tan(rotation);\n          y1 = y1 - height / 2;\n          x1 = x1 - width / 2 / Math.tan(rotation);\n        }\n\n        gradient = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"linearGradient\");\n        gradient.setAttribute(\"id\", name);\n        gradient.setAttribute(\"gradientUnits\", \"userSpaceOnUse\");\n        gradient.setAttribute(\"x1\", String(Math.round(x0)));\n        gradient.setAttribute(\"y1\", String(Math.round(y0)));\n        gradient.setAttribute(\"x2\", String(Math.round(x1)));\n        gradient.setAttribute(\"y2\", String(Math.round(y1)));\n      }\n\n      options.colorStops.forEach(({ offset, color }: { offset: number; color: string }) => {\n        const stop = this._window.document.createElementNS(\"http://www.w3.org/2000/svg\", \"stop\");\n        stop.setAttribute(\"offset\", `${100 * offset}%`);\n        stop.setAttribute(\"stop-color\", color);\n        gradient.appendChild(stop);\n      });\n\n      rect.setAttribute(\"fill\", `url('#${name}')`);\n      this._defs.appendChild(gradient);\n    } else if (color) {\n      rect.setAttribute(\"fill\", color);\n    }\n\n    this._element.appendChild(rect);\n  }\n\n  _roundSize = (value: number) => {\n    if (this._options.dotsOptions.roundSize) {\n      return Math.floor(value);\n    }\n    return value;\n  }\n}\n", "interface ImageSizeOptions {\n  originalHeight: number;\n  originalWidth: number;\n  maxHiddenDots: number;\n  maxHiddenAxisDots?: number;\n  dotSize: number;\n}\n\ninterface ImageSizeResult {\n  height: number;\n  width: number;\n  hideYDots: number;\n  hideXDots: number;\n}\n\nexport default function calculateImageSize({\n  originalHeight,\n  originalWidth,\n  maxHiddenDots,\n  maxHiddenAxisDots,\n  dotSize\n}: ImageSizeOptions): ImageSizeResult {\n  const hideDots = { x: 0, y: 0 };\n  const imageSize = { x: 0, y: 0 };\n\n  if (originalHeight <= 0 || originalWidth <= 0 || maxHiddenDots <= 0 || dotSize <= 0) {\n    return {\n      height: 0,\n      width: 0,\n      hideYDots: 0,\n      hideXDots: 0\n    };\n  }\n\n  const k = originalHeight / originalWidth;\n\n  //Getting the maximum possible axis hidden dots\n  hideDots.x = Math.floor(Math.sqrt(maxHiddenDots / k));\n  //The count of hidden dot's can't be less than 1\n  if (hideDots.x <= 0) hideDots.x = 1;\n  //Check the limit of the maximum allowed axis hidden dots\n  if (maxHiddenAxisDots && maxHiddenAxisDots < hideDots.x) hideDots.x = maxHiddenAxisDots;\n  //The count of dots should be odd\n  if (hideDots.x % 2 === 0) hideDots.x--;\n  imageSize.x = hideDots.x * dotSize;\n  //Calculate opposite axis hidden dots based on axis value.\n  //The value will be odd.\n  //We use ceil to prevent dots covering by the image.\n  hideDots.y = 1 + 2 * Math.ceil((hideDots.x * k - 1) / 2);\n  imageSize.y = Math.round(imageSize.x * k);\n  //If the result dots count is bigger than max - then decrease size and calculate again\n  if (hideDots.y * hideDots.x > maxHiddenDots || (maxHiddenAxisDots && maxHiddenAxisDots < hideDots.y)) {\n    if (maxHiddenAxisDots && maxHiddenAxisDots < hideDots.y) {\n      hideDots.y = maxHiddenAxisDots;\n      if (hideDots.y % 2 === 0) hideDots.x--;\n    } else {\n      hideDots.y -= 2;\n    }\n    imageSize.y = hideDots.y * dotSize;\n    hideDots.x = 1 + 2 * Math.ceil((hideDots.y / k - 1) / 2);\n    imageSize.x = Math.round(imageSize.y / k);\n  }\n\n  return {\n    height: imageSize.y,\n    width: imageSize.x,\n    hideYDots: hideDots.y,\n    hideXDots: hideDots.x\n  };\n}\n", "import { Window } from \"../types\";\n\nexport default async function toDataURL(url: string, window: Window): Promise<string> {\n  return new Promise((resolve) => {\n    const xhr = new window.XMLHttpRequest();\n    xhr.onload = function () {\n      const reader = new window.FileReader();\n      reader.onloadend = function () {\n        resolve(reader.result as string);\n      };\n      reader.readAsDataURL(xhr.response);\n    };\n    xhr.open(\"GET\", url);\n    xhr.responseType = \"blob\";\n    xhr.send();\n  });\n}\n", "import { DrawTypes } from \"../types\";\n\nexport default {\n  canvas: \"canvas\",\n  svg: \"svg\"\n} as DrawTypes;\n", "import { TypeNumber } from \"../types\";\n\ninterface TypesMap {\n  [key: number]: TypeNumber;\n}\n\nconst qrTypes: TypesMap = {};\n\nfor (let type = 0; type <= 40; type++) {\n  qrTypes[type] = type as TypeNumber;\n}\n\n// 0 types is autodetect\n\n// types = {\n//     0: 0,\n//     1: 1,\n//     ...\n//     40: 40\n// }\n\nexport default qrTypes;\n", "import qrTypes from \"../constants/qrTypes\";\nimport drawTypes from \"../constants/drawTypes\";\nimport shapeTypes from \"../constants/shapeTypes\";\nimport errorCorrectionLevels from \"../constants/errorCorrectionLevels\";\nimport { ShapeType, DotType, Options, TypeNumber, ErrorCorrectionLevel, Mode, DrawType, Gradient } from \"../types\";\n\nexport interface RequiredOptions extends Options {\n  type: DrawType;\n  shape: ShapeType;\n  width: number;\n  height: number;\n  margin: number;\n  data: string;\n  qrOptions: {\n    typeNumber: TypeNumber;\n    mode?: Mode;\n    errorCorrectionLevel: ErrorCorrectionLevel;\n  };\n  imageOptions: {\n    saveAsBlob: boolean;\n    hideBackgroundDots: boolean;\n    imageSize: number;\n    crossOrigin?: string;\n    margin: number;\n  };\n  dotsOptions: {\n    type: DotType;\n    color: string;\n    gradient?: Gradient;\n    roundSize?: boolean;\n  };\n  backgroundOptions: {\n    round: number;\n    color: string;\n    gradient?: Gradient;\n  };\n}\n\nconst defaultOptions: RequiredOptions = {\n  type: drawTypes.canvas,\n  shape: shapeTypes.square,\n  width: 300,\n  height: 300,\n  data: \"\",\n  margin: 0,\n  qrOptions: {\n    typeNumber: qrTypes[0],\n    mode: undefined,\n    errorCorrectionLevel: errorCorrectionLevels.Q\n  },\n  imageOptions: {\n    saveAsBlob: true,\n    hideBackgroundDots: true,\n    imageSize: 0.4,\n    crossOrigin: undefined,\n    margin: 0\n  },\n  dotsOptions: {\n    type: \"square\",\n    color: \"#000\",\n    roundSize: true,\n  },\n  backgroundOptions: {\n    round: 0,\n    color: \"#fff\"\n  }\n};\n\nexport default defaultOptions;\n", "import { ErrorCorrectionLevel } from \"../types\";\n\ninterface ErrorCorrectionLevels {\n  [key: string]: ErrorCorrectionLevel;\n}\n\nexport default {\n  L: \"L\",\n  M: \"M\",\n  Q: \"Q\",\n  H: \"H\"\n} as ErrorCorrectionLevels;\n", "import { RequiredOptions } from \"../core/QROptions\";\nimport { Gradient } from \"../types\";\n\nfunction sanitizeGradient(gradient: Gradient): Gradient {\n  const newGradient = { ...gradient };\n\n  if (!newGradient.colorStops || !newGradient.colorStops.length) {\n    throw \"Field 'colorStops' is required in gradient\";\n  }\n\n  if (newGradient.rotation) {\n    newGradient.rotation = Number(newGradient.rotation);\n  } else {\n    newGradient.rotation = 0;\n  }\n\n  newGradient.colorStops = newGradient.colorStops.map((colorStop: { offset: number; color: string }) => ({\n    ...colorStop,\n    offset: Number(colorStop.offset)\n  }));\n\n  return newGradient;\n}\n\nexport default function sanitizeOptions(options: RequiredOptions): RequiredOptions {\n  const newOptions = { ...options };\n\n  newOptions.width = Number(newOptions.width);\n  newOptions.height = Number(newOptions.height);\n  newOptions.margin = Number(newOptions.margin);\n  newOptions.imageOptions = {\n    ...newOptions.imageOptions,\n    hideBackgroundDots: Boolean(newOptions.imageOptions.hideBackgroundDots),\n    imageSize: Number(newOptions.imageOptions.imageSize),\n    margin: Number(newOptions.imageOptions.margin)\n  };\n\n  if (newOptions.margin > Math.min(newOptions.width, newOptions.height)) {\n    newOptions.margin = Math.min(newOptions.width, newOptions.height);\n  }\n\n  newOptions.dotsOptions = {\n    ...newOptions.dotsOptions\n  };\n  if (newOptions.dotsOptions.gradient) {\n    newOptions.dotsOptions.gradient = sanitizeGradient(newOptions.dotsOptions.gradient);\n  }\n\n  if (newOptions.cornersSquareOptions) {\n    newOptions.cornersSquareOptions = {\n      ...newOptions.cornersSquareOptions\n    };\n    if (newOptions.cornersSquareOptions.gradient) {\n      newOptions.cornersSquareOptions.gradient = sanitizeGradient(newOptions.cornersSquareOptions.gradient);\n    }\n  }\n\n  if (newOptions.cornersDotOptions) {\n    newOptions.cornersDotOptions = {\n      ...newOptions.cornersDotOptions\n    };\n    if (newOptions.cornersDotOptions.gradient) {\n      newOptions.cornersDotOptions.gradient = sanitizeGradient(newOptions.cornersDotOptions.gradient);\n    }\n  }\n\n  if (newOptions.backgroundOptions) {\n    newOptions.backgroundOptions = {\n      ...newOptions.backgroundOptions\n    };\n    if (newOptions.backgroundOptions.gradient) {\n      newOptions.backgroundOptions.gradient = sanitizeGradient(newOptions.backgroundOptions.gradient);\n    }\n  }\n\n  return newOptions;\n}\n", "export default function getMimeType(extension: string) {\n  if (!extension) throw new Error('Extension must be defined');\n  if (extension[0] === \".\") {\n    extension = extension.substring(1);\n  }\n  const type = {\n    \"bmp\": \"image/bmp\",\n    \"gif\": \"image/gif\",\n    \"ico\": \"image/vnd.microsoft.icon\",\n    \"jpeg\": \"image/jpeg\",\n    \"jpg\": \"image/jpeg\",\n    \"png\": \"image/png\",\n    \"svg\": \"image/svg+xml\",\n    \"tif\": \"image/tiff\",\n    \"tiff\": \"image/tiff\",\n    \"webp\": \"image/webp\",\n    \"pdf\": \"application/pdf\",\n  }[extension.toLowerCase()]\n\n  if (!type) {\n    throw new Error(`Extension \"${extension}\" is not supported`);\n  }\n\n  return type;\n}", "import getMode from \"../tools/getMode\";\nimport mergeDeep from \"../tools/merge\";\nimport downloadURI from \"../tools/downloadURI\";\nimport QRSVG from \"./QRSVG\";\nimport drawTypes from \"../constants/drawTypes\";\n\nimport defaultOptions, { RequiredOptions } from \"./QROptions\";\nimport sanitizeOptions from \"../tools/sanitizeOptions\";\nimport { FileExtension, QRCode, Options, DownloadOptions, ExtensionFunction, Window } from \"../types\";\nimport qrcode from \"qrcode-generator\";\nimport getMimeType from \"../tools/getMimeType\";\nimport { Canvas as NodeCanvas, Image } from \"canvas\";\n\ndeclare const window: Window;\n\nexport default class QRCodeStyling {\n  _options: RequiredOptions;\n  _window: Window;\n  _container?: HTMLElement;\n  _domCanvas?: HTMLCanvasElement;\n  _nodeCanvas?: NodeCanvas;\n  _svg?: SVGElement;\n  _qr?: QRCode;\n  _extension?: ExtensionFunction;\n  _canvasDrawingPromise?: Promise<void>;\n  _svgDrawingPromise?: Promise<void>;\n\n  constructor(options?: Partial<Options>) {\n    if (options?.jsdom) {\n      this._window = new options.jsdom(\"\", { resources: \"usable\" }).window;\n    } else {\n      this._window = window;\n    }\n    this._options = options ? sanitizeOptions(mergeDeep(defaultOptions, options) as RequiredOptions) : defaultOptions;\n    this.update();\n  }\n\n  static _clearContainer(container?: HTMLElement): void {\n    if (container) {\n      container.innerHTML = \"\";\n    }\n  }\n\n  _setupSvg(): void {\n    if (!this._qr) {\n      return;\n    }\n    const qrSVG = new QRSVG(this._options, this._window);\n\n    this._svg = qrSVG.getElement();\n    this._svgDrawingPromise = qrSVG.drawQR(this._qr).then(() => {\n      if (!this._svg) return;\n      this._extension?.(qrSVG.getElement(), this._options);\n    });\n  }\n\n  _setupCanvas(): void {\n    if (!this._qr) {\n      return;\n    }\n\n    if (this._options.nodeCanvas?.createCanvas) {\n      this._nodeCanvas = this._options.nodeCanvas.createCanvas(this._options.width, this._options.height);\n      this._nodeCanvas.width = this._options.width;\n      this._nodeCanvas.height = this._options.height;\n    } else {\n      this._domCanvas = document.createElement(\"canvas\");\n      this._domCanvas.width = this._options.width;\n      this._domCanvas.height = this._options.height;\n    }\n\n    this._setupSvg();\n    this._canvasDrawingPromise = this._svgDrawingPromise?.then(() => {\n      if (!this._svg) return;\n\n      const svg = this._svg;\n      const xml = new this._window.XMLSerializer().serializeToString(svg);\n      const svg64 = btoa(xml);\n      const image64 = `data:${getMimeType('svg')};base64,${svg64}`;\n\n      if (this._options.nodeCanvas?.loadImage) {\n        return this._options.nodeCanvas.loadImage(image64).then((image: Image) => {\n          // fix blurry svg\n          image.width = this._options.width;\n          image.height = this._options.height;\n          this._nodeCanvas?.getContext(\"2d\")?.drawImage(image, 0, 0);\n        });\n      } else {\n        const image = new this._window.Image();\n\n        return new Promise((resolve) => {\n          image.onload = (): void => {\n            this._domCanvas?.getContext(\"2d\")?.drawImage(image, 0, 0);\n            resolve();\n          };\n\n          image.src = image64;\n        });\n      }\n    });\n  }\n\n  async _getElement(extension: FileExtension = \"png\") {\n    if (!this._qr) throw \"QR code is empty\";\n\n    if (extension.toLowerCase() === \"svg\") {\n      if (!this._svg || !this._svgDrawingPromise) {\n        this._setupSvg();\n      }\n      await this._svgDrawingPromise;\n      return this._svg;\n    } else {\n      if (!(this._domCanvas || this._nodeCanvas) || !this._canvasDrawingPromise) {\n        this._setupCanvas();\n      }\n      await this._canvasDrawingPromise;\n      return this._domCanvas || this._nodeCanvas;\n    }\n  }\n\n  update(options?: Partial<Options>): void {\n    QRCodeStyling._clearContainer(this._container);\n    this._options = options ? sanitizeOptions(mergeDeep(this._options, options) as RequiredOptions) : this._options;\n\n    if (!this._options.data) {\n      return;\n    }\n\n    this._qr = qrcode(this._options.qrOptions.typeNumber, this._options.qrOptions.errorCorrectionLevel);\n    this._qr.addData(this._options.data, this._options.qrOptions.mode || getMode(this._options.data));\n    this._qr.make();\n\n    if (this._options.type === drawTypes.canvas) {\n      this._setupCanvas();\n    } else {\n      this._setupSvg();\n    }\n\n    this.append(this._container);\n  }\n\n  append(container?: HTMLElement): void {\n    if (!container) {\n      return;\n    }\n\n    if (typeof container.appendChild !== \"function\") {\n      throw \"Container should be a single DOM node\";\n    }\n\n    if (this._options.type === drawTypes.canvas) {\n      if (this._domCanvas) {\n        container.appendChild(this._domCanvas);\n      }\n    } else {\n      if (this._svg) {\n        container.appendChild(this._svg);\n      }\n    }\n\n    this._container = container;\n  }\n\n  applyExtension(extension: ExtensionFunction): void {\n    if (!extension) {\n      throw \"Extension function should be defined.\";\n    }\n\n    this._extension = extension;\n    this.update();\n  }\n\n  deleteExtension(): void {\n    this._extension = undefined;\n    this.update();\n  }\n\n  async getRawData(extension: FileExtension = \"png\"): Promise<Blob | Buffer | null> {\n    if (!this._qr) throw \"QR code is empty\";\n    const element = await this._getElement(extension);\n    const mimeType = getMimeType(extension);\n\n    if (!element) {\n      return null;\n    }\n\n    if (extension.toLowerCase() === \"svg\") {\n      const serializer = new this._window.XMLSerializer();\n      const source = serializer.serializeToString(element as SVGElement);\n      const svgString = `<?xml version=\"1.0\" standalone=\"no\"?>\\r\\n${source}`;\n      if (typeof Blob !== \"undefined\" && !this._options.jsdom) {\n        return new Blob([svgString], { type: mimeType });\n      } else {\n        return Buffer.from(svgString);\n      }\n    } else {\n      return new Promise((resolve) => {\n        const canvas = element;\n        if ('toBuffer' in canvas) {\n          // Different call is needed to prevent error TS2769: No overload matches this call.\n          if (mimeType === \"image/png\") {\n            resolve(canvas.toBuffer(mimeType));\n          } else if (mimeType === \"image/jpeg\") {\n            resolve(canvas.toBuffer(mimeType));\n          } else if (mimeType === \"application/pdf\") {\n            resolve(canvas.toBuffer(mimeType));\n          } else {\n            throw Error(\"Unsupported extension\");\n          }\n        } else if ('toBlob' in canvas) {\n          (canvas).toBlob(resolve, mimeType, 1);\n        }\n      });\n    }\n  }\n\n  async download(downloadOptions?: Partial<DownloadOptions> | string): Promise<void> {\n    if (!this._qr) throw \"QR code is empty\";\n    if (typeof Blob === \"undefined\") throw \"Cannot download in Node.js, call getRawData instead.\";\n    let extension = \"png\" as FileExtension;\n    let name = \"qr\";\n\n    //TODO remove deprecated code in the v2\n    if (typeof downloadOptions === \"string\") {\n      extension = downloadOptions as FileExtension;\n      console.warn(\n        \"Extension is deprecated as argument for 'download' method, please pass object { name: '...', extension: '...' } as argument\"\n      );\n    } else if (typeof downloadOptions === \"object\" && downloadOptions !== null) {\n      if (downloadOptions.name) {\n        name = downloadOptions.name;\n      }\n      if (downloadOptions.extension) {\n        extension = downloadOptions.extension;\n      }\n    }\n\n    const element = await this._getElement(extension);\n\n    if (!element) {\n      return;\n    }\n\n    if (extension.toLowerCase() === \"svg\") {\n      const serializer = new XMLSerializer();\n      let source = serializer.serializeToString(element as SVGElement);\n\n      source = '<?xml version=\"1.0\" standalone=\"no\"?>\\r\\n' + source;\n      const url = `data:${getMimeType(extension)};charset=utf-8,${encodeURIComponent(source)}`;\n      downloadURI(url, `${name}.svg`);\n    } else {\n      const url = (element as HTMLCanvasElement).toDataURL(getMimeType(extension));\n      downloadURI(url, `${name}.${extension}`);\n    }\n  }\n}\n", "import modes from \"../constants/modes\";\nimport { Mode } from \"../types\";\n\nexport default function getMode(data: string): Mode {\n  switch (true) {\n    case /^[0-9]*$/.test(data):\n      return modes.numeric;\n    case /^[0-9A-Z $%*+\\-./:]*$/.test(data):\n      return modes.alphanumeric;\n    default:\n      return modes.byte;\n  }\n}\n", "import QRCodeStyling from \"./core/QRCodeStyling\";\nimport dotTypes from \"./constants/dotTypes\";\nimport cornerDotTypes from \"./constants/cornerDotTypes\";\nimport cornerSquareTypes from \"./constants/cornerSquareTypes\";\nimport errorCorrectionLevels from \"./constants/errorCorrectionLevels\";\nimport errorCorrectionPercents from \"./constants/errorCorrectionPercents\";\nimport modes from \"./constants/modes\";\nimport qrTypes from \"./constants/qrTypes\";\nimport drawTypes from \"./constants/drawTypes\";\nimport shapeTypes from \"./constants/shapeTypes\";\nimport gradientTypes from \"./constants/gradientTypes\";\n\nexport * from \"./types\";\n\nexport {\n  dotTypes,\n  cornerDotTypes,\n  cornerSquareTypes,\n  errorCorrectionLevels,\n  errorCorrectionPercents,\n  modes,\n  qrTypes,\n  drawTypes,\n  shapeTypes,\n  gradientTypes\n};\n\nexport default QRCodeStyling;\n"], "mappings": ";;;;;;;KAAA,SAA2CA,GAAMC,GAAAA;AAC1B,kBAAA,OAAZC,WAA0C,YAAA,OAAXC,SACxCA,OAAOD,UAAUD,EAAAA,IACQ,cAAA,OAAXG,UAAyBA,OAAOC,MAC9CD,OAAO,CAAA,GAAIH,CAAAA,IACe,YAAA,OAAZC,UACdA,QAAuB,gBAAID,EAAAA,IAE3BD,EAAoB,gBAAIC,EAAAA;IACzB,EAAEK,SAAM,OAAA,MAAA;AAAA,UAAA,IAAA,EAAA,KAAA,CAAAC,IAAAC,OAAA;ACTT,YAAAC,IAAAC,IAiBIC,IAAS,WAAA;AAWX,cAAIA,KAAS,SAASC,IAAYC,IAAAA;AAEhC,gBAGIC,KAAcF,IACdG,KAAwBC,EAAuBH,EAAAA,GAC/CI,KAAW,MACXC,KAAe,GACfC,KAAa,MACbC,KAAY,CAAA,GAEZC,KAAQ,CAAC,GAETC,IAAW,SAASC,IAAMC,IAAAA;AAG5BP,cAAAA,KAAW,SAASQ,IAAAA;AAElB,yBADIC,KAAU,IAAIC,MAAMF,EAAAA,GACfG,KAAM,GAAGA,KAAMH,IAAaG,MAAO,GAAG;AAC7CF,kBAAAA,GAAQE,EAAAA,IAAO,IAAID,MAAMF,EAAAA;AACzB,2BAASI,KAAM,GAAGA,KAAMJ,IAAaI,MAAO,EAC1CH,CAAAA,GAAQE,EAAAA,EAAKC,EAAAA,IAAO;gBAExB;AACA,uBAAOH;cACT,EAVAR,KAA6B,IAAdJ,KAAkB,EAAA,GAYjCgB,EAA0B,GAAG,CAAA,GAC7BA,EAA0BZ,KAAe,GAAG,CAAA,GAC5CY,EAA0B,GAAGZ,KAAe,CAAA,GAC5Ca,EAAAA,GACAC,EAAAA,GACAC,EAAcV,IAAMC,EAAAA,GAEhBV,MAAe,KACjBoB,EAAgBX,EAAAA,GAGA,QAAdJ,OACFA,KAAagB,EAAWrB,IAAaC,IAAuBK,EAAAA,IAG9DgB,EAAQjB,IAAYK,EAAAA;YACtB,GAEIM,IAA4B,SAASF,IAAKC,IAAAA;AAE5C,uBAASnB,KAAAA,IAAQA,MAAK,GAAGA,MAAK,EAE5B,KAAA,EAAIkB,KAAMlB,MAAAA,MAAWQ,MAAgBU,KAAMlB,IAE3C,UAAS2B,KAAAA,IAAQA,MAAK,GAAGA,MAAK,EAExBR,CAAAA,KAAMQ,MAAAA,MAAWnB,MAAgBW,KAAMQ,OAKzCpB,GAASW,KAAMlB,EAAAA,EAAGmB,KAAMQ,EAAAA,IAHpB,KAAK3B,MAAKA,MAAK,MAAW,KAAL2B,MAAe,KAALA,OAC7B,KAAKA,MAAKA,MAAK,MAAW,KAAL3B,MAAe,KAALA,OAC/B,KAAKA,MAAKA,MAAK,KAAK,KAAK2B,MAAKA,MAAK;YAOjD,GAsBIL,IAAqB,WAAA;AAEvB,uBAAStB,KAAI,GAAGA,KAAIQ,KAAe,GAAGR,MAAK,EACnB,SAAlBO,GAASP,EAAAA,EAAG,CAAA,MAGhBO,GAASP,EAAAA,EAAG,CAAA,IAAMA,KAAI,KAAK;AAG7B,uBAAS2B,KAAI,GAAGA,KAAInB,KAAe,GAAGmB,MAAK,EACnB,SAAlBpB,GAAS,CAAA,EAAGoB,EAAAA,MAGhBpB,GAAS,CAAA,EAAGoB,EAAAA,IAAMA,KAAI,KAAK;YAE/B,GAEIN,IAA6B,WAAA;AAI/B,uBAFIO,KAAMC,EAAOC,mBAAmB1B,EAAAA,GAE3BL,KAAI,GAAGA,KAAI6B,GAAIG,QAAQhC,MAAK,EAEnC,UAASiC,KAAI,GAAGA,KAAIJ,GAAIG,QAAQC,MAAK,GAAG;AAEtC,oBAAId,KAAMU,GAAI7B,EAAAA,GACVoB,KAAMS,GAAII,EAAAA;AAEd,oBAA0B,QAAtBzB,GAASW,EAAAA,EAAKC,EAAAA,EAIlB,UAASnB,KAAAA,IAAQA,MAAK,GAAGA,MAAK,EAE5B,UAAS2B,KAAAA,IAAQA,MAAK,GAAGA,MAAK,EAI1BpB,CAAAA,GAASW,KAAMlB,EAAAA,EAAGmB,KAAMQ,EAAAA,IAAAA,MAFtB3B,MAAgB,KAALA,MAAAA,MAAU2B,MAAgB,KAALA,MACvB,KAAL3B,MAAe,KAAL2B;cAOxB;YAEJ,GAEIH,IAAkB,SAASX,IAAAA;AAI7B,uBAFIoB,KAAOJ,EAAOK,iBAAiB9B,EAAAA,GAE1BL,KAAI,GAAGA,KAAI,IAAIA,MAAK,GAAG;AAC9B,oBAAIoC,KAAAA,CAAQtB,MAA8B,MAAnBoB,MAAQlC,KAAK;AACpCQ,gBAAAA,GAAS6B,KAAKC,MAAMtC,KAAI,CAAA,CAAA,EAAIA,KAAI,IAAIS,KAAe,IAAI,CAAA,IAAK2B;cAC9D;AAEA,mBAASpC,KAAI,GAAGA,KAAI,IAAIA,MAAK,EACvBoC,CAAAA,KAAAA,CAAQtB,MAA8B,MAAnBoB,MAAQlC,KAAK,IACpCQ,GAASR,KAAI,IAAIS,KAAe,IAAI,CAAA,EAAG4B,KAAKC,MAAMtC,KAAI,CAAA,CAAA,IAAMoC;YAEhE,GAEIZ,IAAgB,SAASV,IAAMC,IAAAA;AAMjC,uBAJIwB,KAAQjC,MAAyB,IAAKS,IACtCmB,KAAOJ,EAAOU,eAAeD,EAAAA,GAGxBvC,KAAI,GAAGA,KAAI,IAAIA,MAAK,GAAG;AAE9B,oBAAIoC,KAAAA,CAAQtB,MAA8B,MAAnBoB,MAAQlC,KAAK;AAEhCA,gBAAAA,KAAI,IACNQ,GAASR,EAAAA,EAAG,CAAA,IAAKoC,KACRpC,KAAI,IACbQ,GAASR,KAAI,CAAA,EAAG,CAAA,IAAKoC,KAErB5B,GAASC,KAAe,KAAKT,EAAAA,EAAG,CAAA,IAAKoC;cAEzC;AAGA,mBAASpC,KAAI,GAAGA,KAAI,IAAIA,MAAK,EAEvBoC,CAAAA,KAAAA,CAAQtB,MAA8B,MAAnBoB,MAAQlC,KAAK,IAEhCA,KAAI,IACNQ,GAAS,CAAA,EAAGC,KAAeT,KAAI,CAAA,IAAKoC,KAC3BpC,KAAI,IACbQ,GAAS,CAAA,EAAG,KAAKR,KAAI,IAAI,CAAA,IAAKoC,KAE9B5B,GAAS,CAAA,EAAG,KAAKR,KAAI,CAAA,IAAKoC;AAK9B5B,cAAAA,GAASC,KAAe,CAAA,EAAG,CAAA,IAAA,CAAOK;YACpC,GAEIa,IAAU,SAASY,IAAMxB,IAAAA;AAQ3B,uBANI0B,KAAAA,IACAtB,KAAMV,KAAe,GACrBiC,KAAW,GACXC,KAAY,GACZC,KAAWd,EAAOe,gBAAgB9B,EAAAA,GAE7BK,KAAMX,KAAe,GAAGW,KAAM,GAAGA,MAAO,EAI/C,MAFW,KAAPA,OAAUA,MAAO,QAER;AAEX,yBAASQ,KAAI,GAAGA,KAAI,GAAGA,MAAK,EAE1B,KAA8B,QAA1BpB,GAASW,EAAAA,EAAKC,KAAMQ,EAAAA,GAAY;AAElC,sBAAIkB,KAAAA;AAEAH,kBAAAA,KAAYJ,GAAKP,WACnBc,KAAkD,MAAtCP,GAAKI,EAAAA,MAAeD,KAAY,KAGnCE,GAASzB,IAAKC,KAAMQ,EAAAA,MAG7BkB,KAAAA,CAAQA,KAGVtC,GAASW,EAAAA,EAAKC,KAAMQ,EAAAA,IAAKkB,IAAAA,OACzBJ,MAAY,OAGVC,MAAa,GACbD,KAAW;gBAEf;AAKF,qBAFAvB,MAAOsB,MAEG,KAAKhC,MAAgBU,IAAK;AAClCA,kBAAAA,MAAOsB,IACPA,KAAAA,CAAOA;AACP;gBACF;cACF;YAEJ,GAmEIf,IAAa,SAASvB,IAAYC,IAAsB2C,IAAAA;AAM1D,uBAJIC,KAAWC,EAAUC,YAAY/C,IAAYC,EAAAA,GAE7C+C,KAASC,EAAAA,GAEJpD,KAAI,GAAGA,KAAI+C,GAASf,QAAQhC,MAAK,GAAG;AAC3C,oBAAIuC,KAAOQ,GAAS/C,EAAAA;AACpBmD,gBAAAA,GAAOE,IAAId,GAAKe,QAAAA,GAAW,CAAA,GAC3BH,GAAOE,IAAId,GAAKgB,UAAAA,GAAazB,EAAO0B,gBAAgBjB,GAAKe,QAAAA,GAAWnD,EAAAA,CAAAA,GACpEoC,GAAKkB,MAAMN,EAAAA;cACb;AAGA,kBAAIO,KAAiB;AACrB,mBAAS1D,KAAI,GAAGA,KAAIgD,GAAShB,QAAQhC,MAAK,EACxC0D,CAAAA,MAAkBV,GAAShD,EAAAA,EAAG2D;AAGhC,kBAAIR,GAAOK,gBAAAA,IAAqC,IAAjBE,GAC7B,OAAM,4BACFP,GAAOK,gBAAAA,IACP,MACiB,IAAjBE,KACA;AASN,mBALIP,GAAOK,gBAAAA,IAAoB,KAAsB,IAAjBE,MAClCP,GAAOE,IAAI,GAAG,CAAA,GAITF,GAAOK,gBAAAA,IAAoB,KAAK,IACrCL,CAAAA,GAAOS,OAAAA,KAAO;AAIhB,qBAAA,EAEMT,GAAOK,gBAAAA,KAAsC,IAAjBE,OAGhCP,GAAOE,IAxVA,KAwVU,CAAA,GAEbF,GAAOK,gBAAAA,KAAsC,IAAjBE,OAGhCP,CAAAA,GAAOE,IA5VA,IA4VU,CAAA;AAGnB,qBApHgB,SAASF,IAAQH,IAAAA;AAUjC,yBARIa,KAAS,GAETC,KAAa,GACbC,KAAa,GAEbC,KAAS,IAAI9C,MAAM8B,GAAShB,MAAAA,GAC5BiC,KAAS,IAAI/C,MAAM8B,GAAShB,MAAAA,GAEvB/B,KAAI,GAAGA,KAAI+C,GAAShB,QAAQ/B,MAAK,GAAG;AAE3C,sBAAIiE,KAAUlB,GAAS/C,EAAAA,EAAG0D,WACtBQ,KAAUnB,GAAS/C,EAAAA,EAAGmE,aAAaF;AAEvCJ,kBAAAA,KAAazB,KAAKgC,IAAIP,IAAYI,EAAAA,GAClCH,KAAa1B,KAAKgC,IAAIN,IAAYI,EAAAA,GAElCH,GAAO/D,EAAAA,IAAK,IAAIiB,MAAMgD,EAAAA;AAEtB,2BAASlE,KAAI,GAAGA,KAAIgE,GAAO/D,EAAAA,EAAG+B,QAAQhC,MAAK,EACzCgE,CAAAA,GAAO/D,EAAAA,EAAGD,EAAAA,IAAK,MAAOmD,GAAOmB,UAAAA,EAAYtE,KAAI6D,EAAAA;AAE/CA,kBAAAA,MAAUK;AAEV,sBAAIK,KAASzC,EAAO0C,0BAA0BL,EAAAA,GAG1CM,KAFUC,EAAaV,GAAO/D,EAAAA,GAAIsE,GAAOhB,UAAAA,IAAc,CAAA,EAErCnB,IAAImC,EAAAA;AAE1B,uBADAN,GAAOhE,EAAAA,IAAK,IAAIiB,MAAMqD,GAAOhB,UAAAA,IAAc,CAAA,GAClCvD,KAAI,GAAGA,KAAIiE,GAAOhE,EAAAA,EAAG+B,QAAQhC,MAAK,GAAG;AAC5C,wBAAI2E,KAAW3E,KAAIyE,GAAQlB,UAAAA,IAAcU,GAAOhE,EAAAA,EAAG+B;AACnDiC,oBAAAA,GAAOhE,EAAAA,EAAGD,EAAAA,IAAM2E,MAAY,IAAIF,GAAQG,MAAMD,EAAAA,IAAY;kBAC5D;gBACF;AAEA,oBAAIE,KAAiB;AACrB,qBAAS7E,KAAI,GAAGA,KAAIgD,GAAShB,QAAQhC,MAAK,EACxC6E,CAAAA,MAAkB7B,GAAShD,EAAAA,EAAGoE;AAGhC,oBAAI7B,KAAO,IAAIrB,MAAM2D,EAAAA,GACjBC,KAAQ;AAEZ,qBAAS9E,KAAI,GAAGA,KAAI8D,IAAY9D,MAAK,EACnC,MAASC,KAAI,GAAGA,KAAI+C,GAAShB,QAAQ/B,MAAK,EACpCD,CAAAA,KAAIgE,GAAO/D,EAAAA,EAAG+B,WAChBO,GAAKuC,EAAAA,IAASd,GAAO/D,EAAAA,EAAGD,EAAAA,GACxB8E,MAAS;AAKf,qBAAS9E,KAAI,GAAGA,KAAI+D,IAAY/D,MAAK,EACnC,MAASC,KAAI,GAAGA,KAAI+C,GAAShB,QAAQ/B,MAAK,EACpCD,CAAAA,KAAIiE,GAAOhE,EAAAA,EAAG+B,WAChBO,GAAKuC,EAAAA,IAASb,GAAOhE,EAAAA,EAAGD,EAAAA,GACxB8E,MAAS;AAKf,uBAAOvC;cACT,EAqDqBY,IAAQH,EAAAA;YAC7B;AAEApC,YAAAA,GAAMmE,UAAU,SAASxC,IAAMyC,IAAAA;AAI7B,kBAAIC,KAAU;AAEd,sBAJAD,KAAOA,MAAQ,QAAA;gBAKf,KAAK;AACHC,kBAAAA,KAAUC,EAAS3C,EAAAA;AACnB;gBACF,KAAK;AACH0C,kBAAAA,KAAUE,EAAW5C,EAAAA;AACrB;gBACF,KAAK;AACH0C,kBAAAA,KAAUG,EAAW7C,EAAAA;AACrB;gBACF,KAAK;AACH0C,kBAAAA,KAAUI,EAAQ9C,EAAAA;AAClB;gBACF;AACE,wBAAM,UAAUyC;cAAAA;AAGlBrE,cAAAA,GAAU2E,KAAKL,EAAAA,GACfvE,KAAa;YACf,GAEAE,GAAM2E,SAAS,SAASpE,IAAKC,IAAAA;AAC3B,kBAAID,KAAM,KAAKV,MAAgBU,MAAOC,KAAM,KAAKX,MAAgBW,GAC/D,OAAMD,KAAM,MAAMC;AAEpB,qBAAOZ,GAASW,EAAAA,EAAKC,EAAAA;YACvB,GAEAR,GAAM4E,iBAAiB,WAAA;AACrB,qBAAO/E;YACT,GAEAG,GAAM6E,OAAO,WAAA;AACX,kBAAIpF,KAAc,GAAG;AAGnB,yBAFIF,KAAa,GAEVA,KAAa,IAAIA,MAAc;AAIpC,2BAHI6C,KAAWC,EAAUC,YAAY/C,IAAYG,EAAAA,GAC7C6C,KAASC,EAAAA,GAEJpD,KAAI,GAAGA,KAAIW,GAAUqB,QAAQhC,MAAK;AACzC,wBAAIuC,KAAO5B,GAAUX,EAAAA;AACrBmD,oBAAAA,GAAOE,IAAId,GAAKe,QAAAA,GAAW,CAAA,GAC3BH,GAAOE,IAAId,GAAKgB,UAAAA,GAAazB,EAAO0B,gBAAgBjB,GAAKe,QAAAA,GAAWnD,EAAAA,CAAAA,GACpEoC,GAAKkB,MAAMN,EAAAA;kBACb;AAEA,sBAAIO,KAAiB;AACrB,uBAAS1D,KAAI,GAAGA,KAAIgD,GAAShB,QAAQhC,KACnC0D,CAAAA,MAAkBV,GAAShD,EAAAA,EAAG2D;AAGhC,sBAAIR,GAAOK,gBAAAA,KAAsC,IAAjBE,GAC9B;gBAEJ;AAEArD,gBAAAA,KAAcF;cAChB;AAEAU,gBAAAA,OApWuB,WAAA;AAKvB,yBAHI6E,KAAe,GACfC,KAAU,GAEL3F,KAAI,GAAGA,KAAI,GAAGA,MAAK,GAAG;AAE7Ba,oBAAAA,MAAeb,EAAAA;AAEf,sBAAI4F,KAAY9D,EAAO+D,aAAajF,EAAAA;AAAAA,mBAE3B,KAALZ,MAAU0F,KAAeE,QAC3BF,KAAeE,IACfD,KAAU3F;gBAEd;AAEA,uBAAO2F;cACT,EAkVkBG,CAAAA;YAClB,GAEAlF,GAAMmF,iBAAiB,SAASC,IAAUC,IAAAA;AAExCD,cAAAA,KAAWA,MAAY;AAGvB,kBAAIE,KAAS;AAEbA,cAAAA,MAAU,kBACVA,MAAU,2CACVA,MAAU,+BACVA,MAAU,6BAPVD,KAAAA,WAAiBA,KAAmC,IAAXD,KAAeC,MAOT,OAC/CC,MAAU,MACVA,MAAU;AAEV,uBAASjG,KAAI,GAAGA,KAAIW,GAAM4E,eAAAA,GAAkBvF,MAAK,GAAG;AAElDiG,gBAAAA,MAAU;AAEV,yBAAStE,KAAI,GAAGA,KAAIhB,GAAM4E,eAAAA,GAAkB5D,MAAK,EAC/CsE,CAAAA,MAAU,eACVA,MAAU,2CACVA,MAAU,+BACVA,MAAU,+BACVA,MAAU,aAAaF,KAAW,OAClCE,MAAU,cAAcF,KAAW,OACnCE,MAAU,uBACVA,MAAUtF,GAAM2E,OAAOtF,IAAG2B,EAAAA,IAAI,YAAY,WAC1CsE,MAAU,KACVA,MAAU;AAGZA,gBAAAA,MAAU;cACZ;AAKA,sBAHAA,MAAU,cACA;YAGZ,GAEAtF,GAAMuF,eAAe,SAASH,IAAUC,IAAQG,IAAKC,IAAAA;AAEnD,kBAAIC,KAAO,CAAC;AACe,0BAAA,OAAhBC,UAAU,CAAA,MAInBP,MAFAM,KAAOC,UAAU,CAAA,GAEDP,UAChBC,KAASK,GAAKL,QACdG,KAAME,GAAKF,KACXC,KAAQC,GAAKD,QAGfL,KAAWA,MAAY,GACvBC,KAAAA,WAAiBA,KAAmC,IAAXD,KAAeC,KAGxDG,KAAsB,YAAA,OAARA,KAAoB,EAACI,MAAMJ,GAAAA,IAAOA,MAAO,CAAC,GACpDI,OAAOJ,GAAII,QAAQ,MACvBJ,GAAIK,KAAML,GAAQ,OAAIA,GAAIK,MAAM,uBAAuB,OAGvDJ,KAA0B,YAAA,OAAVA,KAAsB,EAACG,MAAMH,GAAAA,IAASA,MAAS,CAAC,GAC1DG,OAAOH,GAAMG,QAAQ,MAC3BH,GAAMI,KAAMJ,GAAU,OAAIA,GAAMI,MAAM,iBAAiB;AAEvD,kBACI7E,IAAO3B,IAAGyG,IAAcC,IADxBC,KAAOhG,GAAM4E,eAAAA,IAAmBQ,KAAoB,IAATC,IAC7BY,KAAM;AAmBxB,mBAjBAF,KAAO,MAAMX,KAAW,UAAUA,KAChC,OAAOA,KAAW,WAAWA,KAAW,MAE1Ca,MAAS,yDACTA,MAAUP,GAAKQ,WAA+D,KAApD,aAAaF,KAAO,iBAAiBA,KAAO,OACtEC,MAAS,mBAAmBD,KAAO,MAAMA,KAAO,MAChDC,MAAS,wCACTA,MAAUR,GAAMG,QAAQJ,GAAII,OAAQ,kCAChCO,EAAU,CAACV,GAAMI,IAAIL,GAAIK,EAAAA,EAAIO,KAAK,GAAA,EAAKC,KAAAA,CAAAA,IAAW,MAAM,IAC5DJ,MAAS,KACTA,MAAUR,GAAU,OAAI,gBAAgBU,EAAUV,GAAMI,EAAAA,IAAM,OAC1DM,EAAUV,GAAMG,IAAAA,IAAQ,aAAa,IACzCK,MAAUT,GAAQ,OAAI,sBAAsBW,EAAUX,GAAIK,EAAAA,IAAM,OAC5DM,EAAUX,GAAII,IAAAA,IAAQ,mBAAmB,IAC7CK,MAAS,iEACTA,MAAS,aAEJ5G,KAAI,GAAGA,KAAIW,GAAM4E,eAAAA,GAAkBvF,MAAK,EAE3C,MADAyG,KAAKzG,KAAI+F,KAAWC,IACfrE,KAAI,GAAGA,KAAIhB,GAAM4E,eAAAA,GAAkB5D,MAAK,EACvChB,CAAAA,GAAM2E,OAAOtF,IAAG2B,EAAAA,MAElBiF,MAAS,OADJjF,KAAEoE,KAASC,MACI,MAAMS,KAAKC;AAQrC,sBAHAE,MAAS,2CACA;YAGX,GAEAjG,GAAMsG,gBAAgB,SAASlB,IAAUC,IAAAA;AAEvCD,cAAAA,KAAWA,MAAY,GACvBC,KAAAA,WAAiBA,KAAmC,IAAXD,KAAeC;AAExD,kBAAIW,KAAOhG,GAAM4E,eAAAA,IAAmBQ,KAAoB,IAATC,IAC3CkB,KAAMlB,IACN5B,KAAMuC,KAAOX;AAEjB,qBAAOiB,EAAcN,IAAMA,IAAM,SAASQ,IAAGC,IAAAA;AAC3C,oBAAIF,MAAOC,MAAKA,KAAI/C,MAAO8C,MAAOE,MAAKA,KAAIhD,IAAK;AAC9C,sBAAIzC,KAAIS,KAAKC,OAAQ8E,KAAID,MAAOnB,EAAAA,GAC5B/F,KAAIoC,KAAKC,OAAQ+E,KAAIF,MAAOnB,EAAAA;AAChC,yBAAOpF,GAAM2E,OAAOtF,IAAG2B,EAAAA,IAAI,IAAI;gBACjC;AACE,uBAAO;cAEX,CAAA;YACF,GAEAhB,GAAM0G,eAAe,SAAStB,IAAUC,IAAQG,IAAAA;AAE9CJ,cAAAA,KAAWA,MAAY,GACvBC,KAAAA,WAAiBA,KAAmC,IAAXD,KAAeC;AAExD,kBAAIW,KAAOhG,GAAM4E,eAAAA,IAAmBQ,KAAoB,IAATC,IAE3CsB,KAAM;AAkBV,qBAjBAA,MAAO,QACPA,MAAO,UACPA,MAAO3G,GAAMsG,cAAclB,IAAUC,EAAAA,GACrCsB,MAAO,KACPA,MAAO,YACPA,MAAOX,IACPW,MAAO,KACPA,MAAO,aACPA,MAAOX,IACPW,MAAO,KACHnB,OACFmB,MAAO,UACPA,MAAOR,EAAUX,EAAAA,GACjBmB,MAAO,MAETA,KAAO;YAGT;AAEA,gBAAIR,IAAY,SAASS,IAAAA;AAEvB,uBADIC,KAAU,IACLzH,KAAI,GAAGA,KAAIwH,GAAExF,QAAQhC,MAAK,GAAG;AACpC,oBAAI4B,KAAI4F,GAAEE,OAAO1H,EAAAA;AACjB,wBAAO4B,IAAAA;kBACP,KAAK;AAAK6F,oBAAAA,MAAW;AAAQ;kBAC7B,KAAK;AAAKA,oBAAAA,MAAW;AAAQ;kBAC7B,KAAK;AAAKA,oBAAAA,MAAW;AAAS;kBAC9B,KAAK;AAAKA,oBAAAA,MAAW;AAAU;kBAC/B;AAAUA,oBAAAA,MAAW7F;gBAAAA;cAEvB;AACA,qBAAO6F;YACT;AAgHA,mBAtDA7G,GAAM+G,cAAc,SAAS3B,IAAUC,IAAAA;AAGrC,mBAFAD,KAAWA,MAAY,KAER,EACb,QA5DmB,SAASC,IAAAA;AAE9BA,gBAAAA,KAAAA,WAAiBA,KAAwBD,IAAeC;AAExD,oBAIIoB,IAAGD,IAAGQ,IAAIC,IAAIC,IAJdlB,KAHW,IAGJhG,GAAM4E,eAAAA,IAAuC,IAATS,IAC3CkB,KAAMlB,IACN5B,KAAMuC,KAAOX,IAIb8B,KAAS,EACX,MAAM,KACN,MAAM,KACN,MAAM,KACN,MAAM,IAAA,GAGJC,KAAyB,EAC3B,MAAM,KACN,MAAM,KACN,MAAM,KACN,MAAM,IAAA,GAGJC,KAAQ;AACZ,qBAAKZ,KAAI,GAAGA,KAAIT,IAAMS,MAAK,GAAG;AAG5B,uBAFAO,KAAKvF,KAAKC,OAAO+E,KAAIF,MAzBR,CAAA,GA0BbU,KAAKxF,KAAKC,OAAO+E,KAAI,IAAIF,MA1BZ,CAAA,GA2BRC,KAAI,GAAGA,KAAIR,IAAMQ,MAAK,EACzBU,CAAAA,KAAI,KAEAX,MAAOC,MAAKA,KAAI/C,MAAO8C,MAAOE,MAAKA,KAAIhD,MAAOzD,GAAM2E,OAAOqC,IAAIvF,KAAKC,OAAO8E,KAAID,MA9BxE,CAAA,CAAA,MA+BTW,KAAI,MAGFX,MAAOC,MAAKA,KAAI/C,MAAO8C,MAAOE,KAAE,KAAKA,KAAE,IAAIhD,MAAOzD,GAAM2E,OAAOsC,IAAIxF,KAAKC,OAAO8E,KAAID,MAlC5E,CAAA,CAAA,IAmCTW,MAAK,MAGLA,MAAK,KAIPG,MAAUhC,KAAS,KAAKoB,KAAE,KAAKhD,KAAO2D,GAAuBF,EAAAA,IAAKC,GAAOD,EAAAA;AAG3EG,kBAAAA,MAAS;gBACX;AAEA,uBAAIrB,KAAO,KAAKX,KAAS,IAChBgC,GAAMC,UAAU,GAAGD,GAAMjG,SAAS4E,KAAO,CAAA,IAAK1F,MAAM0F,KAAK,CAAA,EAAGI,KAAK,GAAA,IAGnEiB,GAAMC,UAAU,GAAGD,GAAMjG,SAAO,CAAA;cACzC,EAM4BiE,EAAAA;AAG1BD,cAAAA,MAAY,GACZC,KAAAA,WAAiBA,KAAmC,IAAXD,KAAeC;AAExD,kBAIIoB,IAAGD,IAAGnH,IAAG6H,IAJTlB,KAAOhG,GAAM4E,eAAAA,IAAmBQ,KAAoB,IAATC,IAC3CkB,KAAMlB,IACN5B,KAAMuC,KAAOX,IAIbkC,KAAQjH,MAAM8E,KAAS,CAAA,EAAGgB,KAAK,IAAA,GAC/BoB,KAAQlH,MAAM8E,KAAS,CAAA,EAAGgB,KAAK,IAAA,GAE/BiB,KAAQ,IACRI,KAAO;AACX,mBAAKhB,KAAI,GAAGA,KAAIT,IAAMS,MAAK,GAAG;AAG5B,qBAFApH,KAAIoC,KAAKC,OAAQ+E,KAAIF,MAAOnB,EAAAA,GAC5BqC,KAAO,IACFjB,KAAI,GAAGA,KAAIR,IAAMQ,MAAK,EACzBU,CAAAA,KAAI,GAEAX,MAAOC,MAAKA,KAAI/C,MAAO8C,MAAOE,MAAKA,KAAIhD,MAAOzD,GAAM2E,OAAOtF,IAAGoC,KAAKC,OAAO8E,KAAID,MAAOnB,EAAAA,CAAAA,MACvF8B,KAAI,IAINO,MAAQP,KAAIK,KAAQC;AAGtB,qBAAKnI,KAAI,GAAGA,KAAI+F,IAAU/F,MAAK,EAC7BgI,CAAAA,MAASI,KAAO;cAEpB;AAEA,qBAAOJ,GAAMC,UAAU,GAAGD,GAAMjG,SAAO,CAAA;YACzC,GAEApB,GAAM0H,oBAAoB,SAASC,IAASvC,IAAAA;AAC1CA,cAAAA,KAAWA,MAAY;AAEvB,uBADIhE,KAASpB,GAAM4E,eAAAA,GACVrE,KAAM,GAAGA,KAAMa,IAAQb,KAC9B,UAASC,KAAM,GAAGA,KAAMY,IAAQZ,KAC9BmH,CAAAA,GAAQC,YAAY5H,GAAM2E,OAAOpE,IAAKC,EAAAA,IAAO,UAAU,SACvDmH,GAAQE,SAAStH,KAAM6E,IAAU5E,KAAM4E,IAAUA,IAAUA,EAAAA;YAGjE,GAEOpF;UACT;AAiBAV,UAAAA,GAAOwI,iBAXPxI,GAAOyI,qBAAqB,EAC1B,SAAY,SAASnB,IAAAA;AAEnB,qBADIoB,KAAQ,CAAA,GACH5I,KAAI,GAAGA,KAAIwH,GAAExF,QAAQhC,MAAK,GAAG;AACpC,kBAAI4B,KAAI4F,GAAEqB,WAAW7I,EAAAA;AACrB4I,cAAAA,GAAMtD,KAAS,MAAJ1D,EAAAA;YACb;AACA,mBAAOgH;UACT,EAAA,GAGwD,SAW1D1I,GAAO4I,sBAAsB,SAASC,IAAaC,IAAAA;AAIjD,gBAAIC,KAAa,WAAA;AAWf,uBATIC,KAAMC,EAAwBJ,EAAAA,GAC9BK,KAAO,WAAA;AACT,oBAAIC,KAAIH,GAAIE,KAAAA;AACZ,oBAAA,MAAIC,GAAS,OAAM;AACnB,uBAAOA;cACT,GAEIC,KAAQ,GACRL,KAAa,CAAC,OACL;AACX,oBAAIM,KAAKL,GAAIE,KAAAA;AACb,oBAAA,MAAIG,GAAU;AACd,oBAAIC,KAAKJ,GAAAA,GAILK,KAHKL,GAAAA,KAGM,IAFNA,GAAAA;AAGTH,gBAAAA,GAFQS,OAAOC,aAAeJ,MAAM,IAAKC,EAAAA,CAAAA,IAEzBC,IAChBH,MAAS;cACX;AACA,kBAAIA,MAASN,GACX,OAAMM,KAAQ,SAASN;AAGzB,qBAAOC;YACT,EA3BiB,GA6BbW,KAAc,IAAIf,WAAW,CAAA;AAEjC,mBAAO,SAASrB,IAAAA;AAEd,uBADIoB,KAAQ,CAAA,GACH5I,KAAI,GAAGA,KAAIwH,GAAExF,QAAQhC,MAAK,GAAG;AACpC,oBAAI4B,KAAI4F,GAAEqB,WAAW7I,EAAAA;AACrB,oBAAI4B,KAAI,IACNgH,CAAAA,GAAMtD,KAAK1D,EAAAA;qBACN;AACL,sBAAIyH,KAAIJ,GAAWzB,GAAEE,OAAO1H,EAAAA,CAAAA;AACZ,8BAAA,OAALqJ,MACC,MAAJA,OAAaA,KAEjBT,GAAMtD,KAAK+D,EAAAA,KAGXT,GAAMtD,KAAK+D,OAAM,CAAA,GACjBT,GAAMtD,KAAS,MAAJ+D,EAAAA,KAGbT,GAAMtD,KAAKsE,EAAAA;gBAEf;cACF;AACA,qBAAOhB;YACT;UACF;AAMA,cAuCMiB,IA0CAC,IACAC,IAGAnJ,IAEAoJ,GA5EFzJ,IAAyB,EAC3B0J,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,EAAA,GAsBFtI,KAEE+H,KAAyB,CAC3B,CAAA,GACA,CAAC,GAAG,EAAA,GACJ,CAAC,GAAG,EAAA,GACJ,CAAC,GAAG,EAAA,GACJ,CAAC,GAAG,EAAA,GACJ,CAAC,GAAG,EAAA,GACJ,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,IAAI,EAAA,GACZ,CAAC,GAAG,IAAI,IAAI,EAAA,GACZ,CAAC,GAAG,IAAI,IAAI,EAAA,GACZ,CAAC,GAAG,IAAI,IAAI,EAAA,GACZ,CAAC,GAAG,IAAI,IAAI,EAAA,GACZ,CAAC,GAAG,IAAI,IAAI,EAAA,GACZ,CAAC,GAAG,IAAI,IAAI,EAAA,GACZ,CAAC,GAAG,IAAI,IAAI,IAAI,EAAA,GAChB,CAAC,GAAG,IAAI,IAAI,IAAI,EAAA,GAChB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAA,GAChB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAA,GAChB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAA,GAChB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAA,GAChB,CAAC,GAAG,IAAI,IAAI,IAAI,GAAA,GAChB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,GAAA,GACpB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAA,GACrB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAA,GACrB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAA,GACrB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAA,GACrB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAA,GACrB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,GAAA,GACrB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAA,GAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAA,GAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAA,GAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAA,GAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAA,GAC1B,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,GAAA,CAAA,GAExBC,KAAM,MACNC,KAAM,MAKNC,IAAc,SAASzH,IAAAA;AAEzB,qBADI8H,KAAQ,GACG,KAAR9H,KACL8H,CAAAA,MAAS,GACT9H,QAAU;AAEZ,mBAAO8H;UACT,IATIzJ,KAAQ,CAAC,GAWP4B,iBAAiB,SAASD,IAAAA;AAE9B,qBADI+H,KAAI/H,MAAQ,IACTyH,EAAYM,EAAAA,IAAKN,EAAYF,EAAAA,KAAQ,IAC1CQ,CAAAA,MAAMR,MAAQE,EAAYM,EAAAA,IAAKN,EAAYF,EAAAA;AAE7C,mBAlBa,SAkBHvH,MAAQ,KAAM+H;UAC1B,GAEA1J,GAAMuB,mBAAmB,SAASI,IAAAA;AAEhC,qBADI+H,KAAI/H,MAAQ,IACTyH,EAAYM,EAAAA,IAAKN,EAAYD,EAAAA,KAAQ,IAC1CO,CAAAA,MAAMP,MAAQC,EAAYM,EAAAA,IAAKN,EAAYD,EAAAA;AAE7C,mBAAQxH,MAAQ,KAAM+H;UACxB,GAEA1J,GAAMmB,qBAAqB,SAAS5B,IAAAA;AAClC,mBAAO0J,GAAuB1J,KAAa,CAAA;UAC7C,GAEAS,GAAMiC,kBAAkB,SAAS9B,IAAAA;AAE/B,oBAAQA,IAAAA;cAER,KAjGW;AAkGT,uBAAO,SAASf,IAAGiC,IAAAA;AAAK,0BAAQjC,KAAIiC,MAAK,KAAK;gBAAG;cACnD,KAlGW;AAmGT,uBAAO,SAASjC,IAAGiC,IAAAA;AAAK,yBAAOjC,KAAI,KAAK;gBAAG;cAC7C,KAnGW;AAoGT,uBAAO,SAASA,IAAGiC,IAAAA;AAAK,yBAAOA,KAAI,KAAK;gBAAG;cAC7C,KApGW;AAqGT,uBAAO,SAASjC,IAAGiC,IAAAA;AAAK,0BAAQjC,KAAIiC,MAAK,KAAK;gBAAG;cACnD,KArGW;AAsGT,uBAAO,SAASjC,IAAGiC,IAAAA;AAAK,0BAAQI,KAAKC,MAAMtC,KAAI,CAAA,IAAKqC,KAAKC,MAAML,KAAI,CAAA,KAAO,KAAK;gBAAG;cACpF,KAtGW;AAuGT,uBAAO,SAASjC,IAAGiC,IAAAA;AAAK,yBAAQjC,KAAIiC,KAAK,IAAKjC,KAAIiC,KAAK,KAAK;gBAAG;cACjE,KAvGW;AAwGT,uBAAO,SAASjC,IAAGiC,IAAAA;AAAK,0BAAUjC,KAAIiC,KAAK,IAAKjC,KAAIiC,KAAK,KAAK,KAAK;gBAAG;cACxE,KAxGW;AAyGT,uBAAO,SAASjC,IAAGiC,IAAAA;AAAK,0BAAUjC,KAAIiC,KAAK,KAAKjC,KAAIiC,MAAK,KAAK,KAAK;gBAAG;cAExE;AACE,sBAAM,qBAAqBlB;YAAAA;UAE/B,GAEAH,GAAM4D,4BAA4B,SAAS+F,IAAAA;AAEzC,qBADIC,KAAI9F,EAAa,CAAC,CAAA,GAAI,CAAA,GACjB1E,KAAI,GAAGA,KAAIuK,IAAoBvK,MAAK,EAC3CwK,CAAAA,KAAIA,GAAEC,SAAS/F,EAAa,CAAC,GAAGgG,EAAOC,KAAK3K,EAAAA,CAAAA,GAAK,CAAA,CAAA;AAEnD,mBAAOwK;UACT,GAEA5J,GAAM4C,kBAAkB,SAASwB,IAAM4F,IAAAA;AAErC,gBAAI,KAAKA,MAAQA,KAAO,GAItB,SAAO5F,IAAAA;cACP,KA5Ja;AA4JgB,uBAAO;cACpC,KA5Ja;AA4JgB,uBAAO;cACpC,KA5Ja;cA6Jb,KA5Ja;AA4JgB,uBAAO;cACpC;AACE,sBAAM,UAAUA;YAAAA;qBAGT4F,KAAO,GAIhB,SAAO5F,IAAAA;cACP,KAzKa;AAyKgB,uBAAO;cACpC,KAzKa;AAyKgB,uBAAO;cACpC,KAzKa;AAyKgB,uBAAO;cACpC,KAzKa;AAyKgB,uBAAO;cACpC;AACE,sBAAM,UAAUA;YAAAA;iBAGb;AAAA,kBAAA,EAAI4F,KAAO,IAchB,OAAM,UAAUA;AAVhB,sBAAO5F,IAAAA;gBACP,KAtLa;AAsLgB,yBAAO;gBACpC,KAtLa;AAsLgB,yBAAO;gBACpC,KAtLa;AAsLgB,yBAAO;gBACpC,KAtLa;AAsLgB,yBAAO;gBACpC;AACE,wBAAM,UAAUA;cAAAA;YAKpB;UACF,GAEApE,GAAMiF,eAAe,SAAS3F,IAAAA;AAQ5B,qBANIc,KAAcd,GAAOsF,eAAAA,GAErBI,KAAY,GAIPzE,KAAM,GAAGA,KAAMH,IAAaG,MAAO,EAC1C,UAASC,KAAM,GAAGA,KAAMJ,IAAaI,MAAO,GAAG;AAK7C,uBAHIyJ,KAAY,GACZ/H,KAAO5C,GAAOqF,OAAOpE,IAAKC,EAAAA,GAErBnB,KAAAA,IAAQA,MAAK,GAAGA,MAAK,EAE5B,KAAA,EAAIkB,KAAMlB,KAAI,KAAKe,MAAeG,KAAMlB,IAIxC,UAAS2B,KAAAA,IAAQA,MAAK,GAAGA,MAAK,EAExBR,CAAAA,KAAMQ,KAAI,KAAKZ,MAAeI,KAAMQ,MAI/B,KAAL3B,MAAe,KAAL2B,MAIVkB,MAAQ5C,GAAOqF,OAAOpE,KAAMlB,IAAGmB,KAAMQ,EAAAA,MACvCiJ,MAAa;AAKfA,cAAAA,KAAY,MACdjF,MAAc,IAAIiF,KAAY;YAElC;AAKF,iBAAS1J,KAAM,GAAGA,KAAMH,KAAc,GAAGG,MAAO,EAC9C,MAASC,KAAM,GAAGA,KAAMJ,KAAc,GAAGI,MAAO,GAAG;AACjD,kBAAIkI,KAAQ;AACRpJ,cAAAA,GAAOqF,OAAOpE,IAAKC,EAAAA,MAAOkI,MAAS,IACnCpJ,GAAOqF,OAAOpE,KAAM,GAAGC,EAAAA,MAAOkI,MAAS,IACvCpJ,GAAOqF,OAAOpE,IAAKC,KAAM,CAAA,MAAKkI,MAAS,IACvCpJ,GAAOqF,OAAOpE,KAAM,GAAGC,KAAM,CAAA,MAAKkI,MAAS,IAClC,KAATA,MAAuB,KAATA,OAChB1D,MAAa;YAEjB;AAKF,iBAASzE,KAAM,GAAGA,KAAMH,IAAaG,MAAO,EAC1C,MAASC,KAAM,GAAGA,KAAMJ,KAAc,GAAGI,MAAO,EAC1ClB,CAAAA,GAAOqF,OAAOpE,IAAKC,EAAAA,KAAAA,CACflB,GAAOqF,OAAOpE,IAAKC,KAAM,CAAA,KACzBlB,GAAOqF,OAAOpE,IAAKC,KAAM,CAAA,KACzBlB,GAAOqF,OAAOpE,IAAKC,KAAM,CAAA,KACzBlB,GAAOqF,OAAOpE,IAAKC,KAAM,CAAA,KAAA,CACzBlB,GAAOqF,OAAOpE,IAAKC,KAAM,CAAA,KACzBlB,GAAOqF,OAAOpE,IAAKC,KAAM,CAAA,MAC/BwE,MAAa;AAKnB,iBAASxE,KAAM,GAAGA,KAAMJ,IAAaI,MAAO,EAC1C,MAASD,KAAM,GAAGA,KAAMH,KAAc,GAAGG,MAAO,EAC1CjB,CAAAA,GAAOqF,OAAOpE,IAAKC,EAAAA,KAAAA,CACflB,GAAOqF,OAAOpE,KAAM,GAAGC,EAAAA,KACvBlB,GAAOqF,OAAOpE,KAAM,GAAGC,EAAAA,KACvBlB,GAAOqF,OAAOpE,KAAM,GAAGC,EAAAA,KACvBlB,GAAOqF,OAAOpE,KAAM,GAAGC,EAAAA,KAAAA,CACvBlB,GAAOqF,OAAOpE,KAAM,GAAGC,EAAAA,KACvBlB,GAAOqF,OAAOpE,KAAM,GAAGC,EAAAA,MAC7BwE,MAAa;AAOnB,gBAAIkF,KAAY;AAEhB,iBAAS1J,KAAM,GAAGA,KAAMJ,IAAaI,MAAO,EAC1C,MAASD,KAAM,GAAGA,KAAMH,IAAaG,MAAO,EACtCjB,CAAAA,GAAOqF,OAAOpE,IAAKC,EAAAA,MACrB0J,MAAa;AAQnB,mBAFAlF,KADYvD,KAAK0I,IAAI,MAAMD,KAAY9J,KAAcA,KAAc,EAAA,IAAM,IACpD;UAGvB,GAEOJ,KAOL8J,IAAS,WAAA;AAMX,qBAJIM,KAAY,IAAI9J,MAAM,GAAA,GACtB+J,KAAY,IAAI/J,MAAM,GAAA,GAGjBlB,KAAI,GAAGA,KAAI,GAAGA,MAAK,EAC1BgL,CAAAA,GAAUhL,EAAAA,IAAK,KAAKA;AAEtB,iBAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK,EAC5BgL,CAAAA,GAAUhL,EAAAA,IAAKgL,GAAUhL,KAAI,CAAA,IACzBgL,GAAUhL,KAAI,CAAA,IACdgL,GAAUhL,KAAI,CAAA,IACdgL,GAAUhL,KAAI,CAAA;AAEpB,iBAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK,EAC5BiL,CAAAA,GAAUD,GAAUhL,EAAAA,CAAAA,IAAOA;AA2B7B,mBAxBY,EAEZY,MAAa,SAASsK,IAAAA;AAEpB,kBAAIA,KAAI,EACN,OAAM,UAAUA,KAAI;AAGtB,qBAAOD,GAAUC,EAAAA;YACnB,GAEAtK,MAAa,SAASsK,IAAAA;AAEpB,qBAAOA,KAAI,IACTA,CAAAA,MAAK;AAGP,qBAAOA,MAAK,MACVA,CAAAA,MAAK;AAGP,qBAAOF,GAAUE,EAAAA;YACnB,EAAA;UAGF,EA5Ca;AAkDb,mBAASxG,EAAayG,IAAKC,IAAAA;AAEzB,gBAAA,WAAWD,GAAInJ,OACb,OAAMmJ,GAAInJ,SAAS,MAAMoJ;AAG3B,gBAAIC,KAAO,WAAA;AAET,uBADIxH,KAAS,GACNA,KAASsH,GAAInJ,UAAyB,KAAfmJ,GAAItH,EAAAA,IAChCA,CAAAA,MAAU;AAGZ,uBADIwH,KAAO,IAAInK,MAAMiK,GAAInJ,SAAS6B,KAASuH,EAAAA,GAClCpL,KAAI,GAAGA,KAAImL,GAAInJ,SAAS6B,IAAQ7D,MAAK,EAC5CqL,CAAAA,GAAKrL,EAAAA,IAAKmL,GAAInL,KAAI6D,EAAAA;AAEpB,qBAAOwH;YACT,EAVW,GAYPzK,KAAQ,EAEZA,OAAc,SAASkE,IAAAA;AACrB,qBAAOuG,GAAKvG,EAAAA;YACd,GAEAlE,WAAkB,WAAA;AAChB,qBAAOyK,GAAKrJ;YACd,GAEApB,UAAiB,SAASb,IAAAA;AAIxB,uBAFIoL,KAAM,IAAIjK,MAAMN,GAAM2C,UAAAA,IAAcxD,GAAEwD,UAAAA,IAAc,CAAA,GAE/CvD,KAAI,GAAGA,KAAIY,GAAM2C,UAAAA,GAAavD,MAAK,EAC1C,UAASiC,KAAI,GAAGA,KAAIlC,GAAEwD,UAAAA,GAAatB,MAAK,EACtCkJ,CAAAA,GAAInL,KAAIiC,EAAAA,KAAMyI,EAAOC,KAAKD,EAAOY,KAAK1K,GAAMgE,MAAM5E,EAAAA,CAAAA,IAAO0K,EAAOY,KAAKvL,GAAE6E,MAAM3C,EAAAA,CAAAA,CAAAA;AAIjF,qBAAOyC,EAAayG,IAAK,CAAA;YAC3B,GAEAvK,KAAY,SAASb,IAAAA;AAEnB,kBAAIa,GAAM2C,UAAAA,IAAcxD,GAAEwD,UAAAA,IAAc,EACtC,QAAO3C;AAMT,uBAHI2K,KAAQb,EAAOY,KAAK1K,GAAMgE,MAAM,CAAA,CAAA,IAAO8F,EAAOY,KAAKvL,GAAE6E,MAAM,CAAA,CAAA,GAE3DuG,KAAM,IAAIjK,MAAMN,GAAM2C,UAAAA,CAAAA,GACjBvD,KAAI,GAAGA,KAAIY,GAAM2C,UAAAA,GAAavD,MAAK,EAC1CmL,CAAAA,GAAInL,EAAAA,IAAKY,GAAMgE,MAAM5E,EAAAA;AAGvB,mBAASA,KAAI,GAAGA,KAAID,GAAEwD,UAAAA,GAAavD,MAAK,EACtCmL,CAAAA,GAAInL,EAAAA,KAAM0K,EAAOC,KAAKD,EAAOY,KAAKvL,GAAE6E,MAAM5E,EAAAA,CAAAA,IAAOuL,EAAAA;AAInD,qBAAO7G,EAAayG,IAAK,CAAA,EAAG/I,IAAIrC,EAAAA;YAClC,EAAA;AAEA,mBAAOa;UACT;AAMA,cAAIqC,IAAY,2BAAA;AAEd,gBAAIuI,KAAiB,CAQnB,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,CAAA,GAGR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GAGR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GAGR,CAAC,GAAG,KAAK,EAAA,GACT,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,CAAA,GAGR,CAAC,GAAG,KAAK,GAAA,GACT,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GAGnB,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GAGR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,EAAA,GACR,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GAGnB,CAAC,GAAG,KAAK,EAAA,GACT,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GAGnB,CAAC,GAAG,KAAK,GAAA,GACT,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GAGnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GAGnB,CAAC,GAAG,KAAK,EAAA,GACT,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GAGnB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAA,GACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GAGnB,CAAC,GAAG,KAAK,GAAA,GACT,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GAGpB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GAGpB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAA,GACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GAGpB,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,EAAA,GACrB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GAGpB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GAGpB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,EAAA,GACnB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GAGpB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GAGpB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,IAAI,IAAI,EAAA,GACT,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GAGpB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,IAAI,IAAI,EAAA,GACT,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,EAAA,GAGT,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GAGpB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAA,GACvB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GAGpB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAA,GACvB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,GAAA,GACtB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAA,GACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAA,GACvB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,IAAI,KAAK,GAAA,GACV,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAA,GACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAA,GACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GAGpB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAA,GACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAA,GACvB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAA,GAGpB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAA,GACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,GAAA,GACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAA,GACvB,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,EAAA,GACpB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GAGrB,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAA,GACvB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,GACrB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAA,CAAA,GAGnBC,KAAY,SAASrH,IAAYT,IAAAA;AACnC,kBAAI/C,KAAQ,CAAC;AAGb,qBAFAA,GAAMwD,aAAaA,IACnBxD,GAAM+C,YAAYA,IACX/C;YACT,GAEIA,KAAQ,EAkBZA,aAAoB,SAAST,IAAYC,IAAAA;AAEvC,kBAAIsL,KAlBgB,SAASvL,IAAYC,IAAAA;AAEzC,wBAAOA,IAAAA;kBACP,KAAKG,EAAuB0J;AAC1B,2BAAOuB,GAAkC,KAAlBrL,KAAa,KAAS,CAAA;kBAC/C,KAAKI,EAAuB2J;AAC1B,2BAAOsB,GAAkC,KAAlBrL,KAAa,KAAS,CAAA;kBAC/C,KAAKI,EAAuB4J;AAC1B,2BAAOqB,GAAkC,KAAlBrL,KAAa,KAAS,CAAA;kBAC/C,KAAKI,EAAuB6J;AAC1B,2BAAOoB,GAAkC,KAAlBrL,KAAa,KAAS,CAAA;kBAC/C;AACE;gBAAA;cAEJ,EAIgCA,IAAYC,EAAAA;AAE1C,kBAAA,WAAWsL,GACT,OAAM,+BAA+BvL,KACjC,2BAA2BC;AAOjC,uBAJI4B,KAAS0J,GAAQ1J,SAAS,GAE1B2J,KAAO,CAAA,GAEF3L,KAAI,GAAGA,KAAIgC,IAAQhC,MAAK,EAM/B,UAJIsJ,KAAQoC,GAAY,IAAJ1L,KAAQ,CAAA,GACxBoE,KAAasH,GAAY,IAAJ1L,KAAQ,CAAA,GAC7B2D,KAAY+H,GAAY,IAAJ1L,KAAQ,CAAA,GAEvBiC,KAAI,GAAGA,KAAIqH,IAAOrH,MAAK,EAC9B0J,CAAAA,GAAKrG,KAAKmG,GAAUrH,IAAYT,EAAAA,CAAAA;AAIpC,qBAAOgI;YACT,EAAA;AAEA,mBAAO/K;UACT,EA/SgB,GAqTZwC,IAAc,WAAA;AAEhB,gBAAIwI,KAAU,CAAA,GACVC,KAAU,GAEVjL,KAAQ,EAEZA,WAAkB,WAAA;AAChB,qBAAOgL;YACT,GAEAhL,OAAc,SAASkE,IAAAA;AACrB,kBAAIgH,KAAWzJ,KAAKC,MAAMwC,KAAQ,CAAA;AAClC,qBAA0D,MAAhD8G,GAAQE,EAAAA,MAAe,IAAIhH,KAAQ,IAAO;YACtD,GAEAlE,KAAY,SAASuK,IAAKnJ,IAAAA;AACxB,uBAAShC,KAAI,GAAGA,KAAIgC,IAAQhC,MAAK,EAC/BY,CAAAA,GAAMgD,OAA8C,MAAnCuH,OAASnJ,KAAShC,KAAI,IAAO,EAAA;YAElD,GAEAY,iBAAwB,WAAA;AACtB,qBAAOiL;YACT,GAEAjL,QAAe,SAASmL,IAAAA;AAEtB,kBAAID,KAAWzJ,KAAKC,MAAMuJ,KAAU,CAAA;AAChCD,cAAAA,GAAQ5J,UAAU8J,MACpBF,GAAQtG,KAAK,CAAA,GAGXyG,OACFH,GAAQE,EAAAA,KAAc,QAAUD,KAAU,IAG5CA,MAAW;YACb,EAAA;AAEA,mBAAOjL;UACT,GAMIsE,IAAW,SAAS3C,IAAAA;AAEtB,gBACIyJ,KAAQzJ,IAER3B,KAAQ,EAEZA,SAAgB,WAAA;AACd,qBAtxBe;YAuxBjB,GAEAA,WAAkB,SAASuC,IAAAA;AACzB,qBAAO6I,GAAMhK;YACf,GAEApB,OAAc,SAASuC,IAAAA;AAMrB,uBAJIZ,KAAOyJ,IAEPhM,KAAI,GAEDA,KAAI,IAAIuC,GAAKP,SAClBmB,CAAAA,GAAOE,IAAI4I,GAAS1J,GAAK2F,UAAUlI,IAAGA,KAAI,CAAA,CAAA,GAAM,EAAA,GAChDA,MAAK;AAGHA,cAAAA,KAAIuC,GAAKP,WACPO,GAAKP,SAAShC,MAAK,IACrBmD,GAAOE,IAAI4I,GAAS1J,GAAK2F,UAAUlI,IAAGA,KAAI,CAAA,CAAA,GAAM,CAAA,IACvCuC,GAAKP,SAAShC,MAAK,KAC5BmD,GAAOE,IAAI4I,GAAS1J,GAAK2F,UAAUlI,IAAGA,KAAI,CAAA,CAAA,GAAM,CAAA;YAGtD,EAAA,GAEIiM,KAAW,SAASzE,IAAAA;AAEtB,uBADI2D,KAAM,GACDnL,KAAI,GAAGA,KAAIwH,GAAExF,QAAQhC,MAAK,EACjCmL,CAAAA,KAAY,KAANA,KAAWe,GAAU1E,GAAEE,OAAO1H,EAAAA,CAAAA;AAEtC,qBAAOmL;YACT,GAEIe,KAAY,SAAStK,IAAAA;AACvB,kBAAI,OAAOA,MAAKA,MAAK,IACnB,QAAOA,GAAEiH,WAAW,CAAA,IAAK,IAAIA,WAAW,CAAA;AAE1C,oBAAM,mBAAmBjH;YAC3B;AAEA,mBAAOhB;UACT,GAMIuE,IAAa,SAAS5C,IAAAA;AAExB,gBACIyJ,KAAQzJ,IAER3B,KAAQ,EAEZA,SAAgB,WAAA;AACd,qBA90Be;YA+0BjB,GAEAA,WAAkB,SAASuC,IAAAA;AACzB,qBAAO6I,GAAMhK;YACf,GAEApB,OAAc,SAASuC,IAAAA;AAMrB,uBAJIqE,KAAIwE,IAEJhM,KAAI,GAEDA,KAAI,IAAIwH,GAAExF,SACfmB,CAAAA,GAAOE,IACmB,KAAxB8I,GAAQ3E,GAAEE,OAAO1H,EAAAA,CAAAA,IACjBmM,GAAQ3E,GAAEE,OAAO1H,KAAI,CAAA,CAAA,GAAM,EAAA,GAC7BA,MAAK;AAGHA,cAAAA,KAAIwH,GAAExF,UACRmB,GAAOE,IAAI8I,GAAQ3E,GAAEE,OAAO1H,EAAAA,CAAAA,GAAM,CAAA;YAEtC,EAAA,GAEImM,KAAU,SAASvK,IAAAA;AAErB,kBAAI,OAAOA,MAAKA,MAAK,IACnB,QAAOA,GAAEiH,WAAW,CAAA,IAAK,IAAIA,WAAW,CAAA;AACnC,kBAAI,OAAOjH,MAAKA,MAAK,IAC1B,QAAOA,GAAEiH,WAAW,CAAA,IAAK,IAAIA,WAAW,CAAA,IAAK;AAE7C,sBAAQjH,IAAAA;gBACR,KAAK;AAAM,yBAAO;gBAClB,KAAK;AAAM,yBAAO;gBAClB,KAAK;AAAM,yBAAO;gBAClB,KAAK;AAAM,yBAAO;gBAClB,KAAK;AAAM,yBAAO;gBAClB,KAAK;AAAM,yBAAO;gBAClB,KAAK;AAAM,yBAAO;gBAClB,KAAK;AAAM,yBAAO;gBAClB,KAAK;AAAM,yBAAO;gBAClB;AACE,wBAAM,mBAAmBA;cAAAA;YAG/B;AAEA,mBAAOhB;UACT,GAMIwE,IAAa,SAAS7C,IAAAA;AAExB,gBAEI6J,KAASlM,GAAOwI,cAAcnG,EAAAA;AAkBlC,mBAhBY,EAEZ3B,SAAgB,WAAA;AACd,qBA74Be;YA84BjB,GAEAA,WAAkB,SAASuC,IAAAA;AACzB,qBAAOiJ,GAAOpK;YAChB,GAEApB,OAAc,SAASuC,IAAAA;AACrB,uBAASnD,KAAI,GAAGA,KAAIoM,GAAOpK,QAAQhC,MAAK,EACtCmD,CAAAA,GAAOE,IAAI+I,GAAOpM,EAAAA,GAAI,CAAA;YAE1B,EAAA;UAGF,GAMIqF,IAAU,SAAS9C,IAAAA;AAErB,gBAGImG,KAAgBxI,GAAOyI,mBAAyB;AACpD,gBAAA,CAAKD,GACH,OAAM;AAAA,aAEP,WAAA;AAEC,kBAAI5H,KAAO4H,GAIX,GAAA;AAHA,kBAAmB,KAAf5H,GAAKkB,UAGC,UAHiBlB,GAAK,CAAA,KAAM,IAAKA,GAAK,CAAA,GAC9C,OAAM;YAEV,EANC;AAQD,gBAAIsL,KAAS1D,GAAcnG,EAAAA,GAEvB3B,KAAQ,EAEZA,SAAgB,WAAA;AACd,qBAt7Be;YAu7BjB,GAEAA,WAAkB,SAASuC,IAAAA;AACzB,qBAAA,CAAA,EAAUiJ,GAAOpK,SAAS;YAC5B,GAEApB,OAAc,SAASuC,IAAAA;AAMrB,uBAJIZ,KAAO6J,IAEPpM,KAAI,GAEDA,KAAI,IAAIuC,GAAKP,UAAQ;AAE1B,oBAAIJ,MAAO,MAAOW,GAAKvC,EAAAA,MAAO,IAAM,MAAOuC,GAAKvC,KAAI,CAAA;AAEpD,oBAAI,SAAU4B,MAAKA,MAAK,MACtBA,CAAAA,MAAK;qBACA;AAAA,sBAAA,EAAI,SAAUA,MAAKA,MAAK,OAG7B,OAAM,sBAAsB5B,KAAI,KAAK,MAAM4B;AAF3CA,kBAAAA,MAAK;gBAGP;AAEAA,gBAAAA,KAA0B,OAAnBA,OAAM,IAAK,QAAoB,MAAJA,KAElCuB,GAAOE,IAAIzB,IAAG,EAAA,GAEd5B,MAAK;cACP;AAEA,kBAAIA,KAAIuC,GAAKP,OACX,OAAM,sBAAsBhC,KAAI;YAEpC,EAAA;AAEA,mBAAOY;UACT,GAUIyL,IAAwB,WAAA;AAE1B,gBAAID,KAAS,CAAA,GAETxL,KAAQ,EAEZA,WAAkB,SAASyI,IAAAA;AACzB+C,cAAAA,GAAO9G,KAAS,MAAJ+D,EAAAA;YACd,GAEAzI,YAAmB,SAASZ,IAAAA;AAC1BY,cAAAA,GAAM0L,UAAUtM,EAAAA,GAChBY,GAAM0L,UAAUtM,OAAM,CAAA;YACxB,GAEAY,YAAmB,SAASyI,IAAGkD,IAAKC,IAAAA;AAClCD,cAAAA,KAAMA,MAAO,GACbC,KAAMA,MAAOnD,GAAErH;AACf,uBAAShC,KAAI,GAAGA,KAAIwM,IAAKxM,MAAK,EAC5BY,CAAAA,GAAM0L,UAAUjD,GAAErJ,KAAIuM,EAAAA,CAAAA;YAE1B,GAEA3L,aAAoB,SAAS4G,IAAAA;AAC3B,uBAASxH,KAAI,GAAGA,KAAIwH,GAAExF,QAAQhC,MAAK,EACjCY,CAAAA,GAAM0L,UAAU9E,GAAEqB,WAAW7I,EAAAA,CAAAA;YAEjC,GAEAY,aAAoB,WAAA;AAClB,qBAAOwL;YACT,GAEAxL,UAAiB,WAAA;AACf,kBAAI4G,KAAI;AACRA,cAAAA,MAAK;AACL,uBAASxH,KAAI,GAAGA,KAAIoM,GAAOpK,QAAQhC,MAAK,EAClCA,CAAAA,KAAI,MACNwH,MAAK,MAEPA,MAAK4E,GAAOpM,EAAAA;AAGd,qBADAwH,KAAK;YAEP,EAAA;AAEA,mBAAO5G;UACT,GA4EIuI,IAA0B,SAASsD,IAAAA;AAErC,gBAAIC,KAAOD,IACPE,KAAO,GACPf,KAAU,GACVgB,KAAU,GAEVhM,KAAQ,EAEZA,MAAa,WAAA;AAEX,qBAAOgM,KAAU,KAAG;AAElB,oBAAID,MAAQD,GAAK1K,QAAQ;AACvB,sBAAe,KAAX4K,GACF,QAAA;AAEF,wBAAM,6BAA6BA;gBACrC;AAEA,oBAAIhL,KAAI8K,GAAKhF,OAAOiF,EAAAA;AAGpB,oBAFAA,MAAQ,GAEC,OAAL/K,GAEF,QADAgL,KAAU,GAAA;AAEDhL,gBAAAA,GAAEiL,MAAM,MAAA,MAKnBjB,KAAWA,MAAW,IAAKkB,GAAOlL,GAAEiH,WAAW,CAAA,CAAA,GAC/C+D,MAAW;cACb;AAEA,kBAAI1B,KAAKU,OAAagB,KAAU,IAAO;AAEvC,qBADAA,MAAW,GACJ1B;YACT,EAAA,GAEI4B,KAAS,SAASlL,IAAAA;AACpB,kBAAI,MAAQA,MAAKA,MAAK,GACpB,QAAOA,KAAI;AACN,kBAAI,MAAQA,MAAKA,MAAK,IAC3B,QAAOA,KAAI,KAAO;AACb,kBAAI,MAAQA,MAAKA,MAAK,GAC3B,QAAOA,KAAI,KAAO;AACb,kBAAS,MAALA,GACT,QAAO;AACF,kBAAS,MAALA,GACT,QAAO;AAEP,oBAAM,OAAOA;YAEjB;AAEA,mBAAOhB;UACT,GAwNIsG,IAAgB,SAAS6F,IAAOC,IAAQC,IAAAA;AAE1C,qBADIC,KAnNS,SAASH,IAAOC,IAAAA;AAE7B,kBAAIG,KAASJ,IACTK,KAAUJ,IACVhB,KAAQ,IAAI9K,MAAM6L,KAAQC,EAAAA,GAE1BpM,KAAQ,EAEZA,UAAiB,SAASwG,IAAGC,IAAGgG,IAAAA;AAC9BrB,gBAAAA,GAAM3E,KAAI8F,KAAS/F,EAAAA,IAAKiG;cAC1B,GAEAzM,OAAc,SAAS0M,IAAAA;AAKrBA,gBAAAA,GAAIC,YAAY,QAAA,GAKhBD,GAAIE,WAAWL,EAAAA,GACfG,GAAIE,WAAWJ,EAAAA,GAEfE,GAAIhB,UAAU,GAAA,GACdgB,GAAIhB,UAAU,CAAA,GACdgB,GAAIhB,UAAU,CAAA,GAMdgB,GAAIhB,UAAU,CAAA,GACdgB,GAAIhB,UAAU,CAAA,GACdgB,GAAIhB,UAAU,CAAA,GAGdgB,GAAIhB,UAAU,GAAA,GACdgB,GAAIhB,UAAU,GAAA,GACdgB,GAAIhB,UAAU,GAAA,GAKdgB,GAAIC,YAAY,GAAA,GAChBD,GAAIE,WAAW,CAAA,GACfF,GAAIE,WAAW,CAAA,GACfF,GAAIE,WAAWL,EAAAA,GACfG,GAAIE,WAAWJ,EAAAA,GACfE,GAAIhB,UAAU,CAAA;AAQd,oBACImB,KAASC,GADQ,CAAA;AAGrBJ,gBAAAA,GAAIhB,UAHiB,CAAA;AAOrB,yBAFIzI,KAAS,GAEN4J,GAAOzL,SAAS6B,KAAS,MAC9ByJ,CAAAA,GAAIhB,UAAU,GAAA,GACdgB,GAAIK,WAAWF,IAAQ5J,IAAQ,GAAA,GAC/BA,MAAU;AAGZyJ,gBAAAA,GAAIhB,UAAUmB,GAAOzL,SAAS6B,EAAAA,GAC9ByJ,GAAIK,WAAWF,IAAQ5J,IAAQ4J,GAAOzL,SAAS6B,EAAAA,GAC/CyJ,GAAIhB,UAAU,CAAA,GAIdgB,GAAIC,YAAY,GAAA;cAClB,EAAA,GAqCIG,KAAe,SAASE,IAAAA;AAS1B,yBAPIC,KAAY,KAAKD,IACjBE,KAAkC,KAAvB,KAAKF,KAChBG,KAAYH,KAAiB,GAG7BI,KAAQC,GAAAA,GAEHjO,KAAI,GAAGA,KAAI6N,IAAW7N,MAAK,EAClCgO,CAAAA,GAAME,IAAIxE,OAAOC,aAAa3J,EAAAA,CAAAA;AAEhCgO,gBAAAA,GAAME,IAAIxE,OAAOC,aAAakE,EAAAA,CAAAA,GAC9BG,GAAME,IAAIxE,OAAOC,aAAamE,EAAAA,CAAAA;AAE9B,oBAhDIK,IACAC,IACAC,IA8CAC,KAAUjC,EAAAA,GACVkC,MAjDAJ,KAiDyBG,IAhDzBF,KAAa,GACbC,KAAa,GAEL,EAEZzN,OAAc,SAAS2B,IAAMP,IAAAA;AAE3B,sBAAMO,OAASP,MAAW,EACxB,OAAM;AAGR,yBAAOoM,KAAapM,MAAU,IAC5BmM,CAAAA,GAAK7B,UAAU,OAAU/J,MAAQ6L,KAAcC,GAAAA,GAC/CrM,MAAW,IAAIoM,IACf7L,QAAW,IAAI6L,IACfC,KAAa,GACbD,KAAa;AAGfC,kBAAAA,MAAc9L,MAAQ6L,IACtBA,MAA0BpM;gBAC5B,GAEApB,OAAc,WAAA;AACRwN,kBAAAA,KAAa,KACfD,GAAK7B,UAAU+B,EAAAA;gBAEnB,EAAA;AAwBAE,gBAAAA,GAAO9K,MAAMoK,IAAWE,EAAAA;AAExB,oBAAIS,KAAY,GAEZhH,KAAIkC,OAAOC,aAAaqC,GAAMwC,EAAAA,CAAAA;AAGlC,qBAFAA,MAAa,GAENA,KAAYxC,GAAMhK,UAAQ;AAE/B,sBAAIJ,KAAI8H,OAAOC,aAAaqC,GAAMwC,EAAAA,CAAAA;AAClCA,kBAAAA,MAAa,GAETR,GAAMS,SAASjH,KAAI5F,EAAAA,IAErB4F,MAAQ5F,MAIR2M,GAAO9K,MAAMuK,GAAMU,QAAQlH,EAAAA,GAAIuG,EAAAA,GAE3BC,GAAMpH,KAAAA,IAAS,SAEboH,GAAMpH,KAAAA,KAAW,KAAKmH,OACxBA,MAAa,IAGfC,GAAME,IAAI1G,KAAI5F,EAAAA,IAGhB4F,KAAI5F;gBAER;AASA,uBAPA2M,GAAO9K,MAAMuK,GAAMU,QAAQlH,EAAAA,GAAIuG,EAAAA,GAG/BQ,GAAO9K,MAAMqK,IAASC,EAAAA,GAEtBQ,GAAOI,MAAAA,GAEAL,GAAQM,YAAAA;cACjB,GAEIX,KAAW,WAAA;AAEb,oBAAIY,KAAO,CAAC,GACRC,KAAQ,GAERlO,KAAQ,EAEZA,KAAY,SAASmO,IAAAA;AACnB,sBAAInO,GAAM6N,SAASM,EAAAA,EACjB,OAAM,aAAaA;AAErBF,kBAAAA,GAAKE,EAAAA,IAAOD,IACZA,MAAS;gBACX,GAEAlO,MAAa,WAAA;AACX,yBAAOkO;gBACT,GAEAlO,SAAgB,SAASmO,IAAAA;AACvB,yBAAOF,GAAKE,EAAAA;gBACd,GAEAnO,UAAiB,SAASmO,IAAAA;AACxB,yBAAA,WAAcF,GAAKE,EAAAA;gBACrB,EAAA;AAEA,uBAAOnO;cACT;AAEA,qBAAOA;YACT,EAGqBmM,IAAOC,EAAAA,GACjB3F,KAAI,GAAGA,KAAI2F,IAAQ3F,MAAK,EAC/B,UAASD,KAAI,GAAGA,KAAI2F,IAAO3F,MAAK,EAC9B8F,CAAAA,GAAI8B,SAAS5H,IAAGC,IAAG4F,GAAS7F,IAAGC,EAAAA,CAAAA;AAInC,gBAAIgC,KAAIgD,EAAAA;AACRa,YAAAA,GAAIzJ,MAAM4F,EAAAA;AAIV,qBAFI4F,KAlWyB,WAAA;AAE7B,kBAAIrD,KAAU,GACVgB,KAAU,GACVf,KAAU,GACVqD,KAAU,IAEVtO,KAAQ,CAAC,GAETuO,KAAe,SAAS9F,IAAAA;AAC1B6F,gBAAAA,MAAWxF,OAAOC,aAAayF,GAAW,KAAJ/F,EAAAA,CAAAA;cACxC,GAEI+F,KAAS,SAASlE,IAAAA;AACpB,oBAAIA,KAAI,EAAA;qBAED;AAAA,sBAAIA,KAAI,GACb,QAAO,KAAOA;AACT,sBAAIA,KAAI,GACb,QAAeA,KAAI,KAAZ;AACF,sBAAIA,KAAI,GACb,QAAeA,KAAI,KAAZ;AACF,sBAAS,MAALA,GACT,QAAO;AACF,sBAAS,MAALA,GACT,QAAO;gBACT;AACA,sBAAM,OAAOA;cACf;AAmCA,qBAjCAtK,GAAM0L,YAAY,SAASpB,IAAAA;AAMzB,qBAJAU,KAAWA,MAAW,IAAU,MAAJV,IAC5B0B,MAAW,GACXf,MAAW,GAEJe,MAAW,IAChBuC,CAAAA,GAAavD,OAAagB,KAAU,CAAA,GACpCA,MAAW;cAEf,GAEAhM,GAAM+N,QAAQ,WAAA;AAQZ,oBANI/B,KAAU,MACZuC,GAAavD,MAAY,IAAIgB,EAAAA,GAC7BhB,KAAU,GACVgB,KAAU,IAGRf,KAAU,KAAK,EAGjB,UADIwD,KAAS,IAAIxD,KAAU,GAClB7L,KAAI,GAAGA,KAAIqP,IAAQrP,MAAK,EAC/BkP,CAAAA,MAAW;cAGjB,GAEAtO,GAAM0O,WAAW,WAAA;AACf,uBAAOJ;cACT,GAEOtO;YACT,EAkSe2O,GACT3G,KAAQS,GAAEuF,YAAAA,GACL5O,KAAI,GAAGA,KAAI4I,GAAM5G,QAAQhC,MAAK,EACrCiP,CAAAA,GAAO3C,UAAU1D,GAAM5I,EAAAA,CAAAA;AAIzB,mBAFAiP,GAAON,MAAAA,GAEA,2BAA2BM;UACpC;AAKA,iBAAO/O;QACT,EArrEa;AA0rEXA,UAAOyI,mBAAmB,OAAA,IAAW,SAASnB,IAAAA;AAgC5C,iBA9BA,SAAqBiF,IAAAA;AAEnB,qBADI+C,KAAO,CAAA,GACFxP,KAAE,GAAGA,KAAIyM,GAAIzK,QAAQhC,MAAK;AACjC,kBAAIyP,KAAWhD,GAAI5D,WAAW7I,EAAAA;AAC1ByP,cAAAA,KAAW,MAAMD,GAAKlK,KAAKmK,EAAAA,IACtBA,KAAW,OAClBD,GAAKlK,KAAK,MAAQmK,MAAY,GAC1B,MAAmB,KAAXA,EAAAA,IAELA,KAAW,SAAUA,MAAY,QACxCD,GAAKlK,KAAK,MAAQmK,MAAY,IAC1B,MAASA,MAAU,IAAK,IACxB,MAAmB,KAAXA,EAAAA,KAIZzP,MAIAyP,KAAW,UAAwB,OAAXA,OAAmB,KAClB,OAApBhD,GAAI5D,WAAW7I,EAAAA,IACpBwP,GAAKlK,KAAK,MAAQmK,MAAW,IACzB,MAASA,MAAU,KAAM,IACzB,MAASA,MAAU,IAAK,IACxB,MAAmB,KAAXA,EAAAA;YAEhB;AACA,mBAAOD;UACT,EACmBhI,EAAAA;QACrB,GAAA,YAMsBvH,KAAA,cAAA,QAAPD,KAIf,WAAA;AACE,iBAAOE;QACX,KANwBF,GAAA,MAAAD,IAAX,CAAA,CAAA,IAAWC,QAAAF,GAAA,UAAAG;MAAA,EAAA,GCjvEpByP,IAA2B,CAAC;AAGhC,eAASC,EAAoBC,IAAAA;AAE5B,YAAIC,IAAeH,EAAyBE,EAAAA;AAC5C,YAAA,WAAIC,EACH,QAAOA,EAAapQ;AAGrB,YAAIC,IAASgQ,EAAyBE,EAAAA,IAAY,EAGjDnQ,SAAS,CAAC,EAAA;AAOX,eAHAqQ,EAAoBF,EAAAA,EAAUlQ,GAAQA,EAAOD,SAASkQ,CAAAA,GAG/CjQ,EAAOD;MACf;ACrBAkQ,QAAoBzE,IAAKxL,CAAAA,OAAAA;AACxB,YAAIqQ,KAASrQ,MAAUA,GAAOsQ,aAC7B,MAAOtQ,GAAiB,UACxB,MAAMI;AAEP,eADA6P,EAAoBrF,EAAEyF,IAAQ,EAAEvF,GAAGuF,GAAAA,CAAAA,GAC5BA;MAAM,GCLdJ,EAAoBrF,IAAI,CAAC7K,IAASwQ,OAAAA;AACjC,iBAAQlB,MAAOkB,GACXN,GAAoBO,EAAED,IAAYlB,EAAAA,KAAAA,CAASY,EAAoBO,EAAEzQ,IAASsP,EAAAA,KAC5EoB,OAAOC,eAAe3Q,IAASsP,IAAK,EAAEsB,YAAAA,MAAkBC,KAAKL,GAAWlB,EAAAA,EAAAA,CAAAA;MAE1E,GCNDY,EAAoBO,IAAI,CAACK,IAAKC,OAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,IAAKC,EAAAA;AAAAA,UAAAA,IAAAA,CAAAA;AAAAA,cAAAA,MAAAA;AAAAA;AAAAA,UAAAA,EAAAA,GAAAA,EAAAA,SAAAA,MAAAA,EAAAA,CAAAA;ACMlF,cCJMI,KAAYL,CAAAA,OAAAA,CAAAA,CAA4CA,MAAsB,YAAA,OAARA,MAAAA,CAAqBrP,MAAM2P,QAAQN,EAAAA;AAEhG,iBAASO,GAAUC,OAA0BC,IAAAA;AAC1D,cAAA,CAAKA,GAAQhP,OAAQ,QAAO+O;AAC5B,gBAAME,KAASD,GAAQ5F,MAAAA;AACvB,iBAAA,WAAI6F,MAAyBL,GAASG,EAAAA,KAAYH,GAASK,EAAAA,KAC3DF,KAAS,OAAH,OAAA,CAAA,GAAQA,EAAAA,GACdZ,OAAOe,KAAKD,EAAAA,EAAQE,QAASpC,CAAAA,OAAAA;AAC3B,kBAAMqC,KAAcL,GAAOhC,EAAAA,GACrBsC,KAAcJ,GAAOlC,EAAAA;AAEvB7N,kBAAM2P,QAAQO,EAAAA,KAAgBlQ,MAAM2P,QAAQQ,EAAAA,IAC9CN,GAAOhC,EAAAA,IAAOsC,KACLT,GAASQ,EAAAA,KAAgBR,GAASS,EAAAA,IAC3CN,GAAOhC,EAAAA,IAAO+B,GAAUX,OAAOmB,OAAO,CAAC,GAAGF,EAAAA,GAAcC,EAAAA,IAExDN,GAAOhC,EAAAA,IAAOsC;UAChB,CAAA,GAGKP,GAAUC,IAAAA,GAAWC,EAAAA,KAf+CD;QAgB7E;ACvBe,iBAASQ,EAAYC,IAAaC,IAAAA;AAC/C,gBAAMC,KAAOC,SAASC,cAAc,GAAA;AACpCF,UAAAA,GAAKG,WAAWJ,IAChBC,GAAKI,OAAON,IACZG,SAASI,KAAKC,YAAYN,EAAAA,GAC1BA,GAAKO,MAAAA,GACLN,SAASI,KAAKG,YAAYR,EAAAA;QAC5B;ACHA,cAAA,IAAA,EACEzH,GAAG,MACHC,GAAG,MACHC,GAAG,MACHC,GAAG,IAAA;QCLU,MAAM+H,EAAAA;UAMnB,YAAAC,EAAY,KAAEC,IAAG,MAAEzH,IAAI,QAAE0H,GAAAA,GAAAA;AACvBzS,iBAAK0S,OAAOF,IACZxS,KAAK2S,QAAQ5H,IACb/K,KAAK4S,UAAUH;UACjB;UAEA,KAAKlL,IAAWC,IAAWT,IAAc8L,IAAAA;AAEvC,gBAAIC;AAEJ,oBAHa9S,KAAK2S,OAAAA;cAIhB,KCjBE;ADkBAG,gBAAAA,KAAe9S,KAAK+S;AACpB;cACF,KClBI;ADmBFD,gBAAAA,KAAe9S,KAAKgT;AACpB;cACF,KCpBW;ADqBTF,gBAAAA,KAAe9S,KAAKiT;AACpB;cACF,KCzBK;AD0BHH,gBAAAA,KAAe9S,KAAKkT;AACpB;cACF,KCxBU;ADyBRJ,gBAAAA,KAAe9S,KAAKmT;AACpB;cAEF;AACEL,gBAAAA,KAAe9S,KAAKoT;YAAAA;AAGxBN,YAAAA,GAAahC,KAAK9Q,MAAM,EAAEuH,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAM8L,aAAAA,GAAAA,CAAAA;UACxC;UAEA,cAAAQ,EAAc,GAAE9L,IAAC,GAAEC,IAAC,MAAET,IAAI,UAAEuM,KAAW,GAAC,MAAEC,GAAAA,GAAAA;AAAAA,gBAAAA;AACxC,kBAAMC,KAAKjM,KAAIR,KAAO,GAChB0M,KAAKjM,KAAIT,KAAO;AAEtBwM,YAAAA,GAAAA,GACa,UAAblD,KAAArQ,KAAK0T,aAAAA,WAAQrD,MAAAA,GAAEsD,aAAa,aAAa,UAAW,MAAML,KAAY9Q,KAAKoR,EAAAA,IAAMJ,EAAAA,IAAMC,EAAAA,GAAAA;UACzF;UAEA,UAAUI,IAAAA;AACR,kBAAA,EAAM,MAAE9M,IAAI,GAAEQ,IAAC,GAAEC,GAAAA,IAAMqM;AAEvB7T,iBAAKqT,cAAc,OAAD,OAAA,OAAA,OAAA,CAAA,GACbQ,EAAAA,GAAI,EACPN,MAAM,MAAA;AACJvT,mBAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,QAAA,GACpF9T,KAAK0T,SAASC,aAAa,MAAM9J,OAAOtC,KAAIR,KAAO,CAAA,CAAA,GACnD/G,KAAK0T,SAASC,aAAa,MAAM9J,OAAOrC,KAAIT,KAAO,CAAA,CAAA,GACnD/G,KAAK0T,SAASC,aAAa,KAAK9J,OAAO9C,KAAO,CAAA,CAAA;YAAG,EAAA,CAAA,CAAA;UAGvD;UAEA,aAAa8M,IAAAA;AACX,kBAAA,EAAM,MAAE9M,IAAI,GAAEQ,IAAC,GAAEC,GAAAA,IAAMqM;AAEvB7T,iBAAKqT,cAAc,OAAD,OAAA,OAAA,OAAA,CAAA,GACbQ,EAAAA,GAAI,EACPN,MAAM,MAAA;AACJvT,mBAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA,GACpF9T,KAAK0T,SAASC,aAAa,KAAK9J,OAAOtC,EAAAA,CAAAA,GACvCvH,KAAK0T,SAASC,aAAa,KAAK9J,OAAOrC,EAAAA,CAAAA,GACvCxH,KAAK0T,SAASC,aAAa,SAAS9J,OAAO9C,EAAAA,CAAAA,GAC3C/G,KAAK0T,SAASC,aAAa,UAAU9J,OAAO9C,EAAAA,CAAAA;YAAM,EAAA,CAAA,CAAA;UAGxD;UAGA,kBAAkB8M,IAAAA;AAChB,kBAAA,EAAM,MAAE9M,IAAI,GAAEQ,IAAC,GAAEC,GAAAA,IAAMqM;AAEvB7T,iBAAKqT,cAAc,OAAD,OAAA,OAAA,OAAA,CAAA,GACbQ,EAAAA,GAAI,EACPN,MAAM,MAAA;AACJvT,mBAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA,GACpF9T,KAAK0T,SAASC,aACZ,KACA,KAAKpM,EAAAA,IAAKC,EAAAA,KACHT,EAAAA,OACAA,KAAO,IACZ,KAAKA,KAAO,CAAA,IAAKA,KAAO,CAAA,gBAAA,CAAkBA,EAAAA,EAAAA;YAC7C,EAAA,CAAA,CAAA;UAGP;UAGA,oBAAoB8M,IAAAA;AAClB,kBAAA,EAAM,MAAE9M,IAAI,GAAEQ,IAAC,GAAEC,GAAAA,IAAMqM;AAEvB7T,iBAAKqT,cAAc,OAAD,OAAA,OAAA,OAAA,CAAA,GACbQ,EAAAA,GAAI,EACPN,MAAM,MAAA;AACJvT,mBAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA,GACpF9T,KAAK0T,SAASC,aACZ,KACA,KAAKpM,EAAAA,IAAKC,EAAAA,KACHT,EAAAA,KACAA,EAAAA,OAAAA,CACCA,KAAO,IACb,KAAKA,KAAO,CAAA,IAAKA,KAAO,CAAA,cAAA,CAAgBA,KAAO,CAAA,IAAA,CAAMA,KAAO,CAAA,EAAA;YAC/D,EAAA,CAAA,CAAA;UAGP;UAGA,yBAAyB8M,IAAAA;AACvB,kBAAA,EAAM,MAAE9M,IAAI,GAAEQ,IAAC,GAAEC,GAAAA,IAAMqM;AAEvB7T,iBAAKqT,cAAc,OAAD,OAAA,OAAA,OAAA,CAAA,GACbQ,EAAAA,GAAI,EACPN,MAAM,MAAA;AACJvT,mBAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA,GACpF9T,KAAK0T,SAASC,aACZ,KACA,KAAKpM,EAAAA,IAAKC,EAAAA,KACHT,EAAAA,KACAA,EAAAA,KACAA,EAAAA,IAAQA,EAAAA,cAAAA,CAAmBA,EAAAA,IAAAA,CAASA,EAAAA,EAAAA;YAC5C,EAAA,CAAA,CAAA;UAGP;UAGA,qBAAqB8M,IAAAA;AACnB,kBAAA,EAAM,MAAE9M,IAAI,GAAEQ,IAAC,GAAEC,GAAAA,IAAMqM;AAEvB7T,iBAAKqT,cAAc,OAAD,OAAA,OAAA,OAAA,CAAA,GACbQ,EAAAA,GAAI,EACPN,MAAM,MAAA;AACJvT,mBAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA,GACpF9T,KAAK0T,SAASC,aACZ,KACA,KAAKpM,EAAAA,IAAKC,EAAAA,OACHT,KAAO,IACZ,KAAKA,KAAO,CAAA,IAAKA,KAAO,CAAA,cAAeA,KAAO,CAAA,IAAKA,KAAO,CAAA,OACrDA,KAAO,IACZ,OAAA,CAAMA,KAAO,IACb,KAAKA,KAAO,CAAA,IAAKA,KAAO,CAAA,cAAA,CAAgBA,KAAO,CAAA,IAAA,CAAMA,KAAO,CAAA,EAAA;YAC/D,EAAA,CAAA,CAAA;UAGP;UAEA,SAAAgM,EAAS,GAAExL,IAAC,GAAEC,IAAC,MAAET,GAAAA,GAAAA;AACf/G,iBAAK+T,UAAU,EAAExM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU,EAAA,CAAA;UACzC;UAEA,YAAAF,EAAY,GAAE7L,IAAC,GAAEC,IAAC,MAAET,GAAAA,GAAAA;AAClB/G,iBAAKgU,aAAa,EAAEzM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU,EAAA,CAAA;UAC5C;UAEA,aAAAJ,EAAa,GAAE3L,IAAC,GAAEC,IAAC,MAAET,IAAI,aAAE8L,GAAAA,GAAAA;AACzB,kBAAMoB,KAAepB,KAAAA,CAAeA,GAAAA,IAAgB,CAAA,IAAK,GACnDqB,KAAgBrB,KAAAA,CAAeA,GAAY,GAAG,CAAA,IAAK,GACnDsB,KAActB,KAAAA,CAAeA,GAAY,GAAA,EAAI,IAAK,GAClDuB,KAAiBvB,KAAAA,CAAeA,GAAY,GAAG,CAAA,IAAK,GAEpDwB,KAAiBJ,KAAeC,KAAgBC,KAAcC;AAEpE,gBAAuB,MAAnBC,GAKJ,KAAIA,KAAiB,KAAMJ,MAAgBC,MAAmBC,MAAeC,GAC3EpU,MAAKgU,aAAa,EAAEzM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU,EAAA,CAAA;iBAD5C;AAKA,kBAAuB,MAAnBe,IAAsB;AACxB,oBAAIf,KAAW;AAWf,uBATIW,MAAgBE,KAClBb,KAAW9Q,KAAKoR,KAAK,IACZO,MAAeD,KACxBZ,KAAW9Q,KAAKoR,KACPM,MAAiBE,OAC1Bd,KAAAA,CAAY9Q,KAAKoR,KAAK,IAAA,KAGxB5T,KAAKsU,oBAAoB,EAAE/M,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,GAAAA,CAAAA;cAEzC;AAEA,kBAAuB,MAAnBe,IAAsB;AACxB,oBAAIf,KAAW;AAWf,uBATIa,KACFb,KAAW9Q,KAAKoR,KAAK,IACZM,KACTZ,KAAW9Q,KAAKoR,KACPQ,OACTd,KAAAA,CAAY9Q,KAAKoR,KAAK,IAAA,KAGxB5T,KAAKuU,kBAAkB,EAAEhN,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,GAAAA,CAAAA;cAEvC;YA9BA;gBAPEtT,MAAK+T,UAAU,EAAExM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU,EAAA,CAAA;UAsC3C;UAEA,kBAAAH,EAAkB,GAAE5L,IAAC,GAAEC,IAAC,MAAET,IAAI,aAAE8L,GAAAA,GAAAA;AAC9B,kBAAMoB,KAAepB,KAAAA,CAAeA,GAAAA,IAAgB,CAAA,IAAK,GACnDqB,KAAgBrB,KAAAA,CAAeA,GAAY,GAAG,CAAA,IAAK,GACnDsB,KAActB,KAAAA,CAAeA,GAAY,GAAA,EAAI,IAAK,GAClDuB,KAAiBvB,KAAAA,CAAeA,GAAY,GAAG,CAAA,IAAK,GAEpDwB,KAAiBJ,KAAeC,KAAgBC,KAAcC;AAEpE,gBAAuB,MAAnBC,GAKJ,KAAIA,KAAiB,KAAMJ,MAAgBC,MAAmBC,MAAeC,GAC3EpU,MAAKgU,aAAa,EAAEzM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU,EAAA,CAAA;iBAD5C;AAKA,kBAAuB,MAAnBe,IAAsB;AACxB,oBAAIf,KAAW;AAWf,uBATIW,MAAgBE,KAClBb,KAAW9Q,KAAKoR,KAAK,IACZO,MAAeD,KACxBZ,KAAW9Q,KAAKoR,KACPM,MAAiBE,OAC1Bd,KAAAA,CAAY9Q,KAAKoR,KAAK,IAAA,KAGxB5T,KAAKwU,yBAAyB,EAAEjN,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,GAAAA,CAAAA;cAE9C;AAEA,kBAAuB,MAAnBe,IAAsB;AACxB,oBAAIf,KAAW;AAWf,uBATIa,KACFb,KAAW9Q,KAAKoR,KAAK,IACZM,KACTZ,KAAW9Q,KAAKoR,KACPQ,OACTd,KAAAA,CAAY9Q,KAAKoR,KAAK,IAAA,KAGxB5T,KAAKuU,kBAAkB,EAAEhN,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,GAAAA,CAAAA;cAEvC;YA9BA;gBAPEtT,MAAK+T,UAAU,EAAExM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU,EAAA,CAAA;UAsC3C;UAEA,YAAAN,EAAY,GAAEzL,IAAC,GAAEC,IAAC,MAAET,IAAI,aAAE8L,GAAAA,GAAAA;AACxB,kBAAMoB,KAAepB,KAAAA,CAAeA,GAAAA,IAAgB,CAAA,IAAK,GACnDqB,KAAgBrB,KAAAA,CAAeA,GAAY,GAAG,CAAA,IAAK,GACnDsB,KAActB,KAAAA,CAAeA,GAAY,GAAA,EAAI,IAAK,GAClDuB,KAAiBvB,KAAAA,CAAeA,GAAY,GAAG,CAAA,IAAK;AAInC,kBAFAoB,KAAeC,KAAgBC,KAAcC,KAO/DH,MAAiBE,KAKjBD,MAAkBE,KAKvBpU,KAAKgU,aAAa,EAAEzM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU,EAAA,CAAA,IAJxCtT,KAAKsU,oBAAoB,EAAE/M,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU9Q,KAAKoR,KAAK,EAAA,CAAA,IAL3D5T,KAAKsU,oBAAoB,EAAE/M,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,CAAW9Q,KAAKoR,KAAK,EAAA,CAAA,IAL5D5T,KAAKyU,qBAAqB,EAAElN,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU9Q,KAAKoR,KAAK,EAAA,CAAA;UAehE;UAEA,mBAAAX,EAAmB,GAAE1L,IAAC,GAAEC,IAAC,MAAET,IAAI,aAAE8L,GAAAA,GAAAA;AAC/B,kBAAMoB,KAAepB,KAAAA,CAAeA,GAAAA,IAAgB,CAAA,IAAK,GACnDqB,KAAgBrB,KAAAA,CAAeA,GAAY,GAAG,CAAA,IAAK,GACnDsB,KAActB,KAAAA,CAAeA,GAAY,GAAA,EAAI,IAAK,GAClDuB,KAAiBvB,KAAAA,CAAeA,GAAY,GAAG,CAAA,IAAK;AAInC,kBAFAoB,KAAeC,KAAgBC,KAAcC,KAO/DH,MAAiBE,KAKjBD,MAAkBE,KAKvBpU,KAAKgU,aAAa,EAAEzM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU,EAAA,CAAA,IAJxCtT,KAAKwU,yBAAyB,EAAEjN,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU9Q,KAAKoR,KAAK,EAAA,CAAA,IALhE5T,KAAKwU,yBAAyB,EAAEjN,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,CAAW9Q,KAAKoR,KAAK,EAAA,CAAA,IALjE5T,KAAKyU,qBAAqB,EAAElN,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAU9Q,KAAKoR,KAAK,EAAA,CAAA;UAehE;QAAA;AEzTF,cAAA,IAAA,EACEc,KAAK,OACLC,QAAQ,UACRC,cAAc,gBAAA,GCFHC,IAA6BvE,OAAOwE,OAAOC,CAAAA;QAEzC,MAAMC,EAAAA;UAMnB,YAAAzC,EAAY,KAAEC,IAAG,MAAEzH,IAAI,QAAE0H,GAAAA,GAAAA;AACvBzS,iBAAK0S,OAAOF,IACZxS,KAAK2S,QAAQ5H,IACb/K,KAAK4S,UAAUH;UACjB;UAEA,KAAKlL,IAAWC,IAAWT,IAAcuM,IAAAA;AAEvC,gBAAIR;AAEJ,oBAHa9S,KAAK2S,OAAAA;cAIhB,KAAKoC,EAAkBJ;AACrB7B,gBAAAA,KAAe9S,KAAKoT;AACpB;cACF,KAAK2B,EAAkBH;AACrB9B,gBAAAA,KAAe9S,KAAKmT;AACpB;cAEF;AACEL,gBAAAA,KAAe9S,KAAK+S;YAAAA;AAGxBD,YAAAA,GAAahC,KAAK9Q,MAAM,EAAEuH,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,GAAAA,CAAAA;UACxC;UAEA,cAAAD,EAAc,GAAE9L,IAAC,GAAEC,IAAC,MAAET,IAAI,UAAEuM,KAAW,GAAC,MAAEC,GAAAA,GAAAA;AAAAA,gBAAAA;AACxC,kBAAMC,KAAKjM,KAAIR,KAAO,GAChB0M,KAAKjM,KAAIT,KAAO;AAEtBwM,YAAAA,GAAAA,GACa,UAAblD,KAAArQ,KAAK0T,aAAAA,WAAQrD,MAAAA,GAAEsD,aAAa,aAAa,UAAW,MAAML,KAAY9Q,KAAKoR,EAAAA,IAAMJ,EAAAA,IAAMC,EAAAA,GAAAA;UACzF;UAEA,UAAUI,IAAAA;AACR,kBAAA,EAAM,MAAE9M,IAAI,GAAEQ,IAAC,GAAEC,GAAAA,IAAMqM,IACjBoB,KAAUlO,KAAO;AAEvB/G,iBAAKqT,cAAc,OAAD,OAAA,OAAA,OAAA,CAAA,GACbQ,EAAAA,GAAI,EACPN,MAAM,MAAA;AACJvT,mBAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA,GACpF9T,KAAK0T,SAASC,aAAa,aAAa,SAAA,GACxC3T,KAAK0T,SAASC,aACZ,KACA,KAAKpM,KAAIR,KAAO,CAAA,IAAKS,EAAAA,KACdT,KAAO,CAAA,IAAKA,KAAO,CAAA,oBAEjBkO,EAAAA,KACFlO,KAAO,IAAIkO,EAAAA,IAAWlO,KAAO,IAAIkO,EAAAA,gBAAAA;YAEzC,EAAA,CAAA,CAAA;UAGP;UAEA,aAAapB,IAAAA;AACX,kBAAA,EAAM,MAAE9M,IAAI,GAAEQ,IAAC,GAAEC,GAAAA,IAAMqM,IACjBoB,KAAUlO,KAAO;AAEvB/G,iBAAKqT,cAAc,OAAD,OAAA,OAAA,OAAA,CAAA,GACbQ,EAAAA,GAAI,EACPN,MAAM,MAAA;AACJvT,mBAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA,GACpF9T,KAAK0T,SAASC,aAAa,aAAa,SAAA,GACxC3T,KAAK0T,SAASC,aACZ,KACA,KAAKpM,EAAAA,IAAKC,EAAAA,KACHT,EAAAA,KACAA,EAAAA,OAAAA,CACCA,KACN,MACKQ,KAAI0N,EAAAA,IAAWzN,KAAIyN,EAAAA,QACnBlO,KAAO,IAAIkO,MAChB,QAAKlO,KAAO,IAAIkO,MAChB,QAAa,IAAIA,KAAXlO,MACN,GAAA;YACH,EAAA,CAAA,CAAA;UAGP;UAEA,mBAAmB8M,IAAAA;AACjB,kBAAA,EAAM,MAAE9M,IAAI,GAAEQ,IAAC,GAAEC,GAAAA,IAAMqM,IACjBoB,KAAUlO,KAAO;AAEvB/G,iBAAKqT,cAAc,OAAD,OAAA,OAAA,OAAA,CAAA,GACbQ,EAAAA,GAAI,EACPN,MAAM,MAAA;AACJvT,mBAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA,GACpF9T,KAAK0T,SAASC,aAAa,aAAa,SAAA,GACxC3T,KAAK0T,SAASC,aACZ,KACA,KAAKpM,EAAAA,IAAKC,KAAI,MAAMyN,EAAAA,OACb,IAAIA,KACT,KAAK,MAAMA,EAAAA,IAAW,MAAMA,EAAAA,cAA+B,MAAVA,EAAAA,IAA2B,MAAVA,EAAAA,OAC7D,IAAIA,KACT,KAAK,MAAMA,EAAAA,IAAW,MAAMA,EAAAA,cAA+B,MAAVA,EAAAA,IAA4B,MAAA,CAAVA,EAAAA,OAAAA,KACzDA,KACV,KAAK,MAAMA,EAAAA,IAAW,MAAMA,EAAAA,cAAgC,MAAA,CAAVA,EAAAA,IAA4B,MAAA,CAAVA,EAAAA,OAAAA,KAC1DA,KACV,KAAK,MAAMA,EAAAA,IAAW,MAAMA,EAAAA,cAAgC,MAAA,CAAVA,EAAAA,IAA2B,MAAVA,EAAAA,KAC9D1N,KAAI,MAAM0N,EAAAA,IAAWzN,KAAIyN,EAAAA,OACzB,IAAIA,KACT,KAAK,MAAMA,EAAAA,IAAW,MAAMA,EAAAA,cAA+B,MAAVA,EAAAA,IAA2B,MAAVA,EAAAA,OAC7D,IAAIA,KACT,KAAK,MAAMA,EAAAA,IAAW,MAAMA,EAAAA,cAAgC,MAAA,CAAVA,EAAAA,IAA2B,MAAVA,EAAAA,OAAAA,KACzDA,KACV,KAAK,MAAMA,EAAAA,IAAW,MAAMA,EAAAA,cAAgC,MAAA,CAAVA,EAAAA,IAA4B,MAAA,CAAVA,EAAAA,OAAAA,KAC1DA,KACV,KAAK,MAAMA,EAAAA,IAAW,MAAMA,EAAAA,cAA+B,MAAVA,EAAAA,IAA4B,MAAA,CAAVA,EAAAA,EAAAA;YACtE,EAAA,CAAA,CAAA;UAGP;UAEA,SAAAlC,EAAS,GAAExL,IAAC,GAAEC,IAAC,MAAET,IAAI,UAAEuM,GAAAA,GAAAA;AACrBtT,iBAAK+T,UAAU,EAAExM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,GAAAA,CAAAA;UAC/B;UAEA,YAAAF,EAAY,GAAE7L,IAAC,GAAEC,IAAC,MAAET,IAAI,UAAEuM,GAAAA,GAAAA;AACxBtT,iBAAKgU,aAAa,EAAEzM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,GAAAA,CAAAA;UAClC;UAEA,kBAAAH,EAAkB,GAAE5L,IAAC,GAAEC,IAAC,MAAET,IAAI,UAAEuM,GAAAA,GAAAA;AAC9BtT,iBAAKkV,mBAAmB,EAAE3N,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,GAAAA,CAAAA;UACxC;QAAA;ACtIF,cAAA,IAAA,EACEoB,KAAK,OACLC,QAAQ,SAAA,GCDGQ,IAA0B7E,OAAOwE,OAAOM,CAAAA;QAEtC,MAAMC,EAAAA;UAMnB,YAAA9C,EAAY,KAAEC,IAAG,MAAEzH,IAAI,QAAE0H,GAAAA,GAAAA;AACvBzS,iBAAK0S,OAAOF,IACZxS,KAAK2S,QAAQ5H,IACb/K,KAAK4S,UAAUH;UACjB;UAEA,KAAKlL,IAAWC,IAAWT,IAAcuM,IAAAA;AAEvC,gBAAIR;AAIAA,YAAAA,KALS9S,KAAK2S,UAIXyC,EAAeT,SACH3U,KAAKoT,cAILpT,KAAK+S,UAGxBD,GAAahC,KAAK9Q,MAAM,EAAEuH,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,GAAAA,CAAAA;UACxC;UAEA,cAAAD,EAAc,GAAE9L,IAAC,GAAEC,IAAC,MAAET,IAAI,UAAEuM,KAAW,GAAC,MAAEC,GAAAA,GAAAA;AAAAA,gBAAAA;AACxC,kBAAMC,KAAKjM,KAAIR,KAAO,GAChB0M,KAAKjM,KAAIT,KAAO;AAEtBwM,YAAAA,GAAAA,GACa,UAAblD,KAAArQ,KAAK0T,aAAAA,WAAQrD,MAAAA,GAAEsD,aAAa,aAAa,UAAW,MAAML,KAAY9Q,KAAKoR,EAAAA,IAAMJ,EAAAA,IAAMC,EAAAA,GAAAA;UACzF;UAEA,UAAUI,IAAAA;AACR,kBAAA,EAAM,MAAE9M,IAAI,GAAEQ,IAAC,GAAEC,GAAAA,IAAMqM;AAEvB7T,iBAAKqT,cAAc,OAAD,OAAA,OAAA,OAAA,CAAA,GACbQ,EAAAA,GAAI,EACPN,MAAM,MAAA;AACJvT,mBAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,QAAA,GACpF9T,KAAK0T,SAASC,aAAa,MAAM9J,OAAOtC,KAAIR,KAAO,CAAA,CAAA,GACnD/G,KAAK0T,SAASC,aAAa,MAAM9J,OAAOrC,KAAIT,KAAO,CAAA,CAAA,GACnD/G,KAAK0T,SAASC,aAAa,KAAK9J,OAAO9C,KAAO,CAAA,CAAA;YAAG,EAAA,CAAA,CAAA;UAGvD;UAEA,aAAa8M,IAAAA;AACX,kBAAA,EAAM,MAAE9M,IAAI,GAAEQ,IAAC,GAAEC,GAAAA,IAAMqM;AAEvB7T,iBAAKqT,cAAc,OAAD,OAAA,OAAA,OAAA,CAAA,GACbQ,EAAAA,GAAI,EACPN,MAAM,MAAA;AACJvT,mBAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA,GACpF9T,KAAK0T,SAASC,aAAa,KAAK9J,OAAOtC,EAAAA,CAAAA,GACvCvH,KAAK0T,SAASC,aAAa,KAAK9J,OAAOrC,EAAAA,CAAAA,GACvCxH,KAAK0T,SAASC,aAAa,SAAS9J,OAAO9C,EAAAA,CAAAA,GAC3C/G,KAAK0T,SAASC,aAAa,UAAU9J,OAAO9C,EAAAA,CAAAA;YAAM,EAAA,CAAA,CAAA;UAGxD;UAEA,SAAAgM,EAAS,GAAExL,IAAC,GAAEC,IAAC,MAAET,IAAI,UAAEuM,GAAAA,GAAAA;AACrBtT,iBAAK+T,UAAU,EAAExM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,GAAAA,CAAAA;UAC/B;UAEA,YAAAF,EAAY,GAAE7L,IAAC,GAAEC,IAAC,MAAET,IAAI,UAAEuM,GAAAA,GAAAA;AACxBtT,iBAAKgU,aAAa,EAAEzM,GAAAA,IAAGC,GAAAA,IAAGT,MAAAA,IAAMuM,UAAAA,GAAAA,CAAAA;UAClC;QAAA;AC1EF,cCAA,IAEU,UCQJgC,IAAa,CACjB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,CAAA,GAGfC,IAAU,CACd,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,GACnB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA,CAAA;QAGrB,MAAqBC,EAAAA;UAiBnB,YAAYC,IAA0BhD,IAAAA;AAqjBtC,iBAAAiD,aAAcC,CAAAA,OACR3V,KAAK4V,SAASC,YAAYC,YACrBtT,KAAKC,MAAMkT,EAAAA,IAEbA,IAxjBP3V,KAAK4S,UAAUH,IACfzS,KAAK0T,WAAW1T,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,KAAA,GACpF9T,KAAK0T,SAASC,aAAa,SAAS9J,OAAO4L,GAAQvI,KAAAA,CAAAA,GACnDlN,KAAK0T,SAASC,aAAa,UAAU9J,OAAO4L,GAAQtI,MAAAA,CAAAA,GACpDnN,KAAK0T,SAASC,aAAa,eAAe,8BAAA,GACrC8B,GAAQI,YAAYC,aACvB9V,KAAK0T,SAASC,aAAa,mBAAmB,YAAA,GAEhD3T,KAAK0T,SAASC,aAAa,WAAW,OAAO8B,GAAQvI,KAAAA,IAASuI,GAAQtI,MAAAA,EAAAA,GACtEnN,KAAK+V,QAAQ/V,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA,GACjF9T,KAAK0T,SAASvB,YAAYnS,KAAK+V,KAAAA,GAC/B/V,KAAKgW,YAAYP,GAAQQ,OACzBjW,KAAKkW,cAAcV,EAAMW,iBACzBnW,KAAK4V,WAAWH;UAClB;UAEA,IAAA,QAAIvI;AACF,mBAAOlN,KAAK4V,SAAS1I;UACvB;UAEA,IAAA,SAAIC;AACF,mBAAOnN,KAAK4V,SAASzI;UACvB;UAEA,aAAAiJ;AACE,mBAAOpW,KAAK0T;UACd;UAEA,MAAA,OAAa2C,IAAAA;AACX,kBAAM5M,KAAQ4M,GAAG1Q,eAAAA,GACX2Q,KAAU9T,KAAK8E,IAAItH,KAAK4V,SAAS1I,OAAOlN,KAAK4V,SAASzI,MAAAA,IAAiC,IAAvBnN,KAAK4V,SAASxP,QAC9EmQ,KAAavW,KAAK4V,SAASY,UAAUC,IAAoBH,KAAU9T,KAAKkU,KAAK,CAAA,IAAKJ,IAClFrB,KAAUjV,KAAK0V,WAAWa,KAAa9M,EAAAA;AAC7C,gBAAIkN,KAAgB,EAClBC,WAAW,GACXC,WAAW,GACX3J,OAAO,GACPC,QAAQ,EAAA;AAKV,gBAFAnN,KAAK8W,MAAMT,IAEPrW,KAAK4V,SAASK,OAAO;AAGvB,kBAAA,MADMjW,KAAK+W,UAAAA,GAAAA,CACN/W,KAAKgX,OAAQ;AAClB,oBAAA,EAAM,cAAEC,IAAY,WAAEC,GAAAA,IAAclX,KAAK4V,UACnCuB,KAAaF,GAAaG,YAAYC,EAAwBH,GAAU3W,oBAAAA,GACxE+W,KAAgB9U,KAAKC,MAAM0U,KAAa1N,KAAQA,EAAAA;AAEtDkN,cAAAA,KCrFS,SAAA,EAA4B,gBACzCY,IAAc,eACdC,IAAa,eACbF,IAAa,mBACbG,IAAiB,SACjBxC,GAAAA,GAAAA;AAEA,sBAAMyC,KAAW,EAAEnQ,GAAG,GAAGC,GAAG,EAAA,GACtB4P,KAAY,EAAE7P,GAAG,GAAGC,GAAG,EAAA;AAE7B,oBAAI+P,MAAkB,KAAKC,MAAiB,KAAKF,MAAiB,KAAKrC,MAAW,EAChF,QAAO,EACL9H,QAAQ,GACRD,OAAO,GACP2J,WAAW,GACXD,WAAW,EAAA;AAIf,sBAAMe,KAAIJ,KAAiBC;AA6B3B,uBA1BAE,GAASnQ,IAAI/E,KAAKC,MAAMD,KAAKkU,KAAKY,KAAgBK,EAAAA,CAAAA,GAE9CD,GAASnQ,KAAK,MAAGmQ,GAASnQ,IAAI,IAE9BkQ,MAAqBA,KAAoBC,GAASnQ,MAAGmQ,GAASnQ,IAAIkQ,KAElEC,GAASnQ,IAAI,KAAM,KAAGmQ,GAASnQ,KACnC6P,GAAU7P,IAAImQ,GAASnQ,IAAI0N,IAI3ByC,GAASlQ,IAAI,IAAI,IAAIhF,KAAKoV,MAAMF,GAASnQ,IAAIoQ,KAAI,KAAK,CAAA,GACtDP,GAAU5P,IAAIhF,KAAKqV,MAAMT,GAAU7P,IAAIoQ,EAAAA,IAEnCD,GAASlQ,IAAIkQ,GAASnQ,IAAI+P,MAAkBG,MAAqBA,KAAoBC,GAASlQ,OAC5FiQ,MAAqBA,KAAoBC,GAASlQ,KACpDkQ,GAASlQ,IAAIiQ,IACTC,GAASlQ,IAAI,KAAM,KAAGkQ,GAASnQ,OAEnCmQ,GAASlQ,KAAK,GAEhB4P,GAAU5P,IAAIkQ,GAASlQ,IAAIyN,IAC3ByC,GAASnQ,IAAI,IAAI,IAAI/E,KAAKoV,MAAMF,GAASlQ,IAAImQ,KAAI,KAAK,CAAA,GACtDP,GAAU7P,IAAI/E,KAAKqV,MAAMT,GAAU5P,IAAImQ,EAAAA,IAGlC,EACLxK,QAAQiK,GAAU5P,GAClB0F,OAAOkK,GAAU7P,GACjBsP,WAAWa,GAASlQ,GACpBoP,WAAWc,GAASnQ,EAAAA;cAExB,ED+ByC,EACjCiQ,eAAexX,KAAKgX,OAAO9J,OAC3BqK,gBAAgBvX,KAAKgX,OAAO7J,QAC5BmK,eAAAA,IACAG,mBAAmBhO,KAAQ,IAC3BwL,SAAAA,GAAAA,CAAAA;YAEJ;AAEAjV,iBAAK8X,eAAAA,GACL9X,KAAK+X,SAAS,CAACzW,IAAaC,OAAAA;AAAAA,kBAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA;AAC1B,qBAAA,EAAIvB,KAAK4V,SAASqB,aAAae,sBAE3B1W,OAAQmI,KAAQkN,GAAcE,aAAa,KAC3CvV,MAAOmI,KAAQkN,GAAcE,aAAa,KAC1CtV,OAAQkI,KAAQkN,GAAcC,aAAa,KAC3CrV,MAAOkI,KAAQkN,GAAcC,aAAa,MAM3B,UAAfxW,KAAAkV,EAAWhU,EAAAA,MAAAA,WAAIlB,KAAA,SAAAA,GAAGmB,EAAAA,OAAmC,UAA3B8J,KAAAiK,EAAWhU,KAAMmI,KAAQ,CAAA,MAAA,WAAE4B,KAAA,SAAAA,GAAG9J,EAAAA,OAAuB,UAAf8O,KAAAiF,EAAWhU,EAAAA,MAAAA,WAAI+O,KAAA,SAAAA,GAAG9O,KAAMkI,KAAQ,CAAA,OAIpF,UAAZkB,KAAA4K,EAAQjU,EAAAA,MAAAA,WAAIqJ,KAAA,SAAAA,GAAGpJ,EAAAA,OAAgC,UAAxB0W,KAAA1C,EAAQjU,KAAMmI,KAAQ,CAAA,MAAA,WAAEwO,KAAA,SAAAA,GAAG1W,EAAAA,OAAoB,UAAZkJ,KAAA8K,EAAQjU,EAAAA,MAAAA,WAAImJ,KAAA,SAAAA,GAAGlJ,KAAMkI,KAAQ,CAAA;YAIhF,CAAA,GAEbzJ,KAAKkY,YAAAA,GAEDlY,KAAK4V,SAASK,SAAAA,MACVjW,KAAKmY,UAAU,EAAEjL,OAAOyJ,GAAczJ,OAAOC,QAAQwJ,GAAcxJ,QAAQ1D,OAAAA,IAAOwL,SAAAA,GAAAA,CAAAA;UAE5F;UAEA,iBAAA6C;AAAAA,gBAAAA,IAAAA,IAAAA;AACE,kBAAMM,KAAUpY,KAAK0T,UACf+B,KAAUzV,KAAK4V;AAErB,gBAAIwC,IAAS;AACX,oBAAMC,KAA2C,UAAzBpY,KAAAwV,GAAQ6C,sBAAAA,WAAiBrY,KAAA,SAAAA,GAAEsY,UAC7CC,KAAiC,UAAzBtY,KAAAuV,GAAQ6C,sBAAAA,WAAiBpY,KAAA,SAAAA,GAAEsY;AACzC,kBAAIrL,KAASsI,GAAQtI,QACjBD,KAAQuI,GAAQvI;AAEpB,kBAAImL,MAAmBG,IAAO;AAC5B,sBAAMJ,KAAUpY,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA;AACpF9T,qBAAKyY,sBAAsBzY,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,UAAA,GAC/F9T,KAAKyY,oBAAoB9E,aAAa,MAAM,8BAA8B3T,KAAKkW,WAAAA,EAAAA,GAC/ElW,KAAK+V,MAAM5D,YAAYnS,KAAKyY,mBAAAA,IAEC,UAAzBtY,KAAAsV,GAAQ6C,sBAAAA,WAAiBnY,KAAA,SAAAA,GAAE0X,WAC7B1K,KAASD,KAAQ1K,KAAK8E,IAAImO,GAAQvI,OAAOuI,GAAQtI,MAAAA,GACjDiL,GAAQzE,aAAa,MAAM9J,OAAQsD,KAAS,IAAKsI,GAAQ6C,kBAAkBT,KAAAA,CAAAA,IAG7EO,GAAQzE,aAAa,KAAK9J,OAAO7J,KAAK0V,YAAYD,GAAQvI,QAAQA,MAAS,CAAA,CAAA,CAAA,GAC3EkL,GAAQzE,aAAa,KAAK9J,OAAO7J,KAAK0V,YAAYD,GAAQtI,SAASA,MAAU,CAAA,CAAA,CAAA,GAC7EiL,GAAQzE,aAAa,SAAS9J,OAAOqD,EAAAA,CAAAA,GACrCkL,GAAQzE,aAAa,UAAU9J,OAAOsD,EAAAA,CAAAA,GAEtCnN,KAAKyY,oBAAoBtG,YAAYiG,EAAAA,GAErCpY,KAAK0Y,aAAa,EAChBjD,SAAS4C,IACTG,OAAOA,IACPG,oBAAoB,GACpBpR,GAAG,GACHC,GAAG,GACH2F,QAAQsI,GAAQtI,QAChBD,OAAOuI,GAAQvI,OACf0E,MAAM,oBAAoB5R,KAAKkW,WAAAA,GAAAA,CAAAA;cAEnC;YACF;UACF;UAEA,SAAS0C,IAAAA;AAAAA,gBAAAA,IAAAA;AACP,gBAAA,CAAK5Y,KAAK8W,IACR,OAAM;AAGR,kBAAMrB,KAAUzV,KAAK4V,UACfnM,KAAQzJ,KAAK8W,IAAInR,eAAAA;AAEvB,gBAAI8D,KAAQgM,GAAQvI,SAASzD,KAAQgM,GAAQtI,OAC3C,OAAM;AAGR,kBAAMmJ,KAAU9T,KAAK8E,IAAImO,GAAQvI,OAAOuI,GAAQtI,MAAAA,IAA2B,IAAjBsI,GAAQrP,QAC5DmQ,KAAad,GAAQe,UAAUC,IAAoBH,KAAU9T,KAAKkU,KAAK,CAAA,IAAKJ,IAC5ErB,KAAUjV,KAAK0V,WAAWa,KAAa9M,EAAAA,GACvCoP,KAAa7Y,KAAK0V,YAAYD,GAAQvI,QAAQzD,KAAQwL,MAAW,CAAA,GACjE6D,KAAa9Y,KAAK0V,YAAYD,GAAQtI,SAAS1D,KAAQwL,MAAW,CAAA,GAClEP,KAAM,IAAIpC,EAAM,EACpBE,KAAKxS,KAAK0T,UACV3I,MAAM0K,GAAQI,YAAY9K,MAC1B0H,QAAQzS,KAAK4S,QAAAA,CAAAA;AAGf5S,iBAAK+Y,gBAAgB/Y,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,UAAA,GACzF9T,KAAK+Y,cAAcpF,aAAa,MAAM,uBAAuB3T,KAAKkW,WAAAA,EAAAA,GAClElW,KAAK+V,MAAM5D,YAAYnS,KAAK+Y,aAAAA,GAE5B/Y,KAAK0Y,aAAa,EAChBjD,SAA4B,UAAnBvV,KAAAuV,GAAQI,gBAAAA,WAAW3V,KAAA,SAAAA,GAAEqY,UAC9BC,OAAO/C,GAAQI,YAAY2C,OAC3BG,oBAAoB,GACpBpR,GAAG,GACHC,GAAG,GACH2F,QAAQsI,GAAQtI,QAChBD,OAAOuI,GAAQvI,OACf0E,MAAM,aAAa5R,KAAKkW,WAAAA,GAAAA,CAAAA;AAG1B,qBAAS5U,KAAM,GAAGA,KAAMmI,IAAOnI,KAC7B,UAASC,KAAM,GAAGA,KAAMkI,IAAOlI,KACzBqX,CAAAA,MAAAA,CAAWA,GAAOtX,IAAKC,EAAAA,MAGd,UAARpB,KAAAH,KAAK8W,QAAAA,WAAG3W,KAAA,SAAAA,GAAEuF,OAAOpE,IAAKC,EAAAA,OAI3BmT,GAAInB,KACFsF,KAAatX,KAAM0T,IACnB6D,KAAaxX,KAAM2T,IACnBA,IACA,CAAC+D,IAAiBC,OAAAA,EACZ1X,KAAMyX,KAAU,KAAK1X,KAAM2X,KAAU,KAAK1X,KAAMyX,MAAWvP,MAASnI,KAAM2X,MAAWxP,OAAAA,EACrFmP,MAAAA,CAAWA,GAAOtX,KAAM2X,IAAS1X,KAAMyX,EAAAA,MAAAA,CAAAA,CAClChZ,KAAK8W,OAAO9W,KAAK8W,IAAIpR,OAAOpE,KAAM2X,IAAS1X,KAAMyX,EAAAA,CAAAA,GAI1DtE,GAAIhB,YAAY1T,KAAK+Y,iBACvB/Y,KAAK+Y,cAAc5G,YAAYuC,GAAIhB,QAAAA;AAKzC,gBAAI+B,GAAQe,UAAUC,GAAmB;AACvC,oBAAMyC,KAAiBlZ,KAAK0V,YAAYY,KAAUrB,KAAUxL,MAAS,CAAA,GAC/D0P,KAAY1P,KAAyB,IAAjByP,IACpBE,KAAiBP,KAAaK,KAAiBjE,IAC/CoE,KAAiBP,KAAaI,KAAiBjE,IAC/CqE,KAAyB,CAAA,GACzBC,KAASvZ,KAAK0V,WAAWyD,KAAY,CAAA;AAE3C,uBAAS7X,KAAM,GAAGA,KAAM6X,IAAW7X,MAAO;AACxCgY,gBAAAA,GAAWhY,EAAAA,IAAO,CAAA;AAClB,yBAASC,KAAM,GAAGA,KAAM4X,IAAW5X,KAE/BD,CAAAA,MAAO4X,KAAiB,KACxB5X,MAAO6X,KAAYD,MACnB3X,MAAO2X,KAAiB,KACxB3X,MAAO4X,KAAYD,MAMjB1W,KAAKkU,MAAMpV,KAAMiY,OAAWjY,KAAMiY,OAAWhY,KAAMgY,OAAWhY,KAAMgY,GAAAA,IAAWA,KAJjFD,GAAWhY,EAAAA,EAAKC,EAAAA,IAAO,IAUzB+X,GAAWhY,EAAAA,EAAKC,EAAAA,IAAOvB,KAAK8W,IAAIpR,OAC9BnE,KAAM,IAAI2X,KAAiB,IAAI3X,KAAMA,MAAOkI,KAAQlI,KAAM,IAAI2X,KAAiB3X,KAAM2X,IACrF5X,KAAM,IAAI4X,KAAiB,IAAI5X,KAAMA,MAAOmI,KAAQnI,KAAM,IAAI4X,KAAiB5X,KAAM4X,EAAAA,IAEnF,IACA;cAER;AAEA,uBAAS5X,KAAM,GAAGA,KAAM6X,IAAW7X,KACjC,UAASC,KAAM,GAAGA,KAAM4X,IAAW5X,KAC5B+X,CAAAA,GAAWhY,EAAAA,EAAKC,EAAAA,MAErBmT,GAAInB,KACF6F,KAAiB7X,KAAM0T,IACvBoE,KAAiB/X,KAAM2T,IACvBA,IACA,CAAC+D,IAAiBC,OAAAA;AAAAA,oBAAAA;AAChB,uBAAA,CAAA,EAAkC,UAAzB7Y,KAAAkZ,GAAWhY,KAAM2X,EAAAA,MAAAA,WAAQ7Y,KAAA,SAAAA,GAAGmB,KAAMyX,EAAAA;cAAQ,CAAA,GAGnDtE,GAAIhB,YAAY1T,KAAK+Y,iBACvB/Y,KAAK+Y,cAAc5G,YAAYuC,GAAIhB,QAAAA;YAI3C;UACF;UAEA,cAAAwE;AACE,gBAAA,CAAKlY,KAAK8W,IACR,OAAM;AAGR,kBAAMsB,KAAUpY,KAAK0T,UACf+B,KAAUzV,KAAK4V;AAErB,gBAAA,CAAKwC,GACH,OAAM;AAGR,kBAAM3O,KAAQzJ,KAAK8W,IAAInR,eAAAA,GACjB2Q,KAAU9T,KAAK8E,IAAImO,GAAQvI,OAAOuI,GAAQtI,MAAAA,IAA2B,IAAjBsI,GAAQrP,QAC5DmQ,KAAad,GAAQe,UAAUC,IAAoBH,KAAU9T,KAAKkU,KAAK,CAAA,IAAKJ,IAC5ErB,KAAUjV,KAAK0V,WAAWa,KAAa9M,EAAAA,GACvC+P,KAA8B,IAAVvE,IACpBwE,KAA2B,IAAVxE,IACjB4D,KAAa7Y,KAAK0V,YAAYD,GAAQvI,QAAQzD,KAAQwL,MAAW,CAAA,GACjE6D,KAAa9Y,KAAK0V,YAAYD,GAAQtI,SAAS1D,KAAQwL,MAAW,CAAA;AAExE,aACE,CAAC,GAAG,GAAG,CAAA,GACP,CAAC,GAAG,GAAGzS,KAAKoR,KAAK,CAAA,GACjB,CAAC,GAAG,GAAA,CAAIpR,KAAKoR,KAAK,CAAA,CAAA,EAClBtC,QAAQ,CAAA,CAAEoI,IAAQpY,IAAKgS,EAAAA,MAAAA;AAAAA,kBAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,IAAAA,GAAAA,GAAAA;AACvB,oBAAM/L,IAAIsR,KAAaa,KAASzE,MAAWxL,KAAQ,IAC7CjC,IAAIsR,KAAaxX,KAAM2T,MAAWxL,KAAQ;AAChD,kBAAIkQ,IAAwB3Z,KAAK+Y,eAC7Ba,IAAqB5Z,KAAK+Y;AAoB9B,oBAlBgC,UAA5Bc,KAAApE,GAAQqE,yBAAAA,WAAoBD,KAAA,SAAAA,GAAEtB,cAAwC,UAA5BwB,KAAAtE,GAAQqE,yBAAAA,WAAoBC,KAAA,SAAAA,GAAEvB,YAC1EmB,IAAwB3Z,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,UAAA,GAC5F6F,EAAsBhG,aAAa,MAAM,kCAAkC+F,EAAAA,IAAUpY,EAAAA,IAAOtB,KAAKkW,WAAAA,EAAAA,GACjGlW,KAAK+V,MAAM5D,YAAYwH,CAAAA,GACvB3Z,KAAKga,yBAAyBha,KAAKia,sBAAsBL,IAAqBD,GAE9E3Z,KAAK0Y,aAAa,EAChBjD,SAAqC,UAA5ByE,KAAAzE,GAAQqE,yBAAAA,WAAoBI,KAAA,SAAAA,GAAE3B,UACvCC,OAAmC,UAA5BhP,KAAAiM,GAAQqE,yBAAAA,WAAoBtQ,KAAA,SAAAA,GAAEgP,OACrCG,oBAAoBrF,IACpB/L,GAAAA,GACAC,GAAAA,GACA2F,QAAQqM,IACRtM,OAAOsM,IACP5H,MAAM,wBAAwB8H,EAAAA,IAAUpY,EAAAA,IAAOtB,KAAKkW,WAAAA,GAAAA,CAAAA,KAIxB,UAA5B1O,KAAAiO,GAAQqE,yBAAAA,WAAoBtS,KAAA,SAAAA,GAAEuD,SAAQ8J,EAA2BsF,SAAS1E,GAAQqE,qBAAqB/O,IAAAA,GAAO;AAChH,sBAAMqP,KAAgB,IAAIpF,EAAe,EACvCxC,KAAKxS,KAAK0T,UACV3I,MAAM0K,GAAQqE,qBAAqB/O,MACnC0H,QAAQzS,KAAK4S,QAAAA,CAAAA;AAGfwH,gBAAAA,GAAc7G,KAAKhM,GAAGC,GAAGgS,IAAmBlG,EAAAA,GAExC8G,GAAc1G,YAAYiG,KAC5BA,EAAsBxH,YAAYiI,GAAc1G,QAAAA;cAEpD,OAAO;AACL,sBAAMgB,KAAM,IAAIpC,EAAM,EACpBE,KAAKxS,KAAK0T,UACV3I,OAAmC,UAA5BxD,KAAAkO,GAAQqE,yBAAAA,WAAoBvS,KAAA,SAAAA,GAAEwD,SAAoB0K,GAAQI,YAAY9K,MAC7E0H,QAAQzS,KAAK4S,QAAAA,CAAAA;AAGf,yBAAStR,KAAM,GAAGA,KAAMgU,EAAWnT,QAAQb,KACzC,UAASC,KAAM,GAAGA,KAAM+T,EAAWhU,EAAAA,EAAKa,QAAQZ,KAAAA,EAC1B,UAAf8Y,KAAA/E,EAAWhU,EAAAA,MAAAA,WAAI+Y,KAAA,SAAAA,GAAG9Y,EAAAA,OAIvBmT,GAAInB,KACFhM,IAAIhG,KAAM0T,IACVzN,IAAIlG,KAAM2T,IACVA,IACA,CAAC+D,IAAiBC,OAAAA;AAA4B,sBAAA5N;AAAC,yBAAA,CAAA,EAA2B,UAAzBA,KAAAiK,EAAWhU,KAAM2X,EAAAA,MAAAA,WAAQ5N,KAAA,SAAAA,GAAG9J,KAAMyX,EAAAA;gBAAQ,CAAA,GAGzFtE,GAAIhB,YAAYiG,KAClBA,EAAsBxH,YAAYuC,GAAIhB,QAAAA;cAI9C;AAoBA,oBAlB6B,UAAzB4G,KAAA7E,GAAQ8E,sBAAAA,WAAiBD,KAAA,SAAAA,GAAE/B,cAAqC,UAAzBiC,KAAA/E,GAAQ8E,sBAAAA,WAAiBC,KAAA,SAAAA,GAAEhC,YACpEoB,IAAqB5Z,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,UAAA,GACzF8F,EAAmBjG,aAAa,MAAM,+BAA+B+F,EAAAA,IAAUpY,EAAAA,IAAOtB,KAAKkW,WAAAA,EAAAA,GAC3FlW,KAAK+V,MAAM5D,YAAYyH,CAAAA,GACvB5Z,KAAKia,sBAAsBL,GAE3B5Z,KAAK0Y,aAAa,EAChBjD,SAAkC,UAAzBpL,KAAAoL,GAAQ8E,sBAAAA,WAAiBlQ,KAAA,SAAAA,GAAEkO,UACpCC,OAAgC,UAAzBiC,KAAAhF,GAAQ8E,sBAAAA,WAAiBE,KAAA,SAAAA,GAAEjC,OAClCG,oBAAoBrF,IACpB/L,GAAGA,IAAc,IAAV0N,IACPzN,GAAGA,IAAc,IAAVyN,IACP9H,QAAQsM,IACRvM,OAAOuM,IACP7H,MAAM,qBAAqB8H,EAAAA,IAAUpY,EAAAA,IAAOtB,KAAKkW,WAAAA,GAAAA,CAAAA,KAIxB,UAAzB,IAAAT,GAAQ8E,sBAAAA,WAAiB,IAAA,SAAA,EAAExP,SAAQoK,EAAwBgF,SAAS1E,GAAQ8E,kBAAkBxP,IAAAA,GAAO;AACvG,sBAAM2P,KAAa,IAAIrF,EAAY,EACjC7C,KAAKxS,KAAK0T,UACV3I,MAAM0K,GAAQ8E,kBAAkBxP,MAChC0H,QAAQzS,KAAK4S,QAAAA,CAAAA;AAGf8H,gBAAAA,GAAWnH,KAAKhM,IAAc,IAAV0N,IAAazN,IAAc,IAAVyN,IAAawE,IAAgBnG,EAAAA,GAE9DoH,GAAWhH,YAAYkG,KACzBA,EAAmBzH,YAAYuI,GAAWhH,QAAAA;cAE9C,OAAO;AACL,sBAAMgB,KAAM,IAAIpC,EAAM,EACpBE,KAAKxS,KAAK0T,UACV3I,OAAgC,UAAzB,IAAA0K,GAAQ8E,sBAAAA,WAAiB,IAAA,SAAA,EAAExP,SAAoB0K,GAAQI,YAAY9K,MAC1E0H,QAAQzS,KAAK4S,QAAAA,CAAAA;AAGf,yBAAStR,KAAM,GAAGA,KAAMiU,EAAQpT,QAAQb,KACtC,UAASC,KAAM,GAAGA,KAAMgU,EAAQjU,EAAAA,EAAKa,QAAQZ,KAAAA,EAC1B,UAAZ,IAAAgU,EAAQjU,EAAAA,MAAAA,WAAI,IAAA,SAAA,EAAGC,EAAAA,OAIpBmT,GAAInB,KACFhM,IAAIhG,KAAM0T,IACVzN,IAAIlG,KAAM2T,IACVA,IACA,CAAC+D,IAAiBC,OAAAA;AAA4B,sBAAA5N;AAAC,yBAAA,CAAA,EAAwB,UAAtBA,KAAAkK,EAAQjU,KAAM2X,EAAAA,MAAAA,WAAQ5N,KAAA,SAAAA,GAAG9J,KAAMyX,EAAAA;gBAAQ,CAAA,GAGtFtE,GAAIhB,YAAYkG,KAClBA,EAAmBzH,YAAYuC,GAAIhB,QAAAA;cAI3C;YAAA,CAAA;UAEJ;UAEA,YAAAqD;AACE,mBAAO,IAAI4D,QAAQ,CAACC,IAASC,OAAAA;AAAAA,kBAAAA;AAC3B,oBAAMpF,KAAUzV,KAAK4V;AAErB,kBAAA,CAAKH,GAAQQ,MACX,QAAO4E,GAAO,sBAAA;AAGhB,kBAAsB,UAAlB1a,KAAAsV,GAAQqF,eAAAA,WAAU3a,KAAA,SAAAA,GAAE4W,UACtBtB,CAAAA,GAAQqF,WACL/D,UAAUtB,GAAQQ,KAAAA,EAClB8E,KAAM9E,CAAAA,OAAAA;AAAAA,oBAAAA,IAAAA;AAEL,oBADAjW,KAAKgX,SAASf,IACVjW,KAAK4V,SAASqB,aAAa+D,YAAY;AACzC,wBAAMC,KAA2B,UAAlB9a,KAAAsV,GAAQqF,eAAAA,WAAU3a,KAAA,SAAAA,GAAE+a,aAAclb,KAAKgX,OAAO9J,OAAQlN,KAAKgX,OAAO7J,MAAAA;AACzD,4BAAxB9B,KAAA4P,QAAAA,KAAAA,SAAAA,GAAQE,WAAW,IAAA,MAAA,WAAK9P,MAAAA,GAAE8M,UAAUlC,IAAO,GAAG,CAAA,GAC9CjW,KAAKgW,YAAYiF,QAAAA,KAAAA,SAAAA,GAAQG,UAAAA;gBAC3B;AACAR,gBAAAA,GAAAA;cAAS,CAAA,EAEVS,MAAMR,EAAAA;mBACJ;AACL,sBAAM5E,KAAQ,IAAIjW,KAAK4S,QAAQ0I;AAEiB,4BAAA,OAArC7F,GAAQwB,aAAasE,gBAC9BtF,GAAMsF,cAAc9F,GAAQwB,aAAasE,cAG3Cvb,KAAKgX,SAASf,IACdA,GAAMuF,SAASC,YAAAA;AACTzb,uBAAK4V,SAASqB,aAAa+D,eAC7Bhb,KAAKgW,YAAAA,ME7dFyF,eAAyBC,IAAajJ,IAAAA;AACnD,2BAAO,IAAIkI,QAASC,CAAAA,OAAAA;AAClB,4BAAMe,KAAM,IAAIlJ,GAAOmJ;AACvBD,sBAAAA,GAAIH,SAAS,WAAA;AACX,8BAAMK,KAAS,IAAIpJ,GAAOqJ;AAC1BD,wBAAAA,GAAOE,YAAY,WAAA;AACjBnB,0BAAAA,GAAQiB,GAAOG,MAAAA;wBACjB,GACAH,GAAOI,cAAcN,GAAIO,QAAAA;sBAC3B,GACAP,GAAIQ,KAAK,OAAOT,EAAAA,GAChBC,GAAIS,eAAe,QACnBT,GAAIU,KAAAA;oBAAM,CAAA;kBAEd,EF+c6C5G,GAAQQ,SAAS,IAAIjW,KAAK4S,OAAAA,IAE7DgI,GAAAA;gBAAS,GAEX3E,GAAMqG,MAAM7G,GAAQQ;cACtB;YAAA,CAAA;UAEJ;UAEA,MAAA,UAAMkC,EAAU,OACdjL,IAAK,QACLC,IAAM,OACN1D,IAAK,SACLwL,GAAAA,GAAAA;AAOA,kBAAMQ,KAAUzV,KAAK4V,UACfiD,KAAa7Y,KAAK0V,YAAYD,GAAQvI,QAAQzD,KAAQwL,MAAW,CAAA,GACjE6D,KAAa9Y,KAAK0V,YAAYD,GAAQtI,SAAS1D,KAAQwL,MAAW,CAAA,GAClEsH,KAAK1D,KAAa7Y,KAAK0V,WAAWD,GAAQwB,aAAa7Q,UAAUqD,KAAQwL,KAAU/H,MAAS,CAAA,GAC5FsP,KAAK1D,KAAa9Y,KAAK0V,WAAWD,GAAQwB,aAAa7Q,UAAUqD,KAAQwL,KAAU9H,MAAU,CAAA,GAC7FsP,KAAKvP,KAAsC,IAA9BuI,GAAQwB,aAAa7Q,QAClCsW,KAAKvP,KAAuC,IAA9BsI,GAAQwB,aAAa7Q,QAEnC6P,KAAQjW,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,OAAA;AAClFmC,YAAAA,GAAMtC,aAAa,QAAQ3T,KAAKgW,aAAa,EAAA,GAC7CC,GAAMtC,aAAa,cAAc3T,KAAKgW,aAAa,EAAA,GACnDC,GAAMtC,aAAa,KAAK9J,OAAO0S,EAAAA,CAAAA,GAC/BtG,GAAMtC,aAAa,KAAK9J,OAAO2S,EAAAA,CAAAA,GAC/BvG,GAAMtC,aAAa,SAAS,GAAG8I,EAAAA,IAAAA,GAC/BxG,GAAMtC,aAAa,UAAU,GAAG+I,EAAAA,IAAAA,GAEhC1c,KAAK0T,SAASvB,YAAY8D,EAAAA;UAC5B;UAEA,aAAAyC,EAAa,SACXjD,IAAO,OACP+C,IAAK,oBACLG,IAAkB,GAClBpR,IAAC,GACDC,IAAC,QACD2F,IAAM,OACND,IAAK,MACL0E,GAAAA,GAAAA;AAWA,kBAAM7K,KAAOmG,KAAQC,KAASD,KAAQC,IAChCrG,KAAO9G,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA;AAOjF,gBANAhN,GAAK6M,aAAa,KAAK9J,OAAOtC,EAAAA,CAAAA,GAC9BT,GAAK6M,aAAa,KAAK9J,OAAOrC,EAAAA,CAAAA,GAC9BV,GAAK6M,aAAa,UAAU9J,OAAOsD,EAAAA,CAAAA,GACnCrG,GAAK6M,aAAa,SAAS9J,OAAOqD,EAAAA,CAAAA,GAClCpG,GAAK6M,aAAa,aAAa,mBAAmB/B,EAAAA,IAAAA,GAE9C6D,IAAS;AACX,kBAAI8C;AACJ,kBFhiBI,aEgiBA9C,GAAQ1K,KACVwN,CAAAA,KAAWvY,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,gBAAA,GAC/EyE,GAAS5E,aAAa,MAAM/B,EAAAA,GAC5B2G,GAAS5E,aAAa,iBAAiB,gBAAA,GACvC4E,GAAS5E,aAAa,MAAM9J,OAAOtC,KAAI2F,KAAQ,CAAA,CAAA,GAC/CqL,GAAS5E,aAAa,MAAM9J,OAAOrC,KAAI2F,KAAS,CAAA,CAAA,GAChDoL,GAAS5E,aAAa,MAAM9J,OAAOtC,KAAI2F,KAAQ,CAAA,CAAA,GAC/CqL,GAAS5E,aAAa,MAAM9J,OAAOrC,KAAI2F,KAAS,CAAA,CAAA,GAChDoL,GAAS5E,aAAa,KAAK9J,OAAO9C,KAAO,CAAA,CAAA;mBACpC;AACL,sBAAMuM,OAAamC,GAAQnC,YAAY,KAAKqF,OAAuB,IAAInW,KAAKoR,KACtE+I,MAAoBrJ,KAAW,IAAI9Q,KAAKoR,OAAO,IAAIpR,KAAKoR;AAC9D,oBAAIgJ,KAAKrV,KAAI2F,KAAQ,GACjB2P,KAAKrV,KAAI2F,KAAS,GAClB2P,KAAKvV,KAAI2F,KAAQ,GACjB6P,KAAKvV,KAAI2F,KAAS;AAGnBwP,gBAAAA,MAAoB,KAAKA,MAAoB,OAAOna,KAAKoR,MACzD+I,KAAmB,OAAOna,KAAKoR,MAAM+I,MAAoB,IAAIna,KAAKoR,MAEnEgJ,MAAU1P,KAAQ,GAClB2P,MAAW1P,KAAS,IAAK3K,KAAKwa,IAAI1J,EAAAA,GAClCwJ,MAAU5P,KAAQ,GAClB6P,MAAW5P,KAAS,IAAK3K,KAAKwa,IAAI1J,EAAAA,KACzBqJ,KAAmB,OAAOna,KAAKoR,MAAM+I,MAAoB,OAAOna,KAAKoR,MAC9EiJ,MAAU1P,KAAS,GACnByP,MAAU1P,KAAQ,IAAI1K,KAAKwa,IAAI1J,EAAAA,GAC/ByJ,MAAU5P,KAAS,GACnB2P,MAAU5P,KAAQ,IAAI1K,KAAKwa,IAAI1J,EAAAA,KACtBqJ,KAAmB,OAAOna,KAAKoR,MAAM+I,MAAoB,OAAOna,KAAKoR,MAC9EgJ,MAAU1P,KAAQ,GAClB2P,MAAW1P,KAAS,IAAK3K,KAAKwa,IAAI1J,EAAAA,GAClCwJ,MAAU5P,KAAQ,GAClB6P,MAAW5P,KAAS,IAAK3K,KAAKwa,IAAI1J,EAAAA,KACzBqJ,KAAmB,OAAOna,KAAKoR,MAAM+I,MAAoB,OAAOna,KAAKoR,OAC9EiJ,MAAU1P,KAAS,GACnByP,MAAU1P,KAAQ,IAAI1K,KAAKwa,IAAI1J,EAAAA,GAC/ByJ,MAAU5P,KAAS,GACnB2P,MAAU5P,KAAQ,IAAI1K,KAAKwa,IAAI1J,EAAAA,IAGjCiF,KAAWvY,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,gBAAA,GAC/EyE,GAAS5E,aAAa,MAAM/B,EAAAA,GAC5B2G,GAAS5E,aAAa,iBAAiB,gBAAA,GACvC4E,GAAS5E,aAAa,MAAM9J,OAAOrH,KAAKqV,MAAM+E,EAAAA,CAAAA,CAAAA,GAC9CrE,GAAS5E,aAAa,MAAM9J,OAAOrH,KAAKqV,MAAMgF,EAAAA,CAAAA,CAAAA,GAC9CtE,GAAS5E,aAAa,MAAM9J,OAAOrH,KAAKqV,MAAMiF,EAAAA,CAAAA,CAAAA,GAC9CvE,GAAS5E,aAAa,MAAM9J,OAAOrH,KAAKqV,MAAMkF,EAAAA,CAAAA,CAAAA;cAChD;AAEAtH,cAAAA,GAAQwH,WAAW3L,QAAQ,CAAA,EAAGtN,QAAAA,IAAQwU,OAAAA,GAAAA,MAAAA;AACpC,sBAAM0E,KAAOld,KAAK4S,QAAQd,SAASgC,gBAAgB,8BAA8B,MAAA;AACjFoJ,gBAAAA,GAAKvJ,aAAa,UAAa,MAAM3P,KAAT,GAAA,GAC5BkZ,GAAKvJ,aAAa,cAAc6E,EAAAA,GAChCD,GAASpG,YAAY+K,EAAAA;cAAK,CAAA,GAG5BpW,GAAK6M,aAAa,QAAQ,SAAS/B,EAAAA,IAAAA,GACnC5R,KAAK+V,MAAM5D,YAAYoG,EAAAA;YACzB,MAAWC,CAAAA,MACT1R,GAAK6M,aAAa,QAAQ6E,EAAAA;AAG5BxY,iBAAK0T,SAASvB,YAAYrL,EAAAA;UAC5B;QAAA;AAtjBO,UAAAqP,gBAAgB;AAAA,cAAA,IAAA,GG5CzB,IACU,UCGJgH,IAAoB,CAAC;AAE3B,iBAASpS,KAAO,GAAGA,MAAQ,IAAIA,KAC7BoS,GAAQpS,EAAAA,IAAQA;AAYlB,cC+CA,IA9BwC,EACtCA,MAAMqS,GACN5G,ONrCQ,UMsCRtJ,OAAO,KACPC,QAAQ,KACRzK,MAAM,IACN0D,QAAQ,GACR8Q,WAAW,EACT5W,YDzBJ,ECyBwB,CAAA,GACpB6E,MAAAA,QACA5E,sBCvCC,IAAA,GDyCH0W,cAAc,EACZ+D,YAAAA,MACAhD,oBAAAA,MACAZ,WAAW,KACXmE,aAAAA,QACAnV,QAAQ,EAAA,GAEVyP,aAAa,EACX9K,MAAM,UACNyN,OAAO,QACP1C,WAAAA,KAAW,GAEbwC,mBAAmB,EACjBT,OAAO,GACPW,OAAO,OAAA,EAAA;AE7DX,iBAAS6E,EAAiB9E,IAAAA;AACxB,gBAAM+E,KAAc,OAAH,OAAA,CAAA,GAAQ/E,EAAAA;AAEzB,cAAA,CAAK+E,GAAYL,cAAAA,CAAeK,GAAYL,WAAW9a,OACrD,OAAM;AAcR,iBAXImb,GAAYhK,WACdgK,GAAYhK,WAAWiK,OAAOD,GAAYhK,QAAAA,IAE1CgK,GAAYhK,WAAW,GAGzBgK,GAAYL,aAAaK,GAAYL,WAAWO,IAAKC,CAAAA,OAAkD,OAAD,OAAA,OAAA,OAAA,CAAA,GACjGA,EAAAA,GAAS,EACZzZ,QAAQuZ,OAAOE,GAAUzZ,MAAAA,EAAAA,CAAAA,CAAAA,GAGpBsZ;QACT;AAEe,iBAASI,EAAgBjI,IAAAA;AACtC,gBAAMkI,KAAa,OAAH,OAAA,CAAA,GAAQlI,EAAAA;AAkDxB,iBAhDAkI,GAAWzQ,QAAQqQ,OAAOI,GAAWzQ,KAAAA,GACrCyQ,GAAWxQ,SAASoQ,OAAOI,GAAWxQ,MAAAA,GACtCwQ,GAAWvX,SAASmX,OAAOI,GAAWvX,MAAAA,GACtCuX,GAAW1G,eAAe,OAAH,OAAA,OAAA,OAAA,CAAA,GAClB0G,GAAW1G,YAAAA,GAAY,EAC1Be,oBAAoB4F,QAAQD,GAAW1G,aAAae,kBAAAA,GACpDZ,WAAWmG,OAAOI,GAAW1G,aAAaG,SAAAA,GAC1ChR,QAAQmX,OAAOI,GAAW1G,aAAa7Q,MAAAA,EAAAA,CAAAA,GAGrCuX,GAAWvX,SAAS5D,KAAK8E,IAAIqW,GAAWzQ,OAAOyQ,GAAWxQ,MAAAA,MAC5DwQ,GAAWvX,SAAS5D,KAAK8E,IAAIqW,GAAWzQ,OAAOyQ,GAAWxQ,MAAAA,IAG5DwQ,GAAW9H,cAAc,OAAH,OAAA,CAAA,GACjB8H,GAAW9H,WAAAA,GAEZ8H,GAAW9H,YAAY0C,aACzBoF,GAAW9H,YAAY0C,WAAW8E,EAAiBM,GAAW9H,YAAY0C,QAAAA,IAGxEoF,GAAW7D,yBACb6D,GAAW7D,uBAAuB,OAAH,OAAA,CAAA,GAC1B6D,GAAW7D,oBAAAA,GAEZ6D,GAAW7D,qBAAqBvB,aAClCoF,GAAW7D,qBAAqBvB,WAAW8E,EAAiBM,GAAW7D,qBAAqBvB,QAAAA,KAI5FoF,GAAWpD,sBACboD,GAAWpD,oBAAoB,OAAH,OAAA,CAAA,GACvBoD,GAAWpD,iBAAAA,GAEZoD,GAAWpD,kBAAkBhC,aAC/BoF,GAAWpD,kBAAkBhC,WAAW8E,EAAiBM,GAAWpD,kBAAkBhC,QAAAA,KAItFoF,GAAWrF,sBACbqF,GAAWrF,oBAAoB,OAAH,OAAA,CAAA,GACvBqF,GAAWrF,iBAAAA,GAEZqF,GAAWrF,kBAAkBC,aAC/BoF,GAAWrF,kBAAkBC,WAAW8E,EAAiBM,GAAWrF,kBAAkBC,QAAAA,KAInFoF;QACT;AAAA,YAAA,IAAA,EAAA,GAAA,GAAA,IAAA,EAAA,EAAA,CAAA;AC5Ee,iBAASE,EAAYC,IAAAA;AAClC,cAAA,CAAKA,GAAW,OAAM,IAAIC,MAAM,2BAAA;AACX,kBAAjBD,GAAU,CAAA,MACZA,KAAYA,GAAUzV,UAAU,CAAA;AAElC,gBAAM0C,KAAO,EACX,KAAO,aACP,KAAO,aACP,KAAO,4BACP,MAAQ,cACR,KAAO,cACP,KAAO,aACP,KAAO,iBACP,KAAO,cACP,MAAQ,cACR,MAAQ,cACR,KAAO,kBAAA,EACP+S,GAAUE,YAAAA,CAAAA;AAEZ,cAAA,CAAKjT,GACH,OAAM,IAAIgT,MAAM,cAAcD,EAAAA,oBAAAA;AAGhC,iBAAO/S;QACT;QCTe,MAAMkT,EAAAA;UAYnB,YAAYxI,IAAAA;AAAAA,aACNA,QAAAA,KAAAA,SAAAA,GAASyI,SACXle,KAAK4S,UAAU,IAAI6C,GAAQyI,MAAM,IAAI,EAAEC,WAAW,SAAA,CAAA,EAAY1L,SAE9DzS,KAAK4S,UAAUH,QAEjBzS,KAAK4V,WAAWH,KAAUiI,EAAgBzM,GAAU,GAAgBwE,EAAAA,CAAAA,IAA+B,GACnGzV,KAAKoe,OAAAA;UACP;UAEA,OAAA,gBAAuBC,IAAAA;AACjBA,YAAAA,OACFA,GAAUC,YAAY;UAE1B;UAEA,YAAAC;AACE,gBAAA,CAAKve,KAAK8W,IACR;AAEF,kBAAM0H,KAAQ,IAAI,EAAMxe,KAAK4V,UAAU5V,KAAK4S,OAAAA;AAE5C5S,iBAAK0S,OAAO8L,GAAMpI,WAAAA,GAClBpW,KAAKye,qBAAqBD,GAAME,OAAO1e,KAAK8W,GAAAA,EAAKiE,KAAK,MAAA;AAAA,kBAAA7a;AAC/CF,mBAAK0S,SACK,UAAfxS,KAAAF,KAAK2e,eAAAA,WAAUze,MAAAA,GAAA,KAAA,MAAGse,GAAMpI,WAAAA,GAAcpW,KAAK4V,QAAAA;YAAS,CAAA;UAExD;UAEA,eAAAgJ;AAAAA,gBAAAA,IAAAA;AACO5e,iBAAK8W,SAIkB,UAAxB7W,KAAAD,KAAK4V,SAASkF,eAAAA,WAAU7a,KAAA,SAAAA,GAAEib,iBAC5Blb,KAAK6e,cAAc7e,KAAK4V,SAASkF,WAAWI,aAAalb,KAAK4V,SAAS1I,OAAOlN,KAAK4V,SAASzI,MAAAA,GAC5FnN,KAAK6e,YAAY3R,QAAQlN,KAAK4V,SAAS1I,OACvClN,KAAK6e,YAAY1R,SAASnN,KAAK4V,SAASzI,WAExCnN,KAAK8e,aAAahN,SAASC,cAAc,QAAA,GACzC/R,KAAK8e,WAAW5R,QAAQlN,KAAK4V,SAAS1I,OACtClN,KAAK8e,WAAW3R,SAASnN,KAAK4V,SAASzI,SAGzCnN,KAAKue,UAAAA,GACLve,KAAK+e,wBAA+C,UAAvB7e,KAAAF,KAAKye,uBAAAA,WAAkBve,KAAA,SAAAA,GAAE6a,KAAK,MAAA;AAAA,kBAAA9a;AACzD,kBAAA,CAAKD,KAAK0S,KAAM;AAEhB,oBAAMF,KAAMxS,KAAK0S,MACXsM,KAAM,IAAIhf,KAAK4S,QAAQqM,gBAAgBC,kBAAkB1M,EAAAA,GACzD2M,KAAQC,KAAKJ,EAAAA,GACbK,KAAU,QAAQxB,EAAY,KAAA,CAAA,WAAiBsB,EAAAA;AAErD,kBAA4B,UAAxBlf,KAAAD,KAAK4V,SAASkF,eAAAA,WAAU7a,KAAA,SAAAA,GAAE8W,UAC5B,QAAO/W,KAAK4V,SAASkF,WAAW/D,UAAUsI,EAAAA,EAAStE,KAAM9E,CAAAA,OAAAA;AAAAA,oBAAAA,IAAAA;AAEvDA,gBAAAA,GAAM/I,QAAQlN,KAAK4V,SAAS1I,OAC5B+I,GAAM9I,SAASnN,KAAK4V,SAASzI,QACK,UAAlChN,KAAgB,UAAhBD,KAAAF,KAAK6e,gBAAAA,WAAW3e,KAAA,SAAAA,GAAEib,WAAW,IAAA,MAAA,WAAKhb,MAAAA,GAAEgY,UAAUlC,IAAO,GAAG,CAAA;cAAE,CAAA;AAEvD;AACL,sBAAMA,KAAQ,IAAIjW,KAAK4S,QAAQ0I;AAE/B,uBAAO,IAAIX,QAASC,CAAAA,OAAAA;AAClB3E,kBAAAA,GAAMuF,SAAS,MAAA;AAAA,wBAAArb,IAAAC;AACoB,8BAAjCA,KAAe,UAAfD,KAAAH,KAAK8e,eAAAA,WAAU3e,KAAA,SAAAA,GAAEgb,WAAW,IAAA,MAAA,WAAK/a,MAAAA,GAAE+X,UAAUlC,IAAO,GAAG,CAAA,GACvD2E,GAAAA;kBAAS,GAGX3E,GAAMqG,MAAM+C;gBAAO,CAAA;cAEvB;YAAA,CAAA;UAEJ;UAEA,MAAA,YAAkBvB,KAA2B,OAAA;AAC3C,gBAAA,CAAK9d,KAAK8W,IAAK,OAAM;AAErB,mBAAgC,UAA5BgH,GAAUE,YAAAA,KACPhe,KAAK0S,QAAS1S,KAAKye,sBACtBze,KAAKue,UAAAA,GAAAA,MAEDve,KAAKye,oBACJze,KAAK0S,UAEN1S,KAAK8e,cAAc9e,KAAK6e,gBAAiB7e,KAAK+e,yBAClD/e,KAAK4e,aAAAA,GAAAA,MAED5e,KAAK+e,uBACJ/e,KAAK8e,cAAc9e,KAAK6e;UAEnC;UAEA,OAAOpJ,IAAAA;AACLwI,cAAcqB,gBAAgBtf,KAAKuf,UAAAA,GACnCvf,KAAK4V,WAAWH,KAAUiI,EAAgBzM,GAAUjR,KAAK4V,UAAUH,EAAAA,CAAAA,IAA+BzV,KAAK4V,UAElG5V,KAAK4V,SAASlT,SAInB1C,KAAK8W,MAAM,EAAA,EAAO9W,KAAK4V,SAASsB,UAAU5W,YAAYN,KAAK4V,SAASsB,UAAU3W,oBAAAA,GAC9EP,KAAK8W,IAAI5R,QAAQlF,KAAK4V,SAASlT,MAAM1C,KAAK4V,SAASsB,UAAU/R,QC9HlD,SAAiBzC,IAAAA;AAC9B,sBAAA,MAAQ;gBACN,KAAK,WAAWzB,KAAKyB,EAAAA;AACnB,yBtBCK;gBsBAP,KAAK,wBAAwBzB,KAAKyB,EAAAA;AAChC,yBtBAU;gBsBCZ;AACE,yBtBDE;cAAA;YsBGR,EDqHiF1C,KAAK4V,SAASlT,IAAAA,CAAAA,GAC3F1C,KAAK8W,IAAIlR,KAAAA,GAEL5F,KAAK4V,SAAS7K,SAASqS,IACzBpd,KAAK4e,aAAAA,IAEL5e,KAAKue,UAAAA,GAGPve,KAAKwf,OAAOxf,KAAKuf,UAAAA;UACnB;UAEA,OAAOlB,IAAAA;AACL,gBAAKA,IAAL;AAIA,kBAAqC,cAAA,OAA1BA,GAAUlM,YACnB,OAAM;AAGJnS,mBAAK4V,SAAS7K,SAASqS,IACrBpd,KAAK8e,cACPT,GAAUlM,YAAYnS,KAAK8e,UAAAA,IAGzB9e,KAAK0S,QACP2L,GAAUlM,YAAYnS,KAAK0S,IAAAA,GAI/B1S,KAAKuf,aAAalB;YAhBlB;UAiBF;UAEA,eAAeP,IAAAA;AACb,gBAAA,CAAKA,GACH,OAAM;AAGR9d,iBAAK2e,aAAab,IAClB9d,KAAKoe,OAAAA;UACP;UAEA,kBAAAqB;AACEzf,iBAAK2e,aAAAA,QACL3e,KAAKoe,OAAAA;UACP;UAEA,MAAA,WAAiBN,KAA2B,OAAA;AAC1C,gBAAA,CAAK9d,KAAK8W,IAAK,OAAM;AACrB,kBAAMsB,KAAAA,MAAgBpY,KAAK0f,YAAY5B,EAAAA,GACjC6B,KAAW9B,EAAYC,EAAAA;AAE7B,gBAAA,CAAK1F,GACH,QAAO;AAGT,gBAAgC,UAA5B0F,GAAUE,YAAAA,GAAyB;AACrC,oBAEM4B,KAAY;EAFC,IAAI5f,KAAK4S,QAAQqM,gBACVC,kBAAkB9G,EAAAA,CAAAA;AAE5C,qBAAoB,eAAA,OAATyH,QAAyB7f,KAAK4V,SAASsI,QAGzC4B,OAAOC,KAAKH,EAAAA,IAFZ,IAAIC,KAAK,CAACD,EAAAA,GAAY,EAAE7U,MAAM4U,GAAAA,CAAAA;YAIzC;AACE,mBAAO,IAAIhF,QAASC,CAAAA,OAAAA;AAClB,oBAAMK,KAAS7C;AACf,kBAAI,cAAc6C,GAEhB,KAAiB,gBAAb0E,GACF/E,CAAAA,GAAQK,GAAO+E,SAASL,EAAAA,CAAAA;uBACF,iBAAbA,GACT/E,CAAAA,GAAQK,GAAO+E,SAASL,EAAAA,CAAAA;mBACnB;AAAA,oBAAiB,sBAAbA,GAGT,OAAM5B,MAAM,uBAAA;AAFZnD,gBAAAA,GAAQK,GAAO+E,SAASL,EAAAA,CAAAA;cAG1B;kBACS,aAAY1E,MACrB7a,GAAS6f,OAAOrF,IAAS+E,IAAU,CAAA;YACrC,CAAA;UAGN;UAEA,MAAA,SAAeO,IAAAA;AACb,gBAAA,CAAKlgB,KAAK8W,IAAK,OAAM;AACrB,gBAAoB,eAAA,OAAT+I,KAAsB,OAAM;AACvC,gBAAI/B,KAAY,OACZlM,KAAO;AAGoB,wBAAA,OAApBsO,MACTpC,KAAYoC,IACZC,QAAQC,KACN,6HAAA,KAEkC,YAAA,OAApBF,MAAoD,SAApBA,OAC5CA,GAAgBtO,SAClBA,KAAOsO,GAAgBtO,OAErBsO,GAAgBpC,cAClBA,KAAYoC,GAAgBpC;AAIhC,kBAAM1F,KAAAA,MAAgBpY,KAAK0f,YAAY5B,EAAAA;AAEvC,gBAAK1F,GAIL,KAAgC,UAA5B0F,GAAUE,YAAAA,GAAyB;AAErC,kBAAI5M,KADe,IAAI6N,gBACCC,kBAAkB9G,EAAAA;AAE1ChH,cAAAA,KAAS,8CAA8CA,IAEvDM,EADY,QAAQmM,EAAYC,EAAAA,CAAAA,kBAA4BuC,mBAAmBjP,EAAAA,CAAAA,IAC9D,GAAGQ,EAAAA,MAAAA;YACtB,MAEEF,GADa0G,GAA8BgD,UAAUyC,EAAYC,EAAAA,CAAAA,GAChD,GAAGlM,EAAAA,IAAQkM,EAAAA,EAAAA;UAEhC;QAAA;AEnOF,cAAA,IAAA;MAAA,GAAA,GAAA,EAAA;IAAA,GAAA,CAAA;;;", "names": ["root", "factory", "exports", "module", "define", "amd", "this", "t", "e", "i", "r", "qrcode", "typeNumber", "errorCorrectionLevel", "_typeNumber", "_errorCorrectionLevel", "QRErrorCorrectionLevel", "_modules", "_moduleCount", "_dataCache", "_dataList", "_this", "makeImpl", "test", "maskPattern", "moduleCount", "modules", "Array", "row", "col", "setupPositionProbePattern", "setupPositionAdjustPattern", "setupTimingPattern", "setupTypeInfo", "setupTypeNumber", "createData", "mapData", "c", "pos", "QRUtil", "getPatternPosition", "length", "j", "bits", "getBCHTypeNumber", "mod", "Math", "floor", "data", "getBCHTypeInfo", "inc", "bitIndex", "byteIndex", "maskFunc", "getMaskFunction", "dark", "dataList", "rsBlocks", "QRRSBlock", "getRSBlocks", "buffer", "qrBitBuffer", "put", "getMode", "<PERSON><PERSON><PERSON><PERSON>", "getLengthInBits", "write", "totalDataCount", "dataCount", "putBit", "offset", "maxDcCount", "maxEcCount", "dcdata", "ecdata", "dcCount", "ecCount", "totalCount", "max", "<PERSON><PERSON><PERSON><PERSON>", "rsPoly", "getErrorCorrectPolynomial", "modPoly", "qrPolynomial", "modIndex", "getAt", "totalCodeCount", "index", "addData", "mode", "newData", "qrNumber", "qrAlphaNum", "qr8BitByte", "<PERSON>r<PERSON><PERSON><PERSON>", "push", "isDark", "getModuleCount", "make", "minLostPoint", "pattern", "lostPoint", "getLostPoint", "getBestMaskPattern", "createTableTag", "cellSize", "margin", "qrHtml", "createSvgTag", "alt", "title", "opts", "arguments", "text", "id", "mr", "rect", "size", "qrSvg", "scalable", "escapeXml", "join", "trim", "createDataURL", "min", "x", "y", "createImgTag", "img", "s", "escaped", "char<PERSON>t", "createASCII", "r1", "r2", "p", "blocks", "blocksLastLineNoMargin", "ascii", "substring", "white", "black", "line", "renderTo2dContext", "context", "fillStyle", "fillRect", "stringToBytes", "stringToBytesFuncs", "bytes", "charCodeAt", "createStringToBytes", "unicodeData", "numChars", "unicodeMap", "bin", "base64DecodeInputStream", "read", "b", "count", "b0", "b1", "v", "String", "fromCharCode", "unknownChar", "PATTERN_POSITION_TABLE", "G15", "G18", "getBCHDigit", "L", "M", "Q", "H", "digit", "d", "errorCorrectLength", "a", "multiply", "QRMath", "gexp", "type", "sameCount", "darkCount", "abs", "EXP_TABLE", "LOG_TABLE", "n", "num", "shift", "_num", "glog", "ratio", "RS_BLOCK_TABLE", "qrRS<PERSON>lock", "rsBlock", "list", "_buffer", "_length", "bufIndex", "bit", "_data", "strToNum", "chatToNum", "getCode", "_bytes", "byteArrayOutputStream", "writeByte", "off", "len", "str", "_str", "_pos", "_buflen", "match", "decode", "width", "height", "getPixel", "gif", "_width", "_height", "pixel", "out", "writeString", "writeShort", "raster", "getLZWRaster", "writeBytes", "lzwMinCodeSize", "clearCode", "endCode", "bitLength", "table", "lzwTable", "add", "_out", "_bitLength", "_bitBuffer", "byteOut", "bitOut", "dataIndex", "contains", "indexOf", "flush", "toByteArray", "_map", "_size", "key", "setPixel", "base64", "_base64", "writeEncoded", "encode", "padlen", "toString", "base64EncodeOutputStream", "utf8", "charcode", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "getter", "__esModule", "definition", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "isObject", "isArray", "mergeDeep", "target", "sources", "source", "keys", "for<PERSON>ach", "targetValue", "sourceValue", "assign", "downloadURI", "uri", "name", "link", "document", "createElement", "download", "href", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "QRDot", "constructor", "svg", "window", "_svg", "_type", "_window", "getNeighbor", "drawFunction", "_drawDot", "_drawClassy", "_drawClassyRounded", "_drawRounded", "_drawExtraRounded", "_drawSquare", "_rotateFigure", "rotation", "draw", "cx", "cy", "_element", "setAttribute", "PI", "args", "createElementNS", "_basicDot", "_basicSquare", "leftNeighbor", "rightNeighbor", "topNeighbor", "bottomNeighbor", "neighborsCount", "_basicCornerRounded", "_basicSideRounded", "_basicCornerExtraRounded", "_basicCornersRounded", "dot", "square", "extraRounded", "availableCornerSquareTypes", "values", "cornerSquareTypes", "QRCornerSquare", "dotSize", "_basicExtraRounded", "availableCornerDotTypes", "cornerDotTypes", "QRCornerDot", "squareMask", "dotMask", "QRSVG", "options", "_roundSize", "value", "_options", "dotsOptions", "roundSize", "_defs", "_imageUri", "image", "_instanceId", "instanceCount", "getElement", "qr", "minSize", "realQRSize", "shape", "shapeTypes", "sqrt", "drawImageSize", "hideXDots", "hideYDots", "_qr", "loadImage", "_image", "imageOptions", "qrOptions", "coverLevel", "imageSize", "errorCorrectionPercents", "maxHiddenDots", "originalHeight", "originalWidth", "maxHiddenAxisDots", "hideDots", "k", "ceil", "round", "drawBackground", "drawDots", "hideBackgroundDots", "h", "drawCorners", "drawImage", "element", "gradientOptions", "backgroundOptions", "gradient", "color", "_backgroundClip<PERSON>ath", "_createColor", "additionalRotation", "filter", "xBeginning", "yBeginning", "_dotsClipPath", "xOffset", "yOffset", "additionalDots", "fakeCount", "xFakeBeginning", "yFakeBeginning", "fakeMatrix", "center", "cornersSquareSize", "cornersDotSize", "column", "cornersSquareClipPath", "cornersDotClipPath", "g", "cornersSquareOptions", "_", "_cornersSquareClipPath", "_cornersDotClipPath", "m", "includes", "cornersSquare", "S", "C", "cornersDotOptions", "A", "$", "cornersDot", "Promise", "resolve", "reject", "nodeCanvas", "then", "saveAsBlob", "canvas", "createCanvas", "getContext", "toDataURL", "catch", "Image", "crossOrigin", "onload", "async", "url", "xhr", "XMLHttpRequest", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "response", "open", "responseType", "send", "src", "dx", "dy", "dw", "dh", "positiveRotation", "x0", "y0", "x1", "y1", "tan", "colorStops", "stop", "qrTypes", "drawTypes", "sanitizeGradient", "newGradient", "Number", "map", "colorStop", "sanitizeOptions", "newOptions", "Boolean", "getMimeType", "extension", "Error", "toLowerCase", "QRCodeStyling", "jsdom", "resources", "update", "container", "innerHTML", "_setupSvg", "qrSVG", "_svgDrawingPromise", "drawQR", "_extension", "_setup<PERSON>anvas", "_nodeCanvas", "_dom<PERSON><PERSON><PERSON>", "_canvasDrawingPromise", "xml", "XMLSerializer", "serializeToString", "svg64", "btoa", "image64", "_clearContainer", "_container", "append", "deleteExtension", "_getElement", "mimeType", "svgString", "Blob", "<PERSON><PERSON><PERSON>", "from", "<PERSON><PERSON><PERSON><PERSON>", "toBlob", "downloadOptions", "console", "warn", "encodeURIComponent"]}