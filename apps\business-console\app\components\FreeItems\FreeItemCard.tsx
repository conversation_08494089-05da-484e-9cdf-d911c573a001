import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../ui/card";
import { Badge } from "../ui/badge";
import { FreeItem } from "~/types/api/businessConsoleService/FreeItem";
import { DISCOUNT_TYPES } from "~/types/api/mockData/mockdata";
import { Button } from "../ui/button";


interface FreeItemCardProps {
  item: FreeItem;
  onEdit: (item: FreeItem) => void;
}

// Utility function to format date as 'DD MMM YYYY'
const formatDate = (date: Date | string) => {
  const d = typeof date === "string" ? new Date(date) : date;
  return d.toLocaleDateString(undefined, {
    day: "2-digit",
    month: "short",
    year: "numeric",
  });
};

const FreeItemCard: React.FC<FreeItemCardProps> = ({ item, onEdit }) => {
  const getDiscountTypeLabel = (type: string) => {
    return DISCOUNT_TYPES.find((t) => t.value === type)?.label || type;
  };

  const getDiscountValue = () => {
    switch (item.discountType) {
      case "percentage":
        return `${item.discountPercentage}% Off`;
      case "flat":
        return `₹${item.discountFlat} Off`;
      case "freeitem":
        return `Free Item x${item.freeItemQty}`;
      case "buyonegetone":
        return "Buy 1 Get 1 Free";
      default:
        return "Special Offer";
    }
  };

  const now = new Date();
  const isActive = now >= item.validFrom && now <= item.validTo && !item.discountDisabled;

  return (
    <Card className="h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
      <CardHeader className="p-4">
        <div className="flex justify-between items-start">
          <div className="flex flex-col">
            <p className="text-lg font-semibold">
              {getDiscountValue()}
            </p>
            <p className="text-sm text-muted-foreground">
              {getDiscountTypeLabel(item.discountType)}
            </p>
          </div>
          <Badge variant={isActive ? "default" : "outline"} className={isActive ? "bg-green-500" : ""}>
            {isActive ? "Active" : "Inactive"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <div className="space-y-2">
          {item.discountMinOrderQty && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Min. Order</span>
              <span className="font-medium">₹{item.discountMinOrderQty}</span>
            </div>
          )}
          {item.discountUpto && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Max Discount</span>
              <span className="font-medium">₹{item.discountUpto}</span>
            </div>
          )}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Valid From</span>
            <span className="font-medium">{formatDate(item.validFrom)}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Valid Until</span>
            <span className="font-medium">{formatDate(item.validTo)}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0">
        <Button 
          variant="outline" 
          className="w-full"
          onClick={() => onEdit(item)}
        >
          Edit Offer
        </Button>
      </CardFooter>
    </Card>
  );
};

export default FreeItemCard;