import React, { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import { MyAddonData } from "~/types/api/businessConsoleService/SellerManagement";
import { Form, useFetcher } from "@remix-run/react";
import { SquareX } from "lucide-react";
import { useToast } from "../ui/ToastProvider";
import { AddOnGroup } from "~/types/api/businessConsoleService/MyItemList";



interface SelectedVariationAddonsProps {
      isOpen: boolean;
      items: MyAddonData[];
      onClose: () => void;
      header: string;
      groupData?: AddOnGroup,
      sellerId?: number,
      groupId?: number,
      isEdit?: boolean
}
const SelectedVariationAddons: React.FC<SelectedVariationAddonsProps> = ({
      isOpen,
      items,
      onClose,
      header,
      groupData,
      sellerId,
      groupId,
      isEdit
}) => {
      const [selectedId, setSelectedId] = useState<string | number | null>(null);
      const [searchTerm, setSearchTerm] = useState('');
      const [filteredAddon, setFilteredAddon] = useState<MyAddonData[]>(items)
      const [choosenAddon, setChoosenAddon] = useState<boolean>(false);
      const [choossenAddonName, setChoosenAddonName] = useState<string>('');
      const [formData, setFormData] = useState({
            minSelect: groupData?.minSelect.toString() || "0",
            maxSelect: groupData?.seq.toString() || "0",
            name: groupData?.name.toString() || "",
            description: groupData?.description.toString() || "",
            varient: groupData?.varient ?? false,
            seq: groupData?.seq ?? "0"
      });
      const { showToast } = useToast()
      useEffect(() => {
            if (searchTerm.length >= 3 && searchTerm !== "") {
                  setFilteredAddon(items?.filter(addon => addon?.name.toLowerCase().includes(searchTerm.toLowerCase())))
            }
            else {
                  setFilteredAddon(items)
            } ``
      }, [searchTerm, items]);

      useEffect(() => {
            if (!isOpen) {
                  setSelectedId(null);
                  setSearchTerm("");
                  setChoosenAddon(false);
                  setFormData({
                        minSelect: "0",
                        maxSelect: "0",
                        name: "",
                        description: "",
                        varient: false,
                        seq: "0"
                  })
            }

      }, [isOpen]);
      useEffect(() => {
            if (groupData) {
                  setChoosenAddon(true);
                  setSelectedId(groupData?.id)
                  setChoosenAddonName(groupData.name)
                  setFormData(prev => ({
                        ...prev,
                        minSelect: groupData?.minSelect.toString(),
                        maxSelect: groupData?.seq.toString(),
                        name: groupData?.name.toString(),
                        description: groupData?.description.toString(),
                        varient: groupData?.varient,
                  }))
            }

      }, [groupData]);

      const handleSelect = (addon: MyAddonData) => {
            setSelectedId(addon.id);
            setChoosenAddon(true)
            setChoosenAddonName(addon.name)

      }
      const deselectAddon = () => {
            setSelectedId(null)
            setChoosenAddon(false)
      }
      const groupMapfetcher = useFetcher();
      useEffect(() => {
            if (groupMapfetcher.data) {
                  if (groupMapfetcher.data?.sucess) {
                        showToast("sucess to Map GroupData", 'success')
                        onClose()
                        setFormData({
                              minSelect: "0",
                              maxSelect: "0",
                              name: "",
                              description: "",
                              varient: false,
                              seq: "0"
                        })

                  }
                  else if (groupMapfetcher.data?.sucess === false) {
                        showToast("failed to  Map  GroupData", 'success')
                  }

            }

      }, [groupMapfetcher?.data])



      if (!isOpen) return null;
      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="w-full max-w-md sm:max-w-lg bg-white rounded-2xl shadow-2xl">
                        <DialogTitle className="text-2xl font-bold text-gray-900 mb-4">{header}</DialogTitle>
                        <div className="space-y-6">
                              {choosenAddon === false && selectedId === null && <>
                                    <div>
                                          <input
                                                placeholder="Search by Addon Name"
                                                type="search"
                                                className="w-full p-3 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-colors"
                                                autoFocus
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                          />
                                    </div>
                                    <div className="mt-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                          <ul className="space-y-2">
                                                {filteredAddon.length === 0 ? (
                                                      <p className="p-4 text-gray-500 text-center">No add-ons found</p>
                                                ) : (
                                                      filteredAddon.map((item) => (
                                                            <li key={item.id} className="flex items-center gap-3">
                                                                  <input
                                                                        type="checkbox"
                                                                        id={`item-${item.id}`}
                                                                        name="selectedItem"
                                                                        value={item.id}
                                                                        checked={selectedId === item.id}
                                                                        onChange={() => handleSelect(item)}
                                                                        className="h-5 w-5 text-blue-600 focus:ring-blue-500 rounded"
                                                                  />
                                                                  <label
                                                                        htmlFor={`item-${item.id}`}
                                                                        className={`cursor-pointer p-3 w-full rounded-lg border ${selectedId === item.id ? 'bg-blue-50 border-blue-200' : 'border-gray-200'} text-gray-800 hover:bg-gray-50 transition-colors`}
                                                                  >
                                                                        {item?.name} <span className="text-gray-500">({item?.diet})</span>
                                                                  </label>
                                                            </li>
                                                      ))
                                                )}
                                          </ul>
                                    </div>
                              </>
                              }

                              {choosenAddon && selectedId && <div className="space-y-4">
                                    {selectedId && (
                                          <div className="flex items-center justify-between bg-blue-50 p-3 rounded-lg">
                                                <p className="font-medium text-gray-800 truncate max-w-[80%]">{choossenAddonName}</p>
                                                <SquareX
                                                      color="red"
                                                      className="cursor-pointer hover:scale-110 transition-transform"
                                                      onClick={() => deselectAddon()}
                                                />
                                          </div>
                                    )}

                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                          <div>
                                                <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                                                      name
                                                </label>
                                                <input
                                                      type="text"
                                                      id="name"
                                                      name="name"
                                                      placeholder="Enter the Name"
                                                      value={formData.name}
                                                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                                      required
                                                      className="w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
                                                />
                                          </div>
                                          <div>
                                                <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                                                      description
                                                </label>
                                                <input
                                                      type="text"
                                                      id="description"
                                                      name="description"
                                                      placeholder="Enter the description"

                                                      value={formData.description}
                                                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                                                      required
                                                      className="w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
                                                />
                                          </div>
                                          <div>
                                                <label htmlFor="minSelect" className="block text-sm font-medium text-gray-700 mb-1">
                                                      minSelect
                                                </label>
                                                <input
                                                      type="number"
                                                      id="minSelect"
                                                      name="minSelect"
                                                      placeholder="Enter the minimum selection count"
                                                      value={formData.minSelect}
                                                      onChange={(e) => setFormData({ ...formData, minSelect: e.target.value })}
                                                      min="0"
                                                      required
                                                      className="w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
                                                />
                                          </div>
                                          <div>
                                                <label htmlFor="maxSelect" className="block text-sm font-medium text-gray-700 mb-1">
                                                      maxSelect
                                                </label>
                                                <input
                                                      type="number"
                                                      id="maxSelect"
                                                      name="maxSelect"
                                                      placeholder="Enter the maximum selection count"
                                                      value={formData.maxSelect}
                                                      onChange={(e) => setFormData({ ...formData, maxSelect: e.target.value })}
                                                      min="0"
                                                      required
                                                      className="w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
                                                />
                                          </div>
                                          <div>
                                                <label htmlFor="seq" className="block text-sm font-medium text-gray-700 mb-1">
                                                      Sequence
                                                </label>
                                                <input
                                                      type="number"
                                                      id="seq"
                                                      name="seq"
                                                      placeholder="Enter the sequence number"
                                                      value={formData.seq}
                                                      onChange={(e) => setFormData({ ...formData, seq: e.target.value })}
                                                      min="0"
                                                      required
                                                      className="w-full p-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none"
                                                />
                                          </div>

                                    </div>
                              </div>}
                        </div>
                        <Form method="POST" className="mt-6 flex flex-col sm:flex-row gap-3 justify-end">
                              <input type="hidden" name="addonId" value={selectedId?.toString()} />
                              <input type="hidden" name="variationId" value={groupId?.toString()} />
                              <input type="hidden" name="minSelect" value={formData.minSelect?.toString()} />
                              <input type="hidden" name="maxSelect" value={formData.maxSelect?.toString()} />
                              <input type="hidden" name="varient" value={formData.varient?.toString()} />
                              <input type="hidden" name="sellerId" value={sellerId?.toString()} />
                              <input type="hidden" name="name" value={formData.name?.toString()} />
                              <input type="hidden" name="description" value={formData.description?.toString()} />
                              <input type="hidden" name="seq" value={formData.seq?.toString()} />
                              {isEdit && <input type="hidden" name="itemVariationId" value={groupData.id?.toString()} />
                              }
                              <input type="hidden" name="addonName" value={choossenAddonName?.toString()} />
                              <input type="hidden" name="actionType" value={"actionAddonforVariation"} />
                              <input type="hidden" name="mode" value={isEdit ? "EditMode" : ""} />

                              <button
                                    onClick={onClose}
                                    className="w-full sm:w-auto px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm font-medium"
                              >
                                    Cancel
                              </button>
                              <button
                                    type="submit"
                                    disabled={selectedId === null}
                                    className={`w-full sm:w-auto px-4 py-2 rounded-lg text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${selectedId === null ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
                              >
                                    Confirm
                              </button>
                        </Form>
                  </DialogContent>
            </Dialog>
      );
};

export default SelectedVariationAddons;
