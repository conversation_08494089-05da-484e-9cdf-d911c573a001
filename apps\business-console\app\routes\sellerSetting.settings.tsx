import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import { Link } from "@remix-run/react";
import { GalleryHorizontal } from "lucide-react";
import { FaWhatsapp } from "react-icons/fa";

export default function Settings() {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-2">Manage your restaurant configuration</p>
      </div>
      
      {/* Coming Soon Features */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Coming Soon Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="border-dashed border-2 border-gray-300">
            <CardHeader>
              <CardTitle className="text-gray-600">Advanced Analytics</CardTitle>
              <CardDescription>Detailed business insights</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-400">📊</div>
              <p className="text-sm text-gray-500 mt-2">Launching soon</p>
            </CardContent>
          </Card>

          <Card className="border-dashed border-2 border-gray-300">
            <CardHeader>
              <CardTitle className="text-gray-600">Inventory Management</CardTitle>
              <CardDescription>Stock tracking system</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-400">📦</div>
              <p className="text-sm text-gray-500 mt-2">Launching soon</p>
            </CardContent>
          </Card>

          <Card className="border-dashed border-2 border-gray-300">
            <CardHeader>
              <CardTitle className="text-gray-600">Customer Loyalty</CardTitle>
              <CardDescription>Rewards program</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-400">🎁</div>
              <p className="text-sm text-gray-500 mt-2">Launching soon</p>
            </CardContent>
          </Card>

          <Card className="border-dashed border-2 border-gray-300">
            <CardHeader>
              <CardTitle className="text-gray-600">Multi-location</CardTitle>
              <CardDescription>Branch management</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-400">🏢</div>
              <p className="text-sm text-gray-500 mt-2">Launching soon</p>
            </CardContent>
          </Card>

          <Card className="border-dashed border-2 border-gray-300">
            <CardHeader>
              <CardTitle className="text-gray-600">API Integration</CardTitle>
              <CardDescription>Third-party connections</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-400">🔌</div>
              <p className="text-sm text-gray-500 mt-2">Launching soon</p>
            </CardContent>
          </Card>

          <Card className="border-dashed border-2 border-gray-300">
            <CardHeader>
              <CardTitle className="text-gray-600">Advanced Reports</CardTitle>
              <CardDescription>Custom reporting</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-400">📈</div>
              <p className="text-sm text-gray-500 mt-2">Launching soon</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Sub-menu Section */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Additional Settings</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link to="/sellerSetting/nbanners">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <GalleryHorizontal className="text-primary" size={20} />
                  <CardTitle className="text-lg">Banners & Sequence</CardTitle>
                </div>
                <CardDescription>Manage promotional banners</CardDescription>
              </CardHeader>
            </Card>
          </Link>

          <Link to="/sellerSetting/whatsappprofile">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <FaWhatsapp className="text-primary" size={20} />
                  <CardTitle className="text-lg">WhatsApp Settings</CardTitle>
                </div>
                <CardDescription>Configure WhatsApp integration</CardDescription>
              </CardHeader>
            </Card>
          </Link>

          <Link to="/sellerSetting/coupons">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">🎉</span>
                  <CardTitle className="text-lg">Coupons</CardTitle>
                </div>
                <CardDescription>Manage discount offers</CardDescription>
              </CardHeader>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  );
} 