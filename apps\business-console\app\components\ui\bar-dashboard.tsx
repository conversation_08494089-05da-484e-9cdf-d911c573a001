import { DashboardGroupBy } from "~/types/home";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "./tabs";
import { useEffect, useState } from "react";
import { SellerConsoleDataResponse } from "~/types/api/businessConsoleService/SellerConsoleDataResponse";

import { addDays, addMonths, format, getWeekOfMonth, startOfWeek } from "date-fns";
import { Card, CardContent, CardHeader, CardTitle } from "./card";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "./button";
import { Bar, BarChart, CartesianGrid, Label, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";
import { formatCurrency } from "~/utils/format";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select";


const COLORS = ['#afded7', '#f8c8cd', '#cfc2ed', '#AFEFED', '#90c8e7', '#FDDFB4', '#E789B7', '#FFD384', '#00a390', '#7551ce']

export interface DataPoint {
    periodLabel: string;
    startDate: Date;
    Revenue: number;
    returnAmount: number;
    returnweight: number;
    Quantity: number;
    Orders: number;
    AovValue: number;
    NewCustomers: number;



}

export function calculateTargetDate(today: Date, groupBy: DashboardGroupBy, periodIndex: number): Date {
    // console.log('period...Index....Calculate...Tg', periodIndex);
    switch (groupBy) {
        case DashboardGroupBy.Daily:
            // return addDays(today, -7 * periodIndex);
            // Calculate last day of the week (Saturday)
            if (periodIndex > 0) {
                const dayOfWeek = today.getDay(); // Sunday = 0, Monday = 1, ..., Saturday = 6
                const daysUntilSaturday = 6 - dayOfWeek;
                return addDays(addDays(today, -7 * periodIndex), daysUntilSaturday);
            } else { return addDays(today, 0); }

        case DashboardGroupBy.Weekly:
            //return addDays(today, -28 * periodIndex);
            const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1 - periodIndex, 0); // Day 0 gives the last day of the previous month
            return lastDayOfMonth;
        case DashboardGroupBy.Monthly:
            return new Date(today.getFullYear() - periodIndex, 11, 31);
        default:
            return today;
    }
}

export const calculatePeriodDates = (dataPoint: DataPoint, activeTab: DashboardGroupBy): { startDate: Date, endDate: Date } | null => {
    try {
        let startDate = dataPoint.startDate;
        let endDate = activeTab === DashboardGroupBy.Daily ? dataPoint.startDate :
            activeTab === DashboardGroupBy.Weekly ? addDays(startDate, 6) : addDays(addMonths(startDate, 1), -1);
        return { startDate: startDate, endDate: endDate };
    } catch (error) {
        console.error("Error calculating date range:", error);
        return null;
    }
};

const CustomerDashboard = ({
    data, handleGraph,
    dashboardGroupBy,
    handleBarSelected,

}: { data: SellerConsoleDataResponse[], handleGraph: (value: string, summaryDate: Date) => void, dashboardGroupBy: DashboardGroupBy, handleBarSelected?: (startDate: Date, endDate: Date, tab: string, summaryDate: Date, selectedSellerData: DataPoint) => void }) => {


    const [chartData, setChartData] = useState<DataPoint[]>([]);

    const getDefaultSelectedData = (): DataPoint | null => {
        if (chartData === null || chartData.length === 0)
            return null;
        return chartData[chartData.length - 1];
    }

    const [activeTab, setActiveTab] = useState<DashboardGroupBy>(dashboardGroupBy);
    //const [selectedPeriod, setSelectedPeriod] = useState<string | null>(getDefaultSelectedPeriod(dashboardGroupBy));
    const [selectedData, setSelectedData] = useState<DataPoint | null>(getDefaultSelectedData());

    useEffect(() => {
        if (data) {
            const preparedData: DataPoint[] = [];

            if (Array.isArray(data)) {

                console.log(data, "&&&&&&&&&&&&&&&7")
                data.forEach((item) => {
                    var label = dashboardGroupBy === DashboardGroupBy.Daily ? format(item.startDate, "dd/MM") :
                        (dashboardGroupBy === DashboardGroupBy.Weekly ? format(item.startDate, "dd/MM") : format(item.startDate, "MMMM"))

                    //                     returnAmount:number;
                    // returnweight:number;
                    // totalweight:number;
                    // totalAmount:number;
                    preparedData.push({
                        periodLabel: label, startDate: new Date(item.startDate),
                        Revenue: Math.round(item.totalAmount), returnAmount: item.returnAmount, returnweight: item.returnWeight, Quantity: Math.round(item.totalWeight), Orders: item.totalOrders, AovValue: Math.round(item.avgOrderValue), NewCustomers: item.newCustomerCount
                    })
                    // console.log(item)
                });
            }
            setChartData(preparedData);
            // console.log(chartData, "chartData")

        }
    }, [data]);


    useEffect(() => {
        if (data) {
            const preparedData: DataPoint[] = [];

            if (Array.isArray(data)) {
                data.forEach((item) => {
                    var label = dashboardGroupBy === DashboardGroupBy.Daily ? format(item.startDate, "dd/MM") :
                        (dashboardGroupBy === DashboardGroupBy.Weekly ? format(item.startDate, "dd/MM") : format(item.startDate, "MMMM"))
                    preparedData.push({ periodLabel: label, startDate: new Date(item.startDate), Revenue: Math.round(item.totalAmount), returnAmount: item.returnAmount, returnweight: item.returnWeight, Quantity: Math.round(item.totalWeight), Orders: item.totalOrders, AovValue: Math.round(item.avgOrderValue), NewCustomers: item.newCustomerCount })
                    // console.log(item)
                });
            }

            setChartData(preparedData);
            setActiveTab(dashboardGroupBy);
        }

    }, [data, selectedData]);

    const handleBarClick = (dp: DataPoint) => {
        setSelectedData(dp);

        if (handleBarSelected) {
            const periodDates = calculatePeriodDates(dp, activeTab);
            if (periodDates) {
                handleBarSelected(periodDates.startDate, periodDates.endDate, activeTab, chartData[chartData.length - 1].startDate, dp);
            }
        }
    };
    const isBarSelected = (periodLabel: string) => {
        return periodLabel === selectedData?.periodLabel
    }

    const getTimeRangeTitle = () => {
        if (chartData === null || chartData.length === 0)
            return '';
        try {
            const startDate = chartData[0].startDate;
            const endDate = chartData[chartData.length - 1].startDate;

            if (activeTab === DashboardGroupBy.Daily) {
                return `${format(startDate, 'd MMM')} - ${format(endDate, 'd MMM, yyyy')}`;
            }
            if (activeTab === DashboardGroupBy.Weekly) {
                const lastDay = addDays(endDate, 6);
                return `${format(startDate, 'd MMM')} - ${format(lastDay, 'd MMM, yyyy')}`;
            }
            if (activeTab === DashboardGroupBy.Monthly) {
                return `${format(startDate, (new Date(startDate)).getFullYear() === (new Date(endDate)).getFullYear() ? 'MMM' : 'MMM yyyy')} - ${format(endDate, 'MMM yyyy')}`;
            }
        } catch (error) {
            console.error('Date calculation error:', error);
            return 'Date range unavailable';
        }
        return '';
    };


    const getSelectedPeriodTitle = () => {
        if (!selectedData) return '';

        try {
            const startDate = selectedData.startDate;

            if (activeTab === DashboardGroupBy.Daily) {
                return `${format(startDate, 'EEEE, dd MMM')}`;
            }
            if (activeTab === DashboardGroupBy.Weekly) {
                const lastDay = addDays(selectedData.startDate, 6);
                return `${format(startDate, 'd MMM')} - ${format(lastDay, 'd MMM, yyyy')}`;
            }
            if (activeTab === DashboardGroupBy.Monthly) {
                return `${format(startDate, 'MMMM yyyy')}`;
            }
        } catch (error) {
            console.error('Date calculation error:', error);
            return 'Date range unavailable';
        }
        return '';
    };

    const handlePreviousPeriod = () => {
        // const newIndex = currentPeriodIndex + 1;
        // setCurrentPeriodIndex(newIndex);

        var currentSummaryDate = chartData[chartData.length - 1].startDate;
        var prevSD = activeTab === DashboardGroupBy.Daily ? addDays(currentSummaryDate, -15)
            : activeTab === DashboardGroupBy.Weekly ? addDays(currentSummaryDate, -4 * 7)
                : addMonths(currentSummaryDate, -3);

        handleGraph(activeTab, prevSD);

    };

    const handleNextPeriod = () => {
        var currentSummaryDate = chartData[chartData.length - 1].startDate;
        var nextSD = activeTab === DashboardGroupBy.Daily ? addDays(currentSummaryDate, 15)
            : activeTab === DashboardGroupBy.Weekly ? addDays(currentSummaryDate, 4 * 7)
                : addMonths(currentSummaryDate, 3);

        if (addDays(nextSD, -1) > new Date())
            nextSD = new Date();

        handleGraph(activeTab, nextSD);
    };

    const getPrevDisabled = () => {
        if (activeTab === DashboardGroupBy.Daily && (chartData === null || chartData.length < 30)) {
            return true
        }
        if (activeTab === DashboardGroupBy.Weekly && (chartData === null || chartData.length < 12)) {
            return true
        }
        if (activeTab === DashboardGroupBy.Monthly && (chartData === null || chartData.length < 6)) {
            return true
        }
        return false
    }
    const getNextDisabled = () => {
        if (activeTab === DashboardGroupBy.Daily && (chartData === null || chartData.length === 0
            || addDays(chartData[chartData.length - 1].startDate, 1) >= new Date())) {
            return true
        }
        if (activeTab === DashboardGroupBy.Weekly && (chartData === null || chartData.length === 0
            || addDays(chartData[chartData.length - 1].startDate, 7) >= new Date())) {
            return true
        }
        if (activeTab === DashboardGroupBy.Monthly && (chartData === null || chartData.length === 0
            || addMonths(chartData[chartData.length - 1].startDate, 1) >= new Date())) {
            return true
        }
        return false
    }


    const handleTabChange = (value: DashboardGroupBy) => {
        // console.log(value,)
        setActiveTab(value);

        handleGraph(value, new Date())


    };
    const [selectedMetric, setSelectedMetric] = useState("Revenue")
    const updateMetrics = (val: string) => [
        setSelectedMetric(val)
    ]
    return (
        <div>
            <div className="flex justify-between items-center p-4 ">
                <h1 className="text-2xl font-bold text-primary">Dashboard</h1>
                <div className=" flex flex-row gap-2">
                    <Label>Metrics</Label>
                    <Select
                        onValueChange={(val) => updateMetrics(val)}
                        value={selectedMetric}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Select Metrics" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="Revenue">Revenue ( ₹ )</SelectItem>
                            <SelectItem value="Quantity">Quantity ( kg )</SelectItem>
                            <SelectItem value="AovValue">Avg Order Value ( ₹ )</SelectItem>
                            <SelectItem value="Orders">Orders</SelectItem>
                            <SelectItem value="NewCustomers">New Customers</SelectItem>
                        </SelectContent>
                    </Select>


                    <Tabs
                        value={activeTab}
                        onValueChange={(value) => handleTabChange(value as DashboardGroupBy)}
                    >
                        <TabsList>
                            <TabsTrigger value={DashboardGroupBy.Daily}>Daily</TabsTrigger>
                            <TabsTrigger value={DashboardGroupBy.Weekly}>Weekly</TabsTrigger>
                            <TabsTrigger value={DashboardGroupBy.Monthly}>Monthly</TabsTrigger>
                        </TabsList>
                    </Tabs>
                </div>
            </div>
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div className="space-y-1">
                        <CardTitle>Revenue Overview</CardTitle>
                        <p className="text-sm text-muted-foreground">{getTimeRangeTitle()}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button variant="outline" size="icon" onClick={() => { handlePreviousPeriod() }}
                            disabled={getPrevDisabled()}
                        >
                            <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="icon" onClick={() => { handleNextPeriod() }}
                            disabled={getNextDisabled()}
                        >
                            <ChevronRight className="h-4 w-4" />
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    <ResponsiveContainer width="100%" height={250} >
                        <BarChart data={chartData}
                            margin={{ top: 20, right: 20, left: 20, bottom: 20 }}
                            onClick={(data) => {
                                if (data && data.activePayload && data.activePayload.length > 0) {
                                    handleBarClick(data.activePayload[0].payload)
                                }
                            }}>
                            <CartesianGrid strokeDasharray="8 8" vertical={false} />
                            <XAxis dataKey="periodLabel" />
                            {/* <YAxis tickFormatter={(value: number) => `₹${value}`}/> */}
                            <YAxis
                                width={80}
                                orientation="right"
                                axisLine={false}
                                tickLine={false}
                            />
                            <Tooltip formatter={(value: number) => (value)} />
                            <Bar
                                dataKey={selectedMetric}
                                fill="hsl(var(--primary))"
                                // label={{ position: 'outside', fill: '#000' }}
                                shape={(props: any) => {
                                    const { x, y, width, height, periodLabel } = props
                                    const fill = isBarSelected(periodLabel)
                                        ? "hsl(var(--primary))"
                                        : "hsl(var(--primary) / 0.7)"
                                    return (
                                        <rect
                                            x={x}
                                            y={y}
                                            width={width}
                                            height={height}
                                            fill={fill}
                                            rx={4}
                                            ry={4}
                                        />
                                    )
                                }}
                            />
                        </BarChart>
                    </ResponsiveContainer>
                </CardContent>
            </Card>
            {selectedData && (
                <h2 className="text-2xl font-semibold mt-2 text-center mb-0 pb-0">
                    {getSelectedPeriodTitle()}
                </h2>
            )}
        </div>
    );
};

export default CustomerDashboard;