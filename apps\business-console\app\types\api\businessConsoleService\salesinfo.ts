export interface SellerSalesInfo {
  sellerId: number;
  sellerName: string;
  totalBookedWeight: number;
  totalDeliveredWeight: number;
  totalReturnedWeight: number;
  totalCancelledWeight: number;
}

export interface AgentSalesInfo {
  agentId: number;
  agentName: string;
  totalBookedWeight: number;
  totalDeliveredWeight: number;
  totalReturnedWeight: number;
  totalCancelledWeight: number;
  shopCount: number;
}
export interface SalesData {
  id: number;
  name: string;
  bookedWeight: number;
  deliveredWeight: number;
  returnedWeight: number;
  cancelledWeight: number;
  bookedAmount: number;
  deliveredAmount: number;
  returnedAmount: number;
  cancelledAmount: number;
  customerCount: number;
  orderCount: number;
  activeShopCount: number;
}

export interface SalesDetails {
  salesData: SalesData[];
}
export interface BcBuyerDto {
  buyerId: number;
  name: string;
  mobileNumber: string;
  status: boolean;
  latitude: string;
  longitude: string;
  address: string;
  walletBalance: number;
}

export interface SalesRequestBody {}
