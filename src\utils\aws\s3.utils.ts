// src/utils/s3Utils.ts

import { PutObjectCommand } from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import fetch from "node-fetch";
import { s3Client } from "@utils/aws/aws.utils.js";
import logger from "@utils/express-logger.js";
import axios from 'axios';

interface S3UploadConfig {
  bucketName: string;
  key: string;
  body: Buffer;
  contentType: string;
}

class S3Utils {
  /**
   * Uploads a file to S3 using the Upload class for better handling
   * @param config S3 upload configuration
   */
  public async uploadFile(config: S3UploadConfig): Promise<string> {
    const { bucketName, key, body, contentType } = config;
    try {
      const upload = new Upload({
        client: s3Client,
        params: {
          Bucket: bucketName,
          Key: key,
          Body: body,
          ContentType: contentType,
        },
        queueSize: 4, // concurrency
        partSize: 5 * 1024 * 1024, // 5MB
        leavePartsOnError: false,
      });

      upload.on("httpUploadProgress", (progress) => {
        logger.info(
          `Uploading ${key}: ${progress.loaded}/${progress.total} bytes`
        );
      });

      const result = await upload.done();
      logger.info(`Successfully uploaded ${key} to S3.`);
      return result.Location || `https://${bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
    } catch (error: any) {
      logger.error(`Failed to upload ${key} to S3: ${error.message}`);
      throw error;
    }
  }

  /**
   * Fetches an image from a URL and uploads it to S3
   * @param baseUrl Base URL for images
   * @param imageName Name of the image file
   * @param bucketName S3 bucket name
   * @returns S3 URL of the uploaded image
   */
  public async fetchAndUploadImage(
    baseUrl: string,
    imageName: string,
    key:string,
    bucketName: string,
    type?: string
  ): Promise<string> {
    const imageUrl = `${baseUrl}/${imageName.trim()}`;
    try {
      // const response = await fetch(imageUrl);
      // if (!response.ok) {
      //   throw new Error(`Failed to fetch image: ${response.statusText}`);
      // }

      const response = await axios({
        url: imageUrl,
        method: 'GET',
        responseType: 'arraybuffer', // Get binary data
        headers: {
          'Accept': 'image/avif,image/webp,image/*,*/*;q=0.8', // Request AVIF first
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', // Mimic browser
        },
      });

      // const buffer = await response.arrayBuffer();
      // const contentType = "image/" + (type || "png");
      const contentType = response.headers['content-type'] || "image/png";

      const s3Url = await this.uploadFile({
        bucketName,
        key,
        body: Buffer.from(response.data),
        contentType,
      });

      return s3Url;
    } catch (error: any) {
      logger.error(`Error fetching/uploading image ${imageName}: ${error.message}`);
      throw error;
    }
  }
}

export default new S3Utils();
