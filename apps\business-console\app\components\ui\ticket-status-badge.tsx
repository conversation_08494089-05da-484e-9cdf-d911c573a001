import { cn } from "~/lib/utils";
import { Badge } from "./badge";
import { SupportTicketStatus } from "~/types/api/businessConsoleService/Tickets";

interface TicketStatusBadgeProps {
  status: SupportTicketStatus;
  className?: string;
}

export function TicketStatusBadge({ status, className }: TicketStatusBadgeProps) {
  const statusStyles: Record<SupportTicketStatus, string> = {
    OPEN: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
    WIP: "bg-blue-100 text-blue-800 hover:bg-blue-100",
    CLOSED: "bg-green-100 text-green-800 hover:bg-green-100"
  };

  // Get the style for the status, defaulting to OPEN style if not found
  const style = statusStyles[status] || statusStyles.OPEN;

  // Format the display text
  const displayText = status === "WIP" ? "Work In Progress" : status.charAt(0) + status.slice(1).toLowerCase();

  return (
    <Badge 
      className={cn(style, className)}
      variant="outline"
    >
      {displayText}
    </Badge>
  );
} 