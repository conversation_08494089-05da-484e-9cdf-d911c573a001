import { json } from "@remix-run/node";
import { useLoaderData, useNavigate, useParams, useLocation, Outlet, useFetcher } from "@remix-run/react";
import { ArrowLeft, Plus, Printer } from "lucide-react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Layout } from "~/root";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { getSelectedMyStock } from "~/services/stockservices/mystockservice";
import { ItemStock } from "~/types/api/businessConsoleService/ItemStock";


interface LoaderData {
  stock: ItemStock;
  }

  export const loader = withAuth(async ({ request, user, params }) => {
    const url = new URL(request.url);
    const SellerId= parseInt(url.searchParams.get("sellerId") as string||"0")
    const isSeller = SellerId==null || isNaN(SellerId) || SellerId === 0;
  const stockId = (params ?? {}).stockId; // <-- get from params safely
  const numericStockId = stockId ? Number(stockId) : undefined;
  

  try {
    const response = await getSelectedMyStock(isSeller, numericStockId,SellerId, request);
    return withResponse({
      stock: response.data,
    },response?.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    // Return a JSON-based error shape
    throw new Response("failed to get sellers", { status: 500 })
  }
});

export default function SelectedStockWithMe() {
  const { stock } = useLoaderData<LoaderData>();
  const navigate = useNavigate()
  const handleBackClick = () => {
    navigate(-1);
  };
  
  return (
    <Layout>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between no-print">
          <Button variant="outline" size="sm" onClick={handleBackClick}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
            <h1>StockWithMe</h1>
           </Button>
           <div>

           </div>
        </div>
        <div className="printable-area">

          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row justify-between gap-4">
                <div>
                  <h1 className="text-2xl font-bold">{stock.itemName}</h1>
                  <p className="text-muted-foreground">Distributer: {stock.distributor}</p>
                </div>
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-2">
                
                    <Badge variant={stock.active ? "default" : "secondary"} className="font-normal">
                      {stock.active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <p className="text-sm">
                    <span className="font-medium">My Price:</span>₹ {stock.pricePerUnit.toFixed(2)}
                  </p>
                  {stock.maxAvailableQty > 0 && (
                    <p className="text-sm text-blue-600">
                      <span className="font-medium">MaxAvail Qty:</span> {stock.maxAvailableQty} 
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          </div>
          </div>
        
          <Outlet/>
          

    </Layout>
  );
}