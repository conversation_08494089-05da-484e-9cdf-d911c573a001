import { Button } from "./ui/button";
import { Truck } from "lucide-react";
import { useNavigate } from "@remix-run/react";

interface BuyerDetails {
    buyerId: number;
    mobileNumber: string;
}
export function PlaceOrder({
    buyerDetails
}: { buyerDetails: BuyerDetails }) {
    const navigate = useNavigate();

    const handlePlaceOrder = () => {
        navigate(`/home/<USER>

    }
    return <>
        <Button onClick={handlePlaceOrder} variant="outline" size="sm">
            <Truck className="h-4 w-4 mr-2" />
            Place order
        </Button>
    </>

}
