import {
      <PERSON><PERSON><PERSON>,
      <PERSON>,
      Cell,
      Responsive<PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON>,
      <PERSON>
} from "recharts";

import {
      Table,
      TableBody,
      TableCell,
      TableHead,
      TableHeader,
      TableRow
} from "../ui/table";

// Sample data (replace with real data from your API if available)
const localityData = [
      { locality: "Marathahalli", orders: 10, revenue: 1200, conversion: 45, appOpens: 45 },
      { locality: "Koramangala", orders: 15, revenue: 1800, conversion: 50, appOpens: 60 },
      { locality: "Indiranagar", orders: 8, revenue: 900, conversion: 40, appOpens: 35 },
      { locality: "Whitefield", orders: 12, revenue: 1500, conversion: 55, appOpens: 50 },
      { locality: "Jayanagar", orders: 9, revenue: 1100, conversion: 42, appOpens: 40 },


];

export default function LocalityAnalysis() {
      return (
            <div className="w-full my-3 bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
                  <div className="space-y-6">
                        <h3 className="text-lg font-semibold text-gray-800">
                              Locality Analysis
                        </h3>
                        {/* Chart Section */}
                        <div className="w-full ">
                              <ResponsiveContainer width="100%" height={300}>
                                    <PieChart>
                                          <Pie
                                                data={localityData}
                                                cx="50%"
                                                cy="50%"
                                                labelLine={false}
                                                // label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                                outerRadius={80}
                                                fill="#8884d8"
                                                dataKey="revenue"
                                                nameKey="locality"
                                          >
                                                {localityData.map((entry, index) => (
                                                      <Cell
                                                            key={`cell-${index}`}
                                                            fill={[
                                                                  '#34D399',
                                                                  '#60A5FA',
                                                                  '#F59E0B',
                                                                  '#EC4899',
                                                                  '#8B5CF6'
                                                            ][index % 5]}
                                                      />
                                                ))}
                                          </Pie>
                                          <Tooltip
                                                formatter={(value: number) => `₹${value}`}
                                                contentStyle={{
                                                      backgroundColor: "#fff",
                                                      borderRadius: "8px",
                                                      border: "1px solid #e5e7eb",
                                                      boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                                                }}
                                          />
                                          <Legend
                                                layout="horizontal"
                                                verticalAlign="bottom"
                                                align="center"
                                          />
                                    </PieChart>
                              </ResponsiveContainer>
                        </div>
                        {/* Table Section */}
                        <div className="w-full overflow-x-auto">
                              <Table className="w-full border-separate border-spacing-y-2">
                                    <TableHeader>
                                          <TableRow className="bg-gray-50 text-gray-700">
                                                <TableHead className="font-semibold">Locality</TableHead>
                                                <TableHead className="font-semibold text-right"># of Orders</TableHead>
                                                <TableHead className="font-semibold text-right">Revenue</TableHead>
                                                <TableHead className="font-semibold text-right">Conv. %</TableHead>
                                                <TableHead className="font-semibold text-right">App Opens</TableHead>
                                          </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                          {localityData.map((item, index) => (
                                                <TableRow
                                                      key={index}
                                                      className="bg-white hover:bg-gray-50 transition-colors duration-150"
                                                >
                                                      <TableCell className="font-medium text-gray-800">
                                                            {item.locality}
                                                      </TableCell>
                                                      <TableCell className="text-right">
                                                            {item.orders}
                                                      </TableCell>
                                                      <TableCell className="text-right">
                                                            ₹{item.revenue.toLocaleString()}
                                                      </TableCell>
                                                      <TableCell className="text-right">
                                                            {item.conversion}%
                                                      </TableCell>
                                                      <TableCell className="text-right">
                                                            {item.appOpens}
                                                      </TableCell>
                                                </TableRow>
                                          ))}
                                    </TableBody>
                              </Table>
                        </div>
                  </div>
            </div>
      );
}