import s3Utils from "@utils/aws/s3.utils";

interface UploadFileParams {
  file: Buffer;
  fileName: string;
  contentType: string;
  bucketName?: string;
}

class S3Service {
  private readonly defaultBucket: string;
  private serverEnv: string;
  constructor() {
    this.defaultBucket = process.env.AWS_S3_BUCKET || "mneti";
    this.serverEnv = process.env.SERVER_ENV || "uat";

  }

  /**
   * Upload a file to S3
   * @param params Upload parameters including file buffer, name, and content type
   * @returns URL of the uploaded file
   */
  async uploadFile({
    file,
    fileName,
    contentType,
    bucketName,
  }: UploadFileParams): Promise<string> {
    try {
      const bucket = bucketName || this.defaultBucket;
      if (!bucket) {
        throw new Error("S3 bucket name is required but not provided");
      }

      // Extract file extension from fileName or contentType
      let fileExtension = fileName.split('.').pop();
      if (!fileExtension) {
        // Get extension from contentType (e.g. 'image/jpeg' -> 'jpg')
        fileExtension = contentType.split('/').pop();
      }
      
      // Generate UUID for unique file name
      const uuid = crypto.randomUUID();

      let key = `p/mc-uat/${uuid}.${fileExtension}`;

      if(this.serverEnv === "production") {
         key = `p/mc-prod/${uuid}.${fileExtension}`;
      }

      const fileUrl = await s3Utils.uploadFile({
        bucketName: bucket,
        key,
        body: file,
        contentType,
      });

      return fileUrl;
    } catch (error) {
      console.error("Error uploading file to S3:", error);
      throw error;
    }
  }
}

export default new S3Service(); 