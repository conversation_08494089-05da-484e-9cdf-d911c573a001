import { json, LoaderFunction, ActionFunction } from '@remix-run/node';
import { useLoaderData, Form, useActionData } from '@remix-run/react';
import { useState } from 'react';
import { Button } from '~/components/ui/button';

export const loader: LoaderFunction = async () => {
    return json({});
};

export const action: ActionFunction = async ({ request }) => {
    const authToken = process.env.AUTH_TOKEN;
    if (!authToken) {
        throw new Error('AUTH_TOKEN environment variable is not set');
    }

    try {
        // Get the origin from the incoming request
        const url = new URL(request.url);
        const baseUrl = url.origin;

        const response = await fetch(`${baseUrl}/api/whatsapp/send-template-message`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                phoneNo: '8137878484',
                templateName: 'hello_mnet'
            })
        });

        if (!response.ok) {
            throw new Error('Failed to send message');
        }

        const data = await response.json();
        return json({ success: true, data });
    } catch (error) {
        console.error('Error:', error);
        return json({ error: "Failed to send template message" }, { status: 500 });
    }
};

export default function WhatsAppPage() {
    const actionData = useActionData();
    const [isSubmitting, setIsSubmitting] = useState(false);

    return (
        <div className="p-6">
            <Form
                method="post"
                onSubmit={() => setIsSubmitting(true)}
                onChange={() => {
                    if (actionData) setIsSubmitting(false);
                }}
            >
                <Button
                    type="submit"
                    disabled={isSubmitting}
                >
                    {isSubmitting ? 'Sending...' : 'Send Hello World Template'}
                </Button>
            </Form>

            {actionData?.success && (
                <div className="mt-4 text-green-600">Message sent successfully!</div>
            )}

            {actionData?.error && (
                <div className="mt-4 text-red-600">{actionData.error}</div>
            )}
        </div>
    );
}
