import { But<PERSON> } from "@components/ui/button";
import { ScrollArea } from "@components/ui/scroll-area";
import { MessageCircle, Share2 } from "lucide-react";
import { CampaignCard } from "@components/marketing/CampaignCard";
import type { Campaign } from "@components/marketing/CampaignCard";

// Mock data for campaigns - Move to service later
const mockCampaigns: Campaign[] = [
  {
    id: 1,
    title: "Summer Sale",
    type: "SMS",
    status: "Active",
    reach: "1,200 customers",
    date: "2024-03-20",
  },
  {
    id: 2,
    title: "New Product Launch",
    type: "WhatsApp",
    status: "Scheduled",
    reach: "800 customers",
    date: "2024-03-25",
  },
];

export default function Campaigns() {
  const handleViewCampaignDetails = (id: number) => {
    // Implement view campaign details logic
    console.log("View campaign details:", id);
  };

  return (
    <div>
      <div className="flex justify-end mb-4 space-x-2">
        <Button variant="outline">
          <MessageCircle className="h-4 w-4 mr-2" />
          SMS Campaign
        </Button>
        <Button>
          <Share2 className="h-4 w-4 mr-2" />
          WhatsApp Campaign
        </Button>
      </div>

      <ScrollArea className="h-[calc(100vh-300px)]">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {mockCampaigns.map((campaign) => (
            <CampaignCard
              key={campaign.id}
              campaign={campaign}
              onViewDetails={handleViewCampaignDetails}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
} 