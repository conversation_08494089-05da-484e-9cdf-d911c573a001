@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 171 98% 32%; 
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 152 29% 96%; /* Adjusted to match #f0f7f4 */
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 144 41% 35%;
    --radius: 0.5rem;

    /* Primary Brand Color Shades */
    --primary-50: 171 42% 91%;
    --primary-900: 165 100% 15%;
    --primary-800: 169 100% 20%;
    --primary-700: 169 100% 23%;
    --primary-600: 171 100% 26%;
    --primary-500: 171 100% 29%;
    --primary-400: 173 100% 32%;
    --primary-300: 172 47% 48%;
    --primary-200: 171 42% 63%;
    --primary-100: 171 42% 78%;

    /* Secondary Brand Color Shades */
    --secondary-900: 1 72% 44%;
    --secondary-800: 1 65% 49%;
    --secondary-700: 1 70% 53%;
    --secondary-300: 1 75% 69%;
    --secondary-600: 2 83% 57%;
    --secondary-200: 0 80% 79%;
    --secondary-400: 2 90% 65%;
    --secondary-500: 5 96% 60%;
    --secondary-100: 354 100% 91%;
    --secondary-50: 351 100% 96%;

    /* Complementary Brand Color Shades */
    --complementary-50: 178 68% 93%;
    --complementary-900: 177 100% 20%;
    --complementary-800: 180 100% 28%;
    --complementary-700: 182 100% 32%;
    --complementary-600: 183 100% 37%;
    --complementary-500: 183 100% 41%;
    --complementary-400: 182 100% 42%;
    --complementary-300: 181 70% 53%;
    --complementary-200: 180 67% 68%;
    --complementary-100: 178 67% 81%;

    /* Typography Shades */
    --typography-50: 220 9% 93%;
    --typography-100: 210 9% 78%;
    --typography-200: 210 9% 68%;
    --typography-300: 209 9% 53%;
    --typography-400: 209 11% 44%;
    --typography-500: 210 21% 30%;
    --typography-600: 211 21% 28%;
    --typography-700: 209 21% 21%;
    --typography-800: 212 20% 17%;
    --typography-900: 212 20% 13%;

    /* Neutral Shades */
    --neutral-50: 240 20% 99%;
    --neutral-100: 240 13% 97%;
    --neutral-200: 240 13% 95%;
    --neutral-300: 240 12% 93%;
    --neutral-400: 252 12% 92%;
    --neutral-500: 249 14% 90%;
    --neutral-600: 250 7% 82%;
    --neutral-700: 252 3% 64%;
    --neutral-800: 255 2% 49%;
    --neutral-900: 240 2% 38%;

    /* Blue Shades */
    --blue-50: 200 60% 93%;
    --blue-900: 207 100% 26%;
    --blue-800: 203 100% 32%;
    --blue-700: 203 86% 38%;
    --blue-600: 203 74% 45%;
    --blue-500: 202 67% 49%;
    --blue-400: 202 64% 56%;
    --blue-300: 202 63% 64%;
    --blue-200: 201 64% 74%;
    --blue-100: 201 64% 84%;

    /* Orange Shades */
    --orange-50: 37 94% 94%;
    --orange-900: 19 83% 47%;
    --orange-800: 24 81% 50%;
    --orange-700: 28 85% 52%;
    --orange-600: 31 91% 53%;
    --orange-500: 33 94% 55%;
    --orange-400: 35 94% 59%;
    --orange-300: 35 94% 65%;
    --orange-200: 35 95% 75%;
    --orange-100: 35 95% 85%;

    /* Pink Shades */
    --pink-50: 331 60% 93%;
    --pink-900: 318 100% 23%;
    --pink-800: 324 100% 31%;
    --pink-700: 327 100% 35%;
    --pink-600: 328 100% 39%;
    --pink-500: 330 100% 42%;
    --pink-400: 331 70% 53%;
    --pink-300: 331 69% 61%;
    --pink-200: 331 66% 72%;
    --pink-100: 329 64% 83%;

    /* Yellow Shades */
    --yellow-50: 39 98% 96%;
    --yellow-100: 39 99% 86%;
    --yellow-200: 39 100% 76%;
    --yellow-300: 39 100% 66%;
    --yellow-400: 39 100% 56%;
    --yellow-500: 39 87% 46%;
    --yellow-600: 39 98% 37%;
    --yellow-700: 39 100% 30%;
    --yellow-800: 39 100% 23%;
    --yellow-900: 39 100% 17%;

    /* Green Shades */
    --green-50: 131 49% 93%;
    --green-900: 131 100% 19%;
    --green-800: 139 100% 25%;
    --green-700: 141 100% 28%;
    --green-600: 143 100% 32%;
    --green-500: 140 82% 38%;
    --green-400: 134 48% 51%;
    --green-300: 132 47% 61%;
    --green-200: 130 45% 72%;
    --green-100: 130 44% 83%;

    /* Red Shades */
    --red-50: 2 97% 97%;
    --red-100: 2 99% 89%;
    --red-200: 2 99% 82%;
    --red-300: 2 100% 75%;
    --red-400: 2 100% 68%;
    --red-500: 2 69% 57%;
    --red-600: 2 57% 47%;
    --red-700: 2 63% 37%;
    --red-800: 2 69% 28%;
    --red-900: 2 75% 19%;
  }


}

html, body {
  @apply bg-white font-sans;

  @media (prefers-color-scheme: light) {
    color-scheme: light;
  }
}


/* Modern Alert Animations */
@keyframes neon-glow {
  0%, 100% {
    box-shadow:
      0 0 5px rgba(239, 68, 68, 0.4),
      0 0 10px rgba(239, 68, 68, 0.3),
      0 0 15px rgba(239, 68, 68, 0.2),
      inset 0 0 5px rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.6);
  }
  50% {
    box-shadow:
      0 0 10px rgba(239, 68, 68, 0.6),
      0 0 20px rgba(239, 68, 68, 0.4),
      0 0 30px rgba(239, 68, 68, 0.3),
      inset 0 0 10px rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.8);
  }
}

@keyframes warning-pulse {
  0%, 100% {
    box-shadow:
      0 0 8px rgba(245, 158, 11, 0.4),
      0 0 16px rgba(245, 158, 11, 0.2),
      inset 0 0 8px rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow:
      0 0 16px rgba(245, 158, 11, 0.6),
      0 0 32px rgba(245, 158, 11, 0.3),
      inset 0 0 16px rgba(245, 158, 11, 0.15);
    border-color: rgba(245, 158, 11, 0.7);
    transform: scale(1.01);
  }
}

@keyframes breathing {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 0 0 0 rgba(239, 68, 68, 0.4),
      0 4px 12px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.01);
    box-shadow:
      0 0 0 8px rgba(239, 68, 68, 0),
      0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

/* Alert Animation Classes */
.card-alert-animated {
  position: relative;
  border: 2px solid transparent;
  animation: neon-glow 2s ease-in-out infinite;
  transition: all 0.3s ease;
}

.card-alert-warning {
  position: relative;
  border: 2px solid transparent;
  animation: warning-pulse 2s ease-in-out infinite;
  transition: all 0.3s ease;
}

.card-alert-breathing {
  position: relative;
  border: 2px solid rgba(239, 68, 68, 0.4);
  animation: breathing 2s ease-in-out infinite;
  transition: all 0.3s ease;
}