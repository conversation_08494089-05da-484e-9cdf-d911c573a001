import { string } from 'zod';

export interface BuyerNetworkBanners{
            id: number,
            networkId: number,
            bannerUrl: string,
            sequenceId: number,
            target: string,
            active: boolean
}

export  interface WeeklySales{
      week: string,
      dateRange: string,
      totalOrders: number,
      totalDeliveredOrders: number,
      totalRevenue: number
}

export interface SellerSales {
  weeklySales: WeeklySales[]
  totalOrders: number,
  totalRevenue: number
}

export interface WeeklyConversionRate{
      week: string,
      dateRange: string,
      ordered: number,
      searched: number,
      delivered: number
}
export interface CustomerConversionRate{
      weeklySales: WeeklyConversionRate[],
      conversionRate:number
      
}

export interface WeeklyAcquisition{
      week:string,
      dateRange:string,
      ordered: number,
      newCustomers: number,
      repeat: number,
      inActive: number
}
export interface CustomerAcquisition{   
  totalCustomers: number,
  newCustomers: number,
  weeklyAcquisition:WeeklyAcquisition[]
}
