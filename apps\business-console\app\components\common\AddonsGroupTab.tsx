import React, { useEffect, useState } from 'react';
import { Input } from '~/components/ui/input';
import { Pencil, Search, Trash } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { ResponsiveTable } from '../ui/responsiveTable';
import { MyAddonGroupData, MyVariationData } from '~/types/api/businessConsoleService/SellerManagement';
import AddonsGroupModal from './AddonsGropModal';
import { useFetcher, useNavigate } from '@remix-run/react';
import { useDebounce } from '~/hooks/useDebounce';
import SpinnerLoader from '../loader/SpinnerLoader';


interface MyAddonsGroupTabProps {
      addonGroupTabData: MyAddonGroupData[];
      sellerId: number
}

const myAddonGroupHeader = [
      "Id",
      "InternalName",
      "DisplayName",
      "Description",
      "Active",
      "",
      ""
];

const MyAddonsGroupTab: React.FC<MyAddonsGroupTabProps> = ({
      addonGroupTabData,
      sellerId
}) => {
      const [searchTerm, setSearchTerm] = useState('');


      // const handlePageSearch = (value: string) => {
      //       setSearch(value);
      // };
      // const handlePageSizeChange = (newPageSize: string) => {
      //       setPageSize(newPageSize);
      // };
      // const handlePageChange = (newPageNum: number) => {
      //       setPageNum(newPageNum);
      // };

      const addOnGroupFetcher = useFetcher()
      const handleDelete = (addgroupData: MyAddonGroupData) => {
            const formData = new FormData();
            formData.append("actionType", "addonsgroupDelete");
            formData.append("addonGId", addgroupData?.id.toString())
            formData.append("sellerId", sellerId.toString())
            addOnGroupFetcher.submit(formData, { method: 'post' })
      };
      const navigate = useNavigate();
      const [selectedAddonsGData, setSelectedAddonsGData] = useState<MyAddonGroupData>();
      const [isAddonsGEdit, setIsAddonsGEdit] = useState(false);
      const [addonGroupModalOpen, setAddonGroupModalOpen] = useState(false);

      const handleEditModal = (row: MyAddonGroupData) => {
            setSelectedAddonsGData(row);
            setIsAddonsGEdit(true)
            setAddonGroupModalOpen(true)
      }
      const handleAddModal = () => {
            setSelectedAddonsGData(undefined);
            setIsAddonsGEdit(false)
            setAddonGroupModalOpen(true)
      }


      const [filteredGroupData, setFilteredGroupData] = useState<MyAddonGroupData[]>(addonGroupTabData);

      const debouncedSearchTerm = useDebounce(searchTerm, 300);

      useEffect(() => {
            if (debouncedSearchTerm.length >= 3 && debouncedSearchTerm !== "") {
                  const filtered = addonGroupTabData.filter((item) =>
                        [item.displayName, item.internalName].some(
                              (field) => field?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
                        )
                  )
                  setFilteredGroupData(filtered);
            }
            else {
                  setFilteredGroupData(addonGroupTabData)
            }
      }, [debouncedSearchTerm, addonGroupTabData]);


      const loading = addOnGroupFetcher.state !== "idle"


      return (
            <>
                  <div className="flex justify-between mb-4">
                        {loading && <SpinnerLoader loading={loading} size={20} />}

                        <Input
                              placeholder="Search by Internal Name && display Name"
                              value={searchTerm}

                              type='search'
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm mt-2 rounded-full "
                        />
                        {/* <Select value={pageSize} onValueChange={handlePageSizeChange}>
                              <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="Items per page" />
                              </SelectTrigger>
                              <SelectContent>
                                    <SelectItem value="5">5 per page</SelectItem>
                                    <SelectItem value="10">10 per page</SelectItem>
                                    <SelectItem value="20">20 per page</SelectItem>
                                    <SelectItem value="50">50 per page</SelectItem>
                              </SelectContent>
                        </Select> */}
                  </div>
                  {filteredGroupData.length === 0 ? (
                        <div className="text-center py-4">No results found</div>
                  ) : (
                        <ResponsiveTable
                              headers={myAddonGroupHeader}
                              data={filteredGroupData}
                              renderRow={(row) => (
                                    <tr key={row.id} className="border-b">
                                          <td className="py-2 px-3 text-center whitespace-normal break-words " >{row.id}</td>
                                          <td className="py-2 px-3 text-center whitespace-normal break-words text-blue-600 cursor-pointer " onClick={() => navigate(`/home/<USER>/td>
                                          <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.displayName}</td>
                                          <td className="py-2 px-3 text-center whitespace-normal break-words">{row?.description}</td>

                                          <td className="py-2 px-3 text-center whitespace-normal break-words">
                                                {row?.active ? (
                                                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                            <svg
                                                                  className="w-4 h-4 mr-1 text-green-500"
                                                                  fill="currentColor"
                                                                  viewBox="0 0 20 20"
                                                                  xmlns="http://www.w3.org/2000/svg"
                                                            >
                                                                  <path
                                                                        fillRule="evenodd"
                                                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                                        clipRule="evenodd"
                                                                  />
                                                            </svg>
                                                            Active
                                                      </span>
                                                ) : (
                                                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                            <svg
                                                                  className="w-4 h-4 mr-1 text-red-500"
                                                                  fill="currentColor"
                                                                  viewBox="0 0 20 20"
                                                                  xmlns="http://www.w3.org/2000/svg"
                                                            >
                                                                  <path
                                                                        fillRule="evenodd"
                                                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                                        clipRule="evenodd"
                                                                  />
                                                            </svg>
                                                            Inactive
                                                      </span>
                                                )}
                                          </td>
                                          <td className="py-2 px-3 text-center cursor-pointer">
                                                <Button
                                                      variant="ghost"
                                                      size="sm"
                                                      className="text-red-500 hover:text-red-900"
                                                      onClick={() => {
                                                            if (confirm("Are you sure you want to delete this addonsGroup?")) {
                                                                  handleDelete(row)
                                                            }
                                                      }}
                                                      style={{ alignSelf: "flex-end" }}
                                                >
                                                      <Trash size={20} />
                                                </Button>
                                          </td>
                                          <td className="py-2 px-3 text-center cursor-pointer">
                                                <Pencil color='blue' size={20} onClick={() => handleEditModal(row)} />
                                          </td>
                                    </tr>
                              )}
                        />)}
                  {/* <div className="flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap">
                        <h2 className="shrink-0">Current Page: {pageNum + 1}</h2>
                        <div className="overflow-x-auto">
                              <ResponsivePagination
                                    totalPages={Number(pageSize)}
                                    currentPage={pageNum}
                                    onPageChange={handlePageChange}
                              />
                        </div>
                  </div> */}
                  <AddonsGroupModal
                        isOpen={addonGroupModalOpen}
                        data={isAddonsGEdit ? selectedAddonsGData : undefined}
                        onClose={() => setAddonGroupModalOpen(false)}
                        sellerId={sellerId} header={isAddonsGEdit ? 'Edit AddonGroup' : 'Add AddonGroup'} />
                  <Button className="fixed bottom-5 right-5 rounded-full cursor-pointer" onClick={() => handleAddModal()}>+ Add AddonsGroup</Button>
            </>
      );
};

export default MyAddonsGroupTab;
