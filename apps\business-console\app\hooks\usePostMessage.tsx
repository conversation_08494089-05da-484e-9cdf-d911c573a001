import { useEffect } from "react";
import { CommonMessage, Message } from "~/types/iframe";

/**
 * Sends a message to the target window with a specified type and payload.
 *
 * @template TType - The string literal type of the message.
 * @template TPayload - The type of the message payload.
 * @param message - The message to send.
 * @param targetOrigin - The origin to which the message should be sent.
 */
const usePostMessage = <TType extends string, TPayload extends CommonMessage>(
  message: Message<TType, TPayload> | null,
  targetOrigin: string = "*"
) => {
  useEffect(() => {
    if (message && typeof window !== "undefined") {
      window.parent.postMessage(message, targetOrigin);
    }
  }, [message, targetOrigin]);
};

export default usePostMessage;
