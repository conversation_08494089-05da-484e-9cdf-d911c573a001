export interface CouponDetailsType {
  id: number;
  sellerId: number;
  couponCode: string;
  description: string;
  discountType: string;
  couponName: string;
  discountValue: number;
  usageCount: number;
  totalUsers: number;
  totalOrders: number;
  totalDiscountAmount: string;
  minOrderValue: number;
  maxDiscountAmount: number;
  maxUsageCount: number;
  active: boolean;
  validFrom: string;
  validTo: string;
}

export type ConfigKey = "discount" | "filter" | "validity";
export type ConfigAdditionalKey =
  | "discountAdditional"
  | "filterAdditional"
  | "validityAdditional";

export enum configKeysEnum {
  discount = "discountAdditional",
  filter = "filterAdditional",
  validity = "validityAdditional",
}

export interface CouponPreloadType {
  couponTypes: {
    label: string;
    value: string;
  }[];
  couponTypesConfigurations: {
    label: string;
    value: string;
    configuration: {
      [K in ConfigKey]: {
        label: string;
        value: string;
        description: string;
        properties: {
          label: string;
          value: string;
          operatorConfig?: {
            input: string;
            options?: {
              label: string;
              value: string;
            }[];
          };
          valueConfig?: {
            input: string;
            options?: {
              label: string;
              value: string;
            }[];
          };
        }[];
        propertyConfig?: {
          input: string;
          type: string;
          label: string;
          value: string;
          options?: {
            label: string;
            value: string;
          }[];
        }[];
      };
    };
  }[];
}

export const DISCOUNT_UNIT_MAP: Record<string, string> = {
  item: "unit",
  amount: "/-",
  discountFixed: "/-",
  maxDiscountAmount: "/-",
  minDiscountAmount: "/-",
  percentage: "%",
  discountPercent: "%",
  weight: "gm",
  volume: "ltr",
};

export type IdConfigItem = {
  id: string;
  property: string;
  operator?: string;
  value: string;
};

export interface CouponPayloadType {
  couponType: string;
  couponCode?: string;
  description?: string;
  customConfiguration?: {
    discount: IdConfigItem[];
    filter: IdConfigItem[];
    validity: IdConfigItem[];
    discountAdditional?: IdConfigItem[];
    filterAdditional?: IdConfigItem[];
    validityAdditional?: IdConfigItem[];
  };
}

export type ConfigItem = {
  property: string;
  operator?: string;
  value: string;
};

export interface AddCouponType {
  code: string;
  name: string;
  description: string;
  discountConfiguration?: ConfigItem[];
  filterConfiguration?: ConfigItem[];
  validityConfiguration?: ConfigItem[];
}

export interface CouponType {
  id: number;
  code: string;
  name: string;
  description: string;
  discountType?: string;
  discountConfiguration?: ConfigItem[];
  filterConfiguration?: ConfigItem[];
  validityConfiguration?: ConfigItem[];
}
