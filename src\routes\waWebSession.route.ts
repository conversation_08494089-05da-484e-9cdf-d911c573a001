import { Router } from 'express';
import WaWebSessionController from '@/controllers/waWebSession.controller.js';
import { Routes } from '@interfaces/routes.interface.js';
import {authenticateToken} from "@middleware/auth.js"


class WaWebSessionRoute implements Routes {
  public path = '/api/v1/wa-web-session';
  public router = Router();
public waWebSessionController = new WaWebSessionController();

  constructor() {
    this.initializeRoutes();
  }

  private initializeRoutes() {
    this.router.post(`${this.path}/create-session`,authenticateToken, this.waWebSessionController.createSession);
    this.router.post(`${this.path}/validate-session`, authenticateToken,this.waWebSessionController.validateSession);
    this.router.post(`${this.path}/generate-totp-token`, this.waWebSessionController.generateTotpToken);
    this.router.post(`${this.path}/verify-totp-token`, this.waWebSessionController.verifyTotpToken);
  }
}

export default WaWebSessionRoute;
